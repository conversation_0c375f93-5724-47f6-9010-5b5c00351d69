{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(pnpm type-check:*)", "WebFetch(domain:www.shadcn-vue.com)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__serena__write_memory", "mcp__serena__activate_project", "mcp__serena__replace_symbol_body", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__summarize_changes", "WebFetch(domain:tsconfig.net)", "mcp__sequential-thinking__sequentialthinking"], "deny": []}}