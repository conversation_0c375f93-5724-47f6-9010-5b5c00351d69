# 产品管理模型深度研讨与重构

**日期:** 2025年8月10日
**参与人:** <PERSON>, Kai Zhou
**主题:** 重新定义适用于MTO（按订单制造）模式的玻璃深加工及周边企业的产品管理流程。
**文档状态:** 演进中 (从概念讨论到设计规范)

---

## 1. 初始框架提出
... (内容与之前相同，为简洁省略) ...

---

## 2. 对初始框架的审视与挑战
... (内容与之前相同，为简洁省略) ...

---

## 3. 重构后的新模型：以“计算核心”驱动的动态系统
... (内容与之前相同，为简洁省略) ...

---

## 4. 核心结论与后续步骤
... (内容与之前相同，为简洁省略) ...

---

## 5. 设计规范：构建设计师友好的参数化产品建模器
... (内容与之前相同，为简洁省略) ...

---

## 6. 数据模型架构设计：从“巨石”到“微服务”

**版本:** 1.0
**日期:** 2025年8月10日

### 6.1. 现有数据结构分析 (As-Is)

通过对 `productTemplates.json` 和 `productConfigurations.json` 的审视，我们发现现有数据结构具备了参数化系统的基础，但其设计思想无法支撑我们在第5章中定义的“设计师友好的可视化建模器”。

#### 6.1.1. 优点与长处
-   **模板与实例分离:** 成功地将定义产品“可能性”的模板与一个“具体的实现”配置分离开来。
-   **参数化驱动:** 大量使用`formula`和`quantityFormula`，核心是计算而非硬编码，方向正确。
-   **层级化结构:** 通过`components`和`subComponents`的嵌套，清晰地表达了BOM层级。
-   **规则意识:** `materialSelectionRules`和`configurationRules`的出现，表明已认识到规则是独立于结构的重要实体。

#### 6.1.2. 根本性局限
尽管基础良好，但现有结构存在四个核心问题，使其无法满足新一代系统的要求：

1.  **缺乏可视化基因:** 数据中完全没有关于组件**位置(x, y)、层级(z-index)或几何关系（如对齐、约束）**的信息。这使得它只能用于计算BOM，而无法被前端渲染成可供设计师直接操作的可视化模型。
2.  **硬编码的字符串逻辑:** 所有的`formula`和`condition`都是字符串。这导致系统**无法以结构化方式生成或解析这些逻辑**，使得“交互式规则生成引擎”成为不可能。同时，依赖关系不明确，实时计算效率低下。
3.  **高度耦合的“巨石”结构:** 在模板中，组件的定义、数量公式、选料规则等所有信息都混合在一起。这违反了单一职责原则，导致**复用性差、维护成本高**。
4.  **静态的配置快照:** `productConfigurations.json`存储了大量计算后的结果值，造成数据冗余。一旦模板中的公式更新，所有已生成的配置实例都将变成包含过时数据的“僵尸”实例。

### 6.2. 目标数据架构：解耦与结构化 (To-Be)

为了克服上述局限，我们必须对数据结构进行重构。核心思想是**彻底解耦**，将产品定义的不同方面分离成独立的、机器可读的、可复用的模块。

#### 6.2.1. 产品结构定义 (Product Structures) - “骨架”
*   **职责:** 只定义产品的层级关系、组件构成以及在可视化画布上的**布局信息**。
*   **示例 (`product-structures.json`):**
    ```json
    {
      "id": "structure_fire_window_standard",
      "name": "标准防火窗结构",
      "components": [
        {
          "instanceId": "outer_frame",
          "componentId": "comp_frame",
          "layout": {
            "x": { "paramId": "window_x" },
            "y": { "paramId": "window_y" },
            "width": { "paramId": "outer_frame_width" },
            "height": { "paramId": "outer_frame_height" }
          },
          "subComponentInstances": ["sash_left", "sash_right"]
        },
        {
          "instanceId": "sash_left",
          "componentId": "comp_sash",
          "layout": {
            "x": { "formulaId": "f_sash_left_x" },
            "y": { "formulaId": "f_sash_y" },
            "width": { "paramId": "sash_width" },
            "height": { "paramId": "sash_height" }
          }
        }
      ]
    }
    ```

#### 6.2.2. 公式库 (Formula Library) - “神经系统”
*   **职责:** 将所有计算逻辑从字符串转化为结构化的**抽象语法树 (Abstract Syntax Tree, AST)**。这使得系统可以轻松地解析、生成和分析公式。
*   **示例 (`product-formulas.json`):**
    ```json
    {
      "f_outer_frame_width": {
        "description": "外框宽度 = 窗宽 + 框厚 * 2",
        "expression": {
          "operator": "add",
          "operands": [
            { "type": "parameter", "id": "window_width" },
            {
              "operator": "multiply",
              "operands": [
                { "type": "parameter", "id": "frame_thickness" },
                { "type": "literal", "value": 2 }
              ]
            }
          ]
        }
      }
    }
    ```

#### 6.2.3. 规则库 (Rule Library) - “大脑”
*   **职责:** 将所有业务规则（选料、约束等）定义为独立的、可复用的实体。
*   **示例 (`product-rules.json`):**
    ```json
    {
      "rule_heavy_hinge": {
        "description": "如果玻璃重量 > 40kg，则必须使用重型合页",
        "type": "MATERIAL_SELECTION",
        "condition": {
          "operator": ">",
          "operands": [
            { "type": "parameter", "id": "glass_weight" },
            { "type": "literal", "value": 40 }
          ]
        },
        "action": {
          "type": "SET_MATERIAL",
          "target": "hinge.materialId",
          "value": "HDG-001"
        }
      }
    }
    ```

#### 6.2.4. 参数定义 (Parameter Definitions) - “控制面板”
*   **职责:** 独立定义所有用户可配置的输入参数及其元数据（如单位、范围、默认值）。
*   **示例 (`product-parameters.json`):**
    ```json
    {
      "window_width": {
        "name": "窗宽",
        "type": "number",
        "unit": "mm",
        "defaultValue": 1500,
        "minValue": 500,
        "maxValue": 3000
      }
    }
    ```

#### 6.2.5. 轻量化配置实例 (Lightweight Configuration)
*   **职责:** 产品配置实例不再是包含计算结果的“死”快照，而是一个**轻量级的指令集**，只存储模板引用和用户输入。
*   **示例 (`product-configurations.json`):**
    ```json
    {
      "id": "config_fire_window_office_001",
      "name": "办公楼标准防火窗配置",
      "template": {
        "structureId": "structure_fire_window_standard",
        "formulasId": "formulas_fire_window_v1",
        "rulesId": "rules_fire_window_v1",
        "parametersId": "params_fire_window_v1"
      },
      "userInput": {
        "window_width": 1500,
        "window_height": 1800,
        "sash_count": 2,
        "fire_rating": "A"
      }
    }
    ```

### 6.3. 新架构的优势

这种“微服务”式的解耦架构是构建下一代系统的基石，它带来了决定性的优势：
-   **支撑可视化:** 通过引入`layout`信息，为“可视化建模器”提供了渲染和操作的依据。
-   **赋能智能交互:** 结构化的公式和规则可以被“交互式规则生成引擎”轻松地创建和修改。
-   **提升性能与可维护性:** 清晰的依赖关系和独立的模块使得实时计算、规则复用和系统维护变得简单高效。
-   **保证数据一致性:** 配置实例只存储用户输入，确保了模板更新后，所有实例都能通过实时计算引擎获得正确的结果，彻底消除了数据过时问题。
