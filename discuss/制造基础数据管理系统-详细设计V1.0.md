# 制造核心数据平台 - 原型阶段详细设计 (V1.0)

**文档版本**: 1.0
**创建日期**: 2025年8月16日
**负责人**: Gemini
**关联PRD**: [制造核心数据平台 PRD (V2.2 - 精确缓冲模型版)](./制造基础数据管理系统PRD_V1.0.md)

---

### 1. 设计概述

#### 1.1 目标
本文档旨在将PRD V2.2的需求转化为可执行的、面向原型开发的技术设计方案。它将定义核心数据实体的结构、数据服务层的接口，以及用户界面的核心交互和组件选型，为前端开发团队提供清晰的实现蓝图。

#### 1.2 设计原则
*   **原型优先 (Prototype First)**: 设计以快速实现、验证核心流程为首要目标。复杂逻辑（如规则引擎）将进行简化处理，重点在于构建数据结构和交互框架。
*   **数据驱动 (Data-Driven)**: 界面和交互完全由结构化的数据模型驱动。
*   **组件化UI (Component-based UI)**: 严格遵循项目既定的技术栈（Vue 3, TypeScript, Pinia, ShadCN），优先复用现有UI组件，确保风格统一和开发高效。

#### 1.3 技术栈假定
*   **前端**: Vue 3 + Vite + TypeScript
*   **状态管理**: Pinia
*   **UI组件库**: ShadCN-Vue
*   **数据来源**: `public/mock/` 目录下的静态JSON文件。

---

### 2. 数据模型设计 (TypeScript Interfaces)

我们将为PRD中定义的核心实体创建精确的TypeScript接口，作为整个应用的数据契约。

```typescript
// src/types/masterdata.ts

// --- 模块一: 制造资源管理 ---

export interface Equipment {
  id: string; // 唯一ID, e.g., "CUT-01"
  name: string; // "1号自动切割机"
  model: string; // "GL-CUT-3000"
  status: 'running' | 'idle' | 'fault' | 'maintenance';
  // 产能参数 (示例，可扩展)
  parameters: {
    max_width?: number;
    max_height?: number;
    speed?: number; // m/min
    area?: number; // m²
    cycle_time?: number; // seconds
  };
}

export interface WorkCenter {
  id: string; // e.g., "WC-CUT"
  name: string; // "自动切割中心"
  equipmentIds: string[]; // 关联的设备ID列表
  calendarId: string; // 关联的产能日历ID
}

// --- 模块二: 产品与物料工程 ---

export interface Material {
  id: string; // SKU, e.g., "GL-RAW-5-CLR"
  name: string; // "5mm白玻原片"
  type: 'raw' | 'semi-finished' | 'finished' | 'auxiliary';
  baseUnit: 'piece' | 'm' | 'm²' | 'kg';
  // 使用Record<string, any>以支持自定义属性集
  attributes: Record<string, any>; // { thickness: 5, color: "clear", width: 3300, height: 2440 }
}

export interface ConfigurationAttribute {
  id: string; // e.g., "width"
  label: string; // "总宽度"
  type: 'number' | 'select' | 'boolean';
  defaultValue: any;
  options?: string[]; // for select type
  rules?: string[]; // 简化的规则字符串, e.g., "min:500,max:3000"
}

export interface ProductFamily {
  id: string; // e.g., "PF-IGU" (Insulated Glass Unit)
  name: string; // "中空玻璃产品族"
  attributes: ConfigurationAttribute[];
  // 依赖规则 (原型阶段简化为字符串描述)
  dependencyRules: string[]; // e.g., "IF(attribute.is_tempered == true) THEN (attribute.edge_type MUST BE 'polished')"
}

// --- 模块三: 分段式工艺工程 ---

export interface ProcessStep {
  id: string; // e.g., "PS-CUT"
  name: string; // "切割"
  type: 'internal' | 'external';
  // 关联，对于internal是workCenterId，external是supplierId
  assigneeIds: string[];
  // 工时计算公式 (原型阶段简化为字符串)
  processingTimeFormula: string; // e.g., "(params.width + params.height) * 2 / 15"
}

export interface WipBuffer {
  id: string; // e.g., "BUF-EDGE-01"
  name: string; // "磨边前缓冲"
  capacity: number; // 可容纳的单位数
  unit: 'piece' | 'rack';
}

export interface ProcessSegmentNode {
  nodeId: string; // 实例ID, e.g., "node-12345"
  type: 'ProcessStep' | 'WipBuffer';
  entityId: string; // 关联的ProcessStep.id或WipBuffer.id
  position: { x: number; y: number }; // 用于UI可视化
}

export interface ProcessSegmentEdge {
  sourceNodeId: string;
  targetNodeId: string;
}

export interface ProcessSegment {
  id:string; // e.g., "SEG-COLD"
  name: string; // "冷加工段"
  nodes: ProcessSegmentNode[];
  edges: ProcessSegmentEdge[];
}

export interface WipWarehouse {
  id: string; // e.g., "WH-WIP-TEMPER"
  name: string; // "钢化前WIP仓"
  locationId?: string; // 关联WMS的库位ID
}

export interface RoutingNode {
  nodeId: string; // 实例ID, e.g., "r-node-abc"
  type: 'ProcessSegment' | 'WipWarehouse';
  entityId: string; // 关联的ProcessSegment.id或WipWarehouse.id
  position: { x: number; y: number };
}

export interface RoutingEdge {
  sourceNodeId: string;
  targetNodeId: string;
}

export interface Routing {
  id: string; // e.g., "RT-IGU-TEMPERED"
  name: string; // "钢化中空玻璃工艺路线"
  productFamilyId: string;
  nodes: RoutingNode[];
  edges: RoutingEdge[];
  version: number;
  isActive: boolean;
}
```

---

### 3. API服务层设计 (Mock Data Service)

在原型阶段，我们将创建一个 `MasterDataService` 来模拟后端API，所有数据从 `public/mock/` 目录读取。

```typescript
// src/services/masterDataService.ts

import { Equipment, WorkCenter, Material, ProductFamily, ProcessStep, WipBuffer, ProcessSegment, WipWarehouse, Routing } from '@/types/masterdata';

class MasterDataService {
  // --- 模块一: 制造资源管理 ---
  async getEquipments(): Promise<Equipment[]> { /* fetch('/mock/equipments.json') */ }
  async getEquipmentById(id: string): Promise<Equipment> { /* ... */ }
  async createEquipment(data: Omit<Equipment, 'id'>): Promise<Equipment> { /* ... */ }
  async updateEquipment(id: string, data: Partial<Equipment>): Promise<Equipment> { /* ... */ }

  async getWorkCenters(): Promise<WorkCenter[]> { /* fetch('/mock/workcenters.json') */ }
  // ... CRUD for WorkCenter

  // --- 模块二: 产品与物料工程 ---
  async getMaterials(): Promise<Material[]> { /* fetch('/mock/materials.json') */ }
  // ... CRUD for Material

  async getProductFamilies(): Promise<ProductFamily[]> { /* fetch('/mock/productFamilies.json') */ }
  // ... CRUD for ProductFamily

  // --- 模块三: 分段式工艺工程 ---
  async getProcessSteps(): Promise<ProcessStep[]> { /* fetch('/mock/processSteps.json') */ }
  // ... CRUD for ProcessStep

  async getProcessSegments(): Promise<ProcessSegment[]> { /* fetch('/mock/processSegments.json') */ }
  // ... CRUD for ProcessSegment

  async getRoutings(): Promise<Routing[]> { /* fetch('/mock/routings.json') */ }
  // ... CRUD for Routing
}

export const masterDataService = new MasterDataService();
```

---

### 4. UI/UX 原型设计

#### 4.1 整体布局
采用项目标准的`Sidebar` + `Header` + `Main Content`布局。在`Sidebar`中为“元数据管理”创建一个顶级菜单项，包含以下子菜单：
*   资源管理
    *   设备管理
    *   工作中心
*   产品工程
    *   物料管理
    *   产品族
*   工艺工程
    *   标准工序
    *   工艺段
    *   工艺路线

#### 4.2 基础数据管理界面 (设备, 物料, 工序等)
*   **主视图**: 使用 **ShadCN `Table`** 组件，以表格形式展示核心信息列表。
    *   **功能**: 支持分页、搜索、筛选。
    *   **操作**: 每行末尾提供“编辑”和“删除”按钮。表格顶部提供“新增”按钮。
*   **编辑/新增视图**:
    *   点击“新增”或“编辑”后，使用 **ShadCN `Dialog`** 弹出模态框。
    *   模态框内部是一个表单，使用 **ShadCN `Input`, `Select`, `Checkbox`** 等组件构建。
    *   表单验证使用 `Vee-validate` + `Zod`。

#### 4.3 可视化编辑器界面 (工艺段, 工艺路线)
这是设计的核心和难点，原型阶段旨在搭建框架。

*   **布局**:
    *   **左侧 (组件面板)**: 一个可折叠的侧边栏，以列表或卡片形式展示所有可用的“节点”（如工序、缓冲区、工艺段）。使用 **ShadCN `Card`** 和 `lucide-vue-next` 图标。
    *   **中间 (画布)**: 主要的拖拽区域，用于构建流程图。可以使用 `VueFlow` 或 `d3.js` 等库的简单封装。
    *   **右侧 (属性面板)**: 当选中画布上的一个节点或连线时，此面板出现，显示并允许编辑该元素的详细属性（如工序的工时公式、缓冲区的容量）。
*   **交互流程**:
    1.  用户从左侧组件面板拖拽一个节点（如“切割”工序）到中间的画布上。
    2.  画布上生成一个对应的图形节点。
    3.  用户可以继续拖拽其他节点，并通过点击节点的连接点，拖出一条线连接到另一个节点。
    4.  点击画布上的任意节点，右侧属性面板会显示该节点的详细信息，并提供表单供用户编辑。
    5.  页面右上角提供“保存”和“发布新版本”按钮。

---

### 5. Mock 数据策略

1.  在 `public/mock/` 目录下创建新文件夹 `masterdata`。
2.  根据第2节定义的TypeScript接口，创建对应的JSON文件：`equipments.json`, `workCenters.json`, `materials.json`, `productFamilies.json`, `processSteps.json`, `processSegments.json`, `routings.json` 等。
3.  每个文件中至少包含3-5条符合业务逻辑的示例数据，以支撑UI开发和测试。
4.  示例数据需要满足行业特性；

---

### 6. 原型实施路线图 (建议)

1.  **阶段一 (数据基础)**:
    *   完成 `src/types/masterdata.ts` 中所有接口的定义。
    *   创建所有mock JSON文件并填充基础数据。
    *   实现 `src/services/masterDataService.ts`，完成所有数据的读取功能。
2.  **阶段二 (基础CRUD界面)**:
    *   搭建“主数据管理”的整体菜单和路由。
    *   使用ShadCN组件，完成所有非可视化模块（设备、物料、工序等）的表格展示和弹窗表单的CRUD功能。
3.  **阶段三 (可视化编辑器框架)**:
    *   技术选型并集成一个基础的流程图库（如VueFlow）。
    *   实现“工艺段管理”和“工艺路线管理”的编辑器三栏布局。
    *   实现从左侧面板拖拽节点到画布的功能。
    *   实现选中节点后，在右侧显示其基本信息的功能（暂不实现编辑）。
4.  **阶段四 (编辑器功能深化)**:
    *   实现节点间的连线功能。
    *   实现右侧属性面板的表单编辑与数据双向绑定。
    *   实现最终的“保存”功能，将画布上的节点和边线数据结构化，并更新到Pinia状态和mock服务中。

