# 制造核心数据平台 PRD (V2.2 - 完整最终版)

**文档版本**: 2.2
**创建日期**: 2025年8月16日
**负责人**: Gemini
**战略目标**: 本文档定义了**世界级全流程智能管理软件 (ERP+MES)** 的核心数字基石。V2.2版本精确区分了**段内缓冲 (Intra-Segment Buffer)**与**段间仓储 (Inter-Segment Warehouse)**，构建了更逼近物理现实的、支持流量平衡与生产解耦的先进制造模型。

---

### 1. 系统概述

本系统是集团级智能制造体系的**核心数据平台 (Master Data Platform)**。它负责将物理世界的制造要素——**设备、物料、产品、工序、工艺**——转化为支持**大规模参数化定制**的、可扩展的数字模型。它为上层的ERP、MES、APS、WMS等应用系统提供统一、准确、唯一的“事实来源 (Single Source of Truth)”。

**核心构成**:
1.  **制造资源管理**: 定义“在哪里生产”以及“生产能力如何”。
2.  **产品与物料工程**: 定义“生产什么”，并支持从“产品族”到“唯一项目/订单实例”的转化。
3.  **分段式工艺工程**: 定义“如何生产”，并精确描述包含**内部缓冲**与**中间仓储**的制造流程。

---

### 2. 模块一：制造资源管理

#### 2.1 模块概述
本模块负责建立工厂所有生产及辅助资源的数字档案，是产能评估和资源调度的基础。

#### 2.2 核心实体
*   **设备 (Equipment)**: 物理上的机器，如切割机、钢化炉。
*   **工作中心 (Work Center)**: 一个或多个功能相同的设备的逻辑分组，是排产的基本单位。

#### 2.3 功能需求

*   **2.3.1 设备档案 (CRUD)**
    *   **基本信息**: 唯一设备ID、名称、型号、资产编号、位置。
    *   **状态管理**: 可定义设备状态（如：生产、待机、故障、维护、停用）。
    *   **产能参数**: 支持**公式化或多维度的产能定义**。
        *   **示例**: 切割机(最大加工尺寸, 速度)、钢化炉(最大炉区面积, 炉次时间)、CNC加工中心(刀库, 转速)。
    *   **维护信息**: 关联维护计划、记录维护历史。

*   **2.3.2 工作中心管理 (CRUD)**
    *   **创建与关联**: 创建工作中心，并将功能相同的设备关联进来。
    *   **产能日历**: 为每个工作中心配置工作日历，包括班次、工作时间、法定假日、计划性停机等。这是计算可用产能的基础。

---

### 3. 模块二：产品与物料工程 (泛行业增强)

#### 3.1 模块概述
本模块负责定义所有生产、采购、销售的对象。为支持泛行业MTO/ETO模式，它构建了一个从**通用物料 -> 可配置产品族 -> 唯一订单实例**的灵活数据体系。

#### 3.2 核心实体
*   **物料 (Material)**: 系统中所有**标准物理对象**的统称，如玻璃原片、铝型材、五金件、螺丝。
*   **产品族 (Product Family)**: 一个可配置的产品框架，例如“防火窗”、“系统门窗”、“玻璃隔断”。它包含了一系列可供客户选择的**配置属性**和**组件选项**。
*   **项目/订单实例 (Project/Order Instance)**: 当一个销售订单或工程项目被创建时，系统基于某个“产品族”和客户指定的具体参数，生成的一个**临时的、唯一的、带有完整配置信息的制造对象**。

#### 3.3 功能需求

*   **3.3.1 物料主数据 (CRUD)**
    *   **用途**: 管理所有**标准库存/采购物料**。
    *   **通用属性**: 物料编码、名称、规格描述、基本单位、供应商信息。
    *   **行业属性扩展**: 支持为不同物料类别定义**自定义属性集**。
        *   **玻璃类**: 厚度、长、宽、颜色、透光率。
        *   **型材类**: 截面图号、材质、米重、定尺长度。
        *   **五金类**: 品牌、型号、开启方式。

*   **3.3.2 产品族管理**
    *   **族创建**: 创建产品族，如“80系列内开内倒窗”。
    *   **配置属性定义**: 为每个族定义一系列**配置属性**，并设定其**规则和约束**。
        *   **示例属性**: `总高度`, `总宽度`, `玻璃配置方案`, `执手样式`, `表面处理颜色`。
        *   **规则与约束**:
            *   `总高度`: 类型(数值), 范围(800-2400mm)。
            *   `执手样式`: 类型(列表), 可选项(品牌A-001, 品牌B-002)。
            *   **依赖规则**: `IF(开启方式 == '内开内倒') THEN (执手样式 MUST BE IN [品牌A-001, 品牌A-003])`。

*   **3.3.3 参数化BOM (Parametric BOM) 管理**
    *   **关联到产品族**: BOM关联到**“产品族”**。
    *   **多态与可选组件**:
        *   **用量公式化**: 组件用量基于**配置属性**的**公式**。例如，窗扇型材长度 = `(属性.总高度 - 50) * 2`。
        *   **可选组件 (Optional Component)**: 支持定义可选的BOM行。`IF(属性.带纱窗 == true) THEN (包含物料: [纱窗型材, 纱网])`。
    *   **动态生成**: 当“项目/订单实例”被创建时，系统调用参数化BOM并代入参数，**实时计算**出该订单**唯一、精确**的物料清单。

---

### 4. 模块三：分段式工艺工程 (精确缓冲模型)

#### 4.1 模块概述
本模块采用预定义工艺路线模型，但通过精确区分**“WIP缓冲区”**和**“生产中转仓”**，实现了对复杂生产场景的真实映射。

#### 4.2 核心实体
*   **工序 (Process Step)**: 制造的最小执行单元。
*   **WIP缓冲区 (Buffer)**: **(重新定义)** 用于**平衡工艺段内部、工序之间流量**的逻辑区域，通常容量有限，用于解决节拍不匹配问题。
*   **工艺段 (Process Segment)**: 由一个或多个**工序**及它们之间的**WIP缓冲区**组成的逻辑集合。代表一个相对独立的、内部流程紧密耦合的生产阶段。
*   **生产中转仓 (WIP Warehouse)**: **(新增核心实体)** 用于**解耦不同工艺段**的、可管理的**库存单元**。物料在此处为正式的在制品库存状态。
*   **工艺路线 (Routing)**: 由一个或多个有序的**“工艺段”**通过**“生产中转仓”**连接而成的完整生产流程。

#### 4.3 功能需求

*   **4.3.1 工序管理 (Process Step Management)**
    *   **工序库 (CRUD)**: 创建和维护标准工序库。
        *   **基本信息**: 唯一工序ID、名称（如：切割、CNC加工、组角、喷涂、包装）。
        *   **工序类型**: 增加**“内部加工”**与**“外协加工”**的类型区分。
        *   **资源关联**: “内部加工”关联**工作中心**；“外协加工”关联**合格供应商列表**。
        *   **标准工时/周期**: 内部工时支持**参数化计算**；外协工序定义为**标准采购周期**（天）。

*   **4.3.2 工艺段管理 (Process Segment Management) (重构)**
    *   **可视化编辑器**: 提供一个图形化界面，允许用户在一个“工艺段”的画布内：
        1.  从**工序库**中拖拽工序节点（如`切割`、`磨边`）。
        2.  定义**WIP缓冲区**节点（可设定容量、类型等属性）。
        3.  通过连线，按 `[工序]->[缓冲]->[工序]` 的逻辑，编排段内紧密耦合的生产流。
    *   **示例 (“冷加工段”的定义)**:
        *   `[工序: 自动切割]` -> `[缓冲: 磨边前缓冲]` -> `[工序: 双边磨#1]`
        *   (从`磨边前缓冲`分流) -> `[工序: 双边磨#2]`

*   **4.3.3 工艺路线管理 (Routing Management) (重构)**
    *   **关联到产品族**: 工艺路线关联到**“产品族”**。
    *   **可视化编辑器**: 提供一个更高层级的图形化界面，允许用户：
        1.  从已定义的**“工艺段”**库中拖拽节点（如`冷加工段`）。
        2.  定义**“生产中转仓”**节点（可关联到WMS的库位）。
        3.  通过连线，将它们按 `[工艺段]->[中转仓]->[工艺段]` 的逻辑顺序连接起来。
    *   **示例 (钢化中空玻璃路线)**:
        *   `[工艺段: 冷加工]` -> `[中转仓: 钢化前WIP仓]` -> `[工艺段: 钢化]` -> `[中转仓: 合片前WIP仓]` -> `[工艺段: 合片]` -> `[成品仓]`
    *   **版本与生命周期管理**: 对工艺路线的任何修改都必须创建新版本。只有“生效”状态的路线才能被新订单使用。

*   **4.3.4 质量控制点集成**
    *   **功能**: 在**工序**级别上，标记为“质量控制点(QCP)”，并关联“质量检验标准”。
    *   **触发机制**: 在MES中自动触发强制质检任务。

### 5. 依赖与集成

*   **上游**: 强依赖 **ERP/CRM/项目管理系统**，需传入完整的**产品配置参数**。
*   **下游**: 为 **MES、APS、WIP管理、采购管理、成本管理** 提供结构化的分段式BOM和工艺路线。
*   **横向**: 与 **质量管理、供应商管理、WMS(仓库管理系统)** 模块紧密集成。

---