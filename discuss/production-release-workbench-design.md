# “生产发布工作台”功能设计讨论稿

- **版本**: 2.0
- **日期**: 2025-08-21
- **作者**: Gemini
- **状态**: 最终版

---

## 1. 背景与目标

### 1.1. 背景

在对 `ProductionOrderManagement.vue` 视图的审查中，我们发现原有的“交付管理”功能命名与其实际业务场景存在偏差。经过深入讨论，我们明确了用户的核心需求并非一个生产后的物流管理模块，而是一个在 **工单创建之后、下发车间之前** 的，面向 **生产计划员** 的核心决策与调度中心。

MTO（按订单生产）模式下的玻璃深加工业务，面临着物料约束、产能瓶颈、工艺复杂多变以及优化机会稍纵逝等挑战。生产计划员需要一个高效、集成的工具来应对这些挑战，确保每一个投产的工单都是经过深思熟虑且具备高可行性的。

### 1.2. 目标

本项目旨在设计并实现一个 **“生产发布工作台” (Production Release Workbench)**，取代原有的“交付管理”概念。其核心目标是：

- **赋能决策**：为生产计划员提供一站式的信息视图和操作平台，支持他做出快速、准确的工单发布决策。
- **提升效率**：整合物料、产能、工艺及优化信息，避免计划员在多个系统或报表之间频繁切换。
- **降低风险**：通过前置的可行性分析，提前暴露物料短缺、产能冲突等问题，减少车间执行过程中的异常。
- **提高效益**：通过智能化的合并优化建议，提升原片利用率等关键生产指标。

---

## 2. 核心设计理念

- **一站式 (Single Pane of Glass)**: 计划员应在此界面完成90%的发布前决策，无需跳转。
- **情境感知 (Context-Aware)**: 所有信息都必须围绕当前正在处理的工单展开，提供高度相关的决策支持。
- **决策导向 (Decision-Driven)**: 界面不只是展示数据，而是要将数据转化为“建议”和“可执行的操作”，引导计划员完成最优决策。
- **可扩展性 (Scalability)**: 当前设计需为未来的高级功能（如APS精细排程引擎）预留无缝集成的接口和空间。

---

## 3. 功能模块与用户流程

当生产计划员在主界面点击一个状态为 `待发布 (Pending)` 的工单上的“处理”或“发布准备”按钮时，系统将弹出“生产发布工作台”模态对话框。整个工作台的用户流程遵循以下四个逻辑步骤：

### 3.1. 步骤一：工单构成审查 (Review)

**目标**：让计划员在30秒内对工单的“WHAT”建立完整、准确的认知。

- **关联订单项聚合视图**:
    - 以列表形式清晰展示构成此工单的所有 **订单项**。
    - 每一项都必须明确追溯其来源，格式为：`[客户名称] - [客户订单号] - [订单项号]`。
    - 关键信息包括：产品规格、产品族、数量、客户要求的交付日期。
- **技术规格与工艺确认**:
    - **BOM校验**: 提供展开/折叠功能，显示每个订单项的核心物料清单（BOM），如原片规格、胶片类型、五金件等。
    - **工艺路线确认**: 清晰展示系统根据产品族自动匹配的工艺路线（如：切割 -> 磨边 -> 钢化 -> 质检），并允许计划员进行核对。

### 3.2. 步骤二：生产可行性分析 (Analyze)

**目标**：帮助计划员判断“现在能不能做？”。

- **3.2.1. 关键物料可用性检查**:
    - 在BOM清单的每一物料行旁，实时显示 `需求量`、`当前库存`、`缺口`。
    - 当 `缺口 > 0` 时，该行高亮并标记为 **[短缺]**。
    - **交互设计**:
        - **[短缺]** 标签旁提供一个 **[处理]** 按钮。
        - 点击 **[处理]**，弹出一个小型对话框，提供操作选项：`[创建采购建议]` 或 `[加入下次MRP运算]`。
        - 操作完成后，系统在后台创建关联单据，并将按钮状态更新为“已建议采购”，实现操作闭环。

- **3.2.2. 产能负荷评估**:
    - **当前实现 (粗略估算)**:
        - 采用简洁的条形图或仪表盘，可视化展示关键工作中心（如：切割机、钢化炉、中空合片线）未来一周的 **“产能负荷率(%)”**。
        - 负荷率根据 `(已发布工单的总标准工时) / (工作中心总可用工时)` 计算，为计划员提供宏观决策依据。
    - **未来扩展 (APS接口占位)**:
        - 在产能图表下方，预留一个明确的占位区域，标题为 **“APS精细排程模拟 (高级功能)”**。
        - 提示文字：“*连接APS引擎后，可在此处查看将此工单插入当前计划后的详细甘特图、预计完成时间（精确到小时）及潜在瓶颈预警。*”
        - 此设计确保了未来功能扩展时前端界面的平滑过渡。

### 3.3. 步骤三：合并优化决策 (Optimize)

**目标**：帮助计划员发现并执行“能做得更好吗？”的优化机会。

- **智能优化建议**:
    - 系统自动扫描所有“待发布”的工单，如果发现可优化的机会（如相同材质、厚度的玻璃可以合并套料），则在工作台顶部主动推送一个高亮建议卡片。
    - 示例文字：“**优化建议：发现其他2个待发布工单中的5个订单项可与当前工单合并进行切割，预计可提升原片利用率5%。**”
- **一键合并执行**:
    - 建议卡片上提供 **[查看详情与执行合并]** 按钮。
    - 点击后，展开一个清晰的对比视图，展示将被并入的订单项及其来源信息（原工单号、客户、交期）。
    - 提供 **[确认合并]** 按钮。点击后，系统执行后台事务操作：
        1. 将被合并的订单项从其原工单中移除。
        2. 将这些订单项添加到当前工单中。
        3. 实时更新所有受影响工单的聚合数据（总数量、总工时等）。
        4. 界面同步刷新，新并入的项在当前工单列表中出现，并带有特殊标记（如“已从WO-XXX并入”）。

### 3.4. 步骤四：决策与执行 (Act)

**目标**：完成最终的调度确认，处理异常或正式发布。

- **3.4.1. 计划微调与发布**:
    - 系统根据分析结果，给出一个建议的 `计划开始日期` 和 `计划完成日期`。
    - 允许计划员根据实际情况（如等待物料）对这两个日期进行微调。
    - 提供最终的 **[发布到车间]** 按钮。点击后，工单状态由 `pending` 变为 `released`，并正式进入车间执行队列。

- **3.4.2. 异常处理流程**:
    - **系统识别**: 后台服务每日扫描，识别并标记“待发布”状态超过预设阈值（如3天）的工单。
    - **主动通知**: 将超时工单作为高优先级待办事项推送给计划员，并说明主要瓶颈（如“原片短缺”）。
    - **界面预警**: 计划员打开超期工单的工作台时，顶部会出现醒目的警告横幅。
    - **决策操作**: 警告横幅旁提供明确的处置按钮，如：
        - `[标记为挂起]`: 暂时将工单移出常规列表，并要求填写挂起原因。
        - `[通知销售协调交期]`: 触发一个通知给相关的销售或跟单员，请求与客户沟通。

---

## 4. 业务规则与处理逻辑

根据最终讨论，明确以下业务规则以指导开发：

- **4.1. 合并优化的边界**:
    - 当一个工单的所有订单项都通过“合并优化”操作被转移至其他工单，导致其变为空工单时，系统应 **自动将其状态更新为“已作废”(Cancelled)**，并记录作废原因为“优化合并”。

- **4.2. 采购建议的粒度**:
    - 当计划员针对短缺物料点击“创建采购建议”时，系统仅需在后台创建一个 **简单的物料需求记录**。该记录应包含：`物料编码`、`需求数量`、`期望到货日期`、`关联生产工单号`。此记录将进入采购部门的待办池，由采购专员负责后续创建正式的采购订单，无需计划员介入更多细节。

- **4.3. 权限管理**:
    - 在当前版本中， **所有拥有“生产计划员”角色的用户，均有权限执行“合并优化”操作**。系统暂不对此操作做更细化的权限区分。后续版本可根据业务发展需要，考虑引入更复杂的审批或通知流程。

---

## 5. 数据交互与关联

为实现上述功能，本工作台需要与以下模块进行实时、紧密的数据交互：

- **库存管理系统**: 获取实时库存数据，预留/冻结物料。
- **采购管理系统**: 创建采购建议，查询采购订单状态。
- **客户订单 (CRM/ERP)**: 追溯订单来源，回写生产状态。
- **主数据管理**: 获取BOM、工艺路线、工作中心、物料等基础数据。
- **车间执行 (MES Core)**: 接收发布的工单，开始执行。