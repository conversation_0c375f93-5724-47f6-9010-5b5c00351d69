# 智能排产系统产品需求文档 (PRD) - V1.1

**文档版本**: 1.1
**创建日期**: 2025年8月16日
**负责人**: Gemini
**更新日志**:
- V1.0: 基于总体设计方案的初始版本，聚焦自动化排产与行业特性优化。
- V1.1: 整合“人工干预微调功能讨论稿”，引入“人机协同”理念，增加交互式微调与实时影响分析功能。

---

### 1. 产品概述

#### 1.1 背景

本模块是玻璃深加工MES系统的核心大脑，旨在解决“多品种、小批量”生产模式下的排产复杂性。本系统采用**“人机协同”**的设计理念，将强大的自动化算法与计划员的专业经验相结合，通过提供一个高质量的计划草案并赋予计划员直观、强大的微调工具，实现理论最优与现实最优的统一。

#### 1.2 产品目标

1.  **提升生产效率**: 通过算法驱动的资源分配和瓶颈工序（如钢化炉）的深度优化，将设备综合效率(OEE)提升 **≥10%**。
2.  **优化材料成本**: 针对玻璃原片进行精密的切割优化，将材料利用率提升 **≥5%**。
3.  **保障订单交期**: 创建精准、可靠的生产计划，并具备对紧急插单、生产异常的快速响应和重排能力，将交期达成率提升至 **≥95%**。
4.  **赋能计划决策**: 将计划员从繁琐的手工排产中解放出来，转变为计划的“指挥官”，利用系统提供的决策支持信息，做出更优的、更符合即时业务需求的调整。

### 2. 用户画像与核心场景

*   **核心用户**: 生产计划员
*   **场景一：日常人机协同排产**
    1.  **生成草案**: 计划员从“待排产工单池”中选择一批工单，点击“一键智能排产”。
    2.  **审查微调**: 系统在数分钟内生成一个最优计划草案，并呈现在交互式甘特图上。计划员基于经验，将一个VIP客户的订单拖拽提前。
    3.  **获取反馈**: 系统立刻高亮显示被影响的另一个订单，并提示“此操作将导致订单[SO-XXX]延期3小时”。
    4.  **决策确认**: 计划员评估后认为可接受，或选择将另一个非紧急订单延后以腾出时间。完成所有调整后，锁定关键任务。
    5.  **确认下发**: 计划员确认“最终版”计划，系统将其下发至车间执行。
*   **场景二：紧急插单**
    *   销售下达紧急订单，计划员将其加入队列并标记“紧急”。系统自动重排，并将该订单插入最优位置。计划员在甘特图上检查其对现有计划的冲击，并可手动微调周边任务，以最小化影响。
*   **场景三：生产异常（玻璃破损）**
    *   车间上报破损，系统自动生成最高优先级的“补片工单”。该工单自动出现在计划员的甘特图上，作为一个“待插入”的悬浮任务。计划员可以将其拖拽到最合适的生产批次中，例如一个材质厚度相同的批次，以减少浪费。

### 3. 功能需求

#### 3.1 工单管理与解析

*   **3.1.1 工单接收**: 自动从ERP接收生产工单，包含客户、产品规格、数量、BOM、工艺路线、期望交期等信息。
*   **3.1.2 工单分解**: 自动将复合产品分解为多个可独立生产的“组件任务”。
*   **3.1.3 优先级管理**: 支持为工单设置不同优先级（如：紧急、高、中、低），并能被计划员手动调整。

#### 3.2 自动化排产引擎

*   **3.2.1 多约束条件输入**: 综合考虑时间、资源、物料、工艺四大核心约束。
*   **3.2.2 多目标优化**: 支持以交期、成本、设备负荷等多目标进行加权优化。
*   **3.2.3 排产结果输出**: 输出以“生产批次”为单位的详细生产计划草案。

#### 3.3 玻璃行业专用优化引擎

*   **3.3.1 切割优化 (Nesting Optimization)**
    *   **功能**: 自动为切割批次计算材料利用率最大化的排版方案，并为每片小片生成唯一ID。支持余料管理。
*   **3.3.2 摆炉优化 (Tempering Loading Optimization)**
    *   **功能**: 在钢化前，从WIP缓冲池中动态选取玻璃，计算单炉面积利用率最大化的摆放方案，并生成唯一的“钢化炉次号”和“摆炉指导图”。

#### 3.4 人工干预与微调 (Human-in-the-Loop Fine-Tuning)

*   **3.4.1 交互式生产甘特图 (Interactive Production Gantt Chart)**
    *   **基础**: 以可视化的甘特图展示所有生产批次在设备和时间轴上的分布。
    *   **拖拽调整**:
        *   **调整时间**: 允许在时间轴上拖动“生产批次”以提前或延后。
        *   **更换资源**: 允许将“生产批次”从一台设备拖到另一台同类型设备上。
    *   **上下文操作菜单**:
        *   **锁定计划 (Lock)**: 被锁定的批次在后续自动重排中保持不变。
        *   **修改优先级 (Change Priority)**: 手动提升或降低批次优先级。
        *   **拆分批次 (Split)**: 将大批次拆分为小批次，以插入紧急任务。
        *   **强制合并 (Merge)**: 手动合并工艺、物料相似的批次。

*   **3.4.2 实时影响分析器 (Real-time Impact Analyzer)**
    *   **冲突告警**: 当拖拽操作导致资源冲突（如设备超负荷）时，目标区域应高亮红色并禁止放置。
    *   **交期影响预警**: 当一个操作可能导致其他订单延期时，系统立刻弹出非阻塞式提示：“此操作将导致订单 [SO-XXX] 预计延期4小时”。
    *   **成本变化提示**: 当操作导致成本显著变化时，给出量化提示。

#### 3.5 计划确认与下发

*   **3.5.1 计划版本管理**: 保存每次重大调整的历史版本，支持对比和回滚。
*   **3.5.2 确认下发**: 计划员确认最终计划后，系统将任务列表锁定，并按工位下发至对应的车间执行终端。

### 4. 非功能性需求

*   **性能**: 完整“一键智能排产”计算时间应在 **3分钟** 以内。人工微调的实时反馈响应时间应在 **1秒** 以内。
*   **易用性**: 甘特图的交互应流畅、直观，符合主流UI操作习惯，学习成本低。
*   **集成性**: 与ERP、WMS、车间执行终端、IoT数据服务等模块建立稳定、高效的数据接口。
*   **可配置性**: 优化目标的权重、工艺约束规则、实时分析的触发阈值等应支持后台灵活配置。

### 5. 依赖与约束

*   依赖于准确的BOM、工艺路线、设备产能等基础数据。
*   依赖于WMS/库存模块提供实时的原片库存信息。
*   依赖于IoT集成服务提供WIP缓冲池的实时在制品数据，以支持精准的摆炉优化。
