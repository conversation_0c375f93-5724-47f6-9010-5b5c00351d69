{
  "extends": "./tsconfig.app.json",
  "compilerOptions": {
    // 原型开发专用 - 极度宽松模式
    "noEmit": true,
    "allowJs": true,
    "checkJs": false,
    "noImplicitAny": false,
    "skipLibCheck": true,
    "suppressImplicitAnyIndexErrors": true,
    "suppressExcessPropertyErrors": true,
    
    // 关闭所有严格检查
    "strict": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": false,
    "noUncheckedIndexedAccess": false,
    
    // 允许所有类型操作
    "allowUnreachableCode": true,
    "allowUnusedLabels": true,
    "noImplicitOverride": false,
    
    // 模块解析宽松
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "resolveJsonModule": true
  }
}