// 设备能力匹配和智能排产支持服务

import type { 
  EquipmentProcessingCapability, 
  OrderItemRequirement, 
  EquipmentMatchResult,
  SchedulingDecisionData,
  EquipmentCapabilityConstraint,
  WorkCenterCapability
} from '@/types/equipmentCapability';
import type { Equipment, WorkCenter } from '@/types/masterdata';

/**
 * 设备能力匹配服务
 */
export class EquipmentCapabilityService {
  
  /**
   * 为订单项匹配最佳设备
   */
  static async matchEquipmentForOrderItem(
    orderItemRequirement: OrderItemRequirement,
    workCenterId: string
  ): Promise<EquipmentMatchResult[]> {
    
    // 获取工作中心的设备能力数据
    const workCenterCapability = await this.getWorkCenterCapability(workCenterId);
    const results: EquipmentMatchResult[] = [];
    
    for (const equipmentCapability of workCenterCapability.equipmentCapabilities) {
      const matchResult = await this.evaluateEquipmentMatch(
        orderItemRequirement,
        equipmentCapability
      );
      results.push(matchResult);
    }
    
    // 按综合评分排序
    return results.sort((a, b) => b.overallScore - a.overallScore);
  }
  
  /**
   * 评估单个设备与订单项的匹配度
   */
  static async evaluateEquipmentMatch(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability
  ): Promise<EquipmentMatchResult> {
    
    // 1. 约束检查
    const constraintChecks = await this.checkConstraints(requirement, capability);
    const capabilityScore = this.calculateCapabilityScore(constraintChecks);
    
    // 2. 效率评分
    const efficiencyScore = this.calculateEfficiencyScore(requirement, capability);
    
    // 3. 成本评分
    const costScore = this.calculateCostScore(requirement, capability);
    
    // 4. 可用性评分
    const availabilityScore = await this.calculateAvailabilityScore(capability.equipmentId);
    
    // 5. 综合评分
    const overallScore = this.calculateOverallScore({
      capabilityScore,
      efficiencyScore,
      costScore,
      availabilityScore
    });
    
    // 6. 时间和成本预测
    const estimatedProcessingTime = this.estimateProcessingTime(requirement, capability);
    const estimatedSetupTime = this.estimateSetupTime(requirement, capability);
    const estimatedCost = this.estimateCost(requirement, capability);
    
    // 7. 质量风险评估
    const qualityRisk = this.assessQualityRisk(requirement, capability);
    
    // 8. 可用时间窗口
    const availableTimeSlots = await this.getAvailableTimeSlots(capability.equipmentId);
    
    // 9. 推荐原因和限制因素
    const recommendationReasons = this.generateRecommendationReasons(
      requirement, capability, capabilityScore, efficiencyScore
    );
    const limitations = this.identifyLimitations(constraintChecks);
    
    return {
      equipmentId: capability.equipmentId,
      equipmentName: await this.getEquipmentName(capability.equipmentId),
      overallScore,
      capabilityScore,
      efficiencyScore,
      costScore,
      availabilityScore,
      constraintChecks,
      estimatedProcessingTime,
      estimatedSetupTime,
      estimatedCost,
      qualityRisk,
      availableTimeSlots,
      recommendationReasons,
      limitations
    };
  }
  
  /**
   * 检查设备约束
   */
  static async checkConstraints(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability
  ) {
    const checks = [];
    
    for (const constraint of capability.constraints) {
      const check = await this.checkSingleConstraint(requirement, constraint);
      checks.push(check);
    }
    
    return checks;
  }
  
  /**
   * 检查单个约束
   */
  static async checkSingleConstraint(
    requirement: OrderItemRequirement,
    constraint: EquipmentCapabilityConstraint
  ) {
    let passed = true;
    let actualValue: number | undefined;
    let requiredValue: number | undefined;
    let message = '';
    
    switch (constraint.constraintType) {
      case 'size':
        if (constraint.dimension === 'length') {
          actualValue = requirement.dimensions.length;
          requiredValue = constraint.maxValue;
          passed = !constraint.maxValue || actualValue <= constraint.maxValue;
          message = passed 
            ? `长度 ${actualValue}mm 符合要求 (≤${requiredValue}mm)`
            : `长度 ${actualValue}mm 超出限制 (≤${requiredValue}mm)`;
        } else if (constraint.dimension === 'width') {
          actualValue = requirement.dimensions.width;
          requiredValue = constraint.maxValue;
          passed = !constraint.maxValue || actualValue <= constraint.maxValue;
          message = passed 
            ? `宽度 ${actualValue}mm 符合要求 (≤${requiredValue}mm)`
            : `宽度 ${actualValue}mm 超出限制 (≤${requiredValue}mm)`;
        } else if (constraint.dimension === 'area') {
          actualValue = requirement.dimensions.area;
          requiredValue = constraint.maxValue;
          passed = !constraint.maxValue || actualValue <= constraint.maxValue;
          message = passed 
            ? `面积 ${actualValue}m² 符合要求 (≤${requiredValue}m²)`
            : `面积 ${actualValue}m² 超出限制 (≤${requiredValue}m²)`;
        }
        break;
        
      case 'thickness':
        actualValue = requirement.dimensions.thickness;
        requiredValue = constraint.maxValue;
        passed = !constraint.maxValue || actualValue <= constraint.maxValue;
        message = passed 
          ? `厚度 ${actualValue}mm 符合要求 (≤${requiredValue}mm)`
          : `厚度 ${actualValue}mm 超出限制 (≤${requiredValue}mm)`;
        break;
        
      case 'edge_type':
        passed = !constraint.allowedValues || constraint.allowedValues.includes(requirement.edgeType);
        message = passed 
          ? `边型 ${requirement.edgeType} 支持`
          : `边型 ${requirement.edgeType} 不支持，支持类型: ${constraint.allowedValues?.join(', ')}`;
        break;
        
      case 'material':
        passed = !constraint.allowedValues || constraint.allowedValues.includes(requirement.materialType);
        message = passed 
          ? `材料 ${requirement.materialType} 支持`
          : `材料 ${requirement.materialType} 不支持，支持类型: ${constraint.allowedValues?.join(', ')}`;
        break;
    }
    
    return {
      constraintId: constraint.id,
      constraintType: constraint.constraintType,
      passed,
      actualValue,
      requiredValue,
      message
    };
  }
  
  /**
   * 计算能力匹配评分
   */
  static calculateCapabilityScore(constraintChecks: any[]): number {
    const totalConstraints = constraintChecks.length;
    if (totalConstraints === 0) return 100;
    
    const passedConstraints = constraintChecks.filter(check => check.passed).length;
    const hardConstraintsFailed = constraintChecks.filter(
      check => !check.passed && check.isHardConstraint
    ).length;
    
    // 硬约束失败直接返回0
    if (hardConstraintsFailed > 0) return 0;
    
    // 软约束按比例计算
    return Math.round((passedConstraints / totalConstraints) * 100);
  }
  
  /**
   * 计算效率评分
   */
  static calculateEfficiencyScore(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability
  ): number {
    // 基于尺寸适配度计算效率评分
    const { dimensions } = requirement;
    const { sizeOptimalRange } = capability;
    
    let sizeScore = 100;
    
    // 长度适配度
    if (dimensions.length < sizeOptimalRange.lengthMin || 
        dimensions.length > sizeOptimalRange.lengthMax) {
      sizeScore -= 20;
    }
    
    // 宽度适配度
    if (dimensions.width < sizeOptimalRange.widthMin || 
        dimensions.width > sizeOptimalRange.widthMax) {
      sizeScore -= 20;
    }
    
    // 批量适配度
    const batchScore = this.calculateBatchScore(requirement.quantity, capability);
    
    // 综合效率评分
    return Math.round((sizeScore * 0.6 + batchScore * 0.4) * (capability.efficiency / 100));
  }
  
  /**
   * 计算批量评分
   */
  static calculateBatchScore(quantity: number, capability: EquipmentProcessingCapability): number {
    if (quantity >= capability.batchSizeOptimal * 0.8 && 
        quantity <= capability.batchSizeOptimal * 1.2) {
      return 100;
    } else if (quantity >= capability.batchSizeMin && 
               quantity <= capability.batchSizeMax) {
      return 80;
    } else {
      return 50;
    }
  }
  
  /**
   * 计算成本评分
   */
  static calculateCostScore(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability
  ): number {
    // 简化的成本评分，实际应该基于详细的成本模型
    const baseCostScore = 100 - (capability.operatingCost / 100) * 10;
    const setupCostImpact = capability.setupCost > 500 ? -10 : 0;
    
    return Math.max(0, Math.round(baseCostScore + setupCostImpact));
  }
  
  /**
   * 计算可用性评分
   */
  static async calculateAvailabilityScore(equipmentId: string): Promise<number> {
    // 模拟设备可用性计算
    // 实际应该基于设备当前状态、维护计划、已排产任务等
    const mockAvailability = Math.random() * 40 + 60; // 60-100%
    return Math.round(mockAvailability);
  }
  
  /**
   * 计算综合评分
   */
  static calculateOverallScore(scores: {
    capabilityScore: number;
    efficiencyScore: number;
    costScore: number;
    availabilityScore: number;
  }): number {
    const weights = {
      capability: 0.4,
      efficiency: 0.3,
      cost: 0.2,
      availability: 0.1
    };
    
    return Math.round(
      scores.capabilityScore * weights.capability +
      scores.efficiencyScore * weights.efficiency +
      scores.costScore * weights.cost +
      scores.availabilityScore * weights.availability
    );
  }
  
  /**
   * 预估加工时间
   */
  static estimateProcessingTime(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability
  ): number {
    // 基于周长和速度计算基础时间
    const perimeter = requirement.dimensions.perimeter;
    const baseTime = (perimeter / capability.standardSpeed) * 60; // 转换为分钟
    
    // 考虑数量
    const totalTime = baseTime * requirement.quantity;
    
    // 考虑效率系数
    return Math.round(totalTime / (capability.efficiency / 100));
  }
  
  /**
   * 预估换线时间
   */
  static estimateSetupTime(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability
  ): number {
    // 简化的换线时间计算
    return capability.setupTime;
  }
  
  /**
   * 预估成本
   */
  static estimateCost(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability
  ): number {
    const processingTime = this.estimateProcessingTime(requirement, capability);
    const setupTime = this.estimateSetupTime(requirement, capability);
    
    const operatingCost = (processingTime / 60) * capability.operatingCost;
    const setupCost = capability.setupCost;
    
    return Math.round(operatingCost + setupCost);
  }
  
  /**
   * 评估质量风险
   */
  static assessQualityRisk(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability
  ): 'low' | 'medium' | 'high' {
    // 基于精度要求和设备能力评估质量风险
    if (requirement.precisionLevel === 'precision' && capability.qualityLevel !== 'A') {
      return 'high';
    } else if (requirement.precisionLevel === 'high' && capability.qualityLevel === 'C') {
      return 'medium';
    } else {
      return 'low';
    }
  }
  
  /**
   * 获取可用时间窗口
   */
  static async getAvailableTimeSlots(equipmentId: string) {
    // 模拟可用时间窗口
    const now = new Date();
    const slots = [];
    
    for (let i = 0; i < 5; i++) {
      const startTime = new Date(now.getTime() + i * 4 * 60 * 60 * 1000); // 每4小时一个窗口
      const endTime = new Date(startTime.getTime() + 3 * 60 * 60 * 1000); // 3小时窗口
      
      slots.push({
        startTime,
        endTime,
        duration: 180 // 分钟
      });
    }
    
    return slots;
  }
  
  /**
   * 生成推荐原因
   */
  static generateRecommendationReasons(
    requirement: OrderItemRequirement,
    capability: EquipmentProcessingCapability,
    capabilityScore: number,
    efficiencyScore: number
  ): string[] {
    const reasons = [];
    
    if (capabilityScore === 100) {
      reasons.push('完全满足所有技术要求');
    }
    
    if (efficiencyScore > 90) {
      reasons.push('规格匹配度高，加工效率优秀');
    }
    
    if (capability.qualityLevel === 'A') {
      reasons.push('设备质量等级高，适合精密加工');
    }
    
    return reasons;
  }
  
  /**
   * 识别限制因素
   */
  static identifyLimitations(constraintChecks: any[]): string[] {
    return constraintChecks
      .filter(check => !check.passed)
      .map(check => check.message);
  }
  
  /**
   * 获取工作中心能力
   */
  static async getWorkCenterCapability(workCenterId: string): Promise<WorkCenterCapability> {
    // 模拟数据，实际应该从数据库获取
    return {
      workCenterId,
      processType: 'edging',
      equipmentCapabilities: await this.getEquipmentCapabilities(workCenterId),
      overallConstraints: [],
      loadBalancingStrategy: 'best_fit',
      intelligentAllocation: {
        enableSizeOptimization: true,
        enableBatchOptimization: true,
        enableQualityMatching: true,
        enableCostOptimization: true
      }
    };
  }
  
  /**
   * 获取设备能力数据
   */
  static async getEquipmentCapabilities(workCenterId: string): Promise<EquipmentProcessingCapability[]> {
    // 模拟磨边工作中心的两台设备能力数据
    return [
      {
        equipmentId: 'EQ-EDGE-001',
        processType: 'edging',
        constraints: [
          {
            id: 'size-length-001',
            constraintType: 'size',
            dimension: 'length',
            maxValue: 2440,
            unit: 'mm',
            description: '最大长度限制',
            priority: 10,
            isHardConstraint: true
          },
          {
            id: 'size-width-001',
            constraintType: 'size',
            dimension: 'width',
            maxValue: 1830,
            unit: 'mm',
            description: '最大宽度限制',
            priority: 10,
            isHardConstraint: true
          }
        ],
        standardSpeed: 15, // mm/min
        setupTime: 30,
        efficiency: 95,
        qualityLevel: 'A',
        operatingCost: 120,
        setupCost: 200,
        sizeOptimalRange: {
          lengthMin: 500,
          lengthMax: 2200,
          widthMin: 300,
          widthMax: 1600
        },
        batchSizeOptimal: 50,
        batchSizeMin: 10,
        batchSizeMax: 200
      },
      {
        equipmentId: 'EQ-EDGE-002',
        processType: 'edging',
        constraints: [
          {
            id: 'size-length-002',
            constraintType: 'size',
            dimension: 'length',
            maxValue: 2440,
            unit: 'mm',
            description: '最大长度限制',
            priority: 10,
            isHardConstraint: true
          },
          {
            id: 'size-width-002',
            constraintType: 'size',
            dimension: 'width',
            maxValue: 3660,
            unit: 'mm',
            description: '最大宽度限制',
            priority: 10,
            isHardConstraint: true
          }
        ],
        standardSpeed: 12, // mm/min，大尺寸设备速度稍慢
        setupTime: 45,
        efficiency: 92,
        qualityLevel: 'A',
        operatingCost: 150,
        setupCost: 300,
        sizeOptimalRange: {
          lengthMin: 800,
          lengthMax: 2400,
          widthMin: 1000,
          widthMax: 3500
        },
        batchSizeOptimal: 30,
        batchSizeMin: 5,
        batchSizeMax: 100
      }
    ];
  }
  
  /**
   * 获取设备名称
   */
  static async getEquipmentName(equipmentId: string): Promise<string> {
    const nameMap: Record<string, string> = {
      'EQ-EDGE-001': '1号双边磨边机',
      'EQ-EDGE-002': '2号双边磨边机'
    };
    return nameMap[equipmentId] || equipmentId;
  }
  
  /**
   * 生成智能排产决策数据
   */
  static async generateSchedulingDecision(
    orderItemRequirement: OrderItemRequirement,
    workCenterId: string
  ): Promise<SchedulingDecisionData> {
    const matchResults = await this.matchEquipmentForOrderItem(orderItemRequirement, workCenterId);

    return {
      orderItemId: orderItemRequirement.orderItemId,
      workCenterId,
      candidateEquipments: matchResults,
      recommendedEquipment: matchResults[0]?.equipmentId || '',
      alternativeEquipments: matchResults.slice(1, 3).map(r => r.equipmentId),
      schedulingSuggestion: {
        preferredStartTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2小时后
        estimatedCompletionTime: new Date(Date.now() + 6 * 60 * 60 * 1000), // 6小时后
        batchingOpportunities: ['可与同规格订单批量处理'],
        sequencingAdvice: '建议安排在当前批次完成后'
      },
      optimizationAdvice: {
        loadBalancing: ['考虑将大尺寸订单分配给2号设备'],
        efficiencyImprovement: ['批量处理相同规格可提高效率'],
        costReduction: ['减少换线次数可降低成本'],
        qualityAssurance: ['精密加工建议使用1号设备']
      },
      risks: {
        deliveryRisk: 'low',
        qualityRisk: 'low',
        costRisk: 'medium',
        riskFactors: ['设备切换可能增加成本'],
        mitigationSuggestions: ['合理安排批量生产']
      }
    };
  }

  /**
   * 批量分析生产工单中的所有工单项
   */
  static async analyzeProductionOrderItems(
    orderItems: any[],
    workCenterId: string
  ): Promise<{
    batchAnalysis: any[];
    equipmentRecommendations: Map<string, EquipmentMatchResult[]>;
    overallOptimization: any;
  }> {
    const equipmentRecommendations = new Map<string, EquipmentMatchResult[]>();
    const batchAnalysis = [];

    // 按规格分组工单项
    const groupedItems = this.groupItemsBySpecification(orderItems);

    for (const [specKey, items] of groupedItems.entries()) {
      const representativeItem = items[0];
      const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);

      // 构建代表性订单项要求
      const requirement: OrderItemRequirement = {
        orderItemId: `BATCH-${specKey}`,
        dimensions: {
          length: representativeItem.specifications.length,
          width: representativeItem.specifications.width,
          thickness: representativeItem.specifications.thickness,
          area: (representativeItem.specifications.length * representativeItem.specifications.width) / 1000000,
          perimeter: 2 * (representativeItem.specifications.length + representativeItem.specifications.width)
        },
        materialType: 'float_glass',
        glassType: representativeItem.specifications.glassType,
        edgeType: 'straight',
        precisionLevel: 'standard',
        surfaceQuality: 'A',
        quantity: totalQuantity,
        requiredDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        priority: 'medium'
      };

      // 获取设备匹配结果
      const matches = await this.matchEquipmentForOrderItem(requirement, workCenterId);
      equipmentRecommendations.set(specKey, matches);

      batchAnalysis.push({
        specificationKey: specKey,
        items: items,
        totalQuantity: totalQuantity,
        recommendedEquipment: matches[0]?.equipmentId,
        matchScore: matches[0]?.overallScore || 0,
        estimatedTime: matches[0]?.estimatedProcessingTime || 0
      });
    }

    // 整体优化建议
    const overallOptimization = this.generateOverallOptimization(batchAnalysis);

    return {
      batchAnalysis,
      equipmentRecommendations,
      overallOptimization
    };
  }

  /**
   * 按规格分组工单项
   */
  static groupItemsBySpecification(orderItems: any[]): Map<string, any[]> {
    const groups = new Map<string, any[]>();

    for (const item of orderItems) {
      const specKey = `${item.specifications.length}x${item.specifications.width}x${item.specifications.thickness}-${item.specifications.glassType}`;

      if (!groups.has(specKey)) {
        groups.set(specKey, []);
      }
      groups.get(specKey)!.push(item);
    }

    return groups;
  }

  /**
   * 生成整体优化建议
   */
  static generateOverallOptimization(batchAnalysis: any[]): any {
    const totalBatches = batchAnalysis.length;
    const avgScore = batchAnalysis.reduce((sum, batch) => sum + batch.matchScore, 0) / totalBatches;
    const totalTime = batchAnalysis.reduce((sum, batch) => sum + batch.estimatedTime, 0);

    return {
      totalBatches,
      averageMatchScore: Math.round(avgScore),
      totalEstimatedTime: Math.round(totalTime),
      recommendations: [
        totalBatches > 3 ? '建议分批次进行生产，避免设备过载' : '批次数量合理，可以连续生产',
        avgScore > 85 ? '设备匹配度良好，预期效率较高' : '部分批次匹配度较低，建议优化设备分配',
        '建议按设备能力优势安排生产顺序'
      ]
    };
  }
}
