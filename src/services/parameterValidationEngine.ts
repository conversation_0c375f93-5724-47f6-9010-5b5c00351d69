/**
 * 参数验证引擎
 * 
 * 提供完整的参数验证功能，包括类型验证、范围检查、依赖关系处理等
 */

import type {
  ComponentParameter,
  ParameterType,
  ParameterOption,
  ValidationResult,
  ValidationError,
  SeverityLevel
} from '@/types/product-structure';

/**
 * 参数验证结果
 */
export interface ParameterValidationResult {
  /** 参数名称 */
  parameterName: string;
  /** 是否有效 */
  isValid: boolean;
  /** 验证后的值（可能经过类型转换或默认值处理） */
  validatedValue?: any;
  /** 错误信息 */
  errors: ValidationError[];
  /** 警告信息 */
  warnings: ValidationError[];
  /** 建议信息 */
  suggestions: string[];
}

/**
 * 参数依赖关系定义
 */
export interface ParameterDependency {
  /** 依赖的参数名称 */
  dependsOn: string;
  /** 依赖条件表达式 */
  condition: string;
  /** 当依赖条件满足时的处理 */
  action: 'show' | 'hide' | 'enable' | 'disable' | 'require' | 'optional';
}

/**
 * 参数验证引擎类
 */
export class ParameterValidationEngine {
  private dependencies: Map<string, ParameterDependency[]> = new Map();

  /**
   * 设置参数依赖关系
   */
  setDependencies(dependencies: Record<string, ParameterDependency[]>): void {
    this.dependencies.clear();
    Object.entries(dependencies).forEach(([paramName, deps]) => {
      this.dependencies.set(paramName, deps);
    });
  }

  /**
   * 验证单个参数值
   */
  validateParameter(
    parameter: ComponentParameter,
    value: any,
    allValues: Record<string, any> = {}
  ): ParameterValidationResult {
    const result: ParameterValidationResult = {
      parameterName: parameter.name,
      isValid: true,
      validatedValue: value,
      errors: [],
      warnings: [],
      suggestions: []
    };

    try {
      // 1. 处理默认值
      if (value === null || value === undefined || value === '') {
        if (parameter.defaultValue !== undefined) {
          result.validatedValue = parameter.defaultValue;
          value = parameter.defaultValue;
        } else if (parameter.required) {
          // 如果是必填参数但没有默认值，直接标记为无效
          result.isValid = false;
          result.errors.push({
            id: `param_${parameter.id}_required`,
            type: 'required_field',
            message: `参数 ${parameter.displayName} 是必填项`,
            location: {
              objectType: 'parameter',
              objectId: parameter.id,
              fieldName: parameter.name
            },
            severity: 'error',
            suggestions: [`请为 ${parameter.displayName} 提供有效值`]
          });
          return result;
        }
      }

      // 2. 检查依赖关系
      const dependencyResult = this.checkDependencies(parameter, allValues);
      if (!dependencyResult.isVisible) {
        // 如果参数不可见，跳过验证
        return result;
      }
      if (!dependencyResult.isRequired && (value === null || value === undefined || value === '')) {
        // 如果参数非必填且值为空，跳过验证
        return result;
      }

      // 3. 必填检查
      const requiredCheck = this.validateRequired(parameter, value, dependencyResult.isRequired);
      if (!requiredCheck.isValid) {
        result.errors.push(...requiredCheck.errors);
        result.isValid = false;
      }

      // 4. 类型验证
      if (value !== null && value !== undefined && value !== '') {
        const typeCheck = this.validateType(parameter, value);
        if (!typeCheck.isValid) {
          result.errors.push(...typeCheck.errors);
          result.isValid = false;
        } else {
          result.validatedValue = typeCheck.validatedValue;
        }

        // 5. 范围验证
        if (result.isValid) {
          const rangeCheck = this.validateRange(parameter, result.validatedValue);
          if (!rangeCheck.isValid) {
            result.errors.push(...rangeCheck.errors);
            result.isValid = false;
          }
        }

        // 6. 选项验证
        if (result.isValid && parameter.type === 'select') {
          const optionCheck = this.validateOptions(parameter, result.validatedValue);
          if (!optionCheck.isValid) {
            result.errors.push(...optionCheck.errors);
            result.isValid = false;
          }
        }

        // 7. 公式验证
        if (result.isValid && parameter.type === 'formula') {
          const formulaCheck = this.validateFormula(parameter, result.validatedValue, allValues);
          if (!formulaCheck.isValid) {
            result.errors.push(...formulaCheck.errors);
            result.isValid = false;
          }
        }
      }

      // 8. 自定义验证规则
      if (result.isValid && parameter.validationRules) {
        const customCheck = this.validateCustomRules(parameter, result.validatedValue, allValues);
        result.errors.push(...customCheck.errors);
        result.warnings.push(...customCheck.warnings);
        result.suggestions.push(...customCheck.suggestions);
        if (customCheck.errors.length > 0) {
          result.isValid = false;
        }
      }

    } catch (error) {
      result.isValid = false;
      result.errors.push({
        id: `param_${parameter.id}_validation_error`,
        type: 'validation_error',
        message: `参数验证过程中发生错误: ${error.message}`,
        location: {
          objectType: 'parameter',
          objectId: parameter.id,
          fieldName: parameter.name
        },
        severity: 'error',
        suggestions: ['请检查参数定义和输入值']
      });
    }

    return result;
  }

  /**
   * 验证多个参数值
   */
  validateParameters(
    parameters: ComponentParameter[],
    values: Record<string, any>
  ): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const suggestions: string[] = [];
    const validatedValues: Record<string, any> = {};

    // 验证每个参数
    parameters.forEach(parameter => {
      const value = values[parameter.name];
      const result = this.validateParameter(parameter, value, values);
      
      if (result.isValid) {
        validatedValues[parameter.name] = result.validatedValue;
      }
      
      errors.push(...result.errors);
      warnings.push(...result.warnings);
      suggestions.push(...result.suggestions);
    });

    // 检查参数间的交叉依赖
    const crossDependencyCheck = this.validateCrossDependencies(parameters, validatedValues);
    errors.push(...crossDependencyCheck.errors);
    warnings.push(...crossDependencyCheck.warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      validationTime: new Date().toISOString(),
      summary: {
        totalErrors: errors.length,
        totalWarnings: warnings.length,
        totalSuggestions: suggestions.length,
        criticalErrors: errors.filter(e => e.severity === 'error').length
      },
      validatedValues
    };
  }

  /**
   * 检查参数依赖关系
   */
  private checkDependencies(
    parameter: ComponentParameter,
    allValues: Record<string, any>
  ): { isVisible: boolean; isRequired: boolean; isEnabled: boolean } {
    const dependencies = this.dependencies.get(parameter.name) || [];
    
    let isVisible = true;
    let isRequired = parameter.required;
    let isEnabled = parameter.editable;

    dependencies.forEach(dep => {
      const dependentValue = allValues[dep.dependsOn];
      const conditionMet = this.evaluateCondition(dep.condition, dependentValue, allValues);

      if (conditionMet) {
        switch (dep.action) {
          case 'show':
            isVisible = true;
            break;
          case 'hide':
            isVisible = false;
            break;
          case 'enable':
            isEnabled = true;
            break;
          case 'disable':
            isEnabled = false;
            break;
          case 'require':
            isRequired = true;
            break;
          case 'optional':
            isRequired = false;
            break;
        }
      }
    });

    return { isVisible, isRequired, isEnabled };
  }

  /**
   * 验证必填项
   */
  private validateRequired(
    parameter: ComponentParameter,
    value: any,
    isRequired: boolean
  ): { isValid: boolean; errors: ValidationError[] } {
    const errors: ValidationError[] = [];

    if (isRequired && (value === null || value === undefined || value === '')) {
      errors.push({
        id: `param_${parameter.id}_required`,
        type: 'required_field',
        message: `参数 ${parameter.displayName} 是必填项`,
        location: {
          objectType: 'parameter',
          objectId: parameter.id,
          fieldName: parameter.name
        },
        severity: 'error',
        suggestions: [`请为 ${parameter.displayName} 提供有效值`]
      });
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证参数类型
   */
  private validateType(
    parameter: ComponentParameter,
    value: any
  ): { isValid: boolean; validatedValue: any; errors: ValidationError[] } {
    const errors: ValidationError[] = [];
    let validatedValue = value;

    switch (parameter.type) {
      case 'number':
        const numValue = Number(value);
        if (isNaN(numValue)) {
          errors.push({
            id: `param_${parameter.id}_type_number`,
            type: 'type_mismatch',
            message: `参数 ${parameter.displayName} 必须是数值类型`,
            location: {
              objectType: 'parameter',
              objectId: parameter.id,
              fieldName: parameter.name
            },
            severity: 'error',
            suggestions: ['请输入有效的数字']
          });
        } else {
          validatedValue = numValue;
        }
        break;

      case 'string':
        if (typeof value !== 'string') {
          validatedValue = String(value);
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean') {
          if (value === 'true' || value === '1' || value === 1) {
            validatedValue = true;
          } else if (value === 'false' || value === '0' || value === 0) {
            validatedValue = false;
          } else {
            errors.push({
              id: `param_${parameter.id}_type_boolean`,
              type: 'type_mismatch',
              message: `参数 ${parameter.displayName} 必须是布尔值`,
              location: {
                objectType: 'parameter',
                objectId: parameter.id,
                fieldName: parameter.name
              },
              severity: 'error',
              suggestions: ['请选择 true 或 false']
            });
          }
        }
        break;

      case 'select':
        // 选择类型的验证在 validateOptions 中处理
        break;

      case 'formula':
        if (typeof value !== 'string') {
          validatedValue = String(value);
        }
        break;

      default:
        errors.push({
          id: `param_${parameter.id}_unknown_type`,
          type: 'unknown_type',
          message: `参数 ${parameter.displayName} 的类型 ${parameter.type} 不受支持`,
          location: {
            objectType: 'parameter',
            objectId: parameter.id,
            fieldName: parameter.name
          },
          severity: 'error',
          suggestions: ['请检查参数类型定义']
        });
    }

    return { isValid: errors.length === 0, validatedValue, errors };
  }

  /**
   * 验证参数范围
   */
  private validateRange(
    parameter: ComponentParameter,
    value: any
  ): { isValid: boolean; errors: ValidationError[] } {
    const errors: ValidationError[] = [];

    if (parameter.type === 'number' && typeof value === 'number') {
      if (parameter.minValue !== undefined && value < parameter.minValue) {
        errors.push({
          id: `param_${parameter.id}_min_value`,
          type: 'range_violation',
          message: `参数 ${parameter.displayName} 的值 ${value} 小于最小值 ${parameter.minValue}`,
          location: {
            objectType: 'parameter',
            objectId: parameter.id,
            fieldName: parameter.name
          },
          severity: 'error',
          suggestions: [`请输入不小于 ${parameter.minValue} 的值`]
        });
      }

      if (parameter.maxValue !== undefined && value > parameter.maxValue) {
        errors.push({
          id: `param_${parameter.id}_max_value`,
          type: 'range_violation',
          message: `参数 ${parameter.displayName} 的值 ${value} 大于最大值 ${parameter.maxValue}`,
          location: {
            objectType: 'parameter',
            objectId: parameter.id,
            fieldName: parameter.name
          },
          severity: 'error',
          suggestions: [`请输入不大于 ${parameter.maxValue} 的值`]
        });
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证选项值
   */
  private validateOptions(
    parameter: ComponentParameter,
    value: any
  ): { isValid: boolean; errors: ValidationError[] } {
    const errors: ValidationError[] = [];

    if (parameter.options && parameter.options.length > 0) {
      const validOptions = parameter.options.map(opt => opt.value);
      if (!validOptions.includes(value)) {
        errors.push({
          id: `param_${parameter.id}_invalid_option`,
          type: 'invalid_option',
          message: `参数 ${parameter.displayName} 的值 ${value} 不在有效选项中`,
          location: {
            objectType: 'parameter',
            objectId: parameter.id,
            fieldName: parameter.name
          },
          severity: 'error',
          suggestions: [`请从以下选项中选择: ${validOptions.join(', ')}`]
        });
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证公式
   */
  private validateFormula(
    parameter: ComponentParameter,
    value: any,
    allValues: Record<string, any>
  ): { isValid: boolean; errors: ValidationError[] } {
    const errors: ValidationError[] = [];

    try {
      // 简单的公式语法检查
      const formula = String(value);

      // 检查是否包含基本的数学运算符
      const validChars = /^[a-zA-Z0-9+\-*/().\s_]+$/;

      if (!validChars.test(formula)) {
        errors.push({
          id: `param_${parameter.id}_invalid_formula_chars`,
          type: 'invalid_formula',
          message: `参数 ${parameter.displayName} 的公式包含无效字符`,
          location: {
            objectType: 'parameter',
            objectId: parameter.id,
            fieldName: parameter.name
          },
          severity: 'error',
          suggestions: ['公式只能包含字母、数字、运算符和括号']
        });
      }

      // 检查括号匹配
      let openParens = 0;
      for (const char of formula) {
        if (char === '(') openParens++;
        if (char === ')') openParens--;
        if (openParens < 0) break;
      }

      if (openParens !== 0) {
        errors.push({
          id: `param_${parameter.id}_unmatched_parens`,
          type: 'invalid_formula',
          message: `参数 ${parameter.displayName} 的公式括号不匹配`,
          location: {
            objectType: 'parameter',
            objectId: parameter.id,
            fieldName: parameter.name
          },
          severity: 'error',
          suggestions: ['请检查公式中的括号是否正确匹配']
        });
      }

      // 尝试解析公式中的参数引用
      const paramRefs = formula.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];
      const availableParams = Object.keys(allValues);

      for (const paramRef of paramRefs) {
        // 跳过数学函数名
        if (['sin', 'cos', 'tan', 'log', 'exp', 'sqrt', 'abs', 'min', 'max'].includes(paramRef.toLowerCase())) {
          continue;
        }

        if (!availableParams.includes(paramRef) && !['PI', 'E'].includes(paramRef)) {
          errors.push({
            id: `param_${parameter.id}_undefined_param_ref`,
            type: 'undefined_reference',
            message: `参数 ${parameter.displayName} 的公式引用了未定义的参数: ${paramRef}`,
            location: {
              objectType: 'parameter',
              objectId: parameter.id,
              fieldName: parameter.name
            },
            severity: 'warning',
            suggestions: [`请确保参数 ${paramRef} 已定义，或检查拼写是否正确`]
          });
        }
      }

    } catch (error) {
      errors.push({
        id: `param_${parameter.id}_formula_parse_error`,
        type: 'formula_parse_error',
        message: `参数 ${parameter.displayName} 的公式解析失败: ${error.message}`,
        location: {
          objectType: 'parameter',
          objectId: parameter.id,
          fieldName: parameter.name
        },
        severity: 'error',
        suggestions: ['请检查公式语法是否正确']
      });
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 验证自定义规则
   */
  private validateCustomRules(
    parameter: ComponentParameter,
    value: any,
    allValues: Record<string, any>
  ): { errors: ValidationError[]; warnings: ValidationError[]; suggestions: string[] } {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const suggestions: string[] = [];

    if (!parameter.validationRules) {
      return { errors, warnings, suggestions };
    }

    parameter.validationRules.forEach((rule, index) => {
      try {
        const ruleResult = this.evaluateValidationRule(rule, value, allValues);
        if (!ruleResult.isValid) {
          const error: ValidationError = {
            id: `param_${parameter.id}_custom_rule_${index}`,
            type: 'custom_validation',
            message: ruleResult.message || `参数 ${parameter.displayName} 不满足自定义验证规则`,
            location: {
              objectType: 'parameter',
              objectId: parameter.id,
              fieldName: parameter.name
            },
            severity: ruleResult.severity || 'error',
            suggestions: ruleResult.suggestions || []
          };

          if (error.severity === 'error') {
            errors.push(error);
          } else {
            warnings.push(error);
          }
        }

        if (ruleResult.suggestions) {
          suggestions.push(...ruleResult.suggestions);
        }
      } catch (error) {
        errors.push({
          id: `param_${parameter.id}_custom_rule_error_${index}`,
          type: 'validation_rule_error',
          message: `自定义验证规则执行失败: ${error.message}`,
          location: {
            objectType: 'parameter',
            objectId: parameter.id,
            fieldName: parameter.name
          },
          severity: 'error',
          suggestions: ['请检查自定义验证规则的语法']
        });
      }
    });

    return { errors, warnings, suggestions };
  }

  /**
   * 验证参数间的交叉依赖
   */
  private validateCrossDependencies(
    parameters: ComponentParameter[],
    values: Record<string, any>
  ): { errors: ValidationError[]; warnings: ValidationError[] } {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // 检查循环依赖
    const dependencyGraph = this.buildDependencyGraph(parameters);
    const cycles = this.detectCycles(dependencyGraph);

    cycles.forEach(cycle => {
      errors.push({
        id: `cross_dependency_cycle_${cycle.join('_')}`,
        type: 'circular_dependency',
        message: `检测到参数循环依赖: ${cycle.join(' -> ')}`,
        location: {
          objectType: 'parameter_group',
          objectId: cycle[0],
          fieldName: 'dependencies'
        },
        severity: 'error',
        suggestions: ['请检查参数依赖关系，消除循环依赖']
      });
    });

    return { errors, warnings };
  }

  /**
   * 构建参数依赖图
   */
  private buildDependencyGraph(parameters: ComponentParameter[]): Map<string, string[]> {
    const graph = new Map<string, string[]>();

    parameters.forEach(param => {
      const deps = this.dependencies.get(param.name) || [];
      const dependsOn = deps.map(dep => dep.dependsOn);
      graph.set(param.name, dependsOn);
    });

    return graph;
  }

  /**
   * 检测循环依赖
   */
  private detectCycles(graph: Map<string, string[]>): string[][] {
    const cycles: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (node: string, path: string[]): void => {
      if (recursionStack.has(node)) {
        // 找到循环
        const cycleStart = path.indexOf(node);
        if (cycleStart !== -1) {
          cycles.push([...path.slice(cycleStart), node]);
        }
        return;
      }

      if (visited.has(node)) {
        return;
      }

      visited.add(node);
      recursionStack.add(node);
      path.push(node);

      const neighbors = graph.get(node) || [];
      neighbors.forEach(neighbor => {
        dfs(neighbor, [...path]);
      });

      recursionStack.delete(node);
    };

    graph.forEach((_, node) => {
      if (!visited.has(node)) {
        dfs(node, []);
      }
    });

    return cycles;
  }

  /**
   * 评估条件表达式
   */
  private evaluateCondition(
    condition: string,
    dependentValue: any,
    allValues: Record<string, any>
  ): boolean {
    try {
      // 简单的条件评估实现
      // 实际应用中应该使用更安全的表达式解析器

      // 替换变量
      let expression = condition;
      Object.entries(allValues).forEach(([key, value]) => {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        expression = expression.replace(regex, JSON.stringify(value));
      });

      // 使用 Function 构造器评估表达式（注意：生产环境中应使用更安全的方法）
      return new Function('return ' + expression)();
    } catch (error) {
      console.warn(`条件表达式评估失败: ${condition}`, error);
      return false;
    }
  }

  /**
   * 评估验证规则
   */
  private evaluateValidationRule(
    rule: string,
    value: any,
    allValues: Record<string, any>
  ): { isValid: boolean; message?: string; severity?: SeverityLevel; suggestions?: string[] } {
    try {
      // 简单的规则评估实现
      // 支持基本的比较和逻辑操作

      let expression = rule;

      // 替换 value 变量
      expression = expression.replace(/\bvalue\b/g, JSON.stringify(value));

      // 替换其他参数变量
      Object.entries(allValues).forEach(([key, val]) => {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        expression = expression.replace(regex, JSON.stringify(val));
      });

      // 评估表达式
      const result = new Function('return ' + expression)();

      return {
        isValid: Boolean(result),
        message: result ? undefined : `验证规则失败: ${rule}`,
        severity: 'error'
      };
    } catch (error) {
      return {
        isValid: false,
        message: `验证规则执行错误: ${error.message}`,
        severity: 'error',
        suggestions: ['请检查验证规则语法']
      };
    }
  }
}
