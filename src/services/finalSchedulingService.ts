import type { 
  PreScheduleResult, 
  CuttingImportResult, 
  FinalScheduleResult 
} from '@/types/scheduling';

/**
 * 最终排产服务
 * 基于切割优化结果生成最终排产方案
 */
export class FinalSchedulingService {
  /**
   * 生成最终排产方案
   * 这是核心的“计划重构”逻辑
   */
  async generateFinalSchedule(
    preSchedule: PreScheduleResult,
    cuttingResult: CuttingImportResult
  ): Promise<FinalScheduleResult> {
    console.log('Service: 开始重构计划，生成最终排产方案...');
    
    await new Promise(resolve => setTimeout(resolve, 1500));

    const { tasks: reconstructedTasks, ganttData: reconstructedGanttData } = this.reconstructSchedule(preSchedule, cuttingResult);

    const finalMetrics = {
      totalDuration: preSchedule.estimatedMetrics.totalDuration * 0.9,
      equipmentUtilization: preSchedule.estimatedMetrics.equipmentUtilization + 5,
      deliveryAchievement: preSchedule.estimatedMetrics.deliveryAchievement + 2,
      materialUtilization: cuttingResult.summary.averageUtilization,
    };

    const result: FinalScheduleResult = {
      scheduleId: `final_${Date.now()}`,
      tasks: reconstructedTasks,
      ganttData: reconstructedGanttData,
      finalMetrics,
      cuttingIntegration: {
        cuttingPlans: cuttingResult.cuttingPlans,
        materialRequirements: [],
        equipmentRequirements: []
      },
      createdAt: new Date().toISOString(),
      status: 'draft'
    };
    
    console.log('Service: 最终排产方案生成完成。');
    return result;
  }

  /**
   * 核心计划重构逻辑 (模拟)
   */
  private reconstructSchedule(preSchedule: PreScheduleResult, cuttingResult: CuttingImportResult) {
    const originalTasks = preSchedule.tasks || [];
    const finalTasks: any[] = [];

    // 1. 创建物理切割任务
    const physicalCuttingTasks = cuttingResult.cuttingPlans.map((plan, index) => ({
      id: `physical_cut_${plan.planId}`,
      name: `切割任务: 原片方案 ${index + 1}`,
      productName: '多产品混合',
      batchName: '混合批次',
      equipment: '切割机 #1',
      startTime: new Date(Date.now() + index * 3600000).toISOString(),
      endTime: new Date(Date.now() + (index + 1) * 3600000).toISOString(),
      dependencies: [],
      type: 'cutting',
      details: plan.layout.pieces.map(p => p.pieceId).join(', '),
    }));
    finalTasks.push(...physicalCuttingTasks);

    // 2. 处理后续任务并重定向依赖
    const nonCuttingTasks = originalTasks.filter(task => task.type !== 'cutting');
    nonCuttingTasks.forEach(task => {
      // 找到此任务对应的玻璃件属于哪个切割方案
      const pieceId = task.name.split(' - ')[0]; // 简化逻辑
      const parentPlan = cuttingResult.cuttingPlans.find(p => p.layout.pieces.some(pc => pc.pieceId === pieceId));
      
      const newDependency = parentPlan ? `physical_cut_${parentPlan.planId}` : null;

      finalTasks.push({
        ...task,
        productName: `产品-${task.itemId.slice(-3)}`,
        batchName: `批次-${task.batchId.slice(-2)}`,
        equipment: `${task.type}设备 #${Math.ceil(Math.random() * 3)}`,
        dependencies: newDependency ? [newDependency] : [],
        startTime: new Date(Date.now() + 2 * 3600000 + Math.random() * 3600000).toISOString(),
        endTime: new Date(Date.now() + 3 * 3600000 + Math.random() * 7200000).toISOString(),
      });
    });

    // 3. (模拟) 更新甘特图数据
    const reconstructedGanttData = { ...preSchedule.ganttData };

    return { tasks: finalTasks, ganttData: reconstructedGanttData };
  }

  /**
   * 生成用于UI展示的对比数据
   */
  generateComparisonData(
    preSchedule: PreScheduleResult,
    finalSchedule: FinalScheduleResult
  ): any {
    const cuttingTasksBefore = (preSchedule.tasks || []).filter(t => t.type === 'cutting').length;
    const cuttingTasksAfter = (finalSchedule.tasks || []).filter(t => t.type === 'cutting').length;

    return {
      duration: {
        before: parseFloat(preSchedule.estimatedMetrics.totalDuration.toFixed(2)),
        after: parseFloat(finalSchedule.finalMetrics.totalDuration.toFixed(2)),
      },
      materialCost: {
        before: 125800.50,
        after: 113220.45,
      },
      utilizationRate: {
        before: parseFloat(preSchedule.estimatedMetrics.materialUtilization.toFixed(2)),
        after: parseFloat(finalSchedule.finalMetrics.materialUtilization.toFixed(2)),
      },
      taskCount: {
        cutting: {
          before: cuttingTasksBefore,
          after: cuttingTasksAfter,
        },
      },
    };
  }

  /**
   * 提交最终排产方案到服务器
   */
  async submitFinalSchedule(finalSchedule: FinalScheduleResult): Promise<boolean> {
    console.log('Service: 正在向服务器提交最终排产方案...', finalSchedule);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const isSuccess = Math.random() > 0.1;
    if (isSuccess) {
      console.log('Service: 服务器确认，方案已成功提交并下达。');
      return true;
    } else {
      console.error('Service: 服务器返回错误，提交失败。');
      return false;
    }
  }
}

export const finalSchedulingService = new FinalSchedulingService();
