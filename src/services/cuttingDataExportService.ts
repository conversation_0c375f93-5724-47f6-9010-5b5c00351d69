import type { OptimizedBatch } from '@/types/production-order-creation';
import type { CuttingExportData } from '@/types/scheduling';

/**
 * 切割数据导出服务
 * 将排产数据导出为第三方切割优化系统可识别的格式
 */
export class CuttingDataExportService {
  /**
   * 导出切割数据
   */
  async exportCuttingData(batches: OptimizedBatch[]): Promise<CuttingExportData> {
    console.log('开始导出切割数据，批次数量:', batches.length);
    
    // 模拟导出处理时间
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const exportData: CuttingExportData = {
      exportId: `export_${Date.now()}`,
      exportTime: new Date().toISOString(),
      batchCount: batches.length,
      batches: this.formatBatchesForCutting(batches),
      availableMaterials: await this.getAvailableMaterials(),
      constraints: this.getCuttingConstraints(),
      metadata: {
        version: '1.0',
        format: 'json',
        checksum: this.generateChecksum(),
        exportedBy: 'system'
      }
    };
    
    console.log('切割数据导出完成:', exportData);
    return exportData;
  }

  /**
   * 格式化批次数据用于切割优化
   */
  private formatBatchesForCutting(batches: OptimizedBatch[]) {
    return batches.flatMap(batch => 
      batch.items.map(item => ({
        batchId: batch.id,
        specifications: {
          length: item.specifications.length,
          width: item.specifications.width,
          thickness: item.specifications.thickness,
          glassType: item.specifications.glassType || 'clear',
          color: item.specifications.color || 'clear'
        },
        quantity: item.selectedQuantity,
        priority: this.getPriorityNumber(batch.priority),
        deliveryDate: item.deliveryDate || new Date().toISOString(),
        customerName: item.customerName
      }))
    );
  }

  /**
   * 获取可用原片信息
   */
  private async getAvailableMaterials() {
    // 模拟从库存系统获取原片信息
    return [
      {
        materialId: 'MAT_001',
        dimensions: { length: 3300, width: 2140, thickness: 6 },
        quantity: 50,
        cost: 120,
        supplier: '信义玻璃',
        location: 'A区-01'
      },
      {
        materialId: 'MAT_002',
        dimensions: { length: 3660, width: 2440, thickness: 8 },
        quantity: 30,
        cost: 180,
        supplier: '福耀玻璃',
        location: 'A区-02'
      }
    ];
  }

  /**
   * 获取切割约束条件
   */
  private getCuttingConstraints() {
    return {
      maxUtilizationRate: 0.95,
      minPieceSize: { length: 300, width: 300 },
      cuttingMargin: 5,
      wasteThreshold: 0.1
    };
  }

  /**
   * 生成校验和
   */
  private generateChecksum(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  /**
   * 转换优先级为数字
   */
  private getPriorityNumber(priority: string): number {
    const priorityMap = { urgent: 4, high: 3, normal: 2, low: 1 };
    return priorityMap[priority as keyof typeof priorityMap] || 2;
  }
}

export const cuttingDataExportService = new CuttingDataExportService();
