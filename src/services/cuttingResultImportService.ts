import type { CuttingImportResult } from '@/types/scheduling';

/**
 * 切割结果导入服务
 * 导入并验证第三方切割优化系统的结果
 */
export class CuttingResultImportService {
  /**
   * 导入切割结果
   */
  async importCuttingResult(file: File): Promise<CuttingImportResult> {
    console.log('开始导入切割结果文件:', file.name);

    // 模拟文件解析和验证时间
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 模拟解析结果
    const materialUsage = this.generateMockMaterialUsage();
    const result: CuttingImportResult = {
      resultId: `import_${Date.now()}`,
      importTime: new Date().toISOString(),
      cuttingPlans: this.generateMockCuttingPlans(),
      materialUsage: materialUsage,
      timeEstimates: this.generateMockTimeEstimates(),
      summary: {
        averageUtilization: materialUsage.reduce((acc, cur) => acc + cur.utilization, 0) / materialUsage.length,
        totalWaste: materialUsage.reduce((acc, cur) => acc + cur.wasteAmount, 0),
      },
      improvements: {
        materialSaved: 3,
        timeSaved: 1.3,
        utilizationImproved: 4,
        costSaved: 2500
      },
      validation: {
        valid: true,
        errors: [],
        warnings: []
      }
    };

    console.log('切割结果导入完成:', result);
    return result;
  }

  /**
   * 模拟第三方系统处理完成，自动生成切割优化结果
   * 用于原型演示，无需手动上传文件
   */
  async simulateOptimizationComplete(): Promise<CuttingImportResult> {
    console.log('模拟第三方切割优化系统处理完成...');

    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 2000));

    try {
      // 加载真实的模拟数据
      const response = await fetch('/mock/cutting/optimizationResults.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const mockData = await response.json();
      const resultData = mockData.data[0];

      // 计算平均利用率
      const avgUtilization = resultData.materialUsage.reduce((acc: number, cur: any) => acc + cur.utilization, 0) / resultData.materialUsage.length;
      const totalWaste = resultData.materialUsage.reduce((acc: number, cur: any) => acc + cur.wasteAmount, 0);

      const result: CuttingImportResult = {
        resultId: `simulation_${Date.now()}`,
        importTime: new Date().toISOString(),
        cuttingPlans: resultData.cuttingPlans,
        materialUsage: resultData.materialUsage,
        timeEstimates: resultData.timeEstimates,
        summary: {
          averageUtilization: avgUtilization,
          totalWaste: totalWaste,
        },
        improvements: resultData.improvements,
        validation: resultData.validation
      };

      console.log('模拟切割优化完成:', result);
      return result;
    } catch (error) {
      console.warn('加载模拟数据失败，使用默认备用数据:', error);

      // 如果加载失败，使用默认数据
      const materialUsage = this.generateRealisticMaterialUsage();
      const avgUtilization = materialUsage.reduce((acc, cur) => acc + cur.utilization, 0) / materialUsage.length;
      const totalWaste = materialUsage.reduce((acc, cur) => acc + cur.wasteAmount, 0);

      const result: CuttingImportResult = {
        resultId: `simulation_${Date.now()}`,
        importTime: new Date().toISOString(),
        cuttingPlans: this.generateRealisticCuttingPlans(),
        materialUsage: materialUsage,
        timeEstimates: this.generateRealisticTimeEstimates(),
        summary: {
          averageUtilization: avgUtilization,
          totalWaste: totalWaste,
        },
        improvements: {
          materialSaved: 5.2,      // 节约5.2%材料
          timeSaved: 2.1,          // 节约2.1小时
          utilizationImproved: 7.5, // 利用率提升7.5%
          costSaved: 4200          // 节约成本4200元
        },
        validation: {
          valid: true,
          errors: [],
          warnings: [
            {
              code: 'MINOR_WASTE',
              message: '部分原片存在少量边角料，建议考虑小尺寸产品利用',
              suggestion: '可考虑将边角料用于制作装饰条或小型配件'
            }
          ]
        }
      };

      return result;
    }
  }

  /**
   * 生成模拟切割方案
   */
  private generateMockCuttingPlans() {
    return [
      {
        planId: 'PLAN_001',
        materialId: 'MAT_001',
        layout: {
          pieces: [
            {
              batchId: 'batch_1',
              pieceId: 'piece_001',
              position: { x: 0, y: 0 },
              dimensions: { length: 1200, width: 800 },
              rotation: 0
            }
          ],
          utilization: 89,
          wasteArea: 0.5,
          wastePercentage: 11
        },
        cuttingSequence: [
          {
            stepId: 'step_001',
            type: 'horizontal' as const,
            position: 800,
            affectedPieces: ['piece_001']
          }
        ]
      }
    ];
  }

  /**
   * 生成模拟物料使用情况
   */
  private generateMockMaterialUsage() {
    return [
      {
        materialId: 'MAT_001',
        usedQuantity: 42,
        utilization: 89,
        cost: 5040,
        wasteAmount: 3
      }
    ];
  }

  /**
   * 生成模拟时间估算
   */
  private generateMockTimeEstimates() {
    return [
      {
        batchId: 'batch_1',
        cuttingTime: 432, // 7.2小时
        setupTime: 30,
        totalTime: 462
      }
    ];
  }

  /**
   * 生成真实的切割方案（用于模拟）
   */
  private generateRealisticCuttingPlans() {
    return [
      {
        planId: 'PLAN_001_OPTIMIZED',
        materialId: 'GLASS_3300x2140_6MM',
        layout: {
          pieces: [
            {
              batchId: 'batch_1',
              pieceId: 'piece_001',
              position: { x: 0, y: 0 },
              dimensions: { length: 1200, width: 800 },
              rotation: 0
            },
            {
              batchId: 'batch_1',
              pieceId: 'piece_002',
              position: { x: 1200, y: 0 },
              dimensions: { length: 1000, width: 800 },
              rotation: 0
            },
            {
              batchId: 'batch_1',
              pieceId: 'piece_003',
              position: { x: 0, y: 800 },
              dimensions: { length: 800, width: 600 },
              rotation: 0
            },
            {
              batchId: 'batch_1',
              pieceId: 'piece_004',
              position: { x: 800, y: 800 },
              dimensions: { length: 900, width: 600 },
              rotation: 0
            }
          ],
          utilization: 92.3, // 优化后利用率
          wasteArea: 0.3,
          wastePercentage: 7.7
        },
        cuttingSequence: [
          {
            stepId: 'step_001',
            type: 'vertical' as const,
            position: 1200,
            affectedPieces: ['piece_001', 'piece_002']
          },
          {
            stepId: 'step_002',
            type: 'horizontal' as const,
            position: 800,
            affectedPieces: ['piece_001', 'piece_003']
          },
          {
            stepId: 'step_003',
            type: 'vertical' as const,
            position: 800,
            affectedPieces: ['piece_003', 'piece_004']
          }
        ]
      },
      {
        planId: 'PLAN_002_OPTIMIZED',
        materialId: 'GLASS_3660x2440_6MM',
        layout: {
          pieces: [
            {
              batchId: 'batch_1',
              pieceId: 'piece_005',
              position: { x: 0, y: 0 },
              dimensions: { length: 1500, width: 1000 },
              rotation: 0
            },
            {
              batchId: 'batch_1',
              pieceId: 'piece_006',
              position: { x: 1500, y: 0 },
              dimensions: { length: 1200, width: 1000 },
              rotation: 0
            }
          ],
          utilization: 89.7,
          wasteArea: 0.4,
          wastePercentage: 10.3
        },
        cuttingSequence: [
          {
            stepId: 'step_004',
            type: 'vertical' as const,
            position: 1500,
            affectedPieces: ['piece_005', 'piece_006']
          }
        ]
      }
    ];
  }

  /**
   * 生成真实的物料使用情况
   */
  private generateRealisticMaterialUsage() {
    return [
      {
        materialId: 'GLASS_3300x2140_6MM',
        usedQuantity: 38,
        utilization: 92.3,
        cost: 4560,
        wasteAmount: 2.1
      },
      {
        materialId: 'GLASS_3660x2440_6MM',
        usedQuantity: 25,
        utilization: 89.7,
        cost: 3750,
        wasteAmount: 1.8
      }
    ];
  }

  /**
   * 生成真实的时间估算
   */
  private generateRealisticTimeEstimates() {
    return [
      {
        batchId: 'batch_1',
        cuttingTime: 385, // 6.4小时（优化后）
        setupTime: 25,    // 减少设置时间
        totalTime: 410    // 总时间减少
      }
    ];
  }
}

export const cuttingResultImportService = new CuttingResultImportService();
