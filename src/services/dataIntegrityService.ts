/**
 * 数据完整性和外键关联验证服务
 */

import type { OptimizedBatch, SelectedOrderItem } from '@/types/production-order-creation';

export interface DataIntegrityReport {
  isValid: boolean;
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  issues: DataIntegrityIssue[];
  summary: string;
}

export interface DataIntegrityIssue {
  type: 'missing_reference' | 'invalid_reference' | 'data_inconsistency' | 'orphaned_record';
  severity: 'low' | 'medium' | 'high' | 'critical';
  entity: string;
  entityId: string;
  field: string;
  description: string;
  suggestion?: string;
}

/**
 * 数据完整性验证服务
 */
export class DataIntegrityService {
  
  /**
   * 验证批次数据的完整性和外键关联
   */
  static validateBatchIntegrity(
    batches: OptimizedBatch[], 
    sourceOrders: any[] = []
  ): DataIntegrityReport {
    const issues: DataIntegrityIssue[] = [];
    let totalChecks = 0;
    let passedChecks = 0;

    // 检查每个批次
    for (const batch of batches) {
      // 1. 检查基础字段完整性
      totalChecks++;
      if (!batch.id || !batch.name) {
        issues.push({
          type: 'missing_reference',
          severity: 'critical',
          entity: 'OptimizedBatch',
          entityId: batch.id || 'unknown',
          field: 'id/name',
          description: '批次缺少必要的标识信息',
          suggestion: '确保每个批次都有唯一ID和名称'
        });
      } else {
        passedChecks++;
      }

      // 2. 检查外键关联完整性
      totalChecks++;
      if (batch.sourceProductionOrderId) {
        const sourceOrder = sourceOrders.find(order => order.id === batch.sourceProductionOrderId);
        if (!sourceOrder) {
          issues.push({
            type: 'invalid_reference',
            severity: 'high',
            entity: 'OptimizedBatch',
            entityId: batch.id,
            field: 'sourceProductionOrderId',
            description: `批次引用的生产工单 ${batch.sourceProductionOrderId} 不存在`,
            suggestion: '检查生产工单数据源或更新批次关联'
          });
        } else {
          passedChecks++;
          
          // 3. 检查数据一致性
          totalChecks++;
          if (batch.sourceWorkOrderNumber !== sourceOrder.workOrderNumber) {
            issues.push({
              type: 'data_inconsistency',
              severity: 'medium',
              entity: 'OptimizedBatch',
              entityId: batch.id,
              field: 'sourceWorkOrderNumber',
              description: '批次中的工单号与源工单不一致',
              suggestion: '同步批次和源工单的工单号信息'
            });
          } else {
            passedChecks++;
          }
        }
      } else {
        // 缺少外键关联
        issues.push({
          type: 'missing_reference',
          severity: 'medium',
          entity: 'OptimizedBatch',
          entityId: batch.id,
          field: 'sourceProductionOrderId',
          description: '批次缺少与生产工单的关联',
          suggestion: '建立批次与生产工单的外键关联'
        });
      }

      // 4. 检查批次项目的关联性
      for (const item of batch.items) {
        totalChecks++;
        if (!this.validateOrderItemIntegrity(item, batch)) {
          issues.push({
            type: 'data_inconsistency',
            severity: 'medium',
            entity: 'SelectedOrderItem',
            entityId: item.id,
            field: 'batch_consistency',
            description: '订单项与所属批次的数据不一致',
            suggestion: '检查订单项的客户信息和规格是否与批次匹配'
          });
        } else {
          passedChecks++;
        }
      }
    }

    const failedChecks = totalChecks - passedChecks;
    const isValid = failedChecks === 0;

    return {
      isValid,
      totalChecks,
      passedChecks,
      failedChecks,
      issues,
      summary: this.generateSummary(isValid, totalChecks, passedChecks, issues)
    };
  }

  /**
   * 验证订单项的完整性
   */
  private static validateOrderItemIntegrity(item: SelectedOrderItem, batch: OptimizedBatch): boolean {
    // 检查基础字段
    if (!item.id || !item.customerName || !item.specifications) {
      return false;
    }

    // 检查外键关联
    if (item.productionOrderId && batch.sourceProductionOrderId) {
      if (item.productionOrderId !== batch.sourceProductionOrderId) {
        return false;
      }
    }

    // 检查数量逻辑
    if (item.selectedQuantity > item.totalQuantity || item.selectedQuantity <= 0) {
      return false;
    }

    return true;
  }

  /**
   * 生成完整性报告摘要
   */
  private static generateSummary(
    isValid: boolean, 
    totalChecks: number, 
    passedChecks: number, 
    issues: DataIntegrityIssue[]
  ): string {
    if (isValid) {
      return `数据完整性验证通过：${totalChecks} 项检查全部通过，外键关联完整，数据一致性良好。`;
    }

    const criticalIssues = issues.filter(i => i.severity === 'critical').length;
    const highIssues = issues.filter(i => i.severity === 'high').length;
    const mediumIssues = issues.filter(i => i.severity === 'medium').length;
    const lowIssues = issues.filter(i => i.severity === 'low').length;

    let summary = `数据完整性验证发现问题：${totalChecks} 项检查中 ${passedChecks} 项通过。`;
    
    if (criticalIssues > 0) {
      summary += ` 发现 ${criticalIssues} 个严重问题`;
    }
    if (highIssues > 0) {
      summary += ` ${highIssues} 个高优先级问题`;
    }
    if (mediumIssues > 0) {
      summary += ` ${mediumIssues} 个中等问题`;
    }
    if (lowIssues > 0) {
      summary += ` ${lowIssues} 个低优先级问题`;
    }

    summary += '。建议优先处理严重和高优先级问题。';
    return summary;
  }

  /**
   * 生成数据关联性报告
   */
  static generateRelationshipReport(batches: OptimizedBatch[]): {
    totalBatches: number;
    batchesWithFullTraceability: number;
    batchesWithPartialTraceability: number;
    orphanedBatches: number;
    traceabilityRate: number;
    relationshipMatrix: Record<string, number>;
  } {
    const totalBatches = batches.length;
    let batchesWithFullTraceability = 0;
    let batchesWithPartialTraceability = 0;
    let orphanedBatches = 0;

    const relationshipMatrix: Record<string, number> = {
      'hasProductionOrderId': 0,
      'hasWorkOrderNumber': 0,
      'hasCustomerOrderId': 0,
      'hasCustomerOrderNumber': 0,
      'hasCompleteTraceability': 0
    };

    for (const batch of batches) {
      let traceabilityScore = 0;

      if (batch.sourceProductionOrderId) {
        relationshipMatrix.hasProductionOrderId++;
        traceabilityScore++;
      }

      if (batch.sourceWorkOrderNumber) {
        relationshipMatrix.hasWorkOrderNumber++;
        traceabilityScore++;
      }

      if (batch.sourceCustomerOrderId) {
        relationshipMatrix.hasCustomerOrderId++;
        traceabilityScore++;
      }

      if (batch.sourceCustomerOrderNumber) {
        relationshipMatrix.hasCustomerOrderNumber++;
        traceabilityScore++;
      }

      if (traceabilityScore === 4) {
        batchesWithFullTraceability++;
        relationshipMatrix.hasCompleteTraceability++;
      } else if (traceabilityScore >= 2) {
        batchesWithPartialTraceability++;
      } else {
        orphanedBatches++;
      }
    }

    const traceabilityRate = totalBatches > 0 
      ? (batchesWithFullTraceability + batchesWithPartialTraceability) / totalBatches 
      : 0;

    return {
      totalBatches,
      batchesWithFullTraceability,
      batchesWithPartialTraceability,
      orphanedBatches,
      traceabilityRate,
      relationshipMatrix
    };
  }

  /**
   * 修复数据关联性问题
   */
  static repairDataRelationships(
    batches: OptimizedBatch[], 
    sourceOrders: any[]
  ): OptimizedBatch[] {
    return batches.map(batch => {
      // 尝试通过其他字段找到关联的源工单
      if (!batch.sourceProductionOrderId && batch.sourceWorkOrderNumber) {
        const sourceOrder = sourceOrders.find(order => 
          order.workOrderNumber === batch.sourceWorkOrderNumber
        );
        if (sourceOrder) {
          batch.sourceProductionOrderId = sourceOrder.id;
          batch.sourceCustomerOrderId = sourceOrder.customerOrderId;
          batch.sourceCustomerOrderNumber = sourceOrder.customerOrderNumber;
        }
      }

      // 修复批次项目的关联
      batch.items = batch.items.map(item => {
        if (!item.productionOrderId && batch.sourceProductionOrderId) {
          item.productionOrderId = batch.sourceProductionOrderId;
        }
        if (!item.workOrderNumber && batch.sourceWorkOrderNumber) {
          item.workOrderNumber = batch.sourceWorkOrderNumber;
        }
        return item;
      });

      return batch;
    });
  }
}
