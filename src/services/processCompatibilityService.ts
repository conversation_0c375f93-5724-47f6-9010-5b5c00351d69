/**
 * 工艺兼容性检查服务
 */

import type { 
  ProcessConflict, 
  SelectedOrderItem,
  ValidationResult 
} from '@/types/production-order-creation'
import type { ProcessStep } from '@/types/mes-validation'

export interface ProcessCompatibilityResult {
  compatible: boolean
  conflicts: ProcessConflict[]
  suggestions: string[]
  groupingRecommendations: ProcessGroupRecommendation[]
}

export interface ProcessGroupRecommendation {
  groupId: string
  groupName: string
  items: string[]
  reason: string
  efficiency: number
}

export interface ProcessParameterConflict {
  parameter: string
  values: Array<{
    itemId: string
    value: any
    orderNumber: string
  }>
  severity: 'warning' | 'error'
  resolution: string
}

class ProcessCompatibilityService {
  /**
   * 检查订单项之间的工艺兼容性
   */
  async checkCompatibility(orderItems: SelectedOrderItem[]): Promise<ProcessCompatibilityResult> {
    if (orderItems.length <= 1) {
      return {
        compatible: true,
        conflicts: [],
        suggestions: [],
        groupingRecommendations: []
      }
    }

    const conflicts: ProcessConflict[] = []
    const suggestions: string[] = []
    
    // 检查工艺流程兼容性
    const processFlowConflicts = this.checkProcessFlowCompatibility(orderItems)
    conflicts.push(...processFlowConflicts)
    
    // 检查工艺参数兼容性
    const parameterConflicts = this.checkProcessParameterCompatibility(orderItems)
    conflicts.push(...parameterConflicts)
    
    // 检查设备兼容性
    const equipmentConflicts = this.checkEquipmentCompatibility(orderItems)
    conflicts.push(...equipmentConflicts)
    
    // 检查材料兼容性
    const materialConflicts = this.checkMaterialCompatibility(orderItems)
    conflicts.push(...materialConflicts)
    
    // 生成建议
    if (conflicts.length > 0) {
      suggestions.push(...this.generateSuggestions(conflicts, orderItems))
    }
    
    // 生成分组推荐
    const groupingRecommendations = this.generateGroupingRecommendations(orderItems, conflicts)
    
    return {
      compatible: conflicts.filter(c => c.severity === 'error').length === 0,
      conflicts,
      suggestions,
      groupingRecommendations
    }
  }

  /**
   * 检查工艺流程兼容性
   */
  private checkProcessFlowCompatibility(orderItems: SelectedOrderItem[]): ProcessConflict[] {
    const conflicts: ProcessConflict[] = []
    const processFlowGroups = new Map<string, SelectedOrderItem[]>()
    
    // 按工艺流程分组
    orderItems.forEach(item => {
      const flowKey = this.getProcessFlowKey(item.processFlow)
      if (!processFlowGroups.has(flowKey)) {
        processFlowGroups.set(flowKey, [])
      }
      processFlowGroups.get(flowKey)!.push(item)
    })
    
    // 如果有多个不同的工艺流程，检查是否可以合并
    if (processFlowGroups.size > 1) {
      const flowKeys = Array.from(processFlowGroups.keys())
      const incompatibleFlows = this.findIncompatibleFlows(flowKeys, processFlowGroups)
      
      if (incompatibleFlows.length > 0) {
        conflicts.push({
          id: `flow-conflict-${Date.now()}`,
          conflictType: 'sequence',
          severity: 'warning',
          affectedItems: incompatibleFlows.flatMap(flow => 
            processFlowGroups.get(flow)?.map(item => item.id) || []
          ),
          description: '订单项包含不同的工艺流程，可能影响生产效率',
          suggestions: [
            '建议将相同工艺流程的订单项分组到同一批次',
            '考虑调整工艺顺序以实现流程统一',
            '评估是否可以采用通用工艺流程'
          ],
          autoResolvable: false
        })
      }
    }
    
    return conflicts
  }

  /**
   * 检查工艺参数兼容性
   */
  private checkProcessParameterCompatibility(orderItems: SelectedOrderItem[]): ProcessConflict[] {
    const conflicts: ProcessConflict[] = []
    const parameterConflicts = this.analyzeParameterConflicts(orderItems)
    
    parameterConflicts.forEach(conflict => {
      conflicts.push({
        id: `param-conflict-${conflict.parameter}-${Date.now()}`,
        conflictType: 'parameter',
        severity: conflict.severity,
        affectedItems: conflict.values.map(v => v.itemId),
        description: `工艺参数"${conflict.parameter}"存在冲突`,
        suggestions: [conflict.resolution],
        autoResolvable: conflict.severity === 'warning'
      })
    })
    
    return conflicts
  }

  /**
   * 检查设备兼容性
   */
  private checkEquipmentCompatibility(orderItems: SelectedOrderItem[]): ProcessConflict[] {
    const conflicts: ProcessConflict[] = []
    const equipmentRequirements = this.analyzeEquipmentRequirements(orderItems)
    
    // 检查设备冲突
    const conflictingEquipment = equipmentRequirements.filter(req => 
      req.conflictingItems.length > 0
    )
    
    conflictingEquipment.forEach(equipment => {
      conflicts.push({
        id: `equipment-conflict-${equipment.equipmentId}-${Date.now()}`,
        conflictType: 'equipment',
        severity: 'error',
        affectedItems: equipment.conflictingItems,
        description: `设备"${equipment.equipmentName}"无法同时处理所选订单项`,
        suggestions: [
          '将冲突的订单项分配到不同批次',
          '寻找替代设备或工艺方案',
          '调整生产计划以避免设备冲突'
        ],
        autoResolvable: false
      })
    })
    
    return conflicts
  }

  /**
   * 检查材料兼容性
   */
  private checkMaterialCompatibility(orderItems: SelectedOrderItem[]): ProcessConflict[] {
    const conflicts: ProcessConflict[] = []
    
    // 检查玻璃类型兼容性
    const glassTypes = new Set(orderItems.map(item => item.specifications.glassType))
    if (glassTypes.size > 1) {
      const incompatibleTypes = this.findIncompatibleGlassTypes(Array.from(glassTypes))
      
      if (incompatibleTypes.length > 0) {
        const affectedItems = orderItems
          .filter(item => incompatibleTypes.includes(item.specifications.glassType))
          .map(item => item.id)
        
        conflicts.push({
          id: `material-conflict-glass-${Date.now()}`,
          conflictType: 'material',
          severity: 'warning',
          affectedItems,
          description: '选中的订单项包含不兼容的玻璃类型',
          suggestions: [
            '将不同玻璃类型的订单项分组到不同批次',
            '检查是否可以使用统一的玻璃类型',
            '评估混合生产的可行性和风险'
          ],
          autoResolvable: false
        })
      }
    }
    
    // 检查厚度兼容性
    const thicknesses = orderItems.map(item => item.specifications.thickness)
    const thicknessRange = Math.max(...thicknesses) - Math.min(...thicknesses)
    
    if (thicknessRange > 6) { // 厚度差异超过6mm
      conflicts.push({
        id: `material-conflict-thickness-${Date.now()}`,
        conflictType: 'material',
        severity: 'warning',
        affectedItems: orderItems.map(item => item.id),
        description: '订单项厚度差异较大，可能影响工艺参数设置',
        suggestions: [
          '按厚度范围分组订单项',
          '调整工艺参数以适应厚度变化',
          '考虑分批次生产不同厚度的产品'
        ],
        autoResolvable: true
      })
    }
    
    return conflicts
  }

  /**
   * 生成解决建议
   */
  private generateSuggestions(conflicts: ProcessConflict[], orderItems: SelectedOrderItem[]): string[] {
    const suggestions: string[] = []
    
    const errorCount = conflicts.filter(c => c.severity === 'error').length
    const warningCount = conflicts.filter(c => c.severity === 'warning').length
    
    if (errorCount > 0) {
      suggestions.push('存在严重冲突，建议拆分为多个工单')
      suggestions.push('检查并调整冲突的工艺参数')
    }
    
    if (warningCount > 0) {
      suggestions.push('存在工艺差异，建议优化批次分组')
      suggestions.push('考虑调整工艺流程以提高兼容性')
    }
    
    // 基于订单项数量的建议
    if (orderItems.length > 5) {
      suggestions.push('订单项较多，建议按工艺相似性分组')
    }
    
    return suggestions
  }

  /**
   * 生成分组推荐
   */
  private generateGroupingRecommendations(
    orderItems: SelectedOrderItem[], 
    conflicts: ProcessConflict[]
  ): ProcessGroupRecommendation[] {
    const recommendations: ProcessGroupRecommendation[] = []
    
    // 按工艺流程分组
    const processGroups = this.groupByProcessFlow(orderItems)
    processGroups.forEach((items, flowKey, index) => {
      recommendations.push({
        groupId: `process-group-${index}`,
        groupName: `工艺组${index + 1}`,
        items: items.map(item => item.id),
        reason: '相同工艺流程，可提高生产效率',
        efficiency: this.calculateGroupEfficiency(items)
      })
    })
    
    // 按材料类型分组
    const materialGroups = this.groupByMaterial(orderItems)
    materialGroups.forEach((items, materialKey, index) => {
      recommendations.push({
        groupId: `material-group-${index}`,
        groupName: `材料组${index + 1}`,
        items: items.map(item => item.id),
        reason: '相同材料规格，减少换料时间',
        efficiency: this.calculateGroupEfficiency(items)
      })
    })
    
    return recommendations
  }

  // ============ 工具方法 ============
  
  private getProcessFlowKey(processFlow: ProcessStep[]): string {
    return processFlow.map(step => `${step.stepName}-${step.workstation}`).join('|')
  }

  private findIncompatibleFlows(flowKeys: string[], groups: Map<string, SelectedOrderItem[]>): string[] {
    // 简化的不兼容检查逻辑
    const incompatible: string[] = []
    
    flowKeys.forEach(flowKey => {
      const steps = flowKey.split('|')
      const hasTemperingAndLaminating = steps.some(step => step.includes('tempering')) && 
                                       steps.some(step => step.includes('laminating'))
      
      if (hasTemperingAndLaminating) {
        incompatible.push(flowKey)
      }
    })
    
    return incompatible
  }

  private analyzeParameterConflicts(orderItems: SelectedOrderItem[]): ProcessParameterConflict[] {
    const conflicts: ProcessParameterConflict[] = []
    
    // 检查温度参数冲突
    const temperatureValues = this.extractParameterValues(orderItems, 'temperature')
    if (temperatureValues.length > 1) {
      const tempRange = Math.max(...temperatureValues.map(v => v.value)) - 
                       Math.min(...temperatureValues.map(v => v.value))
      
      if (tempRange > 50) { // 温度差异超过50度
        conflicts.push({
          parameter: 'temperature',
          values: temperatureValues,
          severity: 'warning',
          resolution: '调整工艺参数以适应温度范围，或分组生产'
        })
      }
    }
    
    return conflicts
  }

  private analyzeEquipmentRequirements(orderItems: SelectedOrderItem[]): Array<{
    equipmentId: string
    equipmentName: string
    conflictingItems: string[]
  }> {
    // 模拟设备需求分析
    return []
  }

  private findIncompatibleGlassTypes(glassTypes: string[]): string[] {
    const incompatible: string[] = []
    
    // 检查不兼容的玻璃类型组合
    if (glassTypes.includes('low_e') && glassTypes.includes('reflective')) {
      incompatible.push('low_e', 'reflective')
    }
    
    return incompatible
  }

  private extractParameterValues(orderItems: SelectedOrderItem[], parameter: string): Array<{
    itemId: string
    value: any
    orderNumber: string
  }> {
    const values: Array<{ itemId: string; value: any; orderNumber: string }> = []
    
    orderItems.forEach(item => {
      item.processFlow.forEach(step => {
        if (step.constraints && step.constraints[parameter]) {
          values.push({
            itemId: item.id,
            value: step.constraints[parameter],
            orderNumber: item.orderNumber
          })
        }
      })
    })
    
    return values
  }

  private groupByProcessFlow(orderItems: SelectedOrderItem[]): Map<string, SelectedOrderItem[]> {
    const groups = new Map<string, SelectedOrderItem[]>()
    
    orderItems.forEach(item => {
      const flowKey = this.getProcessFlowKey(item.processFlow)
      if (!groups.has(flowKey)) {
        groups.set(flowKey, [])
      }
      groups.get(flowKey)!.push(item)
    })
    
    return groups
  }

  private groupByMaterial(orderItems: SelectedOrderItem[]): Map<string, SelectedOrderItem[]> {
    const groups = new Map<string, SelectedOrderItem[]>()
    
    orderItems.forEach(item => {
      const materialKey = `${item.specifications.glassType}-${item.specifications.thickness}`
      if (!groups.has(materialKey)) {
        groups.set(materialKey, [])
      }
      groups.get(materialKey)!.push(item)
    })
    
    return groups
  }

  private calculateGroupEfficiency(items: SelectedOrderItem[]): number {
    // 简化的效率计算
    const baseEfficiency = 70
    const sizeBonus = Math.min(items.length * 5, 25) // 批次大小奖励
    const varietyPenalty = new Set(items.map(item => 
      `${item.specifications.length}x${item.specifications.width}`
    )).size * 2 // 规格多样性惩罚
    
    return Math.max(50, Math.min(95, baseEfficiency + sizeBonus - varietyPenalty))
  }

  /**
   * 实时验证订单项兼容性
   */
  async validateRealtime(orderItems: SelectedOrderItem[]): Promise<ValidationResult[]> {
    const results: ValidationResult[] = []
    
    if (orderItems.length === 0) {
      return results
    }
    
    const compatibility = await this.checkCompatibility(orderItems)
    
    // 转换冲突为验证结果
    compatibility.conflicts.forEach(conflict => {
      results.push({
        id: conflict.id,
        level: conflict.severity === 'error' ? 'error' : 'warning',
        category: 'process',
        title: conflict.description,
        message: conflict.suggestions[0] || '请检查工艺配置',
        suggestion: conflict.suggestions.slice(1).join('; '),
        affectedItems: conflict.affectedItems,
        autoFixable: conflict.autoResolvable
      })
    })
    
    // 添加兼容性总结
    if (compatibility.compatible) {
      results.push({
        id: 'compatibility-ok',
        level: 'info',
        category: 'process',
        title: '工艺兼容性检查通过',
        message: '所选订单项工艺兼容，可以合并生产',
        affectedItems: orderItems.map(item => item.id)
      })
    }
    
    return results
  }
}

export const processCompatibilityService = new ProcessCompatibilityService()