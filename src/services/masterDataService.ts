// src/services/masterDataService.ts
import type {
  Equipment, WorkCenter, Material, ProductFamily, ProcessStep, WipBuffer,
  ProcessSegment, WipWarehouse, Routing
} from '@/types/masterdata';

// 使用 Vite 的 import.meta.glob 直接加载所有 mock 数据
// 这样更可靠，避免了在不同部署环境下 fetch 路径的问题
const modules = import.meta.glob('/public/mock/masterdata/*.json');

class MasterDataService {
  // 内存数据存储，模拟数据库
  private dataStore = new Map<string, any[]>();
  private initialized = false;

  // 一次性初始化所有数据
  private async initializeDataStore(): Promise<void> {
    if (this.initialized) return;

    for (const path in modules) {
      const key = path.replace('/public/mock/masterdata/', '').replace('.json', '');
      const module: any = await modules[path]();
      this.dataStore.set(key, module.default || []);
    }
    this.initialized = true;
  }

  private async getStore<T>(key: string): Promise<T[]> {
    await this.initializeDataStore();
    return this.dataStore.get(key) || [];
  }

  private generateId(prefix: string): string {
    return `${prefix}-${Date.now()}`;
  }

  // --- 通用 CRUD ---
  private async createEntity<T extends { id: string }>(storeKey: string, entityData: Omit<T, 'id'>, idPrefix: string): Promise<T> {
    const store = await this.getStore<T>(storeKey);
    const newItem = {
      ...entityData,
      id: this.generateId(idPrefix),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    } as T;
    store.push(newItem);
    this.dataStore.set(storeKey, store);
    return newItem;
  }

  private async updateEntity<T extends { id: string }>(storeKey: string, id: string, updates: Partial<T>): Promise<T | null> {
    const store = await this.getStore<T>(storeKey);
    const index = store.findIndex(item => item.id === id);
    if (index === -1) return null;
    store[index] = { ...store[index], ...updates, updatedAt: new Date().toISOString() };
    this.dataStore.set(storeKey, store);
    return store[index];
  }

  private async deleteEntity(storeKey: string, id: string): Promise<boolean> {
    const store = await this.getStore<any>(storeKey);
    const initialLength = store.length;
    const newStore = store.filter(item => item.id !== id);
    if (newStore.length === initialLength) return false;
    this.dataStore.set(storeKey, newStore);
    return true;
  }

  // --- API 方法 ---
  
  getEquipments = () => this.getStore<Equipment>('equipments');
  createEquipment = (data: Omit<Equipment, 'id'>) => this.createEntity('equipments', data, 'EQ');
  updateEquipment = (id: string, data: Partial<Equipment>) => this.updateEntity('equipments', id, data);
  deleteEquipment = (id: string) => this.deleteEntity('equipments', id);

  getWorkCenters = () => this.getStore<WorkCenter>('workCenters');
  createWorkCenter = (data: Omit<WorkCenter, 'id'>) => this.createEntity('workCenters', data, 'WC');
  updateWorkCenter = (id: string, data: Partial<WorkCenter>) => this.updateEntity('workCenters', id, data);
  deleteWorkCenter = (id: string) => this.deleteEntity('workCenters', id);

  getMaterials = () => this.getStore<Material>('materials');
  createMaterial = (data: Omit<Material, 'id'>) => this.createEntity('materials', data, 'MAT');
  updateMaterial = (id: string, data: Partial<Material>) => this.updateEntity('materials', id, data);
  deleteMaterial = (id: string) => this.deleteEntity('materials', id);

  getProductFamilies = () => this.getStore<ProductFamily>('productFamilies');
  createProductFamily = (data: Omit<ProductFamily, 'id'>) => this.createEntity('productFamilies', data, 'PF');
  updateProductFamily = (id: string, data: Partial<ProductFamily>) => this.updateEntity('productFamilies', id, data);
  deleteProductFamily = (id: string) => this.deleteEntity('productFamilies', id);

  getProcessSteps = () => this.getStore<ProcessStep>('processSteps');
  createProcessStep = (data: Omit<ProcessStep, 'id'>) => this.createEntity('processSteps', data, 'PS');
  updateProcessStep = (id: string, data: Partial<ProcessStep>) => this.updateEntity('processSteps', id, data);
  deleteProcessStep = (id: string) => this.deleteEntity('processSteps', id);

  getWipBuffers = () => this.getStore<WipBuffer>('wipBuffers');
  createWipBuffer = (data: Omit<WipBuffer, 'id'>) => this.createEntity('wipBuffers', data, 'BUF');
  updateWipBuffer = (id: string, data: Partial<WipBuffer>) => this.updateEntity('wipBuffers', id, data);
  deleteWipBuffer = (id: string) => this.deleteEntity('wipBuffers', id);

  getProcessSegments = () => this.getStore<ProcessSegment>('processSegments');
  createProcessSegment = (data: Omit<ProcessSegment, 'id'>) => this.createEntity('processSegments', data, 'SEG');
  updateProcessSegment = (id: string, data: Partial<ProcessSegment>) => this.updateEntity('processSegments', id, data);
  deleteProcessSegment = (id: string) => this.deleteEntity('processSegments', id);

  getRoutings = () => this.getStore<Routing>('routings');
  createRouting = (data: Omit<Routing, 'id'>) => this.createEntity('routings', data, 'RT');
  updateRouting = (id: string, data: Partial<Routing>) => this.updateEntity('routings', id, data);
  deleteRouting = (id: string) => this.deleteEntity('routings', id);

  getWipWarehouses = () => this.getStore<WipWarehouse>('wipWarehouses');

  // --- 业务逻辑关联方法 ---

  /**
   * 获取工作中心的关联设备详情
   */
  async getWorkCenterEquipments(workCenterId: string): Promise<Equipment[]> {
    const workCenters = await this.getWorkCenters();
    const equipments = await this.getEquipments();

    const workCenter = workCenters.find(wc => wc.id === workCenterId);
    if (!workCenter) return [];

    return equipments.filter(eq => workCenter.equipmentIds.includes(eq.id));
  }

  /**
   * 获取设备所属的工作中心
   */
  async getEquipmentWorkCenters(equipmentId: string): Promise<WorkCenter[]> {
    const workCenters = await this.getWorkCenters();
    return workCenters.filter(wc => wc.equipmentIds.includes(equipmentId));
  }

  /**
   * 获取工作中心分配的标准工序
   */
  async getWorkCenterProcessSteps(workCenterId: string): Promise<ProcessStep[]> {
    const processSteps = await this.getProcessSteps();
    return processSteps.filter(ps => ps.assigneeIds.includes(workCenterId));
  }

  /**
   * 获取标准工序分配的工作中心详情
   */
  async getProcessStepWorkCenters(processStepId: string): Promise<WorkCenter[]> {
    const processSteps = await this.getProcessSteps();
    const workCenters = await this.getWorkCenters();

    const processStep = processSteps.find(ps => ps.id === processStepId);
    if (!processStep) return [];

    return workCenters.filter(wc => processStep.assigneeIds.includes(wc.id));
  }

  /**
   * 计算工作中心的综合产能
   * 基于关联设备的产能参数和工作中心效率
   */
  async calculateWorkCenterCapacity(workCenterId: string): Promise<{
    totalCapacity: number;
    availableCapacity: number;
    utilizationRate: number;
    equipmentDetails: Array<{
      equipmentId: string;
      name: string;
      status: string;
      capacity: number;
      efficiency: number;
    }>;
  }> {
    const workCenter = (await this.getWorkCenters()).find(wc => wc.id === workCenterId);
    const equipments = await this.getWorkCenterEquipments(workCenterId);

    if (!workCenter || equipments.length === 0) {
      return {
        totalCapacity: 0,
        availableCapacity: 0,
        utilizationRate: 0,
        equipmentDetails: []
      };
    }

    const equipmentDetails = equipments.map(eq => {
      // 根据设备类型计算基础产能
      let baseCapacity = 0;
      if (eq.parameters.speed) {
        baseCapacity = eq.parameters.speed * 8; // 8小时工作制
      } else if (eq.parameters.area) {
        baseCapacity = eq.parameters.area * 3; // 钢化炉按炉次计算
      } else {
        baseCapacity = 100; // 默认产能
      }

      const equipmentCapacity = baseCapacity * (eq.parameters.efficiency || 100) / 100;

      return {
        equipmentId: eq.id,
        name: eq.name,
        status: eq.status,
        capacity: equipmentCapacity,
        efficiency: eq.parameters.efficiency || 100
      };
    });

    const totalCapacity = equipmentDetails.reduce((sum, eq) => sum + eq.capacity, 0);
    const availableCapacity = equipmentDetails
      .filter(eq => eq.status === 'running' || eq.status === 'idle')
      .reduce((sum, eq) => sum + eq.capacity, 0);

    const workCenterEfficiency = (workCenter.efficiency || 100) / 100;
    const finalAvailableCapacity = availableCapacity * workCenterEfficiency;

    return {
      totalCapacity,
      availableCapacity: finalAvailableCapacity,
      utilizationRate: totalCapacity > 0 ? (finalAvailableCapacity / totalCapacity) * 100 : 0,
      equipmentDetails
    };
  }

  /**
   * 设备状态变更时的级联影响
   */
  async updateEquipmentStatusWithCascade(equipmentId: string, newStatus: Equipment['status']): Promise<{
    equipment: Equipment | null;
    affectedWorkCenters: WorkCenter[];
    affectedProcessSteps: ProcessStep[];
  }> {
    // 更新设备状态
    const equipment = await this.updateEquipment(equipmentId, { status: newStatus });

    // 获取受影响的工作中心
    const affectedWorkCenters = await this.getEquipmentWorkCenters(equipmentId);

    // 获取受影响的标准工序
    const affectedProcessSteps: ProcessStep[] = [];
    for (const workCenter of affectedWorkCenters) {
      const processSteps = await this.getWorkCenterProcessSteps(workCenter.id);
      affectedProcessSteps.push(...processSteps);
    }

    return {
      equipment,
      affectedWorkCenters,
      affectedProcessSteps: [...new Set(affectedProcessSteps)] // 去重
    };
  }

  /**
   * 验证数据一致性
   */
  async validateDataConsistency(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const equipments = await this.getEquipments();
      const workCenters = await this.getWorkCenters();
      const processSteps = await this.getProcessSteps();

      // 检查工作中心引用的设备是否存在
      const equipmentIds = new Set(equipments.map(eq => eq.id));
      for (const workCenter of workCenters) {
        for (const equipmentId of workCenter.equipmentIds) {
          if (!equipmentIds.has(equipmentId)) {
            errors.push(`工作中心 ${workCenter.name} 引用了不存在的设备 ${equipmentId}`);
          }
        }
      }

      // 检查标准工序引用的工作中心是否存在
      const workCenterIds = new Set(workCenters.map(wc => wc.id));
      for (const processStep of processSteps) {
        if (processStep.type === 'internal') {
          for (const assigneeId of processStep.assigneeIds) {
            if (!workCenterIds.has(assigneeId)) {
              errors.push(`标准工序 ${processStep.name} 引用了不存在的工作中心 ${assigneeId}`);
            }
          }
        }
      }

      // 检查是否有设备未分配到工作中心
      const assignedEquipmentIds = new Set();
      workCenters.forEach(wc => wc.equipmentIds.forEach(id => assignedEquipmentIds.add(id)));

      for (const equipment of equipments) {
        if (!assignedEquipmentIds.has(equipment.id)) {
          warnings.push(`设备 ${equipment.name} 未分配到任何工作中心`);
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`数据验证过程中发生错误: ${error}`],
        warnings: []
      };
    }
  }
}

export const masterDataService = new MasterDataService();
