import type {
  OptimizedBatch,
  PreScheduleResult,
  ScheduledBatch,
  GanttChartData,
  SchedulingMetrics,
  SchedulingConstraint,
  AssignedResource,
  GanttViewConfiguration
} from '@/types/scheduling';
import { useMasterDataStore } from '@/stores/masterDataStore';
import { processSegmentGanttService } from './processSegmentGanttService';

/**
 * 预排产服务
 * 基于标准工时和设备产能进行初步排产
 */
export class PreSchedulingService {
  private masterDataStore = useMasterDataStore();

  /**
   * 生成预排产方案
   */
  async generatePreSchedule(
    batches: OptimizedBatch[],
    viewConfig?: GanttViewConfiguration
  ): Promise<PreScheduleResult> {
    console.log('开始生成预排产方案，批次数量:', batches.length);
    
    // 模拟计算时间
    await this.simulateCalculationTime();
    
    // 1. 加载主数据
    await this.loadMasterData();
    
    // 2. 分析约束条件
    const constraints = await this.analyzeConstraints(batches);
    
    // 3. 计算资源需求
    const resourceRequirements = await this.calculateResourceRequirements(batches);
    
    // 4. 生成排产计划
    const scheduledBatches = await this.scheduleBatches(batches, resourceRequirements);
    
    // 5. 生成甘特图数据（工艺段视图）
    const ganttData = await this.generateGanttData(scheduledBatches);

    // 6. 计算预估指标
    const estimatedMetrics = await this.calculateEstimatedMetrics(scheduledBatches);

    // 7. 生成任务列表
    const tasks = this.generateTasksFromBatches(scheduledBatches);
    
    const result: PreScheduleResult = {
      scheduleId: `pre_schedule_${Date.now()}`,
      batches: scheduledBatches,
      tasks,
      ganttData,
      estimatedMetrics,
      constraints,
      createdAt: new Date().toISOString()
    };
    
    console.log('预排产方案生成完成:', result);
    return result;
  }

  /**
   * 从批次生成任务列表 (模拟)
   */
  private generateTasksFromBatches(scheduledBatches: ScheduledBatch[]): any[] {
    const tasks: any[] = [];
    scheduledBatches.forEach(batch => {
      batch.items.forEach(item => {
        item.processFlow.forEach((step, index) => {
          const taskId = `${item.id}_${step.stepCode}`;
          tasks.push({
            id: taskId,
            name: `${item.specifications.name} - ${step.stepName}`,
            type: step.workstation === 'cutting' ? 'cutting' : 'processing',
            batchId: batch.id,
            itemId: item.id,
            startTime: batch.scheduledStartTime, // 简化处理，实际应更精确
            endTime: batch.scheduledEndTime,
            dependencies: index > 0 ? [`${item.id}_${item.processFlow[index - 1].stepCode}`] : [],
          });
        });
      });
    });
    return tasks;
  }

  /**
   * 模拟计算时间（实际项目中会是真实的算法计算）
   */
  private async simulateCalculationTime(): Promise<void> {
    // 模拟5-15秒的计算时间
    const calculationTime = 5000 + Math.random() * 10000;
    await new Promise(resolve => setTimeout(resolve, calculationTime));
  }

  /**
   * 加载主数据
   */
  private async loadMasterData(): Promise<void> {
    // 确保主数据已加载
    if (this.masterDataStore.equipments.length === 0) {
      await this.masterDataStore.fetchEquipments();
    }
    if (this.masterDataStore.workCenters.length === 0) {
      await this.masterDataStore.fetchWorkCenters();
    }
  }

  /**
   * 分析约束条件
   */
  private async analyzeConstraints(batches: OptimizedBatch[]): Promise<SchedulingConstraint[]> {
    const constraints: SchedulingConstraint[] = [];
    
    // 设备产能约束
    const equipmentConstraints = this.analyzeEquipmentConstraints(batches);
    constraints.push(...equipmentConstraints);
    
    // 物料约束
    const materialConstraints = this.analyzeMaterialConstraints(batches);
    constraints.push(...materialConstraints);
    
    // 交期约束
    const deliveryConstraints = this.analyzeDeliveryConstraints(batches);
    constraints.push(...deliveryConstraints);
    
    return constraints;
  }

  /**
   * 分析设备产能约束
   */
  private analyzeEquipmentConstraints(batches: OptimizedBatch[]): SchedulingConstraint[] {
    const constraints: SchedulingConstraint[] = [];

    // 检查切割设备产能
    const cuttingBatches = batches.filter(batch => {
      // 检查 workstation 字段（单数）或 workstations 字段（复数）
      if (batch.workstation === 'cutting' || batch.workstation === 'cold_processing') {
        return true;
      }
      if (batch.workstations && batch.workstations.includes('cutting')) {
        return true;
      }
      // 检查工艺流程中是否包含切割工序
      if (batch.processFlow && batch.processFlow.some(step =>
        step.stepName === '切割' || step.workstation === 'cold_processing'
      )) {
        return true;
      }
      return false;
    });
    
    if (cuttingBatches.length > 8) {
      constraints.push({
        type: 'equipment',
        description: '切割设备产能可能不足，建议分批排产',
        severity: 'medium',
        affectedBatches: cuttingBatches.map(b => b.id)
      });
    }
    
    // 检查钢化炉产能
    const temperingBatches = batches.filter(batch => {
      // 检查 workstation 字段（单数）或 workstations 字段（复数）
      if (batch.workstation === 'tempering' || batch.workstation === 'hot_processing') {
        return true;
      }
      if (batch.workstations && batch.workstations.includes('tempering')) {
        return true;
      }
      // 检查工艺流程中是否包含钢化工序
      if (batch.processFlow && batch.processFlow.some(step =>
        step.stepName === '钢化' || step.workstation === 'hot_processing'
      )) {
        return true;
      }
      return false;
    });
    
    if (temperingBatches.length > 6) {
      constraints.push({
        type: 'equipment',
        description: '钢化炉产能紧张，可能影响交期',
        severity: 'high',
        affectedBatches: temperingBatches.map(b => b.id)
      });
    }
    
    return constraints;
  }

  /**
   * 分析物料约束
   */
  private analyzeMaterialConstraints(batches: OptimizedBatch[]): SchedulingConstraint[] {
    const constraints: SchedulingConstraint[] = [];
    
    // 统计原片需求
    const materialRequirements = new Map<string, number>();
    
    batches.forEach(batch => {
      batch.items.forEach(item => {
        const specs = item.specifications;
        const materialKey = `${specs.thickness}mm_${specs.glassType}_${specs.color}`;
        const currentQty = materialRequirements.get(materialKey) || 0;
        materialRequirements.set(materialKey, currentQty + item.selectedQuantity);
      });
    });
    
    // 检查是否超过常规库存
    materialRequirements.forEach((quantity, materialKey) => {
      if (quantity > 1000) {
        constraints.push({
          type: 'material',
          description: `${materialKey} 需求量较大(${quantity}片)，请确认库存充足`,
          severity: 'medium',
          affectedBatches: batches.map(b => b.id)
        });
      }
    });
    
    return constraints;
  }

  /**
   * 分析交期约束
   */
  private analyzeDeliveryConstraints(batches: OptimizedBatch[]): SchedulingConstraint[] {
    const constraints: SchedulingConstraint[] = [];
    
    const urgentBatches = batches.filter(batch => batch.priority === 'urgent');
    
    if (urgentBatches.length > 3) {
      constraints.push({
        type: 'delivery',
        description: `有${urgentBatches.length}个紧急批次，建议优先安排`,
        severity: 'high',
        affectedBatches: urgentBatches.map(b => b.id)
      });
    }
    
    return constraints;
  }

  /**
   * 计算资源需求
   */
  private async calculateResourceRequirements(batches: OptimizedBatch[]): Promise<Map<string, number>> {
    const requirements = new Map<string, number>();

    batches.forEach(batch => {
      // 获取批次的工作站列表
      const workstations = this.getBatchWorkstations(batch);

      workstations.forEach(workstation => {
        const currentTime = requirements.get(workstation) || 0;
        const duration = batch.estimatedTime || batch.estimatedDuration || 480;
        requirements.set(workstation, currentTime + duration);
      });
    });
    
    return requirements;
  }

  /**
   * 排产批次
   */
  private async scheduleBatches(
    batches: OptimizedBatch[], 
    resourceRequirements: Map<string, number>
  ): Promise<ScheduledBatch[]> {
    const scheduledBatches: ScheduledBatch[] = [];
    
    // 按优先级排序
    const sortedBatches = [...batches].sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
    
    // 设置排产开始时间为明天早上8点，避免时间冲突
    let currentTime = new Date();
    currentTime.setDate(currentTime.getDate() + 1); // 明天
    currentTime.setHours(8, 0, 0, 0); // 早上8点开始

    console.log('排产开始时间:', currentTime.toISOString());
    
    for (const batch of sortedBatches) {
      const scheduledBatch = await this.scheduleSingleBatch(batch, currentTime);
      scheduledBatches.push(scheduledBatch);

      // 更新当前时间，添加15分钟的缓冲时间
      currentTime = new Date(scheduledBatch.scheduledEndTime);
      currentTime.setMinutes(currentTime.getMinutes() + 15);
    }
    
    return scheduledBatches;
  }

  /**
   * 排产单个批次
   */
  private async scheduleSingleBatch(
    batch: OptimizedBatch,
    startTime: Date
  ): Promise<ScheduledBatch> {
    // 获取预估时间，优先使用 estimatedTime，否则使用默认计算
    // 确保最小时间为30分钟，最大时间为8小时
    let duration = batch.estimatedTime || (batch.items?.length || 1) * 60; // 分钟
    duration = Math.max(30, Math.min(480, duration)); // 30分钟到8小时之间

    const endTime = new Date(startTime.getTime() + duration * 60 * 1000);

    console.log(`批次 ${batch.id} 排产时间:`, {
      start: startTime.toISOString(),
      end: endTime.toISOString(),
      duration: `${duration}分钟`
    });

    // 分配资源
    const assignedResources = await this.assignResources(batch);

    // 估算成本
    const estimatedCost = this.calculateEstimatedCost(batch);

    return {
      ...batch,
      scheduledStartTime: startTime.toISOString(),
      scheduledEndTime: endTime.toISOString(),
      assignedResources,
      estimatedCost
    };
  }

  /**
   * 分配资源
   */
  private async assignResources(batch: OptimizedBatch): Promise<AssignedResource[]> {
    const resources: AssignedResource[] = [];
    
    // 为工作站分配资源
    const workstation = batch.workstation || 'cutting'; // 使用主要工作站
    const resource = await this.findAvailableResource(workstation);
    if (resource) {
      const duration = batch.estimatedTime || (batch.items.length * 60);
      resources.push({
        resourceId: resource.id,
        resourceName: resource.name,
        resourceType: 'equipment',
        allocatedTime: duration,
        utilization: 75 + Math.random() * 20 // 模拟利用率
      });
    }
    
    return resources;
  }

  /**
   * 查找可用资源
   */
  private async findAvailableResource(workstation: string): Promise<any> {
    // 简化的资源查找逻辑
    const workstationEquipmentMap: Record<string, string> = {
      'cutting': 'CUT-01',
      'edging': 'EDG-01',
      'tempering': 'TMP-01',
      'laminating': 'LAM-01',
      'insulating': 'INS-01'
    };
    
    const equipmentId = workstationEquipmentMap[workstation];
    if (equipmentId) {
      return {
        id: equipmentId,
        name: `${workstation.toUpperCase()}-设备01`,
        type: 'equipment'
      };
    }
    
    return null;
  }

  /**
   * 获取批次的工作站列表
   */
  private getBatchWorkstations(batch: OptimizedBatch): string[] {
    const workstations: string[] = [];

    // 优先使用 workstations 数组
    if (batch.workstations && Array.isArray(batch.workstations)) {
      return batch.workstations;
    }

    // 使用单个 workstation
    if (batch.workstation) {
      workstations.push(batch.workstation);
    }

    // 从工艺流程中提取工作站
    if (batch.processFlow && batch.processFlow.length > 0) {
      batch.processFlow.forEach(step => {
        if (step.workstation && !workstations.includes(step.workstation)) {
          workstations.push(step.workstation);
        }
      });
    }

    // 如果没有找到任何工作站，返回默认值
    if (workstations.length === 0) {
      workstations.push('cold_processing'); // 默认冷工段
    }

    return workstations;
  }

  /**
   * 计算预估成本
   */
  private calculateEstimatedCost(batch: OptimizedBatch): number {
    // 简化的成本计算
    const baseCostPerPiece = 50; // 每片基础成本
    const workstations = this.getBatchWorkstations(batch);
    const complexityMultiplier = workstations.length * 0.2;

    return batch.totalQuantity * baseCostPerPiece * (1 + complexityMultiplier);
  }

  /**
   * 生成甘特图数据（工艺段视图）
   */
  private async generateGanttData(
    scheduledBatches: ScheduledBatch[]
  ): Promise<GanttChartData> {
    console.log('生成工艺段甘特图数据，批次数量:', scheduledBatches.length);

    // 使用工艺段甘特图服务生成数据
    return await processSegmentGanttService.convertToProcessSegmentView(scheduledBatches);
  }





  /**
   * 计算预估指标
   */
  private async calculateEstimatedMetrics(scheduledBatches: ScheduledBatch[]): Promise<SchedulingMetrics> {
    const totalDuration = this.calculateTotalDuration(scheduledBatches);
    const equipmentUtilization = this.calculateEquipmentUtilization(scheduledBatches);
    const deliveryAchievement = this.calculateDeliveryAchievement(scheduledBatches);
    const materialUtilization = this.calculateMaterialUtilization(scheduledBatches);
    
    return {
      totalDuration,
      equipmentUtilization,
      deliveryAchievement,
      materialUtilization
    };
  }

  /**
   * 计算总工期
   */
  private calculateTotalDuration(scheduledBatches: ScheduledBatch[]): number {
    if (scheduledBatches.length === 0) return 0;

    const startTime = new Date(scheduledBatches[0].scheduledStartTime);
    const endTime = new Date(scheduledBatches[scheduledBatches.length - 1].scheduledEndTime);

    return (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24); // 转换为天
  }

  /**
   * 计算设备利用率
   */
  private calculateEquipmentUtilization(scheduledBatches: ScheduledBatch[]): number {
    const totalUtilization = scheduledBatches.reduce((sum, batch) => {
      const avgUtilization = batch.assignedResources.reduce((resSum, res) => 
        resSum + res.utilization, 0) / batch.assignedResources.length;
      return sum + (avgUtilization || 0);
    }, 0);
    
    return scheduledBatches.length > 0 ? totalUtilization / scheduledBatches.length : 0;
  }

  /**
   * 计算交期达成率
   */
  private calculateDeliveryAchievement(scheduledBatches: ScheduledBatch[]): number {
    // 简化计算：基于优先级分布
    const urgentCount = scheduledBatches.filter(b => b.priority === 'urgent').length;
    const highCount = scheduledBatches.filter(b => b.priority === 'high').length;
    const totalCount = scheduledBatches.length;
    
    if (totalCount === 0) return 0;
    
    // 紧急和高优先级批次影响交期达成率
    const riskFactor = (urgentCount * 0.1 + highCount * 0.05) / totalCount;
    return Math.max(85, 95 - riskFactor * 100);
  }

  /**
   * 计算原片利用率（预估）
   */
  private calculateMaterialUtilization(scheduledBatches: ScheduledBatch[]): number {
    // 基于批次利用率的平均值
    const totalUtilization = scheduledBatches.reduce((sum, batch) => sum + batch.utilization, 0);
    return scheduledBatches.length > 0 ? totalUtilization / scheduledBatches.length : 0;
  }
}

// 导出服务实例
export const preSchedulingService = new PreSchedulingService();
