/**
 * 工艺段甘特图服务
 * 负责将排产数据转换为按工艺段组织的甘特图数据
 */

import type { 
  GanttChartData, 
  GanttResource, 
  GanttTask, 
  ProcessSegmentResource,
  GanttDrillDownData,
  ScheduledBatch 
} from '@/types/scheduling';
import type { ProcessSegment, ProcessStep } from '@/types/masterdata';
import { useMasterDataStore } from '@/stores/masterDataStore';

export class ProcessSegmentGanttService {
  private masterDataStore = useMasterDataStore();

  /**
   * 将排产数据转换为工艺段视图的甘特图数据
   */
  async convertToProcessSegmentView(scheduledBatches: ScheduledBatch[]): Promise<GanttChartData> {
    console.log('🔄 开始转换工艺段甘特图数据...', {
      batchesCount: scheduledBatches.length
    });

    // 确保主数据已加载
    await this.ensureMasterDataLoaded();

    // 获取所有工艺段
    let processSegments = this.masterDataStore.processSegments;

    // 如果没有工艺段数据，创建默认工艺段
    if (processSegments.length === 0) {
      console.log('⚠️ 没有工艺段数据，创建默认工艺段...');
      processSegments = this.createDefaultProcessSegments();
    }

    console.log('📋 工艺段数据:', {
      segmentsCount: processSegments.length,
      segments: processSegments.map(s => ({ id: s.id, name: s.name }))
    });

    // 创建工艺段资源
    const segmentResources = await this.createProcessSegmentResources(processSegments, scheduledBatches);
    console.log('🏭 工艺段资源创建完成:', {
      resourcesCount: segmentResources.length,
      resources: segmentResources.map(r => ({ id: r.id, name: r.name, type: r.type }))
    });

    // 聚合任务到工艺段级别
    const segmentTasks = this.aggregateTasksBySegment(scheduledBatches);
    console.log('📊 任务聚合完成:', {
      tasksCount: segmentTasks.length,
      tasks: segmentTasks.map(t => ({ id: t.id, name: t.name, resourceId: t.resourceId }))
    });

    // 计算时间范围
    const timeRange = this.calculateTimeRange(segmentTasks);
    console.log('⏰ 时间范围计算完成:', timeRange);

    const result = {
      resources: segmentResources,
      tasks: segmentTasks,
      timeRange
    };

    console.log('✅ 工艺段甘特图数据转换完成:', {
      resourcesCount: result.resources.length,
      tasksCount: result.tasks.length,
      hasTimeRange: !!result.timeRange
    });

    return result;
  }

  /**
   * 创建工艺段资源列表
   */
  private async createProcessSegmentResources(
    processSegments: ProcessSegment[], 
    scheduledBatches: ScheduledBatch[]
  ): Promise<ProcessSegmentResource[]> {
    const resources: ProcessSegmentResource[] = [];

    for (const segment of processSegments) {
      // 获取工艺段内的设备和工序
      const equipmentIds = await this.getEquipmentIdsForSegment(segment);
      const processStepIds = segment.processStepIds || [];

      // 计算工艺段的产能和利用率
      const capacity = await this.calculateSegmentCapacity(segment, equipmentIds);
      const utilization = this.calculateSegmentUtilization(segment, scheduledBatches);
      const status = this.determineSegmentStatus(utilization);

      resources.push({
        id: segment.id,
        name: segment.name,
        type: 'process_segment',
        capacity: capacity.total,
        utilization,
        equipmentIds,
        processStepIds,
        status,
        workstationGroup: this.getWorkstationGroupForSegment(segment),
        totalCapacity: capacity.total,
        availableCapacity: capacity.available,
        currentLoad: capacity.used
      });
    }

    return resources;
  }

  /**
   * 将任务聚合到工艺段级别
   */
  private aggregateTasksBySegment(scheduledBatches: ScheduledBatch[]): GanttTask[] {
    const segmentTasks: GanttTask[] = [];

    for (const batch of scheduledBatches) {
      // 按工艺段分组批次中的任务
      const segmentGroups = this.groupTasksBySegment(batch);

      for (const [segmentId, tasks] of segmentGroups.entries()) {
        // 为每个工艺段创建聚合任务
        const aggregatedTask = this.createAggregatedTask(segmentId, batch, tasks);
        if (aggregatedTask) {
          segmentTasks.push(aggregatedTask);
        }
      }
    }

    return segmentTasks;
  }

  /**
   * 按工艺段分组批次任务
   */
  private groupTasksBySegment(batch: ScheduledBatch): Map<string, any[]> {
    const segmentGroups = new Map<string, any[]>();

    // 处理批次的工作站信息
    if (batch.workstation) {
      const segmentId = this.getSegmentIdForWorkstation(batch.workstation);
      if (segmentId) {
        segmentGroups.set(segmentId, [{
          batch,
          workstation: batch.workstation
        }]);
      }
    }

    // 如果批次有详细的工单项和工艺流程，则按工艺流程分组
    if (batch.items && batch.items.length > 0) {
      for (const item of batch.items) {
        if (item.processFlow) {
          for (const step of item.processFlow) {
            const segmentId = this.getSegmentIdForWorkstation(step.workstationGroup || step.workstation);

            if (segmentId) {
              if (!segmentGroups.has(segmentId)) {
                segmentGroups.set(segmentId, []);
              }
              segmentGroups.get(segmentId)!.push({
                step,
                item,
                batchId: batch.id
              });
            }
          }
        }
      }
    }

    return segmentGroups;
  }

  /**
   * 创建聚合任务
   */
  private createAggregatedTask(segmentId: string, batch: ScheduledBatch, tasks: any[]): GanttTask | null {
    if (!batch.scheduledStartTime || !batch.scheduledEndTime) {
      console.warn(`批次 ${batch.id} 缺少调度时间信息`);
      return null;
    }

    // 使用批次的实际调度时间
    const start = new Date(batch.scheduledStartTime);
    const end = new Date(batch.scheduledEndTime);

    const duration = (end.getTime() - start.getTime()) / (1000 * 60 * 60); // 小时

    return {
      id: `${segmentId}-${batch.id}`,
      name: batch.name || batch.batchName || `批次${batch.id}`,
      start: start.toISOString(),
      end: end.toISOString(),
      duration,
      resourceId: segmentId,
      batchId: batch.id,
      workstation: this.getSegmentName(segmentId),
      status: batch.status || 'planned',
      dependencies: []
    };
  }

  /**
   * 获取工艺段的下钻数据
   */
  async getDrillDownData(segmentId: string, scheduledBatches: ScheduledBatch[]): Promise<GanttDrillDownData> {
    const segment = this.masterDataStore.getProcessSegmentById(segmentId);
    if (!segment) {
      throw new Error(`工艺段 ${segmentId} 不存在`);
    }

    // 获取工艺段内的设备
    const equipmentIds = await this.getEquipmentIdsForSegment(segment);
    const childResources: GanttResource[] = [];

    for (const equipmentId of equipmentIds) {
      const equipment = this.masterDataStore.equipments.find(e => e.id === equipmentId);
      if (equipment) {
        childResources.push({
          id: equipment.id,
          name: equipment.name,
          type: 'equipment',
          capacity: equipment.parameters.area || 100,
          utilization: this.calculateEquipmentUtilization(equipment.id, scheduledBatches),
          location: equipment.location
        });
      }
    }

    // 获取该工艺段的详细任务
    const childTasks = this.getDetailedTasksForSegment(segmentId, scheduledBatches);

    return {
      parentResourceId: segmentId,
      childResources,
      childTasks,
      aggregationLevel: 'equipment'
    };
  }

  /**
   * 创建默认工艺段（当主数据不可用时）
   */
  private createDefaultProcessSegments(): ProcessSegment[] {
    return [
      {
        id: 'SEG-COLD',
        name: '冷工段',
        description: '玻璃冷加工工艺段（切割、磨边、钻孔、清洗）',
        nodes: [],
        edges: [],
        processStepIds: ['STEP-CUT', 'STEP-EDGE', 'STEP-DRILL', 'STEP-CLEAN'],
        wipBufferIds: []
      },
      {
        id: 'SEG-TEMPERING',
        name: '钢化工段',
        description: '玻璃钢化处理工艺段',
        nodes: [],
        edges: [],
        processStepIds: ['STEP-TEMPER'],
        wipBufferIds: []
      },
      {
        id: 'SEG-LAMINATING',
        name: '合片工段',
        description: '夹胶和中空玻璃加工工艺段',
        nodes: [],
        edges: [],
        processStepIds: ['STEP-LAM-PREP', 'STEP-LAM-PRESS', 'STEP-INS-ASSEMBLE', 'STEP-INS-SEAL'],
        wipBufferIds: []
      },
      {
        id: 'SEG-SPECIAL',
        name: '特殊工段',
        description: '特殊工艺处理段（热弯、防火等）',
        nodes: [],
        edges: [],
        processStepIds: ['STEP-BEND', 'STEP-FIRE-RESIST'],
        wipBufferIds: []
      },
      {
        id: 'SEG-COATING',
        name: '镀膜工段',
        description: '玻璃表面镀膜处理工艺段',
        nodes: [],
        edges: [],
        processStepIds: ['STEP-COATING'],
        wipBufferIds: []
      },
      {
        id: 'SEG-QUALITY',
        name: '质检工段',
        description: '产品质量检测工艺段',
        nodes: [],
        edges: [],
        processStepIds: ['STEP-QC'],
        wipBufferIds: []
      },
      {
        id: 'SEG-PACKAGING',
        name: '包装工段',
        description: '产品包装工艺段',
        nodes: [],
        edges: [],
        processStepIds: ['STEP-PACK'],
        wipBufferIds: []
      }
    ];
  }

  /**
   * 辅助方法：确保主数据已加载
   */
  private async ensureMasterDataLoaded(): Promise<void> {
    console.log('🔄 检查主数据加载状态...');

    if (this.masterDataStore.processSegments.length === 0) {
      console.log('📋 加载工艺段数据...');
      await this.masterDataStore.fetchProcessSegments();
    }
    if (this.masterDataStore.equipments.length === 0) {
      console.log('🏭 加载设备数据...');
      await this.masterDataStore.fetchEquipments();
    }
    if (this.masterDataStore.processSteps.length === 0) {
      console.log('⚙️ 加载工序数据...');
      await this.masterDataStore.fetchProcessSteps();
    }

    console.log('✅ 主数据加载完成:', {
      processSegments: this.masterDataStore.processSegments.length,
      equipments: this.masterDataStore.equipments.length,
      processSteps: this.masterDataStore.processSteps.length
    });
  }

  /**
   * 获取工艺段内的设备ID列表
   */
  private async getEquipmentIdsForSegment(segment: ProcessSegment): Promise<string[]> {
    const equipmentIds: string[] = [];
    
    // 从工艺段的节点中提取设备信息
    for (const node of segment.nodes) {
      if (node.type === 'ProcessStep') {
        const processStep = this.masterDataStore.getProcessStepById(node.entityId);
        if (processStep && processStep.assigneeIds) {
          // 假设 assigneeIds 包含工作中心ID，需要进一步获取设备ID
          for (const assigneeId of processStep.assigneeIds) {
            const workCenter = this.masterDataStore.workCenters.find(wc => wc.id === assigneeId);
            if (workCenter) {
              equipmentIds.push(...workCenter.equipmentIds);
            }
          }
        }
      }
    }

    return [...new Set(equipmentIds)]; // 去重
  }

  /**
   * 计算工艺段产能
   */
  private async calculateSegmentCapacity(segment: ProcessSegment, equipmentIds: string[]): Promise<{
    total: number;
    available: number;
    used: number;
  }> {
    let totalCapacity = 0;
    let availableCapacity = 0;
    let usedCapacity = 0;

    for (const equipmentId of equipmentIds) {
      const equipment = this.masterDataStore.equipments.find(e => e.id === equipmentId);
      if (equipment) {
        const capacity = equipment.parameters.area || 100;
        totalCapacity += capacity;
        
        if (equipment.status === 'running' || equipment.status === 'idle') {
          availableCapacity += capacity;
        }
        
        if (equipment.status === 'running') {
          usedCapacity += capacity;
        }
      }
    }

    return {
      total: totalCapacity,
      available: availableCapacity,
      used: usedCapacity
    };
  }

  /**
   * 计算工艺段利用率
   */
  private calculateSegmentUtilization(segment: ProcessSegment, scheduledBatches: ScheduledBatch[]): number {
    // 简化计算：基于批次数量和工艺段容量
    let totalWorkload = 0;
    const totalCapacity = 100; // 假设基础容量

    for (const batch of scheduledBatches) {
      for (const item of batch.items) {
        if (item.processFlow) {
          for (const step of item.processFlow) {
            const segmentId = this.getSegmentIdForWorkstation(step.workstationGroup || step.workstation);
            if (segmentId === segment.id) {
              totalWorkload += step.estimatedDuration || 0;
            }
          }
        }
      }
    }

    return Math.min(100, (totalWorkload / totalCapacity) * 100);
  }

  /**
   * 确定工艺段状态
   */
  private determineSegmentStatus(utilization: number): 'normal' | 'bottleneck' | 'idle' {
    if (utilization > 90) return 'bottleneck';
    if (utilization < 30) return 'idle';
    return 'normal';
  }

  /**
   * 获取工艺段对应的工段分组
   */
  private getWorkstationGroupForSegment(segment: ProcessSegment): string {
    // 根据工艺段名称映射到工段分组
    const nameMapping: Record<string, string> = {
      '冷工段': '冷工段',
      '冷加工段': '冷工段',
      '热工段': '热工段',
      '钢化工段': '热工段',
      '夹胶工段': '夹胶组',
      '合片工段': '夹胶组',
      '中空工段': '中空组',
      '包装工段': '包装组'
    };

    return nameMapping[segment.name] || segment.name;
  }

  /**
   * 根据工作站获取工艺段ID
   */
  private getSegmentIdForWorkstation(workstation: string): string | null {
    if (!workstation) return null;

    // 根据工作站名称映射到工艺段ID
    const workstationMapping: Record<string, string> = {
      // 冷工段
      '冷工段': 'SEG-COLD',
      'cold_processing': 'SEG-COLD',
      'cold_processing工作站': 'SEG-COLD',
      'cutting': 'SEG-COLD',
      'edging': 'SEG-COLD',

      // 钢化工段
      '钢化工段': 'SEG-TEMPERING',
      'tempering': 'SEG-TEMPERING',
      'tempering工作站': 'SEG-TEMPERING',
      '热工段': 'SEG-TEMPERING',
      'hot_processing': 'SEG-TEMPERING',

      // 合片工段（夹胶+中空）
      '合片工段': 'SEG-LAMINATING',
      'laminating': 'SEG-LAMINATING',
      'laminating工作站': 'SEG-LAMINATING',
      '夹胶组': 'SEG-LAMINATING',
      'insulating': 'SEG-LAMINATING',
      'insulating工作站': 'SEG-LAMINATING',
      '中空组': 'SEG-LAMINATING',

      // 特殊工段
      '特殊工段': 'SEG-SPECIAL',
      'special_processing': 'SEG-SPECIAL',
      'bending': 'SEG-SPECIAL',
      '热弯工段': 'SEG-SPECIAL',

      // 镀膜工段
      '镀膜工段': 'SEG-COATING',
      'coating': 'SEG-COATING',
      'coating工作站': 'SEG-COATING',

      // 质检工段
      '质检工段': 'SEG-QUALITY',
      'quality_control': 'SEG-QUALITY',
      'quality工作站': 'SEG-QUALITY',

      // 包装工段
      '包装工段': 'SEG-PACKAGING',
      'packaging': 'SEG-PACKAGING',
      'packaging工作站': 'SEG-PACKAGING'
    };

    // 先尝试精确匹配
    if (workstationMapping[workstation]) {
      return workstationMapping[workstation];
    }

    // 尝试模糊匹配
    const lowerWorkstation = workstation.toLowerCase();
    if (lowerWorkstation.includes('cold') || lowerWorkstation.includes('冷') ||
        lowerWorkstation.includes('cutting') || lowerWorkstation.includes('edging')) {
      return 'SEG-COLD';
    }
    if (lowerWorkstation.includes('tempering') || lowerWorkstation.includes('钢化') ||
        lowerWorkstation.includes('hot') || lowerWorkstation.includes('热')) {
      return 'SEG-TEMPERING';
    }
    if (lowerWorkstation.includes('lam') || lowerWorkstation.includes('夹胶') ||
        lowerWorkstation.includes('ins') || lowerWorkstation.includes('中空') ||
        lowerWorkstation.includes('合片')) {
      return 'SEG-LAMINATING';
    }
    if (lowerWorkstation.includes('coating') || lowerWorkstation.includes('镀膜')) {
      return 'SEG-COATING';
    }
    if (lowerWorkstation.includes('bending') || lowerWorkstation.includes('热弯') ||
        lowerWorkstation.includes('special') || lowerWorkstation.includes('特殊')) {
      return 'SEG-SPECIAL';
    }
    if (lowerWorkstation.includes('quality') || lowerWorkstation.includes('质检')) {
      return 'SEG-QUALITY';
    }
    if (lowerWorkstation.includes('pack') || lowerWorkstation.includes('包装')) {
      return 'SEG-PACKAGING';
    }

    // 默认返回冷工段
    console.warn(`未知工作站: ${workstation}，默认分配到冷工段`);
    return 'SEG-COLD';
  }

  /**
   * 获取工艺段名称
   */
  private getSegmentName(segmentId: string): string {
    const segment = this.masterDataStore.getProcessSegmentById(segmentId);
    return segment?.name || segmentId;
  }

  /**
   * 计算设备利用率
   */
  private calculateEquipmentUtilization(equipmentId: string, scheduledBatches: ScheduledBatch[]): number {
    // 简化实现
    return Math.random() * 100; // 实际应该基于排产数据计算
  }

  /**
   * 获取工艺段的详细任务
   */
  private getDetailedTasksForSegment(segmentId: string, scheduledBatches: ScheduledBatch[]): GanttTask[] {
    const tasks: GanttTask[] = [];
    
    for (const batch of scheduledBatches) {
      for (const item of batch.items) {
        if (item.processFlow) {
          for (const step of item.processFlow) {
            const stepSegmentId = this.getSegmentIdForWorkstation(step.workstationGroup || step.workstation);
            if (stepSegmentId === segmentId) {
              tasks.push({
                id: `${batch.id}-${item.id}-${step.stepName}`,
                name: `${step.stepName} - ${item.specifications?.productType || '产品'}`,
                start: batch.scheduledStartTime,
                end: batch.scheduledEndTime,
                duration: step.estimatedDuration || 0,
                resourceId: step.workstation,
                batchId: batch.id,
                workstation: step.workstation,
                status: 'planned'
              });
            }
          }
        }
      }
    }

    return tasks;
  }

  /**
   * 计算时间范围
   */
  private calculateTimeRange(tasks: GanttTask[]): { start: string; end: string } {
    if (tasks.length === 0) {
      const now = new Date();
      return {
        start: now.toISOString(),
        end: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()
      };
    }

    const startTimes = tasks.map(t => new Date(t.start));
    const endTimes = tasks.map(t => new Date(t.end));

    const rangeStart = new Date(Math.min(...startTimes.map(d => d.getTime())));
    const rangeEnd = new Date(Math.max(...endTimes.map(d => d.getTime())));

    // 添加一些缓冲时间，确保任务不会紧贴边界
    const bufferTime = 2 * 60 * 60 * 1000; // 2小时缓冲
    const adjustedStart = new Date(rangeStart.getTime() - bufferTime);
    const adjustedEnd = new Date(rangeEnd.getTime() + bufferTime);

    console.log('甘特图时间范围计算:', {
      tasksCount: tasks.length,
      originalRange: {
        start: rangeStart.toISOString(),
        end: rangeEnd.toISOString()
      },
      adjustedRange: {
        start: adjustedStart.toISOString(),
        end: adjustedEnd.toISOString()
      },
      taskTimes: tasks.map(t => ({
        id: t.id,
        start: t.start,
        end: t.end
      }))
    });

    return {
      start: adjustedStart.toISOString(),
      end: adjustedEnd.toISOString()
    };
  }
}

export const processSegmentGanttService = new ProcessSegmentGanttService();
