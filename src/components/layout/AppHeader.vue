<template>
  <header class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
    <div class="flex items-center gap-2 px-4">
      <SidebarTrigger class="-ml-1" />
      <Separator orientation="vertical" class="mr-2 h-4" />
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem class="hidden md:block">
            <BreadcrumbLink href="/">
              首页
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator class="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbPage>{{ currentPageTitle }}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
    
    <div class="ml-auto flex items-center gap-2 px-4">
      <!-- 搜索框 -->
      <div class="relative hidden md:block">
        <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="搜索功能..."
          class="w-[200px] pl-8 lg:w-[300px]"
          v-model="searchQuery"
          @keyup.enter="handleSearch"
        />
      </div>

      <!-- 通知中心 -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="icon" class="relative">
            <Bell class="h-4 w-4" />
            <Badge 
              v-if="unreadNotifications > 0"
              class="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
              variant="destructive"
            >
              {{ unreadNotifications > 9 ? '9+' : unreadNotifications }}
            </Badge>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-80">
          <DropdownMenuLabel class="flex items-center justify-between">
            通知中心
            <Button variant="ghost" size="sm" @click="markAllAsRead">
              全部已读
            </Button>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div class="max-h-80 overflow-y-auto">
            <div v-if="appStore.notifications.length === 0" class="p-4 text-center text-sm text-muted-foreground">
              暂无通知
            </div>
            <div v-else>
              <div
                v-for="notification in appStore.notifications.slice(0, 5)"
                :key="notification.id"
                class="flex items-start gap-3 p-3 hover:bg-accent cursor-pointer"
                :class="{ 'bg-accent/50': !notification.read }"
                @click="markAsRead(notification.id)"
              >
                <div class="flex-shrink-0">
                  <component 
                    :is="getNotificationIcon(notification.type)" 
                    class="h-4 w-4"
                    :class="getNotificationIconColor(notification.type)"
                  />
                </div>
                <div class="flex-1 space-y-1">
                  <p class="text-sm font-medium">{{ notification.title }}</p>
                  <p class="text-xs text-muted-foreground">{{ notification.message }}</p>
                  <p class="text-xs text-muted-foreground">{{ formatTime(notification.createdAt) }}</p>
                </div>
              </div>
            </div>
          </div>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <router-link to="/notifications" class="w-full text-center">
              查看全部通知
            </router-link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <!-- 用户菜单 -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="icon">
            <Avatar class="h-8 w-8">
              <AvatarImage :src="userStore.currentUser?.avatar || ''" :alt="userStore.currentUser?.name || ''" />
              <AvatarFallback>
                {{ userStore.currentUser?.name?.charAt(0) || 'U' }}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-56">
          <DropdownMenuLabel class="font-normal">
            <div class="flex flex-col space-y-1">
              <p class="text-sm font-medium leading-none">{{ userStore.currentUser?.name || '未登录' }}</p>
              <p class="text-xs leading-none text-muted-foreground">{{ userStore.currentUser?.email || '' }}</p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <router-link to="/profile" class="flex items-center">
              <User class="mr-2 h-4 w-4" />
              个人资料
            </router-link>
          </DropdownMenuItem>
          <DropdownMenuItem as-child>
            <router-link to="/settings" class="flex items-center">
              <Settings class="mr-2 h-4 w-4" />
              设置
            </router-link>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="handleLogout" class="text-red-600">
            <LogOut class="mr-2 h-4 w-4" />
            退出登录
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <!-- 主题切换 -->
      <Button variant="outline" size="icon" @click="toggleTheme">
        <Sun class="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
        <Moon class="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      </Button>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Separator } from '@/components/ui/separator'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Search,
  Bell,
  User,
  Settings,
  LogOut,
  Sun,
  Moon,
  AlertTriangle,
  Info,
  CheckCircle,
  AlertCircle
} from 'lucide-vue-next'

// 移除本地User接口定义，使用stores中的类型

// 移除本地Notification接口定义，使用stores中的类型

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()
const searchQuery = ref('')

// 页面标题映射
const pageTitleMap: Record<string, string> = {
  '/': '首页',
  '/dashboard': '仪表盘',
  '/crm': '客户关系',
  '/inventory': '库存管理',
  '/mes': '生产执行',
  '/procurement': '采购管理',
  '/quality': '质量管理',
  '/admin/users': '用户管理',
  // '/admin/metadata': '元数据管理',
  // '/metadata': '元数据管理',
  // '/metadata/materials': '物料元数据管理',
  '/admin/settings': '系统设置'
}

// 当前页面标题
const currentPageTitle = computed(() => {
  return pageTitleMap[route.path] || '未知页面'
})

// 未读通知数量（使用appStore）
const unreadNotifications = computed(() => {
  return appStore.unreadNotifications
})

// 获取通知图标
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'warning':
      return AlertTriangle
    case 'success':
      return CheckCircle
    case 'error':
      return AlertCircle
    default:
      return Info
  }
}

// 获取通知图标颜色
const getNotificationIconColor = (type: string) => {
  switch (type) {
    case 'warning':
      return 'text-yellow-500'
    case 'success':
      return 'text-green-500'
    case 'error':
      return 'text-red-500'
    default:
      return 'text-blue-500'
  }
}

// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString()
}

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push(`/search?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

// 标记通知为已读
const markAsRead = (notificationId: string) => {
  appStore.markNotificationAsRead(notificationId)
}

// 标记所有通知为已读
const markAllAsRead = () => {
  appStore.markAllNotificationsAsRead()
}

// 旧的handleLogout函数已移除，使用下面的新版本

// 切换主题（使用appStore）
const toggleTheme = () => {
  appStore.toggleTheme()
}

// 初始化用户状态
const initializeUser = async () => {
  // 尝试从本地存储恢复用户状态
  const restored = userStore.restoreAuthState()

  if (!restored) {
    // 如果没有保存的状态，默认登录admin用户进行演示
    await userStore.login({
      username: 'admin',
      password: 'admin' // 在实际项目中不会这样处理
    })
  }
}

// 处理用户登出
const handleLogout = async () => {
  await userStore.logout()
  // 可以重定向到登录页面
  // router.push('/login')
}

// 加载通知数据（使用appStore）
const loadNotifications = () => {
  // 添加模拟通知数据到appStore
  appStore.addNotification({
    type: 'warning',
    title: '库存预警',
    message: '6mm透明浮法玻璃库存不足，当前库存：15片',
    read: false
  })

  appStore.addNotification({
    type: 'info',
    title: '新订单',
    message: '客户"建筑公司A"提交了新的玻璃加工订单',
    read: false
  })

  appStore.addNotification({
    type: 'success',
    title: '生产完成',
    message: '订单#2024001的钢化玻璃生产已完成',
    read: true
  })

  appStore.addNotification({
    type: 'error',
    title: '设备故障',
    message: '钢化炉#2出现温度异常，请及时检修',
    read: false
  })
}

onMounted(() => {
  // 初始化应用状态（包括主题）
  appStore.initializeApp()

  // 初始化用户状态
  initializeUser()

  // 加载通知数据
  loadNotifications()
})
</script>