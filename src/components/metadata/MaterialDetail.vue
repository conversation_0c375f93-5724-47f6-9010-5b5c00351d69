<template>
  <Sheet v-model:open="isOpen">
    <SheetContent class="overflow-y-auto !w-[50vw] !max-w-none" side="right">
      <SheetHeader>
        <SheetTitle class="flex items-center justify-between">
          <div class="flex items-center">
            <Info class="h-5 w-5 mr-2" />
            <div>
              <div class="text-xl font-bold">{{ material.displayName }}</div>
              <div class="text-sm font-normal text-gray-500">{{ material.materialId }}</div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <!-- 关键指标快速显示 -->
            <div class="flex items-center space-x-4 text-sm">
              <div class="text-center">
                <div class="font-bold text-blue-600">{{ material.variants.length }}</div>
                <div class="text-gray-500">变体</div>
              </div>
              <div class="text-center">
                <div class="font-bold text-green-600">{{ getTotalStock() }}</div>
                <div class="text-gray-500">库存</div>
              </div>
              <div class="text-center">
                <div class="font-bold text-orange-600">¥{{ getTotalValue().toFixed(0) }}</div>
                <div class="text-gray-500">价值</div>
              </div>
            </div>
            <Badge :variant="hasActiveVariants ? 'default' : 'secondary'" class="text-sm">
              {{ hasActiveVariants ? '正常' : '停用' }}
            </Badge>
          </div>
        </SheetTitle>
        <SheetDescription>
          {{ getCategoryName(material.categoryId) }} 分类 • 基础属性：{{ Object.keys(material.attributes).join('、') }}
        </SheetDescription>
      </SheetHeader>
      
      <div class="flex-1 overflow-y-auto p-6">
        <div class="space-y-6">
          <!-- 关键统计卡片组 - 顶部突出显示 -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card class="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-1">{{ material.variants.length }}</div>
                <div class="text-sm text-blue-700 font-medium">库存变体</div>
                <div class="text-xs text-blue-600 mt-1">规格种类</div>
              </div>
            </Card>
            
            <Card class="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
              <div class="text-center">
                <div class="text-3xl font-bold text-green-600 mb-1">{{ getTotalStock() }}</div>
                <div class="text-sm text-green-700 font-medium">总库存</div>
                <div class="text-xs text-green-600 mt-1">件/平米</div>
              </div>
            </Card>
            
            <Card class="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
              <div class="text-center">
                <div class="text-3xl font-bold text-purple-600 mb-1">{{ getActiveVariants() }}</div>
                <div class="text-sm text-purple-700 font-medium">正常变体</div>
                <div class="text-xs text-purple-600 mt-1">可用规格</div>
              </div>
            </Card>
            
            <Card class="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
              <div class="text-center">
                <div class="text-3xl font-bold text-orange-600 mb-1">¥{{ getTotalValue().toFixed(0) }}</div>
                <div class="text-sm text-orange-700 font-medium">总价值</div>
                <div class="text-xs text-orange-600 mt-1">库存价值</div>
              </div>
            </Card>
          </div>

          <!-- 物料基本信息卡片 -->
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center justify-between">
                <div class="flex items-center">
                  <Info class="h-5 w-5 mr-2" />
                  物料信息
                </div>
                <div class="flex items-center space-x-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger as-child>
                      <Button variant="outline" size="sm">
                        <Settings class="mr-2 h-4 w-4" />
                        快速操作
                        <ChevronDown class="ml-2 h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>物料操作</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="editMaterial">
                        <Edit class="mr-2 h-4 w-4" />
                        编辑物料
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="duplicateMaterial">
                        <Copy class="mr-2 h-4 w-4" />
                        复制物料
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="exportMaterial">
                        <Download class="mr-2 h-4 w-4" />
                        导出数据
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="refreshMaterial">
                        <RefreshCw class="mr-2 h-4 w-4" />
                        刷新数据
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        @click="toggleMaterialStatus" 
                        :class="hasActiveVariants ? 'text-orange-600' : 'text-green-600'"
                      >
                        <Power class="mr-2 h-4 w-4" />
                        {{ hasActiveVariants ? '停用物料' : '启用物料' }}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 基础信息 -->
                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">基础信息</h4>
                    <dl class="space-y-3">
                      <div class="flex justify-between items-center">
                        <dt class="text-sm text-gray-500">物料ID</dt>
                        <dd class="text-sm font-mono font-medium bg-gray-100 px-2 py-1 rounded">{{ material.materialId }}</dd>
                      </div>
                      <div class="flex justify-between items-center">
                        <dt class="text-sm text-gray-500">物料名称</dt>
                        <dd class="text-sm font-medium">{{ material.displayName }}</dd>
                      </div>
                      <div class="flex justify-between items-center">
                        <dt class="text-sm text-gray-500">所属分类</dt>
                        <dd>
                          <Badge variant="secondary" class="bg-blue-100 text-blue-800">{{ getCategoryName(material.categoryId) }}</Badge>
                        </dd>
                      </div>
                    </dl>
                  </div>
                </div>

                <!-- 关键属性标签化显示 -->
                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">产品规格</h4>
                    <div class="flex flex-wrap gap-2">
                      <div
                        v-for="(value, key) in material.attributes"
                        :key="key"
                        class="inline-flex items-center px-3 py-1.5 rounded-full text-sm bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300"
                      >
                        <span class="font-medium text-gray-700">{{ key }}</span>
                        <span class="mx-2 text-gray-400">·</span>
                        <span class="font-bold text-gray-900">{{ value }}</span>
                      </div>
                      <div v-if="Object.keys(material.attributes).length === 0" class="text-sm text-gray-500">
                        暂无规格信息
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 库存变体表格 -->
          <MaterialVariantTable :variants="material.variants" />
        </div>

    <!-- 物料编辑对话框 -->
    <MaterialOperationDialog 
      :open="showEditDialog" 
      :material="material" 
      :available-categories="availableCategories"
      @update:open="showEditDialog = $event" 
      @submit="handleMaterialEditSubmit" 
    />

    <!-- 复制物料对话框 -->
    <Dialog :open="showDuplicateDialog" @update:open="showDuplicateDialog = $event">
      <DialogContent class="!w-[90vw] !max-w-[1200px] !max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle class="flex items-center">
            <Copy class="mr-2 h-5 w-5" />
            复制物料
          </DialogTitle>
          <DialogDescription>
            创建当前物料的副本，可修改基本信息
          </DialogDescription>
        </DialogHeader>
        
        <!-- 内容滚动区域 -->
        <div class="flex-1 overflow-y-auto">
          <div class="space-y-4 py-4">
            <div>
              <label class="text-sm font-medium">新物料名称 *</label>
              <Input 
                v-model="duplicateForm.displayName"
                placeholder="请输入新物料名称"
                class="mt-2"
              />
            </div>
            <div>
              <label class="text-sm font-medium">新物料ID *</label>
              <Input 
                v-model="duplicateForm.materialId"
                placeholder="请输入新物料ID"
                class="mt-2"
              />
            </div>
            <div class="space-y-2">
              <label class="text-sm font-medium">复制选项</label>
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <input 
                    id="copyAttributes" 
                    type="checkbox"
                    v-model="duplicateForm.copyAttributes"
                  />
                  <label for="copyAttributes" class="text-sm">
                    复制基础属性
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    id="copyVariants" 
                    type="checkbox"
                    v-model="duplicateForm.copyVariants"
                  />
                  <label for="copyVariants" class="text-sm">
                    复制库存变体（不含库存数据）
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter class="flex-shrink-0 border-t pt-4">
          <Button variant="outline" @click="cancelDuplicate">取消</Button>
          <Button @click="confirmDuplicate" :disabled="!duplicateForm.displayName || !duplicateForm.materialId">
            创建副本
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
      </div>
    </SheetContent>
  </Sheet>
</template>

<script setup lang="ts">
import { computed, ref, withDefaults } from 'vue';
import type { Material } from '@/types/material';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from '@/components/ui/sheet';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  Info, 
  Settings, 
  Edit, 
  Copy, 
  Download, 
  RefreshCw, 
  Power, 
  ChevronDown
} from 'lucide-vue-next';
import MaterialVariantTable from './MaterialVariantTable.vue';
import MaterialOperationDialog from './MaterialOperationDialog.vue';
import { useMetadataStore } from '@/stores/metadata';
import { storeToRefs } from 'pinia';

interface Props {
  material: Material;
  open?: boolean;
}

interface Emits {
  (e: 'material-updated', material: Material): void;
  (e: 'material-duplicated', material: Partial<Material>): void;
  (e: 'material-status-changed', materialId: string): void;
  (e: 'update:open', value: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  open: false
});
const emit = defineEmits<Emits>();

// Sheet 状态
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
});

const metadataStore = useMetadataStore();
const { materialCategories } = storeToRefs(metadataStore);

// 获取可选分类
const availableCategories = computed(() => metadataStore.selectableCategories);

// 对话框状态
const showEditDialog = ref(false);
const showDuplicateDialog = ref(false);

// 复制表单
const duplicateForm = ref({
  displayName: '',
  materialId: '',
  copyAttributes: true,
  copyVariants: false
});

const getCategoryName = (categoryId: string) => {
  const category = materialCategories.value.find(cat => cat.categoryId === categoryId);
  return category?.categoryName || categoryId;
};

const getTotalStock = () => {
  return props.material.variants.reduce((total, variant) => total + variant.stock, 0);
};

const getActiveVariants = () => {
  return props.material.variants.filter(variant => variant.isActive).length;
};

const getTotalValue = () => {
  return props.material.variants.reduce((total, variant) => total + (variant.stock * variant.cost), 0);
};

// 检查是否有激活的变体
const hasActiveVariants = computed(() => {
  return props.material.variants.some(variant => variant.isActive);
});

// 初始化复制表单
const initDuplicateForm = () => {
  duplicateForm.value = {
    displayName: `${props.material.displayName} (副本)`,
    materialId: `${props.material.materialId}_copy`,
    copyAttributes: true,
    copyVariants: false
  };
};

// 操作方法
const editMaterial = () => {
  showEditDialog.value = true;
};

// 处理物料编辑提交
const handleMaterialEditSubmit = (materialData: unknown) => {
  // 将MaterialFormData转换为Material格式并触发事件
  emit('material-updated', materialData as Material);
  showEditDialog.value = false;
  console.log('物料编辑完成:', materialData);
};

const duplicateMaterial = () => {
  initDuplicateForm();
  showDuplicateDialog.value = true;
};

const exportMaterial = () => {
  console.log('导出物料数据:', props.material.materialId);
  // TODO: 实现导出功能
};

const refreshMaterial = () => {
  console.log('刷新物料数据:', props.material.materialId);
  // TODO: 实现刷新功能
};

const toggleMaterialStatus = () => {
  emit('material-status-changed', props.material.materialId);
  console.log('切换物料状态:', props.material.materialId);
};

const cancelDuplicate = () => {
  showDuplicateDialog.value = false;
  initDuplicateForm();
};

const confirmDuplicate = () => {
  const duplicatedMaterial = {
    materialId: duplicateForm.value.materialId,
    displayName: duplicateForm.value.displayName,
    categoryId: props.material.categoryId,
    attributes: duplicateForm.value.copyAttributes ? { ...props.material.attributes } : {},
    variants: duplicateForm.value.copyVariants 
      ? props.material.variants.map(v => ({ ...v, stock: 0, variantId: `${v.variantId}_copy` })) 
      : []
  };
  
  emit('material-duplicated', duplicatedMaterial);
  cancelDuplicate();
  console.log('创建物料副本:', duplicatedMaterial);
};
</script>