<template>
  <div class="flex items-center space-x-2">
    <!-- 添加分类按钮 -->
    <Button variant="default" size="sm" @click="handleAdd" class="h-8">
      <Plus class="h-3 w-3 mr-1" />
      添加分类
    </Button>

    <!-- 当有选中分类时显示的操作按钮 -->
    <template v-if="selectedCategory">
      <Button variant="outline" size="sm" @click="handleEdit" class="h-8">
        <Edit class="h-3 w-3 mr-1" />
        编辑
      </Button>

      <Button 
        variant="outline" 
        size="sm" 
        @click="handleAddChild" 
        class="h-8"
        :disabled="selectedCategory.level >= 2"
      >
        <Plus class="h-3 w-3 mr-1" />
        添加子分类
      </Button>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" class="h-8">
            <MoreHorizontal class="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem @click="handleDuplicate">
            <Copy class="h-3 w-3 mr-2" />
            复制分类
          </DropdownMenuItem>
          
          <DropdownMenuItem @click="handleExport">
            <Download class="h-3 w-3 mr-2" />
            导出配置
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            @click="handleDelete" 
            class="text-red-600 focus:text-red-600"
            :disabled="selectedCategory.hasChildren"
          >
            <Trash2 class="h-3 w-3 mr-2" />
            删除分类
            <span v-if="selectedCategory.hasChildren" class="ml-auto text-xs text-gray-400">
              (有子分类)
            </span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </template>

    <!-- 导入按钮 -->
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" class="h-8">
          <Upload class="h-3 w-3 mr-1" />
          导入
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem @click="handleImportTemplate">
          <FileText class="h-3 w-3 mr-2" />
          导入模板
        </DropdownMenuItem>
        
        <DropdownMenuItem @click="handleImportConfig">
          <Settings class="h-3 w-3 mr-2" />
          导入配置
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <!-- 批量操作 -->
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" class="h-8">
          <Package class="h-3 w-3 mr-1" />
          批量操作
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem @click="handleBatchExport">
          <Download class="h-3 w-3 mr-2" />
          批量导出
        </DropdownMenuItem>
        
        <DropdownMenuItem @click="handleBatchDelete">
          <Trash2 class="h-3 w-3 mr-2" />
          批量删除
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem @click="handleResetAll" class="text-red-600 focus:text-red-600">
          <RotateCcw class="h-3 w-3 mr-2" />
          重置所有分类
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>

  <!-- 确认删除对话框 -->
  <AlertDialog :open="deleteConfirmOpen" @update:open="deleteConfirmOpen = $event">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>确认删除分类</AlertDialogTitle>
        <AlertDialogDescription>
          您即将删除分类"{{ selectedCategory?.categoryName }}"，此操作不可逆转。
          <br><br>
          <strong>注意：</strong>删除分类将同时删除该分类下的所有物料数据。
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel>取消</AlertDialogCancel>
        <AlertDialogAction 
          @click="confirmDelete"
          class="bg-red-600 hover:bg-red-700"
        >
          确认删除
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>

  <!-- 批量重置确认对话框 -->
  <AlertDialog :open="resetConfirmOpen" @update:open="resetConfirmOpen = $event">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>确认重置所有分类</AlertDialogTitle>
        <AlertDialogDescription>
          您即将重置所有物料分类配置，恢复到系统初始状态。
          <br><br>
          <strong class="text-red-600">警告：</strong>此操作将清除所有自定义分类和配置，不可逆转！
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel>取消</AlertDialogCancel>
        <AlertDialogAction 
          @click="confirmReset"
          class="bg-red-600 hover:bg-red-700"
        >
          确认重置
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { MaterialCategory } from '@/types/material';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Plus,
  Edit,
  MoreHorizontal,
  Copy,
  Download,
  Trash2,
  Upload,
  FileText,
  Settings,
  Package,
  RotateCcw,
} from 'lucide-vue-next';

interface Props {
  selectedCategory?: MaterialCategory | null;
}

interface Emits {
  (e: 'add-category'): void;
  (e: 'edit-category', category: MaterialCategory): void;
  (e: 'add-child-category', parentCategory: MaterialCategory): void;
  (e: 'duplicate-category', category: MaterialCategory): void;
  (e: 'delete-category', categoryId: string): void;
  (e: 'export-category', category: MaterialCategory): void;
  (e: 'import-template'): void;
  (e: 'import-config'): void;
  (e: 'batch-export'): void;
  (e: 'batch-delete'): void;
  (e: 'reset-all'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 确认对话框状态
const deleteConfirmOpen = ref(false);
const resetConfirmOpen = ref(false);

// 操作处理函数
const handleAdd = () => {
  emit('add-category');
};

const handleEdit = () => {
  if (props.selectedCategory) {
    emit('edit-category', props.selectedCategory);
  }
};

const handleAddChild = () => {
  if (props.selectedCategory) {
    emit('add-child-category', props.selectedCategory);
  }
};

const handleDuplicate = () => {
  if (props.selectedCategory) {
    emit('duplicate-category', props.selectedCategory);
  }
};

const handleDelete = () => {
  if (props.selectedCategory && !props.selectedCategory.hasChildren) {
    deleteConfirmOpen.value = true;
  }
};

const confirmDelete = () => {
  if (props.selectedCategory) {
    emit('delete-category', props.selectedCategory.categoryId);
  }
  deleteConfirmOpen.value = false;
};

const handleExport = () => {
  if (props.selectedCategory) {
    emit('export-category', props.selectedCategory);
  }
};

const handleImportTemplate = () => {
  emit('import-template');
};

const handleImportConfig = () => {
  emit('import-config');
};

const handleBatchExport = () => {
  emit('batch-export');
};

const handleBatchDelete = () => {
  emit('batch-delete');
};

const handleResetAll = () => {
  resetConfirmOpen.value = true;
};

const confirmReset = () => {
  emit('reset-all');
  resetConfirmOpen.value = false;
};
</script>