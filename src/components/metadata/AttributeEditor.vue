<template>
  <div class="space-y-3">
    <!-- 属性基本信息 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
      <div class="space-y-1">
        <Label class="text-xs">属性名称</Label>
        <Input 
          v-model="modelValue.name" 
          placeholder="如：厚度"
          class="text-sm"
        />
      </div>
      
      <div class="space-y-1">
        <Label class="text-xs">数据类型</Label>
        <Select v-model="modelValue.type" @update:modelValue="handleTypeChange">
          <SelectTrigger class="text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="select">选择项</SelectItem>
            <SelectItem value="number">数值</SelectItem>
            <SelectItem value="text">文本</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div class="space-y-1">
        <Label class="text-xs">单位</Label>
        <Input 
          v-model="modelValue.unit" 
          placeholder="如：mm"
          class="text-sm"
          :disabled="modelValue.type === 'select'"
        />
      </div>
    </div>

    <!-- 属性描述 -->
    <div class="space-y-1">
      <Label class="text-xs">描述</Label>
      <Input 
        v-model="modelValue.description" 
        placeholder="属性的详细说明"
        class="text-sm"
      />
    </div>

    <!-- 类型特定配置 -->
    <div v-if="modelValue.type === 'select'" class="space-y-2">
      <div class="flex items-center justify-between">
        <Label class="text-xs font-medium">可选值</Label>
        <Button type="button" variant="ghost" size="sm" @click="addOption" class="h-6 px-2 text-xs">
          <Plus class="h-3 w-3 mr-1" />
          添加选项
        </Button>
      </div>
      
      <div class="space-y-1">
        <div 
          v-for="(option, index) in (modelValue.options || [])" 
          :key="index"
          class="flex items-center space-x-2"
        >
          <Input 
            v-model="modelValue.options![index]" 
            placeholder="选项值"
            class="flex-1 text-sm"
          />
          <Button 
            type="button" 
            variant="ghost" 
            size="sm" 
            @click="removeOption(index)"
            class="h-8 w-8 p-0 text-red-500 hover:text-red-700"
          >
            ×
          </Button>
        </div>
      </div>
      
      <!-- 预设选项快捷按钮 -->
      <div v-if="presetOptions.length > 0" class="space-y-2">
        <Label class="text-xs text-gray-600">常用预设</Label>
        <div class="flex flex-wrap gap-1">
          <Button
            v-for="preset in presetOptions"
            :key="preset.name"
            type="button"
            variant="outline"
            size="sm"
            @click="applyPreset(preset.options)"
            class="h-6 px-2 text-xs"
          >
            {{ preset.name }}
          </Button>
        </div>
      </div>
    </div>

    <div v-if="modelValue.type === 'number'" class="grid grid-cols-2 gap-3">
      <div class="space-y-1">
        <Label class="text-xs">最小值</Label>
        <Input 
          v-model.number="modelValue.minValue" 
          type="number"
          placeholder="0"
          class="text-sm"
        />
      </div>
      
      <div class="space-y-1">
        <Label class="text-xs">最大值</Label>
        <Input 
          v-model.number="modelValue.maxValue" 
          type="number"
          placeholder="1000"
          class="text-sm"
        />
      </div>
    </div>

    <!-- 删除按钮 -->
    <div class="flex justify-end pt-2 border-t">
      <Button 
        type="button" 
        variant="ghost" 
        size="sm" 
        @click="$emit('remove')"
        class="text-red-500 hover:text-red-700 text-xs"
      >
        删除属性
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { AttributeDefinition } from '@/types/material';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus } from 'lucide-vue-next';

interface Props {
  modelValue: AttributeDefinition;
}

interface Emits {
  (e: 'update:modelValue', value: AttributeDefinition): void;
  (e: 'remove'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 创建响应式的本地副本
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 预设选项配置
const presetOptions = computed(() => {
  const attributeName = modelValue.value.name?.toLowerCase() || '';
  
  if (attributeName.includes('厚度')) {
    return [
      { name: '玻璃厚度', options: ['4mm', '5mm', '6mm', '8mm', '10mm', '12mm', '15mm', '19mm'] },
      { name: '型材壁厚', options: ['1.2mm', '1.4mm', '1.6mm', '2.0mm', '2.5mm'] }
    ];
  }
  
  if (attributeName.includes('颜色')) {
    return [
      { name: '玻璃颜色', options: ['透明', '超白', '茶色', '蓝色', '绿色', '灰色', '黑色'] },
      { name: '型材颜色', options: ['白色', '黑色', '香槟色', '银色', '古铜色'] }
    ];
  }
  
  if (attributeName.includes('等级') || attributeName.includes('级别')) {
    return [
      { name: '玻璃等级', options: ['汽车级', '建筑级', '制镜级', '家具级'] },
      { name: '质量等级', options: ['优等品', '一等品', '合格品'] }
    ];
  }

  if (attributeName.includes('表面') || attributeName.includes('处理')) {
    return [
      { name: '型材表面', options: ['阳极氧化', '粉末喷涂', '木纹转印', '电泳涂装'] },
      { name: '五金表面', options: ['抛光', '拉丝', '阳极氧化', '电镀'] }
    ];
  }

  if (attributeName.includes('规格') || attributeName.includes('截面')) {
    return [
      { name: '型材截面', options: ['50x30mm', '60x40mm', '80x50mm', '100x60mm'] }
    ];
  }
  
  return [];
});

// 类型变化处理
const handleTypeChange = (newType: string) => {
  const updated = { ...modelValue.value, type: newType as any };
  
  if (newType === 'select' && !updated.options) {
    updated.options = [];
  } else if (newType !== 'select') {
    delete updated.options;
  }
  
  if (newType === 'number') {
    if (!updated.minValue) updated.minValue = 0;
    if (!updated.maxValue) updated.maxValue = 1000;
  } else {
    delete updated.minValue;
    delete updated.maxValue;
  }
  
  emit('update:modelValue', updated);
};

// 选项管理
const addOption = () => {
  const options = [...(modelValue.value.options || []), ''];
  emit('update:modelValue', { ...modelValue.value, options });
};

const removeOption = (index: number) => {
  const options = [...(modelValue.value.options || [])];
  options.splice(index, 1);
  emit('update:modelValue', { ...modelValue.value, options });
};

const applyPreset = (presetOptions: string[]) => {
  emit('update:modelValue', { 
    ...modelValue.value, 
    options: [...presetOptions] 
  });
};
</script>