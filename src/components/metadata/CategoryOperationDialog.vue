<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="!w-[90vw] !max-w-[1200px] !max-h-[90vh] overflow-hidden flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle>
          {{ isEditMode ? '编辑分类' : '添加分类' }}
        </DialogTitle>
        <DialogDescription>
          {{ isEditMode ? '修改物料分类信息' : '创建新的物料分类' }}
        </DialogDescription>
      </DialogHeader>

      <!-- 内容滚动区域 -->
            <!-- 内容滚动区域 -->
      <div class="flex-1 overflow-y-auto">
        <form @submit="handleSubmit" class="space-y-6">
          <!-- 基本信息 -->
          <div class="space-y-4">
            <h3 class="font-semibold text-sm text-gray-700">基本信息</h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label htmlFor="categoryId">分类编码 *</Label>
                <Input 
                  id="categoryId"
                  v-model="formData.categoryId" 
                  :disabled="isEditMode"
                  placeholder="如：CAT_TEMPERED_GLASS"
                  class="uppercase"
                />
                <p class="text-xs text-gray-500">编码一旦创建不可修改</p>
              </div>

              <div class="space-y-2">
                <Label htmlFor="categoryName">分类名称 *</Label>
                <Input 
                  id="categoryName"
                  v-model="formData.categoryName" 
                  placeholder="如：钢化玻璃"
                />
              </div>
            </div>

            <div class="space-y-2">
              <Label htmlFor="description">分类描述</Label>
              <Textarea 
                id="description"
                v-model="formData.description" 
                placeholder="详细描述该分类的用途和特点"
                rows="2"
              />
            </div>

            <div class="space-y-2">
              <Label htmlFor="parentId">父级分类</Label>
              <Select v-model="formData.parentId">
                <SelectTrigger>
                  <SelectValue placeholder="选择父级分类（可选）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">无父级分类（顶级）</SelectItem>
                  <SelectItem 
                    v-for="category in availableParentCategories" 
                    :key="category.categoryId" 
                    :value="category.categoryId"
                  >
                    {{ category.categoryName }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <!-- 属性模板设计 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <h3 class="font-semibold text-sm text-gray-700">属性模板设计</h3>
              <div class="text-xs text-gray-500">
                用于定义该分类下物料的属性结构
              </div>
            </div>

            <!-- 基础属性 -->
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <Label class="text-sm font-medium">基础属性</Label>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  @click="addBaseAttribute"
                >
                  <Plus class="h-3 w-3 mr-1" />
                  添加属性
                </Button>
              </div>

              <div v-if="formData.attributeSchema.baseAttributes.length === 0" 
                   class="p-4 border-2 border-dashed border-gray-200 rounded-lg text-center text-sm text-gray-500">
                点击"添加属性"定义基础属性
              </div>

              <div v-else class="grid grid-cols-1 xl:grid-cols-2 gap-3">
                <div
                  v-for="(attr, index) in formData.attributeSchema.baseAttributes"
                  :key="`base-${index}`"
                  class="p-3 border rounded-lg bg-gray-50"
                >
                  <AttributeEditor
                    v-model="formData.attributeSchema.baseAttributes[index]"
                    @remove="removeBaseAttribute(index)"
                  />
                </div>
              </div>
            </div>

            <!-- 变体属性 -->
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <Label class="text-sm font-medium">变体属性</Label>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  @click="addVariantAttribute"
                >
                  <Plus class="h-3 w-3 mr-1" />
                  添加属性
                </Button>
              </div>

              <div v-if="formData.attributeSchema.variantAttributes.length === 0" 
                   class="p-4 border-2 border-dashed border-gray-200 rounded-lg text-center text-sm text-gray-500">
                点击"添加属性"定义变体属性
              </div>

              <div v-else class="grid grid-cols-1 xl:grid-cols-2 gap-3">
                <div
                  v-for="(attr, index) in formData.attributeSchema.variantAttributes"
                  :key="`variant-${index}`"
                  class="p-3 border rounded-lg bg-blue-50"
                >
                  <AttributeEditor
                    v-model="formData.attributeSchema.variantAttributes[index]"
                    @remove="removeVariantAttribute(index)"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 预览区域 -->
          <div v-if="hasAttributes" class="space-y-3 border-t pt-4">
            <h3 class="font-semibold text-sm text-gray-700">属性预览</h3>
            <div class="grid grid-cols-1 gap-2">
              <div class="flex items-center space-x-2">
                <Badge variant="secondary">基础属性</Badge>
                <div class="flex flex-wrap gap-1">
                  <span 
                    v-for="attr in formData.attributeSchema.baseAttributes" 
                    :key="attr.name"
                    class="px-2 py-1 text-xs bg-gray-100 rounded"
                  >
                    {{ attr.name }}({{ attr.type }})
                  </span>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <Badge variant="outline">变体属性</Badge>
                <div class="flex flex-wrap gap-1">
                  <span 
                    v-for="attr in formData.attributeSchema.variantAttributes" 
                    :key="attr.name"
                    class="px-2 py-1 text-xs bg-blue-100 rounded"
                  >
                    {{ attr.name }}({{ attr.type }})
                  </span>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- 固定底部按钮 -->
      <div class="flex-shrink-0 flex justify-end space-x-2 pt-4 border-t">
        <Button 
          type="button" 
          variant="outline" 
          @click="$emit('update:open', false)"
        >
          取消
        </Button>
        <Button 
          type="submit" 
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          {{ isEditMode ? '保存修改' : '创建分类' }}
        </Button>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { MaterialCategory, AttributeSchema } from '@/types/material';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus } from 'lucide-vue-next';
import AttributeEditor from './AttributeEditor.vue';

interface Props {
  open: boolean;
  category?: MaterialCategory | null;
  availableParentCategories: MaterialCategory[];
}

interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'submit', category: MaterialCategory): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const isEditMode = computed(() => !!props.category);

// 表单数据
const formData = ref<Partial<MaterialCategory> & { attributeSchema: AttributeSchema }>({
  categoryId: '',
  categoryName: '',
  description: '',
  parentId: null,
  level: 0,
  hasChildren: false,
  attributeSchema: {
    baseAttributes: [],
    variantAttributes: []
  }
});

// 监听category变化，初始化表单
watch(() => props.category, (newCategory) => {
  if (newCategory) {
    formData.value = {
      ...newCategory,
      attributeSchema: newCategory.attributeSchema || {
        baseAttributes: [],
        variantAttributes: []
      }
    };
  } else {
    // 重置表单
    formData.value = {
      categoryId: '',
      categoryName: '',
      description: '',
      parentId: null,
      level: 0,
      hasChildren: false,
      attributeSchema: {
        baseAttributes: [],
        variantAttributes: []
      }
    };
  }
}, { immediate: true });

// 计算属性
const hasAttributes = computed(() => {
  return formData.value.attributeSchema.baseAttributes.length > 0 || 
         formData.value.attributeSchema.variantAttributes.length > 0;
});

const isFormValid = computed(() => {
  return formData.value.categoryId?.trim() && formData.value.categoryName?.trim();
});

// 属性管理方法
const addBaseAttribute = () => {
  formData.value.attributeSchema.baseAttributes.push({
    name: '',
    type: 'text',
    description: ''
  });
};

const removeBaseAttribute = (index: number) => {
  formData.value.attributeSchema.baseAttributes.splice(index, 1);
};

const addVariantAttribute = () => {
  formData.value.attributeSchema.variantAttributes.push({
    name: '',
    type: 'text',
    description: ''
  });
};

const removeVariantAttribute = (index: number) => {
  formData.value.attributeSchema.variantAttributes.splice(index, 1);
};

// 表单提交
const handleSubmit = (event: Event) => {
  event.preventDefault();
  
  if (!isFormValid.value) return;

  // 计算level
  const level = formData.value.parentId && formData.value.parentId !== 'none' ? 
    (props.availableParentCategories.find(c => c.categoryId === formData.value.parentId)?.level ?? 0) + 1 : 0;

  const category: MaterialCategory = {
    categoryId: formData.value.categoryId!,
    categoryName: formData.value.categoryName!,
    description: formData.value.description || '',
    parentId: formData.value.parentId === 'none' ? null : (formData.value.parentId || null),
    level,
    hasChildren: false,
    attributeSchema: hasAttributes.value ? formData.value.attributeSchema : null
  };

  emit('submit', category);
};
</script>