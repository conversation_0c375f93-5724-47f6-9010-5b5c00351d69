<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="!w-[700px] !max-w-[90vw] max-h-[90vh] overflow-hidden flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle class="flex items-center">
          <Download class="mr-2 h-5 w-5" />
          导出物料数据
        </DialogTitle>
        <DialogDescription>
          将物料和库存数据导出为Excel文件
        </DialogDescription>
      </DialogHeader>

      <!-- 主要内容区域 -->
      <div class="flex-1 overflow-y-auto space-y-6">
        <!-- 导出范围 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-base">导出范围</CardTitle>
            <CardDescription>
              选择要导出的物料范围和数据内容
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <label class="text-sm font-medium mb-2 block">导出范围</label>
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="scope-all" 
                    value="all" 
                    v-model="exportConfig.scope"
                    class="rounded border-gray-300"
                  />
                  <label for="scope-all" class="text-sm">导出所有物料</label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="scope-category" 
                    value="category" 
                    v-model="exportConfig.scope"
                    class="rounded border-gray-300"
                  />
                  <label for="scope-category" class="text-sm">按分类导出</label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="scope-selected" 
                    value="selected" 
                    v-model="exportConfig.scope"
                    class="rounded border-gray-300"
                  />
                  <label for="scope-selected" class="text-sm">导出选中物料</label>
                </div>
              </div>
            </div>

            <!-- 分类选择 -->
            <div v-if="exportConfig.scope === 'category'">
              <label class="text-sm font-medium mb-2 block">选择分类</label>
              <Select v-model="exportConfig.categoryId">
                <SelectTrigger>
                  <SelectValue placeholder="选择要导出的分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="category in categories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 选中物料列表 -->
            <div v-if="exportConfig.scope === 'selected'">
              <label class="text-sm font-medium mb-2 block">选中物料 ({{ selectedMaterials.length }})</label>
              <div class="border rounded-lg p-3 max-h-32 overflow-y-auto bg-gray-50">
                <div v-if="selectedMaterials.length === 0" class="text-sm text-gray-500 text-center">
                  暂无选中物料
                </div>
                <div v-else class="space-y-1">
                  <div 
                    v-for="material in selectedMaterials" 
                    :key="material.materialId"
                    class="text-sm flex items-center justify-between p-1"
                  >
                    <span>{{ material.displayName }}</span>
                    <Badge variant="outline" class="text-xs">{{ material.materialId }}</Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 导出内容 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-base">导出内容</CardTitle>
            <CardDescription>
              选择要包含在导出文件中的数据类型
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <input 
                    id="include-basic" 
                    type="checkbox"
                    v-model="exportConfig.includeBasic"
                    class="rounded border-gray-300"
                  />
                  <label for="include-basic" class="text-sm font-medium">
                    基础信息
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    id="include-attributes" 
                    type="checkbox"
                    v-model="exportConfig.includeAttributes"
                    class="rounded border-gray-300"
                  />
                  <label for="include-attributes" class="text-sm font-medium">
                    物料属性
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    id="include-variants" 
                    type="checkbox"
                    v-model="exportConfig.includeVariants"
                    class="rounded border-gray-300"
                  />
                  <label for="include-variants" class="text-sm font-medium">
                    库存变体
                  </label>
                </div>
              </div>
              
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <input 
                    id="include-stock" 
                    type="checkbox"
                    v-model="exportConfig.includeStock"
                    class="rounded border-gray-300"
                  />
                  <label for="include-stock" class="text-sm font-medium">
                    库存数据
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    id="include-pricing" 
                    type="checkbox"
                    v-model="exportConfig.includePricing"
                    class="rounded border-gray-300"
                  />
                  <label for="include-pricing" class="text-sm font-medium">
                    价格成本
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    id="include-suppliers" 
                    type="checkbox"
                    v-model="exportConfig.includeSuppliers"
                    class="rounded border-gray-300"
                  />
                  <label for="include-suppliers" class="text-sm font-medium">
                    供应商信息
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 导出格式 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-base">导出格式</CardTitle>
            <CardDescription>
              选择导出文件的格式和结构
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <label class="text-sm font-medium mb-2 block">文件格式</label>
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="format-xlsx" 
                    value="xlsx" 
                    v-model="exportConfig.format"
                    class="rounded border-gray-300"
                  />
                  <label for="format-xlsx" class="text-sm flex items-center">
                    <FileSpreadsheet class="mr-2 h-4 w-4" />
                    Excel文件 (.xlsx)
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="format-csv" 
                    value="csv" 
                    v-model="exportConfig.format"
                    class="rounded border-gray-300"
                  />
                  <label for="format-csv" class="text-sm flex items-center">
                    <FileText class="mr-2 h-4 w-4" />
                    CSV文件 (.csv)
                  </label>
                </div>
              </div>
            </div>

            <div v-if="exportConfig.format === 'xlsx'">
              <label class="text-sm font-medium mb-2 block">表结构</label>
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="structure-single" 
                    value="single" 
                    v-model="exportConfig.structure"
                    class="rounded border-gray-300"
                  />
                  <label for="structure-single" class="text-sm">
                    单表结构 - 所有数据在一个表中
                  </label>
                </div>
                <div class="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="structure-multiple" 
                    value="multiple" 
                    v-model="exportConfig.structure"
                    class="rounded border-gray-300"
                  />
                  <label for="structure-multiple" class="text-sm">
                    多表结构 - 分别导出物料、变体、库存等
                  </label>
                </div>
              </div>
            </div>

            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <input 
                  id="include-headers" 
                  type="checkbox"
                  v-model="exportConfig.includeHeaders"
                  class="rounded border-gray-300"
                />
                <label for="include-headers" class="text-sm font-medium">
                  包含列标题
                </label>
              </div>
              <div class="flex items-center space-x-2">
                <input 
                  id="include-timestamp" 
                  type="checkbox"
                  v-model="exportConfig.includeTimestamp"
                  class="rounded border-gray-300"
                />
                <label for="include-timestamp" class="text-sm font-medium">
                  文件名包含时间戳
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 预览信息 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-base">导出预览</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-600">预计记录数:</span>
                <span class="font-medium ml-2">{{ estimatedRecords }} 条</span>
              </div>
              <div>
                <span class="text-gray-600">预计文件大小:</span>
                <span class="font-medium ml-2">{{ estimatedSize }}</span>
              </div>
              <div>
                <span class="text-gray-600">文件名:</span>
                <span class="font-mono text-xs ml-2">{{ previewFileName }}</span>
              </div>
              <div>
                <span class="text-gray-600">生成时间:</span>
                <span class="font-medium ml-2">{{ new Date().toLocaleString() }}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <DialogFooter class="flex-shrink-0 border-t pt-4">
        <Button variant="outline" @click="handleCancel">取消</Button>
        <Button 
          @click="handleExport" 
          :disabled="!canExport || exporting"
        >
          <Component v-if="exporting" :is="Loader2" class="mr-2 h-4 w-4 animate-spin" />
          <Download v-else class="mr-2 h-4 w-4" />
          {{ exporting ? '导出中...' : '开始导出' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Download, FileSpreadsheet, FileText, Loader2 } from 'lucide-vue-next';

interface Material {
  materialId: string;
  displayName: string;
  categoryId: string;
}

interface Category {
  id: string;
  name: string;
}

interface ExportConfig {
  scope: 'all' | 'category' | 'selected';
  categoryId: string;
  includeBasic: boolean;
  includeAttributes: boolean;
  includeVariants: boolean;
  includeStock: boolean;
  includePricing: boolean;
  includeSuppliers: boolean;
  format: 'xlsx' | 'csv';
  structure: 'single' | 'multiple';
  includeHeaders: boolean;
  includeTimestamp: boolean;
}

interface Props {
  open: boolean;
  selectedMaterials?: Material[];
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'export-complete', filename: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedMaterials: () => []
});

const emit = defineEmits<Emits>();

// 对话框状态
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
});

const exporting = ref(false);

// 模拟数据
const categories: Category[] = [
  { id: 'CAT_GLASS', name: '浮法玻璃' },
  { id: 'CAT_ALUMINUM', name: '铝型材' },
  { id: 'CAT_HARDWARE', name: '五金配件' }
];

// 导出配置
const exportConfig = ref<ExportConfig>({
  scope: 'all',
  categoryId: '',
  includeBasic: true,
  includeAttributes: true,
  includeVariants: true,
  includeStock: false,
  includePricing: false,
  includeSuppliers: false,
  format: 'xlsx',
  structure: 'single',
  includeHeaders: true,
  includeTimestamp: true
});

// 计算属性
const estimatedRecords = computed(() => {
  switch (exportConfig.value.scope) {
    case 'all':
      return 150; // 模拟所有物料数量
    case 'category':
      return 45; // 模拟分类物料数量
    case 'selected':
      return props.selectedMaterials.length;
    default:
      return 0;
  }
});

const estimatedSize = computed(() => {
  const baseSize = estimatedRecords.value * 0.5; // 每条记录约0.5KB
  const format = exportConfig.value.format === 'xlsx' ? 1.2 : 0.8; // Excel文件相对较大
  const content = Object.values(exportConfig.value).filter(v => typeof v === 'boolean' && v).length * 0.2;
  
  const totalKB = baseSize * format * (1 + content);
  
  if (totalKB < 1024) {
    return `${Math.round(totalKB)} KB`;
  } else {
    return `${(totalKB / 1024).toFixed(1)} MB`;
  }
});

const previewFileName = computed(() => {
  const timestamp = exportConfig.value.includeTimestamp 
    ? `_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}`
    : '';
  
  const scope = exportConfig.value.scope === 'category' 
    ? `_${categories.find(c => c.id === exportConfig.value.categoryId)?.name || '分类'}`
    : exportConfig.value.scope === 'selected'
    ? '_选中物料'
    : '';
  
  const ext = exportConfig.value.format;
  
  return `物料数据导出${scope}${timestamp}.${ext}`;
});

const canExport = computed(() => {
  if (exportConfig.value.scope === 'category' && !exportConfig.value.categoryId) {
    return false;
  }
  
  if (exportConfig.value.scope === 'selected' && props.selectedMaterials.length === 0) {
    return false;
  }
  
  const hasContent = exportConfig.value.includeBasic || 
                    exportConfig.value.includeAttributes || 
                    exportConfig.value.includeVariants || 
                    exportConfig.value.includeStock || 
                    exportConfig.value.includePricing || 
                    exportConfig.value.includeSuppliers;
  
  return hasContent;
});

// 初始化
const initDialog = () => {
  exportConfig.value = {
    scope: props.selectedMaterials.length > 0 ? 'selected' : 'all',
    categoryId: '',
    includeBasic: true,
    includeAttributes: true,
    includeVariants: true,
    includeStock: false,
    includePricing: false,
    includeSuppliers: false,
    format: 'xlsx',
    structure: 'single',
    includeHeaders: true,
    includeTimestamp: true
  };
};

// 监听对话框打开
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    initDialog();
  }
});

// 导出处理
const handleExport = async () => {
  exporting.value = true;
  
  try {
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 创建模拟数据
    const data = generateExportData();
    
    // 下载文件
    downloadFile(data, previewFileName.value);
    
    emit('export-complete', previewFileName.value);
    emit('update:open', false);
  } catch (error) {
    console.error('导出失败:', error);
  } finally {
    exporting.value = false;
  }
};

const generateExportData = () => {
  const headers = [];
  const rows = [];
  
  // 根据配置生成表头
  if (exportConfig.value.includeBasic) {
    headers.push('物料编码', '物料名称', '分类', '描述');
  }
  
  if (exportConfig.value.includeAttributes) {
    headers.push('厚度', '颜色', '尺寸');
  }
  
  if (exportConfig.value.includeVariants) {
    headers.push('变体SKU', '变体名称');
  }
  
  if (exportConfig.value.includeStock) {
    headers.push('库存数量', '单位');
  }
  
  if (exportConfig.value.includePricing) {
    headers.push('成本价', '销售价');
  }
  
  if (exportConfig.value.includeSuppliers) {
    headers.push('供应商');
  }
  
  // 生成示例数据行
  for (let i = 1; i <= Math.min(estimatedRecords.value, 5); i++) {
    const row = [];
    
    if (exportConfig.value.includeBasic) {
      row.push(`MAT_${i.toString().padStart(3, '0')}`, `示例物料${i}`, '浮法玻璃', `物料${i}的描述`);
    }
    
    if (exportConfig.value.includeAttributes) {
      row.push(`${6 + i}mm`, i % 2 === 0 ? '透明' : '超白', `${1220 + i * 10}x${2440 + i * 20}mm`);
    }
    
    if (exportConfig.value.includeVariants) {
      row.push(`SKU_${i.toString().padStart(3, '0')}`, `变体${i}`);
    }
    
    if (exportConfig.value.includeStock) {
      row.push(100 + i * 10, '张');
    }
    
    if (exportConfig.value.includePricing) {
      row.push(85.5 + i, 120.0 + i * 2);
    }
    
    if (exportConfig.value.includeSuppliers) {
      row.push(`供应商${i}`);
    }
    
    rows.push(row);
  }
  
  return { headers, rows };
};

const downloadFile = (data: { headers: string[], rows: (string | number)[][] }, filename: string) => {
  let content = '';
  
  if (exportConfig.value.format === 'csv') {
    // CSV格式
    if (exportConfig.value.includeHeaders) {
      content += data.headers.join(',') + '\n';
    }
    
    data.rows.forEach(row => {
      content += row.map(cell => `"${cell}"`).join(',') + '\n';
    });
    
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  } else {
    // Excel格式 (这里简化为CSV，实际项目中可以使用xlsx库)
    console.log('导出Excel文件:', filename, data);
    alert(`模拟导出Excel文件: ${filename}`);
  }
};

const handleCancel = () => {
  emit('update:open', false);
};
</script>
