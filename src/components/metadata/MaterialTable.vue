<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center justify-between">
        <div class="flex items-center">
          <Package class="h-5 w-5 mr-2" />
          物料列表
        </div>
        <div class="flex items-center space-x-2">
          <Badge variant="outline" class="text-sm">
            共 {{ filteredMaterials.length }} / {{ materials.length }} 个物料
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="outline" size="sm">
                <Settings class="mr-2 h-4 w-4" />
                设置
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>表格设置</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem 
                :checked="showColumns.category"
                @update:checked="showColumns.category = $event"
              >
                显示分类
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem 
                :checked="showColumns.attributes"
                @update:checked="showColumns.attributes = $event"
              >
                显示属性
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem 
                :checked="showColumns.variants"
                @update:checked="showColumns.variants = $event"
              >
                显示变体数
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem 
                :checked="showColumns.stock"
                @update:checked="showColumns.stock = $event"
              >
                显示库存
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardTitle>
      <CardDescription>
        点击物料行查看详细信息和库存变体
      </CardDescription>
    </CardHeader>

    <CardContent class="p-0">
      <div v-if="filteredMaterials.length === 0" class="p-8 text-center">
        <div class="text-gray-400 mb-2">
          <Package class="h-12 w-12 mx-auto mb-4" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          {{ materials.length === 0 ? '暂无物料数据' : '没有找到匹配的物料' }}
        </h3>
        <p class="text-gray-500">
          {{ materials.length === 0 ? '当前分类下还没有配置任何物料信息' : '请尝试调整搜索条件或筛选选项' }}
        </p>
      </div>
      
      <div v-else>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead class="w-[300px]">
                <Button variant="ghost" @click="toggleSort('name')" class="h-auto p-0 font-medium">
                  物料名称
                  <ArrowUpDown class="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead v-if="showColumns.category" class="w-[150px]">分类</TableHead>
              <TableHead v-if="showColumns.attributes" class="w-[200px]">基础属性</TableHead>
              <TableHead v-if="showColumns.variants" class="w-[100px]">
                <Button variant="ghost" @click="toggleSort('variants')" class="h-auto p-0 font-medium">
                  变体数量
                  <ArrowUpDown class="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead v-if="showColumns.stock" class="w-[100px]">
                <Button variant="ghost" @click="toggleSort('stock')" class="h-auto p-0 font-medium">
                  总库存
                  <ArrowUpDown class="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead class="w-[80px]">状态</TableHead>
              <TableHead class="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow
              v-for="material in paginatedMaterials"
              :key="material.materialId"
              :class="[
                'cursor-pointer hover:bg-gray-50 transition-colors',
                selectedMaterialId === material.materialId ? 'bg-blue-50' : ''
              ]"
              @click="handleSelectMaterial(material.materialId)"
            >
              <TableCell>
                <div class="font-medium">{{ material.displayName }}</div>
                <div class="text-sm text-gray-500">{{ material.materialId }}</div>
              </TableCell>
              <TableCell v-if="showColumns.category">
                <Badge variant="secondary">
                  {{ getCategoryName(material.categoryId) }}
                </Badge>
              </TableCell>
              <TableCell v-if="showColumns.attributes">
                <div class="space-y-1">
                  <div
                    v-for="(value, key) in Object.entries(material.attributes).slice(0, 2)"
                    :key="key"
                    class="text-sm"
                  >
                    <span class="text-gray-500">{{ value[0] }}:</span>
                    <span class="ml-1 font-medium">{{ value[1] }}</span>
                  </div>
                  <div v-if="Object.keys(material.attributes).length > 2" class="text-xs text-gray-400">
                    +{{ Object.keys(material.attributes).length - 2 }} 项
                  </div>
                </div>
              </TableCell>
              <TableCell v-if="showColumns.variants">
                <div class="text-center">
                  <div class="text-lg font-semibold">{{ material.variants.length }}</div>
                  <div class="text-xs text-gray-500">个变体</div>
                </div>
              </TableCell>
              <TableCell v-if="showColumns.stock">
                <div class="text-center">
                  <div class="text-lg font-semibold">{{ getTotalStock(material) }}</div>
                  <div class="text-xs text-gray-500">{{ getStockUnit(material) }}</div>
                </div>
              </TableCell>
              <TableCell>
                <Badge :variant="hasActiveVariants(material) ? 'default' : 'secondary'">
                  {{ hasActiveVariants(material) ? '正常' : '停用' }}
                </Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="ghost" size="sm" @click.stop>
                      <MoreHorizontal class="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>操作</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem @click.stop="viewMaterial(material.materialId)">
                      <Eye class="mr-2 h-4 w-4" />
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem @click.stop="editMaterial(material.materialId)">
                      <Edit class="mr-2 h-4 w-4" />
                      编辑物料
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      @click.stop="toggleMaterialStatus(material.materialId)"
                      :class="hasActiveVariants(material) ? 'text-orange-600' : 'text-green-600'"
                    >
                      <Power class="mr-2 h-4 w-4" />
                      {{ hasActiveVariants(material) ? '停用' : '启用' }}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
        
        <!-- 分页 -->
        <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t">
          <div class="text-sm text-gray-700">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredMaterials.length) }} 条，
            共 {{ filteredMaterials.length }} 条记录
          </div>
          <div class="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              :disabled="currentPage === 1"
              @click="currentPage--"
            >
              <ChevronLeft class="h-4 w-4" />
              上一页
            </Button>
            <span class="text-sm font-medium">{{ currentPage }} / {{ totalPages }}</span>
            <Button 
              variant="outline" 
              size="sm" 
              :disabled="currentPage === totalPages"
              @click="currentPage++"
            >
              下一页
              <ChevronRight class="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </CardContent>

    <!-- 物料详情抽屉 -->
    <MaterialDetail
      v-if="selectedMaterial"
      :material="selectedMaterial"
      :open="showMaterialDetail"
      @update:open="showMaterialDetail = $event"
      @material-updated="handleMaterialUpdated"
      @material-duplicated="handleMaterialDuplicated"
      @material-status-changed="handleMaterialStatusChanged"
    />

    <!-- 高级筛选对话框 -->
    <Dialog :open="showAdvancedFilter" @update:open="showAdvancedFilter = $event">
      <DialogContent class="sm:max-w-[500px] !max-h-[90vh]">
        <DialogHeader>
          <DialogTitle class="flex items-center">
            <Filter class="mr-2 h-5 w-5" />
            高级筛选
          </DialogTitle>
          <DialogDescription>
            设置详细的筛选条件来精确找到需要的物料
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4 py-4">
          <div>
            <label class="text-sm font-medium">库存范围</label>
            <div class="flex items-center space-x-2 mt-2">
              <Input 
                type="number" 
                placeholder="最小库存" 
                v-model="advancedFilter.minStock"
                class="w-full"
              />
              <span class="text-gray-500">-</span>
              <Input 
                type="number" 
                placeholder="最大库存" 
                v-model="advancedFilter.maxStock"
                class="w-full"
              />
            </div>
          </div>
          <div>
            <label class="text-sm font-medium">变体数量范围</label>
            <div class="flex items-center space-x-2 mt-2">
              <Input 
                type="number" 
                placeholder="最少变体" 
                v-model="advancedFilter.minVariants"
                class="w-full"
              />
              <span class="text-gray-500">-</span>
              <Input 
                type="number" 
                placeholder="最多变体" 
                v-model="advancedFilter.maxVariants"
                class="w-full"
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="cancelAdvancedFilter">取消</Button>
          <Button @click="applyAdvancedFilter">应用筛选</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 物料编辑对话框 -->
    <MaterialOperationDialog 
      :open="showMaterialEditDialog" 
      :material="editingMaterial" 
      :available-categories="availableCategories"
      @update:open="showMaterialEditDialog = $event" 
      @submit="handleMaterialEditSubmit" 
    />
  </Card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { Material } from '@/types/material';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuCheckboxItem } from '@/components/ui/dropdown-menu';
import { 
  Package, 
  Settings, 
  Filter, 
  ArrowUpDown,
  MoreHorizontal,
  Eye,
  Edit,
  Power,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next';
import MaterialDetail from './MaterialDetail.vue';
import MaterialOperationDialog from './MaterialOperationDialog.vue';
import { useMetadataStore } from '@/stores/metadata';
import { storeToRefs } from 'pinia';

interface Props {
  materials: Material[];
  selectedMaterialId: string | null;
}

interface Emits {
  (e: 'material-selected', materialId: string | null): void;
  (e: 'material-updated', material: Material): void;
  (e: 'material-duplicated', material: Partial<Material>): void;
  (e: 'material-status-changed', materialId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const metadataStore = useMetadataStore();
const { materialCategories } = storeToRefs(metadataStore);

// 获取可选分类
const availableCategories = computed(() => metadataStore.selectableCategories);

// 搜索和筛选状态
const searchQuery = ref('');
const statusFilter = ref('all');
const sortBy = ref('name');
const sortOrder = ref<'asc' | 'desc'>('asc');

// 高级筛选
const showAdvancedFilter = ref(false);
const advancedFilter = ref({
  minStock: '',
  maxStock: '',
  minVariants: '',
  maxVariants: ''
});

// 分页状态
const currentPage = ref(1);
const pageSize = ref(10);

// 列显示控制
const showColumns = ref({
  category: true,
  attributes: true,
  variants: true,
  stock: true
});

// 物料详情抽屉状态
const showMaterialDetail = ref(false);

// 物料编辑对话框状态
const showMaterialEditDialog = ref(false);
const editingMaterial = ref<Material | null>(null);

// 当前选中的物料
const selectedMaterial = computed(() => {
  if (!props.selectedMaterialId) return null;
  return props.materials.find(material => material.materialId === props.selectedMaterialId);
});

// 筛选后的物料数据
const filteredMaterials = computed(() => {
  let filtered = [...props.materials];

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(material => 
      material.displayName.toLowerCase().includes(query) ||
      material.materialId.toLowerCase().includes(query) ||
      Object.values(material.attributes).some(attr => 
        String(attr).toLowerCase().includes(query)
      )
    );
  }

  // 状态过滤
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(material => {
      const isActive = hasActiveVariants(material);
      return statusFilter.value === 'active' ? isActive : !isActive;
    });
  }

  // 高级筛选
  if (advancedFilter.value.minStock || advancedFilter.value.maxStock) {
    filtered = filtered.filter(material => {
      const totalStock = getTotalStock(material);
      const min = advancedFilter.value.minStock ? parseInt(advancedFilter.value.minStock) : 0;
      const max = advancedFilter.value.maxStock ? parseInt(advancedFilter.value.maxStock) : Infinity;
      return totalStock >= min && totalStock <= max;
    });
  }

  if (advancedFilter.value.minVariants || advancedFilter.value.maxVariants) {
    filtered = filtered.filter(material => {
      const variantCount = material.variants.length;
      const min = advancedFilter.value.minVariants ? parseInt(advancedFilter.value.minVariants) : 0;
      const max = advancedFilter.value.maxVariants ? parseInt(advancedFilter.value.maxVariants) : Infinity;
      return variantCount >= min && variantCount <= max;
    });
  }

  // 排序
  filtered.sort((a, b) => {
    let aValue: string | number, bValue: string | number;
    
    switch (sortBy.value) {
      case 'name':
        aValue = a.displayName;
        bValue = b.displayName;
        break;
      case 'variants':
        aValue = a.variants.length;
        bValue = b.variants.length;
        break;
      case 'stock':
        aValue = getTotalStock(a);
        bValue = getTotalStock(b);
        break;
      case 'category':
        aValue = getCategoryName(a.categoryId);
        bValue = getCategoryName(b.categoryId);
        break;
      default:
        return 0;
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    return sortOrder.value === 'asc' ? comparison : -comparison;
  });

  return filtered;
});

// 分页数据
const totalPages = computed(() => Math.ceil(filteredMaterials.value.length / pageSize.value));

const paginatedMaterials = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredMaterials.value.slice(start, end);
});

// 方法
const handleSelectMaterial = (materialId: string) => {
  if (props.selectedMaterialId === materialId) {
    emit('material-selected', null);
    showMaterialDetail.value = false;
  } else {
    emit('material-selected', materialId);
    showMaterialDetail.value = true;
  }
};

const getCategoryName = (categoryId: string) => {
  const category = materialCategories.value.find(cat => cat.categoryId === categoryId);
  return category?.categoryName || categoryId;
};

const getTotalStock = (material: Material) => {
  return material.variants.reduce((total, variant) => total + variant.stock, 0);
};

const getStockUnit = (material: Material) => {
  return material.variants[0]?.unit || '件';
};

const hasActiveVariants = (material: Material) => {
  return material.variants.some(variant => variant.isActive);
};

const toggleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'asc';
  }
};

const cancelAdvancedFilter = () => {
  showAdvancedFilter.value = false;
};

const applyAdvancedFilter = () => {
  showAdvancedFilter.value = false;
  currentPage.value = 1;
};

// 操作方法
const viewMaterial = (materialId: string) => {
  emit('material-selected', materialId);
  showMaterialDetail.value = true;
};

const handleMaterialUpdated = (material: Material) => {
  emit('material-updated', material);
  showMaterialDetail.value = false;
};

const handleMaterialDuplicated = (material: Partial<Material>) => {
  emit('material-duplicated', material);
};

const handleMaterialStatusChanged = (materialId: string) => {
  emit('material-status-changed', materialId);
};

const editMaterial = (materialId: string) => {
  const material = props.materials.find(m => m.materialId === materialId);
  if (material) {
    editingMaterial.value = material;
    showMaterialEditDialog.value = true;
  }
};

const toggleMaterialStatus = (materialId: string) => {
  // 切换物料下所有变体的状态
  const material = props.materials.find(m => m.materialId === materialId);
  if (material) {
    const hasActive = material.variants.some(variant => variant.isActive);
    // 如果有激活的变体，则全部停用；如果全部停用，则全部激活
    const newStatus = !hasActive;
    
    // 这里应该调用API来更新状态，现在先触发事件
    emit('material-status-changed', materialId);
    console.log(`物料 ${materialId} 状态切换为: ${newStatus ? '启用' : '停用'}`);
  }
};

// 处理物料编辑提交
const handleMaterialEditSubmit = (materialData: Material) => {
  emit('material-updated', materialData);
  showMaterialEditDialog.value = false;
  editingMaterial.value = null;
};
</script>