<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="!w-[90vw] !max-w-[1000px] !max-h-[90vh] overflow-hidden flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle class="flex items-center">
          <FileUp class="mr-2 h-5 w-5" />
          批量导入物料
        </DialogTitle>
        <DialogDescription>
          通过Excel文件批量导入物料和变体数据
        </DialogDescription>
      </DialogHeader>

      <!-- 主要内容区域 -->
      <div class="flex-1 overflow-y-auto">
        <Tabs default-value="upload" class="space-y-4">
          <TabsList class="grid w-full grid-cols-3">
            <TabsTrigger value="upload">文件上传</TabsTrigger>
            <TabsTrigger value="template">下载模板</TabsTrigger>
            <TabsTrigger value="preview">数据预览</TabsTrigger>
          </TabsList>

          <!-- 文件上传标签页 -->
          <TabsContent value="upload" class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center">
                  <Upload class="mr-2 h-5 w-5" />
                  选择导入文件
                </CardTitle>
                <CardDescription>
                  请选择包含物料数据的Excel文件（.xlsx 或 .xls 格式）
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
                  <input
                    ref="fileInput"
                    type="file"
                    accept=".xlsx,.xls"
                    @change="handleFileSelect"
                    class="hidden"
                  />
                  
                  <div v-if="!selectedFile">
                    <FileSpreadsheet class="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 mb-2">选择Excel文件</h3>
                    <p class="text-gray-500 mb-4">点击选择或拖拽文件到此区域</p>
                    <Button @click="triggerFileSelect">
                      <Upload class="mr-2 h-4 w-4" />
                      选择文件
                    </Button>
                  </div>
                  
                  <div v-else class="space-y-4">
                    <div class="flex items-center justify-center space-x-2">
                      <FileSpreadsheet class="h-8 w-8 text-green-600" />
                      <div class="text-left">
                        <div class="font-medium">{{ selectedFile.name }}</div>
                        <div class="text-sm text-gray-500">{{ formatFileSize(selectedFile.size) }}</div>
                      </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                      <Button @click="triggerFileSelect" variant="outline" size="sm">
                        <Edit class="mr-2 h-4 w-4" />
                        重新选择
                      </Button>
                      <Button @click="parseFile" :disabled="parsing">
                        <Component v-if="parsing" :is="Loader2" class="mr-2 h-4 w-4 animate-spin" />
                        <Eye v-else class="mr-2 h-4 w-4" />
                        {{ parsing ? '解析中...' : '解析文件' }}
                      </Button>
                    </div>
                  </div>
                </div>
                
                <!-- 导入设置 -->
                <div v-if="selectedFile" class="mt-6 space-y-4">
                  <h4 class="font-medium">导入设置</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="text-sm font-medium">数据表名</label>
                      <Select v-model="importSettings.sheetName">
                        <SelectTrigger class="mt-2">
                          <SelectValue placeholder="选择数据表" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem v-for="sheet in availableSheets" :key="sheet" :value="sheet">
                            {{ sheet }}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label class="text-sm font-medium">标题行</label>
                      <Input 
                        v-model.number="importSettings.headerRow"
                        type="number"
                        min="1"
                        placeholder="1"
                        class="mt-2"
                      />
                    </div>
                  </div>
                  
                  <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                      <input 
                        id="skipExisting" 
                        type="checkbox"
                        v-model="importSettings.skipExisting"
                        class="rounded border-gray-300"
                      />
                      <label for="skipExisting" class="text-sm font-medium">
                        跳过已存在的物料
                      </label>
                    </div>
                    <div class="flex items-center space-x-2">
                      <input 
                        id="updateExisting" 
                        type="checkbox"
                        v-model="importSettings.updateExisting"
                        class="rounded border-gray-300"
                      />
                      <label for="updateExisting" class="text-sm font-medium">
                        更新已存在的物料
                      </label>
                    </div>
                    <div class="flex items-center space-x-2">
                      <input 
                        id="validateOnly" 
                        type="checkbox"
                        v-model="importSettings.validateOnly"
                        class="rounded border-gray-300"
                      />
                      <label for="validateOnly" class="text-sm font-medium">
                        仅验证不导入
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 模板下载标签页 -->
          <TabsContent value="template" class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center">
                  <Download class="mr-2 h-5 w-5" />
                  导入模板
                </CardTitle>
                <CardDescription>
                  下载标准的物料导入模板，填写数据后上传
                </CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="border rounded-lg p-4">
                    <div class="flex items-center mb-3">
                      <FileText class="h-6 w-6 text-blue-600 mr-2" />
                      <h4 class="font-medium">基础物料模板</h4>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">
                      包含物料基本信息、分类和属性字段
                    </p>
                    <Button @click="downloadTemplate('basic')" variant="outline" class="w-full">
                      <Download class="mr-2 h-4 w-4" />
                      下载基础模板
                    </Button>
                  </div>
                  
                  <div class="border rounded-lg p-4">
                    <div class="flex items-center mb-3">
                      <FileText class="h-6 w-6 text-green-600 mr-2" />
                      <h4 class="font-medium">完整物料模板</h4>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">
                      包含物料信息、属性和库存变体完整数据
                    </p>
                    <Button @click="downloadTemplate('full')" variant="outline" class="w-full">
                      <Download class="mr-2 h-4 w-4" />
                      下载完整模板
                    </Button>
                  </div>
                </div>
                
                <!-- 字段说明 -->
                <div class="border rounded-lg p-4">
                  <h4 class="font-medium mb-3">字段说明</h4>
                  <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                      <thead>
                        <tr class="border-b">
                          <th class="text-left p-2">字段名</th>
                          <th class="text-left p-2">必填</th>
                          <th class="text-left p-2">说明</th>
                          <th class="text-left p-2">示例</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="field in templateFields" :key="field.name" class="border-b">
                          <td class="p-2 font-mono text-xs">{{ field.name }}</td>
                          <td class="p-2">
                            <Badge :variant="field.required ? 'destructive' : 'secondary'" class="text-xs">
                              {{ field.required ? '必填' : '可选' }}
                            </Badge>
                          </td>
                          <td class="p-2">{{ field.description }}</td>
                          <td class="p-2 font-mono text-xs text-gray-600">{{ field.example }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 数据预览标签页 -->
          <TabsContent value="preview" class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center justify-between">
                  <div class="flex items-center">
                    <TableIcon class="mr-2 h-5 w-5" />
                    数据预览
                  </div>
                  <div v-if="parsedData.length > 0" class="flex items-center space-x-2">
                    <Badge variant="outline">共 {{ parsedData.length }} 条记录</Badge>
                    <Badge variant="outline">{{ validRecords }} 条有效</Badge>
                    <Badge v-if="errorRecords > 0" variant="destructive">{{ errorRecords }} 条错误</Badge>
                  </div>
                </CardTitle>
                <CardDescription>
                  检查导入数据的格式和内容
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div v-if="parsedData.length === 0" class="text-center py-8">
                  <TableIcon class="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
                  <p class="text-gray-500">请先上传并解析Excel文件</p>
                </div>
                
                <div v-else class="space-y-4">
                  <!-- 验证结果摘要 -->
                  <div v-if="validationResults.length > 0" class="border rounded-lg p-4 bg-yellow-50">
                    <h4 class="font-medium text-yellow-800 mb-2">验证结果</h4>
                    <div class="space-y-1">
                      <div v-for="result in validationResults.slice(0, 5)" :key="result.row" class="text-sm text-yellow-700">
                        第{{ result.row }}行: {{ result.message }}
                      </div>
                      <div v-if="validationResults.length > 5" class="text-sm text-yellow-600">
                        还有 {{ validationResults.length - 5 }} 个问题...
                      </div>
                    </div>
                  </div>
                  
                  <!-- 数据表格 -->
                  <div class="border rounded-lg overflow-hidden">
                    <div class="max-h-[400px] overflow-auto">
                      <table class="w-full text-sm">
                        <thead class="bg-gray-50 sticky top-0">
                          <tr>
                            <th class="text-left p-2 border-r">行号</th>
                            <th class="text-left p-2 border-r">状态</th>
                            <th class="text-left p-2 border-r">物料编码</th>
                            <th class="text-left p-2 border-r">物料名称</th>
                            <th class="text-left p-2 border-r">分类</th>
                            <th class="text-left p-2">操作</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(record, index) in parsedData" :key="index" class="border-b hover:bg-gray-50">
                            <td class="p-2 border-r">{{ index + 1 }}</td>
                            <td class="p-2 border-r">
                              <Badge :variant="record.valid ? 'default' : 'destructive'" class="text-xs">
                                {{ record.valid ? '有效' : '错误' }}
                              </Badge>
                            </td>
                            <td class="p-2 border-r font-mono text-xs">{{ record.materialId }}</td>
                            <td class="p-2 border-r">{{ record.displayName }}</td>
                            <td class="p-2 border-r">{{ record.categoryId }}</td>
                            <td class="p-2">
                              <Button 
                                v-if="!record.valid" 
                                variant="ghost" 
                                size="sm" 
                                @click="showRowErrors(record)"
                              >
                                <AlertCircle class="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <DialogFooter class="flex-shrink-0 border-t pt-4">
        <Button variant="outline" @click="handleCancel">取消</Button>
        <Button 
          @click="handleImport" 
          :disabled="!canImport || importing"
        >
          <Component v-if="importing" :is="Loader2" class="mr-2 h-4 w-4 animate-spin" />
          {{ importing ? '导入中...' : importSettings.validateOnly ? '验证数据' : '开始导入' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  FileUp, 
  Upload, 
  Download, 
  FileSpreadsheet,
  FileText,
  Edit,
  Eye,
  Loader2,
  Table as TableIcon,
  AlertCircle
} from 'lucide-vue-next';

interface ImportRecord {
  materialId: string;
  displayName: string;
  categoryId: string;
  valid: boolean;
  errors: string[];
}

interface ImportSettings {
  sheetName: string;
  headerRow: number;
  skipExisting: boolean;
  updateExisting: boolean;
  validateOnly: boolean;
}

interface ValidationResult {
  row: number;
  message: string;
  severity: 'error' | 'warning';
}

interface Props {
  open: boolean;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'import-complete', data: ImportRecord[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 对话框状态
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
});

const fileInput = ref<HTMLInputElement>();
const selectedFile = ref<File | null>(null);
const parsing = ref(false);
const importing = ref(false);

// 数据状态
const availableSheets = ref<string[]>([]);
const parsedData = ref<ImportRecord[]>([]);
const validationResults = ref<ValidationResult[]>([]);

// 导入设置
const importSettings = ref<ImportSettings>({
  sheetName: '',
  headerRow: 1,
  skipExisting: true,
  updateExisting: false,
  validateOnly: false
});

// 模板字段定义
const templateFields = [
  { name: 'materialId', required: true, description: '物料编码', example: 'MAT_001' },
  { name: 'displayName', required: true, description: '物料名称', example: '6mm透明浮法玻璃' },
  { name: 'categoryId', required: true, description: '分类编码', example: 'CAT_GLASS' },
  { name: 'description', required: false, description: '物料描述', example: '高质量浮法玻璃' },
  { name: 'attributes', required: false, description: '基础属性（JSON）', example: '{"厚度": 6, "颜色": "透明"}' },
  { name: 'variantSku', required: false, description: '变体SKU', example: 'SKU_001' },
  { name: 'variantName', required: false, description: '变体名称', example: '1220x2440mm' },
  { name: 'stock', required: false, description: '库存数量', example: '100' },
  { name: 'unit', required: false, description: '单位', example: '张' },
  { name: 'cost', required: false, description: '成本', example: '85.50' },
  { name: 'supplier', required: false, description: '供应商', example: '南玻集团' }
];

// 计算属性
const validRecords = computed(() => {
  return parsedData.value.filter(record => record.valid).length;
});

const errorRecords = computed(() => {
  return parsedData.value.filter(record => !record.valid).length;
});

const canImport = computed(() => {
  return parsedData.value.length > 0 && (importSettings.value.validateOnly || validRecords.value > 0);
});

// 初始化
const initDialog = () => {
  selectedFile.value = null;
  parsedData.value = [];
  validationResults.value = [];
  availableSheets.value = [];
  
  importSettings.value = {
    sheetName: '',
    headerRow: 1,
    skipExisting: true,
    updateExisting: false,
    validateOnly: false
  };
};

// 监听对话框打开
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    initDialog();
  }
});

// 文件操作
const triggerFileSelect = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  
  if (file) {
    selectedFile.value = file;
    // 模拟获取Excel表名
    availableSheets.value = ['Sheet1', '物料数据', '库存清单'];
    importSettings.value.sheetName = availableSheets.value[0];
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 文件解析
const parseFile = async () => {
  if (!selectedFile.value) return;
  
  parsing.value = true;
  
  try {
    // 模拟文件解析
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 模拟解析数据
    parsedData.value = [
      {
        materialId: 'MAT_001',
        displayName: '6mm透明浮法玻璃',
        categoryId: 'CAT_GLASS',
        valid: true,
        errors: []
      },
      {
        materialId: 'MAT_002',
        displayName: '8mm钢化玻璃',
        categoryId: 'CAT_GLASS',
        valid: true,
        errors: []
      },
      {
        materialId: '',
        displayName: '无效物料',
        categoryId: 'CAT_UNKNOWN',
        valid: false,
        errors: ['物料编码不能为空', '分类不存在']
      }
    ];
    
    // 模拟验证结果
    validationResults.value = [
      {
        row: 3,
        message: '物料编码不能为空',
        severity: 'error'
      },
      {
        row: 3,
        message: '分类"CAT_UNKNOWN"不存在',
        severity: 'error'
      }
    ];
    
  } catch (error) {
    console.error('文件解析失败:', error);
  } finally {
    parsing.value = false;
  }
};

// 模板下载
const downloadTemplate = (type: 'basic' | 'full') => {
  // 创建模板数据
  const templateData = type === 'basic' 
    ? 'materialId,displayName,categoryId,description\nMAT_001,6mm透明浮法玻璃,CAT_GLASS,高质量浮法玻璃'
    : 'materialId,displayName,categoryId,description,variantSku,variantName,stock,unit,cost,supplier\nMAT_001,6mm透明浮法玻璃,CAT_GLASS,高质量浮法玻璃,SKU_001,1220x2440mm,100,张,85.50,南玻集团';
  
  const blob = new Blob([templateData], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `物料导入模板_${type === 'basic' ? '基础' : '完整'}.csv`;
  a.click();
  URL.revokeObjectURL(url);
  
  console.log(`下载${type === 'basic' ? '基础' : '完整'}模板`);
};

// 错误详情
const showRowErrors = (record: ImportRecord) => {
  alert(`第${parsedData.value.indexOf(record) + 1}行错误:\n${record.errors.join('\n')}`);
};

// 导入处理
const handleImport = async () => {
  importing.value = true;
  
  try {
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    if (importSettings.value.validateOnly) {
      console.log('数据验证完成');
    } else {
      console.log('数据导入完成');
      emit('import-complete', parsedData.value.filter(record => record.valid));
    }
    
    emit('update:open', false);
  } catch (error) {
    console.error('导入失败:', error);
  } finally {
    importing.value = false;
  }
};

const handleCancel = () => {
  emit('update:open', false);
};
</script>
