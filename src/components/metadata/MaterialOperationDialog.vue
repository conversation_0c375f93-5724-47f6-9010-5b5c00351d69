<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="!w-[95vw] !max-w-[1400px] !max-h-[95vh] overflow-hidden flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle class="flex items-center">
          <Component v-if="isEdit" :is="Edit" class="mr-2 h-5 w-5" />
          <Component v-else :is="Plus" class="mr-2 h-5 w-5" />
          {{ isEdit ? '编辑物料' : '新增物料' }}
        </DialogTitle>
        <DialogDescription>
          {{ isEdit ? '修改物料的基本信息和属性配置' : '创建新的物料，配置基础信息和初始变体' }}
        </DialogDescription>
      </DialogHeader>

      <!-- 主要内容区域 -->
      <div class="flex-1 overflow-y-auto">
        <Tabs default-value="basic" class="space-y-4">
          <TabsList class="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="attributes">基础属性</TabsTrigger>
            <TabsTrigger value="variants">库存变体</TabsTrigger>
            <TabsTrigger value="settings">高级设置</TabsTrigger>
          </TabsList>

          <!-- 基本信息标签页 -->
          <TabsContent value="basic" class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center">
                  <Info class="mr-2 h-5 w-5" />
                  物料基本信息
                </CardTitle>
                <CardDescription>
                  配置物料的基础标识信息
                </CardDescription>
              </CardHeader>
              <CardContent class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="text-sm font-medium">物料名称 *</label>
                    <Input 
                      v-model="materialForm.displayName"
                      placeholder="请输入物料名称"
                      class="mt-2"
                      :class="{ 'border-red-500': errors.displayName }"
                    />
                    <p v-if="errors.displayName" class="text-sm text-red-500 mt-1">{{ errors.displayName }}</p>
                  </div>
                  <div>
                    <label class="text-sm font-medium">物料编码 *</label>
                    <Input 
                      v-model="materialForm.materialId"
                      placeholder="请输入物料编码（例如：MAT_001）"
                      class="mt-2"
                      :disabled="isEdit"
                      :class="{ 'border-red-500': errors.materialId }"
                    />
                    <p v-if="errors.materialId" class="text-sm text-red-500 mt-1">{{ errors.materialId }}</p>
                    <p v-if="!isEdit" class="text-xs text-gray-500 mt-1">编码创建后不可修改</p>
                  </div>
                </div>
                
                <div>
                  <label class="text-sm font-medium">所属分类 *</label>
                  <Select v-model="materialForm.categoryId" :disabled="isEdit">
                    <SelectTrigger class="mt-2" :class="{ 'border-red-500': errors.categoryId }">
                      <SelectValue placeholder="选择物料分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem 
                        v-for="category in selectableCategories"
                        :key="category.categoryId"
                        :value="category.categoryId"
                      >
                        {{ category.categoryName }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="errors.categoryId" class="text-sm text-red-500 mt-1">{{ errors.categoryId }}</p>
                  <p v-if="isEdit" class="text-xs text-gray-500 mt-1">分类创建后不可修改</p>
                </div>

                <div>
                  <label class="text-sm font-medium">物料描述</label>
                  <Textarea
                    v-model="materialForm.description"
                    placeholder="请输入物料描述（可选）"
                    class="mt-2"
                    rows="3"
                  />
                </div>
                
                <!-- 分类属性模板预览 -->
                <div v-if="selectedCategory && selectedCategory.attributeSchema" class="border rounded-lg p-4 bg-gray-50">
                  <div class="flex items-center mb-3">
                    <Template class="h-4 w-4 mr-2" />
                    <span class="text-sm font-medium">分类属性模板</span>
                  </div>
                  <div class="text-sm text-gray-600 mb-2">
                    该分类包含 {{ selectedCategory.attributeSchema.baseAttributes.length }} 个基础属性
                    和 {{ selectedCategory.attributeSchema.variantAttributes.length }} 个变体属性
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <Badge 
                      v-for="attr in selectedCategory.attributeSchema.baseAttributes.slice(0, 6)"
                      :key="attr.name"
                      variant="outline"
                      class="text-xs"
                    >
                      {{ attr.name }} ({{ attr.type }})
                    </Badge>
                    <Badge 
                      v-if="selectedCategory.attributeSchema.baseAttributes.length > 6"
                      variant="outline"
                      class="text-xs text-gray-500"
                    >
                      +{{ selectedCategory.attributeSchema.baseAttributes.length - 6 }} 项
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 基础属性标签页 -->
          <TabsContent value="attributes" class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center">
                  <Settings class="mr-2 h-5 w-5" />
                  基础属性配置
                </CardTitle>
                <CardDescription>
                  根据分类模板配置物料的基础属性值
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div v-if="!selectedCategory?.attributeSchema" class="text-center py-8">
                  <AlertCircle class="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 mb-2">请先选择分类</h3>
                  <p class="text-gray-500">选择分类后才能配置基础属性</p>
                </div>
                
                <div v-else class="space-y-4">
                  <AttributeValueEditor
                    :schema="selectedCategory.attributeSchema.baseAttributes"
                    v-model="materialForm.attributes"
                    :errors="attributeErrors"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 库存变体标签页 -->
          <TabsContent value="variants" class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center justify-between">
                  <div class="flex items-center">
                    <Box class="mr-2 h-5 w-5" />
                    库存变体配置
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge variant="outline" class="text-sm">
                      共 {{ materialForm.variants.length }} 个变体
                    </Badge>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      @click="addVariant"
                      :disabled="!selectedCategory?.attributeSchema"
                    >
                      <Plus class="mr-2 h-4 w-4" />
                      添加变体
                    </Button>
                  </div>
                </CardTitle>
                <CardDescription>
                  配置物料的具体库存变体规格和初始库存
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div v-if="!selectedCategory?.attributeSchema" class="text-center py-8">
                  <Box class="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 mb-2">请先完成基础配置</h3>
                  <p class="text-gray-500">选择分类并配置基础属性后才能添加变体</p>
                </div>
                
                <div v-else-if="materialForm.variants.length === 0" class="text-center py-8">
                  <Package class="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 mb-2">暂无变体</h3>
                  <p class="text-gray-500 mb-4">点击"添加变体"按钮创建第一个库存变体</p>
                  <Button @click="addVariant">
                    <Plus class="mr-2 h-4 w-4" />
                    添加第一个变体
                  </Button>
                </div>
                
                <div v-else class="space-y-4">
                  <div 
                    v-for="(variant, index) in materialForm.variants"
                    :key="variant.tempId || variant.variantId"
                    class="border rounded-lg p-4 relative"
                  >
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="font-medium">变体 {{ index + 1 }}</h4>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        @click="removeVariant(index)"
                        class="text-red-600 hover:text-red-700"
                      >
                        <Trash2 class="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label class="text-sm font-medium">SKU *</label>
                        <Input 
                          v-model="variant.sku"
                          placeholder="请输入SKU"
                          class="mt-2"
                        />
                      </div>
                      <div>
                        <label class="text-sm font-medium">变体名称 *</label>
                        <Input 
                          v-model="variant.displayName"
                          placeholder="请输入变体名称"
                          class="mt-2"
                        />
                      </div>
                      <div>
                        <label class="text-sm font-medium">初始库存</label>
                        <Input 
                          v-model.number="variant.stock"
                          type="number"
                          placeholder="0"
                          class="mt-2"
                        />
                      </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                      <div>
                        <label class="text-sm font-medium">单位</label>
                        <Input 
                          v-model="variant.unit"
                          placeholder="件/m²/kg"
                          class="mt-2"
                        />
                      </div>
                      <div>
                        <label class="text-sm font-medium">成本单价</label>
                        <Input 
                          v-model.number="variant.cost"
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          class="mt-2"
                        />
                      </div>
                      <div>
                        <label class="text-sm font-medium">供应商</label>
                        <Input 
                          v-model="variant.supplier"
                          placeholder="请输入供应商"
                          class="mt-2"
                        />
                      </div>
                    </div>
                    
                    <!-- 变体属性配置 -->
                    <div v-if="selectedCategory?.attributeSchema.variantAttributes.length" class="mt-4">
                      <h5 class="text-sm font-medium mb-3">变体属性</h5>
                      <AttributeValueEditor
                        :schema="selectedCategory.attributeSchema.variantAttributes"
                        v-model="variant.variantAttributes"
                        compact
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 高级设置标签页 -->
          <TabsContent value="settings" class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center">
                  <Cog class="mr-2 h-5 w-5" />
                  高级设置
                </CardTitle>
                <CardDescription>
                  配置物料的高级选项和业务规则
                </CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <div class="space-y-4">
                  <div class="flex items-center space-x-2">
                    <input 
                      id="autoGenerateVariants" 
                      type="checkbox"
                      v-model="materialForm.autoGenerateVariants"
                      class="rounded border-gray-300"
                    />
                    <label for="autoGenerateVariants" class="text-sm font-medium">
                      自动生成变体
                    </label>
                  </div>
                  <p class="text-xs text-gray-500 ml-6">
                    根据变体属性组合自动生成所有可能的变体
                  </p>
                  
                  <div class="flex items-center space-x-2">
                    <input 
                      id="trackSerialNumbers" 
                      type="checkbox"
                      v-model="materialForm.trackSerialNumbers"
                      class="rounded border-gray-300"
                    />
                    <label for="trackSerialNumbers" class="text-sm font-medium">
                      启用序列号管理
                    </label>
                  </div>
                  <p class="text-xs text-gray-500 ml-6">
                    为该物料的库存变体启用序列号跟踪
                  </p>
                  
                  <div class="flex items-center space-x-2">
                    <input 
                      id="allowNegativeStock" 
                      type="checkbox"
                      v-model="materialForm.allowNegativeStock"
                      class="rounded border-gray-300"
                    />
                    <label for="allowNegativeStock" class="text-sm font-medium">
                      允许负库存
                    </label>
                  </div>
                  <p class="text-xs text-gray-500 ml-6">
                    允许该物料的库存数量为负数（欠库处理）
                  </p>
                </div>
                
                <div class="border-t pt-4">
                  <h4 class="text-sm font-medium mb-3">库存预警设置</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="text-sm font-medium">最低库存预警</label>
                      <Input 
                        v-model.number="materialForm.minStockAlert"
                        type="number"
                        placeholder="0"
                        class="mt-2"
                      />
                    </div>
                    <div>
                      <label class="text-sm font-medium">最高库存预警</label>
                      <Input 
                        v-model.number="materialForm.maxStockAlert"
                        type="number"
                        placeholder="1000"
                        class="mt-2"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <DialogFooter class="flex-shrink-0 border-t pt-4">
        <Button variant="outline" @click="handleCancel">取消</Button>
        <Button @click="handleSave" :disabled="!isFormValid || saving">
          <Component v-if="saving" :is="Loader2" class="mr-2 h-4 w-4 animate-spin" />
          {{ saving ? '保存中...' : (isEdit ? '更新物料' : '创建物料') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { Material, MaterialCategory } from '@/types/material';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Edit, 
  Plus, 
  Info, 
  Settings, 
  Box, 
  Package,
  Cog,
  AlertCircle,
  Trash2,
  Loader2
} from 'lucide-vue-next';
import AttributeValueEditor from './AttributeValueEditor.vue';

interface Props {
  open: boolean;
  material?: Material | null;
  availableCategories: MaterialCategory[];
}

interface VariantFormData {
  tempId?: number;
  variantId: string;
  sku: string;
  displayName: string;
  variantAttributes: Record<string, string | number>;
  stock: number;
  unit: string;
  cost: number;
  supplier: string;
  leadTimeDays: number;
  isActive: boolean;
}

interface MaterialFormData {
  materialId: string;
  displayName: string;
  categoryId: string;
  description: string;
  attributes: Record<string, string | number>;
  variants: VariantFormData[];
  autoGenerateVariants: boolean;
  trackSerialNumbers: boolean;
  allowNegativeStock: boolean;
  minStockAlert: number;
  maxStockAlert: number;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'submit', material: MaterialFormData): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 对话框状态
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
});

const isEdit = computed(() => !!props.material);
const saving = ref(false);

// 可选择的分类（只有叶子节点）
const selectableCategories = computed(() => {
  return props.availableCategories.filter(category => 
    !category.hasChildren && category.attributeSchema
  );
});

// 当前选中的分类
const selectedCategory = computed(() => {
  if (!materialForm.value.categoryId) return null;
  return selectableCategories.value.find(cat => cat.categoryId === materialForm.value.categoryId);
});

// 表单数据
const materialForm = ref<MaterialFormData>({
  materialId: '',
  displayName: '',
  categoryId: '',
  description: '',
  attributes: {},
  variants: [],
  autoGenerateVariants: false,
  trackSerialNumbers: false,
  allowNegativeStock: false,
  minStockAlert: 0,
  maxStockAlert: 1000
});

// 验证错误
const errors = ref({
  materialId: '',
  displayName: '',
  categoryId: ''
});

const attributeErrors = ref({} as Record<string, string>);

// 初始化表单
const initForm = () => {
  if (props.material) {
    materialForm.value = {
      materialId: props.material.materialId,
      displayName: props.material.displayName,
      categoryId: props.material.categoryId,
      description: '',
      attributes: { ...props.material.attributes },
      variants: props.material.variants.map(v => ({ ...v, tempId: Date.now() + Math.random() })),
      autoGenerateVariants: false,
      trackSerialNumbers: false,
      allowNegativeStock: false,
      minStockAlert: 0,
      maxStockAlert: 1000
    };
  } else {
    materialForm.value = {
      materialId: '',
      displayName: '',
      categoryId: '',
      description: '',
      attributes: {},
      variants: [],
      autoGenerateVariants: false,
      trackSerialNumbers: false,
      allowNegativeStock: false,
      minStockAlert: 0,
      maxStockAlert: 1000
    };
  }
  
  // 清空错误
  errors.value = {
    materialId: '',
    displayName: '',
    categoryId: ''
  };
  attributeErrors.value = {};
};

// 监听分类变化，重置属性
watch(() => materialForm.value.categoryId, (newCategoryId, oldCategoryId) => {
  if (newCategoryId !== oldCategoryId && !isEdit.value) {
    materialForm.value.attributes = {};
    materialForm.value.variants = [];
  }
});

// 监听对话框打开
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    initForm();
  }
});

// 表单验证
const validateForm = () => {
  errors.value = {
    materialId: '',
    displayName: '',
    categoryId: ''
  };

  let isValid = true;

  if (!materialForm.value.materialId.trim()) {
    errors.value.materialId = '物料编码不能为空';
    isValid = false;
  } else if (!/^[A-Z][A-Z0-9_]*$/.test(materialForm.value.materialId)) {
    errors.value.materialId = '物料编码必须以字母开头，只能包含大写字母、数字和下划线';
    isValid = false;
  }

  if (!materialForm.value.displayName.trim()) {
    errors.value.displayName = '物料名称不能为空';
    isValid = false;
  }

  if (!materialForm.value.categoryId) {
    errors.value.categoryId = '请选择物料分类';
    isValid = false;
  }

  // 验证必填的基础属性
  if (selectedCategory.value?.attributeSchema) {
    const requiredAttrs = selectedCategory.value.attributeSchema.baseAttributes.filter(attr => attr.required);
    for (const attr of requiredAttrs) {
      if (!materialForm.value.attributes[attr.name]) {
        attributeErrors.value[attr.name] = `${attr.name}为必填属性`;
        isValid = false;
      }
    }
  }

  return isValid;
};

const isFormValid = computed(() => {
  return materialForm.value.materialId && 
         materialForm.value.displayName && 
         materialForm.value.categoryId;
});

// 变体操作
const addVariant = () => {
  if (!selectedCategory.value?.attributeSchema) return;
  
  const newVariant = {
    tempId: Date.now() + Math.random(),
    variantId: `${materialForm.value.materialId}_V${materialForm.value.variants.length + 1}`,
    sku: `${materialForm.value.materialId}_SKU${materialForm.value.variants.length + 1}`,
    displayName: `${materialForm.value.displayName} 变体${materialForm.value.variants.length + 1}`,
    variantAttributes: {},
    stock: 0,
    unit: '件',
    cost: 0,
    supplier: '',
    leadTimeDays: 7,
    isActive: true
  };
  
  materialForm.value.variants.push(newVariant);
};

const removeVariant = (index: number) => {
  materialForm.value.variants.splice(index, 1);
};

// 事件处理
const handleCancel = () => {
  emit('update:open', false);
};

const handleSave = async () => {
  if (!validateForm()) return;
  
  saving.value = true;
  
  try {
    const materialData = {
      ...materialForm.value,
      variants: materialForm.value.variants.map(v => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { tempId, ...variant } = v;
        return variant;
      })
    };
    
    emit('submit', materialData);
    emit('update:open', false);
  } catch (error) {
    console.error('保存失败:', error);
  } finally {
    saving.value = false;
  }
};
</script>
