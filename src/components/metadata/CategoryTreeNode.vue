<template>
  <div>
    <!-- 当前节点 -->
    <div
      :class="[
        'px-3 py-2 cursor-pointer transition-all duration-200 hover:bg-gray-50 flex items-center group',
        isSelected 
          ? 'bg-blue-50 border-r-4 border-r-blue-500 text-blue-900' 
          : 'hover:border-r-4 hover:border-r-gray-300'
      ]"
      :style="{ paddingLeft: `${12 + (node.level || 0) * 16}px` }"
      @click="handleNodeClick"
      @contextmenu.prevent="handleContextMenu"
    >
      <!-- 展开/折叠图标 -->
      <div class="w-4 h-4 flex items-center justify-center mr-2" @click.stop="handleToggleClick">
        <ChevronRight 
          v-if="hasChildren" 
          :class="[
            'h-4 w-4 transition-transform duration-200 text-gray-500 group-hover:text-gray-800',
            node.expanded ? 'rotate-90' : ''
          ]"
        />
        <span v-else class="w-4"></span>
      </div>
      
      <!-- 分类图标 -->
      <div class="w-4 h-4 flex items-center justify-center mr-2">
        <Folder 
          v-if="hasChildren" 
          :class="[
            'h-4 w-4',
            node.expanded ? 'text-blue-600' : 'text-gray-500'
          ]"
        />
        <Package 
          v-else 
          class="h-4 w-4 text-gray-500"
        />
      </div>
      
      <!-- 分类名称和物料数量 -->
      <div class="flex-1 min-w-0">
        <div class="text-sm font-medium truncate" :class="{'text-blue-900': isSelected}">
          {{ node.categoryName }}
        </div>
      </div>
      
      <!-- 物料数量徽章 -->
      <div class="ml-2 flex-shrink-0">
        <Badge 
          v-if="!hasChildren" 
          variant="outline" 
          class="text-xs font-normal"
        >
          {{ getMaterialCount(node.categoryId) }}
        </Badge>
      </div>
    </div>
    
    <!-- 子节点 -->
    <div v-if="hasChildren && node.expanded">
      <CategoryTreeNode
        v-for="child in node.children"
        :key="child.categoryId"
        :node="child"
        :selected-category-id="selectedCategoryId"
        :materials="materials"
        @node-selected="$emit('node-selected', $event)"
        @node-toggle="$emit('node-toggle', $event)"
        @node-context-menu="$emit('node-context-menu', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { MaterialCategoryTreeNode, Material } from '@/types/material';
import { Badge } from '@/components/ui/badge';
import { ChevronRight, Folder, Package } from 'lucide-vue-next';

interface Props {
  node: MaterialCategoryTreeNode;
  selectedCategoryId: string | null;
  materials: Material[];
}

interface Emits {
  (e: 'node-selected', node: MaterialCategoryTreeNode): void;
  (e: 'node-toggle', node: MaterialCategoryTreeNode): void;
  (e: 'node-context-menu', event: { e: MouseEvent, node: MaterialCategoryTreeNode }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const isSelected = computed(() => {
  return props.selectedCategoryId === props.node.categoryId;
});

const hasChildren = computed(() => {
  return props.node.children && props.node.children.length > 0;
});

const getMaterialCount = (categoryId: string) => {
  // This count could be pre-calculated and passed down for better performance
  return props.materials.filter(material => material.categoryId === categoryId).length;
};

const handleNodeClick = () => {
  emit('node-selected', props.node);
};

const handleToggleClick = (event: MouseEvent) => {
  event.stopPropagation(); // Prevent node selection when only toggling
  if (hasChildren.value) {
    emit('node-toggle', props.node);
  }
};

const handleContextMenu = (event: MouseEvent) => {
  emit('node-context-menu', { e: event, node: props.node });
};
</script>