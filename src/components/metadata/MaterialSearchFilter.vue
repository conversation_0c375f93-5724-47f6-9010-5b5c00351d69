<template>
  <div class="space-y-4">
    <!-- 搜索栏 -->
    <div class="flex items-center space-x-4">
      <div class="flex-1 relative">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          v-model="searchQuery"
          placeholder="搜索物料编码、名称或描述..."
          class="pl-10 pr-10"
        />
        <Button
          v-if="searchQuery"
          variant="ghost"
          size="sm"
          @click="clearSearch"
          class="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
        >
          <X class="h-4 w-4" />
        </Button>
      </div>
      
      <Button
        variant="outline"
        @click="toggleAdvancedSearch"
        class="flex items-center space-x-2"
      >
        <Filter class="h-4 w-4" />
        <span>{{ showAdvanced ? '简单搜索' : '高级搜索' }}</span>
      </Button>
      
      <Button
        v-if="hasActiveFilters"
        variant="outline"
        @click="clearAllFilters"
        class="flex items-center space-x-2"
      >
        <RotateCcw class="h-4 w-4" />
        <span>清除筛选</span>
      </Button>
    </div>

    <!-- 高级搜索面板 -->
    <Card v-if="showAdvanced" class="border-dashed">
      <CardHeader class="pb-3">
        <CardTitle class="text-base flex items-center">
          <Settings class="mr-2 h-4 w-4" />
          高级搜索
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 分类筛选 -->
          <div>
            <label class="text-sm font-medium mb-2 block">物料分类</label>
            <Select v-model="filters.categoryId">
              <SelectTrigger>
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分类</SelectItem>
                <SelectItem v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 状态筛选 -->
          <div>
            <label class="text-sm font-medium mb-2 block">物料状态</label>
            <Select v-model="filters.status">
              <SelectTrigger>
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="active">启用</SelectItem>
                <SelectItem value="inactive">停用</SelectItem>
                <SelectItem value="draft">草稿</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 库存状态 -->
          <div>
            <label class="text-sm font-medium mb-2 block">库存状态</label>
            <Select v-model="filters.stockStatus">
              <SelectTrigger>
                <SelectValue placeholder="选择库存状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="in-stock">有库存</SelectItem>
                <SelectItem value="out-of-stock">无库存</SelectItem>
                <SelectItem value="low-stock">库存不足</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 价格范围 -->
          <div>
            <label class="text-sm font-medium mb-2 block">成本价格范围</label>
            <div class="flex items-center space-x-2">
              <Input
                v-model="filters.priceMin"
                type="number"
                placeholder="最低价"
                class="flex-1"
              />
              <span class="text-gray-400">-</span>
              <Input
                v-model="filters.priceMax"
                type="number"
                placeholder="最高价"
                class="flex-1"
              />
            </div>
          </div>

          <!-- 创建时间 -->
          <div>
            <label class="text-sm font-medium mb-2 block">创建时间</label>
            <Select v-model="filters.dateRange">
              <SelectTrigger>
                <SelectValue placeholder="选择时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部时间</SelectItem>
                <SelectItem value="today">今天</SelectItem>
                <SelectItem value="week">本周</SelectItem>
                <SelectItem value="month">本月</SelectItem>
                <SelectItem value="quarter">本季度</SelectItem>
                <SelectItem value="year">本年</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 供应商 -->
          <div>
            <label class="text-sm font-medium mb-2 block">主要供应商</label>
            <Select v-model="filters.supplierId">
              <SelectTrigger>
                <SelectValue placeholder="选择供应商" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部供应商</SelectItem>
                <SelectItem v-for="supplier in suppliers" :key="supplier.id" :value="supplier.id">
                  {{ supplier.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 属性筛选 -->
        <div v-if="availableAttributes.length > 0">
          <label class="text-sm font-medium mb-2 block">物料属性</label>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="attr in availableAttributes" :key="attr.id">
              <label class="text-xs text-gray-600 mb-1 block">{{ attr.name }}</label>
              <Select v-model="filters.attributes[attr.id]">
                <SelectTrigger class="h-8">
                  <SelectValue :placeholder="`选择${attr.name}`" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem v-for="option in attr.options" :key="option" :value="option">
                    {{ option }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <!-- 搜索操作 -->
        <div class="flex items-center justify-between pt-2 border-t">
          <div class="flex items-center space-x-2">
            <input
              id="save-search"
              type="checkbox"
              v-model="saveCurrentSearch"
              class="rounded border-gray-300"
            />
            <label for="save-search" class="text-sm text-gray-600">
              保存当前搜索条件
            </label>
          </div>
          
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              @click="resetAdvancedFilters"
            >
              重置
            </Button>
            <Button
              size="sm"
              @click="applyAdvancedSearch"
            >
              <Search class="mr-2 h-4 w-4" />
              搜索
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 活跃筛选标签 -->
    <div v-if="activeFilterTags.length > 0" class="flex flex-wrap gap-2">
      <Badge
        v-for="tag in activeFilterTags"
        :key="tag.key"
        variant="secondary"
        class="flex items-center space-x-1"
      >
        <span>{{ tag.label }}</span>
        <Button
          variant="ghost"
          size="sm"
          @click="removeFilter(tag.key)"
          class="h-4 w-4 p-0 ml-1"
        >
          <X class="h-3 w-3" />
        </Button>
      </Badge>
    </div>

    <!-- 快速筛选按钮 -->
    <div class="flex flex-wrap gap-2">
      <Button
        v-for="preset in quickFilters"
        :key="preset.id"
        variant="outline"
        size="sm"
        @click="applyQuickFilter(preset)"
        class="flex items-center space-x-1"
      >
        <Component :is="preset.icon" class="h-4 w-4" />
        <span>{{ preset.label }}</span>
      </Button>
    </div>

    <!-- 搜索结果统计 -->
    <div class="flex items-center justify-between text-sm text-gray-600">
      <div>
        找到 <span class="font-medium">{{ totalResults }}</span> 个物料
        <span v-if="searchQuery || hasActiveFilters">
          (已应用筛选条件)
        </span>
      </div>
      
      <div class="flex items-center space-x-4">
        <Select v-model="sortBy">
          <SelectTrigger class="w-40 h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name-asc">名称 ↑</SelectItem>
            <SelectItem value="name-desc">名称 ↓</SelectItem>
            <SelectItem value="created-desc">创建时间 ↓</SelectItem>
            <SelectItem value="created-asc">创建时间 ↑</SelectItem>
            <SelectItem value="updated-desc">更新时间 ↓</SelectItem>
            <SelectItem value="price-asc">价格 ↑</SelectItem>
            <SelectItem value="price-desc">价格 ↓</SelectItem>
          </SelectContent>
        </Select>
        
        <div class="flex items-center space-x-1">
          <Button
            variant="outline"
            size="sm"
            @click="exportSearchResults"
            class="h-8"
          >
            <Download class="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="shareSearchUrl"
            class="h-8"
          >
            <Share class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  X, 
  Filter, 
  Settings, 
  RotateCcw,
  Download,
  Share,
  Package,
  AlertTriangle,
  TrendingDown,
  Calendar,
  Star
} from 'lucide-vue-next';

interface Category {
  id: string;
  name: string;
}

interface Supplier {
  id: string;
  name: string;
}

interface Attribute {
  id: string;
  name: string;
  options: string[];
}

interface FilterTag {
  key: string;
  label: string;
  value: string | { min: string; max: string };
}

interface QuickFilter {
  id: string;
  label: string;
  icon: any; // Vue component
  filters: Partial<SearchFilters> & { sortBy?: string };
}

interface SearchFilters {
  categoryId: string;
  status: string;
  stockStatus: string;
  priceMin: string;
  priceMax: string;
  dateRange: string;
  supplierId: string;
  attributes: Record<string, string>;
}

interface Props {
  totalResults?: number;
  loading?: boolean;
}

interface Emits {
  (e: 'search', params: { query: string; filters: SearchFilters; sortBy: string }): void;
  (e: 'export-results'): void;
  (e: 'share-search', url: string): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

// 搜索状态
const searchQuery = ref('');
const showAdvanced = ref(false);
const saveCurrentSearch = ref(false);
const sortBy = ref('name-asc');

// 筛选器
const filters = ref<SearchFilters>({
  categoryId: '',
  status: '',
  stockStatus: '',
  priceMin: '',
  priceMax: '',
  dateRange: '',
  supplierId: '',
  attributes: {}
});

// 模拟数据
const categories: Category[] = [
  { id: 'CAT_GLASS', name: '浮法玻璃' },
  { id: 'CAT_ALUMINUM', name: '铝型材' },
  { id: 'CAT_HARDWARE', name: '五金配件' }
];

const suppliers: Supplier[] = [
  { id: 'SUP_001', name: '南玻集团' },
  { id: 'SUP_002', name: '信义玻璃' },
  { id: 'SUP_003', name: '华南铝业' }
];

const availableAttributes: Attribute[] = [
  {
    id: 'thickness',
    name: '厚度',
    options: ['3mm', '5mm', '6mm', '8mm', '10mm', '12mm']
  },
  {
    id: 'color',
    name: '颜色',
    options: ['透明', '超白', '茶色', '灰色', '蓝色']
  },
  {
    id: 'surface',
    name: '表面处理',
    options: ['磨砂', '钢化', '夹胶', '中空']
  }
];

// 快速筛选预设
const quickFilters: QuickFilter[] = [
  {
    id: 'out-of-stock',
    label: '缺货物料',
    icon: AlertTriangle,
    filters: { stockStatus: 'out-of-stock' }
  },
  {
    id: 'low-stock',
    label: '库存不足',
    icon: TrendingDown,
    filters: { stockStatus: 'low-stock' }
  },
  {
    id: 'new-materials',
    label: '新增物料',
    icon: Package,
    filters: { dateRange: 'week' }
  },
  {
    id: 'popular',
    label: '热门物料',
    icon: Star,
    filters: { sortBy: 'usage-desc' }
  },
  {
    id: 'recent',
    label: '最近更新',
    icon: Calendar,
    filters: { sortBy: 'updated-desc' }
  }
];

// 计算属性
const hasActiveFilters = computed(() => {
  return filters.value.categoryId !== '' ||
         filters.value.status !== '' ||
         filters.value.stockStatus !== '' ||
         filters.value.priceMin !== '' ||
         filters.value.priceMax !== '' ||
         filters.value.dateRange !== '' ||
         filters.value.supplierId !== '' ||
         Object.values(filters.value.attributes).some(v => v !== '');
});

const activeFilterTags = computed((): FilterTag[] => {
  const tags: FilterTag[] = [];
  
  if (filters.value.categoryId) {
    const category = categories.find(c => c.id === filters.value.categoryId);
    tags.push({
      key: 'categoryId',
      label: `分类: ${category?.name}`,
      value: filters.value.categoryId
    });
  }
  
  if (filters.value.status) {
    const statusLabels = {
      active: '启用',
      inactive: '停用',
      draft: '草稿'
    };
    tags.push({
      key: 'status',
      label: `状态: ${statusLabels[filters.value.status as keyof typeof statusLabels]}`,
      value: filters.value.status
    });
  }
  
  if (filters.value.stockStatus) {
    const stockLabels = {
      'in-stock': '有库存',
      'out-of-stock': '无库存',
      'low-stock': '库存不足'
    };
    tags.push({
      key: 'stockStatus',
      label: `库存: ${stockLabels[filters.value.stockStatus as keyof typeof stockLabels]}`,
      value: filters.value.stockStatus
    });
  }
  
  if (filters.value.priceMin || filters.value.priceMax) {
    const min = filters.value.priceMin || '0';
    const max = filters.value.priceMax || '∞';
    tags.push({
      key: 'priceRange',
      label: `价格: ${min} - ${max}`,
      value: { min: filters.value.priceMin, max: filters.value.priceMax }
    });
  }
  
  if (filters.value.supplierId) {
    const supplier = suppliers.find(s => s.id === filters.value.supplierId);
    tags.push({
      key: 'supplierId',
      label: `供应商: ${supplier?.name}`,
      value: filters.value.supplierId
    });
  }
  
  Object.entries(filters.value.attributes).forEach(([attrId, value]) => {
    if (value) {
      const attr = availableAttributes.find(a => a.id === attrId);
      tags.push({
        key: `attr_${attrId}`,
        label: `${attr?.name}: ${value}`,
        value
      });
    }
  });
  
  return tags;
});

// 搜索处理
const performSearch = () => {
  emit('search', {
    query: searchQuery.value,
    filters: filters.value,
    sortBy: sortBy.value
  });
};

// 监听搜索条件变化
watch([searchQuery, filters, sortBy], () => {
  performSearch();
}, { deep: true });

// 操作方法
const clearSearch = () => {
  searchQuery.value = '';
};

const toggleAdvancedSearch = () => {
  showAdvanced.value = !showAdvanced.value;
};

const clearAllFilters = () => {
  searchQuery.value = '';
  filters.value = {
    categoryId: '',
    status: '',
    stockStatus: '',
    priceMin: '',
    priceMax: '',
    dateRange: '',
    supplierId: '',
    attributes: {}
  };
  sortBy.value = 'name-asc';
};

const resetAdvancedFilters = () => {
  filters.value = {
    categoryId: '',
    status: '',
    stockStatus: '',
    priceMin: '',
    priceMax: '',
    dateRange: '',
    supplierId: '',
    attributes: {}
  };
};

const applyAdvancedSearch = () => {
  performSearch();
  if (saveCurrentSearch.value) {
    console.log('保存搜索条件');
  }
};

const removeFilter = (key: string) => {
  if (key.startsWith('attr_')) {
    const attrId = key.replace('attr_', '');
    filters.value.attributes[attrId] = '';
  } else if (key === 'priceRange') {
    filters.value.priceMin = '';
    filters.value.priceMax = '';
  } else {
    const filterKey = key as keyof SearchFilters;
    if (filterKey !== 'attributes') {
      (filters.value[filterKey] as string) = '';
    }
  }
};

const applyQuickFilter = (preset: QuickFilter) => {
  Object.assign(filters.value, preset.filters);
  if (preset.filters.sortBy) {
    sortBy.value = preset.filters.sortBy;
  }
};

const exportSearchResults = () => {
  emit('export-results');
};

const shareSearchUrl = () => {
  const params = new URLSearchParams();
  
  if (searchQuery.value) {
    params.set('q', searchQuery.value);
  }
  
  Object.entries(filters.value).forEach(([key, value]) => {
    if (value && typeof value === 'string') {
      params.set(key, value);
    }
  });
  
  if (sortBy.value !== 'name-asc') {
    params.set('sort', sortBy.value);
  }
  
  const url = `${window.location.origin}${window.location.pathname}?${params.toString()}`;
  emit('share-search', url);
};
</script>
