<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="!w-[90vw] !max-w-[1200px] !max-h-[90vh] overflow-hidden flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle>批量操作 - {{ operationTitle }}</DialogTitle>
        <DialogDescription>
          {{ operationDescription }}
        </DialogDescription>
      </DialogHeader>

      <!-- 内容滚动区域 -->
      <div class="flex-1 overflow-y-auto">
        <!-- 批量导出 -->
        <div v-if="operation === 'export'" class="space-y-4">
          <div class="space-y-3">
            <Label class="text-sm font-medium">选择要导出的分类</Label>
            <div class="border rounded-lg p-4 max-h-60 overflow-y-auto">
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <input
                    id="select-all"
                    type="checkbox"
                    :checked="isAllSelected"
                    @change="handleSelectAll"
                    class="rounded"
                  />
                  <Label htmlFor="select-all" class="text-sm font-medium">全选</Label>
                </div>
                <div
                  v-for="category in categories"
                  :key="category.categoryId"
                  class="flex items-center space-x-2 pl-4"
                >
                  <input
                    :id="`category-${category.categoryId}`"
                    type="checkbox"
                    :value="category.categoryId"
                    v-model="selectedCategories"
                    class="rounded"
                  />
                  <Label 
                    :htmlFor="`category-${category.categoryId}`" 
                    class="text-sm flex-1 cursor-pointer"
                  >
                    {{ category.categoryName }}
                    <span class="text-xs text-gray-500 ml-2">
                      ({{ category.categoryId }})
                    </span>
                  </Label>
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <Label class="text-sm font-medium">导出格式</Label>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <div class="flex items-center space-x-2">
                <input
                  id="format-json"
                  type="radio"
                  value="json"
                  v-model="exportFormat"
                  class="rounded"
                />
                <Label htmlFor="format-json" class="text-sm">JSON 配置</Label>
              </div>
              <div class="flex items-center space-x-2">
                <input
                  id="format-excel"
                  type="radio"
                  value="excel"
                  v-model="exportFormat"
                  class="rounded"
                />
                <Label htmlFor="format-excel" class="text-sm">Excel 表格</Label>
              </div>
              <div class="flex items-center space-x-2">
                <input
                  id="format-template"
                  type="radio"
                  value="template"
                  v-model="exportFormat"
                  class="rounded"
                />
                <Label htmlFor="format-template" class="text-sm">模板文件</Label>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量删除 -->
        <div v-if="operation === 'delete'" class="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle class="h-4 w-4" />
            <AlertTitle>危险操作警告</AlertTitle>
            <AlertDescription>
              批量删除操作不可逆转，请仔细确认要删除的分类。
            </AlertDescription>
          </Alert>

          <div class="space-y-3">
            <Label class="text-sm font-medium">选择要删除的分类</Label>
            <div class="border rounded-lg p-4 max-h-60 overflow-y-auto">
              <div class="space-y-2">
                <div
                  v-for="category in deletableCategories"
                  :key="category.categoryId"
                  class="flex items-center space-x-2"
                >
                  <input
                    :id="`delete-category-${category.categoryId}`"
                    type="checkbox"
                    :value="category.categoryId"
                    v-model="selectedCategories"
                    class="rounded"
                  />
                  <Label 
                    :htmlFor="`delete-category-${category.categoryId}`" 
                    class="text-sm flex-1 cursor-pointer"
                  >
                    {{ category.categoryName }}
                    <span class="text-xs text-gray-500 ml-2">
                      ({{ category.categoryId }})
                    </span>
                  </Label>
                </div>
              </div>
            </div>
            
            <div v-if="undeletableCategories.length > 0" class="space-y-2">
              <Label class="text-sm font-medium text-gray-500">不能删除的分类（有子分类或物料）</Label>
              <div class="border rounded-lg p-3 bg-gray-50">
                <div class="space-y-1">
                  <div
                    v-for="category in undeletableCategories"
                    :key="category.categoryId"
                    class="text-sm text-gray-600"
                  >
                    {{ category.categoryName }}
                    <span class="text-xs text-gray-400">
                      ({{ category.hasChildren ? '有子分类' : '有关联物料' }})
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量导入 -->
        <div v-if="operation === 'import'" class="space-y-4">
          <div class="space-y-3">
            <Label class="text-sm font-medium">导入方式</Label>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div class="flex items-center space-x-2">
                <input
                  id="import-file"
                  type="radio"
                  value="file"
                  v-model="importMode"
                  class="rounded"
                />
                <Label htmlFor="import-file" class="text-sm">上传文件</Label>
              </div>
              <div class="flex items-center space-x-2">
                <input
                  id="import-template"
                  type="radio"
                  value="template"
                  v-model="importMode"
                  class="rounded"
                />
                <Label htmlFor="import-template" class="text-sm">使用模板</Label>
              </div>
            </div>
          </div>

          <div v-if="importMode === 'file'" class="space-y-3">
            <Label class="text-sm font-medium">选择文件</Label>
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                ref="fileInput"
                type="file"
                accept=".json,.xlsx,.csv"
                @change="handleFileSelect"
                class="hidden"
              />
              <div class="space-y-2">
                <Upload class="h-8 w-8 mx-auto text-gray-400" />
                <div class="text-sm text-gray-600">
                  <Button type="button" variant="link" @click="fileInput?.click()">
                    点击上传文件
                  </Button>
                  或拖拽文件到此处
                </div>
                <p class="text-xs text-gray-500">
                  支持 JSON、Excel、CSV 格式
                </p>
              </div>
            </div>
            
            <div v-if="selectedFile" class="p-3 bg-gray-50 rounded border">
              <div class="flex items-center justify-between">
                <span class="text-sm">{{ selectedFile.name }}</span>
                <Button type="button" variant="ghost" size="sm" @click="clearFile">
                  <X class="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>

          <div v-if="importMode === 'template'" class="space-y-3">
            <Label class="text-sm font-medium">选择预设模板</Label>
            <div class="grid grid-cols-1 gap-2">
              <div
                v-for="template in availableTemplates"
                :key="template.id"
                class="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                @click="selectedTemplate = template.id"
              >
                <input
                  :id="`template-${template.id}`"
                  type="radio"
                  :value="template.id"
                  v-model="selectedTemplate"
                  class="rounded"
                />
                <div class="flex-1">
                  <Label :htmlFor="`template-${template.id}`" class="font-medium text-sm cursor-pointer">
                    {{ template.name }}
                  </Label>
                  <p class="text-xs text-gray-500 mt-1">{{ template.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作预览 -->
        <div v-if="selectedCategories.length > 0 || selectedTemplate" class="border-t pt-4">
          <Label class="text-sm font-medium">操作预览</Label>
          <div class="mt-2 p-3 bg-gray-50 rounded text-sm">
            <div v-if="operation === 'export'">
              将导出 {{ selectedCategories.length }} 个分类为 {{ exportFormat }} 格式
            </div>
            <div v-if="operation === 'delete'" class="text-red-600">
              将删除 {{ selectedCategories.length }} 个分类
            </div>
            <div v-if="operation === 'import' && selectedTemplate">
              将导入模板"{{ availableTemplates.find(t => t.id === selectedTemplate)?.name }}"
            </div>
            <div v-if="operation === 'import' && selectedFile">
              将导入文件"{{ selectedFile.name }}"
            </div>
          </div>
        </div>
      </div>

      <DialogFooter class="flex-shrink-0 border-t pt-4">
        <Button variant="outline" @click="$emit('update:open', false)">
          取消
        </Button>
        <Button 
          @click="handleConfirm" 
          :disabled="!canConfirm"
          :variant="operation === 'delete' ? 'destructive' : 'default'"
        >
          {{ operation === 'delete' ? '确认删除' : '确认操作' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { MaterialCategory, BatchOperationData } from '@/types/material';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle, Upload, X } from 'lucide-vue-next';

interface Props {
  open: boolean;
  operation: 'export' | 'delete' | 'import';
  categories: MaterialCategory[];
}

interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'confirm', data: BatchOperationData): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 状态管理
const selectedCategories = ref<string[]>([]);
const exportFormat = ref<'json' | 'excel' | 'template'>('json');
const importMode = ref<'file' | 'template'>('file');
const selectedFile = ref<File | null>(null);
const selectedTemplate = ref<string>('');
const fileInput = ref<HTMLInputElement>();

// 预设模板
const availableTemplates = ref([
  {
    id: 'glass-industry',
    name: '玻璃行业标准分类',
    description: '包含玻璃、型材、五金等完整分类体系'
  },
  {
    id: 'door-window',
    name: '门窗行业分类',
    description: '门窗制造企业常用的物料分类'
  },
  {
    id: 'curtain-wall',
    name: '幕墙行业分类',
    description: '幕墙工程相关的物料分类体系'
  }
]);

// 计算属性
const operationTitle = computed(() => {
  switch (props.operation) {
    case 'export':
      return '批量导出分类';
    case 'delete':
      return '批量删除分类';
    case 'import':
      return '批量导入分类';
    default:
      return '';
  }
});

const operationDescription = computed(() => {
  switch (props.operation) {
    case 'export':
      return '选择要导出的分类，支持多种格式输出';
    case 'delete':
      return '选择要删除的分类，注意此操作不可逆转';
    case 'import':
      return '导入新的分类配置，可以选择上传文件或使用预设模板';
    default:
      return '';
  }
});

const deletableCategories = computed(() => {
  // 只显示没有子分类的分类（这里简化处理，实际应该检查是否有关联物料）
  return props.categories.filter(category => !category.hasChildren);
});

const undeletableCategories = computed(() => {
  return props.categories.filter(category => category.hasChildren);
});

const isAllSelected = computed(() => {
  return selectedCategories.value.length === props.categories.length;
});

const canConfirm = computed(() => {
  switch (props.operation) {
    case 'export':
      return selectedCategories.value.length > 0;
    case 'delete':
      return selectedCategories.value.length > 0;
    case 'import':
      return (importMode.value === 'file' && selectedFile.value) || 
             (importMode.value === 'template' && selectedTemplate.value);
    default:
      return false;
  }
});

// 事件处理
const handleSelectAll = (event: Event) => {
  const checked = (event.target as HTMLInputElement).checked;
  if (checked) {
    selectedCategories.value = props.categories.map(c => c.categoryId);
  } else {
    selectedCategories.value = [];
  }
};

const handleFileSelect = (event: Event) => {
  const files = (event.target as HTMLInputElement).files;
  if (files && files.length > 0) {
    selectedFile.value = files[0];
  }
};

const clearFile = () => {
  selectedFile.value = null;
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

const handleConfirm = () => {
  let data: BatchOperationData = {};
  
  switch (props.operation) {
    case 'export':
      data = {
        exportData: {
          categoryIds: selectedCategories.value,
          format: exportFormat.value
        }
      };
      break;
    case 'delete':
      data = {
        deleteData: {
          categoryIds: selectedCategories.value
        }
      };
      break;
    case 'import':
      data = {
        importData: {
          mode: importMode.value,
          file: selectedFile.value || undefined,
          templateId: selectedTemplate.value || undefined
        }
      };
      break;
  }
  
  emit('confirm', data);
  emit('update:open', false);
};
</script>