<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center justify-between">
        <div class="flex items-center">
          <Box class="h-5 w-5 mr-2" />
          库存变体
        </div>
        <div class="flex items-center space-x-2">
          <Badge variant="outline" class="text-sm">
            共 {{ filteredVariants.length }} / {{ variants.length }} 个变体
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="outline" size="sm">
                <Settings class="mr-2 h-4 w-4" />
                设置
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>表格设置</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem 
                :checked="showColumns.attributes"
                @update:checked="showColumns.attributes = $event"
              >
                显示变体属性
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem 
                :checked="showColumns.cost"
                @update:checked="showColumns.cost = $event"
              >
                显示成本
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem 
                :checked="showColumns.supplier"
                @update:checked="showColumns.supplier = $event"
              >
                显示供应商
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem 
                :checked="showColumns.leadTime"
                @update:checked="showColumns.leadTime = $event"
              >
                显示交期
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardTitle>
      <CardDescription>
        该物料的所有库存变体规格和库存信息，点击表头可排序
      </CardDescription>
    </CardHeader>
    
    <!-- 搜索和筛选区域 -->
    <div v-if="variants.length > 0" class="px-6 py-4 border-b space-y-4">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input 
              v-model="searchQuery"
              placeholder="搜索SKU、变体名称或属性..."
              class="pl-10"
            />
          </div>
        </div>
        <Select v-model="statusFilter">
          <SelectTrigger class="w-[120px]">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="active">正常</SelectItem>
            <SelectItem value="inactive">停用</SelectItem>
          </SelectContent>
        </Select>
        <Select v-model="stockFilter">
          <SelectTrigger class="w-[140px]">
            <SelectValue placeholder="库存状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="in-stock">有库存</SelectItem>
            <SelectItem value="low-stock">低库存</SelectItem>
            <SelectItem value="out-of-stock">无库存</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="sm" @click="exportVariants">
          <Download class="mr-2 h-4 w-4" />
          导出
        </Button>
      </div>
      
      <!-- 筛选标签 -->
      <div v-if="hasActiveFilters" class="flex items-center space-x-2">
        <span class="text-sm text-gray-500">已应用筛选:</span>
        <div class="flex items-center space-x-2">
          <Badge v-if="searchQuery" variant="secondary" class="text-xs">
            搜索: {{ searchQuery }}
            <button @click="searchQuery = ''" class="ml-1">
              <X class="h-3 w-3" />
            </button>
          </Badge>
          <Badge v-if="statusFilter !== 'all'" variant="secondary" class="text-xs">
            状态: {{ statusFilter === 'active' ? '正常' : '停用' }}
            <button @click="statusFilter = 'all'" class="ml-1">
              <X class="h-3 w-3" />
            </button>
          </Badge>
          <Badge v-if="stockFilter !== 'all'" variant="secondary" class="text-xs">
            库存: {{ getStockFilterLabel(stockFilter) }}
            <button @click="stockFilter = 'all'" class="ml-1">
              <X class="h-3 w-3" />
            </button>
          </Badge>
        </div>
        <Button variant="ghost" size="sm" @click="resetFilters" class="text-xs">
          清除全部
        </Button>
      </div>
    </div>
    <CardContent class="p-0">
      <div v-if="variants.length === 0" class="p-8 text-center">
        <div class="text-gray-400 mb-2">
          <Box class="h-12 w-12 mx-auto mb-4" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无变体数据</h3>
        <p class="text-gray-500">当前物料还没有配置任何库存变体</p>
      </div>
      
      <div v-else>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead class="w-[200px]">
                <Button variant="ghost" @click="toggleSort('sku')" class="h-auto p-0 font-medium">
                  SKU
                  <ArrowUpDown class="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead class="w-[250px]">
                <Button variant="ghost" @click="toggleSort('displayName')" class="h-auto p-0 font-medium">
                  变体名称
                  <ArrowUpDown class="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead v-if="showColumns.attributes" class="w-[200px]">变体属性</TableHead>
              <TableHead class="w-[100px]">
                <Button variant="ghost" @click="toggleSort('stock')" class="h-auto p-0 font-medium">
                  库存
                  <ArrowUpDown class="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead v-if="showColumns.cost" class="w-[100px]">
                <Button variant="ghost" @click="toggleSort('cost')" class="h-auto p-0 font-medium">
                  成本
                  <ArrowUpDown class="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead v-if="showColumns.supplier" class="w-[120px]">供应商</TableHead>
              <TableHead v-if="showColumns.leadTime" class="w-[100px]">交期</TableHead>
              <TableHead class="w-[80px]">状态</TableHead>
              <TableHead class="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow
              v-for="variant in paginatedVariants"
              :key="variant.variantId"
              :class="[
                'hover:bg-gray-50 transition-colors',
                selectedVariantId === variant.variantId ? 'bg-blue-50' : ''
              ]"
              @click="handleSelectVariant(variant.variantId)"
            >
              <TableCell>
                <div class="font-mono text-sm">{{ variant.sku }}</div>
              </TableCell>
              <TableCell>
                <div class="font-medium">{{ variant.displayName }}</div>
                <div class="text-sm text-gray-500">{{ variant.variantId }}</div>
              </TableCell>
              <TableCell v-if="showColumns.attributes">
                <div class="space-y-1">
                  <div
                    v-for="(value, key) in Object.entries(variant.variantAttributes).slice(0, 2)"
                    :key="key"
                    class="text-sm"
                  >
                    <span class="text-gray-500">{{ value[0] }}:</span>
                    <span class="ml-1 font-medium">{{ formatAttributeValue(value[0], value[1]) }}</span>
                  </div>
                  <div v-if="Object.keys(variant.variantAttributes).length > 2" class="text-xs text-gray-400">
                    +{{ Object.keys(variant.variantAttributes).length - 2 }} 项
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div class="text-center">
                  <div 
                    :class="[
                      'text-lg font-semibold',
                      variant.stock > 0 ? 'text-green-600' : 'text-red-600'
                    ]"
                  >
                    {{ variant.stock }}
                  </div>
                  <div class="text-xs text-gray-500">{{ variant.unit }}</div>
                </div>
              </TableCell>
              <TableCell v-if="showColumns.cost">
                <div class="text-right">
                  <div class="font-medium">¥{{ variant.cost.toFixed(2) }}</div>
                  <div class="text-xs text-gray-500">/ {{ variant.unit }}</div>
                </div>
              </TableCell>
              <TableCell v-if="showColumns.supplier">
                <div class="text-sm">{{ variant.supplier }}</div>
              </TableCell>
              <TableCell v-if="showColumns.leadTime">
                <div class="text-center">
                  <div class="font-medium">{{ variant.leadTimeDays }}</div>
                  <div class="text-xs text-gray-500">天</div>
                </div>
              </TableCell>
              <TableCell>
                <Badge :variant="variant.isActive ? 'default' : 'secondary'">
                  {{ variant.isActive ? '正常' : '停用' }}
                </Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="ghost" size="sm" @click.stop>
                      <MoreHorizontal class="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>操作</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem @click.stop="editVariant(variant)">
                      <Edit class="mr-2 h-4 w-4" />
                      编辑变体
                    </DropdownMenuItem>
                    <DropdownMenuItem @click.stop="duplicateVariant(variant)">
                      <Copy class="mr-2 h-4 w-4" />
                      复制变体
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem @click.stop="adjustStock(variant)">
                      <Package class="mr-2 h-4 w-4" />
                      调整库存
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      @click.stop="toggleVariantStatus(variant)"
                      :class="variant.isActive ? 'text-orange-600' : 'text-green-600'"
                    >
                      <Power class="mr-2 h-4 w-4" />
                      {{ variant.isActive ? '停用' : '启用' }}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
        
        <!-- 分页 -->
        <div v-if="totalPages > 1" class="flex items-center justify-between px-6 py-4 border-t">
          <div class="text-sm text-gray-700">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredVariants.length) }} 条，
            共 {{ filteredVariants.length }} 条记录
          </div>
          <div class="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              :disabled="currentPage === 1"
              @click="currentPage--"
            >
              <ChevronLeft class="h-4 w-4" />
              上一页
            </Button>
            <span class="text-sm font-medium">{{ currentPage }} / {{ totalPages }}</span>
            <Button 
              variant="outline" 
              size="sm" 
              :disabled="currentPage === totalPages"
              @click="currentPage++"
            >
              下一页
              <ChevronRight class="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <!-- 汇总信息 -->
        <div class="border-t bg-gray-50 px-6 py-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div class="text-lg font-semibold text-blue-600">{{ totalStock }}</div>
              <div class="text-sm text-gray-600">总库存</div>
            </div>
            <div>
              <div class="text-lg font-semibold text-green-600">¥{{ totalValue.toFixed(2) }}</div>
              <div class="text-sm text-gray-600">总价值</div>
            </div>
            <div>
              <div class="text-lg font-semibold text-purple-600">{{ activeVariants }}</div>
              <div class="text-sm text-gray-600">正常变体</div>
            </div>
            <div>
              <div class="text-lg font-semibold text-orange-600">{{ averageLeadTime.toFixed(1) }}</div>
              <div class="text-sm text-gray-600">平均交期(天)</div>
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { MaterialVariant } from '@/types/material';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuCheckboxItem } from '@/components/ui/dropdown-menu';
import { 
  Box, 
  Settings, 
  Search, 
  Download, 
  X, 
  ArrowUpDown,
  MoreHorizontal,
  Edit,
  Copy,
  Package,
  Power,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next';

interface Props {
  variants: MaterialVariant[];
  selectedVariantId?: string | null;
}

interface Emits {
  (e: 'variant-selected', variantId: string | null): void;
  (e: 'variant-updated', variant: MaterialVariant): void;
  (e: 'variant-duplicated', variant: any): void;
  (e: 'stock-adjusted', variantId: string, adjustment: number): void;
  (e: 'variant-status-changed', variantId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 搜索和筛选状态
const searchQuery = ref('');
const statusFilter = ref('all');
const stockFilter = ref('all');
const sortBy = ref('displayName');
const sortOrder = ref<'asc' | 'desc'>('asc');

// 分页状态
const currentPage = ref(1);
const pageSize = ref(10);

// 列显示控制
const showColumns = ref({
  attributes: true,
  cost: true,
  supplier: true,
  leadTime: true
});

// 格式化属性值（主要用于处理数值和单位）
const formatAttributeValue = (key: string, value: any): string => {
  if (typeof value === 'number') {
    // 对于尺寸类属性，添加单位
    if (key.includes('宽度') || key.includes('高度') || key.includes('长度')) {
      return `${value}mm`;
    }
    // 对于重量属性，保留小数位
    if (key.includes('重量')) {
      return `${value.toFixed(2)}kg`;
    }
    // 对于面积属性
    if (key.includes('面积')) {
      return `${value.toFixed(2)}m²`;
    }
  }
  return String(value);
};

// 计算汇总信息
const totalStock = computed(() => {
  return props.variants.reduce((total, variant) => total + variant.stock, 0);
});

const totalValue = computed(() => {
  return props.variants.reduce((total, variant) => total + (variant.stock * variant.cost), 0);
});

const activeVariants = computed(() => {
  return props.variants.filter(variant => variant.isActive).length;
});

const averageLeadTime = computed(() => {
  if (props.variants.length === 0) return 0;
  const totalLeadTime = props.variants.reduce((total, variant) => total + variant.leadTimeDays, 0);
  return totalLeadTime / props.variants.length;
});

// 筛选后的变体数据
const filteredVariants = computed(() => {
  let filtered = [...props.variants];

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(variant => 
      variant.sku.toLowerCase().includes(query) ||
      variant.displayName.toLowerCase().includes(query) ||
      Object.values(variant.variantAttributes).some(attr => 
        String(attr).toLowerCase().includes(query)
      )
    );
  }

  // 状态过滤
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(variant => {
      return statusFilter.value === 'active' ? variant.isActive : !variant.isActive;
    });
  }

  // 库存状态过滤
  if (stockFilter.value !== 'all') {
    filtered = filtered.filter(variant => {
      switch (stockFilter.value) {
        case 'in-stock':
          return variant.stock > 10; // 假设10为安全库存
        case 'low-stock':
          return variant.stock > 0 && variant.stock <= 10;
        case 'out-of-stock':
          return variant.stock === 0;
        default:
          return true;
      }
    });
  }

  // 排序
  filtered.sort((a, b) => {
    let aValue: any, bValue: any;
    
    switch (sortBy.value) {
      case 'sku':
        aValue = a.sku;
        bValue = b.sku;
        break;
      case 'displayName':
        aValue = a.displayName;
        bValue = b.displayName;
        break;
      case 'stock':
        aValue = a.stock;
        bValue = b.stock;
        break;
      case 'cost':
        aValue = a.cost;
        bValue = b.cost;
        break;
      default:
        return 0;
    }

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    return sortOrder.value === 'asc' ? comparison : -comparison;
  });

  return filtered;
});

// 分页数据
const totalPages = computed(() => Math.ceil(filteredVariants.value.length / pageSize.value));

const paginatedVariants = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredVariants.value.slice(start, end);
});

// 是否有激活的筛选
const hasActiveFilters = computed(() => {
  return searchQuery.value || 
         statusFilter.value !== 'all' || 
         stockFilter.value !== 'all';
});

// 方法
const handleSelectVariant = (variantId: string) => {
  if (props.selectedVariantId === variantId) {
    emit('variant-selected', null);
  } else {
    emit('variant-selected', variantId);
  }
};

const toggleSort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = field;
    sortOrder.value = 'asc';
  }
};

const resetFilters = () => {
  searchQuery.value = '';
  statusFilter.value = 'all';
  stockFilter.value = 'all';
  currentPage.value = 1;
};

const getStockFilterLabel = (filter: string) => {
  const labels: Record<string, string> = {
    'in-stock': '有库存',
    'low-stock': '低库存',
    'out-of-stock': '无库存'
  };
  return labels[filter] || filter;
};

// 操作方法
const exportVariants = () => {
  console.log('导出变体数据');
  // TODO: 实现导出功能
};

const editVariant = (variant: MaterialVariant) => {
  console.log('编辑变体:', variant.variantId);
  // TODO: 实现编辑功能
};

const duplicateVariant = (variant: MaterialVariant) => {
  const duplicated = {
    ...variant,
    variantId: `${variant.variantId}_copy`,
    sku: `${variant.sku}_COPY`,
    displayName: `${variant.displayName} (副本)`,
    stock: 0
  };
  emit('variant-duplicated', duplicated);
  console.log('复制变体:', duplicated);
};

const adjustStock = (variant: MaterialVariant) => {
  const adjustment = prompt(`当前库存: ${variant.stock}\n请输入调整数量（正数为入库，负数为出库）:`);
  if (adjustment !== null) {
    const adjustmentNum = parseInt(adjustment);
    if (!isNaN(adjustmentNum)) {
      emit('stock-adjusted', variant.variantId, adjustmentNum);
      console.log('调整库存:', variant.variantId, adjustmentNum);
    }
  }
};

const toggleVariantStatus = (variant: MaterialVariant) => {
  emit('variant-status-changed', variant.variantId);
  console.log('切换变体状态:', variant.variantId);
};
</script>