<template>
  <Card class="h-fit">
    <CardHeader>
      <CardTitle class="flex items-center justify-between">
        <div class="flex items-center">
          <FolderTree class="h-5 w-5 mr-2" />
          物料分类树
        </div>
        <div class="flex items-center space-x-2">
          <!-- 操作下拉菜单 -->
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="outline" size="sm">
                <Settings class="mr-2 h-4 w-4" />
                操作
                <ChevronDown class="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>分类操作</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem @click="showCreateDialog = true">
                <Plus class="mr-2 h-4 w-4" />
                新增分类
              </DropdownMenuItem>
              <DropdownMenuItem @click="showBatchDialog = true">
                <FileUp class="mr-2 h-4 w-4" />
                批量导入
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem @click="exportCategories">
                <Download class="mr-2 h-4 w-4" />
                导出分类
              </DropdownMenuItem>
              <DropdownMenuItem @click="refreshCategories">
                <RefreshCw class="mr-2 h-4 w-4" />
                刷新数据
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button 
            variant="ghost" 
            size="sm" 
            @click="handleExpandAll"
            class="text-xs"
          >
            <Plus class="h-3 w-3 mr-1" />
            {{ isAllExpanded ? '全部折叠' : '全部展开' }}
          </Button>
        </div>
      </CardTitle>
      <CardDescription>
        树形结构展示物料分类层次关系，右键点击分类可进行更多操作
      </CardDescription>
    </CardHeader>
    <CardContent class="p-0">
      <div v-if="categoryTree.length === 0" class="p-8 text-center">
        <div class="text-gray-400 mb-2">
          <FolderTree class="h-12 w-12 mx-auto mb-4" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无分类数据</h3>
        <p class="text-gray-500 mb-4">当前系统中还没有配置任何物料分类</p>
        <Button @click="showCreateDialog = true">
          <Plus class="mr-2 h-4 w-4" />
          创建首个分类
        </Button>
      </div>
      
      <div v-else class="max-h-[60vh] overflow-y-auto p-2">
        <CategoryTreeNode
          v-for="node in localTree"
          :key="node.categoryId"
          :node="node"
          :selected-category-id="selectedCategoryId"
          :materials="materials"
          @node-selected="handleCategorySelected"
          @node-toggle="handleNodeToggle"
          @node-context-menu="handleCategoryContextMenu"
        />
      </div>
      
      <!-- 选中分类的属性预览 -->
      <div v-if="selectedCategory && selectedCategory.attributeSchema" class="border-t bg-gray-50 p-3">
        <div class="flex items-center justify-between mb-2">
          <div class="text-xs font-medium text-gray-700">
            {{ selectedCategory.categoryName }} - 属性模板
          </div>
          <Button variant="ghost" size="sm" @click="editSelectedCategory" class="text-xs h-6">
            <Edit class="h-3 w-3 mr-1" />
            编辑
          </Button>
        </div>
        <div class="flex flex-wrap gap-1">
          <div
            v-for="attr in selectedCategory.attributeSchema.baseAttributes.slice(0, 4)"
            :key="attr.name"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-white border text-gray-700"
          >
            {{ attr.name }}
          </div>
          <div
            v-if="selectedCategory.attributeSchema.baseAttributes.length > 4"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-white border text-gray-500"
          >
            +{{ selectedCategory.attributeSchema.baseAttributes.length - 4 }}
          </div>
        </div>
      </div>
    </CardContent>

    <!-- 分类创建/编辑对话框 -->
    <Dialog v-model:open="showCreateDialog">
      <DialogContent class="!w-[90vw] !max-w-[1200px] !max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader class="flex-shrink-0">
          <DialogTitle class="flex items-center">
            <Plus class="mr-2 h-5 w-5" />
            {{ editingCategory ? '编辑分类' : '新增分类' }}
          </DialogTitle>
          <DialogDescription>
            {{ editingCategory ? '修改物料分类的基本信息和属性配置' : '创建新的物料分类，配置基础信息和属性模板' }}
          </DialogDescription>
        </DialogHeader>
        
        <!-- 内容滚动区域 -->
        <div class="flex-1 overflow-y-auto">
          <div class="space-y-4 py-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <label class="text-sm font-medium">分类名称 *</label>
                <Input 
                  v-model="categoryForm.categoryName"
                  placeholder="请输入分类名称"
                  class="mt-2"
                />
              </div>
              <div>
                <label class="text-sm font-medium">分类代码 *</label>
                <Input 
                  v-model="categoryForm.categoryId"
                  placeholder="请输入分类代码 (例如 CAT_...)"
                  class="mt-2"
                  :disabled="!!editingCategory"
                />
              </div>
            </div>
            <div>
              <label class="text-sm font-medium">父级分类</label>
              <Select v-model="categoryForm.parentId">
                <SelectTrigger class="mt-2">
                  <SelectValue placeholder="选择父级分类（可选，默认为根级）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="null">无父级分类 (根级)</SelectItem>
                  <SelectItem 
                    v-for="category in flatCategories"
                    :key="category.value"
                    :value="category.value"
                    :disabled="category.disabled"
                  >
                    {{ category.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label class="text-sm font-medium">分类描述</label>
              <Textarea
                v-model="categoryForm.description"
                placeholder="请输入分类描述（可选）"
                class="mt-2"
                rows="3"
              />
            </div>
          </div>
        </div>
        
        <DialogFooter class="flex-shrink-0 border-t pt-4">
          <Button variant="outline" @click="cancelCreateDialog">取消</Button>
          <Button @click="saveCategory" :disabled="!categoryForm.categoryName || !categoryForm.categoryId">
            {{ editingCategory ? '更新' : '创建' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 批量导入对话框 -->
    <Dialog :open="showBatchDialog" @update:open="showBatchDialog = $event">
      <DialogContent class="sm:max-w-[500px] !max-h-[90vh]">
        <DialogHeader>
          <DialogTitle class="flex items-center">
            <FileUp class="mr-2 h-5 w-5" />
            批量导入分类
          </DialogTitle>
          <DialogDescription>
            支持 Excel 或 CSV 格式文件，请确保文件格式正确
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4 py-4">
          <div>
            <label class="text-sm font-medium">选择文件</label>
            <div class="mt-2 border-2 border-dashed border-gray-200 rounded-lg p-6 text-center hover:border-gray-300 transition-colors">
              <FileUp class="mx-auto h-12 w-12 text-gray-400" />
              <div class="mt-4">
                <Button variant="outline" size="sm">
                  选择文件
                  <input type="file" class="hidden" accept=".xlsx,.xls,.csv" />
                </Button>
                <p class="mt-2 text-xs text-gray-500">支持 .xlsx, .xls, .csv 格式</p>
              </div>
            </div>
          </div>
          <Alert>
            <AlertCircle class="h-4 w-4" />
            <AlertTitle>导入说明</AlertTitle>
            <AlertDescription class="text-sm">
              请确保Excel文件包含：分类名称、分类代码、父级分类等必要字段。
              <Button variant="link" class="p-0 h-auto text-xs" @click="downloadTemplate">
                下载模板文件
              </Button>
            </AlertDescription>
          </Alert>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="cancelBatchDialog">取消</Button>
          <Button @click="startImport">开始导入</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 右键菜单对话框 -->
    <Dialog :open="showContextMenu" @update:open="showContextMenu = $event">
      <DialogContent class="sm:max-w-[400px] !max-h-[90vh]">
        <DialogHeader>
          <DialogTitle class="flex items-center">
            <Settings class="mr-2 h-5 w-5" />
            分类操作
          </DialogTitle>
          <DialogDescription>
            对 "{{ contextMenuCategory?.categoryName }}" 进行操作
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-2 py-4">
          <Button 
            variant="ghost" 
            class="w-full justify-start" 
            @click="editCategory(contextMenuCategory)"
          >
            <Edit class="mr-2 h-4 w-4" />
            编辑分类
          </Button>
          <Button 
            variant="ghost" 
            class="w-full justify-start" 
            @click="createSubCategory(contextMenuCategory)"
          >
            <Plus class="mr-2 h-4 w-4" />
            添加子分类
          </Button>
          <Button 
            variant="ghost" 
            class="w-full justify-start" 
            @click="duplicateCategory(contextMenuCategory)"
          >
            <Copy class="mr-2 h-4 w-4" />
            复制分类
          </Button>
          <Button 
            variant="ghost" 
            class="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50" 
            @click="deleteCategory(contextMenuCategory)"
          >
            <Trash2 class="mr-2 h-4 w-4" />
            删除分类
          </Button>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="cancelContextMenu">关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </Card>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { MaterialCategoryTreeNode, MaterialCategory, Material } from '@/types/material';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  FolderTree, 
  Plus, 
  Settings, 
  ChevronDown, 
  FileUp, 
  Download, 
  RefreshCw, 
  Edit, 
  Copy, 
  Trash2,
  AlertCircle
} from 'lucide-vue-next';
import CategoryTreeNode from './CategoryTreeNode.vue';

interface Props {
  categoryTree: MaterialCategoryTreeNode[];
  selectedCategoryId: string | null;
  materials: Material[];
}

interface Emits {
  (e: 'category-selected', categoryId: string | null): void;
  (e: 'update:categoryTree', tree: MaterialCategoryTreeNode[]): void;
  (e: 'category-created', category: any): void;
  (e: 'category-updated', category: any): void;
  (e: 'category-deleted', categoryId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const localTree = ref<MaterialCategoryTreeNode[]>([]);

watch(() => props.categoryTree, (newTree) => {
  localTree.value = JSON.parse(JSON.stringify(newTree));
}, { immediate: true, deep: true });

const selectedCategory = computed(() => {
  if (!props.selectedCategoryId) return null;
  return findNodeById(localTree.value, props.selectedCategoryId);
});

// 对话框状态
const showCreateDialog = ref(false);
const showBatchDialog = ref(false);
const showContextMenu = ref(false);
const editingCategory = ref<MaterialCategoryTreeNode | null>(null);
const contextMenuCategory = ref<MaterialCategoryTreeNode | null>(null);

// 分类表单
const categoryForm = ref({
  categoryName: '',
  categoryId: '',
  parentId: null as string | null,
  description: ''
});

// 重置表单
const resetForm = () => {
  categoryForm.value = {
    categoryName: '',
    categoryId: '',
    parentId: null,
    description: ''
  };
  editingCategory.value = null;
};

// 扁平化分类列表（用于父级分类选择）
const flatCategories = computed(() => {
  const flatten = (nodes: MaterialCategoryTreeNode[], level = 0): any[] => {
    let result: any[] = [];
    for (const node of nodes) {
      result.push({
        value: node.categoryId,
        label: `${'—'.repeat(level)} ${node.categoryName}`,
        disabled: editingCategory.value?.categoryId === node.categoryId
      });
      if (node.children) {
        result = result.concat(flatten(node.children, level + 1));
      }
    }
    return result;
  };
  return flatten(localTree.value);
});

const isAllExpanded = computed(() => {
  const checkExpanded = (nodes: MaterialCategoryTreeNode[]): boolean => {
    return nodes.every(node => {
      if (!node.children || node.children.length === 0) return true;
      if (!node.expanded) return false;
      return checkExpanded(node.children);
    });
  };
  return checkExpanded(localTree.value);
});

// --- Tree Traversal Functions ---
const traverseTree = (nodes: MaterialCategoryTreeNode[], callback: (node: MaterialCategoryTreeNode) => void) => {
  for (const node of nodes) {
    callback(node);
    if (node.children) {
      traverseTree(node.children, callback);
    }
  }
};

const findNodeById = (nodes: MaterialCategoryTreeNode[], id: string): MaterialCategoryTreeNode | null => {
  for (const node of nodes) {
    if (node.categoryId === id) return node;
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// --- Event Handlers ---
const handleCategorySelected = (node: MaterialCategoryTreeNode) => {
  emit('category-selected', node.categoryId);
};

const handleNodeToggle = (node: MaterialCategoryTreeNode) => {
  const targetNode = findNodeById(localTree.value, node.categoryId);
  if (targetNode) {
    targetNode.expanded = !targetNode.expanded;
    emit('update:categoryTree', localTree.value);
  }
};

const handleExpandAll = () => {
  const expand = !isAllExpanded.value;
  traverseTree(localTree.value, node => {
    if (node.children && node.children.length > 0) {
      node.expanded = expand;
    }
  });
  emit('update:categoryTree', localTree.value);
};

const handleCategoryContextMenu = (event: { e: MouseEvent, node: MaterialCategoryTreeNode }) => {
  event.e.preventDefault();
  contextMenuCategory.value = event.node;
  showContextMenu.value = true;
};

// --- CRUD Operations ---
const editSelectedCategory = () => {
  if (selectedCategory.value) {
    editCategory(selectedCategory.value);
  }
};

const editCategory = (category: MaterialCategoryTreeNode | null) => {
  if (!category) return;
  
  editingCategory.value = category;
  categoryForm.value = {
    categoryName: category.categoryName,
    categoryId: category.categoryId,
    parentId: category.parentId || null,
    description: category.description || ''
  };
  showCreateDialog.value = true;
  cancelContextMenu();
};

const createSubCategory = (parentCategory: MaterialCategoryTreeNode | null) => {
  resetForm();
  if (parentCategory) {
    categoryForm.value.parentId = parentCategory.categoryId;
  }
  showCreateDialog.value = true;
  showContextMenu.value = false;
};

const duplicateCategory = (category: MaterialCategoryTreeNode | null) => {
  if (!category) return;
  
  resetForm();
  categoryForm.value = {
    categoryName: `${category.categoryName} (副本)`,
    categoryId: ``, // 让用户输入新的ID
    parentId: category.parentId,
    description: category.description || ''
  };
  showCreateDialog.value = true;
  showContextMenu.value = false;
};

const deleteCategory = (category: MaterialCategoryTreeNode | null) => {
  if (!category) return;
  
  if (confirm(`确定要删除分类 "${category.categoryName}" 吗？此操作不可撤销。`)) {
    emit('category-deleted', category.categoryId);
  }
  showContextMenu.value = false;
};

const saveCategory = () => {
  if (!categoryForm.value.categoryName || !categoryForm.value.categoryId) {
    alert('分类名称和分类代码不能为空');
    return;
  }
  
  const categoryData = { ...categoryForm.value };
  
  if (editingCategory.value) {
    emit('category-updated', categoryData);
  } else {
    emit('category-created', categoryData);
  }
  
  cancelCreateDialog();
};

// --- Dialog Control ---
const cancelCreateDialog = () => {
  showCreateDialog.value = false;
  resetForm();
};

const cancelBatchDialog = () => { showBatchDialog.value = false; };
const cancelContextMenu = () => { showContextMenu.value = false; contextMenuCategory.value = null; };
const startImport = () => { console.log('开始导入分类'); cancelBatchDialog(); };
const exportCategories = () => { console.log('导出分类数据'); };
const refreshCategories = () => { console.log('刷新分类数据'); };
const downloadTemplate = () => { console.log('下载导入模板'); };

</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>