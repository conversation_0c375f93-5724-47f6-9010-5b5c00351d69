<template>
  <div class="space-y-4">
    <div v-if="!schema || schema.length === 0" class="text-center py-8">
      <div class="text-gray-400 mb-2">
        <Settings class="h-8 w-8 mx-auto mb-2" />
      </div>
      <p class="text-sm text-gray-500">该分类暂无属性模板</p>
    </div>
    
    <div v-else class="space-y-4">
      <div 
        v-for="attribute in schema" 
        :key="attribute.name"
        class="space-y-2"
      >
        <div class="flex items-center justify-between">
          <label class="text-sm font-medium flex items-center">
            {{ attribute.name }}
            <span v-if="attribute.required" class="text-red-500 ml-1">*</span>
            <span v-if="attribute.unit" class="text-gray-500 ml-2 text-xs">({{ attribute.unit }})</span>
          </label>
          <Badge 
            v-if="attribute.type" 
            variant="outline" 
            class="text-xs"
          >
            {{ getTypeLabel(attribute.type) }}
          </Badge>
        </div>
        
        <!-- 选择类型属性 -->
        <Select 
          v-if="attribute.type === 'select'" 
          :model-value="String(modelValue[attribute.name] || '')" 
          @update:model-value="updateValue(attribute.name, $event)"
        >
          <SelectTrigger 
            :class="{ 'border-red-500': errors[attribute.name] }"
          >
            <SelectValue :placeholder="`请选择${attribute.name}`" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem 
              v-for="option in attribute.options" 
              :key="option" 
              :value="option"
            >
              {{ option }}
            </SelectItem>
          </SelectContent>
        </Select>
        
        <!-- 数值类型属性 -->
        <Input
          v-else-if="attribute.type === 'number'"
          type="number"
          :model-value="modelValue[attribute.name]"
          @update:model-value="updateValue(attribute.name, $event)"
          :placeholder="attribute.placeholder || `请输入${attribute.name}`"
          :min="attribute.minValue"
          :max="attribute.maxValue"
          :step="0.01"
          :class="{ 'border-red-500': errors[attribute.name] }"
        />
        
        <!-- 文本类型属性 -->
        <Input
          v-else-if="attribute.type === 'text'"
          type="text"
          :model-value="modelValue[attribute.name]"
          @update:model-value="updateValue(attribute.name, $event)"
          :placeholder="attribute.placeholder || `请输入${attribute.name}`"
          :pattern="attribute.validationPattern"
          :class="{ 'border-red-500': errors[attribute.name] }"
        />
        
        <!-- 错误提示 -->
        <p v-if="errors[attribute.name]" class="text-sm text-red-500">
          {{ errors[attribute.name] }}
        </p>
        
        <!-- 属性描述 -->
        <p v-if="attribute.description" class="text-xs text-gray-500">
          {{ attribute.description }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AttributeDefinition } from '@/types/material';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings } from 'lucide-vue-next';

interface Props {
  schema: AttributeDefinition[];
  modelValue: Record<string, string | number>;
  errors?: Record<string, string>;
  compact?: boolean;
}

interface Emits {
  (e: 'update:modelValue', values: Record<string, string | number>): void;
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({}),
  compact: false
});

const emit = defineEmits<Emits>();

 
const updateValue = (name: string, value: any) => {
  const newValues = { ...props.modelValue };
  
  // 类型转换
  if (value === '' || value === null || value === undefined) {
    delete newValues[name];
  } else {
    const attribute = props.schema.find(attr => attr.name === name);
    if (attribute?.type === 'number') {
      newValues[name] = typeof value === 'number' ? value : parseFloat(value as string) || 0;
    } else {
      newValues[name] = String(value);
    }
  }
  
  emit('update:modelValue', newValues);
};

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    'select': '选择',
    'number': '数值',
    'text': '文本'
  };
  return labels[type] || type;
};
</script>
