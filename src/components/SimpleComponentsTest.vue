<template>
  <div class="p-6 space-y-6">
    <h1 class="text-2xl font-bold">Simple Components Test (Without Form Validation)</h1>
    
    <!-- Button Component Test -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Button Components</h2>
      <div class="flex gap-2">
        <Button>Default But<PERSON></Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="destructive">Destructive</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost">Ghost</Button>
      </div>
    </div>

    <!-- Card Component Test -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Card Component</h2>
      <Card class="w-96">
        <CardHeader>
          <CardTitle>Card Title</CardTitle>
          <CardDescription>This is a card description</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Card content goes here</p>
        </CardContent>
        <CardFooter>
          <Button>Action</Button>
        </CardFooter>
      </Card>
    </div>

    <!-- Simple Form Components (without validation) -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Simple Form Components</h2>
      <div class="w-96 space-y-4">
        <div class="space-y-2">
          <Label for="simple-input">Input Field</Label>
          <Input id="simple-input" v-model="inputValue" placeholder="Enter text..." />
        </div>

        <div class="space-y-2">
          <Label for="simple-select">Select Field</Label>
          <Select v-model="selectValue">
            <SelectTrigger id="simple-select">
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="option1">Option 1</SelectItem>
              <SelectItem value="option2">Option 2</SelectItem>
              <SelectItem value="option3">Option 3</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div class="space-y-2">
          <Label for="simple-textarea">Textarea Field</Label>
          <Textarea id="simple-textarea" v-model="textareaValue" placeholder="Enter description..." />
        </div>

        <Button @click="handleSubmit">Submit</Button>
      </div>
    </div>

    <!-- Table Component Test -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Table Component</h2>
      <Table>
        <TableCaption>A list of sample data</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Amount</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>Item 1</TableCell>
            <TableCell>Active</TableCell>
            <TableCell>$100.00</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Item 2</TableCell>
            <TableCell>Inactive</TableCell>
            <TableCell>$200.00</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

const inputValue = ref('')
const selectValue = ref('')
const textareaValue = ref('')

function handleSubmit() {
  console.log('Simple form submitted:', {
    input: inputValue.value,
    select: selectValue.value,
    textarea: textareaValue.value,
  })
}
</script>