<template>
  <div class="user-guide-system">
    <!-- 引导遮罩层 -->
    <div 
      v-if="isGuideActive && currentStep"
      class="fixed inset-0 z-50 pointer-events-none"
    >
      <!-- 半透明背景 -->
      <div class="absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300"></div>
      
      <!-- 高亮区域 -->
      <div 
        class="absolute border-4 border-blue-500 rounded-lg shadow-lg transition-all duration-500"
        :style="highlightStyle"
      ></div>
      
      <!-- 引导提示卡片 -->
      <div 
        class="absolute bg-white rounded-lg shadow-xl p-6 max-w-sm pointer-events-auto transition-all duration-500"
        :style="tooltipStyle"
      >
        <!-- 卡片头部 -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <component :is="currentStep.icon" class="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">{{ currentStep.title }}</h3>
              <p class="text-xs text-gray-500">步骤 {{ currentStepIndex + 1 }} / {{ guideSteps.length }}</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" @click="closeGuide">
            <X class="h-4 w-4" />
          </Button>
        </div>
        
        <!-- 卡片内容 -->
        <div class="mb-4">
          <p class="text-sm text-gray-700 mb-3">{{ currentStep.description }}</p>
          
          <!-- 操作提示 -->
          <div v-if="currentStep.actions && currentStep.actions.length > 0" class="space-y-2">
            <p class="text-xs font-medium text-gray-600 mb-2">操作步骤：</p>
            <ul class="space-y-1">
              <li 
                v-for="(action, index) in currentStep.actions" 
                :key="index"
                class="flex items-center gap-2 text-xs text-gray-600"
              >
                <div class="w-4 h-4 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span class="text-xs font-medium text-blue-600">{{ index + 1 }}</span>
                </div>
                {{ action }}
              </li>
            </ul>
          </div>
          
          <!-- 业务价值说明 -->
          <div v-if="currentStep.businessValue" class="mt-3 p-3 bg-blue-50 rounded-lg">
            <div class="flex items-center gap-2 mb-1">
              <Lightbulb class="h-4 w-4 text-blue-600" />
              <span class="text-xs font-medium text-blue-900">业务价值</span>
            </div>
            <p class="text-xs text-blue-700">{{ currentStep.businessValue }}</p>
          </div>
        </div>
        
        <!-- 卡片底部按钮 -->
        <div class="flex items-center justify-between">
          <Button 
            variant="outline" 
            size="sm" 
            @click="previousStep"
            :disabled="currentStepIndex === 0"
          >
            <ChevronLeft class="h-4 w-4 mr-1" />
            上一步
          </Button>
          
          <div class="flex items-center gap-2">
            <Button variant="ghost" size="sm" @click="skipGuide">
              跳过引导
            </Button>
            <Button 
              size="sm" 
              @click="nextStep"
              :disabled="currentStepIndex === guideSteps.length - 1"
            >
              {{ currentStepIndex === guideSteps.length - 1 ? '完成' : '下一步' }}
              <ChevronRight class="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 帮助按钮（固定位置） -->
    <div 
      v-if="showHelpButton && !isGuideActive"
      class="fixed bottom-6 right-6 z-40"
    >
      <Button 
        class="rounded-full w-12 h-12 shadow-lg"
        @click="startGuide"
        :title="helpButtonTitle"
      >
        <HelpCircle class="h-5 w-5" />
      </Button>
    </div>

    <!-- 工具提示组件 -->
    <div 
      v-if="showTooltip && tooltipData"
      class="fixed z-30 bg-gray-900 text-white text-xs rounded-lg px-3 py-2 max-w-xs pointer-events-none"
      :style="tooltipPosition"
    >
      <div class="font-medium mb-1">{{ tooltipData.title }}</div>
      <div class="text-gray-300">{{ tooltipData.description }}</div>
      <!-- 工具提示箭头 -->
      <div 
        class="absolute w-2 h-2 bg-gray-900 transform rotate-45"
        :style="tooltipArrowStyle"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { Button } from '@/components/ui/button';
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  HelpCircle, 
  Lightbulb,
  ClipboardList,
  Scissors,
  Calendar,
  Play,
  MousePointer,
  Eye,
  Download
} from 'lucide-vue-next';

interface GuideStep {
  id: string;
  title: string;
  description: string;
  target: string; // CSS选择器
  icon: any;
  actions?: string[];
  businessValue?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

interface TooltipData {
  title: string;
  description: string;
}

interface Props {
  steps?: GuideStep[];
  autoStart?: boolean;
  showHelpButton?: boolean;
  helpButtonTitle?: string;
}

const props = withDefaults(defineProps<Props>(), {
  steps: () => [],
  autoStart: false,
  showHelpButton: true,
  helpButtonTitle: '查看操作指南'
});

// 响应式状态
const isGuideActive = ref(false);
const currentStepIndex = ref(0);
const showTooltip = ref(false);
const tooltipData = ref<TooltipData | null>(null);
const tooltipPosition = ref({ left: '0px', top: '0px' });
const tooltipArrowStyle = ref({});

// 默认引导步骤（生产发布工作台）
const defaultGuideSteps: GuideStep[] = [
  {
    id: 'step-progress',
    title: '生产发布流程',
    description: '这是生产发布的四个关键步骤，每个步骤都有特定的业务目标和操作要求。',
    target: '.release-steps, [class*="step"]',
    icon: ClipboardList,
    actions: [
      '点击任意步骤圆圈可以直接跳转',
      '绿色表示已完成，蓝色表示当前步骤',
      '灰色表示待执行的步骤'
    ],
    businessValue: '标准化的发布流程确保生产计划的准确性和可执行性，降低生产风险。',
    position: 'bottom'
  },
  {
    id: 'batch-details',
    title: '批次详情管理',
    description: '工单会自动按照产品特征、工艺兼容性等因素进行智能分批，提高生产效率。',
    target: '[class*="batch"], .batch-header, .expand-button',
    icon: Eye,
    actions: [
      '点击展开按钮查看批次内的具体产品',
      '批次颜色代表不同的生产组合',
      '查看批次优势和优化建议'
    ],
    businessValue: '智能批次管理可以提高材料利用率15-25%，减少设备调整时间30%以上。',
    position: 'right'
  },
  {
    id: 'cutting-optimization',
    title: '切割优化',
    description: '与第三方切割优化系统集成，获取最优的原片切割方案，最大化材料利用率。',
    target: '[class*="cutting"], .optimization-result',
    icon: Scissors,
    actions: [
      '导出工单数据到切割优化系统',
      '等待系统计算最优切割方案',
      '导入优化结果并查看布局图'
    ],
    businessValue: '切割优化可以将材料利用率提升到90%以上，每月节约原料成本数万元。',
    position: 'left'
  },
  {
    id: 'production-scheduling',
    title: '生产排程',
    description: '基于切割优化结果，自动生成详细的生产执行计划和甘特图。',
    target: '[class*="scheduling"], .gantt-chart',
    icon: Calendar,
    actions: [
      '查看自动生成的甘特图',
      '调整工序时间和设备分配',
      '确认生产计划可行性'
    ],
    businessValue: '精确的生产排程可以提高设备利用率20%，确保按时交付率达到95%以上。',
    position: 'top'
  },
  {
    id: 'execution-decision',
    title: '决策与执行',
    description: '最终确认生产计划，发布到各个车间，开始实际生产执行。',
    target: '[class*="execution"], .workshop-release',
    icon: Play,
    actions: [
      '确认最终的生产计划参数',
      '发布工单到相关车间',
      '监控生产执行状态'
    ],
    businessValue: '标准化的发布流程确保车间能够准确理解和执行生产计划。',
    position: 'top'
  }
];

// 计算属性
const guideSteps = computed(() => {
  return props.steps.length > 0 ? props.steps : defaultGuideSteps;
});

const currentStep = computed(() => {
  return guideSteps.value[currentStepIndex.value] || null;
});

const highlightStyle = computed(() => {
  if (!currentStep.value || !isGuideActive.value) return {};
  
  const element = document.querySelector(currentStep.value.target);
  if (!element) return {};
  
  const rect = element.getBoundingClientRect();
  return {
    left: `${rect.left - 8}px`,
    top: `${rect.top - 8}px`,
    width: `${rect.width + 16}px`,
    height: `${rect.height + 16}px`
  };
});

const tooltipStyle = computed(() => {
  if (!currentStep.value || !isGuideActive.value) return {};
  
  const element = document.querySelector(currentStep.value.target);
  if (!element) return {};
  
  const rect = element.getBoundingClientRect();
  const position = currentStep.value.position || 'bottom';
  
  let left = 0;
  let top = 0;
  
  switch (position) {
    case 'top':
      left = rect.left + rect.width / 2 - 200; // 卡片宽度的一半
      top = rect.top - 280; // 卡片高度 + 间距
      break;
    case 'bottom':
      left = rect.left + rect.width / 2 - 200;
      top = rect.bottom + 20;
      break;
    case 'left':
      left = rect.left - 420; // 卡片宽度 + 间距
      top = rect.top + rect.height / 2 - 140;
      break;
    case 'right':
      left = rect.right + 20;
      top = rect.top + rect.height / 2 - 140;
      break;
  }
  
  // 确保卡片不会超出视窗
  left = Math.max(20, Math.min(left, window.innerWidth - 420));
  top = Math.max(20, Math.min(top, window.innerHeight - 280));
  
  return {
    left: `${left}px`,
    top: `${top}px`
  };
});

// 方法
const startGuide = () => {
  isGuideActive.value = true;
  currentStepIndex.value = 0;
  
  // 滚动到第一个目标元素
  nextTick(() => {
    scrollToTarget();
  });
};

const closeGuide = () => {
  isGuideActive.value = false;
  currentStepIndex.value = 0;
};

const skipGuide = () => {
  closeGuide();
};

const nextStep = () => {
  if (currentStepIndex.value < guideSteps.value.length - 1) {
    currentStepIndex.value++;
    nextTick(() => {
      scrollToTarget();
    });
  } else {
    closeGuide();
  }
};

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--;
    nextTick(() => {
      scrollToTarget();
    });
  }
};

const scrollToTarget = () => {
  if (!currentStep.value) return;
  
  const element = document.querySelector(currentStep.value.target);
  if (element) {
    element.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'center',
      inline: 'center'
    });
  }
};

// 工具提示相关方法
const showTooltipFor = (element: HTMLElement, data: TooltipData) => {
  tooltipData.value = data;
  
  const rect = element.getBoundingClientRect();
  tooltipPosition.value = {
    left: `${rect.left + rect.width / 2 - 100}px`,
    top: `${rect.top - 60}px`
  };
  
  tooltipArrowStyle.value = {
    left: '50%',
    top: '100%',
    transform: 'translateX(-50%)'
  };
  
  showTooltip.value = true;
};

const hideTooltip = () => {
  showTooltip.value = false;
  tooltipData.value = null;
};

// 暴露方法给父组件
defineExpose({
  startGuide,
  closeGuide,
  showTooltipFor,
  hideTooltip
});

// 生命周期
onMounted(() => {
  if (props.autoStart) {
    setTimeout(() => {
      startGuide();
    }, 1000);
  }
});
</script>

<style scoped>
.user-guide-system {
  position: relative;
}

/* 动画效果 */
.transition-all {
  transition: all 0.3s ease-in-out;
}

/* 确保引导层在最顶层 */
.z-50 {
  z-index: 9999;
}

.z-40 {
  z-index: 9998;
}

.z-30 {
  z-index: 9997;
}
</style>
