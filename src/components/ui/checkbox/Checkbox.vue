<template>
  <button
    ref="checkboxRef"
    type="button"
    role="checkbox"
    :aria-checked="checked"
    :disabled="disabled"
    :class="cn(
      'peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',
      props.class
    )"
    :data-state="checked ? 'checked' : 'unchecked'"
    @click="toggle"
  >
    <CheckIcon 
      v-if="checked" 
      class="h-4 w-4" 
    />
    <MinusIcon 
      v-else-if="indeterminate" 
      class="h-4 w-4" 
    />
  </button>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Check as CheckIcon, Minus as MinusIcon } from 'lucide-vue-next'
import { cn } from '@/lib/utils'

interface Props {
  checked?: boolean
  indeterminate?: boolean
  disabled?: boolean
  class?: string
}

interface Emits {
  (e: 'update:checked', checked: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  checked: false,
  indeterminate: false,
  disabled: false
})

const emit = defineEmits<Emits>()

const checkboxRef = ref<HTMLButtonElement>()

const toggle = () => {
  if (!props.disabled) {
    emit('update:checked', !props.checked)
  }
}
</script>