<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent>
      <slot />
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface Props {
  open: boolean;
}

interface Emits {
  (e: 'update:open', open: boolean): void;
}

defineProps<Props>();
defineEmits<Emits>();
</script>