<template>
  <div class="p-6 space-y-6">
    <h1 class="text-2xl font-bold">Shadcn Vue Components Test</h1>
    
    <!-- Button Component Test -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Button Components</h2>
      <div class="flex gap-2">
        <Button>Default But<PERSON></Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="destructive">Destructive</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost">Ghost</Button>
      </div>
    </div>

    <!-- Card Component Test -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Card Component</h2>
      <Card class="w-96">
        <CardHeader>
          <CardTitle>Card Title</CardTitle>
          <CardDescription>This is a card description</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Card content goes here</p>
        </CardContent>
        <CardFooter>
          <Button>Action</Button>
        </CardFooter>
      </Card>
    </div>

    <!-- Form Components Test -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Form Components</h2>
      <Form :validation-schema="formSchema" @submit="onSubmit">
        <div class="w-96 space-y-4">
          <FormField v-slot="{ componentField }" name="inputField">
            <FormItem>
              <FormLabel>Input Field</FormLabel>
              <FormControl>
                <Input v-bind="componentField" placeholder="Enter text..." />
              </FormControl>
              <FormDescription>This is a form input field</FormDescription>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="selectField">
            <FormItem>
              <FormLabel>Select Field</FormLabel>
              <FormControl>
                <Select v-bind="componentField">
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="option1">Option 1</SelectItem>
                    <SelectItem value="option2">Option 2</SelectItem>
                    <SelectItem value="option3">Option 3</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="textareaField">
            <FormItem>
              <FormLabel>Textarea Field</FormLabel>
              <FormControl>
                <Textarea v-bind="componentField" placeholder="Enter description..." />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <Button type="submit">Submit Form</Button>
        </div>
      </Form>
    </div>

    <!-- Table Component Test -->
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Table Component</h2>
      <Table>
        <TableCaption>A list of sample data</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Amount</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>Item 1</TableCell>
            <TableCell>Active</TableCell>
            <TableCell>$100.00</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Item 2</TableCell>
            <TableCell>Inactive</TableCell>
            <TableCell>$200.00</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

const formSchema = toTypedSchema(z.object({
  inputField: z.string().min(2, {
    message: 'Input must be at least 2 characters.',
  }),
  selectField: z.string().min(1, {
    message: 'Please select an option.',
  }),
  textareaField: z.string().min(10, {
    message: 'Description must be at least 10 characters.',
  }),
}))

function onSubmit(values: any) {
  console.log('Form submitted:', values)
}
</script>