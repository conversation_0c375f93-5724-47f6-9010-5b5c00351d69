<template>
  <div class="order-item-status-indicator">
    <!-- 主状态指示器 -->
    <div class="flex items-center gap-2">
      <div 
        class="status-dot"
        :class="getStatusDotClass()"
        :title="getStatusTooltip()"
      >
        <component :is="getStatusIcon()" class="w-3 h-3" />
      </div>
      
      <div class="status-text">
        <div class="text-xs font-medium" :class="getStatusTextClass()">
          {{ getStatusText() }}
        </div>
        <div v-if="showDetails" class="text-xs text-gray-500 mt-0.5">
          {{ getStatusDetails() }}
        </div>
      </div>
    </div>
    
    <!-- 进度条（如果适用） -->
    <div v-if="showProgress && progress !== undefined" class="mt-2">
      <div class="w-full bg-gray-200 rounded-full h-1">
        <div 
          class="h-1 rounded-full transition-all duration-300"
          :class="getProgressBarClass()"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
      <div class="text-xs text-gray-500 mt-1">
        {{ progress }}% 完成
      </div>
    </div>
    
    <!-- 警告和错误信息 -->
    <div v-if="warnings.length > 0" class="mt-2 space-y-1">
      <div 
        v-for="warning in warnings" 
        :key="warning.id"
        class="flex items-start gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs"
      >
        <AlertTriangle class="w-3 h-3 text-yellow-600 mt-0.5 flex-shrink-0" />
        <div class="text-yellow-800">{{ warning.message }}</div>
      </div>
    </div>
    
    <div v-if="errors.length > 0" class="mt-2 space-y-1">
      <div 
        v-for="error in errors" 
        :key="error.id"
        class="flex items-start gap-2 p-2 bg-red-50 border border-red-200 rounded text-xs"
      >
        <XCircle class="w-3 h-3 text-red-600 mt-0.5 flex-shrink-0" />
        <div class="text-red-800">{{ error.message }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Play,
  Pause,
  Settings,
  Package,
} from 'lucide-vue-next'

type OrderItemStatus = 
  | 'available'      // 可用
  | 'selected'       // 已选择
  | 'processing'     // 处理中
  | 'completed'      // 已完成
  | 'unavailable'    // 不可用
  | 'conflict'       // 冲突
  | 'warning'        // 警告
  | 'pending'        // 待处理

interface StatusMessage {
  id: string
  message: string
  type: 'warning' | 'error'
}

interface Props {
  status: OrderItemStatus
  showDetails?: boolean
  showProgress?: boolean
  progress?: number
  warnings?: StatusMessage[]
  errors?: StatusMessage[]
  customMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: false,
  showProgress: false,
  warnings: () => [],
  errors: () => []
})

// 计算属性
const getStatusIcon = () => {
  switch (props.status) {
    case 'available': return Package
    case 'selected': return CheckCircle
    case 'processing': return Play
    case 'completed': return CheckCircle
    case 'unavailable': return XCircle
    case 'conflict': return AlertTriangle
    case 'warning': return AlertTriangle
    case 'pending': return Clock
    default: return Package
  }
}

const getStatusDotClass = () => {
  const baseClass = 'w-6 h-6 rounded-full flex items-center justify-center'
  
  switch (props.status) {
    case 'available':
      return `${baseClass} bg-gray-100 text-gray-600`
    case 'selected':
      return `${baseClass} bg-blue-100 text-blue-600`
    case 'processing':
      return `${baseClass} bg-orange-100 text-orange-600 animate-pulse`
    case 'completed':
      return `${baseClass} bg-green-100 text-green-600`
    case 'unavailable':
      return `${baseClass} bg-red-100 text-red-600`
    case 'conflict':
      return `${baseClass} bg-yellow-100 text-yellow-600`
    case 'warning':
      return `${baseClass} bg-yellow-100 text-yellow-600`
    case 'pending':
      return `${baseClass} bg-gray-100 text-gray-600`
    default:
      return `${baseClass} bg-gray-100 text-gray-600`
  }
}

const getStatusTextClass = () => {
  switch (props.status) {
    case 'available': return 'text-gray-700'
    case 'selected': return 'text-blue-700'
    case 'processing': return 'text-orange-700'
    case 'completed': return 'text-green-700'
    case 'unavailable': return 'text-red-700'
    case 'conflict': return 'text-yellow-700'
    case 'warning': return 'text-yellow-700'
    case 'pending': return 'text-gray-700'
    default: return 'text-gray-700'
  }
}

const getProgressBarClass = () => {
  switch (props.status) {
    case 'processing': return 'bg-orange-500'
    case 'completed': return 'bg-green-500'
    case 'selected': return 'bg-blue-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = () => {
  if (props.customMessage) {
    return props.customMessage
  }
  
  switch (props.status) {
    case 'available': return '可选择'
    case 'selected': return '已选择'
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'unavailable': return '不可用'
    case 'conflict': return '存在冲突'
    case 'warning': return '需要注意'
    case 'pending': return '待处理'
    default: return '未知状态'
  }
}

const getStatusDetails = () => {
  switch (props.status) {
    case 'available': 
      return '可以添加到生产计划'
    case 'selected': 
      return '已加入当前批次'
    case 'processing': 
      return '正在生产中'
    case 'completed': 
      return '生产已完成'
    case 'unavailable': 
      return '暂时无法生产'
    case 'conflict': 
      return '与其他项目存在工艺冲突'
    case 'warning': 
      return '存在潜在问题'
    case 'pending': 
      return '等待处理'
    default: 
      return ''
  }
}

const getStatusTooltip = () => {
  const text = getStatusText()
  const details = getStatusDetails()
  return details ? `${text} - ${details}` : text
}
</script>

<style scoped>
.order-item-status-indicator {
  min-width: 120px;
}

.status-dot {
  transition: all 0.2s ease;
}

.status-dot:hover {
  transform: scale(1.1);
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>