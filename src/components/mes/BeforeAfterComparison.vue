<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps<{ title: string; before: number; after: number; unit?: string }>()
</script>

<template>
  <div class="border rounded p-3">
    <div class="font-medium mb-2">{{ props.title }}</div>
    <div class="flex items-end gap-4">
      <div class="flex-1">
        <div class="text-sm text-gray-500">传统</div>
        <div class="text-2xl">{{ props.before }}<span class="text-base ml-1">{{ props.unit }}</span></div>
      </div>
      <div class="flex-1">
        <div class="text-sm text-gray-500">智能</div>
        <div class="text-2xl">{{ props.after }}<span class="text-base ml-1">{{ props.unit }}</span></div>
      </div>
      <div class="flex-1 text-right">
        <div class="text-sm text-gray-500">提升</div>
        <div class="text-2xl font-bold text-green-600">{{ (props.after - props.before).toFixed(2) }}<span class="text-base ml-1">{{ props.unit }}</span></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>

