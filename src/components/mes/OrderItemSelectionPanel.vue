<template>
  <div
    class="h-full flex flex-col"
    :class="[responsiveClasses, `screen-${screenSize}`]"
  >
    <!-- 搜索和筛选区域 -->
    <div class="p-4 border-b bg-white flex-shrink-0">
      <OrderItemSearchFilter
        :search-query="searchQuery"
        :status-filter="statusFilter"
        :process-type-filter="processTypeFilter"
        :customer-filter="customerFilter"
        :available-orders="availableOrders"
        :filtered-count="filteredOrders.length"
        :total-count="availableOrders.length"
        @search-changed="handleSearchChanged"
        @filter-changed="handleFilterChanged"
        @advanced-filter-changed="handleAdvancedFilterChanged"
      />
    </div>

    <!-- 已选订单项快速预览条 -->
    <div
      v-if="selectedOrderItems.length > 0"
      class="selected-items-preview px-4 py-2 border-b flex-shrink-0 transition-all duration-300"
    >
      <div class="flex items-center justify-between">
        <!-- 状态指示器 -->
        <div class="flex items-center gap-3">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <Badge variant="default" class="text-xs font-medium">
              {{ selectedOrderItems.length }}项
            </Badge>
          </div>

          <div class="flex items-center gap-4 text-sm">
            <span class="text-blue-700 font-medium">
              {{ totalSelectedQuantity }}片
            </span>
            <span class="text-blue-600"> {{ uniqueCustomers }}个客户 </span>
            <span
              v-if="hasProcessConflicts"
              class="text-amber-600 flex items-center gap-1"
            >
              <AlertTriangle class="w-3 h-3" />
              有冲突
            </span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            @click="showSelectedDetails = !showSelectedDetails"
            class="text-xs h-7 px-2"
          >
            {{ showSelectedDetails ? "收起" : "详情" }}
            <ChevronDown
              class="w-3 h-3 ml-1 transition-transform duration-200"
              :class="{ 'rotate-180': showSelectedDetails }"
            />
          </Button>

          <Button
            size="sm"
            variant="ghost"
            @click="clearAllSelections"
            class="text-xs h-7 px-2 text-red-600 hover:text-red-700"
          >
            <X class="w-3 h-3 mr-1" />
            清空
          </Button>
        </div>
      </div>
    </div>

    <!-- 订单列表区域 -->
    <div class="order-list-container flex-1 min-h-0 overflow-hidden">
      <div v-if="loading" class="flex items-center justify-center h-32">
        <div class="flex items-center gap-2 text-gray-500">
          <div
            class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"
          ></div>
          <span class="text-sm">加载订单数据...</span>
        </div>
      </div>

      <div
        v-else-if="filteredOrders.length === 0"
        class="flex flex-col items-center justify-center h-32 text-gray-500"
      >
        <Package class="w-8 h-8 mb-2 text-gray-400" />
        <span class="text-sm">暂无可用订单</span>
      </div>

      <div v-else class="order-list-scroll h-full overflow-y-auto">
        <div class="space-y-3 p-3">
          <OrderItemSelector
            v-for="order in filteredOrders"
            :key="order.id"
            :order="order"
            :selected-order-items="selectedOrderItems"
            :conflicting-items="conflictingItems"
            :unavailable-items="unavailableItems"
            @order-item-selected="handleOrderItemSelected"
            @order-item-removed="handleOrderItemRemoved"
            @quantity-changed="handleQuantityChanged"
            @batch-optimization-requested="handleBatchOptimizationRequested"
            class="order-item-card"
          />
        </div>
      </div>
    </div>

    <!-- 已选订单项详情面板 (可折叠) -->
    <div
      v-if="selectedOrderItems.length > 0 && showSelectedDetails"
      class="selected-details-panel flex-shrink-0 overflow-hidden transition-all duration-300"
    >
      <!-- 面板头部 -->
      <div class="details-header px-4 py-3 border-b bg-gray-50">
        <div class="flex items-center justify-between">
          <h4 class="font-medium text-sm text-gray-800">已选订单项详情</h4>
          <div class="flex items-center gap-2">
            <Badge variant="outline" class="text-xs">
              {{ selectedOrderItems.length }}项
            </Badge>
            <Button
              size="sm"
              variant="ghost"
              @click="showSelectedDetails = false"
              class="h-6 w-6 p-0"
            >
              <X class="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>

      <!-- 详情内容 -->
      <div class="details-content flex-1 overflow-hidden flex flex-col">
        <!-- 已选项列表 -->
        <div class="selected-items-list p-4 border-b">
          <div class="space-y-2 max-h-32 overflow-y-auto">
            <div
              v-for="item in selectedOrderItems"
              :key="item.id"
              class="selected-item-card flex items-center justify-between p-3 bg-white rounded-lg border hover:shadow-sm transition-shadow"
            >
              <div class="flex-1 min-w-0">
                <div class="flex items-center gap-2 mb-1">
                  <span class="font-medium text-sm text-gray-900">{{
                    item.orderNumber
                  }}</span>
                  <Badge variant="secondary" class="text-xs">{{
                    item.customerName
                  }}</Badge>
                </div>
                <div class="text-xs text-gray-600 mb-1">
                  {{ item.specifications.length }}×{{
                    item.specifications.width
                  }}×{{ item.specifications.thickness }}mm
                </div>
                <div class="flex items-center gap-3 text-xs">
                  <span class="text-blue-600 font-medium"
                    >{{ item.selectedQuantity }}片</span
                  >
                  <span class="text-gray-500">{{
                    formatDate(item.deliveryDate)
                  }}</span>
                </div>
              </div>

              <div class="flex items-center gap-2 ml-3">
                <!-- 快速数量调整 -->
                <div class="flex items-center gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    class="h-6 w-6 p-0"
                    @click="adjustQuantity(item.id, -1)"
                    :disabled="item.selectedQuantity <= 1"
                  >
                    <Minus class="w-3 h-3" />
                  </Button>
                  <span class="text-xs w-8 text-center">{{
                    item.selectedQuantity
                  }}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    class="h-6 w-6 p-0"
                    @click="adjustQuantity(item.id, 1)"
                    :disabled="item.selectedQuantity >= item.totalQuantity"
                  >
                    <Plus class="w-3 h-3" />
                  </Button>
                </div>

                <!-- 移除按钮 -->
                <Button
                  size="sm"
                  variant="ghost"
                  class="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  @click="removeOrderItem(item.id)"
                >
                  <X class="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 工艺兼容性检查 -->
        <div class="compatibility-check flex-1 overflow-y-auto p-4">
          <ProcessCompatibilityChecker
            :selected-order-items="selectedOrderItems"
            :auto-check="true"
            @conflict-resolved="handleConflictResolved"
            @conflict-ignored="handleConflictIgnored"
            @grouping-applied="handleGroupingApplied"
            @compatibility-changed="handleCompatibilityChanged"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Package,
  X,
  ChevronDown,
  AlertTriangle,
  Plus,
  Minus,
} from "lucide-vue-next";

import OrderItemSearchFilter from "./OrderItemSearchFilter.vue";
import OrderItemSelector from "./OrderItemSelector.vue";
import ProcessCompatibilityChecker from "./ProcessCompatibilityChecker.vue";

import type {
  OrderItemSelectionPanelProps,
  OrderItemSelectionPanelEvents,
} from "@/types/production-order-creation";
import type { CustomerOrderItem } from "@/types/mes-validation";
import { layoutManager } from "@/utils/smartLayoutManager";

// Props
interface ExtendedProps extends OrderItemSelectionPanelProps {
  customerFilter: string;
  conflictingItems?: string[];
  unavailableItems?: Record<string, string>;
}

const props = defineProps<ExtendedProps>();

// Events
const emit = defineEmits<OrderItemSelectionPanelEvents>();

// 响应式状态
const showSelectedDetails = ref(false);

// 状态持久化（可选）
const saveLayoutState = () => {
  const state = {
    showSelectedDetails: showSelectedDetails.value,
    timestamp: Date.now(),
  };
  try {
    localStorage.setItem("orderSelectionLayout", JSON.stringify(state));
  } catch (error) {
    console.warn("无法保存布局状态:", error);
  }
};

const restoreLayoutState = () => {
  try {
    const saved = localStorage.getItem("orderSelectionLayout");
    if (saved) {
      const state = JSON.parse(saved);
      // 只在1小时内的状态才恢复
      if (Date.now() - state.timestamp < 3600000) {
        showSelectedDetails.value = state.showSelectedDetails;
      }
    }
  } catch (error) {
    console.warn("无法恢复布局状态:", error);
  }
};

// 组件挂载时恢复状态
restoreLayoutState();

// 监听状态变化并保存
watch(showSelectedDetails, () => {
  saveLayoutState();
});

// 监听选中项变化，自动收起详情面板（如果没有选中项）
watch(
  () => props.selectedOrderItems.length,
  (newLength) => {
    if (newLength === 0) {
      showSelectedDetails.value = false;
    }
  }
);

// 响应式断点检测
const screenSize = ref<"sm" | "md" | "lg">("lg");
const isMobile = computed(() => screenSize.value === "sm");

// 防抖优化的屏幕尺寸更新
let resizeTimer: number | null = null;
const updateScreenSize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  resizeTimer = window.setTimeout(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;

    if (width < 768) {
      screenSize.value = "sm";
    } else if (width < 1024) {
      screenSize.value = "md";
    } else {
      screenSize.value = "lg";
    }

    // 更新布局管理器
    layoutManager.adjustForScreenSize(width, height);
  }, 150); // 150ms防抖
};

// 生命周期钩子
onMounted(() => {
  updateScreenSize();
  window.addEventListener("resize", updateScreenSize);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScreenSize);
});

// 计算属性
const filteredOrders = computed(() => {
  let orders = props.availableOrders;

  // 搜索过滤
  if (props.searchQuery) {
    const query = props.searchQuery.toLowerCase();
    orders = orders.filter(
      (order) =>
        order.orderNumber.toLowerCase().includes(query) ||
        order.customerName.toLowerCase().includes(query) ||
        order.items.some(
          (item) =>
            `${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}`.includes(
              query
            ) ||
            item.specifications.glassType.toLowerCase().includes(query) ||
            item.specifications.color?.toLowerCase().includes(query)
        )
    );
  }

  // 状态过滤
  if (props.statusFilter !== "all") {
    orders = orders.filter((order) => order.status === props.statusFilter);
  }

  // 工艺类型过滤
  if (props.processTypeFilter !== "all") {
    orders = orders.filter((order) =>
      order.items.some((item) =>
        (item as any).processFlow?.some((step: unknown) =>
          step.stepName.toLowerCase().includes(props.processTypeFilter)
        )
      )
    );
  }

  // 客户过滤
  if (props.customerFilter !== "all") {
    orders = orders.filter(
      (order) => order.customerName === props.customerFilter
    );
  }

  return orders;
});

const totalSelectedQuantity = computed(() => {
  return props.selectedOrderItems.reduce(
    (sum, item) => sum + item.selectedQuantity,
    0
  );
});

const uniqueCustomers = computed(() => {
  const customers = new Set(
    props.selectedOrderItems.map((item) => item.customerName)
  );
  return customers.size;
});

const hasProcessConflicts = computed(() => {
  return props.conflictingItems && props.conflictingItems.length > 0;
});

// 智能布局计算
const layoutAllocation = computed(() => {
  return layoutManager.calculateOptimalLayout(
    800, // 默认容器高度，实际应该从DOM获取
    props.selectedOrderItems.length,
    hasProcessConflicts.value,
    showSelectedDetails.value
  );
});

const responsiveClasses = computed(() => {
  return layoutManager.getResponsiveClasses().join(" ");
});

// 性能优化：大量数据时的虚拟滚动支持
const shouldUseVirtualScroll = computed(() => {
  return filteredOrders.value.length > 50;
});

// 性能优化：缓存计算结果
const cachedSelectionSummary = computed(() => {
  return layoutManager.calculateSelectionSummary(props.selectedOrderItems);
});

// 事件处理
const handleSearchChanged = (query: string) => {
  emit("search-changed", query);
};

const handleFilterChanged = (filterType: string, value: string) => {
  emit("filter-changed", filterType, value);
};

const handleAdvancedFilterChanged = (filters: Record<string, unknown>) => {
  // 处理高级筛选变化
  console.log("高级筛选变化:", filters);
  // 这里可以触发额外的筛选逻辑
};

const handleOrderItemSelected = (item: CustomerOrderItem, quantity: number) => {
  emit("order-item-selected", item, quantity);
};

const handleOrderItemRemoved = (itemId: string) => {
  emit("order-item-removed", itemId);
};

const handleQuantityChanged = (itemId: string, quantity: number) => {
  emit("quantity-changed", itemId, quantity);
};

const handleBatchOptimizationRequested = (orderItems: CustomerOrderItem[]) => {
  // 触发批次优化请求
  console.log("批次优化请求:", orderItems.length, "个订单项");
};

const handleConflictResolved = (conflictId: string) => {
  console.log("冲突已解决:", conflictId);
  // 这里可以触发重新优化批次
};

const handleConflictIgnored = (conflictId: string) => {
  console.log("冲突已忽略:", conflictId);
};

const handleGroupingApplied = (groupId: string, itemIds: string[]) => {
  console.log("应用分组建议:", groupId, itemIds);
  // 这里可以根据分组建议重新组织选择
};

const handleCompatibilityChanged = (result: unknown) => {
  // 将兼容性结果传递给父组件
};

const removeOrderItem = (itemId: string) => {
  emit("order-item-removed", itemId);
};

const clearAllSelections = () => {
  props.selectedOrderItems.forEach((item) => {
    emit("order-item-removed", item.id);
  });
};

// 数量调整方法
const adjustQuantity = (itemId: string, delta: number) => {
  const item = props.selectedOrderItems.find((item) => item.id === itemId);
  if (!item) return;

  const newQuantity = Math.max(
    1,
    Math.min(item.selectedQuantity + delta, item.totalQuantity)
  );
  if (newQuantity !== item.selectedQuantity) {
    emit("quantity-changed", itemId, newQuantity);
  }
};

// 工具方法
const formatDate = (dateString: string) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    month: "short",
    day: "numeric",
  });
};
</script>

<style scoped>
/* 快速预览条样式 */
.selected-items-preview {
  background: linear-gradient(90deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 订单列表容器样式 */
.order-list-container {
  /* 确保订单列表占用主要空间 */
  flex: 1;
  min-height: 0; /* 允许flex子元素收缩 */
}

.order-list-scroll {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.order-list-scroll::-webkit-scrollbar {
  width: 6px;
}

.order-list-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.order-list-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.order-list-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.order-item-card {
  transition: all 0.2s ease;
}

.order-item-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 详情面板样式 */
.selected-details-panel {
  max-height: 320px;
  background: #f8fafc;
  border-top: 2px solid #e2e8f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.details-header {
  flex-shrink: 0;
}

.details-content {
  min-height: 0;
}

.selected-items-list {
  flex-shrink: 0;
}

.compatibility-check {
  min-height: 120px;
}

.selected-item-card {
  transition: all 0.2s ease;
}

.selected-item-card:hover {
  border-color: #3b82f6;
}

/* 动画效果优化 */
.selected-details-panel {
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    max-height: 320px;
    opacity: 1;
    transform: translateY(0);
  }
}

.selected-items-preview {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停动画增强 */
.order-item-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.order-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.selected-item-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.selected-item-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* 按钮动画效果 */
.selected-items-preview button {
  transition: all 0.2s ease;
}

.selected-items-preview button:hover {
  transform: scale(1.05);
}

/* 状态指示器动画 */
.w-2.h-2.bg-blue-500.rounded-full.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 响应式调整 */
@media (max-height: 600px) {
  .selected-details-panel {
    max-height: 240px;
  }

  @keyframes slideDown {
    to {
      max-height: 240px;
    }
  }

  .selected-items-list .space-y-2 {
    max-height: 80px;
  }

  .compatibility-check {
    min-height: 80px;
  }
}

/* 小屏幕适配 */
.screen-sm .selected-items-preview {
  padding: 0.5rem 1rem;
}

.screen-sm .selected-items-preview .flex.items-center.gap-4 {
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.screen-sm .selected-details-panel {
  max-height: 200px;
}

.screen-sm .selected-item-card {
  padding: 0.5rem;
}

.screen-sm .selected-item-card .flex.items-center.gap-2 {
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
}

/* 中等屏幕适配 */
.screen-md .selected-details-panel {
  max-height: 280px;
}

/* 大屏幕优化 */
.screen-lg .order-list-scroll {
  padding: 1rem;
}

.screen-lg .selected-item-card {
  padding: 1rem;
}

/* 移动端底部抽屉样式（预留） */
@media (max-width: 767px) {
  .mobile-details-drawer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(calc(100% - 60px));
    transition: transform 0.3s ease;
    z-index: 50;
  }

  .mobile-details-drawer.expanded {
    transform: translateY(0);
  }

  .drawer-handle {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .handle-bar {
    width: 40px;
    height: 4px;
    background: #d1d5db;
    border-radius: 2px;
  }
}
</style>
