<template>
  <div class="selected-items-summary h-full flex flex-col">
    <!-- 标题区域 -->
    <div class="summary-header p-4 border-b">
      <div class="flex items-center justify-between mb-3">
        <h3 class="font-semibold text-gray-800 flex items-center gap-2">
          <ShoppingCart class="w-4 h-4" />
          已选订单项
        </h3>
        <Badge variant="secondary" class="text-xs">
          {{ selectedItems.length }}项
        </Badge>
      </div>
      

      
      <!-- 冲突提示 -->
      <div v-if="hasConflicts" class="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-md">
        <div class="flex items-center gap-2 text-amber-700">
          <AlertTriangle class="w-3 h-3" />
          <span class="text-xs">检测到工艺冲突</span>
        </div>
      </div>
    </div>
    
    <!-- 已选项列表 -->
    <div class="flex-1 overflow-hidden">
      <div v-if="selectedItems.length === 0" class="flex flex-col items-center justify-center h-full text-gray-400">
        <Package class="w-12 h-12 mb-3" />
        <span class="text-sm">暂未选择订单项</span>
        <span class="text-xs mt-1">请在左侧选择需要生产的订单项</span>
      </div>
      
      <div v-else class="h-full overflow-y-auto p-4 space-y-3">
        <div 
          v-for="item in selectedItems" 
          :key="item.id"
          class="selected-item-card p-3 bg-white rounded-lg border hover:shadow-md transition-all duration-200"
        >
          <!-- 订单信息 -->
          <div class="flex items-start justify-between mb-2">
            <div class="flex-1 min-w-0">
              <div class="font-medium text-sm text-gray-900 truncate">
                {{ item.orderNumber }}
              </div>
              <div class="text-xs text-gray-500 mt-1">
                {{ item.customerName }}
              </div>
            </div>
            <Button 
              size="sm" 
              variant="ghost" 
              class="h-6 w-6 p-0 text-red-500 hover:text-red-700 ml-2"
              @click="$emit('item-removed', item.id)"
            >
              <X class="w-3 h-3" />
            </Button>
          </div>
          
          <!-- 规格信息 -->
          <div class="text-xs text-gray-600 mb-2">
            {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
          </div>
          
          <!-- 数量控制 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Button 
                size="sm" 
                variant="outline" 
                class="h-6 w-6 p-0"
                @click="decreaseQuantity(item.id, item.selectedQuantity)"
                :disabled="item.selectedQuantity <= 1"
              >
                <Minus class="w-3 h-3" />
              </Button>
              
              <Input 
                type="number" 
                :model-value="item.selectedQuantity"
                @update:model-value="(value) => handleQuantityChange(item.id, value)"
                @blur="handleQuantityBlur(item.id, $event)"
                :max="item.totalQuantity"
                :min="1"
                class="w-16 h-6 text-xs text-center px-1"
              />
              
              <Button 
                size="sm" 
                variant="outline" 
                class="h-6 w-6 p-0"
                @click="increaseQuantity(item.id, item.selectedQuantity)"
                :disabled="item.selectedQuantity >= item.totalQuantity"
              >
                <Plus class="w-3 h-3" />
              </Button>
            </div>
            
            <div class="text-xs text-gray-500">
              / {{ item.totalQuantity }}
            </div>
          </div>
          
          <!-- 进度条 -->
          <div class="mt-2">
            <div class="w-full bg-gray-200 rounded-full h-1">
              <div 
                class="bg-blue-500 h-1 rounded-full transition-all duration-300"
                :style="{ width: `${(item.selectedQuantity / item.totalQuantity) * 100}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部操作 -->
    <div v-if="selectedItems.length > 0" class="summary-footer p-4 border-t">
      <div class="space-y-3">
        <!-- 快速操作 -->
        <div class="flex items-center gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            @click="selectAllMaxQuantity"
            class="flex-1 text-xs"
          >
            <CheckSquare class="w-3 h-3 mr-1" />
            全选最大
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            @click="$emit('clear-all')"
            class="flex-1 text-xs text-red-600 hover:text-red-700"
          >
            <Trash2 class="w-3 h-3 mr-1" />
            清空全部
          </Button>
        </div>
        
        <!-- 汇总信息 -->
        <div class="summary-stats p-3 bg-blue-50 rounded-lg">
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div class="flex justify-between">
              <span class="text-gray-600">预估价值:</span>
              <span class="font-medium text-blue-700">¥{{ estimatedValue.toLocaleString() }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">预估工时:</span>
              <span class="font-medium text-blue-700">{{ estimatedHours }}h</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  ShoppingCart,
  Package,
  X,
  Plus,
  Minus,
  AlertTriangle,
  CheckSquare,
  Trash2,
} from 'lucide-vue-next'

import type { SelectedOrderItem } from '@/types/production-order-creation'

// Props
interface Props {
  selectedItems: SelectedOrderItem[]
  totalQuantity: number
  uniqueCustomers: number
  hasConflicts: boolean
}

// Events
interface Emits {
  (e: 'item-removed', itemId: string): void
  (e: 'quantity-changed', itemId: string, quantity: number): void
  (e: 'clear-all'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const estimatedValue = computed(() => {
  // 简单的价值估算，实际应该基于产品定价
  return props.selectedItems.reduce((sum, item) => {
    const basePrice = item.specifications.length * item.specifications.width * 0.001 * 50 // 50元/平米
    return sum + (basePrice * item.selectedQuantity)
  }, 0)
})

const estimatedHours = computed(() => {
  // 简单的工时估算
  return Math.round(props.totalQuantity * 0.1 * 10) / 10 // 每片0.1小时
})

// 事件处理
const handleQuantityChange = (itemId: string, value: string | number) => {
  const quantity = typeof value === 'string' ? parseInt(value) || 1 : value
  const item = props.selectedItems.find(item => item.id === itemId)
  if (!item) return
  
  const validQuantity = Math.max(1, Math.min(quantity, item.totalQuantity))
  emit('quantity-changed', itemId, validQuantity)
}

const handleQuantityInput = (itemId: string, event: Event) => {
  const target = event.target as HTMLInputElement
  const quantity = parseInt(target.value) || 1
  handleQuantityChange(itemId, quantity)
}

const handleQuantityBlur = (itemId: string, event: Event) => {
  const target = event.target as HTMLInputElement
  const quantity = parseInt(target.value) || 1
  const item = props.selectedItems.find(item => item.id === itemId)
  if (!item) return
  
  const validQuantity = Math.max(1, Math.min(quantity, item.totalQuantity))
  target.value = validQuantity.toString()
  emit('quantity-changed', itemId, validQuantity)
}

const increaseQuantity = (itemId: string, currentQuantity: number) => {
  const item = props.selectedItems.find(item => item.id === itemId)
  if (!item || currentQuantity >= item.totalQuantity) return
  
  emit('quantity-changed', itemId, currentQuantity + 1)
}

const decreaseQuantity = (itemId: string, currentQuantity: number) => {
  if (currentQuantity <= 1) return
  emit('quantity-changed', itemId, currentQuantity - 1)
}

const selectAllMaxQuantity = () => {
  props.selectedItems.forEach(item => {
    if (item.selectedQuantity < item.totalQuantity) {
      emit('quantity-changed', item.id, item.totalQuantity)
    }
  })
}
</script>

<style scoped>
.selected-items-summary {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

.summary-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
}



.selected-item-card {
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.selected-item-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.summary-footer {
  background: white;
  border-top: 1px solid #e2e8f0;
}

.summary-stats {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
}

/* 滚动条样式 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .selected-item-card {
    padding: 0.75rem;
  }
  
  .summary-footer {
    padding: 0.75rem;
  }
}
</style>