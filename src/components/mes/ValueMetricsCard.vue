<script setup lang="ts">
import { defineProps } from 'vue'

interface ValueMetric { name: string; target: number; actual: number; unit: string; period: string; notes?: string }

const props = defineProps<{ metrics: ValueMetric[] }>()
</script>

<template>
  <div class="grid md:grid-cols-3 gap-3">
    <div v-for="m in props.metrics" :key="m.name" class="border rounded p-3">
      <div class="text-sm text-gray-600">{{ m.period }}</div>
      <div class="text-base font-semibold">{{ m.name }}</div>
      <div class="mt-1">目标：{{ m.target }}{{ m.unit }}</div>
      <div>实际：<b>{{ m.actual }}</b>{{ m.unit }}</div>
      <div :class="(m.actual - m.target) >= 0 ? 'text-green-600' : 'text-rose-600'" class="font-semibold">偏差：{{ (m.actual - m.target).toFixed(2) }}{{ m.unit }}</div>
      <div v-if="m.notes" class="mt-1 text-xs text-gray-500">{{ m.notes }}</div>
    </div>
  </div>
</template>

<style scoped>
</style>

