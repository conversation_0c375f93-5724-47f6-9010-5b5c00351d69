<template>
  <div class="production-order-wizard">
    <!-- 向导进度条 -->
    <div class="wizard-progress mb-6">
      <div class="flex items-center justify-between">
        <div 
          v-for="(step, index) in wizardSteps" 
          :key="step.id"
          class="flex items-center"
          :class="{ 'flex-1': index < wizardSteps.length - 1 }"
        >
          <!-- 步骤圆圈 -->
          <div 
            class="step-circle"
            :class="{
              'active': currentStep === step.id,
              'completed': completedSteps.includes(step.id),
              'disabled': !canAccessStep(step.id)
            }"
          >
            <CheckCircle v-if="completedSteps.includes(step.id)" class="w-4 h-4" />
            <span v-else class="text-sm font-medium">{{ step.id }}</span>
          </div>
          
          <!-- 步骤标题 -->
          <div class="ml-3">
            <div class="text-sm font-medium" :class="getStepTitleClass(step.id)">
              {{ step.title }}
            </div>
            <div class="text-xs text-gray-500">{{ step.description }}</div>
          </div>
          
          <!-- 连接线 -->
          <div 
            v-if="index < wizardSteps.length - 1" 
            class="flex-1 h-px bg-gray-300 mx-4"
            :class="{ 'bg-green-500': completedSteps.includes(step.id) }"
          ></div>
        </div>
      </div>
    </div>
    
    <!-- 步骤内容 -->
    <div class="wizard-content">
      <!-- 第一步：订单选择 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="step-header mb-4">
          <h2 class="text-lg font-semibold">选择订单项</h2>
          <p class="text-sm text-gray-600">从客户订单中选择需要生产的订单项</p>
        </div>
        
        <StepOneOrderSelection
          :available-orders="availableOrders"
          :selected-order-items="selectedOrderItems"
          :search-query="searchQuery"
          :status-filter="statusFilter"
          :process-type-filter="processTypeFilter"
          :customer-filter="customerFilter"
          :loading="loading"
          @order-item-selected="handleOrderItemSelected"
          @order-item-removed="handleOrderItemRemoved"
          @quantity-changed="handleQuantityChanged"
          @search-changed="handleSearchChanged"
          @filter-changed="handleFilterChanged"
          @next-step="handleNextStep"
          @cancel="$emit('cancel')"
        />
      </div>
      
      <!-- 第二步：批次优化 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="step-header mb-4">
          <h2 class="text-lg font-semibold">批次优化配置</h2>
          <p class="text-sm text-gray-600">系统将自动优化批次分组，提高生产效率</p>
        </div>
        
        <StepTwoBatchOptimization
          :selected-order-items="selectedOrderItems"
          :batch-optimization="batchOptimization"
          :process-conflicts="processConflicts"
          :validation-results="validationResults"
          :work-order-priority="workOrderPriority"
          :planned-start-date="plannedStartDate"
          :estimated-end-date="estimatedEndDate"
          :schedule-recommendation="scheduleRecommendation"
          :loading="loading"
          :is-creating="isCreating"
          :can-create="canCreateOrder"
          @batch-modified="handleBatchModified"
          @conflict-resolved="handleConflictResolved"
          @optimization-requested="handleOptimizationRequested"
          @priority-changed="handlePriorityChanged"
          @schedule-changed="handleScheduleChanged"
          @prev-step="handlePrevStep"
          @create-order="handleCreateOrder"
        />
      </div>
      
      <!-- 第三步：确认创建（可选扩展步骤） -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="step-header mb-4">
          <h2 class="text-lg font-semibold">确认创建工单</h2>
          <p class="text-sm text-gray-600">请确认工单信息无误后创建</p>
        </div>
        
        <OrderConfirmationStep
          :selected-order-items="selectedOrderItems"
          :batch-optimization="batchOptimization"
          :work-order-config="workOrderConfig"
          :estimated-cost="estimatedCost"
          :estimated-duration="estimatedDuration"
          @prev-step="handlePrevStep"
          @create-order="handleCreateOrder"
          @save-draft="handleSaveDraft"
        />
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="wizard-footer mt-6 pt-4 border-t">
      <div class="flex items-center justify-between">
        <!-- 左侧信息 -->
        <div class="text-sm text-gray-600">
          <span v-if="selectedOrderItems.length > 0">
            已选择 {{ selectedOrderItems.length }} 个订单项，共 {{ totalSelectedQuantity }} 片
          </span>
          <span v-else>请选择订单项开始创建工单</span>
        </div>
        
        <!-- 右侧操作按钮 -->
        <div class="flex items-center gap-3">
          <Button 
            v-if="currentStep > 1" 
            variant="outline" 
            @click="handlePrevStep"
          >
            <ArrowLeft class="w-4 h-4 mr-2" />
            上一步
          </Button>
          
          <Button 
            v-if="currentStep < maxSteps && canProceedToNext" 
            @click="handleNextStep"
            class="bg-blue-600 hover:bg-blue-700"
          >
            下一步
            <ArrowRight class="w-4 h-4 ml-2" />
          </Button>
          
          <Button 
            v-if="currentStep === maxSteps && canCreateOrder" 
            @click="handleCreateOrder"
            :loading="isCreating"
            class="bg-green-600 hover:bg-green-700"
          >
            <Plus class="w-4 h-4 mr-2" />
            创建工单
          </Button>
          
          <Button variant="outline" @click="$emit('cancel')">
            取消
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import {
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Plus,
} from 'lucide-vue-next'

import StepOneOrderSelection from './StepOneOrderSelection.vue'
import StepTwoBatchOptimization from './StepTwoBatchOptimization.vue'
import OrderConfirmationStep from './OrderConfirmationStep.vue'

import type { 
  SelectedOrderItem,
  BatchOptimizationResult,
  ProcessConflict,
  ValidationResult,
  BatchConfiguration
} from '@/types/production-order-creation'
import type { CustomerOrder } from '@/types/mes-validation'

// Props
interface Props {
  availableOrders: CustomerOrder[]
  selectedOrderItems: SelectedOrderItem[]
  batchOptimization: BatchOptimizationResult | null
  processConflicts: ProcessConflict[]
  validationResults: ValidationResult[]
  workOrderPriority: string
  plannedStartDate: string
  estimatedEndDate: string
  scheduleRecommendation: string
  searchQuery: string
  statusFilter: string
  processTypeFilter: string
  customerFilter: string
  loading: boolean
  isCreating: boolean
  canCreateOrder: boolean
}

// Events
interface Emits {
  (e: 'order-item-selected', item: any, quantity: number): void
  (e: 'order-item-removed', itemId: string): void
  (e: 'quantity-changed', itemId: string, quantity: number): void
  (e: 'search-changed', query: string): void
  (e: 'filter-changed', filterType: string, value: string): void
  (e: 'batch-modified', batchId: string, config: BatchConfiguration): void
  (e: 'conflict-resolved', conflictId: string): void
  (e: 'optimization-requested'): void
  (e: 'priority-changed', priority: string): void
  (e: 'schedule-changed', schedule: { startDate: string; endDate: string }): void
  (e: 'create-order'): void
  (e: 'save-draft'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 向导状态
const currentStep = ref(1)
const completedSteps = ref<number[]>([])
const maxSteps = 2 // 可以扩展为3步

// 向导步骤配置
const wizardSteps = [
  {
    id: 1,
    title: '选择订单项',
    description: '从客户订单中选择需要生产的项目'
  },
  {
    id: 2,
    title: '批次优化',
    description: '配置批次参数和工单设置'
  },
  // 可选的第三步
  // {
  //   id: 3,
  //   title: '确认创建',
  //   description: '确认工单信息并创建'
  // }
]

// 计算属性
const totalSelectedQuantity = computed(() => {
  return props.selectedOrderItems.reduce((sum, item) => sum + item.selectedQuantity, 0)
})

const canProceedToNext = computed(() => {
  switch (currentStep.value) {
    case 1:
      return props.selectedOrderItems.length > 0
    case 2:
      return props.canCreateOrder
    default:
      return false
  }
})

const canAccessStep = (stepId: number) => {
  if (stepId === 1) return true
  if (stepId === 2) return props.selectedOrderItems.length > 0
  if (stepId === 3) return props.canCreateOrder
  return false
}

const getStepTitleClass = (stepId: number) => {
  if (currentStep.value === stepId) return 'text-blue-600'
  if (completedSteps.value.includes(stepId)) return 'text-green-600'
  if (!canAccessStep(stepId)) return 'text-gray-400'
  return 'text-gray-700'
}

// 工单配置
const workOrderConfig = computed(() => ({
  priority: props.workOrderPriority,
  plannedStartDate: props.plannedStartDate,
  estimatedEndDate: props.estimatedEndDate,
  scheduleRecommendation: props.scheduleRecommendation
}))

const estimatedCost = computed(() => {
  return props.selectedOrderItems.reduce((sum, item) => {
    const basePrice = item.specifications.length * item.specifications.width * 0.001 * 50
    return sum + (basePrice * item.selectedQuantity)
  }, 0)
})

const estimatedDuration = computed(() => {
  return Math.round(totalSelectedQuantity.value * 0.1 * 10) / 10
})

// 事件处理
const handleNextStep = () => {
  if (canProceedToNext.value && currentStep.value < maxSteps) {
    completedSteps.value.push(currentStep.value)
    currentStep.value++
  }
}

const handlePrevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const handleOrderItemSelected = (item: any, quantity: number) => {
  emit('order-item-selected', item, quantity)
}

const handleOrderItemRemoved = (itemId: string) => {
  emit('order-item-removed', itemId)
}

const handleQuantityChanged = (itemId: string, quantity: number) => {
  emit('quantity-changed', itemId, quantity)
}

const handleSearchChanged = (query: string) => {
  emit('search-changed', query)
}

const handleFilterChanged = (filterType: string, value: string) => {
  emit('filter-changed', filterType, value)
}

const handleBatchModified = (batchId: string, config: BatchConfiguration) => {
  emit('batch-modified', batchId, config)
}

const handleConflictResolved = (conflictId: string) => {
  emit('conflict-resolved', conflictId)
}

const handleOptimizationRequested = () => {
  emit('optimization-requested')
}

const handlePriorityChanged = (priority: string) => {
  emit('priority-changed', priority)
}

const handleScheduleChanged = (schedule: { startDate: string; endDate: string }) => {
  emit('schedule-changed', schedule)
}

const handleCreateOrder = () => {
  emit('create-order')
}

const handleSaveDraft = () => {
  emit('save-draft')
}
</script>

<style scoped>
.production-order-wizard {
  max-width: 1200px;
  margin: 0 auto;
}

.wizard-progress {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.step-circle {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #d1d5db;
  background: white;
  transition: all 0.2s ease;
}

.step-circle.active {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.step-circle.completed {
  border-color: #10b981;
  background: #10b981;
  color: white;
}

.step-circle.disabled {
  border-color: #e5e7eb;
  background: #f9fafb;
  color: #9ca3af;
}

.wizard-content {
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  min-height: 600px;
}

.step-content {
  padding: 1.5rem;
}

.step-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

.wizard-footer {
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wizard-progress .flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .wizard-progress .flex-1 {
    flex: none;
  }
  
  .wizard-progress .h-px {
    width: 2px;
    height: 1rem;
    margin: 0.5rem auto;
  }
  
  .wizard-footer .flex {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .wizard-footer .text-sm {
    text-align: center;
  }
}
</style>