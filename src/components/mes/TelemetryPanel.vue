<script setup lang="ts">
import { getEvents, getSummary } from '@/utils/Telemetry'
</script>

<template>
  <div class="fixed bottom-3 right-3 bg-white/90 backdrop-blur border rounded shadow p-3 w-72 text-xs space-y-2">
    <div class="font-semibold text-sm">性能监测 (原型)</div>
    <div>
      <div class="font-medium">摘要</div>
      <ul class="list-disc pl-4">
        <li v-for="s in getSummary()" :key="s.name">{{ s.name }}: 次数 {{ s.count }}，平均耗时 {{ s.avgDuration ?? '-' }}ms</li>
      </ul>
    </div>
    <div>
      <div class="font-medium">最近事件</div>
      <ul class="list-disc pl-4 max-h-24 overflow-auto">
        <li v-for="e in getEvents().slice().reverse()" :key="e.ts">{{ new Date(e.ts).toLocaleTimeString() }} - {{ e.name }} ({{ e.durationMs ?? '-' }}ms)</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
</style>

