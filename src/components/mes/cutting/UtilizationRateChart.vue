<script setup lang="ts">
const props = defineProps<{ traditional: number; optimized: number }>()
</script>

<template>
  <div class="border rounded p-3">
    <div class="font-medium mb-2">利用率对比图（原型占位）</div>
    <div class="h-28 flex items-end gap-4">
      <div class="bg-gray-300 w-16" :style="{ height: props.traditional + '%' }" title="传统"></div>
      <div class="bg-green-500 w-16" :style="{ height: props.optimized + '%' }" title="智能"></div>
    </div>
    <div class="text-xs text-gray-500 mt-1">后续可替换为 Chart.js</div>
  </div>
</template>

