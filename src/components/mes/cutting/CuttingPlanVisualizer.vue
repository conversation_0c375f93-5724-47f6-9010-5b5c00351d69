<script setup lang="ts">
const props = defineProps<{ sheet: { sheetLength: number; sheetWidth: number; pieces: { x:number; y:number; length:number; width:number; orderItemId:string; rotation:boolean }[] } }>()
</script>

<template>
  <div class="border rounded p-3">
    <div class="font-medium mb-2">排版方案可视化（占位SVG）</div>
    <svg :viewBox="`0 0 ${props.sheet.sheetWidth} ${props.sheet.sheetLength}`" class="w-full border">
      <rect :width="props.sheet.sheetWidth" :height="props.sheet.sheetLength" fill="#f9fafb" stroke="#cbd5e1" />
      <g>
        <rect v-for="(p, idx) in props.sheet.pieces" :key="idx" :x="p.x" :y="p.y" :width="p.width" :height="p.length" fill="#86efac" stroke="#16a34a" />
      </g>
    </svg>
  </div>
</template>

