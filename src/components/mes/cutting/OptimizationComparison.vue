<script setup lang="ts">
const props = defineProps<{ traditional: number; optimized: number; unit?: string }>()
</script>

<template>
  <div class="border rounded p-3">
    <div class="font-medium mb-2">算法效果对比</div>
    <div class="grid grid-cols-3 gap-3">
      <div>
        <div class="text-sm text-gray-500">传统</div>
        <div class="text-2xl">{{ props.traditional }}<span class="text-base ml-1">{{ props.unit }}</span></div>
      </div>
      <div>
        <div class="text-sm text-gray-500">智能</div>
        <div class="text-2xl">{{ props.optimized }}<span class="text-base ml-1">{{ props.unit }}</span></div>
      </div>
      <div class="text-right">
        <div class="text-sm text-gray-500">提升</div>
        <div class="text-2xl font-bold text-green-600">{{ (props.optimized - props.traditional).toFixed(2) }}<span class="text-base ml-1">{{ props.unit }}</span></div>
      </div>
    </div>
  </div>
</template>

