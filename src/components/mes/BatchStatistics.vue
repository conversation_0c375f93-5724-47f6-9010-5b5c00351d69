<template>
  <div class="batch-statistics space-y-3">
    <!-- 效率统计 -->
    <div class="stats-grid grid grid-cols-2 gap-3">
      <div class="stat-card p-3 bg-green-50 rounded-lg border border-green-200">
        <div class="stat-value text-green-700">{{ efficiency }}%</div>
        <div class="stat-label text-green-600">效率提升</div>
      </div>
      <div class="stat-card p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div class="stat-value text-blue-700">{{ timeSaved }}h</div>
        <div class="stat-label text-blue-600">节省工时</div>
      </div>
    </div>
    
    <!-- 批次信息 -->
    <div class="batch-info p-3 bg-gray-50 rounded-lg">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">批次分布</span>
        <Badge variant="outline" class="text-xs">{{ batchCount }}个批次</Badge>
      </div>
      
      <div class="space-y-2">
        <div 
          v-for="(batch, index) in batches" 
          :key="index"
          class="flex items-center justify-between text-xs"
        >
          <span class="text-gray-600">批次 {{ index + 1 }}</span>
          <div class="flex items-center gap-2">
            <span class="text-gray-800">{{ batch.itemCount }}项</span>
            <span class="text-blue-600">{{ batch.quantity }}片</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 资源利用率 -->
    <div class="resource-usage p-3 bg-orange-50 rounded-lg">
      <div class="text-sm font-medium text-orange-800 mb-2">资源利用率</div>
      <div class="space-y-2">
        <div class="flex items-center justify-between text-xs">
          <span class="text-orange-700">设备利用率</span>
          <span class="text-orange-800 font-medium">{{ equipmentUtilization }}%</span>
        </div>
        <div class="flex items-center justify-between text-xs">
          <span class="text-orange-700">人员利用率</span>
          <span class="text-orange-800 font-medium">{{ staffUtilization }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import type { BatchOptimizationResult, SelectedOrderItem } from '@/types/production-order-creation'

interface Props {
  batchOptimization: BatchOptimizationResult | null
  selectedItems: SelectedOrderItem[]
}

const props = defineProps<Props>()

const efficiency = computed(() => {
  return props.batchOptimization?.efficiency || 0
})

const timeSaved = computed(() => {
  return props.batchOptimization?.timeSaved || 0
})

const batchCount = computed(() => {
  return props.batchOptimization?.batches?.length || 1
})

const batches = computed(() => {
  if (!props.batchOptimization?.batches) {
    return [{
      itemCount: props.selectedItems.length,
      quantity: props.selectedItems.reduce((sum, item) => sum + item.selectedQuantity, 0)
    }]
  }
  
  return props.batchOptimization.batches.map(batch => ({
    itemCount: batch.items?.length || 0,
    quantity: batch.items?.reduce((sum: number, item: any) => sum + item.selectedQuantity, 0) || 0
  }))
})

const equipmentUtilization = computed(() => {
  return Math.round(75 + Math.random() * 20) // 模拟数据
})

const staffUtilization = computed(() => {
  return Math.round(70 + Math.random() * 25) // 模拟数据
})
</script>

<style scoped>
.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
}

.stat-label {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.stat-card {
  text-align: center;
}
</style>