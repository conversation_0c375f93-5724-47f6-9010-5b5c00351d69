<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Settings class="w-5 h-5" />
          配置工艺流程
        </DialogTitle>
        <DialogDescription>
          为订单项 "{{ orderItem?.specifications.length }}×{{ orderItem?.specifications.width }}×{{ orderItem?.specifications.thickness }}mm" 配置生产工艺流程
        </DialogDescription>
      </DialogHeader>
      
      <div class="flex-1 overflow-hidden flex flex-col">
        <!-- 产品信息 -->
        <div v-if="orderItem" class="p-4 bg-gray-50 rounded-lg mb-4">
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">规格尺寸:</span>
              <span class="ml-2 font-medium">{{ orderItem.specifications.length }}×{{ orderItem.specifications.width }}×{{ orderItem.specifications.thickness }}mm</span>
            </div>
            <div>
              <span class="text-gray-600">玻璃类型:</span>
              <span class="ml-2 font-medium">{{ orderItem.specifications.glassType }}</span>
            </div>
            <div>
              <span class="text-gray-600">颜色:</span>
              <span class="ml-2 font-medium">{{ orderItem.specifications.color || '透明' }}</span>
            </div>
            <div>
              <span class="text-gray-600">数量:</span>
              <span class="ml-2 font-medium">{{ orderItem.quantity }}片</span>
            </div>
          </div>
        </div>
        
        <!-- 工艺流程配置 -->
        <div class="flex-1 overflow-hidden">
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-medium">工艺流程步骤</h3>
            <div class="flex items-center gap-2">
              <Select v-model="selectedTemplate" @update:model-value="applyTemplate">
                <SelectTrigger class="w-48">
                  <SelectValue placeholder="选择工艺模板" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard_clear">标准透明玻璃</SelectItem>
                  <SelectItem value="tempered_clear">钢化透明玻璃</SelectItem>
                  <SelectItem value="laminated_clear">夹胶透明玻璃</SelectItem>
                  <SelectItem value="insulating_clear">中空透明玻璃</SelectItem>
                  <SelectItem value="low_e">Low-E玻璃</SelectItem>
                </SelectContent>
              </Select>
              
              <Button size="sm" @click="addProcessStep">
                <Plus class="w-4 h-4 mr-1" />
                添加步骤
              </Button>
            </div>
          </div>
          
          <!-- 工艺步骤列表 -->
          <div class="space-y-3 max-h-96 overflow-y-auto">
            <div 
              v-for="(step, index) in processSteps" 
              :key="step.id"
              class="process-step-card p-4 border rounded-lg bg-white"
            >
              <div class="flex items-start gap-4">
                <!-- 步骤序号 -->
                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                  {{ index + 1 }}
                </div>
                
                <!-- 步骤配置 -->
                <div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label class="text-xs">工序名称</Label>
                    <Select v-model="step.stepName" @update:model-value="updateStep(step.id, 'stepName', $event)">
                      <SelectTrigger class="h-8">
                        <SelectValue placeholder="选择工序" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cutting">切割</SelectItem>
                        <SelectItem value="edging">磨边</SelectItem>
                        <SelectItem value="drilling">打孔</SelectItem>
                        <SelectItem value="tempering">钢化</SelectItem>
                        <SelectItem value="laminating">夹胶</SelectItem>
                        <SelectItem value="insulating">中空合片</SelectItem>
                        <SelectItem value="coating">镀膜</SelectItem>
                        <SelectItem value="quality_check">质检</SelectItem>
                        <SelectItem value="packaging">包装</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label class="text-xs">工作站</Label>
                    <Select v-model="step.workstation" @update:model-value="updateStep(step.id, 'workstation', $event)">
                      <SelectTrigger class="h-8">
                        <SelectValue placeholder="选择工作站" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cutting_station_1">切割工位1</SelectItem>
                        <SelectItem value="cutting_station_2">切割工位2</SelectItem>
                        <SelectItem value="edging_station_1">磨边工位1</SelectItem>
                        <SelectItem value="edging_station_2">磨边工位2</SelectItem>
                        <SelectItem value="tempering_furnace_1">钢化炉1</SelectItem>
                        <SelectItem value="tempering_furnace_2">钢化炉2</SelectItem>
                        <SelectItem value="laminating_line">夹胶生产线</SelectItem>
                        <SelectItem value="insulating_line">中空生产线</SelectItem>
                        <SelectItem value="quality_station">质检工位</SelectItem>
                        <SelectItem value="packaging_station">包装工位</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label class="text-xs">预估工时(分钟)</Label>
                    <Input 
                      type="number" 
                      :model-value="step.estimatedDuration"
                      @update:model-value="updateStep(step.id, 'estimatedDuration', parseInt($event) || 0)"
                      placeholder="0"
                      class="h-8"
                      min="0"
                    />
                  </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex-shrink-0 flex items-center gap-1">
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    @click="moveStepUp(index)"
                    :disabled="index === 0"
                    class="h-6 w-6 p-0"
                  >
                    <ChevronUp class="w-3 h-3" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    @click="moveStepDown(index)"
                    :disabled="index === processSteps.length - 1"
                    class="h-6 w-6 p-0"
                  >
                    <ChevronDown class="w-3 h-3" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    @click="removeStep(step.id)"
                    class="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  >
                    <Trash2 class="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <!-- 步骤约束条件 -->
              <div class="mt-3 pt-3 border-t">
                <Label class="text-xs">约束条件</Label>
                <div class="mt-1 flex flex-wrap gap-2">
                  <Badge 
                    v-for="constraint in step.constraints" 
                    :key="constraint"
                    variant="outline" 
                    class="text-xs"
                  >
                    {{ getConstraintText(constraint) }}
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      class="h-3 w-3 p-0 ml-1"
                      @click="removeConstraint(step.id, constraint)"
                    >
                      <X class="w-2 h-2" />
                    </Button>
                  </Badge>
                  
                  <Select @update:model-value="addConstraint(step.id, $event)">
                    <SelectTrigger class="w-32 h-6">
                      <SelectValue placeholder="添加约束" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="temperature_control">温度控制</SelectItem>
                      <SelectItem value="humidity_control">湿度控制</SelectItem>
                      <SelectItem value="clean_environment">洁净环境</SelectItem>
                      <SelectItem value="skilled_operator">熟练操作员</SelectItem>
                      <SelectItem value="quality_inspection">质量检验</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 工艺流程预览 -->
        <div class="mt-4 p-4 bg-blue-50 rounded-lg">
          <h4 class="font-medium text-blue-800 mb-2">工艺流程预览</h4>
          <div class="flex items-center gap-2 text-sm">
            <div 
              v-for="(step, index) in processSteps" 
              :key="step.id"
              class="flex items-center gap-2"
            >
              <div class="px-2 py-1 bg-white rounded border text-xs">
                {{ getStepDisplayName(step.stepName) }}
              </div>
              <ChevronRight v-if="index < processSteps.length - 1" class="w-3 h-3 text-blue-600" />
            </div>
          </div>
          
          <div class="mt-2 text-xs text-blue-600">
            总预估工时: {{ totalEstimatedTime }}分钟 ({{ Math.round(totalEstimatedTime / 60 * 10) / 10 }}小时)
          </div>
        </div>
      </div>
      
      <!-- 底部操作 -->
      <DialogFooter class="mt-4">
        <div class="flex items-center justify-between w-full">
          <div class="text-sm text-gray-600">
            {{ processSteps.length }}个工艺步骤，预估总工时 {{ totalEstimatedTime }}分钟
          </div>
          
          <div class="flex items-center gap-2">
            <Button variant="outline" @click="$emit('update:open', false)">
              取消
            </Button>
            <Button 
              @click="saveProcessFlow"
              :disabled="!isValidProcessFlow"
              class="bg-blue-600 hover:bg-blue-700"
            >
              <Save class="w-4 h-4 mr-1" />
              保存工艺流程
            </Button>
          </div>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Settings,
  Plus,
  ChevronUp,
  ChevronDown,
  ChevronRight,
  Trash2,
  X,
  Save,
} from 'lucide-vue-next'

import type { CustomerOrderItem } from '@/types/mes-validation'

interface ProcessStep {
  id: string
  stepName: string
  workstation: string
  estimatedDuration: number
  constraints: string[]
  status: 'pending' | 'in_progress' | 'completed'
}

interface Props {
  open: boolean
  orderItem: CustomerOrderItem | null
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'process-configured', orderItem: CustomerOrderItem, processFlow: ProcessStep[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const selectedTemplate = ref('')
const processSteps = ref<ProcessStep[]>([])

// 计算属性
const totalEstimatedTime = computed(() => {
  return processSteps.value.reduce((sum, step) => sum + step.estimatedDuration, 0)
})

const isValidProcessFlow = computed(() => {
  return processSteps.value.length > 0 && 
         processSteps.value.every(step => 
           step.stepName && step.workstation && step.estimatedDuration > 0
         )
})

// 监听订单项变化，初始化工艺流程
watch(() => props.orderItem, (newItem) => {
  if (newItem && props.open) {
    initializeProcessFlow(newItem)
  }
})

// 方法
const initializeProcessFlow = (orderItem: CustomerOrderItem) => {
  // 根据玻璃类型和规格推荐默认工艺流程
  const { glassType, thickness } = orderItem.specifications
  
  processSteps.value = []
  
  // 基础工艺步骤
  addStep('cutting', 'cutting_station_1', 15)
  addStep('edging', 'edging_station_1', 20)
  
  // 根据玻璃类型添加特殊工艺
  if (glassType === 'tempered' || thickness >= 6) {
    addStep('tempering', 'tempering_furnace_1', 45)
  }
  
  if (glassType === 'laminated') {
    addStep('laminating', 'laminating_line', 30)
  }
  
  if (glassType === 'insulating') {
    addStep('insulating', 'insulating_line', 25)
  }
  
  // 质检和包装
  addStep('quality_check', 'quality_station', 10)
  addStep('packaging', 'packaging_station', 5)
}

const addStep = (stepName: string, workstation: string, duration: number) => {
  const step: ProcessStep = {
    id: `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    stepName,
    workstation,
    estimatedDuration: duration,
    constraints: [],
    status: 'pending'
  }
  processSteps.value.push(step)
}

const addProcessStep = () => {
  addStep('', '', 0)
}

const updateStep = (stepId: string, field: keyof ProcessStep, value: any) => {
  const step = processSteps.value.find(s => s.id === stepId)
  if (step) {
    (step as any)[field] = value
  }
}

const removeStep = (stepId: string) => {
  const index = processSteps.value.findIndex(s => s.id === stepId)
  if (index > -1) {
    processSteps.value.splice(index, 1)
  }
}

const moveStepUp = (index: number) => {
  if (index > 0) {
    const step = processSteps.value.splice(index, 1)[0]
    processSteps.value.splice(index - 1, 0, step)
  }
}

const moveStepDown = (index: number) => {
  if (index < processSteps.value.length - 1) {
    const step = processSteps.value.splice(index, 1)[0]
    processSteps.value.splice(index + 1, 0, step)
  }
}

const addConstraint = (stepId: string, constraint: string) => {
  const step = processSteps.value.find(s => s.id === stepId)
  if (step && !step.constraints.includes(constraint)) {
    step.constraints.push(constraint)
  }
}

const removeConstraint = (stepId: string, constraint: string) => {
  const step = processSteps.value.find(s => s.id === stepId)
  if (step) {
    const index = step.constraints.indexOf(constraint)
    if (index > -1) {
      step.constraints.splice(index, 1)
    }
  }
}

const applyTemplate = (template: string) => {
  processSteps.value = []
  
  switch (template) {
    case 'standard_clear':
      addStep('cutting', 'cutting_station_1', 15)
      addStep('edging', 'edging_station_1', 20)
      addStep('quality_check', 'quality_station', 10)
      addStep('packaging', 'packaging_station', 5)
      break
      
    case 'tempered_clear':
      addStep('cutting', 'cutting_station_1', 15)
      addStep('edging', 'edging_station_1', 20)
      addStep('tempering', 'tempering_furnace_1', 45)
      addStep('quality_check', 'quality_station', 15)
      addStep('packaging', 'packaging_station', 5)
      break
      
    case 'laminated_clear':
      addStep('cutting', 'cutting_station_1', 15)
      addStep('edging', 'edging_station_1', 20)
      addStep('laminating', 'laminating_line', 30)
      addStep('quality_check', 'quality_station', 15)
      addStep('packaging', 'packaging_station', 8)
      break
      
    case 'insulating_clear':
      addStep('cutting', 'cutting_station_1', 15)
      addStep('edging', 'edging_station_1', 20)
      addStep('insulating', 'insulating_line', 25)
      addStep('quality_check', 'quality_station', 12)
      addStep('packaging', 'packaging_station', 8)
      break
      
    case 'low_e':
      addStep('cutting', 'cutting_station_1', 18)
      addStep('edging', 'edging_station_1', 25)
      addStep('coating', 'coating_line', 40)
      addStep('tempering', 'tempering_furnace_1', 50)
      addStep('quality_check', 'quality_station', 20)
      addStep('packaging', 'packaging_station', 10)
      break
  }
}

const saveProcessFlow = () => {
  if (props.orderItem && isValidProcessFlow.value) {
    emit('process-configured', props.orderItem, [...processSteps.value])
    emit('update:open', false)
  }
}

// 工具方法
const getStepDisplayName = (stepName: string): string => {
  const names: Record<string, string> = {
    cutting: '切割',
    edging: '磨边',
    drilling: '打孔',
    tempering: '钢化',
    laminating: '夹胶',
    insulating: '中空合片',
    coating: '镀膜',
    quality_check: '质检',
    packaging: '包装'
  }
  return names[stepName] || stepName
}

const getConstraintText = (constraint: string): string => {
  const texts: Record<string, string> = {
    temperature_control: '温度控制',
    humidity_control: '湿度控制',
    clean_environment: '洁净环境',
    skilled_operator: '熟练操作员',
    quality_inspection: '质量检验'
  }
  return texts[constraint] || constraint
}
</script>

<style scoped>
.process-step-card {
  transition: all 0.2s ease;
}

.process-step-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>