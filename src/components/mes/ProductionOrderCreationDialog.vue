<template>
  <Dialog v-model:open="isOpen" @update:open="handleOpenChange">
    <DialogContent class="max-w-7xl w-full h-full dialog-content-scroll p-0">
      <DialogHeader class="px-6 py-3 border-b flex-shrink-0">
        <DialogTitle class="flex items-center gap-2">
          <Factory class="w-5 h-5" />
          创建生产工单
        </DialogTitle>
      </DialogHeader>

      <!-- TAB导航 -->
      <div class="border-b bg-white flex-shrink-0">
        <div class="flex">
          <button
            class="tab-button"
            :class="{ 'active': currentStep === 1 }"
            @click="handleStepNavigation(1)"
          >
            <div class="flex items-center gap-2">
              <div class="tab-indicator" :class="{ 'completed': completedSteps.includes(1) }">
                <CheckCircle v-if="completedSteps.includes(1)" class="w-4 h-4" />
                <span v-else class="text-xs">1</span>
              </div>
              选择订单项
            </div>
          </button>
          <button
            class="tab-button"
            :class="{ 'active': currentStep === 2 }"
            @click="handleStepNavigation(2)"
            :disabled="!stepValidation[1]"
          >
            <div class="flex items-center gap-2">
              <div class="tab-indicator">
                <span class="text-xs">2</span>
              </div>
              优化批次配置
            </div>
          </button>
        </div>
      </div>

      <!-- TAB内容区域 -->
      <div class="flex-1 min-h-0 relative">
        <!-- 第一步：订单项选择 -->
        <Transition name="step-fade" mode="out-in">
          <div v-if="currentStep === 1" key="step-1" class="h-full">
            <StepOneOrderSelection
              :available-orders="state.availableOrders"
              :selected-order-items="state.selectedOrderItems"
              :search-query="state.searchQuery"
              :status-filter="state.statusFilter"
              :process-type-filter="state.processTypeFilter"
              :customer-filter="state.customerFilter"
              :loading="state.isLoading"
              @order-item-selected="handleOrderItemSelected"
              @order-item-removed="handleOrderItemRemoved"
              @quantity-changed="handleQuantityChanged"
              @search-changed="handleSearchChanged"
              @filter-changed="handleFilterChanged"
              @next-step="handleNextStep"
              @cancel="handleCancel"
            />
          </div>

          <!-- 第二步：批次优化配置 -->
          <div v-else-if="currentStep === 2" key="step-2" class="h-full">
            <StepTwoBatchOptimization
              :selected-order-items="state.selectedOrderItems"
              :batch-optimization="state.batchOptimization"
              :process-conflicts="state.processConflicts"
              :validation-results="state.validationResults"
              :work-order-priority="state.workOrderPriority"
              :planned-start-date="state.plannedStartDate"
              :estimated-end-date="state.estimatedEndDate"
              :schedule-recommendation="state.scheduleRecommendation"
              :loading="state.isLoading"
              :is-creating="state.isCreating"
              :can-create="canCreateOrder"
              @batch-modified="handleBatchModified"
              @conflict-resolved="handleConflictResolved"
              @optimization-requested="handleOptimizationRequested"
              @priority-changed="handlePriorityChanged"
              @schedule-changed="handleScheduleChanged"
              @prev-step="handlePrevStep"
              @create-order="handleCreateOrder"
            />
          </div>
        </Transition>

        <!-- 加载遮罩 -->
        <div
          v-if="state.isLoading"
          class="absolute inset-0 bg-white/80 flex items-center justify-center z-10"
        >
          <div class="flex items-center gap-3">
            <div
              class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"
            ></div>
            <span class="text-sm text-gray-600">{{ loadingMessage }}</span>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Factory } from "lucide-vue-next";

import { CheckCircle } from "lucide-vue-next";
import StepOneOrderSelection from "./StepOneOrderSelection.vue";
import StepTwoBatchOptimization from "./StepTwoBatchOptimization.vue";

import type {
  WorkOrderCreationState,
  SelectedOrderItem,
  BatchConfiguration,
  ValidationSummary,
  CreateWorkOrderRequest,
} from "@/types/production-order-creation";
import type { CustomerOrderItem } from "@/types/mes-validation";
import { mesService } from "@/services/mesService";

// Props
interface Props {
  open: boolean;
}

// Events
interface Emits {
  (e: "update:open", value: boolean): void;
  (e: "order-created", orderIds: string[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const isOpen = ref(props.open);
const loadingMessage = ref("加载中...");

// 两步式工作流状态
const currentStep = ref<1 | 2>(1);
const completedSteps = ref<number[]>([]);
const stepValidation = ref<Record<number, boolean>>({
  1: false,
  2: false
});

// 状态管理
const state = ref<WorkOrderCreationState>({
  // 基础数据
  availableOrders: [],
  selectedOrderItems: [],

  // 批次优化
  batchOptimization: null,
  processConflicts: [],

  // 配置参数
  workOrderPriority: "normal",
  plannedStartDate: new Date().toISOString().split("T")[0],
  customBatchConfig: {},

  // 智能推荐
  scheduleRecommendation: "",
  estimatedEndDate: "",
  validationResults: [],

  // 界面状态
  isLoading: false,
  isCreating: false,
  showConflictDetails: false,
  showBatchDetails: false,
  activeTab: "selection",
  errors: {},

  // 搜索和筛选
  searchQuery: "",
  statusFilter: "all",
  processTypeFilter: "all",
  customerFilter: "all",
});

// 计算属性
const canCreateOrder = computed(() => {
  return (
    state.value.selectedOrderItems.length > 0 &&
    !state.value.isCreating &&
    validationSummary.value.canProceed
  );
});

const validationSummary = computed((): ValidationSummary => {
  const results = state.value.validationResults;
  const errors = results.filter((r) => r.level === "error").length;
  const warnings = results.filter((r) => r.level === "warning").length;
  const infos = results.filter((r) => r.level === "info").length;

  return {
    totalIssues: results.length,
    errors,
    warnings,
    infos,
    canProceed: errors === 0,
    criticalIssues: results.filter((r) => r.level === "error"),
  };
});

// 事件处理
const handleOpenChange = (open: boolean) => {
  isOpen.value = open;
  emit("update:open", open);

  if (!open) {
    // 清理状态
    resetState();
  }
};

const handleOrderItemSelected = async (
  item: CustomerOrderItem,
  quantity: number
) => {
  // 查找对应的客户订单
  const customerOrder = state.value.availableOrders.find((order) =>
    order.items.some((orderItem) => orderItem.id === item.id)
  );

  // 添加选中的订单项
  const selectedItem: SelectedOrderItem = {
    id: item.id,
    customerOrderId: customerOrder?.id || "",
    orderNumber: customerOrder?.orderNumber || "",
    customerName: customerOrder?.customerName || "",
    specifications: item.specifications,
    totalQuantity: item.quantity,
    selectedQuantity: quantity,
    processFlow: (item as any).processFlow || [],
    deliveryDate: (customerOrder as unknown)?.deliveryDate || "",
    originalItem: item,
  };

  state.value.selectedOrderItems.push(selectedItem);

  // 触发批次优化
  await requestBatchOptimization();
};

const handleOrderItemRemoved = (itemId: string) => {
  const index = state.value.selectedOrderItems.findIndex(
    (item) => item.id === itemId
  );
  if (index > -1) {
    state.value.selectedOrderItems.splice(index, 1);
    // 重新优化批次
    requestBatchOptimization();
  }
};

const handleQuantityChanged = (itemId: string, quantity: number) => {
  const item = state.value.selectedOrderItems.find(
    (item) => item.id === itemId
  );
  if (item) {
    item.selectedQuantity = quantity;
    // 重新优化批次
    requestBatchOptimization();
  }
};

const handleSearchChanged = (query: string) => {
  state.value.searchQuery = query;
};

const handleFilterChanged = (filterType: string, value: string) => {
  switch (filterType) {
    case "status":
      state.value.statusFilter = value;
      break;
    case "processType":
      state.value.processTypeFilter = value;
      break;
    case "customer":
      state.value.customerFilter = value;
      break;
  }
};

const handleBatchModified = (batchId: string, config: BatchConfiguration) => {
  state.value.customBatchConfig[batchId] = config;
};

const handleConflictResolved = (conflictId: string, resolution: string) => {
  // 移除已解决的冲突
  const index = state.value.processConflicts.findIndex(
    (c) => c.id === conflictId
  );
  if (index > -1) {
    state.value.processConflicts.splice(index, 1);
  }
};

const handleOptimizationRequested = () => {
  requestBatchOptimization();
};

const handleBatchDetailsRequested = (batchId: string) => {
  state.value.showBatchDetails = true;
  // 这里可以显示批次详情对话框
};

const handleCreateOrder = async () => {
  if (!canCreateOrder.value) return;

  state.value.isCreating = true;
  loadingMessage.value = "创建工单中...";

  try {
    // 如果有批次优化结果，基于批次创建工单
    if (
      state.value.batchOptimization &&
      state.value.batchOptimization.batches.length > 0
    ) {
      const orderIds: string[] = [];

      // 为每个批次创建一个工单
      for (const batch of state.value.batchOptimization.batches) {
        const workOrderNumber = `WO-${new Date().getFullYear()}${String(Date.now()).slice(-6)}`;

        // 调用MES服务创建工单
        const newWorkOrder = await mesService.createProductionOrder({
          workOrderNumber,
          items: batch.items.map((item) => ({
            ...item.originalItem,
            quantity: item.selectedQuantity,
          })),
          priority: state.value.workOrderPriority,
          plannedStartDate: state.value.plannedStartDate,
          batchConfig: batch,
        });

        if (newWorkOrder) {
          orderIds.push(newWorkOrder.workOrderNumber);
        }
      }

      emit("order-created", orderIds);
    } else {
      // 没有批次优化，创建单个工单
      const workOrderNumber = `WO-${new Date().getFullYear()}${String(Date.now()).slice(-6)}`;

      const newWorkOrder = await mesService.createProductionOrder({
        workOrderNumber,
        items: state.value.selectedOrderItems.map((item) => ({
          ...item.originalItem,
          quantity: item.selectedQuantity,
        })),
        priority: state.value.workOrderPriority,
        plannedStartDate: state.value.plannedStartDate,
      });

      if (newWorkOrder) {
        emit("order-created", [newWorkOrder.workOrderNumber]);
      }
    }

    // 关闭对话框
    handleOpenChange(false);
  } catch (error) {
    console.error("创建工单失败:", error);
    // 显示错误信息
    alert("创建工单失败: " + (error as Error).message);
  } finally {
    state.value.isCreating = false;
  }
};

const handleSaveDraft = () => {
  // 保存草稿逻辑
  console.log("保存草稿");
};

const handleCancel = () => {
  handleOpenChange(false);
};

const handlePreviewOrder = () => {
  // 预览工单逻辑
  console.log("预览工单");
};

// 步骤导航处理
const handleStepNavigation = (step: 1 | 2) => {
  if (step === currentStep.value) return;
  
  // 验证当前步骤是否可以离开
  if (currentStep.value === 1 && !validateStepOne()) {
    return;
  }
  
  currentStep.value = step;
  
  // 如果进入第二步，自动触发批次优化
  if (step === 2 && state.value.selectedOrderItems.length > 0) {
    requestBatchOptimization();
  }
};

const handleNextStep = () => {
  if (currentStep.value === 1 && validateStepOne()) {
    completedSteps.value = [1];
    currentStep.value = 2;
    requestBatchOptimization();
  }
};

const handlePrevStep = () => {
  if (currentStep.value === 2) {
    currentStep.value = 1;
  }
};

const handlePriorityChanged = (priority: string) => {
  state.value.workOrderPriority = priority as any;
};

const handleScheduleChanged = (schedule: { startDate: string; endDate: string }) => {
  state.value.plannedStartDate = schedule.startDate;
  state.value.estimatedEndDate = schedule.endDate;
};

// 步骤验证
const validateStepOne = (): boolean => {
  const isValid = state.value.selectedOrderItems.length > 0;
  stepValidation.value[1] = isValid;
  return isValid;
};

const validateStepTwo = (): boolean => {
  const isValid = state.value.selectedOrderItems.length > 0 && 
                  state.value.workOrderPriority !== '' &&
                  state.value.plannedStartDate !== '';
  stepValidation.value[2] = isValid;
  return isValid;
};

// 工具方法
const resetState = () => {
  state.value = {
    availableOrders: [],
    selectedOrderItems: [],
    batchOptimization: null,
    processConflicts: [],
    workOrderPriority: "normal",
    plannedStartDate: new Date().toISOString().split("T")[0],
    customBatchConfig: {},
    scheduleRecommendation: "",
    estimatedEndDate: "",
    validationResults: [],
    isLoading: false,
    isCreating: false,
    showConflictDetails: false,
    showBatchDetails: false,
    activeTab: "selection",
    errors: {},
    searchQuery: "",
    statusFilter: "all",
    processTypeFilter: "all",
    customerFilter: "all",
  };
  
  // 重置步骤状态
  currentStep.value = 1;
  completedSteps.value = [];
  stepValidation.value = { 1: false, 2: false };
};

const requestBatchOptimization = async () => {
  if (state.value.selectedOrderItems.length === 0) {
    state.value.batchOptimization = null;
    return;
  }

  state.value.isLoading = true;
  loadingMessage.value = "优化批次中...";

  try {
    // 调用批次优化服务
    const { batchOptimizationService } = await import(
      "@/services/batchOptimizationService"
    );
    const optimization = await batchOptimizationService.optimizeBatches(
      state.value.selectedOrderItems
    );
    state.value.batchOptimization = optimization;
  } catch (error) {
    console.error("批次优化失败:", error);
    // 回退到基础结果
    const totalQuantity = state.value.selectedOrderItems.reduce(
      (sum, item) => sum + item.selectedQuantity,
      0
    );
    
    state.value.batchOptimization = {
      efficiency: 10,
      timeSaved: 1.5,
      batches: [{
        id: 'batch_1',
        name: '批次 1',
        items: state.value.selectedOrderItems,
        specifications: '混合规格',
        totalQuantity,
        estimatedDuration: Math.max(120, totalQuantity * 0.5), // 每片0.5分钟
        estimatedTime: Math.max(120, totalQuantity * 0.5),
        workstations: ['cutting_station_1', 'edging_station_1'],
        workstationGroup: '冷加工组',
        priority: 'normal',
        utilization: 75
      }],
      recommendations: ["建议合并相同规格的产品", "优化工序顺序可节省换线时间"],
      totalItems: state.value.selectedOrderItems.length,
      totalQuantity,
    };
  } finally {
    state.value.isLoading = false;
  }
};

const loadAvailableOrders = async () => {
  state.value.isLoading = true;
  loadingMessage.value = "加载订单数据...";

  try {
    // 调用MES服务加载可用订单
    const allOrders = await mesService.getCustomerOrders();
    // 只显示已确认且未转换为工单的客户订单
    state.value.availableOrders = allOrders.filter(
      (order) =>
        order.status === "confirmed" || order.status === "in_production"
    );
  } catch (error) {
    
    state.value.errors.loadOrders = "加载订单失败";
    // 回退到空数组
    state.value.availableOrders = [];
  } finally {
    state.value.isLoading = false;
  }
};

// 监听props变化
watch(
  () => props.open,
  (newValue) => {
    isOpen.value = newValue;
    if (newValue) {
      loadAvailableOrders();
    }
  }
);

// 监听选中项变化，实时验证步骤
watch(
  () => state.value.selectedOrderItems.length,
  () => {
    validateStepOne();
    validateStepTwo();
  }
);

// 监听工单配置变化
watch(
  [() => state.value.workOrderPriority, () => state.value.plannedStartDate],
  () => {
    validateStepTwo();
  }
);

// 生命周期
onMounted(() => {
  if (props.open) {
    loadAvailableOrders();
  }
});
</script>

<style scoped>
/* 步骤切换动画 */
.step-fade-enter-active,
.step-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-fade-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.step-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.step-fade-enter-to,
.step-fade-leave-from {
  opacity: 1;
  transform: translateX(0);
}

/* TAB样式 */
.tab-button {
  padding: 0.75rem 1.5rem;
  border-bottom: 2px solid transparent;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s ease;
  background: transparent;
  border-top: none;
  border-left: none;
  border-right: none;
  cursor: pointer;
}

.tab-button:hover:not(:disabled) {
  color: #374151;
  background-color: #f9fafb;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background-color: #f8fafc;
}

.tab-button:disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

.tab-indicator {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  background-color: #e5e7eb;
  color: #6b7280;
  transition: all 0.2s ease;
}

.tab-button.active .tab-indicator {
  background-color: #3b82f6;
  color: white;
}

.tab-indicator.completed {
  background-color: #10b981;
  color: white;
}

/* 确保步骤内容占满高度 */
.step-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
