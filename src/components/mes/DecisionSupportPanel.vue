<template>
  <div class="flex flex-col h-full">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="font-medium text-gray-900 flex items-center">
        <Brain class="h-4 w-4 mr-2" />
        决策支持面板
      </h3>
    </div>

    <div class="flex-1 overflow-y-auto custom-scrollbar">
      <div class="px-6 py-4 space-y-6">
        <!-- 关键指标卡片 -->
        <div>
          <h4 class="text-sm font-medium text-gray-800 mb-3">关键指标</h4>
          <div class="grid grid-cols-2 gap-3">
            <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg text-center">
              <div class="text-lg font-bold text-blue-700">{{ keyMetrics.feasibilityScore }}%</div>
              <div class="text-xs text-blue-600">可行性评分</div>
            </div>
            <div class="p-3 bg-green-50 border border-green-200 rounded-lg text-center">
              <div class="text-lg font-bold text-green-700">{{ keyMetrics.utilizationRate }}%</div>
              <div class="text-xs text-green-600">预计利用率</div>
            </div>
            <div class="p-3 bg-orange-50 border border-orange-200 rounded-lg text-center">
              <div class="text-lg font-bold text-orange-700">{{ keyMetrics.riskLevel }}</div>
              <div class="text-xs text-orange-600">风险等级</div>
            </div>
            <div class="p-3 bg-purple-50 border border-purple-200 rounded-lg text-center">
              <div class="text-lg font-bold text-purple-700">{{ keyMetrics.optimizationPotential }}%</div>
              <div class="text-xs text-purple-600">优化潜力</div>
            </div>
          </div>
        </div>

        <!-- 智能建议 -->
        <div>
          <h4 class="text-sm font-medium text-gray-800 mb-3 flex items-center">
            <Lightbulb class="h-4 w-4 mr-1" />
            智能建议
          </h4>
          <div class="space-y-2">
            <div v-for="suggestion in intelligentSuggestions" :key="suggestion.id" 
                 class="p-3 rounded-lg border" :class="getSuggestionClass(suggestion.priority)">
              <div class="flex items-start gap-2">
                <component :is="getSuggestionIcon(suggestion.type)" class="h-4 w-4 mt-0.5 flex-shrink-0" 
                          :class="getSuggestionIconClass(suggestion.priority)" />
                <div class="flex-1">
                  <div class="text-sm font-medium" :class="getSuggestionTextClass(suggestion.priority)">
                    {{ suggestion.title }}
                  </div>
                  <div class="text-xs mt-1" :class="getSuggestionDescClass(suggestion.priority)">
                    {{ suggestion.description }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 历史案例参考 -->
        <div>
          <h4 class="text-sm font-medium text-gray-800 mb-3 flex items-center">
            <History class="h-4 w-4 mr-1" />
            历史案例参考
          </h4>
          <div class="space-y-2">
            <div v-for="case_ in historicalCases" :key="case_.id" 
                 class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <div class="text-sm font-medium text-gray-900">{{ case_.title }}</div>
                <Badge variant="outline" size="sm">{{ case_.similarity }}%相似</Badge>
              </div>
              <div class="text-xs text-gray-600 mb-2">{{ case_.description }}</div>
              <div class="flex items-center gap-4 text-xs">
                <span class="text-green-600">利用率: {{ case_.utilizationRate }}%</span>
                <span class="text-blue-600">工期: {{ case_.duration }}天</span>
                <span class="text-purple-600">成本: ¥{{ case_.cost }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 专家建议 -->
        <div>
          <h4 class="text-sm font-medium text-gray-800 mb-3 flex items-center">
            <UserCheck class="h-4 w-4 mr-1" />
            专家建议
          </h4>
          <div class="space-y-2">
            <div v-for="advice in expertAdvice" :key="advice.id" 
                 class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div class="flex items-start gap-2">
                <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span class="text-xs font-medium text-blue-700">{{ advice.expert.charAt(0) }}</span>
                </div>
                <div class="flex-1">
                  <div class="text-sm font-medium text-blue-900">{{ advice.expert }}</div>
                  <div class="text-xs text-blue-700 mt-1">{{ advice.content }}</div>
                  <div class="text-xs text-blue-600 mt-1">{{ advice.timestamp }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div>
          <h4 class="text-sm font-medium text-gray-800 mb-3 flex items-center">
            <Zap class="h-4 w-4 mr-1" />
            快速操作
          </h4>
          <div class="grid grid-cols-1 gap-2">
            <Button variant="outline" size="sm" class="justify-start" @click="handleQuickAction('optimize')">
              <Settings class="h-4 w-4 mr-2" />
              一键优化切割方案
            </Button>
            <Button variant="outline" size="sm" class="justify-start" @click="handleQuickAction('schedule')">
              <Calendar class="h-4 w-4 mr-2" />
              智能排程建议
            </Button>
            <Button variant="outline" size="sm" class="justify-start" @click="handleQuickAction('risk')">
              <AlertTriangle class="h-4 w-4 mr-2" />
              风险评估报告
            </Button>
            <Button variant="outline" size="sm" class="justify-start" @click="handleQuickAction('contact')">
              <Phone class="h-4 w-4 mr-2" />
              联系相关人员
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Brain, 
  Lightbulb, 
  History, 
  UserCheck, 
  Zap,
  Settings,
  Calendar,
  AlertTriangle,
  Phone,
  TrendingUp,
  Clock,
  Target
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
  currentStep?: string;
}

const props = defineProps<Props>();

// 关键指标数据
const keyMetrics = ref({
  feasibilityScore: 85,
  utilizationRate: 92,
  riskLevel: '低',
  optimizationPotential: 15
});

// 智能建议数据
const intelligentSuggestions = ref([
  {
    id: 1,
    type: 'optimization',
    priority: 'high',
    title: '建议合并相似规格工单',
    description: '检测到2个相似规格工单，合并后可提升15%材料利用率'
  },
  {
    id: 2,
    type: 'scheduling',
    priority: 'medium',
    title: '调整钢化炉排程',
    description: '钢化炉当前负荷85%，建议将部分任务调整到明天执行'
  },
  {
    id: 3,
    type: 'inventory',
    priority: 'low',
    title: '原片库存预警',
    description: '6mm Low-E玻璃库存偏低，建议提前安排采购'
  }
]);

// 历史案例数据
const historicalCases = ref([
  {
    id: 1,
    title: '华润中心B座幕墙项目',
    description: '相似规格的大型幕墙项目，采用了工单合并优化策略',
    similarity: 87,
    utilizationRate: 94,
    duration: 3.5,
    cost: 125000
  },
  {
    id: 2,
    title: '万达广场玻璃幕墙',
    description: '类似的商业建筑项目，成功应用了智能排程算法',
    similarity: 82,
    utilizationRate: 89,
    duration: 4.2,
    cost: 98000
  }
]);

// 专家建议数据
const expertAdvice = ref([
  {
    id: 1,
    expert: '张工程师',
    content: '建议优先安排钢化工序，避免后续工期紧张',
    timestamp: '2小时前'
  },
  {
    id: 2,
    expert: '李主管',
    content: '该客户对质量要求较高，建议增加质检环节',
    timestamp: '4小时前'
  }
]);

// 样式辅助方法
const getSuggestionClass = (priority: string) => {
  switch (priority) {
    case 'high': return 'bg-red-50 border-red-200';
    case 'medium': return 'bg-orange-50 border-orange-200';
    case 'low': return 'bg-blue-50 border-blue-200';
    default: return 'bg-gray-50 border-gray-200';
  }
};

const getSuggestionIcon = (type: string) => {
  switch (type) {
    case 'optimization': return TrendingUp;
    case 'scheduling': return Clock;
    case 'inventory': return Target;
    default: return Lightbulb;
  }
};

const getSuggestionIconClass = (priority: string) => {
  switch (priority) {
    case 'high': return 'text-red-600';
    case 'medium': return 'text-orange-600';
    case 'low': return 'text-blue-600';
    default: return 'text-gray-600';
  }
};

const getSuggestionTextClass = (priority: string) => {
  switch (priority) {
    case 'high': return 'text-red-900';
    case 'medium': return 'text-orange-900';
    case 'low': return 'text-blue-900';
    default: return 'text-gray-900';
  }
};

const getSuggestionDescClass = (priority: string) => {
  switch (priority) {
    case 'high': return 'text-red-700';
    case 'medium': return 'text-orange-700';
    case 'low': return 'text-blue-700';
    default: return 'text-gray-700';
  }
};

// 快速操作处理
const handleQuickAction = (action: string) => {
  console.log(`执行快速操作: ${action}`);
  // 这里可以添加具体的操作逻辑
};

// 根据当前步骤更新建议
watch(() => props.currentStep, (newStep) => {
  // 根据不同步骤显示不同的建议
  console.log(`当前步骤: ${newStep}`);
});

onMounted(() => {
  // 初始化数据
});
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
