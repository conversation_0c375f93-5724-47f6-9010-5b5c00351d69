<template>
  <div class="cutting-layout-visualization">
    <!-- 可视化控制面板 -->
    <div class="mb-4 flex items-center justify-between">
      <div class="flex items-center gap-4">
        <h4 class="text-lg font-medium text-gray-900">切割布局方案</h4>
        <div class="flex items-center gap-2">
          <Button 
            v-for="(plan, index) in cuttingPlans" 
            :key="plan.planId"
            :variant="selectedPlanIndex === index ? 'default' : 'outline'"
            size="sm"
            @click="selectPlan(index)"
          >
            方案 {{ index + 1 }}
          </Button>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="toggleView">
          <RotateCcw class="h-4 w-4 mr-1" />
          {{ viewMode === '2d' ? '3D视图' : '2D视图' }}
        </Button>
        <Button variant="outline" size="sm" @click="exportLayout">
          <Download class="h-4 w-4 mr-1" />
          导出布局
        </Button>
      </div>
    </div>

    <!-- 当前方案信息 -->
    <div v-if="selectedPlan" class="mb-6 grid grid-cols-4 gap-4">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
        <div class="text-2xl font-bold text-blue-600">{{ selectedPlan.efficiency }}%</div>
        <div class="text-sm text-blue-700">材料利用率</div>
      </div>
      <div class="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
        <div class="text-2xl font-bold text-green-600">{{ selectedPlan.layout.pieces.length }}</div>
        <div class="text-sm text-green-700">切割件数</div>
      </div>
      <div class="bg-orange-50 border border-orange-200 rounded-lg p-3 text-center">
        <div class="text-2xl font-bold text-orange-600">{{ calculateWasteArea(selectedPlan) }}m²</div>
        <div class="text-sm text-orange-700">废料面积</div>
      </div>
      <div class="bg-purple-50 border border-purple-200 rounded-lg p-3 text-center">
        <div class="text-2xl font-bold text-purple-600">¥{{ calculateSavings(selectedPlan) }}</div>
        <div class="text-sm text-purple-700">成本节约</div>
      </div>
    </div>

    <!-- SVG 切割布局图 -->
    <div class="border border-gray-200 rounded-lg bg-white p-4">
      <div class="flex items-center justify-between mb-4">
        <h5 class="font-medium text-gray-900">原片切割布局</h5>
        <div class="flex items-center gap-2 text-sm text-gray-600">
          <span>原片规格: {{ rawSheetDimensions.length }}×{{ rawSheetDimensions.width }}mm</span>
          <span>|</span>
          <span>厚度: {{ rawSheetDimensions.thickness }}mm</span>
        </div>
      </div>

      <!-- SVG 绘图区域 -->
      <div class="relative bg-gray-50 rounded-lg overflow-hidden" :style="{ height: svgHeight + 'px' }">
        <svg 
          :width="svgWidth" 
          :height="svgHeight" 
          :viewBox="`0 0 ${svgWidth} ${svgHeight}`"
          class="w-full h-full"
        >
          <!-- 原片背景 -->
          <rect 
            x="20" 
            y="20" 
            :width="sheetDisplayWidth" 
            :height="sheetDisplayHeight"
            fill="#f8fafc" 
            stroke="#64748b" 
            stroke-width="2"
            rx="4"
          />
          
          <!-- 原片标注 -->
          <text 
            :x="20 + sheetDisplayWidth / 2" 
            y="15" 
            text-anchor="middle" 
            class="text-xs fill-gray-600 font-medium"
          >
            原片 {{ rawSheetDimensions.length }}×{{ rawSheetDimensions.width }}mm
          </text>

          <!-- 切割件 -->
          <g v-if="selectedPlan">
            <rect
              v-for="(piece, index) in selectedPlan.layout.pieces"
              :key="piece.pieceId"
              :x="20 + piece.position.x * scale"
              :y="20 + piece.position.y * scale"
              :width="piece.dimensions.length * scale"
              :height="piece.dimensions.width * scale"
              :fill="getPieceColor(piece, index)"
              :stroke="getPieceStrokeColor(piece)"
              stroke-width="1"
              class="cursor-pointer transition-all duration-200"
              @mouseenter="highlightPiece(piece)"
              @mouseleave="unhighlightPiece()"
              @click="selectPiece(piece)"
            />
            
            <!-- 切割件标签 -->
            <text
              v-for="(piece, index) in selectedPlan.layout.pieces"
              :key="`label-${piece.pieceId}`"
              :x="20 + (piece.position.x + piece.dimensions.length / 2) * scale"
              :y="20 + (piece.position.y + piece.dimensions.width / 2) * scale"
              text-anchor="middle"
              dominant-baseline="middle"
              class="text-xs fill-white font-medium pointer-events-none"
              :class="{ 'text-xs': piece.dimensions.length * scale < 60 }"
            >
              {{ piece.pieceId }}
            </text>
          </g>

          <!-- 废料区域 -->
          <g v-if="selectedPlan && showWasteAreas">
            <rect
              v-for="(waste, index) in calculateWasteAreas(selectedPlan)"
              :key="`waste-${index}`"
              :x="20 + waste.x * scale"
              :y="20 + waste.y * scale"
              :width="waste.width * scale"
              :height="waste.height * scale"
              fill="rgba(239, 68, 68, 0.2)"
              stroke="#ef4444"
              stroke-width="1"
              stroke-dasharray="4,4"
            />
          </g>

          <!-- 尺寸标注 -->
          <g class="dimension-lines">
            <!-- 水平尺寸线 -->
            <line 
              x1="20" 
              :y1="25 + sheetDisplayHeight" 
              :x2="20 + sheetDisplayWidth" 
              :y2="25 + sheetDisplayHeight"
              stroke="#6b7280" 
              stroke-width="1"
            />
            <text 
              :x="20 + sheetDisplayWidth / 2" 
              :y="40 + sheetDisplayHeight" 
              text-anchor="middle" 
              class="text-xs fill-gray-600"
            >
              {{ rawSheetDimensions.length }}mm
            </text>
            
            <!-- 垂直尺寸线 -->
            <line 
              :x1="25 + sheetDisplayWidth" 
              y1="20" 
              :x2="25 + sheetDisplayWidth" 
              :y2="20 + sheetDisplayHeight"
              stroke="#6b7280" 
              stroke-width="1"
            />
            <text 
              :x="35 + sheetDisplayWidth" 
              :y="20 + sheetDisplayHeight / 2" 
              text-anchor="middle" 
              dominant-baseline="middle"
              class="text-xs fill-gray-600"
              transform="rotate(90, ${35 + sheetDisplayWidth}, ${20 + sheetDisplayHeight / 2})"
            >
              {{ rawSheetDimensions.width }}mm
            </text>
          </g>
        </svg>

        <!-- 悬浮信息卡片 -->
        <div 
          v-if="hoveredPiece" 
          class="absolute bg-white border border-gray-200 rounded-lg shadow-lg p-3 pointer-events-none z-10"
          :style="{ left: tooltipPosition.x + 'px', top: tooltipPosition.y + 'px' }"
        >
          <div class="text-sm font-medium text-gray-900 mb-1">{{ hoveredPiece.pieceId }}</div>
          <div class="text-xs text-gray-600 space-y-1">
            <div>尺寸: {{ hoveredPiece.dimensions.length }}×{{ hoveredPiece.dimensions.width }}mm</div>
            <div>厚度: {{ hoveredPiece.dimensions.thickness }}mm</div>
            <div>客户: {{ hoveredPiece.customerName }}</div>
            <div>订单: {{ hoveredPiece.orderNumber }}</div>
          </div>
        </div>
      </div>

      <!-- 控制选项 -->
      <div class="mt-4 flex items-center justify-between">
        <div class="flex items-center gap-4">
          <label class="flex items-center gap-2 text-sm">
            <input 
              type="checkbox" 
              v-model="showWasteAreas"
              class="rounded border-gray-300"
            />
            显示废料区域
          </label>
          <label class="flex items-center gap-2 text-sm">
            <input 
              type="checkbox" 
              v-model="showDimensions"
              class="rounded border-gray-300"
            />
            显示尺寸标注
          </label>
        </div>
        
        <!-- 图例 -->
        <div class="flex items-center gap-4 text-xs">
          <div class="flex items-center gap-1">
            <div class="w-3 h-3 bg-blue-500 rounded"></div>
            <span>切割件</span>
          </div>
          <div class="flex items-center gap-1">
            <div class="w-3 h-3 bg-red-200 border border-red-400 border-dashed rounded"></div>
            <span>废料</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 切割件详情列表 -->
    <div class="mt-6 border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h5 class="font-medium text-gray-900">切割件详情</h5>
      </div>
      <div class="p-4">
        <div class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="border-b border-gray-200">
                <th class="text-left py-2">件号</th>
                <th class="text-left py-2">尺寸(mm)</th>
                <th class="text-left py-2">厚度</th>
                <th class="text-left py-2">客户</th>
                <th class="text-left py-2">订单号</th>
                <th class="text-left py-2">位置</th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="piece in selectedPlan?.layout.pieces || []" 
                :key="piece.pieceId"
                class="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                @click="highlightPieceInLayout(piece)"
              >
                <td class="py-2 font-medium">{{ piece.pieceId }}</td>
                <td class="py-2">{{ piece.dimensions.length }}×{{ piece.dimensions.width }}</td>
                <td class="py-2">{{ piece.dimensions.thickness }}mm</td>
                <td class="py-2">{{ piece.customerName }}</td>
                <td class="py-2">{{ piece.orderNumber }}</td>
                <td class="py-2">{{ piece.position.x }},{{ piece.position.y }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Button } from '@/components/ui/button';
import { RotateCcw, Download } from 'lucide-vue-next';

interface CuttingPlan {
  planId: string;
  efficiency: number;
  layout: {
    pieces: CuttingPiece[];
  };
}

interface CuttingPiece {
  pieceId: string;
  dimensions: {
    length: number;
    width: number;
    thickness: number;
  };
  position: {
    x: number;
    y: number;
  };
  customerName: string;
  orderNumber: string;
}

interface Props {
  cuttingResult?: {
    cuttingPlans: CuttingPlan[];
  };
}

const props = defineProps<Props>();

// 响应式状态
const selectedPlanIndex = ref(0);
const viewMode = ref<'2d' | '3d'>('2d');
const showWasteAreas = ref(true);
const showDimensions = ref(true);
const hoveredPiece = ref<CuttingPiece | null>(null);
const tooltipPosition = ref({ x: 0, y: 0 });

// 原片尺寸配置
const rawSheetDimensions = ref({
  length: 3300, // mm
  width: 2140,  // mm
  thickness: 6  // mm
});

// SVG 显示配置
const svgWidth = 800;
const svgHeight = 600;
const sheetDisplayWidth = 660; // 3300mm 按比例缩放
const sheetDisplayHeight = 428; // 2140mm 按比例缩放
const scale = sheetDisplayWidth / rawSheetDimensions.value.length;

// 计算属性
const cuttingPlans = computed(() => {
  return props.cuttingResult?.cuttingPlans || generateMockCuttingPlans();
});

const selectedPlan = computed(() => {
  return cuttingPlans.value[selectedPlanIndex.value];
});

// 生成模拟切割方案数据
const generateMockCuttingPlans = (): CuttingPlan[] => {
  return [
    {
      planId: 'plan_001',
      efficiency: 92.5,
      layout: {
        pieces: [
          {
            pieceId: 'P001',
            dimensions: { length: 1800, width: 1200, thickness: 6 },
            position: { x: 50, y: 50 },
            customerName: '华润置地',
            orderNumber: 'CO-2024-001'
          },
          {
            pieceId: 'P002', 
            dimensions: { length: 1400, width: 1000, thickness: 6 },
            position: { x: 1900, y: 50 },
            customerName: '万科集团',
            orderNumber: 'CO-2024-002'
          },
          {
            pieceId: 'P003',
            dimensions: { length: 1200, width: 800, thickness: 6 },
            position: { x: 50, y: 1300 },
            customerName: '保利地产',
            orderNumber: 'CO-2024-003'
          }
        ]
      }
    },
    {
      planId: 'plan_002',
      efficiency: 88.7,
      layout: {
        pieces: [
          {
            pieceId: 'P001',
            dimensions: { length: 2000, width: 1400, thickness: 6 },
            position: { x: 100, y: 100 },
            customerName: '华润置地',
            orderNumber: 'CO-2024-001'
          },
          {
            pieceId: 'P002',
            dimensions: { length: 1200, width: 600, thickness: 6 },
            position: { x: 2200, y: 100 },
            customerName: '万科集团', 
            orderNumber: 'CO-2024-002'
          }
        ]
      }
    }
  ];
};

// 方法
const selectPlan = (index: number) => {
  selectedPlanIndex.value = index;
};

const toggleView = () => {
  viewMode.value = viewMode.value === '2d' ? '3d' : '2d';
};

const exportLayout = () => {
  // 导出切割布局数据
  const layoutData = {
    plan: selectedPlan.value,
    rawSheet: rawSheetDimensions.value,
    exportTime: new Date().toISOString()
  };
  
  const blob = new Blob([JSON.stringify(layoutData, null, 2)], { 
    type: 'application/json' 
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `cutting-layout-${selectedPlan.value?.planId}.json`;
  a.click();
  URL.revokeObjectURL(url);
};

const getPieceColor = (piece: CuttingPiece, index: number): string => {
  const colors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
  ];
  return colors[index % colors.length];
};

const getPieceStrokeColor = (piece: CuttingPiece): string => {
  return '#1f2937';
};

const highlightPiece = (piece: CuttingPiece) => {
  hoveredPiece.value = piece;
};

const unhighlightPiece = () => {
  hoveredPiece.value = null;
};

const selectPiece = (piece: CuttingPiece) => {
  console.log('选中切割件:', piece);
};

const highlightPieceInLayout = (piece: CuttingPiece) => {
  // 在布局图中高亮显示选中的切割件
  highlightPiece(piece);
};

const calculateWasteArea = (plan: CuttingPlan): string => {
  const totalSheetArea = (rawSheetDimensions.value.length * rawSheetDimensions.value.width) / 1000000;
  const usedArea = plan.layout.pieces.reduce((sum, piece) => {
    return sum + (piece.dimensions.length * piece.dimensions.width) / 1000000;
  }, 0);
  return (totalSheetArea - usedArea).toFixed(2);
};

const calculateSavings = (plan: CuttingPlan): string => {
  // 基于利用率计算成本节约
  const baseCost = 450; // 原片基础成本
  const savings = baseCost * (plan.efficiency / 100 - 0.8) * 5; // 简化计算
  return Math.max(0, savings).toFixed(0);
};

const calculateWasteAreas = (plan: CuttingPlan) => {
  // 简化的废料区域计算
  return [
    { x: 50, y: 1800, width: 1200, height: 340 },
    { x: 2200, y: 1300, width: 1100, height: 840 }
  ];
};

onMounted(() => {
  console.log('切割布局可视化组件已加载');
});
</script>

<style scoped>
.cutting-layout-visualization {
  @apply w-full;
}

.dimension-lines text {
  font-family: 'Inter', sans-serif;
}

svg text {
  user-select: none;
}

.cursor-pointer:hover {
  filter: brightness(1.1);
}
</style>
