<template>
  <div class="quick-action-panel">
    <Card>
      <CardHeader>
        <CardTitle class="text-sm flex items-center gap-2">
          <Zap class="w-4 h-4" />
          快速操作
        </CardTitle>
      </CardHeader>
      <CardContent class="p-4">
        <div class="grid grid-cols-2 gap-3">
          <!-- 新建工单 -->
          <Button 
            @click="$emit('create-order')"
            class="h-16 flex flex-col items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700"
          >
            <Plus class="w-5 h-5" />
            <span class="text-xs">新建工单</span>
          </Button>
          
          <!-- 批量导入 -->
          <Button 
            @click="$emit('batch-import')"
            variant="outline"
            class="h-16 flex flex-col items-center justify-center gap-2"
          >
            <Upload class="w-5 h-5" />
            <span class="text-xs">批量导入</span>
          </Button>
          
          <!-- 工单模板 -->
          <Button 
            @click="$emit('use-template')"
            variant="outline"
            class="h-16 flex flex-col items-center justify-center gap-2"
          >
            <FileTextIcon class="w-5 h-5" />
            <span class="text-xs">工单模板</span>
          </Button>
          
          <!-- 快速复制 -->
          <Button 
            @click="$emit('quick-copy')"
            variant="outline"
            class="h-16 flex flex-col items-center justify-center gap-2"
          >
            <Copy class="w-5 h-5" />
            <span class="text-xs">快速复制</span>
          </Button>
        </div>
        
        <!-- 最近操作 -->
        <div class="mt-4 pt-4 border-t">
          <h4 class="text-xs font-medium text-gray-700 mb-2">最近操作</h4>
          <div class="space-y-2">
            <div 
              v-for="action in recentActions" 
              :key="action.id"
              class="flex items-center justify-between p-2 bg-gray-50 rounded text-xs hover:bg-gray-100 cursor-pointer"
              @click="$emit('repeat-action', action)"
            >
              <div class="flex items-center gap-2">
                <component :is="getActionIcon(action.type)" class="w-3 h-3 text-gray-500" />
                <span>{{ action.description }}</span>
              </div>
              <span class="text-gray-400">{{ formatTime(action.timestamp) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 快捷键提示 -->
        <div class="mt-4 pt-4 border-t">
          <h4 class="text-xs font-medium text-gray-700 mb-2">快捷键</h4>
          <div class="space-y-1 text-xs text-gray-600">
            <div class="flex items-center justify-between">
              <span>新建工单</span>
              <kbd class="px-1 py-0.5 bg-gray-200 rounded text-xs">Ctrl+N</kbd>
            </div>
            <div class="flex items-center justify-between">
              <span>搜索工单</span>
              <kbd class="px-1 py-0.5 bg-gray-200 rounded text-xs">Ctrl+F</kbd>
            </div>
            <div class="flex items-center justify-between">
              <span>刷新数据</span>
              <kbd class="px-1 py-0.5 bg-gray-200 rounded text-xs">F5</kbd>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Zap,
  Plus,
  Upload,
  FileTextIcon,
  Copy,
  FileText,
  Edit,
  Trash2,
} from 'lucide-vue-next'

interface RecentAction {
  id: string
  type: 'create' | 'edit' | 'delete' | 'copy'
  description: string
  timestamp: string
}

interface Props {
  recentActions?: RecentAction[]
}

interface Emits {
  (e: 'create-order'): void
  (e: 'batch-import'): void
  (e: 'use-template'): void
  (e: 'quick-copy'): void
  (e: 'repeat-action', action: RecentAction): void
}

const props = withDefaults(defineProps<Props>(), {
  recentActions: () => [
    {
      id: '1',
      type: 'create',
      description: '创建工单 WO-2024001',
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
    },
    {
      id: '2',
      type: 'edit',
      description: '编辑工单 WO-2024002',
      timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
    },
    {
      id: '3',
      type: 'copy',
      description: '复制工单 WO-2024003',
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
    }
  ]
})

defineEmits<Emits>()

// 工具方法
const getActionIcon = (type: string) => {
  switch (type) {
    case 'create': return Plus
    case 'edit': return Edit
    case 'delete': return Trash2
    case 'copy': return Copy
    default: return FileText
  }
}

const formatTime = (timestamp: string) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffMs = now.getTime() - time.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  
  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`
  return time.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.quick-action-panel {
  width: 280px;
}

kbd {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .quick-action-panel {
    width: 100%;
  }
}
</style>