<template>
  <div class="border rounded-lg bg-white shadow-sm order-selector-container">
    <!-- 订单头部 -->
    <div
      class="p-3 bg-gradient-to-r from-gray-50 to-blue-50/30 border-b rounded-t-lg"
    >
      <div class="flex items-start justify-between">
        <div class="flex items-start gap-3">
          <!-- 全选复选框 -->
          <Checkbox
            :checked="isOrderFullySelected"
            :indeterminate="isOrderPartiallySelected"
            @update:checked="handleOrderSelectAll"
            :disabled="!hasAvailableItems"
            class="mt-1"
          />
          <div class="flex-1">
            <!-- 订单基本信息 -->
            <div class="flex items-center gap-3 mb-2">
              <div class="font-semibold text-gray-900">
                {{ order.orderNumber }}
              </div>
              <Badge
                :variant="getOrderTypeVariant(order.orderType)"
                class="text-xs"
              >
                {{ order.orderType }}
              </Badge>
              <Badge
                :variant="getPriorityVariant(order.priority)"
                class="text-xs"
              >
                {{ getPriorityText(order.priority) }}
              </Badge>
            </div>

            <!-- 客户和项目信息 -->
            <div class="flex items-center gap-4 text-sm text-gray-700 mb-2">
              <div class="flex items-center gap-1">
                <Building2 class="w-4 h-4 text-gray-500" />
                <span class="font-medium">{{ order.customerName }}</span>
              </div>
              <div v-if="order.projectName" class="flex items-center gap-1">
                <FileText class="w-4 h-4 text-gray-500" />
                <span>{{ order.projectName }}</span>
              </div>
            </div>

            <!-- 订单统计和关键指标 -->
            <div class="flex items-center gap-6 text-xs">
              <div class="flex items-center gap-1 text-gray-600">
                <Package class="w-3 h-3" />
                <span>{{ order.items.length }}个产品项</span>
              </div>
              <div class="flex items-center gap-1 text-gray-600">
                <Layers class="w-3 h-3" />
                <span>{{ getTotalQuantity() }}片</span>
              </div>
              <div class="flex items-center gap-1 text-gray-600">
                <Tag class="w-3 h-3" />
                <span>{{ getUniqueProductFamilies().length }}种产品族</span>
              </div>
              <div class="flex items-center gap-1 text-gray-600">
                <Clock class="w-3 h-3" />
                <span>{{ getEstimatedDuration() }}工作日</span>
              </div>
              <div
                v-if="selectedItemsCount > 0"
                class="flex items-center gap-1 text-blue-600 font-medium"
              >
                <CheckCircle class="w-3 h-3" />
                <span
                  >已选 {{ selectedItemsCount }}/{{ order.items.length }}</span
                >
              </div>
            </div>
          </div>
        </div>
        <!-- 右侧状态和交期信息 -->
        <div class="text-right">
          <div class="flex items-center gap-2 mb-2">
            <Badge
              :variant="getOrderStatusVariant(order.status)"
              class="text-xs"
            >
              {{ getOrderStatusText(order.status) }}
            </Badge>
            <div
              v-if="isUrgentDelivery()"
              class="flex items-center gap-1 text-red-600"
            >
              <AlertTriangle class="w-3 h-3" />
              <span class="text-xs font-medium">紧急</span>
            </div>
          </div>
          <div class="text-xs text-gray-600">
            <div class="flex items-center gap-1">
              <Calendar class="w-3 h-3" />
              <span>交期: {{ formatDate((order as any).deliveryDate) }}</span>
            </div>
            <div class="mt-1 text-gray-500">
              {{ getDeliveryDaysLeft((order as any).deliveryDate) }}
            </div>
          </div>
        </div>
      </div>

      <!-- MTO模式特征展示 -->
      <div
        v-if="getMTOCharacteristics().length > 0"
        class="mt-3 pt-3 border-t border-gray-200"
      >
        <div class="flex items-center gap-2 text-xs">
          <span class="text-gray-600 font-medium">MTO特征:</span>
          <div class="flex items-center gap-2">
            <Badge
              v-for="characteristic in getMTOCharacteristics()"
              :key="characteristic.type"
              variant="outline"
              class="text-xs"
              :class="characteristic.class"
            >
              {{ characteristic.label }}
            </Badge>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单项表格 -->
    <div class="overflow-hidden">
      <!-- 表格头部 -->
      <div
        class="grid grid-cols-12 gap-2 px-3 py-2 bg-gray-100 text-xs font-medium text-gray-700 border-b"
      >
        <div class="col-span-1 flex items-center justify-center">选择</div>
        <div class="col-span-3">产品规格</div>
        <div class="col-span-2">产品族/类型</div>
        <div class="col-span-1 text-center">数量</div>
        <div class="col-span-1 text-center">面积</div>
        <div class="col-span-2">工艺流程</div>
        <div class="col-span-1 text-center">交期</div>
        <div class="col-span-1 text-center">状态</div>
      </div>

      <!-- 表格内容 -->
      <div class="divide-y">
        <div
          v-for="item in order.items"
          :key="item.id"
          class="grid grid-cols-12 gap-2 px-3 py-2 hover:bg-gray-50 transition-colors text-sm"
          :class="{
            'bg-blue-50 border-l-4 border-l-blue-500': isOrderItemSelected(
              item.id
            ),
            'opacity-50 bg-gray-100': !isOrderItemAvailable(item),
            'border-l-4 border-l-transparent': !isOrderItemSelected(item.id),
          }"
        >
          <!-- 选择框 -->
          <div class="col-span-1 flex items-center justify-center">
            <Checkbox
              :checked="isOrderItemSelected(item.id)"
              @update:checked="
                (checked) => handleOrderItemToggle(item, checked)
              "
              :disabled="!isOrderItemAvailable(item)"
            />
          </div>

          <!-- 产品规格 -->
          <div class="col-span-3">
            <div class="font-medium text-gray-900">
              {{ item.specifications.length }}×{{
                item.specifications.width
              }}×{{ item.specifications.thickness }}mm
            </div>
            <div class="text-xs text-gray-500 mt-1">
              {{ getGlassTypeDisplay(item.specifications.glassType) }} |
              {{ item.specifications.color }}
            </div>
          </div>

          <!-- 产品族/类型 -->
          <div class="col-span-2">
            <Badge
              :variant="getProductFamilyVariant(item)"
              class="text-xs mb-1"
            >
              {{ getProductFamilyName(item) }}
            </Badge>
            <div
              v-if="item.notes"
              class="flex items-center gap-1 text-amber-600 text-xs"
            >
              <AlertCircle class="w-3 h-3" />
              <span>特殊要求</span>
            </div>
          </div>

          <!-- 数量 -->
          <div class="col-span-1 text-center">
            <div class="font-medium text-gray-900">{{ item.quantity }}</div>
            <div class="text-xs text-gray-500">片</div>
          </div>

          <!-- 面积 -->
          <div class="col-span-1 text-center">
            <div class="font-medium text-gray-900">{{ getItemArea(item) }}</div>
            <div class="text-xs text-gray-500">m²</div>
          </div>

          <!-- 工艺流程 -->
          <div class="col-span-2">
            <div class="flex items-center gap-1 mb-1">
              <Settings class="w-3 h-3 text-gray-500" />
              <Badge variant="outline" class="text-xs">
                {{ (item as any).processFlow?.length || 0 }}道工序
              </Badge>
            </div>
            <div class="text-xs text-gray-600">
              <span
                v-for="(step, index) in (item as any).processFlow?.slice(0, 2)"
                :key="index"
              >
                {{ step.stepName
                }}<span
                  v-if="
                    index <
                    Math.min(1, ((item as any).processFlow?.length || 1) - 1)
                  "
                  class="text-gray-400 mx-1"
                  >→</span
                >
              </span>
              <span
                v-if="(item as any).processFlow?.length > 2"
                class="text-gray-500"
              >
                ...+{{ (item as any).processFlow.length - 2 }}
              </span>
            </div>
          </div>

          <!-- 交期 -->
          <div class="col-span-1 text-center">
            <div class="text-xs text-gray-900">
              {{ formatShortDate(item.deliveryDate) }}
            </div>
            <div
              class="text-xs"
              :class="getDeliveryUrgencyClass(item.deliveryDate)"
            >
              {{ getDeliveryDaysLeft(item.deliveryDate) }}
            </div>
          </div>

          <!-- 状态 -->
          <div class="col-span-1 text-center">
            <div class="flex flex-col items-center gap-1">
              <!-- 可用性状态 -->
              <div
                v-if="getAvailableQuantity(item.id) < item.quantity"
                class="flex items-center gap-1 text-yellow-600 text-xs"
              >
                <AlertTriangle class="w-3 h-3" />
                <span>{{ getAvailableQuantity(item.id) }}</span>
              </div>

              <!-- 工艺兼容性 -->
              <div v-if="isOrderItemSelected(item.id)" class="text-xs">
                <div
                  v-if="getProcessCompatibility(item.id) === 'compatible'"
                  class="flex items-center gap-1 text-green-600"
                >
                  <CheckCircle class="w-3 h-3" />
                </div>
                <div
                  v-else-if="getProcessCompatibility(item.id) === 'warning'"
                  class="flex items-center gap-1 text-yellow-600"
                >
                  <AlertTriangle class="w-3 h-3" />
                </div>
                <div
                  v-else-if="getProcessCompatibility(item.id) === 'conflict'"
                  class="flex items-center gap-1 text-red-600"
                >
                  <XCircle class="w-3 h-3" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Settings,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Building2,
  FileText,
  Layers,
  Tag,
  Clock,
  Calendar,
  AlertCircle,
} from "lucide-vue-next";

import type { CustomerOrder, CustomerOrderItem } from "@/types/mes-validation";
import type { SelectedOrderItem } from "@/types/production-order-creation";

// Props
interface Props {
  order: CustomerOrder;
  selectedOrderItems: SelectedOrderItem[];
  conflictingItems?: string[];
  unavailableItems?: Record<string, string>;
}

// Events
interface Emits {
  (e: "order-item-selected", item: CustomerOrderItem, quantity: number): void;
  (e: "order-item-removed", itemId: string): void;
  (e: "quantity-changed", itemId: string, quantity: number): void;
  (e: "batch-optimization-requested", orderItems: CustomerOrderItem[]): void;
  (e: "configure-process", item: CustomerOrderItem): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性
const selectedItemsCount = computed(() => {
  return props.order.items.filter((item) => isOrderItemSelected(item.id))
    .length;
});

const isOrderFullySelected = computed(() => {
  const availableItems = props.order.items.filter((item) =>
    isOrderItemAvailable(item)
  );
  return (
    availableItems.length > 0 &&
    availableItems.every((item) => isOrderItemSelected(item.id))
  );
});

const isOrderPartiallySelected = computed(() => {
  const selectedCount = selectedItemsCount.value;
  return (
    selectedCount > 0 &&
    selectedCount <
      props.order.items.filter((item) => isOrderItemAvailable(item)).length
  );
});

const hasAvailableItems = computed(() => {
  return props.order.items.some((item) => isOrderItemAvailable(item));
});

// 事件处理
const handleOrderSelectAll = (checked: boolean) => {
  const availableItems = props.order.items.filter((item) =>
    isOrderItemAvailable(item)
  );

  if (checked) {
    // 全选
    availableItems.forEach((item) => {
      if (!isOrderItemSelected(item.id)) {
        emit("order-item-selected", item, getAvailableQuantity(item.id));
      }
    });
  } else {
    // 取消全选
    availableItems.forEach((item) => {
      if (isOrderItemSelected(item.id)) {
        emit("order-item-removed", item.id);
      }
    });
  }
};

const handleOrderItemToggle = (item: CustomerOrderItem, checked: boolean) => {
  if (checked) {
    const defaultQuantity = Math.min(item.quantity, 100); // 默认选择100片或全部
    emit("order-item-selected", item, defaultQuantity);
  } else {
    emit("order-item-removed", item.id);
  }
};

// 工具方法
const isOrderItemSelected = (itemId: string): boolean => {
  return props.selectedOrderItems.some((item) => item.id === itemId);
};

const isOrderItemAvailable = (item: CustomerOrderItem): boolean => {
  // 检查是否在不可用列表中
  if (props.unavailableItems?.[item.id]) {
    return false;
  }

  // 检查是否有工艺流程定义
  const processFlow = (item as any).processFlow;
  if (!processFlow || processFlow.length === 0) {
    return false; // 未定义工艺流程的订单项不可选择
  }

  // 检查工艺流程是否完整
  const hasValidProcess = processFlow.every(
    (step: any) => step.stepName && step.workstation && step.estimatedDuration
  );

  if (!hasValidProcess) {
    return false; // 工艺流程不完整的订单项不可选择
  }

  return true;
};

const getAvailableQuantity = (itemId: string): number => {
  const item = props.order.items.find((item) => item.id === itemId);
  return item?.quantity || 0;
};

const getProcessCompatibility = (
  itemId: string
): "compatible" | "warning" | "conflict" => {
  if (props.conflictingItems?.includes(itemId)) {
    return "conflict";
  }

  // 简单的兼容性检查逻辑
  const selectedItems = props.selectedOrderItems;
  if (selectedItems.length <= 1) return "compatible";

  const currentItem = selectedItems.find((item) => item.id === itemId);
  if (!currentItem) return "compatible";

  const otherItems = selectedItems.filter((item) => item.id !== itemId);
  const hasProcessDifference = otherItems.some(
    (other) =>
      other.processFlow.length !== currentItem.processFlow.length ||
      other.processFlow.some(
        (step, index) =>
          step.stepName !== currentItem.processFlow[index]?.stepName
      )
  );

  return hasProcessDifference ? "warning" : "compatible";
};

const getOrderStatusVariant = (status: string) => {
  switch (status) {
    case "confirmed":
      return "default";
    case "ready":
      return "secondary";
    case "partial":
      return "outline";
    default:
      return "secondary";
  }
};

const getOrderStatusText = (status: string): string => {
  switch (status) {
    case "confirmed":
      return "已确认";
    case "ready":
      return "待转换";
    case "partial":
      return "部分转换";
    default:
      return status;
  }
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("zh-CN");
};

// 新增的辅助函数

// 获取订单类型标签样式
const getOrderTypeVariant = (orderType: string) => {
  const typeMap = {
    幕墙工程: "default",
    门窗工程: "secondary",
    办公隔断: "outline",
    家具玻璃: "destructive",
    装饰玻璃: "default",
    阳光房: "secondary",
    展示柜: "outline",
    特殊工艺: "destructive",
  };
  return typeMap[orderType] || "outline";
};

// 获取优先级标签样式
const getPriorityVariant = (priority: string) => {
  const priorityMap = {
    urgent: "destructive",
    high: "default",
    medium: "secondary",
    low: "outline",
  };
  return priorityMap[priority] || "outline";
};

// 获取优先级文本
const getPriorityText = (priority: string): string => {
  const priorityMap = {
    urgent: "紧急",
    high: "高",
    medium: "中",
    low: "低",
  };
  return priorityMap[priority] || priority;
};

// 获取订单总数量
const getTotalQuantity = (): number => {
  return props.order.items.reduce((sum, item) => sum + item.quantity, 0);
};

// 获取唯一产品族
const getUniqueProductFamilies = (): string[] => {
  const families = new Set(
    props.order.items.map((item) => (item as any).productFamilyId || "unknown")
  );
  return Array.from(families);
};

// 获取预计工期
const getEstimatedDuration = (): string => {
  const maxDuration = Math.max(
    ...props.order.items.map((item) => {
      const processFlow = (item as any).processFlow || [];
      return processFlow.reduce(
        (sum: number, step: any) => sum + (step.estimatedDuration || 0),
        0
      );
    })
  );

  const days = Math.ceil(maxDuration / (8 * 60)); // 假设每天8小时工作
  return `${days}`;
};

// 检查是否紧急交期
const isUrgentDelivery = (): boolean => {
  const deliveryDate = new Date((props.order as any).deliveryDate);
  const now = new Date();
  const daysLeft = Math.ceil(
    (deliveryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
  );
  return daysLeft <= 7; // 7天内交期视为紧急
};

// 获取MTO模式特征
const getMTOCharacteristics = () => {
  const characteristics = [];

  // 个性化规格
  const uniqueSpecs = new Set(
    props.order.items.map(
      (item) =>
        `${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}`
    )
  );
  if (uniqueSpecs.size > 1) {
    characteristics.push({
      type: "custom_specs",
      label: "多规格定制",
      class: "text-blue-600 border-blue-300",
    });
  }

  // 特殊工艺
  const hasSpecialProcess = props.order.items.some((item) =>
    (item as any).processFlow?.some((step: any) =>
      ["镀膜", "夹胶", "钢化", "表面处理"].includes(step.stepName)
    )
  );
  if (hasSpecialProcess) {
    characteristics.push({
      type: "special_process",
      label: "特殊工艺",
      class: "text-purple-600 border-purple-300",
    });
  }

  // 大批量
  const totalQty = getTotalQuantity();
  if (totalQty > 500) {
    characteristics.push({
      type: "large_batch",
      label: "大批量",
      class: "text-green-600 border-green-300",
    });
  }

  // 紧急订单
  if ((props.order as any).priority === "urgent") {
    characteristics.push({
      type: "urgent",
      label: "紧急订单",
      class: "text-red-600 border-red-300",
    });
  }

  return characteristics;
};

// 获取产品族标签样式
const getProductFamilyVariant = (item: CustomerOrderItem) => {
  const familyId = (item as any).productFamilyId;
  const variantMap = {
    "PF-TEMPERED": "default",
    "PF-IGU": "secondary",
    "PF-LAMINATED": "destructive",
    "PF-DECORATIVE": "outline",
    "PF-FURNITURE": "default",
  };
  return variantMap[familyId] || "outline";
};

// 获取产品族名称
const getProductFamilyName = (item: CustomerOrderItem): string => {
  const familyId = (item as any).productFamilyId;
  const nameMap = {
    "PF-TEMPERED": "单片钢化",
    "PF-IGU": "中空玻璃",
    "PF-LAMINATED": "夹胶玻璃",
    "PF-DECORATIVE": "装饰玻璃",
    "PF-FURNITURE": "家具玻璃",
  };
  return nameMap[familyId] || "其他";
};

// 获取玻璃类型显示名称
const getGlassTypeDisplay = (glassType: string): string => {
  const displayMap = {
    clear: "白玻",
    low_e: "Low-E",
    tinted: "茶玻",
    reflective: "镀膜",
    laminated: "夹胶",
    tempered: "钢化",
  };
  return displayMap[glassType] || glassType;
};

// 计算工单项面积
const getItemArea = (item: CustomerOrderItem): string => {
  const area =
    (item.specifications.length * item.specifications.width) / 1000000; // 转换为平方米
  return (area * item.quantity).toFixed(2);
};

// 格式化短日期
const formatShortDate = (dateString: string): string => {
  const date = new Date(dateString);
  return `${date.getMonth() + 1}/${date.getDate()}`;
};

// 获取交期紧急程度样式
const getDeliveryUrgencyClass = (deliveryDate: string): string => {
  const delivery = new Date(deliveryDate);
  const now = new Date();
  const daysLeft = Math.ceil(
    (delivery.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (daysLeft < 0) return "text-red-600"; // 已逾期
  if (daysLeft <= 3) return "text-red-600"; // 3天内
  if (daysLeft <= 7) return "text-yellow-600"; // 7天内
  return "text-gray-900"; // 正常
};

// 获取交期剩余天数（单个工单项）
const getDeliveryDaysLeft = (deliveryDate: string): string => {
  const delivery = new Date(deliveryDate);
  const now = new Date();
  const daysLeft = Math.ceil(
    (delivery.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (daysLeft < 0) return `逾期${Math.abs(daysLeft)}天`;
  if (daysLeft === 0) return "今日";
  if (daysLeft === 1) return "明日";
  if (daysLeft <= 7) return `${daysLeft}天`;
  return `${daysLeft}天`;
};
</script>
