<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[98vw] h-[96vh] max-w-none flex flex-col p-0">
      <!-- 对话框头部 -->
      <DialogHeader class="flex-shrink-0 border-b px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <DialogTitle class="flex items-center gap-2 text-lg font-semibold">
              <Settings class="h-5 w-5 text-blue-600" />
              生产发布工作台
            </DialogTitle>
            <DialogDescription class="text-sm text-gray-600 mt-1">
              {{ workOrder?.workOrderNumber }} - {{ workOrder?.customerName }} | 工单发布前的决策支持与优化分析
            </DialogDescription>
          </div>

          <!-- 生产发布步骤 - 动态4步骤进度条 -->
          <div class="hidden lg:flex items-center gap-4 ml-6">
            <div class="flex items-center gap-2 release-steps">
              <div
                v-for="(step, index) in releaseSteps"
                :key="step.key"
                class="flex items-center"
              >
                <div
                  class="flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium cursor-pointer transition-colors step-circle"
                  :class="getStepClass(step.key)"
                  @click="switchToStep(step.key)"
                  :title="step.name"
                  :data-guide-target="`step-${step.key}`"
                >
                  <component :is="step.icon" class="h-3 w-3" />
                </div>
                <div
                  v-if="index < releaseSteps.length - 1"
                  class="w-8 h-0.5 mx-1 transition-colors"
                  :class="isStepCompleted(step.key) ? 'bg-green-500' : 'bg-gray-300'"
                ></div>
              </div>
            </div>
            <div class="ml-3 text-xs">
              <div class="font-medium text-blue-900">{{ getCurrentStepText() }}</div>
              <div class="text-blue-600">{{ getCurrentStepStatus() }}</div>
            </div>

            <!-- 帮助按钮 -->
            <Button
              variant="ghost"
              size="sm"
              @click="startUserGuide"
              class="ml-2"
              title="查看操作指南"
            >
              <HelpCircle class="h-4 w-4" />
            </Button>
          </div>
        </div>
      </DialogHeader>

      <div v-if="!workOrder" class="flex-1 flex items-center justify-center py-8">
        <div class="text-center text-gray-500">
          <Package class="h-12 w-12 mx-auto mb-2 text-gray-300" />
          <p>工单信息加载中...</p>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div v-else class="flex-1 overflow-y-auto custom-scrollbar flex flex-col">
        <!-- 工单基本信息 - 改为单行水平布局 -->
        <div class="flex-shrink-0 px-6 py-3 border-b bg-gray-50">
          <div class="flex flex-wrap items-center gap-x-8 gap-y-2 text-sm">
            <div class="flex items-center gap-2">
              <span class="text-gray-500">工单号:</span>
              <span class="font-medium">{{ workOrder.workOrderNumber }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">客户名称:</span>
              <span class="font-medium">{{ workOrder.customerName }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">工单项数:</span>
              <span class="font-medium text-blue-600">{{ workOrder.items?.length || 0 }}项</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">计划交期:</span>
              <span class="font-medium">{{ formatDate(workOrder.plannedEndDate) }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">优先级:</span>
              <span class="font-medium" :class="getPriorityClass(workOrder.priority)">{{ getPriorityText(workOrder.priority) }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">当前状态:</span>
              <span class="font-medium" :class="getStatusClass(workOrder.status)">{{ getStatusText(workOrder.status) }}</span>
            </div>
          </div>
        </div>



        <!-- 主工作区域 -->
        <div class="flex-1">
          <div class="grid grid-cols-1 gap-0">
            <!-- 左侧：工单详情和阶段操作 -->
            <div class="flex flex-col">
              <!-- 资源状态总览 -->
              <!-- <div class="flex-shrink-0 border-b border-gray-200">
                <ResourceStatusOverview :work-order="workOrder" />
              </div> -->

              <!-- 工单详情 - 只在第一步显示 -->
              <div v-if="currentStep === 'review'" class="flex-shrink-0 border-b border-gray-200">
                <div class="px-6 py-4">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="font-medium text-gray-900">工单详情</h3>
                    <div class="flex items-center gap-2 batch-controls">
                      <button
                        @click="expandAllBatches()"
                        class="text-xs px-3 py-1 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors expand-button"
                        title="展开所有批次查看详细信息"
                      >
                        全部展开
                      </button>
                      <button
                        @click="collapseAllBatches()"
                        class="text-xs px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                      >
                        全部收起
                      </button>
                    </div>
                  </div>
                  
                  <!-- 步骤说明 -->
                  <!-- <div class="mb-4 p-3 rounded-lg border" :class="getStepNoticeClass()">
                    <div class="flex items-center gap-2 mb-1">
                      <component :is="getCurrentStepIcon()" class="h-4 w-4" />
                      <span class="font-medium text-sm">{{ getCurrentStepNoticeTitle() }}</span>
                    </div>
                    <p class="text-xs" :class="getStepNoticeTextClass()">{{ getCurrentStepNoticeText() }}</p>
                  </div> -->
                  
                  <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                      <thead class="sticky top-0 bg-white">
                        <tr class="border-b">
                          <th class="text-left py-2 w-8"></th>
                          <th class="text-left py-2">批次/产品规格</th>
                          <th class="text-left py-2">产品信息</th>
                          <th class="text-left py-2">订单来源</th>
                          <th class="text-left py-2">数量(片)</th>
                          <th class="text-left py-2">总面积</th>
                          <th class="text-left py-2">要求交期</th>
                          <th class="text-left py-2">工艺流程</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- 加载状态 -->
                        <tr v-if="isLoadingItems" class="border-b">
                          <td colspan="8" class="py-8 text-center text-gray-500">
                            <div class="flex items-center justify-center gap-2">
                              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                              加载工单详情中...
                            </div>
                          </td>
                        </tr>

                        <!-- 错误状态 -->
                        <tr v-else-if="loadError" class="border-b">
                          <td colspan="8" class="py-8 text-center text-red-500">
                            {{ loadError }}
                          </td>
                        </tr>

                        <!-- 无数据状态 -->
                        <tr v-else-if="workOrderItems.length === 0" class="border-b">
                          <td colspan="8" class="py-8 text-center text-gray-500">
                            暂无工单详情数据
                          </td>
                        </tr>

                        <!-- 层级数据行 -->
                        <template v-else v-for="(batch, batchIndex) in getProductionBatches()" :key="batch.id">
                          <!-- 批次头部行 -->
                          <tr class="border-b bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100 batch-header" :data-batch-id="batch.id">
                            <td class="py-3 pl-2">
                              <button 
                                @click="toggleBatchExpansion(batch.id)"
                                class="flex items-center justify-center w-5 h-5 rounded-sm border border-purple-300 hover:bg-purple-100 transition-colors"
                                :title="expandedBatches.has(batch.id) ? '收起批次详情' : '展开批次详情'"
                              >
                                <ChevronDown 
                                  class="h-3 w-3 text-purple-600 transition-transform"
                                  :class="{ 'transform rotate-180': !expandedBatches.has(batch.id) }"
                                />
                              </button>
                            </td>
                            <td class="py-3">
                              <div class="flex items-center gap-3">
                                <div 
                                  class="w-3 h-3 rounded-full flex-shrink-0" 
                                  :style="{ backgroundColor: batchColors[batchIndex % batchColors.length] }"
                                ></div>
                                <div>
                                  <div class="font-semibold text-purple-700">批次-{{ batchIndex + 1 }}</div>
                                  <div class="text-xs text-gray-600 mt-1">
                                    {{ batch.specifications.length }}种规格 • {{ batch.customerOrders.length }}个订单
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td class="py-3 text-center text-gray-400">
                              <span class="text-xs">展开查看详情</span>
                            </td>
                            <td class="py-3 text-center text-gray-400">
                              <span class="text-xs">展开查看详情</span>
                            </td>
                            <td class="py-3">
                              <div class="font-semibold text-lg text-blue-600">{{ batch.totalQuantity }}</div>
                              <div class="text-xs text-gray-500">总片数</div>
                            </td>
                            <td class="py-3">
                              <div class="text-xs">
                                <div class="font-medium text-blue-600">{{ calculateBatchTotalArea(batch) }} m²</div>
                                <div class="text-gray-500">总面积</div>
                              </div>
                            </td>
                            <td class="py-3">
                              <div class="text-xs">
                                <div class="font-medium text-orange-600">{{ getBatchEarliestDeadline(batch) }}</div>
                                <div class="text-gray-500">最早交期</div>
                              </div>
                            </td>
                            <td class="py-3">
                              <!-- <div class="text-xs">
                                <div class="font-medium">{{ batch.specifications.length }}种规格</div>
                                <div class="text-gray-500">混合工艺</div>
                              </div> -->
                            </td>
                          </tr>

                          <!-- 批次内的产品规格详情行 -->
                          <template v-if="expandedBatches.has(batch.id)">
                            <tr v-for="item in batch.items" :key="item.id" 
                                class="border-b bg-gray-50 hover:bg-gray-100">
                              <td class="py-2 pl-8">
                                <div class="w-4 h-4 flex items-center justify-center">
                                  <div class="w-1 h-1 bg-gray-400 rounded-full"></div>
                                </div>
                              </td>
                              <td class="py-2">
                                <div class="pl-4">
                                  <div class="font-medium text-gray-800">
                                    {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                                    {{ getGlassTypeName(item.specifications.glassType) }}
                                  </div>
                                  <div class="text-xs text-gray-500 mt-1">
                                    规格: {{ item.specifications.length }}×{{ item.specifications.width }}mm
                                  </div>
                                </div>
                              </td>
                              <td class="py-2">
                                <div class="pl-4">
                                  <div class="font-medium text-gray-800">
                                    <button 
                                      @click="viewProductBOM(item.productId, item.productName)"
                                      class="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer transition-colors"
                                      :title="`点击查看${item.productName}的BOM信息`"
                                    >
                                      {{ item.productName }}
                                    </button>
                                  </div>
                                  <div class="text-xs text-gray-500 mt-1">
                                    产品代码: {{ item.productCode }}
                                  </div>
                                  <div class="text-xs text-gray-500">
                                    产品族: {{ item.productFamilyName }}
                                  </div>
                                </div>
                              </td>
                              <td class="py-2">
                                <div class="pl-4">
                                  <div class="font-medium text-blue-600">
                                    <button 
                                      @click="viewCustomerOrder(item.customerOrderItemId)"
                                      class="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer transition-colors"
                                      :title="`点击查看客户订单详情`"
                                    >
                                      {{ getCustomerOrderNumber(item.customerOrderItemId) }}
                                    </button>
                                  </div>
                                  <div class="text-xs text-gray-500 mt-1">
                                    订单项: {{ item.customerOrderItemId }}
                                  </div>
                                </div>
                              </td>
                              <td class="py-2">
                                <span class="font-medium">{{ item.quantity }}</span>
                                <span class="text-xs text-gray-500 ml-1">片</span>
                              </td>
                              <td class="py-2">
                                <div class="text-xs">
                                  <div class="font-medium text-blue-600">{{ calculateItemArea(item) }} m²</div>
                                  <!-- <div class="text-gray-500">单件面积</div> -->
                                </div>
                              </td>
                              <td class="py-2">
                                <div class="text-xs">
                                  <div class="font-medium text-orange-600">{{ getItemDeadline(item) }}</div>
                                  <!-- <div class="text-gray-500">要求交期</div> -->
                                </div>
                              </td>
                              <td class="py-2">
                                <div class="text-xs">
                                  {{ item.processFlow.map(step => step.stepName).join(' → ') }}
                                </div>
                              </td>
                            </tr>
                          </template>
                        </template>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <!-- 阶段操作面板 - 动态切换 -->
              <div class="flex-1">
                <div class="px-6 py-4 border-b border-gray-200">
                  <div class="flex items-center justify-between">
                    <h3 class="font-medium text-gray-900">{{ getCurrentStepText() }} - 操作面板</h3>
                    <!-- 工单概要信息 - 非第一步显示 -->
                    <div v-if="currentStep !== 'review'" class="flex items-center gap-4 text-sm text-gray-600">
                      <span>工单: {{ workOrder?.id }}</span>
                      <span>|</span>
                      <span>{{ getProductionBatches().length }} 个批次</span>
                      <span>|</span>
                      <span>{{ getTotalItems() }} 个产品</span>
                    </div>
                  </div>
                </div>

                <div class="px-6 py-4">
                  <!-- 动态步骤内容 -->
                  <div class="space-y-6 step-content">
                    <!-- 步骤一：工单构成审查 -->
                    <div v-if="currentStep === 'review'" class="review-content">
                      <WorkOrderReviewContent
                        :work-order="workOrder"
                        @step-completed="handleStepCompleted"
                        @start-cutting-optimization="handleStartCuttingOptimization"
                        @material-requirements-updated="handleMaterialRequirementsUpdated"
                      />
                    </div>

                    <!-- 步骤二：切割优化 -->
                    <div v-else-if="currentStep === 'cutting'" class="cutting-content">
                      <CuttingOptimizationContent
                        :work-order="workOrder"
                        :material-requirements="materialRequirements"
                        @step-completed="handleStepCompleted"
                      />
                    </div>

                    <!-- 步骤三：生产排程 -->
                    <div v-else-if="currentStep === 'scheduling'" class="scheduling-content">
                      <ProductionSchedulingContent
                        :work-order="workOrder"
                        :cutting-result="cuttingResult"
                        @step-completed="handleStepCompleted"
                      />
                    </div>

                    <!-- 步骤四：决策与执行 -->
                    <div v-else-if="currentStep === 'execution'" class="execution-content">
                      <ExecutionDecisionContent
                        :work-order="workOrder"
                        @step-completed="handleStepCompleted"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对话框底部 -->
      <DialogFooter class="flex-shrink-0 border-t px-6 py-4">
        <!-- 关键指标 - 移入footer区域 -->
        <div class="flex-1 grid grid-cols-4 gap-4 mr-6">
          <div class="text-center">
            <div class="text-lg font-bold text-blue-600">{{ getOverallProgress() }}%</div>
            <div class="text-xs text-gray-600">完成进度</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-green-600">{{ getQualityScore() }}</div>
            <div class="text-xs text-gray-600">质量评分</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-orange-600">{{ getRemainingDays() }}</div>
            <div class="text-xs text-gray-600">剩余天数</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold" :class="getDeliveryStatusColor()">{{ getDeliveryStatus() }}</div>
            <div class="text-xs text-gray-600">交期状态</div>
          </div>
        </div>

        <!-- 动态操作按钮 -->
        <div class="flex items-center gap-3">
          <!-- 通用关闭按钮 -->
          <Button variant="outline" @click="$emit('update:open', false)">
            关闭
          </Button>

          <!-- 步骤一：工单构成审查 -->
          <template v-if="currentStep === 'review'">
            <Button
              variant="outline"
              @click="handleSaveReview"
              :disabled="!canSaveReview()"
            >
              <Package class="h-4 w-4 mr-2" />
              保存审查
            </Button>
            <Button
              @click="handleProceedToCutting"
              :disabled="!canProceedToCutting()"
              class="bg-blue-600 hover:bg-blue-700"
            >
              <ArrowRight class="h-4 w-4 mr-2" />
              进入切割优化
            </Button>
          </template>

          <!-- 步骤二：切割优化 -->
          <template v-else-if="currentStep === 'cutting'">
            <Button
              variant="outline"
              @click="handleBackToReview"
            >
              <ChevronLeft class="h-4 w-4 mr-2" />
              返回审查
            </Button>
            <Button
              variant="outline"
              @click="handleSaveCuttingResult"
              :disabled="!canSaveCuttingResult()"
            >
              <Settings class="h-4 w-4 mr-2" />
              保存优化结果
            </Button>
            <Button
              @click="handleProceedToScheduling"
              :disabled="!canProceedToScheduling()"
              class="bg-green-600 hover:bg-green-700"
            >
              <ArrowRight class="h-4 w-4 mr-2" />
              进入生产排程
            </Button>
          </template>

          <!-- 步骤三：生产排程 -->
          <template v-else-if="currentStep === 'scheduling'">
            <Button
              variant="outline"
              @click="handleBackToCutting"
            >
              <ChevronLeft class="h-4 w-4 mr-2" />
              返回优化
            </Button>
            <Button
              variant="outline"
              @click="handleSaveSchedule"
              :disabled="!canSaveSchedule()"
            >
              <Calendar class="h-4 w-4 mr-2" />
              保存排程
            </Button>
            <Button
              @click="handleProceedToExecution"
              :disabled="!canProceedToExecution()"
              class="bg-purple-600 hover:bg-purple-700"
            >
              <ArrowRight class="h-4 w-4 mr-2" />
              进入决策执行
            </Button>
          </template>

          <!-- 步骤四：决策与执行 -->
          <template v-else-if="currentStep === 'execution'">
            <Button
              variant="outline"
              @click="handleBackToScheduling"
            >
              <ChevronLeft class="h-4 w-4 mr-2" />
              返回排程
            </Button>
            <Button
              variant="outline"
              @click="handleSaveExecution"
              :disabled="!canSaveExecution()"
            >
              <Settings class="h-4 w-4 mr-2" />
              保存决策
            </Button>
            <Button
              @click="handleCompleteRelease"
              :disabled="!canCompleteRelease()"
              class="bg-orange-600 hover:bg-orange-700"
            >
              <Play class="h-4 w-4 mr-2" />
              完成发布
            </Button>
          </template>
        </div>
      </DialogFooter>
    </DialogContent>

    <!-- 用户引导组件 -->
    <UserGuide
      ref="userGuideRef"
      :auto-start="false"
      :show-help-button="false"
    />
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import UserGuide from '@/components/common/UserGuide.vue';
import {
  Settings,
  Package,
  ClipboardList,
  Calendar,
  Play,
  ArrowRight,
  ChevronDown,
  HelpCircle,
  ChevronLeft
} from 'lucide-vue-next';
import { mesService } from '@/services/mesService';
import type { ProductionOrderItem } from '@/types/mes-validation';

// 导入生产发布步骤组件
import WorkOrderReviewContent from './release-steps/WorkOrderReviewContent.vue';
import CuttingOptimizationContent from './release-steps/CuttingOptimizationContent.vue';
import ProductionSchedulingContent from './release-steps/ProductionSchedulingContent.vue';
import ExecutionDecisionContent from './release-steps/ExecutionDecisionContent.vue';

interface Props {
  open: boolean;
  workOrder?: any;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'step-updated', workOrderId: string, step: ReleaseStep): void;
  (e: 'work-order-released', workOrderId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 工单详情数据状态
const workOrderItems = ref<ProductionOrderItem[]>([]);
const isLoadingItems = ref(false);
const loadError = ref<string | null>(null);

// 客户订单信息映射
const customerOrderMap = ref<Record<string, { orderNumber: string; customerName: string }>>({});

// 物料需求汇总
const materialRequirements = ref<any[]>([]);

// 批次详情显示状态
const showBatchDetails = ref(false);

// 切割优化相关状态
const showCuttingOptimization = ref(false);
const cuttingOptimizationData = ref<any>(null);
const exportStatus = ref<'idle' | 'exporting' | 'exported' | 'error'>('idle');
const importStatus = ref<'waiting' | 'importing' | 'imported' | 'error'>('waiting');
const cuttingResult = ref<any>(null);
const cuttingExport = ref<any>(null);
const cuttingStep = ref<'export' | 'waiting' | 'import' | 'completed'>('waiting');

// 批次展开状态
const expandedBatches = ref<Set<string>>(new Set());

// 用户引导相关
const userGuideRef = ref();

// 生产发布步骤管理
type ReleaseStep = 'review' | 'cutting' | 'scheduling' | 'execution';
const currentStep = ref<ReleaseStep>('review'); // 默认为工单构成审查

// 发布步骤定义
const releaseSteps = [
  { key: 'review' as ReleaseStep, name: '工单构成审查', icon: ClipboardList },
  { key: 'cutting' as ReleaseStep, name: '切割优化', icon: Calendar },
  { key: 'scheduling' as ReleaseStep, name: '生产排程', icon: Settings },
  { key: 'execution' as ReleaseStep, name: '决策与执行', icon: Play }
];

// 加载工单详情数据
const loadWorkOrderItems = async () => {
  if (!props.workOrder) {
    workOrderItems.value = [];
    return;
  }

  isLoadingItems.value = true;
  loadError.value = null;

  try {
    // 直接从production-orders.json获取工单数据
    const response = await fetch('/mock/mes/production-orders.json');
    const productionOrdersData = await response.json();
    const workOrder = productionOrdersData.productionOrders?.find(
      (wo: any) => wo.id === props.workOrder.id || wo.workOrderNumber === props.workOrder.workOrderNumber
    );

    if (workOrder && workOrder.items) {
      // 加载真实的客户订单映射
      const { orderMap } = await loadCustomerOrderData();
      customerOrderMap.value = orderMap;

      // 使用production-orders.json中的完整数据
      workOrderItems.value = workOrder.items.map((item: any) => {
        const productInfo = generateProductInfo(item);
        return {
          id: item.id,
          productionOrderId: item.productionOrderId,
          customerOrderItemId: item.customerOrderItemId,
          productFamilyId: item.productFamilyId,
          productFamilyName: item.productFamilyName,
          ...productInfo,
          specifications: {
            ...item.specifications,
            glassType: item.specifications.glassType as "clear" | "tinted" | "low_e" | "reflective"
          },
          quantity: item.quantity,
          processFlow: item.processFlow.map((step: any) => ({
            ...step,
            status: step.status as "pending" | "in_progress" | "completed" | "skipped"
          })),
          currentStatus: item.status,
          currentWorkstation: item.processFlow.find((step: any) => step.status === 'in_progress')?.workstation,
          utilizationRate: Math.random() * 0.3 + 0.7 // 模拟利用率
        };
      });
    } else if (props.workOrder.items && Array.isArray(props.workOrder.items)) {
      // 如果数据不可用，使用传入的workOrder.items
      workOrderItems.value = props.workOrder.items;
    } else {
      // 最后尝试从MES服务获取完整的工单数据
      const allWorkOrders = await mesService.getProductionOrders();
      const fullWorkOrder = allWorkOrders.find(wo => wo.id === props.workOrder.id || wo.workOrderNumber === props.workOrder.workOrderNumber);

      if (fullWorkOrder && fullWorkOrder.items) {
        workOrderItems.value = fullWorkOrder.items;
      } else {
        // 没有找到工单数据
        workOrderItems.value = [];
      }
    }
  } catch {
    // 加载工单详情失败
    loadError.value = '加载工单详情失败';
    workOrderItems.value = [];
  } finally {
    isLoadingItems.value = false;
  }
};

// 从mock数据中加载客户订单信息
const loadCustomerOrderData = async () => {
  try {
    const [customerOrdersResponse, productionOrdersResponse] = await Promise.all([
      fetch('/mock/mes/customer-orders.json'),
      fetch('/mock/mes/production-orders.json')
    ]);
    
    const customerOrdersData = await customerOrdersResponse.json();
    const productionOrdersData = await productionOrdersResponse.json();
    
    // 构建客户订单映射
    const orderMap: Record<string, { orderNumber: string; customerName: string }> = {};
    
    customerOrdersData.orders?.forEach((order: any) => {
      order.items?.forEach((item: any) => {
        orderMap[item.id] = {
          orderNumber: order.orderNumber,
          customerName: order.customerName
        };
      });
    });
    
    return { orderMap, productionOrders: productionOrdersData.productionOrders || [] };
  } catch {
    // 加载mock数据失败，返回空数据
    return { orderMap: {}, productionOrders: [] };
  }
};

// 初始化当前步骤
const initializeCurrentStep = () => {
  if (!props.workOrder) return;

  // 根据工单状态确定当前步骤
  switch (props.workOrder.status) {
    case 'pending':
      currentStep.value = 'review'; // 待发布工单从审查开始
      break;
    case 'released':
      currentStep.value = 'execution'; // 已发布工单显示执行步骤
      break;
    case 'in_progress':
      currentStep.value = 'execution'; // 进行中工单显示执行步骤
      break;
    case 'completed':
      currentStep.value = 'execution'; // 已完成工单显示执行步骤
      break;
    default:
      currentStep.value = 'review'; // 默认从审查开始
  }
};

// 监听workOrder变化，重新加载数据
watch(() => props.workOrder, () => {
  if (props.open) {
    loadWorkOrderItems();
    initializeCurrentStep();
  }
}, { immediate: true });

// 监听对话框打开状态
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    loadWorkOrderItems();
    initializeCurrentStep();
  }
});



// 获取玻璃类型中文名称
const getGlassTypeName = (glassType: string): string => {
  const typeMap: Record<string, string> = {
    'clear': '透明玻璃',
    'tinted': '有色玻璃',
    'low_e': 'Low-E玻璃',
    'reflective': '反射玻璃'
  };
  return typeMap[glassType] || '透明玻璃';
};

// 获取颜色代码
const getColorCode = (color: string): string => {
  const colorMap: Record<string, string> = {
    '透明': 'C',
    '白色': 'W', 
    '灰色': 'G',
    '茶色': 'B',
    '蓝色': 'BL',
    '绿色': 'GR',
    '金色': 'GO',
    '银色': 'S',
    '黑色': 'BK'
  };
  return colorMap[color] || 'C';
};

// 根据产品族和规格生成产品信息
const generateProductInfo = (item: any) => {
  const { productFamilyId, specifications } = item;
  
  let productId = '';
  let productName = '';
  let productCode = '';
  
  switch (productFamilyId) {
    case 'PF-IGU': // 中空玻璃产品族
      if (specifications.structure) {
        // 如果有结构信息，使用结构信息
        productId = `${productFamilyId}-${specifications.structure.replace(/\s+/g, '')}`;
        productName = `${specifications.structure} 中空玻璃`;
        productCode = `IGU-${specifications.totalThickness || (specifications.glass1_thickness + specifications.spacer_width + specifications.glass2_thickness)}`;
      } else {
        // 没有结构信息时，根据规格推测
        const innerGlass = specifications.innerGlass || { thickness: 5, type: 'clear' };
        const outerGlass = specifications.outerGlass || { thickness: 5, type: 'low_e' };
        const spacerWidth = 12; // 默认间隔条
        const structure = `${innerGlass.thickness}mm${getGlassTypeName(innerGlass.type)}+${spacerWidth}A+${outerGlass.thickness}mm${getGlassTypeName(outerGlass.type)}`;
        productId = `${productFamilyId}-${structure.replace(/\s+/g, '')}`;
        productName = `${structure} 中空玻璃`;
        productCode = `IGU-${innerGlass.thickness + spacerWidth + outerGlass.thickness}`;
      }
      break;
      
    case 'PF-TEMPERED': // 钢化玻璃产品族
      productId = `${productFamilyId}-${specifications.thickness}mm${specifications.glassType}${specifications.color}钢化`;
      productName = `${specifications.thickness}mm ${getGlassTypeName(specifications.glassType)} ${specifications.color}玻璃钢化`;
      productCode = `TEMP-${specifications.thickness}${specifications.glassType.charAt(0).toUpperCase()}${getColorCode(specifications.color)}`;
      break;
      
    case 'PF-LAMINATED': // 夹胶玻璃产品族
      const glass1 = specifications.glass1_thickness || 5;
      const glass2 = specifications.glass2_thickness || 5;
      const pvb = specifications.pvb_thickness || 0.76;
      productId = `${productFamilyId}-${glass1}+${pvb}PVB+${glass2}`;
      productName = `${glass1}mm+${pvb}mmPVB+${glass2}mm 夹胶玻璃`;
      productCode = `LAM-${glass1}${glass2}P${pvb.toString().replace('.', '')}`;
      break;
      
    case 'PF-DECORATIVE': // 装饰玻璃产品族
      productId = `${productFamilyId}-${specifications.thickness}mm${specifications.glassType}${specifications.color}装饰`;
      productName = `${specifications.thickness}mm ${getGlassTypeName(specifications.glassType)} ${specifications.color} 装饰玻璃`;
      productCode = `DEC-${specifications.thickness}${specifications.glassType.charAt(0).toUpperCase()}${getColorCode(specifications.color)}`;
      break;
      
    case 'PF-FURNITURE': // 家具玻璃产品族
      productId = `${productFamilyId}-${specifications.thickness}mm${specifications.glassType}${specifications.color}家具`;
      productName = `${specifications.thickness}mm ${getGlassTypeName(specifications.glassType)} ${specifications.color} 家具玻璃`;
      productCode = `FUR-${specifications.thickness}${specifications.glassType.charAt(0).toUpperCase()}${getColorCode(specifications.color)}`;
      break;
      
    default:
      // 默认处理
      productId = `${productFamilyId}-${specifications.length}x${specifications.width}x${specifications.thickness}-${specifications.glassType}`;
      productName = `${specifications.length}×${specifications.width}×${specifications.thickness}mm ${getGlassTypeName(specifications.glassType)} ${specifications.color}玻璃`;
      productCode = `P-${productFamilyId.split('-')[1]}-${specifications.thickness}${specifications.glassType.charAt(0).toUpperCase()}`;
  }
  
  return { productId, productName, productCode };
};

// 获取状态徽章样式
const getStatusVariant = (status: string): "default" | "destructive" | "outline" | "secondary" => {
  const statusMap: Record<string, "default" | "destructive" | "outline" | "secondary"> = {
    'pending': 'secondary',
    'in_progress': 'default',
    'completed': 'outline',
    'cancelled': 'destructive',
    '待开始': 'secondary',
    '进行中': 'default',
    '已完成': 'outline',
    '已取消': 'destructive'
  };
  return statusMap[status] || 'secondary';
};

// 获取状态中文名称
const getStatusName = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': '待开始',
    'in_progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// 计算原片需求
const calculateRawGlassNeeded = (item: any): number => {
  // 简化计算：根据产品尺寸和数量估算原片需求
  const area = (item.specifications.length * item.specifications.width) / 1000000; // 转换为平方米
  const totalArea = area * item.quantity;
  // 假设标准原片为3.3m x 2.14m = 7.062平方米
  const standardSheetArea = 7.062;
  return Math.ceil(totalArea / standardSheetArea);
};

// 计算型材需求
const calculateProfileNeeded = (item: any): number => {
  // 简化计算：根据产品周长估算型材需求
  const perimeter = 2 * (item.specifications.length + item.specifications.width) / 1000; // 转换为米
  return Math.round(perimeter * item.quantity * 1.1); // 加10%损耗
};

// 计算物料成本
const calculateMaterialCost = (item: any): number => {
  const rawGlassNeeded = calculateRawGlassNeeded(item);
  const profileNeeded = calculateProfileNeeded(item);
  // 简化成本计算
  const glassCost = rawGlassNeeded * 450; // 假设每片原片450元
  const profileCost = profileNeeded * 25; // 假设每米型材25元
  return Math.round(glassCost + profileCost);
};

// 计算利用率
const calculateUtilizationRate = (item: any): number => {
  // 简化计算：基于产品尺寸与标准原片的匹配度
  const productArea = (item.specifications.length * item.specifications.width) / 1000000;
  const standardSheetArea = 7.062;
  const utilizationRate = Math.min(productArea / standardSheetArea * 100, 95);
  return Math.round(utilizationRate);
};

// 计算余料
const calculateWasteMaterial = (item: any): number => {
  const rawGlassNeeded = calculateRawGlassNeeded(item);
  const utilizationRate = calculateUtilizationRate(item) / 100;
  const wasteRate = 1 - utilizationRate;
  return Math.round(rawGlassNeeded * wasteRate);
};

// 步骤管理方法
const getStepClass = (stepKey: ReleaseStep): string => {
  const stepOrder: ReleaseStep[] = ['review', 'cutting', 'scheduling', 'execution'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepKey);

  if (stepIndex < currentIndex) {
    return 'bg-green-100 text-green-600'; // 已完成
  } else if (stepIndex === currentIndex) {
    return 'bg-blue-100 text-blue-600'; // 当前步骤
  } else {
    return 'bg-gray-100 text-gray-400'; // 未开始
  }
};

const isStepCompleted = (stepKey: ReleaseStep): boolean => {
  const stepOrder: ReleaseStep[] = ['review', 'cutting', 'scheduling', 'execution'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepKey);
  return stepIndex < currentIndex;
};

const getCurrentStepText = (): string => {
  const step = releaseSteps.find(s => s.key === currentStep.value);
  return step?.name || '未知步骤';
};

const getCurrentStepStatus = (): string => {
  const statusMap: Record<ReleaseStep, string> = {
    'review': '审查中',
    'cutting': '优化中',
    'scheduling': '排程中',
    'execution': '执行中'
  };
  return statusMap[currentStep.value] || '未知状态';
};

const switchToStep = (step: ReleaseStep) => {
  currentStep.value = step;
  // 通知父组件步骤变更
  if (props.workOrder) {
    emit('step-updated', props.workOrder.id, step);
  }
};

// 启动用户引导
const startUserGuide = () => {
  if (userGuideRef.value) {
    userGuideRef.value.startGuide();
  }
};

const handleStepCompleted = (completedStep: string) => {
  const stepOrder: ReleaseStep[] = ['review', 'cutting', 'scheduling', 'execution'];
  const currentIndex = stepOrder.indexOf(completedStep as ReleaseStep);

  if (currentIndex >= 0 && currentIndex < stepOrder.length - 1) {
    // 自动切换到下一个步骤
    const nextStep = stepOrder[currentIndex + 1];
    currentStep.value = nextStep;

    // 通知父组件步骤变更
    if (props.workOrder) {
      emit('step-updated', props.workOrder.id, nextStep);
    }
  } else if (completedStep === 'execution') {
    // 所有步骤已完成，工单可以发布
    if (props.workOrder) {
      emit('step-updated', props.workOrder.id, 'execution');
      emit('work-order-released', props.workOrder.id);
    }
  }
};

// 处理开始切割优化
const handleStartCuttingOptimization = (data: { workOrder: any; materialSummary: any[] }) => {
  // 保存切割优化数据
  cuttingOptimizationData.value = data;
  
  // 打开切割优化对话框
  showCuttingOptimization.value = true;
  
  // 重置状态
  exportStatus.value = 'idle';
  importStatus.value = 'waiting';
  cuttingResult.value = null;
  cuttingExport.value = null;
};

// 处理物料需求更新
const handleMaterialRequirementsUpdated = (requirements: any[]) => {
  materialRequirements.value = requirements;
};

// 切割优化相关处理函数
const handleExportData = async () => {
  exportStatus.value = 'exporting';
  cuttingStep.value = 'export';
  try {
    // 模拟导出数据
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 生成导出数据
    cuttingExport.value = {
      exportId: `export_${Date.now()}`,
      workOrderId: cuttingOptimizationData.value?.workOrder?.id,
      timestamp: new Date().toISOString(),
      data: cuttingOptimizationData.value
    };
    
    exportStatus.value = 'exported';
    cuttingStep.value = 'import';
  } catch (error) {
    exportStatus.value = 'error';
  }
};

const handleImportResult = async (file: File) => {
  importStatus.value = 'importing';
  try {
    // 模拟导入处理
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // 生成模拟的切割优化结果
    cuttingResult.value = {
      improvements: {
        materialUtilization: 15.5,
        costSaving: 8200,
        timeReduction: 25,
        wasteReduction: 12.3
      },
      optimizedLayouts: [
        { id: 1, efficiency: 92.5, pieces: 45 },
        { id: 2, efficiency: 88.7, pieces: 38 }
      ]
    };
    
    importStatus.value = 'imported';
    cuttingStep.value = 'completed';
  } catch (error) {
    importStatus.value = 'error';
  }
};

const getCurrentOptimizationStep = () => {
  if (cuttingResult.value) {
    return 'completed';
  } else if (cuttingExport.value && importStatus.value === 'waiting') {
    return 'waiting';
  } else if (cuttingExport.value && importStatus.value === 'importing') {
    return 'import';
  } else if (cuttingExport.value) {
    return 'import';
  } else {
    return 'export';
  }
};

const handleDownloadFile = () => {
  if (cuttingExport.value) {
    // 模拟文件下载
    const filename = `cutting_data_${cuttingExport.value.exportId}.xlsx`;
    console.log('下载文件:', filename);
  }
};

const handleSimulateComplete = async () => {
  // 直接模拟完成切割优化
  await handleImportResult(new File([], 'mock.xlsx'));
};

const handleViewResults = () => {
  showCuttingOptimization.value = false;
  console.log('查看切割优化结果详情', cuttingResult.value);
};

const handleProceedToAnalysis = () => {
  // 关闭切割优化对话框，进入下一步骤
  showCuttingOptimization.value = false;
  currentStep.value = 'cutting';
  
  // 通知父组件步骤变更
  if (props.workOrder) {
    emit('step-updated', props.workOrder.id, 'cutting');
  }
};

// 获取客户订单号
const getCustomerOrderNumber = (customerOrderItemId: string): string => {
  if (!customerOrderItemId) return '未知订单';
  
  // 优先从客户订单映射中获取真实的订单号
  const orderInfo = customerOrderMap.value[customerOrderItemId];
  if (orderInfo && orderInfo.orderNumber) {
    return orderInfo.orderNumber;
  }
  
  // 如果映射中没有找到，从订单项ID提取订单号（去掉最后的项目编号）
  const parts = customerOrderItemId.split('-');
  if (parts.length >= 4) {
    return parts.slice(0, -1).join('-'); // 例如：CO-2024-A001-01 -> CO-2024-A001
  }
  return customerOrderItemId;
};

// 查看客户订单详情
const viewCustomerOrder = (customerOrderItemId: string) => {
  const orderNumber = getCustomerOrderNumber(customerOrderItemId);
  const orderInfo = customerOrderMap.value[customerOrderItemId];
  const customerName = orderInfo?.customerName || '未知客户';
  
  // 这里可以打开客户订单详情对话框或跳转到订单页面
  // 暂时使用alert演示，实际项目中可以替换为路由跳转或打开对话框
  alert(`查看客户订单详情：\n订单号：${orderNumber}\n客户名称：${customerName}\n订单项：${customerOrderItemId}\n\n此功能将打开客户订单详情页面`);
  
  // 实际实现示例：
  // 1. 路由跳转：router.push(`/orders/${orderNumber}`)
  // 2. 打开对话框：emit('open-order-dialog', orderNumber)
  // 3. 调用API获取订单详情：orderService.getOrderDetails(orderNumber)
};

// 查看产品BOM详情
const viewProductBOM = (productId: string, productName: string) => {
  // 这里可以打开具体产品BOM详情对话框或跳转到产品BOM管理页面
  // 暂时使用alert演示，实际项目中可以替换为路由跳转或打开对话框
  alert(`查看产品BOM：\n产品ID：${productId}\n产品名称：${productName}\n\n此功能将显示该产品的详细BOM清单，包括：\n- 原料清单和用量\n- 具体工艺路线\n- 质量检验标准\n- 包装要求\n- 成本构成明细\n\n点击确定将打开产品BOM管理界面`);
  
  // 实际实现示例：
  // 1. 路由跳转：router.push(`/product-bom/${productId}`)
  // 2. 打开对话框：emit('open-product-bom-dialog', productId)
  // 3. 调用API获取产品BOM详情：productBOMService.getBOMDetails(productId)
};

// 生产批次相关函数
const getProductionBatches = () => {
  if (!workOrderItems.value.length) return [];
  
  // 使用更灵活的批次分组策略
  const batches = [];
  const processedItems = new Set();
  
  workOrderItems.value.forEach(item => {
    if (processedItems.has(item.id)) return;
    
    // 创建新批次
    const batch = {
      id: `BATCH-${batches.length + 1}`,
      items: [item],
      totalQuantity: item.quantity,
      specifications: [item.specifications],
      processFlows: [item.processFlow],
      customerOrders: [getCustomerOrderNumber(item.customerOrderItemId)],
      glassTypes: [item.specifications.glassType],
      thicknesses: [item.specifications.thickness]
    };
    
    processedItems.add(item.id);
    
    // 查找可以加入同一批次的其他工单项
    workOrderItems.value.forEach(otherItem => {
      if (processedItems.has(otherItem.id)) return;
      
      // 批次兼容性检查：可以合并的条件
      if (canMergeIntoBatch(batch, otherItem)) {
        batch.items.push(otherItem);
        batch.totalQuantity += otherItem.quantity;
        
        // 更新批次信息
        if (!batch.specifications.some(spec => 
          spec.length === otherItem.specifications.length && 
          spec.width === otherItem.specifications.width &&
          spec.thickness === otherItem.specifications.thickness &&
          spec.glassType === otherItem.specifications.glassType
        )) {
          batch.specifications.push(otherItem.specifications);
        }
        
        const orderNumber = getCustomerOrderNumber(otherItem.customerOrderItemId);
        if (!batch.customerOrders.includes(orderNumber)) {
          batch.customerOrders.push(orderNumber);
        }
        
        if (!batch.glassTypes.includes(otherItem.specifications.glassType)) {
          batch.glassTypes.push(otherItem.specifications.glassType);
        }
        
        if (!batch.thicknesses.includes(otherItem.specifications.thickness)) {
          batch.thicknesses.push(otherItem.specifications.thickness);
        }
        
        processedItems.add(otherItem.id);
      }
    });
    
    batches.push(batch);
  });
  
  return batches;
};

// 获取工单总产品数量
const getTotalItems = () => {
  if (!props.workOrder?.orderItems) return 0;
  return props.workOrder.orderItems.reduce((sum: number, item: any) => sum + item.quantity, 0);
};

// 检查是否可以合并到同一批次
const canMergeIntoBatch = (batch: any, item: any) => {
  // 1. 玻璃厚度兼容性（允许相近厚度合并）
  const thicknessCompatible = batch.thicknesses.some((thickness: number) => 
    Math.abs(thickness - item.specifications.thickness) <= 2
  );
  
  // 2. 玻璃类型兼容性（某些类型可以合并）
  const glassTypeCompatible = isGlassTypeCompatible(batch.glassTypes, item.specifications.glassType);
  
  // 3. 工艺流程兼容性（至少50%步骤相同）
  const processCompatible = batch.processFlows.some((flow: any[]) => 
    getProcessSimilarity(flow, item.processFlow) >= 0.5
  );
  
  // 4. 尺寸优化（可以使用相同原片切割）
  const sizeOptimizable = batch.specifications.some((spec: any) => 
    canOptimizeCutting(spec, item.specifications)
  );
  
  return (thicknessCompatible && glassTypeCompatible) && (processCompatible || sizeOptimizable);
};

// 玻璃类型兼容性检查
const isGlassTypeCompatible = (batchTypes: string[], newType: string) => {
  const compatibilityGroups = [
    ['clear', 'low_e'], // 透明和Low-E可以合并
    ['tinted', 'reflective'], // 有色和反射可以合并
  ];
  
  return batchTypes.includes(newType) || 
    compatibilityGroups.some(group => 
      group.includes(newType) && batchTypes.some(type => group.includes(type))
    );
};

// 工艺流程相似度计算
const getProcessSimilarity = (flow1: any[], flow2: any[]) => {
  const steps1 = flow1.map(step => step.stepName);
  const steps2 = flow2.map(step => step.stepName);
  const commonSteps = steps1.filter(step => steps2.includes(step));
  return commonSteps.length / Math.max(steps1.length, steps2.length);
};

// 尺寸切割优化检查
const canOptimizeCutting = (spec1: any, spec2: any) => {
  // 检查是否可以从同一原片切割
  const standardSheetLength = 3300; // 标准原片3.3m
  const standardSheetWidth = 2140;  // 标准原片2.14m
  
  // 简化检查：如果两个尺寸可以合理安排在同一原片上
  return (spec1.length + spec2.length <= standardSheetLength) ||
         (spec1.width + spec2.width <= standardSheetWidth) ||
         (spec1.length <= standardSheetLength && spec2.length <= standardSheetLength &&
          spec1.width <= standardSheetWidth && spec2.width <= standardSheetWidth);
};

const getTotalQuantity = () => {
  return workOrderItems.value.reduce((total, item) => total + item.quantity, 0);
};

const getUniqueCustomers = () => {
  const customers = new Set();
  workOrderItems.value.forEach(item => {
    const orderInfo = customerOrderMap.value[item.customerOrderItemId];
    if (orderInfo && orderInfo.customerName) {
      customers.add(orderInfo.customerName);
    }
  });
  return Array.from(customers);
};

const getUniqueOrders = () => {
  const orders = new Set();
  workOrderItems.value.forEach(item => {
    const orderInfo = customerOrderMap.value[item.customerOrderItemId];
    if (orderInfo && orderInfo.orderNumber) {
      orders.add(orderInfo.orderNumber);
    }
  });
  return Array.from(orders);
};

// 切换批次详情显示
const toggleBatchDetails = () => {
  showBatchDetails.value = !showBatchDetails.value;
};

// 切换批次展开状态
const toggleBatchExpansion = (batchId: string) => {
  if (expandedBatches.value.has(batchId)) {
    expandedBatches.value.delete(batchId);
  } else {
    expandedBatches.value.add(batchId);
  }
};

// 展开所有批次
const expandAllBatches = () => {
  const batches = getProductionBatches();
  batches.forEach(batch => {
    expandedBatches.value.add(batch.id);
  });
};

// 收起所有批次
const collapseAllBatches = () => {
  expandedBatches.value.clear();
};

// 批次颜色配置
const batchColors = [
  '#8B5CF6', // 紫色
  '#06B6D4', // 青色
  '#10B981', // 绿色
  '#F59E0B', // 橙色
  '#EF4444', // 红色
  '#8B5A2B', // 棕色
  '#6366F1', // 靛色
  '#EC4899'  // 粉色
];

// 获取批次生产优势描述
const getBatchAdvantages = (batch: any) => {
  const advantages = [];
  
  // 检查产品分布（具体产品）
  const products = [...new Set(batch.items.map((item: any) => item.productCode))];
  if (products.length === 1) {
    advantages.push(`单一产品: ${products[0]}`);
  } else if (products.length > 1) {
    advantages.push(`${products.length}个产品`);
  }
  
  // 检查产品族分布
  const productFamilies = [...new Set(batch.items.map((item: any) => item.productFamilyName))];
  if (productFamilies.length === 1) {
    advantages.push(`${productFamilies[0]}`);
  } else if (productFamilies.length > 1) {
    advantages.push(`${productFamilies.length}个产品族`);
  }
  
  // 检查各种优化优势
  if (batch.specifications.length > 1) {
    advantages.push('多规格协同');
  }
  
  if (batch.customerOrders.length > 1) {
    advantages.push('跨订单整合');
  }
  
  if (batch.thicknesses.length === 1) {
    advantages.push('厚度统一');
  }
  
  if (batch.glassTypes.length === 1) {
    advantages.push('玻璃类型统一');
  }
  
  // 检查是否有切割优化
  const canOptimize = batch.specifications.some((spec1: any, i: number) => 
    batch.specifications.some((spec2: any, j: number) => 
      i !== j && canOptimizeCutting(spec1, spec2)
    )
  );
  
  if (canOptimize) {
    advantages.push('切割优化');
  }
  
  // 检查工艺流程优化
  const commonSteps = batch.processFlows.reduce((common: string[], flow: any[]) => {
    const steps = flow.map(step => step.stepName);
    return common.length === 0 ? steps : common.filter(step => steps.includes(step));
  }, []);
  
  if (commonSteps.length >= 3) {
    advantages.push('工艺流程优化');
  }
  
  return advantages.length > 0 ? advantages.join(' • ') : '规模化生产';
};

// 新增的计算函数
// 计算批次总面积
const calculateBatchTotalArea = (batch: any): string => {
  const totalArea = batch.items.reduce((total: number, item: any) => {
    const itemArea = (item.specifications.length * item.specifications.width * item.quantity) / 1000000; // 转换为平方米
    return total + itemArea;
  }, 0);
  return totalArea.toFixed(2);
};

// 获取批次最早交期
const getBatchEarliestDeadline = (batch: any): string => {
  // 从客户订单映射中获取交期信息，这里简化为模拟数据
  const deadlines = batch.items.map((item: any) => {
    const orderIndex = parseInt(item.customerOrderItemId.split('-').pop() || '1');
    const baseDate = new Date('2024-01-15');
    baseDate.setDate(baseDate.getDate() + orderIndex * 3);
    return baseDate;
  });
  
  const earliestDate = new Date(Math.min(...deadlines.map(d => d.getTime())));
  return earliestDate.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric'
  });
};

// 计算单件面积
const calculateItemArea = (item: any): string => {
  const area = (item.specifications.length * item.specifications.width * item.quantity) / 1000000; // 转换为平方米
  return area.toFixed(2);
};

// 获取订单项要求交期
const getItemDeadline = (item: any): string => {
  // 从客户订单映射中获取交期信息，这里简化为模拟数据
  const orderIndex = parseInt(item.customerOrderItemId.split('-').pop() || '1');
  const baseDate = new Date('2024-01-15');
  baseDate.setDate(baseDate.getDate() + orderIndex * 3);
  return baseDate.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric'
  });
};

// 获取批次优势详细说明
const getBatchAdvantageDetails = (batch: any) => {
  const details = [];
  
  // 产品分布信息
  const products = [...new Set(batch.items.map((item: any) => item.productCode))];
  if (products.length === 1) {
    details.push(`单一产品：${products[0]}，BOM和工艺路线一致性好`);
  } else if (products.length > 1) {
    details.push(`涵盖${products.length}个产品：${products.join('、')}`);
  }
  
  // 产品族分布信息
  const productFamilies = [...new Set(batch.items.map((item: any) => item.productFamilyName))];
  if (productFamilies.length === 1) {
    details.push(`单一产品族：${productFamilies[0]}，产品结构框架统一`);
  } else if (productFamilies.length > 1) {
    details.push(`跨产品族生产：${productFamilies.join('、')}`);
  }
  
  if (batch.specifications.length > 1) {
    details.push(`包含${batch.specifications.length}种不同规格，实现多样化生产`);
  }
  
  if (batch.customerOrders.length > 1) {
    details.push(`整合${batch.customerOrders.length}个客户订单，提高生产效率`);
  }
  
  if (batch.thicknesses.length === 1) {
    details.push(`统一厚度${batch.thicknesses[0]}mm，减少设备调整时间`);
  }
  
  if (batch.glassTypes.length === 1) {
    details.push(`统一玻璃类型，优化工艺流程`);
  }
  
  const totalQuantity = batch.totalQuantity;
  if (totalQuantity >= 1000) {
    details.push(`大批量生产${totalQuantity}片，降低单位成本`);
  }
  
  return details.join('\n') || '优化生产安排，提高整体效率';
};

// 批次级别的计算函数
const calculateBatchRawGlass = (batch: any) => {
  return batch.items.reduce((total: number, item: any) => total + calculateRawGlassNeeded(item), 0);
};

const calculateBatchProfile = (batch: any) => {
  return batch.items.reduce((total: number, item: any) => total + calculateProfileNeeded(item), 0);
};

const calculateBatchCost = (batch: any) => {
  return batch.items.reduce((total: number, item: any) => total + calculateMaterialCost(item), 0);
};

const calculateBatchUtilization = (batch: any) => {
  const totalUtilization = batch.items.reduce((total: number, item: any) => total + calculateUtilizationRate(item), 0);
  return Math.round(totalUtilization / batch.items.length);
};

const calculateBatchWaste = (batch: any) => {
  return batch.items.reduce((total: number, item: any) => total + calculateWasteMaterial(item), 0);
};

const getBatchStatusVariant = (batch: any): "default" | "destructive" | "outline" | "secondary" => {
  const statuses = batch.items.map((item: any) => item.currentStatus);
  
  if (statuses.every((status: string) => status === 'completed')) return 'outline';
  if (statuses.some((status: string) => status === 'in_progress')) return 'default';
  if (statuses.every((status: string) => status === 'pending')) return 'secondary';
  return 'secondary';
};

const getBatchStatusText = (batch: any) => {
  const statuses = batch.items.map((item: any) => item.currentStatus);
  const completedCount = statuses.filter((status: string) => status === 'completed').length;
  const inProgressCount = statuses.filter((status: string) => status === 'in_progress').length;
  const totalCount = statuses.length;
  
  if (completedCount === totalCount) return '已完成';
  if (inProgressCount > 0) return `进行中 (${inProgressCount}/${totalCount})`;
  return '待开始';
};

// 步骤说明相关函数
const getCurrentStepIcon = () => {
  const step = releaseSteps.find(s => s.key === currentStep.value);
  return step?.icon || ClipboardList;
};

const getStepNoticeClass = (): string => {
  const classMap: Record<ReleaseStep, string> = {
    'review': 'bg-blue-50 border-blue-200',
    'cutting': 'bg-orange-50 border-orange-200',
    'scheduling': 'bg-purple-50 border-purple-200',
    'execution': 'bg-green-50 border-green-200'
  };
  return classMap[currentStep.value] || 'bg-gray-50 border-gray-200';
};

const getStepNoticeTextClass = (): string => {
  const classMap: Record<ReleaseStep, string> = {
    'review': 'text-blue-700',
    'cutting': 'text-orange-700',
    'scheduling': 'text-purple-700',
    'execution': 'text-green-700'
  };
  return classMap[currentStep.value] || 'text-gray-700';
};

const getCurrentStepNoticeTitle = (): string => {
  const titleMap: Record<ReleaseStep, string> = {
    'review': '当前阶段：工单构成审查',
    'cutting': '当前阶段：切割优化',
    'scheduling': '当前阶段：生产排程',
    'execution': '当前阶段：决策与执行'
  };
  return titleMap[currentStep.value] || '当前阶段';
};

const getCurrentStepNoticeText = (): string => {
  const textMap: Record<ReleaseStep, string> = {
    'review': '正在审查工单构成和产品规格信息，原料需求、成本利用率、余料回收等数据将在后续分析阶段计算。',
    'cutting': '正在进行切割优化分析，计算最优的切割方案以提高材料利用率。',
    'scheduling': '正在制定生产排程计划，按工序安排执行时间和资源分配。',
    'execution': '准备执行生产计划，所有分析数据已就绪。'
  };
  return textMap[currentStep.value] || '';
};

// 工具函数
const formatDate = (dateString: string): string => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric'
  });
};

const getPriorityText = (priority: string): string => {
  const priorityMap: Record<string, string> = {
    'high': '高',
    'medium': '中',
    'low': '低',
    'urgent': '紧急'
  };
  return priorityMap[priority] || priority;
};

const getPriorityClass = (priority: string): string => {
  const classMap: Record<string, string> = {
    'high': 'text-red-600',
    'medium': 'text-orange-600',
    'low': 'text-green-600',
    'urgent': 'text-red-800 font-bold'
  };
  return classMap[priority] || 'text-gray-600';
};

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': '待发布',
    'released': '已发布',
    'in_progress': '执行中',
    'completed': '已完成',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

const getStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    'pending': 'text-yellow-600',
    'released': 'text-blue-600',
    'in_progress': 'text-green-600',
    'completed': 'text-gray-600',
    'cancelled': 'text-red-600'
  };
  return classMap[status] || 'text-gray-600';
};

// 底部指标计算方法
const getOverallProgress = (): number => {
  const stepOrder: ReleaseStep[] = ['review', 'cutting', 'scheduling', 'execution'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  return Math.round(((currentIndex + 1) / stepOrder.length) * 100);
};

const getQualityScore = (): number => {
  // 基于当前步骤和工单状态计算质量评分
  const baseScore = 85;
  const stepBonus = getOverallProgress() * 0.1;
  return Math.min(100, Math.round(baseScore + stepBonus));
};

const getRemainingDays = (): number => {
  if (!props.workOrder?.plannedDeliveryDate) return 16;

  const deliveryDate = new Date(props.workOrder.plannedDeliveryDate);
  const today = new Date();
  const diffTime = deliveryDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
};

const getDeliveryStatus = (): string => {
  const remainingDays = getRemainingDays();
  if (remainingDays > 7) return '正常';
  if (remainingDays > 3) return '紧急';
  if (remainingDays > 0) return '急迫';
  return '逾期';
};

const getDeliveryStatusColor = (): string => {
  const status = getDeliveryStatus();
  const colorMap: Record<string, string> = {
    '正常': 'text-green-600',
    '紧急': 'text-orange-600',
    '急迫': 'text-red-600',
    '逾期': 'text-red-700'
  };
  return colorMap[status] || 'text-gray-600';
};

// 步骤操作按钮状态检查方法
const canSaveReview = (): boolean => {
  return workOrderItems.value.length > 0;
};

const canProceedToCutting = (): boolean => {
  return canSaveReview() && getProductionBatches().length > 0;
};

const canSaveCuttingResult = (): boolean => {
  return currentStep.value === 'cutting';
};

const canProceedToScheduling = (): boolean => {
  return canSaveCuttingResult();
};

const canSaveSchedule = (): boolean => {
  return currentStep.value === 'scheduling';
};

const canProceedToExecution = (): boolean => {
  return canSaveSchedule();
};

const canSaveExecution = (): boolean => {
  return currentStep.value === 'execution';
};

const canCompleteRelease = (): boolean => {
  return canSaveExecution();
};

// 步骤操作按钮处理方法
const handleSaveReview = () => {
  console.log('保存工单构成审查结果');
  // 这里可以添加保存逻辑
};

const handleProceedToCutting = () => {
  if (canProceedToCutting()) {
    currentStep.value = 'cutting';
    emit('step-updated', props.workOrder?.id || '', 'cutting');
  }
};

const handleBackToReview = () => {
  currentStep.value = 'review';
  emit('step-updated', props.workOrder?.id || '', 'review');
};

const handleSaveCuttingResult = () => {
  console.log('保存切割优化结果');
  // 这里可以添加保存逻辑
};

const handleProceedToScheduling = () => {
  if (canProceedToScheduling()) {
    currentStep.value = 'scheduling';
    emit('step-updated', props.workOrder?.id || '', 'scheduling');
  }
};

const handleBackToCutting = () => {
  currentStep.value = 'cutting';
  emit('step-updated', props.workOrder?.id || '', 'cutting');
};

const handleSaveSchedule = () => {
  console.log('保存生产排程结果');
  // 这里可以添加保存逻辑
};

const handleProceedToExecution = () => {
  if (canProceedToExecution()) {
    currentStep.value = 'execution';
    emit('step-updated', props.workOrder?.id || '', 'execution');
  }
};

const handleBackToScheduling = () => {
  currentStep.value = 'scheduling';
  emit('step-updated', props.workOrder?.id || '', 'scheduling');
};

const handleSaveExecution = () => {
  console.log('保存决策与执行结果');
  // 这里可以添加保存逻辑
};

const handleCompleteRelease = () => {
  if (canCompleteRelease()) {
    console.log('完成生产发布');
    emit('work-order-released', props.workOrder?.id || '');
    // 可以关闭对话框或显示成功消息
    emit('update:open', false);
  }
};
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
