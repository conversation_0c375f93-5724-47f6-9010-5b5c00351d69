<template>
  <div class="h-full flex flex-col">
    <!-- 页面标题 -->
    <div class="mb-6">
      <div class="flex items-center space-x-3 mb-2">
        <Calendar class="h-6 w-6 text-green-600" />
        <h2 class="text-xl font-semibold text-gray-900">生产排程</h2>
      </div>
      <p class="text-sm text-gray-600">
        基于切割优化结果，按工序制定详细的生产执行计划
      </p>
    </div>

    <!-- 进度指示器 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <template v-for="(step, index) in schedulingSteps" :key="step.id">
          <!-- 步骤圆圈 -->
          <div class="flex flex-col items-center">
            <div 
              class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300"
              :class="getStepCircleClass(step.id)"
            >
              <component 
                :is="step.icon" 
                class="w-5 h-5"
                :class="getStepIconClass(step.id)"
              />
            </div>
            <div class="mt-2 text-sm text-center font-medium" :class="getStepTextClass(step.id)">
              {{ step.title }}
            </div>
            <div class="mt-1 text-xs text-center text-gray-500">
              {{ step.description }}
            </div>
          </div>
          
          <!-- 连接线 -->
          <div 
            v-if="index < schedulingSteps.length - 1"
            class="flex-1 h-0.5 mx-4 transition-all duration-300"
            :class="getStepConnectionClass(index)"
          />
        </template>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-6">
      <!-- 左侧：甘特图视图 -->
      <div class="flex-1 space-y-4">
        <div class="border rounded-lg p-6 bg-white h-full">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-2">
              <BarChart3 class="h-6 w-6 text-green-600" />
              <h3 class="text-xl font-medium text-gray-900">生产甘特图</h3>
            </div>
            <div class="flex space-x-2">
              <Button size="sm" variant="outline" @click="refreshSchedule">
                <RefreshCw class="h-4 w-4 mr-2" />
                重新排程
              </Button>
              <Button size="sm" @click="autoOptimize">
                <Zap class="h-4 w-4 mr-2" />
                自动优化
              </Button>
            </div>
          </div>

          <div v-if="!scheduleData" class="flex items-center justify-center h-96 text-gray-500">
            <div class="text-center">
              <Calendar class="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <div class="text-lg font-medium mb-2">正在生成排程方案</div>
              <div class="text-sm">基于切割优化结果制定执行计划</div>
              <Button @click="generateSchedule" class="mt-4">
                <Play class="h-4 w-4 mr-2" />
                开始排程
              </Button>
            </div>
          </div>

          <div v-else class="space-y-4">
            <!-- 时间轴头部 -->
            <div class="border-b pb-4">
              <div class="flex items-center space-x-4 text-sm text-gray-600">
                <span>排程周期：{{ scheduleData.timeRange.start }} ~ {{ scheduleData.timeRange.end }}</span>
                <span>工序数：{{ scheduleData.processes.length }} 个</span>
                <span>批次数：{{ scheduleData.batches.length }} 个</span>
                <span>预计完工：{{ scheduleData.estimatedCompletion }}</span>
              </div>
            </div>

            <!-- 甘特图主体 -->
            <div class="overflow-x-auto">
              <div class="min-w-[800px]">
                <!-- 时间轴 -->
                <div class="flex border-b border-gray-200 bg-gray-50 p-2 text-xs text-gray-600">
                  <div class="w-48 text-left font-medium">工序/批次</div>
                  <div class="flex-1 grid grid-cols-24 gap-1 text-center">
                    <div v-for="hour in 24" :key="hour" class="text-xs">
                      {{ String(hour - 1).padStart(2, '0') }}:00
                    </div>
                  </div>
                </div>

                <!-- 工序行 -->
                <div v-for="process in scheduleData.processes" :key="process.id" class="border-b border-gray-100">
                  <div class="flex">
                    <div class="w-48 p-3 bg-gray-50 border-r border-gray-200">
                      <div class="font-medium text-sm text-gray-900">{{ process.name }}</div>
                      <div class="text-xs text-gray-500">{{ process.workCenter }}</div>
                    </div>
                    <div class="flex-1 relative h-12 bg-white">
                      <!-- 批次条 -->
                      <div 
                        v-for="batch in process.batches" 
                        :key="batch.id"
                        class="absolute h-8 mt-2 rounded px-2 flex items-center text-xs font-medium text-white cursor-pointer hover:opacity-80"
                        :class="getBatchColorClass(batch.status)"
                        :style="getBatchStyle(batch)"
                        @click="selectBatch(batch)"
                        :title="`批次${batch.batchNumber} - ${batch.duration}小时`"
                      >
                        <span class="truncate">{{ batch.batchNumber }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：控制面板 -->
      <div class="w-80 space-y-4">
        <!-- 排程参数设置 -->
        <div class="border rounded-lg p-4 bg-white">
          <div class="flex items-center space-x-2 mb-4">
            <Settings class="h-5 w-5 text-blue-600" />
            <h3 class="text-lg font-medium text-gray-900">排程参数</h3>
          </div>
          
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-700 mb-2 block">排程策略</label>
              <select v-model="schedulingStrategy" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                <option value="earliest_start">最早开始时间</option>
                <option value="shortest_processing">最短加工时间</option>
                <option value="critical_ratio">临界比率</option>
                <option value="mixed_strategy">混合策略</option>
              </select>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-700 mb-2 block">优化目标</label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input type="checkbox" v-model="optimizationTargets.makespan" class="mr-2">
                  <span class="text-sm">最小化总完工时间</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" v-model="optimizationTargets.utilization" class="mr-2">
                  <span class="text-sm">最大化设备利用率</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" v-model="optimizationTargets.delay" class="mr-2">
                  <span class="text-sm">最小化延期惩罚</span>
                </label>
              </div>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-700 mb-2 block">缓冲时间（分钟）</label>
              <input 
                type="number" 
                v-model="bufferTime" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                min="0"
                max="120"
              >
            </div>

            <Button @click="applySchedulingParameters" size="sm" class="w-full">
              <CheckCircle class="h-4 w-4 mr-2" />
              应用参数
            </Button>
          </div>
        </div>

        <!-- 资源利用率 -->
        <div class="border rounded-lg p-4 bg-white">
          <div class="flex items-center space-x-2 mb-4">
            <TrendingUp class="h-5 w-5 text-orange-600" />
            <h3 class="text-lg font-medium text-gray-900">资源利用率</h3>
          </div>

          <div v-if="resourceUtilization" class="space-y-3">
            <div v-for="resource in resourceUtilization" :key="resource.name" class="space-y-2">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">{{ resource.name }}</span>
                <span class="text-sm text-gray-600">{{ resource.utilization }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="h-2 rounded-full transition-all duration-300"
                  :class="getUtilizationColorClass(resource.utilization)"
                  :style="{ width: resource.utilization + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 批次详情 -->
        <div v-if="selectedBatch" class="border rounded-lg p-4 bg-white">
          <div class="flex items-center space-x-2 mb-4">
            <Package class="h-5 w-5 text-purple-600" />
            <h3 class="text-lg font-medium text-gray-900">批次详情</h3>
          </div>

          <div class="space-y-3">
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span class="text-gray-600">批次号：</span>
                <span class="font-medium">{{ selectedBatch.batchNumber }}</span>
              </div>
              <div>
                <span class="text-gray-600">工序：</span>
                <span class="font-medium">{{ selectedBatch.processName }}</span>
              </div>
              <div>
                <span class="text-gray-600">开始时间：</span>
                <span class="font-medium">{{ selectedBatch.startTime }}</span>
              </div>
              <div>
                <span class="text-gray-600">持续时间：</span>
                <span class="font-medium">{{ selectedBatch.duration }}小时</span>
              </div>
              <div>
                <span class="text-gray-600">工件数：</span>
                <span class="font-medium">{{ selectedBatch.pieceCount }}件</span>
              </div>
              <div>
                <span class="text-gray-600">状态：</span>
                <span 
                  class="inline-flex px-2 py-1 rounded-full text-xs font-medium"
                  :class="getBatchStatusClass(selectedBatch.status)"
                >
                  {{ getBatchStatusText(selectedBatch.status) }}
                </span>
              </div>
            </div>

            <div class="pt-3 border-t space-y-2">
              <Button size="sm" variant="outline" class="w-full">
                <Edit class="h-3 w-3 mr-1" />
                调整时间
              </Button>
              <Button size="sm" variant="outline" class="w-full">
                <ArrowRight class="h-3 w-3 mr-1" />
                移动到其他工序
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="mt-6 flex items-center justify-between p-4 bg-gray-50 border rounded-lg">
      <div class="flex items-center space-x-4 text-sm text-gray-600">
        <span>排程状态：</span>
        <span 
          class="inline-flex px-2 py-1 rounded-full text-xs font-medium"
          :class="getScheduleStatusClass()"
        >
          {{ getScheduleStatusText() }}
        </span>
      </div>
      
      <div class="flex space-x-3">
        <Button variant="outline" @click="handleStepBack">
          <ArrowLeft class="h-4 w-4 mr-2" />
          返回上一步
        </Button>
        <Button @click="confirmSchedule" :disabled="!scheduleData">
          确认排程方案
          <ArrowRight class="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { 
  Calendar, 
  BarChart3, 
  Settings, 
  TrendingUp, 
  Package, 
  Play, 
  RefreshCw, 
  Zap, 
  CheckCircle, 
  Edit, 
  ArrowRight, 
  ArrowLeft
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';

// Props
interface Props {
  workOrder: any;
  cuttingResult?: any;
}

interface Emits {
  (e: 'step-completed', step: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 排程步骤定义
const schedulingSteps = [
  { id: 'parameter', title: '参数设置', description: '配置排程策略', icon: Settings },
  { id: 'generate', title: '生成排程', description: '计算执行计划', icon: Play },
  { id: 'optimize', title: '优化调整', description: '手动微调', icon: Zap },
  { id: 'confirm', title: '确认发布', description: '发布执行', icon: CheckCircle }
];

// 当前步骤状态
const currentStep = ref<string>('parameter');

// 排程数据
const scheduleData = ref<any>(null);
const selectedBatch = ref<any>(null);

// 排程参数
const schedulingStrategy = ref<string>('mixed_strategy');
const optimizationTargets = ref({
  makespan: true,
  utilization: true,
  delay: false
});
const bufferTime = ref<number>(30);

// 资源利用率数据
const resourceUtilization = ref<any[]>([
  { name: '切割中心', utilization: 85 },
  { name: '磨边中心', utilization: 72 },
  { name: '钢化中心', utilization: 68 },
  { name: '清洗中心', utilization: 90 },
  { name: '包装中心', utilization: 55 }
]);

// 步骤样式方法
const getStepCircleClass = (stepId: string) => {
  const stepOrder = ['parameter', 'generate', 'optimize', 'confirm'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepId);

  if (stepIndex < currentIndex) {
    return 'bg-green-500 border-green-500 text-white';
  } else if (stepIndex === currentIndex) {
    return 'bg-blue-500 border-blue-500 text-white';
  } else {
    return 'bg-white border-gray-300 text-gray-400';
  }
};

const getStepIconClass = (stepId: string) => {
  const stepOrder = ['parameter', 'generate', 'optimize', 'confirm'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepId);

  if (stepIndex <= currentIndex) {
    return 'text-white';
  } else {
    return 'text-gray-400';
  }
};

const getStepTextClass = (stepId: string) => {
  const stepOrder = ['parameter', 'generate', 'optimize', 'confirm'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepId);

  if (stepIndex < currentIndex) {
    return 'text-green-600';
  } else if (stepIndex === currentIndex) {
    return 'text-blue-600';
  } else {
    return 'text-gray-400';
  }
};

const getStepConnectionClass = (index: number) => {
  const stepOrder = ['parameter', 'generate', 'optimize', 'confirm'];
  const currentIndex = stepOrder.indexOf(currentStep.value);

  if (index < currentIndex) {
    return 'bg-green-500';
  } else {
    return 'bg-gray-300';
  }
};

// 生成排程数据
const generateSchedule = async () => {
  currentStep.value = 'generate';
  
  // 模拟排程生成
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  scheduleData.value = generateMockScheduleData();
  currentStep.value = 'optimize';
};

const generateMockScheduleData = () => {
  return {
    timeRange: {
      start: '2024-01-15 08:00',
      end: '2024-01-17 18:00'
    },
    estimatedCompletion: '2024-01-17 16:30',
    processes: [
      {
        id: 'cutting',
        name: '切割工序',
        workCenter: '切割中心-01',
        batches: [
          { 
            id: 'batch-1', 
            batchNumber: 'B001', 
            startTime: '08:00', 
            duration: 4, 
            pieceCount: 24, 
            status: 'scheduled',
            processName: '切割工序'
          },
          { 
            id: 'batch-2', 
            batchNumber: 'B002', 
            startTime: '13:00', 
            duration: 3, 
            pieceCount: 18, 
            status: 'scheduled',
            processName: '切割工序'
          }
        ]
      },
      {
        id: 'grinding',
        name: '磨边工序',
        workCenter: '磨边中心-01',
        batches: [
          { 
            id: 'batch-3', 
            batchNumber: 'B001', 
            startTime: '12:30', 
            duration: 3, 
            pieceCount: 24, 
            status: 'scheduled',
            processName: '磨边工序'
          },
          { 
            id: 'batch-4', 
            batchNumber: 'B002', 
            startTime: '16:30', 
            duration: 2, 
            pieceCount: 18, 
            status: 'scheduled',
            processName: '磨边工序'
          }
        ]
      },
      {
        id: 'tempering',
        name: '钢化工序',
        workCenter: '钢化中心-01',
        batches: [
          { 
            id: 'batch-5', 
            batchNumber: 'B001', 
            startTime: '16:00', 
            duration: 2, 
            pieceCount: 24, 
            status: 'scheduled',
            processName: '钢化工序'
          }
        ]
      }
    ],
    batches: [
      { id: 'B001', totalPieces: 24, processes: 3 },
      { id: 'B002', totalPieces: 18, processes: 2 }
    ]
  };
};

// 批次样式方法
const getBatchColorClass = (status: string) => {
  const colorMap = {
    'scheduled': 'bg-blue-500',
    'in_progress': 'bg-yellow-500',
    'completed': 'bg-green-500',
    'delayed': 'bg-red-500'
  };
  return colorMap[status as keyof typeof colorMap] || 'bg-gray-500';
};

const getBatchStyle = (batch: any) => {
  const startHour = parseInt(batch.startTime.split(':')[0]);
  const left = (startHour / 24) * 100;
  const width = (batch.duration / 24) * 100;
  
  return {
    left: `${left}%`,
    width: `${Math.max(width, 4)}%`
  };
};

const getBatchStatusClass = (status: string) => {
  const classMap = {
    'scheduled': 'bg-blue-100 text-blue-800',
    'in_progress': 'bg-yellow-100 text-yellow-800',
    'completed': 'bg-green-100 text-green-800',
    'delayed': 'bg-red-100 text-red-800'
  };
  return classMap[status as keyof typeof classMap] || 'bg-gray-100 text-gray-800';
};

const getBatchStatusText = (status: string) => {
  const textMap = {
    'scheduled': '已排程',
    'in_progress': '进行中',
    'completed': '已完成',
    'delayed': '延期'
  };
  return textMap[status as keyof typeof textMap] || '未知';
};

// 资源利用率样式
const getUtilizationColorClass = (utilization: number) => {
  if (utilization >= 90) return 'bg-red-500';
  if (utilization >= 75) return 'bg-yellow-500';
  if (utilization >= 50) return 'bg-green-500';
  return 'bg-blue-500';
};

// 排程状态
const getScheduleStatusClass = () => {
  if (!scheduleData.value) return 'bg-gray-100 text-gray-800';
  return 'bg-green-100 text-green-800';
};

const getScheduleStatusText = () => {
  if (!scheduleData.value) return '待生成';
  return '已优化';
};

// 交互方法
const selectBatch = (batch: any) => {
  selectedBatch.value = batch;
};

const refreshSchedule = () => {
  scheduleData.value = null;
  currentStep.value = 'parameter';
};

const autoOptimize = () => {
  // 自动优化逻辑
  if (scheduleData.value) {
    currentStep.value = 'optimize';
  }
};

const applySchedulingParameters = () => {
  // 应用排程参数
  if (scheduleData.value) {
    generateSchedule();
  }
};

const confirmSchedule = () => {
  currentStep.value = 'confirm';
  
  // 延迟确认，模拟发布过程
  setTimeout(() => {
    emit('step-completed', 'scheduling');
  }, 1000);
};

const handleStepBack = () => {
  emit('step-completed', 'step-back');
};

onMounted(() => {
  // 组件挂载完成，可以开始参数设置
});
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
