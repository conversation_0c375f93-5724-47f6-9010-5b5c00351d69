<template>
  <div class="space-y-6">
    <!-- 步骤说明和操作指导 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
          <ClipboardList class="h-4 w-4 text-blue-600" />
          <h4 class="font-medium text-blue-900">工单构成审查</h4>
        </div>
        <Button variant="ghost" size="sm" @click="showHelp = !showHelp">
          <HelpCircle class="h-4 w-4" />
        </Button>
      </div>
      <p class="text-sm text-blue-700">根据工单中的批次产品信息，汇总物料清单并关联库存信息，为生产计划提供参考</p>

      <!-- 操作指导 -->
      <div v-if="showHelp" class="mt-3 p-3 bg-blue-100 rounded-lg">
        <h5 class="text-sm font-medium text-blue-900 mb-2">操作指导：</h5>
        <ul class="text-xs text-blue-800 space-y-1">
          <li>• 检查物料清单的完整性和准确性</li>
          <li>• 确认原料库存是否满足生产需求</li>
          <li>• 识别可能的库存短缺和供应风险</li>
          <li>• 评估原料质量等级与产品要求的匹配度</li>
        </ul>
      </div>
    </div>

    <!-- 物料需求汇总 -->
    <div class="border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h4 class="font-medium text-gray-900 flex items-center">
          <Package class="h-4 w-4 mr-2" />
          物料需求汇总
          <Badge variant="outline" class="ml-2">{{ materialSummary.length }}种材料</Badge>
        </h4>
      </div>
      
      <div class="p-4">
        <div v-if="isLoading" class="text-center py-8 text-gray-500">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
          正在分析物料需求...
        </div>
        
        <div v-else class="space-y-4">
          <!-- 原片玻璃需求 -->
          <div class="bg-blue-50 rounded-lg p-4">
            <h5 class="text-sm font-medium text-blue-900 mb-3 flex items-center">
              <Package class="h-4 w-4 mr-2" />
              原片玻璃需求
            </h5>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div 
                v-for="glass in materialSummary.filter(m => m.category === 'glass')" 
                :key="glass.materialCode"
                class="bg-white border border-blue-200 rounded-lg p-3"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-900">{{ glass.materialName }}</span>
                  <Badge 
                    :variant="getStockStatusVariant(glass.stockStatus)"
                    size="sm"
                  >
                    {{ getStockStatusText(glass.stockStatus) }}
                  </Badge>
                </div>
                <div class="space-y-1 text-xs text-gray-600">
                  <div>规格: {{ glass.specification }}</div>
                  <div class="flex justify-between">
                    <span>需求: {{ glass.requiredQuantity }}{{ glass.unit }}</span>
                  </div>
                  <div class="grid grid-cols-2 gap-2 bg-blue-50 p-1 rounded">
                    <div>需求面积: {{ glass.totalRequiredArea?.toFixed(1) }}m²</div>
                    <div>库存面积: {{ glass.availableArea?.toFixed(1) }}m²</div>
                  </div>
                  <div class="flex justify-end">
                    <span 
                      :class="glass.shortage > 0 ? 'text-red-600 font-medium' : 'text-green-600'"
                    >
                      {{ glass.shortage > 0 ? `缺口: ${glass.shortage}${glass.unit}` : '充足' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 型材辅料需求 -->
          <div class="bg-green-50 rounded-lg p-4">
            <h5 class="text-sm font-medium text-green-900 mb-3 flex items-center">
              <Package class="h-4 w-4 mr-2" />
              型材辅料需求
            </h5>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div 
                v-for="accessory in materialSummary.filter(m => m.category === 'accessory')" 
                :key="accessory.materialCode"
                class="bg-white border border-green-200 rounded-lg p-3"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-900">{{ accessory.materialName }}</span>
                  <Badge 
                    :variant="getStockStatusVariant(accessory.stockStatus)"
                    size="sm"
                  >
                    {{ getStockStatusText(accessory.stockStatus) }}
                  </Badge>
                </div>
                <div class="space-y-1 text-xs text-gray-600">
                  <div>规格: {{ accessory.specification }}</div>
                  <div class="flex justify-between">
                    <span>需求: {{ accessory.requiredQuantity }}{{ accessory.unit }}</span>
                  </div>
                  <div class="flex justify-end">
                    <span 
                      :class="accessory.shortage > 0 ? 'text-red-600 font-medium' : 'text-green-600'"
                    >
                      {{ accessory.shortage > 0 ? `缺口: ${accessory.shortage}${accessory.unit}` : '充足' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 包装材料需求 -->
          <div class="bg-orange-50 rounded-lg p-4">
            <h5 class="text-sm font-medium text-orange-900 mb-3 flex items-center">
              <Package class="h-4 w-4 mr-2" />
              包装材料需求
            </h5>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div 
                v-for="packaging in materialSummary.filter(m => m.category === 'packaging')" 
                :key="packaging.materialCode"
                class="bg-white border border-orange-200 rounded-lg p-3"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-900">{{ packaging.materialName }}</span>
                  <Badge 
                    :variant="getStockStatusVariant(packaging.stockStatus)"
                    size="sm"
                  >
                    {{ getStockStatusText(packaging.stockStatus) }}
                  </Badge>
                </div>
                <div class="space-y-1 text-xs text-gray-600">
                  <div>规格: {{ packaging.specification }}</div>
                  <div class="flex justify-between">
                    <span>需求: {{ packaging.requiredQuantity }}{{ packaging.unit }}</span>
                  </div>
                  <div v-if="packaging.stockUnit && packaging.stockUnit !== packaging.unit" class="bg-orange-50 p-1 rounded text-xs">
                    <div>库存: {{ packaging.stockQuantity }}{{ packaging.stockUnit }} ({{ packaging.availableStock }}{{ packaging.unit }})</div>
                    <div class="text-gray-500">换算: 1{{ packaging.stockUnit }} = {{ packaging.conversionRate }}{{ packaging.unit }}</div>
                  </div>
                  <div class="flex justify-end">
                    <span 
                      :class="packaging.shortage > 0 ? 'text-red-600 font-medium' : 'text-green-600'"
                    >
                      {{ packaging.shortage > 0 ? `缺口: ${packaging.shortage}${packaging.unit}` : '充足' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存风险预警 -->
    <div class="border border-gray-200 rounded-lg" v-if="riskItems.length > 0">
      <div class="px-4 py-3 border-b border-gray-200 bg-red-50">
        <h4 class="font-medium text-red-900 flex items-center">
          <AlertTriangle class="h-4 w-4 mr-2" />
          库存风险预警
          <Badge variant="destructive" class="ml-2">{{ riskItems.length }}个风险项</Badge>
        </h4>
      </div>
      
      <div class="p-4">
        <div class="space-y-3">
          <div 
            v-for="risk in riskItems" 
            :key="risk.materialCode"
            class="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-lg"
          >
            <AlertTriangle class="h-4 w-4 text-red-600 flex-shrink-0" />
            <div class="flex-1">
              <div class="text-sm font-medium text-red-900">
                {{ risk.materialName }} - {{ risk.specification }}
              </div>
              <div class="text-xs text-red-700 mt-1">
                需求: {{ risk.requiredQuantity }}{{ risk.unit }}，
                缺口: {{ risk.shortage }}{{ risk.unit }}
              </div>
              <div class="text-xs text-red-600 mt-1">
                建议: {{ risk.recommendation }}
              </div>
            </div>
            <Button variant="outline" size="sm" @click="handleProcurement">
              申请采购
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存状态汇总 -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 mb-3 flex items-center">
        <Calculator class="h-4 w-4 mr-2" />
        库存状态汇总
      </h4>
      <div class="grid grid-cols-2 gap-4 text-center">
        <div>
          <div class="text-xl font-bold text-green-600">{{ materialReadyPercentage }}%</div>
          <div class="text-xs text-gray-600">材料就绪率</div>
        </div>
        <div>
          <div class="text-xl font-bold text-orange-600">{{ riskItems.length }}</div>
          <div class="text-xs text-gray-600">风险材料数</div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <!-- <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 mb-3 flex items-center">
        <Zap class="h-4 w-4 mr-2" />
        快速操作
      </h4>
      <div class="grid grid-cols-2 gap-2 mb-4">
        <Button variant="outline" size="sm" @click="handleRefreshStock">
          <RefreshCw class="h-4 w-4 mr-2" />
          刷新库存数据
        </Button>
        <Button variant="outline" size="sm" @click="handleExportBOM">
          <Download class="h-4 w-4 mr-2" />
          导出BOM清单
        </Button>
      </div>
    </div> -->

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button class="flex-1" @click="completeReview" :disabled="!canProceed">
        <Scissors class="h-4 w-4 mr-2" />
        确认物料清单，进入切割优化
      </Button>
      <Button variant="outline" class="flex-1" @click="handleRequestProcurement" v-if="riskItems.length > 0">
        <ShoppingCart class="h-4 w-4 mr-2" />
        批量申请采购
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Package,
  AlertTriangle,
  Scissors,
  ClipboardList,
  HelpCircle,
  Zap,
  Calculator,
  RefreshCw,
  Download,
  ShoppingCart
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

interface MaterialRequirement {
  materialCode: string;
  materialName: string;
  category: 'glass' | 'accessory' | 'packaging';
  specification: string;
  requiredQuantity: number;
  availableStock: number;
  unit: string;
  unitPrice: number;
  supplier: string;
  stockStatus: 'sufficient' | 'pending' | 'shortage';
  shortage: number;
  recommendation?: string;
  // 面积相关字段（用于更准确的需求分析）
  pieceArea?: number; // 单片面积 (m²)
  totalRequiredArea?: number; // 总需求面积 (m²)
  availableArea?: number; // 可用库存面积 (m²)
  // 包装材料单位换算
  stockUnit?: string; // 库存单位
  conversionRate?: number; // 换算比率
  stockQuantity?: number; // 换算后的库存数量
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'step-completed': [step: string];
  'start-cutting-optimization': [data: { workOrder: any; materialSummary: MaterialRequirement[] }];
  'material-requirements-updated': [requirements: MaterialRequirement[]];
}>();

// 数据状态
const materialSummary = ref<MaterialRequirement[]>([]);
const isLoading = ref(false);
const showHelp = ref(false);

// 计算属性
const riskItems = computed(() => {
  return materialSummary.value.filter(item => item.stockStatus === 'shortage' || item.shortage > 0);
});

const materialReadyPercentage = computed(() => {
  if (materialSummary.value.length === 0) return 0;
  const readyItems = materialSummary.value.filter(item => item.stockStatus === 'sufficient');
  return Math.round((readyItems.length / materialSummary.value.length) * 100);
});

const canProceed = computed(() => {
  // 如果没有关键材料短缺，可以继续
  const criticalShortages = riskItems.value.filter(item => 
    item.category === 'glass' && item.shortage > item.requiredQuantity * 0.5
  );
  return criticalShortages.length === 0;
});

// 根据工单批次信息生成物料需求
const generateMaterialRequirements = (workOrder: any): MaterialRequirement[] => {
  if (!workOrder?.items) return [];

  const materialMap = new Map<string, MaterialRequirement>();

  // 遍历工单项，计算物料需求
  workOrder.items.forEach((item: any) => {
    const specs = item.specifications;
    const quantity = item.quantity;

    // 根据产品族生成物料需求
    switch (item.productFamilyId) {
      case 'PF-IGU': // 中空玻璃
        // 内片玻璃
        const innerGlassType = specs.innerGlass?.type || 'clear';
        const innerThickness = specs.innerGlass?.thickness || 5;
        const innerArea = (specs.length * specs.width) / 1000000; // 单片面积(m²)
        
        addGlassMaterialRequirement(materialMap, {
          materialCode: `GLASS-${innerThickness}-${innerGlassType}`,
          materialName: `${innerThickness}mm ${getGlassTypeName(innerGlassType)}`,
          category: 'glass',
          specification: `${specs.length}×${specs.width}×${innerThickness}mm`,
          requiredQuantity: quantity,
          unit: '片',
          unitPrice: calculateGlassPrice(innerThickness, innerGlassType),
          supplier: '华南玻璃供应商',
          pieceArea: innerArea,
          totalRequiredArea: innerArea * quantity
        });

        // 外片玻璃
        const outerGlassType = specs.outerGlass?.type || 'low_e';
        const outerThickness = specs.outerGlass?.thickness || 5;
        const outerArea = (specs.length * specs.width) / 1000000;
        
        addGlassMaterialRequirement(materialMap, {
          materialCode: `GLASS-${outerThickness}-${outerGlassType}`,
          materialName: `${outerThickness}mm ${getGlassTypeName(outerGlassType)}`,
          category: 'glass',
          specification: `${specs.length}×${specs.width}×${outerThickness}mm`,
          requiredQuantity: quantity,
          unit: '片',
          unitPrice: calculateGlassPrice(outerThickness, outerGlassType),
          supplier: '华南玻璃供应商',
          pieceArea: outerArea,
          totalRequiredArea: outerArea * quantity
        });

        // 间隔条
        addMaterialRequirement(materialMap, {
          materialCode: `SPACER-${specs.spacer_width || 12}`,
          materialName: `${specs.spacer_width || 12}mm 间隔条`,
          category: 'accessory',
          specification: `宽度${specs.spacer_width || 12}mm`,
          requiredQuantity: quantity * 2 * (specs.length + specs.width) / 1000, // 米
          unit: '米',
          unitPrice: 15,
          supplier: '型材供应商'
        });

        // 密封胶
        addMaterialRequirement(materialMap, {
          materialCode: 'SEALANT-STRUCTURAL',
          materialName: '结构密封胶',
          category: 'accessory',
          specification: '590ml/支',
          requiredQuantity: Math.ceil(quantity * 0.3), // 每片约需0.3支
          unit: '支',
          unitPrice: 45,
          supplier: '胶水供应商'
        });
        break;

      case 'PF-TEMPERED': // 钢化玻璃
        const temperedArea = (specs.length * specs.width) / 1000000;
        
        addGlassMaterialRequirement(materialMap, {
          materialCode: `GLASS-${specs.thickness}-${specs.glassType}`,
          materialName: `${specs.thickness}mm ${getGlassTypeName(specs.glassType)}`,
          category: 'glass',
          specification: `${specs.length}×${specs.width}×${specs.thickness}mm`,
          requiredQuantity: quantity,
          unit: '片',
          unitPrice: calculateGlassPrice(specs.thickness, specs.glassType),
          supplier: '华南玻璃供应商',
          pieceArea: temperedArea,
          totalRequiredArea: temperedArea * quantity
        });
        break;

      case 'PF-LAMINATED': // 夹胶玻璃
        const laminatedArea = (specs.length * specs.width) / 1000000;
        
        // 内层玻璃
        addGlassMaterialRequirement(materialMap, {
          materialCode: `GLASS-${specs.glass1_thickness}-clear`,
          materialName: `${specs.glass1_thickness}mm 透明玻璃`,
          category: 'glass',
          specification: `${specs.length}×${specs.width}×${specs.glass1_thickness}mm`,
          requiredQuantity: quantity,
          unit: '片',
          unitPrice: calculateGlassPrice(specs.glass1_thickness, 'clear'),
          supplier: '华南玻璃供应商',
          pieceArea: laminatedArea,
          totalRequiredArea: laminatedArea * quantity
        });

        // 外层玻璃
        addGlassMaterialRequirement(materialMap, {
          materialCode: `GLASS-${specs.glass2_thickness}-clear`,
          materialName: `${specs.glass2_thickness}mm 透明玻璃`,
          category: 'glass',
          specification: `${specs.length}×${specs.width}×${specs.glass2_thickness}mm`,
          requiredQuantity: quantity,
          unit: '片',
          unitPrice: calculateGlassPrice(specs.glass2_thickness, 'clear'),
          supplier: '华南玻璃供应商',
          pieceArea: laminatedArea,
          totalRequiredArea: laminatedArea * quantity
        });

        // PVB胶片
        addMaterialRequirement(materialMap, {
          materialCode: `PVB-${specs.pvb_thickness}`,
          materialName: `${specs.pvb_thickness}mm PVB胶片`,
          category: 'accessory',
          specification: `厚度${specs.pvb_thickness}mm`,
          requiredQuantity: quantity * (specs.length * specs.width) / 1000000, // 平方米
          unit: 'm²',
          unitPrice: 80,
          supplier: '胶片供应商'
        });
        break;
    }

    // 通用包装材料
    addMaterialRequirement(materialMap, {
      materialCode: 'PACK-WOODEN-CRATE',
      materialName: '木箱包装',
      category: 'packaging',
      specification: '标准木箱',
      requiredQuantity: Math.ceil(quantity / 50), // 每50片一个木箱
      unit: '个',
      unitPrice: 120,
      supplier: '包装供应商'
    });

    // 泡沫保护膜 - 按片需求，但库存按卷管理
    addPackagingMaterialRequirement(materialMap, {
      materialCode: 'PACK-FOAM-PROTECTION',
      materialName: '泡沫保护膜',
      category: 'packaging',
      specification: '5mm厚度',
      requiredQuantity: quantity * 2, // 每片两面
      unit: '片',
      stockUnit: '卷', // 库存单位
      conversionRate: 100, // 每卷100片
      unitPrice: 3,
      supplier: '包装供应商'
    });

    // 防护角条 - 按米需求，但库存按根管理
    addPackagingMaterialRequirement(materialMap, {
      materialCode: 'PACK-CORNER-PROTECTION',
      materialName: '防护角条',
      category: 'packaging',
      specification: '纸质角条',
      requiredQuantity: quantity * 0.02 * (specs.length + specs.width) / 500, // 按周长计算米数
      unit: '米',
      stockUnit: '根', // 库存单位
      conversionRate: 3, // 每根3米
      unitPrice: 8,
      supplier: '包装供应商'
    });
  });

  return Array.from(materialMap.values());
};

// 添加包装材料需求到映射中（支持单位换算）
const addPackagingMaterialRequirement = (materialMap: Map<string, MaterialRequirement>, requirement: Partial<MaterialRequirement>) => {
  const key = requirement.materialCode!;
  
  if (materialMap.has(key)) {
    const existing = materialMap.get(key)!;
    existing.requiredQuantity += requirement.requiredQuantity || 0;
  } else {
    // 模拟库存数据 - 支持单位换算
    const stockInBaseUnit = Math.floor(Math.random() * 20) + 5; // 5-25个库存单位
    const conversionRate = requirement.conversionRate || 1;
    const availableStock = stockInBaseUnit * conversionRate; // 换算为需求单位
    
    const reqQty = requirement.requiredQuantity || 0;
    const shortage = Math.max(0, reqQty - availableStock);
    
    let stockStatus: 'sufficient' | 'pending' | 'shortage' = 'sufficient';
    if (shortage > 0) {
      stockStatus = 'shortage';
    } else if (availableStock < reqQty * 1.2) {
      stockStatus = 'pending';
    }

    materialMap.set(key, {
      materialCode: key,
      materialName: requirement.materialName || '',
      category: requirement.category || 'packaging',
      specification: requirement.specification || '',
      requiredQuantity: reqQty,
      availableStock,
      unit: requirement.unit || '个',
      unitPrice: requirement.unitPrice || 0,
      supplier: requirement.supplier || '未知供应商',
      stockStatus,
      shortage,
      stockUnit: requirement.stockUnit,
      conversionRate: requirement.conversionRate,
      stockQuantity: stockInBaseUnit,
      recommendation: shortage > 0 ? 
        `建议采购${Math.ceil(shortage / conversionRate)}${requirement.stockUnit}` : 
        undefined
    });
  }
};

// 添加玻璃物料需求到映射中（按面积计算）
const addGlassMaterialRequirement = (materialMap: Map<string, MaterialRequirement>, requirement: Partial<MaterialRequirement>) => {
  const key = requirement.materialCode!;
  
  if (materialMap.has(key)) {
    const existing = materialMap.get(key)!;
    existing.requiredQuantity += requirement.requiredQuantity || 0;
    existing.totalRequiredArea = (existing.totalRequiredArea || 0) + (requirement.totalRequiredArea || 0);
  } else {
    // 模拟库存数据 - 按面积计算
    const availableStock = Math.floor(Math.random() * 1000) + 200; // 200-1200片
    const availableArea = availableStock * (requirement.pieceArea || 1); // 可用总面积
    
    const reqQty = requirement.requiredQuantity || 0;
    const reqArea = requirement.totalRequiredArea || 0;
    const shortage = Math.max(0, reqQty - availableStock);
    const areaShortage = Math.max(0, reqArea - availableArea);
    
    let stockStatus: 'sufficient' | 'pending' | 'shortage' = 'sufficient';
    if (shortage > 0 || areaShortage > 0) {
      stockStatus = 'shortage';
    } else if (availableStock < reqQty * 1.2 || availableArea < reqArea * 1.2) {
      stockStatus = 'pending';
    }

    materialMap.set(key, {
      materialCode: key,
      materialName: requirement.materialName || '',
      category: requirement.category || 'glass',
      specification: requirement.specification || '',
      requiredQuantity: reqQty,
      availableStock,
      unit: requirement.unit || '片',
      unitPrice: requirement.unitPrice || 0,
      supplier: requirement.supplier || '未知供应商',
      stockStatus,
      shortage,
      pieceArea: requirement.pieceArea,
      totalRequiredArea: requirement.totalRequiredArea,
      availableArea,
      recommendation: shortage > 0 ? 
        `建议立即采购${shortage}${requirement.unit}，约${areaShortage.toFixed(1)}m²` : 
        undefined
    });
  }
};

// 添加物料需求到映射中
const addMaterialRequirement = (materialMap: Map<string, MaterialRequirement>, requirement: Partial<MaterialRequirement>) => {
  const key = requirement.materialCode!;
  
  if (materialMap.has(key)) {
    const existing = materialMap.get(key)!;
    existing.requiredQuantity += requirement.requiredQuantity || 0;
  } else {
    // 模拟库存数据
    const availableStock = Math.floor(Math.random() * 1000) + 50;
    const reqQty = requirement.requiredQuantity || 0;
    const shortage = Math.max(0, reqQty - availableStock);
    
    let stockStatus: 'sufficient' | 'pending' | 'shortage' = 'sufficient';
    if (shortage > 0) {
      stockStatus = 'shortage';
    } else if (availableStock < reqQty * 1.2) {
      stockStatus = 'pending';
    }

    materialMap.set(key, {
      materialCode: key,
      materialName: requirement.materialName || '',
      category: requirement.category || 'glass',
      specification: requirement.specification || '',
      requiredQuantity: reqQty,
      availableStock,
      unit: requirement.unit || '片',
      unitPrice: requirement.unitPrice || 0,
      supplier: requirement.supplier || '未知供应商',
      stockStatus,
      shortage,
      recommendation: shortage > 0 ? `建议立即采购${shortage}${requirement.unit}` : undefined
    });
  }
};

// 计算玻璃价格
const calculateGlassPrice = (thickness: number, type: string): number => {
  const basePrice = 80; // 基础价格每平方米
  const thicknessMultiplier = thickness / 5; // 厚度系数
  const typeMultiplier = {
    'clear': 1.0,
    'tinted': 1.2,
    'low_e': 1.8,
    'reflective': 1.5
  }[type] || 1.0;
  
  return basePrice * thicknessMultiplier * typeMultiplier;
};

// 获取玻璃类型名称
const getGlassTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    'clear': '透明玻璃',
    'tinted': '有色玻璃',
    'low_e': 'Low-E玻璃',
    'reflective': '反射玻璃'
  };
  return typeMap[type] || '透明玻璃';
};

// 状态相关方法
const getStockStatusVariant = (status: string): "default" | "destructive" | "outline" | "secondary" => {
  switch (status) {
    case 'sufficient': return 'outline';
    case 'pending': return 'secondary';
    case 'shortage': return 'destructive';
    default: return 'secondary';
  }
};

const getStockStatusText = (status: string): string => {
  switch (status) {
    case 'sufficient': return '库存充足';
    case 'pending': return '库存紧张';
    case 'shortage': return '库存不足';
    default: return '状态未知';
  }
};

// 操作方法
const handleProcurement = () => {
  // 打开采购申请对话框
  // 这里可以调用采购API或打开采购申请对话框
};

const handleRefreshStock = () => {
  // 刷新库存数据
  loadMaterialRequirements();
};

const handleExportBOM = () => {
  // 导出BOM清单
  // 这里可以生成并下载BOM文件
};

const handleRequestProcurement = () => {
  // 批量申请采购
  // 这里可以批量处理采购申请
};

const completeReview = () => {
  if (canProceed.value) {
    // 发出切割优化事件，传递工单和物料需求数据
    emit('start-cutting-optimization', {
      workOrder: props.workOrder,
      materialSummary: materialSummary.value
    });
  }
};

// 加载物料需求数据
const loadMaterialRequirements = async () => {
  if (!props.workOrder) return;

  isLoading.value = true;
  try {
    // 基于工单数据生成物料需求
    materialSummary.value = generateMaterialRequirements(props.workOrder);
    
    // 向父组件传递物料需求数据
    emit('material-requirements-updated', materialSummary.value);
    
    // 模拟异步加载延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  } catch {
    // 处理加载错误
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  loadMaterialRequirements();
});

// 监听props变化
watch(() => props.workOrder, (newWorkOrder) => {
  if (newWorkOrder) {
    loadMaterialRequirements();
  }
}, { immediate: true });
</script>
