<template>
  <div class="space-y-6">
    <!-- 步骤说明 -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <Zap class="h-4 w-4 text-green-600" />
        <h4 class="font-medium text-green-900">合并优化决策</h4>
      </div>
      <p class="text-sm text-green-700">基于AI算法提供智能优化建议，支持工单合并和资源优化</p>
    </div>

    <!-- 智能优化建议 -->
    <div class="border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h4 class="font-medium text-gray-900 flex items-center">
          <Brain class="h-4 w-4 mr-2" />
          智能优化建议
        </h4>
      </div>
      
      <div class="p-4">
        <!-- 推荐的优化方案 -->
        <div v-if="isLoading" class="text-center py-4 text-gray-500">
          加载中...
        </div>
        <div v-else-if="optimizationData?.mergeRecommendation" class="space-y-4">
          <div class="p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <h5 class="font-medium text-blue-900">推荐方案A：工单合并优化</h5>
              </div>
              <Badge variant="default" size="sm">推荐</Badge>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
              <div class="text-center p-2 bg-white rounded">
                <div class="text-lg font-bold text-blue-700">+{{ optimizationData.mergeRecommendation.benefits.efficiencyImprovement }}%</div>
                <div class="text-xs text-blue-600">效率提升</div>
              </div>
              <div class="text-center p-2 bg-white rounded">
                <div class="text-lg font-bold text-green-700">-{{ optimizationData.mergeRecommendation.benefits.timeSaving }}天</div>
                <div class="text-xs text-green-600">工期缩短</div>
              </div>
              <div class="text-center p-2 bg-white rounded">
                <div class="text-lg font-bold text-green-700">-{{ optimizationData.mergeRecommendation.benefits.costReduction }}%</div>
                <div class="text-xs text-green-600">成本降低</div>
              </div>
              <div class="text-center p-2 bg-white rounded">
                <div class="text-lg font-bold text-blue-700">{{ optimizationData.mergeRecommendation.benefits.materialUtilization }}%</div>
                <div class="text-xs text-blue-600">材料利用率</div>
              </div>
            </div>
            <div class="text-sm text-blue-700">
              {{ optimizationData.mergeRecommendation.description }}
            </div>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                <h5 class="font-medium text-gray-900">方案B：独立执行</h5>
              </div>
              <Badge variant="secondary" size="sm">备选</Badge>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
              <div class="text-center p-2 bg-gray-50 rounded">
                <div class="text-lg font-bold text-gray-700">+0%</div>
                <div class="text-xs text-gray-600">效率提升</div>
              </div>
              <div class="text-center p-2 bg-gray-50 rounded">
                <div class="text-lg font-bold text-gray-700">5天</div>
                <div class="text-xs text-gray-600">标准工期</div>
              </div>
              <div class="text-center p-2 bg-gray-50 rounded">
                <div class="text-lg font-bold text-gray-700">标准</div>
                <div class="text-xs text-gray-600">成本水平</div>
              </div>
              <div class="text-center p-2 bg-gray-50 rounded">
                <div class="text-lg font-bold text-gray-700">85%</div>
                <div class="text-xs text-gray-600">材料利用率</div>
              </div>
            </div>
            <div class="text-sm text-gray-600">
              按原计划独立执行，不进行工单合并
            </div>
          </div>
        </div>

        <!-- 合并工单详情 -->
        <div class="mt-6">
          <h5 class="text-sm font-medium text-gray-800 mb-3">合并工单详情</h5>
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <table class="w-full text-sm">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-3 py-2 text-left">工单号</th>
                  <th class="px-3 py-2 text-left">客户</th>
                  <th class="px-3 py-2 text-left">产品类型</th>
                  <th class="px-3 py-2 text-left">数量</th>
                  <th class="px-3 py-2 text-left">优先级</th>
                  <th class="px-3 py-2 text-left">状态</th>
                </tr>
              </thead>
              <tbody>
                <tr class="border-t bg-blue-50">
                  <td class="px-3 py-2 font-medium">{{ props.workOrder?.workOrderNumber || 'WO-2024-0002' }}</td>
                  <td class="px-3 py-2">{{ props.workOrder?.customerName || '万科集团' }}</td>
                  <td class="px-3 py-2">透明玻璃</td>
                  <td class="px-3 py-2">385片</td>
                  <td class="px-3 py-2">
                    <Badge variant="secondary" size="sm">中</Badge>
                  </td>
                  <td class="px-3 py-2">
                    <Badge variant="default" size="sm">当前工单</Badge>
                  </td>
                </tr>
                <tr v-if="optimizationData?.mergeRecommendation?.mergeDetails" class="border-t">
                  <td class="px-3 py-2 font-medium">{{ optimizationData.mergeRecommendation.mergeDetails.targetWorkOrder }}</td>
                  <td class="px-3 py-2">{{ optimizationData.mergeRecommendation.mergeDetails.targetCustomer }}</td>
                  <td class="px-3 py-2">透明玻璃</td>
                  <td class="px-3 py-2">{{ optimizationData.mergeRecommendation.mergeDetails.targetItems }}项</td>
                  <td class="px-3 py-2">
                    <Badge variant="secondary" size="sm">中</Badge>
                  </td>
                  <td class="px-3 py-2">
                    <Badge variant="outline" size="sm">待发布</Badge>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 一键合并执行 -->
    <div class="border border-green-200 rounded-lg bg-green-50">
      <div class="px-4 py-3 border-b border-green-200">
        <h4 class="font-medium text-green-900 flex items-center">
          <Merge class="h-4 w-4 mr-2" />
          一键合并执行
        </h4>
      </div>
      
      <div class="p-4">
        <div class="mb-4">
          <div class="flex items-center gap-2 mb-2">
            <CheckCircle class="h-4 w-4 text-green-600" />
            <span class="text-sm font-medium text-green-900">合并执行预检查</span>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
            <div class="flex items-center gap-1">
              <Check class="h-3 w-3 text-green-600" />
              <span>工艺兼容性</span>
            </div>
            <div class="flex items-center gap-1">
              <Check class="h-3 w-3 text-green-600" />
              <span>交期可行性</span>
            </div>
            <div class="flex items-center gap-1">
              <Check class="h-3 w-3 text-green-600" />
              <span>资源可用性</span>
            </div>
            <div class="flex items-center gap-1">
              <Check class="h-3 w-3 text-green-600" />
              <span>质量标准</span>
            </div>
          </div>
        </div>

        <div class="bg-white border border-green-200 rounded-lg p-3">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-green-900">合并后预期效果</span>
            <Badge variant="outline" size="sm">已验证</Badge>
          </div>
          <div v-if="optimizationData?.mergeRecommendation?.mergeDetails" class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-lg font-bold text-green-700">{{ optimizationData.mergeRecommendation.mergeDetails.combinedQuantity }}片</div>
              <div class="text-xs text-green-600">总产量</div>
            </div>
            <div>
              <div class="text-lg font-bold text-blue-700">{{ optimizationData.mergeRecommendation.mergeDetails.estimatedDuration }}天</div>
              <div class="text-xs text-blue-600">合并工期</div>
            </div>
            <div>
              <div class="text-lg font-bold text-green-700">{{ optimizationData.mergeRecommendation.mergeDetails.materialUtilization }}%</div>
              <div class="text-xs text-green-600">材料利用率</div>
            </div>
          </div>
          <div v-else class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-lg font-bold text-gray-400">-</div>
              <div class="text-xs text-gray-600">总产量</div>
            </div>
            <div>
              <div class="text-lg font-bold text-gray-400">-</div>
              <div class="text-xs text-gray-600">合并工期</div>
            </div>
            <div>
              <div class="text-lg font-bold text-gray-400">-</div>
              <div class="text-xs text-gray-600">材料利用率</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 决策确认 -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 mb-3">决策确认</h4>
      <div class="space-y-3">
        <div class="flex items-center gap-3">
          <input 
            type="radio" 
            id="merge" 
            name="decision" 
            value="merge" 
            v-model="selectedDecision"
            class="text-blue-600"
          />
          <label for="merge" class="text-sm font-medium text-gray-900">
            采用推荐方案A：工单合并优化
          </label>
        </div>
        <div class="flex items-center gap-3">
          <input 
            type="radio" 
            id="independent" 
            name="decision" 
            value="independent" 
            v-model="selectedDecision"
            class="text-blue-600"
          />
          <label for="independent" class="text-sm font-medium text-gray-900">
            采用方案B：独立执行
          </label>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button 
        class="flex-1" 
        @click="completeOptimization"
        :disabled="!selectedDecision"
      >
        <Play class="h-4 w-4 mr-2" />
        确认决策，进入执行阶段
      </Button>
      <Button variant="outline" class="flex-1">
        <RotateCcw class="h-4 w-4 mr-2" />
        重新分析
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { productionReleaseService } from '@/services/productionReleaseService';
import {
  Zap,
  Brain,
  Merge,
  CheckCircle,
  Check,
  Play,
  RotateCcw
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'step-completed': [step: string];
}>();

// 优化建议数据
const optimizationData = ref<any>(null);
const isLoading = ref(false);
const selectedDecision = ref<string>('');

// 加载优化建议数据
const loadOptimizationData = async () => {
  if (!props.workOrder?.id) return;

  isLoading.value = true;
  try {
    optimizationData.value = await productionReleaseService.getOptimizationSuggestions(props.workOrder.id);
  } catch (error) {
    console.error('加载优化建议数据失败:', error);
  } finally {
    isLoading.value = false;
  }
};

const completeOptimization = () => {
  if (selectedDecision.value) {
    emit('step-completed', 'optimization');
  }
};

onMounted(() => {
  loadOptimizationData();
});
</script>
