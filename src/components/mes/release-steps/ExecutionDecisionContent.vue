<template>
  <div class="space-y-6">
    <!-- 步骤说明 -->
    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <Rocket class="h-4 w-4 text-orange-600" />
        <h4 class="font-medium text-orange-900">决策与执行</h4>
      </div>
      <p class="text-sm text-orange-700">
        汇总前三步成果，最终决策确认，正式发布生产任务
      </p>
    </div>

    <!-- 前三步成果汇总 -->
    <div class="border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h4 class="font-medium text-gray-900 flex items-center">
          <ClipboardCheck class="h-4 w-4 mr-2" />
          前期准备成果汇总
        </h4>
      </div>

      <div class="p-4 space-y-4">
        <!-- 第一步：工单构成审查成果 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h5 class="text-sm font-medium text-blue-900 flex items-center">
              <CheckCircle class="h-4 w-4 mr-2" />
              步骤一：工单构成审查
            </h5>
            <Badge variant="outline" class="text-blue-700 border-blue-300"
              >已完成</Badge
            >
          </div>
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-lg font-bold text-blue-700">
                {{ reviewSummary.totalItems }}
              </div>
              <div class="text-xs text-blue-600">工单项目</div>
            </div>
            <div>
              <div class="text-lg font-bold text-blue-700">
                {{ reviewSummary.batchCount }}
              </div>
              <div class="text-xs text-blue-600">优化批次</div>
            </div>
            <div>
              <div class="text-lg font-bold text-green-700">
                {{ reviewSummary.materialStatus }}
              </div>
              <div class="text-xs text-green-600">物料状态</div>
            </div>
          </div>
        </div>

        <!-- 第二步：切割优化成果 -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h5 class="text-sm font-medium text-green-900 flex items-center">
              <CheckCircle class="h-4 w-4 mr-2" />
              步骤二：切割优化
            </h5>
            <Badge variant="outline" class="text-green-700 border-green-300"
              >已完成</Badge
            >
          </div>
          <div class="grid grid-cols-4 gap-4 text-center">
            <div>
              <div class="text-lg font-bold text-green-700">
                {{ cuttingSummary.efficiency }}%
              </div>
              <div class="text-xs text-green-600">材料利用率</div>
            </div>
            <div>
              <div class="text-lg font-bold text-green-700">
                ¥{{ formatCurrency(cuttingSummary.costSaving) }}
              </div>
              <div class="text-xs text-green-600">成本节约</div>
            </div>
            <div>
              <div class="text-lg font-bold text-green-700">
                {{ cuttingSummary.sheetCount }}
              </div>
              <div class="text-xs text-green-600">原片数量</div>
            </div>
            <div>
              <div class="text-lg font-bold text-green-700">
                {{ cuttingSummary.wasteReduction }}%
              </div>
              <div class="text-xs text-green-600">废料减少</div>
            </div>
          </div>
        </div>

        <!-- 第三步：生产排程成果 -->
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h5 class="text-sm font-medium text-purple-900 flex items-center">
              <CheckCircle class="h-4 w-4 mr-2" />
              步骤三：生产排程
            </h5>
            <Badge variant="outline" class="text-purple-700 border-purple-300"
              >已完成</Badge
            >
          </div>
          <div class="grid grid-cols-4 gap-4 text-center">
            <div>
              <div class="text-lg font-bold text-purple-700">
                {{ schedulingSummary.totalDuration }}
              </div>
              <div class="text-xs text-purple-600">预计工期</div>
            </div>
            <div>
              <div class="text-lg font-bold text-purple-700">
                {{ schedulingSummary.equipmentUtilization }}%
              </div>
              <div class="text-xs text-purple-600">设备利用率</div>
            </div>
            <div>
              <div class="text-lg font-bold text-purple-700">
                {{ schedulingSummary.startDate }}
              </div>
              <div class="text-xs text-purple-600">计划开始</div>
            </div>
            <div>
              <div class="text-lg font-bold text-purple-700">
                {{ schedulingSummary.deliveryDate }}
              </div>
              <div class="text-xs text-purple-600">预计交付</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最终决策确认 -->
    <div class="border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h4 class="font-medium text-gray-900 flex items-center">
          <Target class="h-4 w-4 mr-2" />
          最终决策确认
        </h4>
      </div>

      <div class="p-4">
        <!-- 综合效益评估 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium text-gray-800 mb-3">综合效益评估</h5>
          <div
            class="p-4 bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg"
          >
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center mb-4">
              <div>
                <div class="text-2xl font-bold text-blue-700">
                  {{ finalSummary.totalValue }}
                </div>
                <div class="text-xs text-blue-600">订单总价值</div>
              </div>
              <div>
                <div class="text-2xl font-bold text-green-700">
                  {{ finalSummary.profitMargin }}%
                </div>
                <div class="text-xs text-green-600">预期利润率</div>
              </div>
              <div>
                <div class="text-2xl font-bold text-orange-700">
                  {{ finalSummary.deliveryDays }}
                </div>
                <div class="text-xs text-orange-600">交付周期(天)</div>
              </div>
              <div>
                <div class="text-2xl font-bold text-purple-700">
                  {{ finalSummary.riskLevel }}
                </div>
                <div class="text-xs text-purple-600">风险等级</div>
              </div>
            </div>

            <!-- 关键决策点 -->
            <div class="border-t border-blue-200 pt-4">
              <div class="text-sm font-medium text-blue-900 mb-3">
                关键决策点确认
              </div>
              <div class="space-y-2">
                <div
                  class="flex items-center justify-between p-2 bg-white rounded border"
                >
                  <span class="text-sm text-gray-700">原料采购计划</span>
                  <div class="flex items-center gap-2">
                    <CheckCircle class="h-4 w-4 text-green-500" />
                    <span class="text-xs text-green-600">已确认</span>
                  </div>
                </div>
                <div
                  class="flex items-center justify-between p-2 bg-white rounded border"
                >
                  <span class="text-sm text-gray-700">设备产能分配</span>
                  <div class="flex items-center gap-2">
                    <CheckCircle class="h-4 w-4 text-green-500" />
                    <span class="text-xs text-green-600">已确认</span>
                  </div>
                </div>
                <div
                  class="flex items-center justify-between p-2 bg-white rounded border"
                >
                  <span class="text-sm text-gray-700">质量标准要求</span>
                  <div class="flex items-center gap-2">
                    <CheckCircle class="h-4 w-4 text-green-500" />
                    <span class="text-xs text-green-600">已确认</span>
                  </div>
                </div>
                <div
                  class="flex items-center justify-between p-2 bg-white rounded border"
                >
                  <span class="text-sm text-gray-700">交付时间承诺</span>
                  <div class="flex items-center gap-2">
                    <CheckCircle class="h-4 w-4 text-green-500" />
                    <span class="text-xs text-green-600">已确认</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 正式发布生产任务 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium text-gray-800 mb-3">
            正式发布生产任务
          </h5>

          <!-- 发布确认按钮 -->
          <div
            v-if="!isReleased"
            class="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg"
          >
            <div class="flex items-center justify-between">
              <div>
                <h6 class="text-sm font-medium text-orange-900 mb-1">
                  准备发布生产任务
                </h6>
                <p class="text-xs text-orange-700">
                  确认所有准备工作已完成，即将正式下达生产任务到各车间
                </p>
              </div>
              <Button
                @click="handleReleaseProduction"
                class="bg-orange-600 hover:bg-orange-700 text-white"
                size="lg"
              >
                <Rocket class="h-4 w-4 mr-2" />
                正式发布
              </Button>
            </div>
          </div>

          <!-- 发布后的车间状态 -->
          <div v-if="isReleased" class="space-y-3">
            <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-500 rounded-full animate-pulse"
                  ></div>
                  <div>
                    <div class="text-sm font-medium text-green-900">
                      切割车间
                    </div>
                    <div class="text-xs text-green-700">
                      任务已发布 - {{ releaseTime }}
                    </div>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    class="text-green-700 border-green-300"
                    >已发布</Badge
                  >
                  <Button variant="outline" size="sm">
                    <MessageSquare class="h-3 w-3 mr-1" />
                    联系车间
                  </Button>
                </div>
              </div>
              <div class="text-xs text-green-600 bg-white p-2 rounded border">
                工单编号: {{ workOrder?.id }} | 预计开始: 明天 08:00 | 负责人:
                张师傅
              </div>
            </div>

            <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-3">
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <div class="text-sm font-medium text-blue-900">
                      磨边车间
                    </div>
                    <div class="text-xs text-blue-700">
                      等待切割完成后自动发布
                    </div>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <Badge variant="secondary" size="sm">待发布</Badge>
                  <Button variant="outline" size="sm" disabled>
                    <Clock class="h-3 w-3 mr-1" />
                    等待中
                  </Button>
                </div>
              </div>
              <div class="text-xs text-blue-600 bg-white p-2 rounded border">
                预计开始: 后天 14:00 | 负责人: 李师傅 | 依赖: 切割车间完成
              </div>
            </div>

            <div class="p-3 bg-purple-50 border border-purple-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-3">
                  <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">
                      包装车间
                    </div>
                    <div class="text-xs text-gray-700">等待前序工序完成</div>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <Badge variant="secondary" size="sm">待发布</Badge>
                  <Button variant="outline" size="sm" disabled>
                    <Clock class="h-3 w-3 mr-1" />
                    等待中
                  </Button>
                </div>
              </div>
            </div>
          </div>

          
        </div>
      </div>

      <!-- 异常处理流程 -->
      <div class="border border-orange-200 rounded-lg">
        <div class="px-4 py-3 border-b border-orange-200 bg-orange-50">
          <h4 class="font-medium text-orange-900 flex items-center">
            <AlertTriangle class="h-4 w-4 mr-2" />
            异常处理流程
          </h4>
        </div>

        <div class="p-4">
          <div class="space-y-3">
            <!-- 无异常状态 -->
            <div
              class="flex items-center justify-center p-6 bg-green-50 border border-green-200 rounded-lg"
            >
              <div class="text-center">
                <CheckCircle class="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div class="text-sm font-medium text-green-900">当前无异常</div>
                <div class="text-xs text-green-700">所有工序按计划正常执行</div>
              </div>
            </div>

            <!-- 异常处理选项 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
              <Button
                variant="outline"
                class="h-auto p-3 flex flex-col items-center gap-2"
              >
                <AlertTriangle class="h-4 w-4 text-orange-600" />
                <div class="text-xs text-center">
                  <div class="font-medium">报告异常</div>
                  <div class="text-gray-500">设备故障、质量问题</div>
                </div>
              </Button>

              <Button
                variant="outline"
                class="h-auto p-3 flex flex-col items-center gap-2"
              >
                <Clock class="h-4 w-4 text-blue-600" />
                <div class="text-xs text-center">
                  <div class="font-medium">调整计划</div>
                  <div class="text-gray-500">延期、加急处理</div>
                </div>
              </Button>

              <Button
                variant="outline"
                class="h-auto p-3 flex flex-col items-center gap-2"
              >
                <Users class="h-4 w-4 text-purple-600" />
                <div class="text-xs text-center">
                  <div class="font-medium">资源调配</div>
                  <div class="text-gray-500">人员、设备调整</div>
                </div>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Rocket,
  Settings,
  ArrowRight,
  MessageSquare,
  Clock,
  AlertTriangle,
  CheckCircle,
  Users,
  RotateCcw,
  ClipboardCheck,
  Target,
} from "lucide-vue-next";
import { ref } from "vue";

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  "step-completed": [step: string];
}>();

// 响应式状态
const isReleased = ref(false);
const releaseTime = ref("");

// 前三步成果汇总数据
const reviewSummary = ref({
  totalItems: 45,
  batchCount: 3,
  materialStatus: "充足",
});

const cuttingSummary = ref({
  efficiency: 92.5,
  costSaving: 15680,
  sheetCount: 12,
  wasteReduction: 28.7,
});

const schedulingSummary = ref({
  totalDuration: "3天",
  equipmentUtilization: 85,
  startDate: "明天",
  deliveryDate: "周五",
});

const finalSummary = ref({
  totalValue: "¥485,600",
  profitMargin: 18.5,
  deliveryDays: 3,
  riskLevel: "低",
});

// 方法
const formatCurrency = (value: number): string => {
  return value.toLocaleString();
};

const handleReleaseProduction = () => {
  isReleased.value = true;
  releaseTime.value = new Date().toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  });

  // 延迟完成步骤，给用户时间查看发布结果
  setTimeout(() => {
    emit("step-completed", "execution");
  }, 2000);
};

const completeExecution = () => {
  emit("step-completed", "execution");
};
</script>
