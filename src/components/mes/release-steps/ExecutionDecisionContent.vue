<template>
  <div class="space-y-6">
    <!-- 步骤说明 -->
    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <Rocket class="h-4 w-4 text-orange-600" />
        <h4 class="font-medium text-orange-900">决策与执行</h4>
      </div>
      <p class="text-sm text-orange-700">最终计划微调，发布到车间，处理异常情况</p>
    </div>

    <!-- 计划微调与发布 -->
    <div class="border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h4 class="font-medium text-gray-900 flex items-center">
          <Settings class="h-4 w-4 mr-2" />
          计划微调与发布
        </h4>
      </div>
      
      <div class="p-4">
        <!-- 最终计划确认 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium text-gray-800 mb-3">最终计划确认</h5>
          <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center mb-4">
              <div>
                <div class="text-lg font-bold text-blue-700">605片</div>
                <div class="text-xs text-blue-600">合并总量</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-700">3天</div>
                <div class="text-xs text-green-600">预计工期</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-700">92%</div>
                <div class="text-xs text-green-600">材料利用率</div>
              </div>
              <div>
                <div class="text-lg font-bold text-blue-700">明天</div>
                <div class="text-xs text-blue-600">计划开始</div>
              </div>
            </div>
            
            <div class="border-t border-blue-200 pt-3">
              <div class="text-sm font-medium text-blue-900 mb-2">工艺路线确认</div>
              <div class="flex items-center gap-2 text-xs text-blue-700">
                <span class="px-2 py-1 bg-white rounded">原料准备</span>
                <ArrowRight class="h-3 w-3" />
                <span class="px-2 py-1 bg-white rounded">切割加工</span>
                <ArrowRight class="h-3 w-3" />
                <span class="px-2 py-1 bg-white rounded">磨边处理</span>
                <ArrowRight class="h-3 w-3" />
                <span class="px-2 py-1 bg-white rounded">质检包装</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 发布到车间 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium text-gray-800 mb-3">发布到车间</h5>
          <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div class="flex items-center gap-3">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <div class="text-sm font-medium text-green-900">切割车间</div>
                  <div class="text-xs text-green-700">工单已发布，等待确认</div>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Badge variant="outline" size="sm">已发布</Badge>
                <Button variant="outline" size="sm">
                  <MessageSquare class="h-3 w-3 mr-1" />
                  联系车间
                </Button>
              </div>
            </div>
            
            <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div class="flex items-center gap-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <div class="text-sm font-medium text-blue-900">磨边车间</div>
                  <div class="text-xs text-blue-700">等待切割完成后自动发布</div>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Badge variant="secondary" size="sm">待发布</Badge>
                <Button variant="outline" size="sm" disabled>
                  <Clock class="h-3 w-3 mr-1" />
                  等待中
                </Button>
              </div>
            </div>
            
            <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div class="flex items-center gap-3">
                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                <div>
                  <div class="text-sm font-medium text-gray-900">包装车间</div>
                  <div class="text-xs text-gray-700">等待前序工序完成</div>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <Badge variant="secondary" size="sm">待发布</Badge>
                <Button variant="outline" size="sm" disabled>
                  <Clock class="h-3 w-3 mr-1" />
                  等待中
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时监控 -->
        <div>
          <h5 class="text-sm font-medium text-gray-800 mb-3">实时监控</h5>
          <div class="p-4 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <span class="text-sm font-medium text-gray-900">执行进度</span>
              <span class="text-sm text-gray-600">5%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
              <div class="bg-blue-600 h-2 rounded-full" style="width: 5%"></div>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div class="text-lg font-bold text-blue-700">1</div>
                <div class="text-xs text-blue-600">进行中任务</div>
              </div>
              <div>
                <div class="text-lg font-bold text-gray-700">0</div>
                <div class="text-xs text-gray-600">已完成任务</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-700">100%</div>
                <div class="text-xs text-green-600">质量合格率</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-700">正常</div>
                <div class="text-xs text-green-600">进度状态</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 异常处理流程 -->
    <div class="border border-orange-200 rounded-lg">
      <div class="px-4 py-3 border-b border-orange-200 bg-orange-50">
        <h4 class="font-medium text-orange-900 flex items-center">
          <AlertTriangle class="h-4 w-4 mr-2" />
          异常处理流程
        </h4>
      </div>
      
      <div class="p-4">
        <div class="space-y-3">
          <!-- 无异常状态 -->
          <div class="flex items-center justify-center p-6 bg-green-50 border border-green-200 rounded-lg">
            <div class="text-center">
              <CheckCircle class="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div class="text-sm font-medium text-green-900">当前无异常</div>
              <div class="text-xs text-green-700">所有工序按计划正常执行</div>
            </div>
          </div>

          <!-- 异常处理选项 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button variant="outline" class="h-auto p-3 flex flex-col items-center gap-2">
              <AlertTriangle class="h-4 w-4 text-orange-600" />
              <div class="text-xs text-center">
                <div class="font-medium">报告异常</div>
                <div class="text-gray-500">设备故障、质量问题</div>
              </div>
            </Button>
            
            <Button variant="outline" class="h-auto p-3 flex flex-col items-center gap-2">
              <Clock class="h-4 w-4 text-blue-600" />
              <div class="text-xs text-center">
                <div class="font-medium">调整计划</div>
                <div class="text-gray-500">延期、加急处理</div>
              </div>
            </Button>
            
            <Button variant="outline" class="h-auto p-3 flex flex-col items-center gap-2">
              <Users class="h-4 w-4 text-purple-600" />
              <div class="text-xs text-center">
                <div class="font-medium">资源调配</div>
                <div class="text-gray-500">人员、设备调整</div>
              </div>
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 发布状态汇总 -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 mb-3">发布状态汇总</h4>
      <div class="grid grid-cols-3 gap-4 text-center">
        <div>
          <div class="text-2xl font-bold text-green-600">已发布</div>
          <div class="text-xs text-gray-600">工单状态</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-blue-600">1/4</div>
          <div class="text-xs text-gray-600">车间确认</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-600">5%</div>
          <div class="text-xs text-gray-600">执行进度</div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button class="flex-1" @click="completeExecution">
        <CheckCircle class="h-4 w-4 mr-2" />
        确认发布完成
      </Button>
      <Button variant="outline" class="flex-1">
        <RotateCcw class="h-4 w-4 mr-2" />
        撤回重新发布
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Rocket,
  Settings,
  ArrowRight,
  MessageSquare,
  Clock,
  AlertTriangle,
  CheckCircle,
  Users,
  RotateCcw
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'step-completed': [step: string];
}>();

const completeExecution = () => {
  emit('step-completed', 'execution');
};
</script>
