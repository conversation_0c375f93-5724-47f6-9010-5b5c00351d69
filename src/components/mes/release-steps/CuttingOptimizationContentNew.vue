<template>
  <div class="h-full flex flex-col">
    <!-- 页面标题 -->
    <div class="mb-6">
      <div class="flex items-center space-x-3 mb-2">
        <Scissors class="h-6 w-6 text-blue-600" />
        <h2 class="text-xl font-semibold text-gray-900">切割优化</h2>
      </div>
      <p class="text-sm text-gray-600">
        与第三方切割优化系统进行数据交换，获取最优切割方案以提高材料利用率
      </p>
    </div>

    <!-- 进度指示器 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <template v-for="(step, index) in optimizationSteps" :key="step.id">
          <!-- 步骤圆圈 -->
          <div class="flex flex-col items-center">
            <div 
              class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300"
              :class="getStepCircleClass(step.id)"
            >
              <component 
                :is="step.icon" 
                class="w-5 h-5"
                :class="getStepIconClass(step.id)"
              />
            </div>
            <div class="mt-2 text-sm text-center font-medium" :class="getStepTextClass(step.id)">
              {{ step.title }}
            </div>
            <div class="mt-1 text-xs text-center text-gray-500">
              {{ step.description }}
            </div>
          </div>
          
          <!-- 连接线 -->
          <div 
            v-if="index < optimizationSteps.length - 1"
            class="flex-1 h-0.5 mx-4 transition-all duration-300"
            :class="getStepConnectionClass(index)"
          />
        </template>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-6">
      <!-- 主要区域：优化结果 -->
      <div class="flex-1 space-y-4">
        <div class="border rounded-lg p-6 bg-white h-full">
          <div class="flex items-center space-x-2 mb-6">
            <BarChart3 class="h-6 w-6 text-purple-600" />
            <h3 class="text-xl font-medium text-gray-900">优化结果</h3>
          </div>

          <div v-if="!importResult" class="flex items-center justify-center h-96 text-gray-500">
            <div class="text-center">
              <TrendingUp class="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <div class="text-lg font-medium mb-2">暂无优化结果</div>
              <div class="text-sm">完成数据导入后查看优化效果</div>
            </div>
          </div>

          <div v-else class="space-y-6">
            <!-- 关键指标展示 -->
            <div class="grid grid-cols-4 gap-4">
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div class="text-3xl font-bold text-blue-600 mb-1">{{ importResult.improvements?.materialUtilization || 0 }}%</div>
                <div class="text-sm text-blue-700">材料利用率</div>
              </div>
              <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div class="text-3xl font-bold text-green-600 mb-1">¥{{ formatCurrency(importResult.improvements?.costSaving || 0) }}</div>
                <div class="text-sm text-green-700">成本节约</div>
              </div>
              <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
                <div class="text-3xl font-bold text-orange-600 mb-1">{{ importResult.improvements?.timeReduction || 0 }}%</div>
                <div class="text-sm text-orange-700">时间减少</div>
              </div>
              <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                <div class="text-3xl font-bold text-purple-600 mb-1">{{ importResult.improvements?.wasteReduction || 0 }}%</div>
                <div class="text-sm text-purple-700">废料减少</div>
              </div>
            </div>

            <!-- 优化方案详细列表 -->
            <div>
              <div class="text-lg font-medium text-gray-900 mb-4">优化方案</div>
              <div class="grid grid-cols-1 gap-3">
                <div v-for="(layout, index) in importResult.optimizedLayouts" :key="index" 
                     class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span class="text-base font-medium text-gray-700">方案 {{ layout.id }}</span>
                    <span class="text-sm text-gray-500">效率 {{ layout.efficiency }}%</span>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">{{ layout.pieces }} 件</div>
                    <div class="text-xs text-gray-500">切割件数</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 详细分析按钮 -->
            <div class="pt-4 border-t">
              <Button @click="handleViewDetails" size="lg" class="w-full">
                <Eye class="h-4 w-4 mr-2" />
                查看详情
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：操作面板 -->
      <div class="w-80 space-y-4">
        <!-- 数据导出卡片 -->
        <div class="border rounded-lg p-4 bg-white">
          <div class="flex items-center space-x-2 mb-4">
            <Download class="h-5 w-5 text-blue-600" />
            <h3 class="text-lg font-medium text-gray-900">数据导出</h3>
          </div>
          
          <div v-if="exportStatus === 'idle'" class="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
            <FileText class="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <div class="text-sm font-medium text-gray-900 mb-1">准备导出切割数据</div>
            <div class="text-xs text-gray-500 mb-3">
              将包含 {{ materialRequirements.length }} 种材料的需求数据导出
            </div>
            <Button @click="handleExportData" size="sm">
              <Download class="h-4 w-4 mr-2" />
              开始导出
            </Button>
          </div>

          <div v-else-if="exportStatus === 'exporting'" class="text-center py-4 border border-blue-200 bg-blue-50 rounded-lg">
            <RefreshCw class="h-6 w-6 text-blue-500 animate-spin mx-auto mb-2" />
            <div class="text-sm font-medium text-blue-900 mb-1">正在导出数据...</div>
            <div class="text-xs text-blue-700">正在整理材料需求和约束条件</div>
          </div>

          <div v-else-if="exportStatus === 'exported'" class="py-4 border border-green-200 bg-green-50 rounded-lg">
            <div class="flex items-center justify-center mb-3">
              <CheckCircle class="h-6 w-6 text-green-500 mr-2" />
              <span class="text-sm font-medium text-green-900">导出完成</span>
            </div>
            <div class="text-center space-y-2">
              <div class="text-xs text-green-700 space-y-1">
                <div>导出时间: {{ new Date().toLocaleString('zh-CN') }}</div>
                <div>文件格式: Excel + JSON</div>
                <div>材料数量: {{ materialRequirements.length }} 种</div>
              </div>
              <Button variant="outline" size="sm" @click="handleDownloadFile" class="w-full">
                <Download class="h-3 w-3 mr-1" />
                下载文件
              </Button>
              <Button variant="outline" size="sm" @click="handleExportData" class="w-full">
                <RefreshCw class="h-3 w-3 mr-1" />
                重新导出
              </Button>
            </div>
          </div>

          <div v-else-if="exportStatus === 'error'" class="text-center py-4 border border-red-200 bg-red-50 rounded-lg">
            <XCircle class="h-6 w-6 text-red-500 mx-auto mb-2" />
            <div class="text-sm font-medium text-red-900 mb-1">导出失败</div>
            <div class="text-xs text-red-700 mb-3">请重试或联系技术支持</div>
            <Button variant="outline" size="sm" @click="handleExportData" class="w-full">
              <RefreshCw class="h-3 w-3 mr-1" />
              重试
            </Button>
          </div>
        </div>

        <!-- 导入结果卡片 -->
        <div class="border rounded-lg p-4 bg-white">
          <div class="flex items-center space-x-2 mb-4">
            <Upload class="h-5 w-5 text-green-600" />
            <h3 class="text-lg font-medium text-gray-900">导入结果</h3>
          </div>

          <div v-if="importStatus === 'waiting'" class="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
            <Upload class="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <div class="text-sm font-medium text-gray-900 mb-1">等待上传优化结果</div>
            <div class="text-xs text-gray-500 mb-3">
              请上传第三方系统生成的切割优化文件
            </div>
            <input
              ref="fileInput"
              type="file"
              class="hidden"
              accept=".xlsx,.xls,.json"
              @change="handleFileSelect"
            />
            <Button @click="triggerFileSelect" size="sm" :disabled="exportStatus !== 'exported'" class="w-full mb-2">
              <Upload class="h-4 w-4 mr-2" />
              选择文件
            </Button>
            <Button variant="ghost" size="sm" @click="handleSimulateUpload" :disabled="exportStatus !== 'exported'" class="w-full">
              <Zap class="h-3 w-3 mr-1" />
              模拟上传
            </Button>
          </div>

          <div v-else-if="importStatus === 'processing'" class="py-4 border border-blue-200 bg-blue-50 rounded-lg">
            <div class="flex items-center justify-center mb-3">
              <RefreshCw class="h-6 w-6 text-blue-500 animate-spin mr-2" />
              <span class="text-sm font-medium text-blue-900">正在处理...</span>
            </div>
            <div class="text-center space-y-2">
              <div class="text-xs text-blue-700 mb-2">{{ getProcessingStageText() }}</div>
              <div class="w-full bg-blue-200 rounded-full h-1.5">
                <div class="bg-blue-600 h-1.5 rounded-full transition-all duration-300" :style="{ width: getProcessingProgress() + '%' }"></div>
              </div>
              <div class="text-xs text-blue-600">{{ getProcessingProgress() }}%</div>
            </div>
          </div>

          <div v-else-if="importStatus === 'completed'" class="py-4 border border-green-200 bg-green-50 rounded-lg">
            <div class="flex items-center justify-center mb-3">
              <CheckCircle class="h-6 w-6 text-green-500 mr-2" />
              <span class="text-sm font-medium text-green-900">导入成功</span>
            </div>
            <div class="text-center space-y-2">
              <div class="text-xs text-green-700 space-y-1">
                <div>处理时间: {{ new Date().toLocaleString('zh-CN') }}</div>
                <div>优化批次: {{ importResult?.optimizedLayouts?.length || 0 }} 个</div>
                <div>利用率提升: +{{ importResult?.improvements?.materialUtilization || 0 }}%</div>
              </div>
              <Button variant="outline" size="sm" @click="handleViewDetails" class="w-full">
                <Eye class="h-3 w-3 mr-1" />
                查看详情
              </Button>
              <Button variant="outline" size="sm" @click="triggerFileSelect" class="w-full">
                <RefreshCw class="h-3 w-3 mr-1" />
                重新选择
              </Button>
            </div>
          </div>

          <div v-else-if="importStatus === 'error'" class="text-center py-4 border border-red-200 bg-red-50 rounded-lg">
            <XCircle class="h-6 w-6 text-red-500 mx-auto mb-2" />
            <div class="text-sm font-medium text-red-900 mb-1">导入失败</div>
            <div class="text-xs text-red-700 mb-3">文件格式不正确或数据损坏</div>
            <Button variant="outline" size="sm" @click="triggerFileSelect" class="w-full">
              <RefreshCw class="h-3 w-3 mr-1" />
              重新选择
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="mt-6 flex justify-between items-center pt-4 border-t border-gray-200">
      <Button variant="outline" @click="handleStepBack" :disabled="currentStep === 'export'">
        <ArrowLeft class="h-4 w-4 mr-2" />
        上一步
      </Button>

      <div class="flex items-center space-x-2">
        <Button 
          v-if="currentStep === 'completed'" 
          @click="handleConfirmResults" 
          class="bg-green-600 hover:bg-green-700 text-white"
        >
          <CheckCircle class="h-4 w-4 mr-2" />
          确认结果
        </Button>
        <Button 
          v-else
          @click="handleStepForward" 
          :disabled="!canProceedToNext"
        >
          下一步
          <ArrowRight class="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Button } from '@/components/ui/button';
import { 
  Scissors, 
  Download, 
  Upload, 
  BarChart3, 
  TrendingUp, 
  FileText, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Eye, 
  ArrowLeft, 
  ArrowRight,
  Zap
} from 'lucide-vue-next';

// 定义props
interface Props {
  workOrder: any;
  materialRequirements: any[];
}

const props = defineProps<Props>();

// 优化步骤定义
const optimizationSteps = [
  { 
    id: 'export', 
    title: '数据导出', 
    description: '导出生产数据',
    icon: Download 
  },
  { 
    id: 'waiting', 
    title: '第三方优化', 
    description: '等待优化完成',
    icon: RefreshCw 
  },
  { 
    id: 'import', 
    title: '导入结果', 
    description: '导入优化结果',
    icon: Upload 
  },
  { 
    id: 'completed', 
    title: '优化完成', 
    description: '结果分析完成',
    icon: CheckCircle 
  }
];

// 状态管理
const currentStep = ref<'export' | 'waiting' | 'import' | 'completed'>('export');
const exportStatus = ref<'idle' | 'exporting' | 'exported' | 'error'>('idle');
const importStatus = ref<'waiting' | 'importing' | 'processing' | 'completed' | 'imported' | 'error'>('waiting');
const exportData = ref<any>(null);
const importResult = ref<any>(null);
const fileInput = ref<HTMLInputElement>();
const simulationData = ref<any>(null);
const processingStage = ref<string>('');

// 样式计算
const getStepCircleClass = (stepId: string) => {
  const stepOrder = ['export', 'waiting', 'import', 'completed'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepId);
  
  if (stepIndex < currentIndex) {
    return 'bg-green-100 border-green-500 text-green-600';
  } else if (stepIndex === currentIndex) {
    return 'bg-blue-100 border-blue-500 text-blue-600';
  } else {
    return 'bg-gray-100 border-gray-300 text-gray-400';
  }
};

const getStepIconClass = (stepId: string) => {
  const stepOrder = ['export', 'waiting', 'import', 'completed'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepId);
  
  if (stepIndex < currentIndex) {
    return 'text-green-600';
  } else if (stepIndex === currentIndex) {
    return 'text-blue-600';
  } else {
    return 'text-gray-400';
  }
};

const getStepTextClass = (stepId: string) => {
  const stepOrder = ['export', 'waiting', 'import', 'completed'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepId);
  
  if (stepIndex <= currentIndex) {
    return 'text-gray-900';
  } else {
    return 'text-gray-500';
  }
};

const getStepConnectionClass = (index: number) => {
  const stepOrder = ['export', 'waiting', 'import', 'completed'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  
  if (index < currentIndex) {
    return 'bg-green-300';
  } else {
    return 'bg-gray-300';
  }
};

// 计算属性
const canProceedToNext = computed(() => {
  switch (currentStep.value) {
    case 'export':
      return exportStatus.value === 'exported';
    case 'waiting':
      return true;
    case 'import':
      return importStatus.value === 'completed' || importStatus.value === 'imported';
    case 'completed':
      return false;
    default:
      return false;
  }
});

// 事件处理
const handleExportData = async () => {
  exportStatus.value = 'exporting';
  
  try {
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    exportData.value = {
      exportTime: new Date(),
      materials: props.materialRequirements,
      format: 'Excel + JSON'
    };
    
    exportStatus.value = 'exported';
    currentStep.value = 'waiting';
  } catch {
    exportStatus.value = 'error';
  }
};

const handleDownloadFile = () => {
  // 模拟文件下载
};

const triggerFileSelect = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];
  if (file) {
    processUploadedFile(file);
  }
};

const handleSimulateUpload = () => {
  // 模拟文件上传
  processUploadedFile(null);
};

const processUploadedFile = async (file: File | null) => {
  importStatus.value = 'processing';
  currentStep.value = 'import';
  
  await loadSimulationData();
  
  try {
    const stages = ['upload', 'validation', 'parsing', 'analysis', 'integration'];
    
    for (const stage of stages) {
      processingStage.value = stage;
      await new Promise(resolve => setTimeout(resolve, 800));
    }
    
    if (simulationData.value?.success !== false) {
      importResult.value = simulationData.value?.results || generateMockResult();
      importStatus.value = 'completed';
      currentStep.value = 'completed';
    }
  } catch {
    importStatus.value = 'error';
  } finally {
    processingStage.value = '';
  }
};

const loadSimulationData = async () => {
  if (!simulationData.value) {
    try {
      const response = await fetch('/mock/cutting/upload-simulation.json');
      simulationData.value = await response.json();
    } catch {
      // 加载模拟数据失败
    }
  }
};

const generateMockResult = () => {
  return {
    improvements: {
      materialUtilization: 92.5,
      costSaving: 15680,
      timeReduction: 35.2,
      wasteReduction: 28.7
    },
    optimizedLayouts: generateMockLayouts(3)
  };
};

const generateMockLayouts = (count: number) => {
  const layouts = [];
  for (let i = 1; i <= count; i++) {
    layouts.push({
      id: i,
      efficiency: Math.round((85 + Math.random() * 12) * 10) / 10,
      pieces: Math.floor(30 + Math.random() * 20)
    });
  }
  return layouts;
};

const getProcessingStageText = () => {
  if (!simulationData.value) return '正在解析优化结果';
  
  const stageMap: Record<string, string> = {
    'upload': '上传文件到服务器...',
    'validation': '验证文件格式和数据结构...',
    'parsing': '解析切割优化结果数据...',
    'analysis': '分析优化效果和质量指标...',
    'integration': '集成到生产计划系统...'
  };
  
  return stageMap[processingStage.value] || '正在处理...';
};

const getProcessingProgress = () => {
  if (!processingStage.value || !simulationData.value) return 0;
  
  const stages = ['upload', 'validation', 'parsing', 'analysis', 'integration'];
  const currentIndex = stages.indexOf(processingStage.value);
  
  if (currentIndex === -1) return 0;
  
  return Math.round(((currentIndex + 1) / stages.length) * 100);
};

const handleViewDetails = () => {
  // 查看优化详情
};

const handleStepBack = () => {
  const stepOrder = ['export', 'waiting', 'import', 'completed'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  if (currentIndex > 0) {
    currentStep.value = stepOrder[currentIndex - 1] as any;
  }
};

const handleStepForward = () => {
  const stepOrder = ['export', 'waiting', 'import', 'completed'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  if (currentIndex < stepOrder.length - 1) {
    currentStep.value = stepOrder[currentIndex + 1] as any;
  }
};

const handleConfirmResults = () => {
  // 确认结果并继续到下一步
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN').format(amount);
};

onMounted(() => {
  // 组件挂载完成
});
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
