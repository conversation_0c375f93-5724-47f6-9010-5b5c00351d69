<template>
  <div class="space-y-6">
    <!-- 步骤说明 -->
    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <BarChart3 class="h-4 w-4 text-purple-600" />
        <h4 class="font-medium text-purple-900">生产可行性分析</h4>
      </div>
      <p class="text-sm text-purple-700">检查关键物料可用性，评估产能负荷，确保生产可行性</p>
    </div>

    <!-- 关键物料可用性检查 -->
    <div class="border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h4 class="font-medium text-gray-900 flex items-center">
          <Package class="h-4 w-4 mr-2" />
          关键物料可用性检查
        </h4>
      </div>
      
      <div class="p-4">
        <div class="space-y-4">
          <!-- 原片玻璃库存 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <h5 class="font-medium text-gray-900">原片玻璃库存</h5>
              <Badge variant="outline" size="sm">充足</Badge>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="text-center p-3 bg-green-50 rounded-lg">
                <div class="text-lg font-bold text-green-700">8</div>
                <div class="text-xs text-green-600">5mm透明玻璃(片)</div>
                <div class="text-xs text-gray-500">需要: 2片</div>
              </div>
              <div class="text-center p-3 bg-green-50 rounded-lg">
                <div class="text-lg font-bold text-green-700">5</div>
                <div class="text-xs text-green-600">8mm有色玻璃(片)</div>
                <div class="text-xs text-gray-500">需要: 1片</div>
              </div>
              <div class="text-center p-3 bg-orange-50 rounded-lg">
                <div class="text-lg font-bold text-orange-700">2</div>
                <div class="text-xs text-orange-600">包装材料(套)</div>
                <div class="text-xs text-gray-500">需要: 3套</div>
              </div>
              <div class="text-center p-3 bg-red-50 rounded-lg">
                <div class="text-lg font-bold text-red-700">0</div>
                <div class="text-xs text-red-600">特殊标签(张)</div>
                <div class="text-xs text-gray-500">需要: 50张</div>
              </div>
            </div>
          </div>

          <!-- 短缺物料处理 -->
          <div class="p-4 border border-red-200 rounded-lg bg-red-50">
            <div class="flex items-center gap-2 mb-3">
              <AlertTriangle class="h-4 w-4 text-red-600" />
              <h5 class="font-medium text-red-900">短缺物料处理</h5>
            </div>
            <div class="space-y-2">
              <div class="flex items-center justify-between p-2 bg-white rounded border">
                <div>
                  <div class="text-sm font-medium text-red-900">特殊标签</div>
                  <div class="text-xs text-red-700">短缺: 50张，预计到货: 2天</div>
                </div>
                <div class="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Phone class="h-3 w-3 mr-1" />
                    联系供应商
                  </Button>
                  <Button variant="outline" size="sm">
                    <Package class="h-3 w-3 mr-1" />
                    替代方案
                  </Button>
                </div>
              </div>
              <div class="flex items-center justify-between p-2 bg-white rounded border">
                <div>
                  <div class="text-sm font-medium text-orange-900">包装材料</div>
                  <div class="text-xs text-orange-700">短缺: 1套，可用替代材料</div>
                </div>
                <div class="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Check class="h-3 w-3 mr-1" />
                    使用替代
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产能负荷评估 -->
    <div class="border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h4 class="font-medium text-gray-900 flex items-center">
          <Activity class="h-4 w-4 mr-2" />
          产能负荷评估
        </h4>
      </div>
      
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 工段负荷分析 -->
          <div>
            <h5 class="text-sm font-medium text-gray-800 mb-3">工段负荷分析</h5>
            <div v-if="isLoading" class="text-center py-4 text-gray-500">
              加载中...
            </div>
            <div v-else-if="analysisData?.workstationCapacity" class="space-y-3">
              <div
                v-for="(capacity, key) in analysisData.workstationCapacity"
                :key="key"
                class="flex items-center justify-between p-3 rounded-lg"
                :class="getCapacityClass(capacity.status)"
              >
                <div>
                  <div
                    class="text-sm font-medium"
                    :class="getCapacityTextClass(capacity.status)"
                  >
                    {{ capacity.name }}
                  </div>
                  <div
                    class="text-xs"
                    :class="getCapacitySubTextClass(capacity.status)"
                  >
                    当前负荷: {{ capacity.currentLoad }}%
                  </div>
                </div>
                <div class="text-right">
                  <div
                    class="text-sm font-bold"
                    :class="getCapacityStatusTextClass(capacity.status)"
                  >
                    {{ getCapacityStatusText(capacity.status) }}
                  </div>
                  <div class="text-xs text-gray-500">余量: {{ capacity.availableCapacity }}%</div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-4 text-gray-500">
              暂无数据
            </div>
          </div>

          <!-- APS接口占位 -->
          <div>
            <h5 class="text-sm font-medium text-gray-800 mb-3">APS高级排程</h5>
            <div class="p-4 border-2 border-dashed border-gray-300 rounded-lg text-center">
              <div class="mb-3">
                <Calendar class="h-8 w-8 text-gray-400 mx-auto" />
              </div>
              <div class="text-sm text-gray-600 mb-2">APS系统接口</div>
              <div class="text-xs text-gray-500 mb-3">
                智能排程算法将在此处提供最优生产计划建议
              </div>
              <Button variant="outline" size="sm" disabled>
                <Settings class="h-3 w-3 mr-1" />
                连接APS系统
              </Button>
            </div>
          </div>
        </div>

        <!-- 时间窗口分析 -->
        <div class="mt-6">
          <h5 class="text-sm font-medium text-gray-800 mb-3">时间窗口分析</h5>
          <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div class="text-lg font-bold text-blue-700">2天</div>
                <div class="text-xs text-blue-600">最早开始</div>
              </div>
              <div>
                <div class="text-lg font-bold text-blue-700">5天</div>
                <div class="text-xs text-blue-600">预计工期</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-700">7天</div>
                <div class="text-xs text-green-600">承诺交期</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-700">85%</div>
                <div class="text-xs text-green-600">按时概率</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 可行性评估结果 -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 mb-3">可行性评估结果</h4>
      <div class="grid grid-cols-3 gap-4 text-center">
        <div>
          <div class="text-2xl font-bold text-orange-600">中等</div>
          <div class="text-xs text-gray-600">物料风险</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-600">良好</div>
          <div class="text-xs text-gray-600">产能状况</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-600">可行</div>
          <div class="text-xs text-gray-600">总体评估</div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button class="flex-1" @click="completeAnalysis">
        <Settings class="h-4 w-4 mr-2" />
        确认可行性，进入优化决策
      </Button>
      <Button variant="outline" class="flex-1">
        <AlertTriangle class="h-4 w-4 mr-2" />
        处理风险项
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { productionReleaseService } from '@/services/productionReleaseService';
import {
  BarChart3,
  Package,
  AlertTriangle,
  Phone,
  Check,
  Activity,
  Calendar,
  Settings
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'step-completed': [step: string];
}>();

// 分析数据
const analysisData = ref<any>(null);
const isLoading = ref(false);

// 加载分析数据
const loadAnalysisData = async () => {
  if (!props.workOrder?.id) return;

  isLoading.value = true;
  try {
    analysisData.value = await productionReleaseService.getProductionAnalysisData(props.workOrder.id);
  } catch (error) {
    console.error('加载生产可行性分析数据失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 辅助方法
const getCapacityClass = (status: string) => {
  switch (status) {
    case 'available': return 'bg-green-50 border border-green-200';
    case 'tight': return 'bg-orange-50 border border-orange-200';
    case 'overloaded': return 'bg-red-50 border border-red-200';
    default: return 'bg-gray-50 border border-gray-200';
  }
};

const getCapacityTextClass = (status: string) => {
  switch (status) {
    case 'available': return 'text-green-900';
    case 'tight': return 'text-orange-900';
    case 'overloaded': return 'text-red-900';
    default: return 'text-gray-900';
  }
};

const getCapacitySubTextClass = (status: string) => {
  switch (status) {
    case 'available': return 'text-green-700';
    case 'tight': return 'text-orange-700';
    case 'overloaded': return 'text-red-700';
    default: return 'text-gray-700';
  }
};

const getCapacityStatusTextClass = (status: string) => {
  switch (status) {
    case 'available': return 'text-green-700';
    case 'tight': return 'text-orange-700';
    case 'overloaded': return 'text-red-700';
    default: return 'text-gray-700';
  }
};

const getCapacityStatusText = (status: string) => {
  switch (status) {
    case 'available': return '可用';
    case 'tight': return '紧张';
    case 'overloaded': return '超载';
    default: return '未知';
  }
};

const completeAnalysis = () => {
  emit('step-completed', 'analysis');
};

onMounted(() => {
  loadAnalysisData();
});
</script>
