<template>
  <div class="space-y-4">
    <!-- 兼容性状态概览 -->
    <Card>
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center gap-2">
          <component :is="getStatusIcon()" class="w-4 h-4" :class="getStatusIconClass()" />
          工艺兼容性检查
          <Badge :variant="getStatusBadgeVariant()" class="text-xs">
            {{ getStatusText() }}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="flex items-center gap-2 text-sm text-gray-600">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          正在检查工艺兼容性...
        </div>
        
        <div v-else-if="compatibilityResult" class="space-y-3">
          <!-- 兼容性统计 -->
          <div class="grid grid-cols-3 gap-4">
            <div class="text-center p-2 bg-gray-50 rounded">
              <div class="text-lg font-bold text-gray-700">{{ selectedOrderItems.length }}</div>
              <div class="text-xs text-gray-600">订单项</div>
            </div>
            <div class="text-center p-2 rounded" :class="getConflictCountClass()">
              <div class="text-lg font-bold">{{ errorConflicts.length }}</div>
              <div class="text-xs">严重冲突</div>
            </div>
            <div class="text-center p-2 rounded" :class="getWarningCountClass()">
              <div class="text-lg font-bold">{{ warningConflicts.length }}</div>
              <div class="text-xs">工艺差异</div>
            </div>
          </div>
          
          <!-- 兼容性建议 -->
          <div v-if="compatibilityResult.suggestions.length > 0" 
               class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="text-sm font-medium text-blue-800 mb-2">兼容性建议</div>
            <ul class="text-xs text-blue-700 space-y-1">
              <li v-for="(suggestion, index) in compatibilityResult.suggestions" 
                  :key="index" 
                  class="flex items-start gap-2">
                <Lightbulb class="w-3 h-3 mt-0.5 flex-shrink-0" />
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
        
        <div v-else class="text-sm text-gray-500 text-center py-4">
          请选择订单项以检查工艺兼容性
        </div>
      </CardContent>
    </Card>
    
    <!-- 冲突详情 -->
    <div v-if="compatibilityResult && compatibilityResult.conflicts.length > 0" class="space-y-3">
      <div 
        v-for="conflict in compatibilityResult.conflicts" 
        :key="conflict.id"
        class="border rounded-lg"
        :class="getConflictCardClass(conflict.severity)"
      >
        <div class="p-4">
          <div class="flex items-start justify-between mb-3">
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-1">
                <component 
                  :is="getConflictIcon(conflict.severity)" 
                  class="w-4 h-4" 
                  :class="getConflictIconClass(conflict.severity)"
                />
                <h4 class="font-medium text-sm">{{ conflict.description }}</h4>
              </div>
              <div class="text-xs text-gray-600">
                冲突类型: {{ getConflictTypeText(conflict.conflictType) }} | 
                影响订单项: {{ conflict.affectedItems.length }}个
              </div>
            </div>
            <div class="flex items-center gap-2">
              <Badge 
                :variant="conflict.severity === 'error' ? 'destructive' : 'secondary'" 
                class="text-xs"
              >
                {{ conflict.severity === 'error' ? '严重' : '警告' }}
              </Badge>
              <Button
                v-if="conflict.autoResolvable"
                size="sm"
                variant="outline"
                @click="resolveConflict(conflict.id)"
                class="text-xs"
              >
                自动解决
              </Button>
            </div>
          </div>
          
          <!-- 受影响的订单项 -->
          <div class="mb-3">
            <div class="text-xs font-medium text-gray-700 mb-2">受影响的订单项:</div>
            <div class="flex flex-wrap gap-1">
              <Badge
                v-for="itemId in conflict.affectedItems.slice(0, 5)"
                :key="itemId"
                variant="outline"
                class="text-xs"
              >
                {{ getOrderItemDisplayName(itemId) }}
              </Badge>
              <Badge
                v-if="conflict.affectedItems.length > 5"
                variant="outline"
                class="text-xs"
              >
                +{{ conflict.affectedItems.length - 5 }}个
              </Badge>
            </div>
          </div>
          
          <!-- 解决建议 -->
          <div v-if="conflict.suggestions.length > 0" class="border-t pt-3">
            <div class="text-xs font-medium text-gray-700 mb-2">解决建议:</div>
            <ul class="text-xs text-gray-600 space-y-1">
              <li v-for="(suggestion, index) in conflict.suggestions" 
                  :key="index" 
                  class="flex items-start gap-2">
                <div class="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                {{ suggestion }}
              </li>
            </ul>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex gap-2 mt-3 pt-3 border-t">
            <Button 
              size="sm" 
              variant="outline"
              @click="showConflictDetails(conflict)"
              class="text-xs"
            >
              <Eye class="w-3 h-3 mr-1" />
              详情
            </Button>
            <Button 
              size="sm" 
              variant="ghost"
              @click="ignoreConflict(conflict.id)"
              class="text-xs"
            >
              忽略
            </Button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分组推荐 -->
    <div v-if="compatibilityResult && compatibilityResult.groupingRecommendations.length > 0">
      <Card>
        <CardHeader class="pb-3">
          <CardTitle class="text-sm flex items-center gap-2">
            <Users class="w-4 h-4" />
            智能分组推荐
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div 
              v-for="group in compatibilityResult.groupingRecommendations" 
              :key="group.groupId"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
              @click="applyGrouping(group)"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="font-medium text-sm">{{ group.groupName }}</div>
                <div class="flex items-center gap-2">
                  <Badge variant="secondary" class="text-xs">
                    {{ group.items.length }}项
                  </Badge>
                  <Badge 
                    :variant="getEfficiencyBadgeVariant(group.efficiency)" 
                    class="text-xs"
                  >
                    效率 {{ group.efficiency }}%
                  </Badge>
                </div>
              </div>
              <div class="text-xs text-gray-600 mb-2">{{ group.reason }}</div>
              <div class="flex flex-wrap gap-1">
                <Badge
                  v-for="itemId in group.items.slice(0, 4)"
                  :key="itemId"
                  variant="outline"
                  class="text-xs"
                >
                  {{ getOrderItemDisplayName(itemId) }}
                </Badge>
                <Badge
                  v-if="group.items.length > 4"
                  variant="outline"
                  class="text-xs"
                >
                  +{{ group.items.length - 4 }}个
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
    
    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button 
        size="sm" 
        @click="recheckCompatibility"
        :disabled="isLoading || selectedOrderItems.length === 0"
      >
        <RefreshCw class="w-3 h-3 mr-1" :class="{ 'animate-spin': isLoading }" />
        重新检查
      </Button>
      <Button 
        size="sm" 
        variant="outline"
        @click="exportReport"
        :disabled="!compatibilityResult"
      >
        <Download class="w-3 h-3 mr-1" />
        导出报告
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  Lightbulb,
  Eye,
  Users,
  RefreshCw,
  Download,
} from 'lucide-vue-next'

import type { 
  SelectedOrderItem,
  ProcessConflict
} from '@/types/production-order-creation'
import type { ProcessCompatibilityResult } from '@/services/processCompatibilityService'
import { processCompatibilityService } from '@/services/processCompatibilityService'

// Props
interface Props {
  selectedOrderItems: SelectedOrderItem[]
  autoCheck?: boolean
}

// Events
interface Emits {
  (e: 'conflict-resolved', conflictId: string): void
  (e: 'conflict-ignored', conflictId: string): void
  (e: 'grouping-applied', groupId: string, itemIds: string[]): void
  (e: 'compatibility-changed', result: ProcessCompatibilityResult): void
}

const props = withDefaults(defineProps<Props>(), {
  autoCheck: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)
const compatibilityResult = ref<ProcessCompatibilityResult | null>(null)

// 计算属性
const errorConflicts = computed(() => {
  return compatibilityResult.value?.conflicts.filter(c => c.severity === 'error') || []
})

const warningConflicts = computed(() => {
  return compatibilityResult.value?.conflicts.filter(c => c.severity === 'warning') || []
})

const isCompatible = computed(() => {
  return compatibilityResult.value?.compatible ?? true
})

// 事件处理
const recheckCompatibility = async () => {
  if (props.selectedOrderItems.length === 0) {
    compatibilityResult.value = null
    return
  }
  
  isLoading.value = true
  try {
    const result = await processCompatibilityService.checkCompatibility(props.selectedOrderItems)
    compatibilityResult.value = result
    emit('compatibility-changed', result)
  } catch (error) {
    console.error('工艺兼容性检查失败:', error)
  } finally {
    isLoading.value = false
  }
}

const resolveConflict = (conflictId: string) => {
  emit('conflict-resolved', conflictId)
  // 移除已解决的冲突
  if (compatibilityResult.value) {
    compatibilityResult.value.conflicts = compatibilityResult.value.conflicts.filter(
      c => c.id !== conflictId
    )
  }
}

const ignoreConflict = (conflictId: string) => {
  emit('conflict-ignored', conflictId)
  // 移除被忽略的冲突
  if (compatibilityResult.value) {
    compatibilityResult.value.conflicts = compatibilityResult.value.conflicts.filter(
      c => c.id !== conflictId
    )
  }
}

const showConflictDetails = (conflict: ProcessConflict) => {
  // 显示冲突详情对话框
  console.log('显示冲突详情:', conflict)
}

const applyGrouping = (group: any) => {
  emit('grouping-applied', group.groupId, group.items)
}

const exportReport = () => {
  // 导出兼容性检查报告
  console.log('导出兼容性报告')
}

// 工具方法
const getStatusIcon = () => {
  if (isLoading.value) return RefreshCw
  if (!compatibilityResult.value) return AlertTriangle
  if (errorConflicts.value.length > 0) return XCircle
  if (warningConflicts.value.length > 0) return AlertTriangle
  return CheckCircle
}

const getStatusIconClass = () => {
  if (isLoading.value) return 'text-blue-600 animate-spin'
  if (!compatibilityResult.value) return 'text-gray-400'
  if (errorConflicts.value.length > 0) return 'text-red-600'
  if (warningConflicts.value.length > 0) return 'text-yellow-600'
  return 'text-green-600'
}

const getStatusText = () => {
  if (isLoading.value) return '检查中'
  if (!compatibilityResult.value) return '待检查'
  if (errorConflicts.value.length > 0) return '存在冲突'
  if (warningConflicts.value.length > 0) return '存在差异'
  return '兼容'
}

const getStatusBadgeVariant = () => {
  if (isLoading.value) return 'secondary'
  if (!compatibilityResult.value) return 'outline'
  if (errorConflicts.value.length > 0) return 'destructive'
  if (warningConflicts.value.length > 0) return 'secondary'
  return 'default'
}

const getConflictCountClass = () => {
  const count = errorConflicts.value.length
  if (count === 0) return 'bg-green-50 text-green-700'
  return 'bg-red-50 text-red-700'
}

const getWarningCountClass = () => {
  const count = warningConflicts.value.length
  if (count === 0) return 'bg-green-50 text-green-700'
  return 'bg-yellow-50 text-yellow-700'
}

const getConflictCardClass = (severity: string) => {
  return severity === 'error' 
    ? 'border-red-200 bg-red-50' 
    : 'border-yellow-200 bg-yellow-50'
}

const getConflictIcon = (severity: string) => {
  return severity === 'error' ? XCircle : AlertTriangle
}

const getConflictIconClass = (severity: string) => {
  return severity === 'error' ? 'text-red-600' : 'text-yellow-600'
}

const getConflictTypeText = (type: string): string => {
  switch (type) {
    case 'parameter': return '参数冲突'
    case 'sequence': return '工序冲突'
    case 'equipment': return '设备冲突'
    case 'material': return '材料冲突'
    default: return type
  }
}

const getOrderItemDisplayName = (itemId: string): string => {
  const item = props.selectedOrderItems.find(item => item.id === itemId)
  if (!item) return itemId
  
  return `${item.orderNumber}-${item.specifications.length}×${item.specifications.width}`
}

const getEfficiencyBadgeVariant = (efficiency: number) => {
  if (efficiency >= 85) return 'default'
  if (efficiency >= 70) return 'secondary'
  return 'outline'
}

// 监听器
watch(
  () => props.selectedOrderItems,
  () => {
    if (props.autoCheck) {
      recheckCompatibility()
    }
  },
  { deep: true, immediate: true }
)
</script>