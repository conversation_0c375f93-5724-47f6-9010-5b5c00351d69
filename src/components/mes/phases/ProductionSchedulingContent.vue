<template>
  <div class="space-y-6">
    <!-- 阶段说明 -->
    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <Calendar class="h-4 w-4 text-purple-600" />
        <h4 class="font-medium text-purple-900">生产排程优化</h4>
      </div>
      <p class="text-sm text-purple-700">优化切割方案，安排生产资源，制定执行时间表</p>
    </div>

    <!-- 切割方案优化 -->
    <div>
      <h4 class="font-medium text-gray-800 mb-3">切割方案优化</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="p-3 border border-blue-200 rounded-lg bg-blue-50">
          <div class="flex items-center justify-between mb-2">
            <span class="font-medium text-blue-900">方案A</span>
            <Badge variant="default" size="sm">推荐</Badge>
          </div>
          <div class="space-y-1 text-sm text-blue-700">
            <div>材料利用率: 87%</div>
            <div>切割时间: 4.5小时</div>
            <div>余料: 13%</div>
          </div>
        </div>
        <div class="p-3 border border-gray-200 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <span class="font-medium text-gray-900">方案B</span>
            <Badge variant="secondary" size="sm">备选</Badge>
          </div>
          <div class="space-y-1 text-sm text-gray-600">
            <div>材料利用率: 82%</div>
            <div>切割时间: 3.8小时</div>
            <div>余料: 18%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资源分配 -->
    <div>
      <h4 class="font-medium text-gray-800 mb-3">资源分配</h4>
      <div class="space-y-2">
        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
          <span class="text-sm text-gray-600">切割工</span>
          <span class="text-sm font-medium">张师傅、李师傅</span>
        </div>
        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
          <span class="text-sm text-gray-600">设备</span>
          <span class="text-sm font-medium">切割台#1, #3</span>
        </div>
        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
          <span class="text-sm text-gray-600">计划时间</span>
          <span class="text-sm font-medium">明天 08:00-17:00</span>
        </div>
      </div>
    </div>

    <!-- 时间线规划 -->
    <div>
      <h4 class="font-medium text-gray-800 mb-3">时间线规划</h4>
      <div class="space-y-3">
        <div class="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          <div class="flex-1">
            <div class="text-sm font-medium text-green-900">原料准备</div>
            <div class="text-xs text-green-700">明天 07:30 - 08:00</div>
          </div>
          <Badge variant="outline" size="sm">已完成</Badge>
        </div>
        <div class="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <div class="flex-1">
            <div class="text-sm font-medium text-blue-900">切割加工</div>
            <div class="text-xs text-blue-700">明天 08:00 - 12:30</div>
          </div>
          <Badge variant="default" size="sm">进行中</Badge>
        </div>
        <div class="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
          <div class="flex-1">
            <div class="text-sm font-medium text-gray-900">磨边处理</div>
            <div class="text-xs text-gray-700">明天 13:30 - 16:00</div>
          </div>
          <Badge variant="secondary" size="sm">待开始</Badge>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button class="flex-1" @click="completeScheduling">
        <Play class="h-4 w-4 mr-2" />
        确认排程，开始执行
      </Button>
      <Button variant="outline" class="flex-1">
        <Settings class="h-4 w-4 mr-2" />
        重新优化
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar,
  Play,
  Settings
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'phase-completed': [phase: string];
}>();

const completeScheduling = () => {
  emit('phase-completed', 'scheduling');
};
</script>
