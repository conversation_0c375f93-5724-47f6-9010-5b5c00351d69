<template>
  <div class="space-y-6">
    <!-- 阶段说明 -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <CheckCircle class="h-4 w-4 text-green-600" />
        <h4 class="font-medium text-green-900">交付确认</h4>
      </div>
      <p class="text-sm text-green-700">最终质量检验，包装发货，客户确认收货</p>
    </div>

    <!-- 交付状态概览 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-3">
        <h4 class="font-medium text-blue-900">交付状态概览</h4>
        <Badge variant="default" size="sm">准备就绪</Badge>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div class="text-2xl font-bold text-blue-700">385</div>
          <div class="text-xs text-blue-600">总数量(片)</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-700">380</div>
          <div class="text-xs text-green-600">合格数量(片)</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-700">98.7%</div>
          <div class="text-xs text-green-600">合格率</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-700">是</div>
          <div class="text-xs text-green-600">按时交付</div>
        </div>
      </div>
    </div>

    <!-- 交付检查清单 -->
    <div class="p-4 border border-gray-200 rounded-lg">
      <h4 class="font-medium text-gray-900 mb-4 flex items-center">
        <ClipboardCheck class="h-4 w-4 mr-2" />
        交付检查清单
      </h4>
      
      <div class="space-y-3">
        <div class="flex items-center gap-3 p-2 bg-green-50 border border-green-200 rounded">
          <Check class="h-4 w-4 text-green-600" />
          <span class="text-sm text-green-900">最终质量检验</span>
          <Badge variant="outline" size="sm" class="ml-auto">已完成</Badge>
        </div>
        <div class="flex items-center gap-3 p-2 bg-green-50 border border-green-200 rounded">
          <Check class="h-4 w-4 text-green-600" />
          <span class="text-sm text-green-900">产品包装</span>
          <Badge variant="outline" size="sm" class="ml-auto">已完成</Badge>
        </div>
        <div class="flex items-center gap-3 p-2 bg-blue-50 border border-blue-200 rounded">
          <Clock class="h-4 w-4 text-blue-600" />
          <span class="text-sm text-blue-900">发货准备</span>
          <Badge variant="default" size="sm" class="ml-auto">进行中</Badge>
        </div>
        <div class="flex items-center gap-3 p-2 bg-gray-50 border border-gray-200 rounded">
          <Circle class="h-4 w-4 text-gray-400" />
          <span class="text-sm text-gray-900">客户确认</span>
          <Badge variant="secondary" size="sm" class="ml-auto">待确认</Badge>
        </div>
      </div>
    </div>

    <!-- 交付文档 -->
    <div>
      <h4 class="font-medium text-gray-800 mb-3">交付文档</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="p-3 border border-gray-200 rounded-lg">
          <div class="flex items-center gap-2 mb-2">
            <FileText class="h-4 w-4 text-blue-600" />
            <span class="text-sm font-medium">质量检验报告</span>
          </div>
          <div class="text-xs text-gray-600">包含所有产品的质量检验数据</div>
          <Button variant="outline" size="sm" class="mt-2 w-full">
            <Download class="h-3 w-3 mr-1" />
            下载
          </Button>
        </div>
        <div class="p-3 border border-gray-200 rounded-lg">
          <div class="flex items-center gap-2 mb-2">
            <FileText class="h-4 w-4 text-green-600" />
            <span class="text-sm font-medium">发货清单</span>
          </div>
          <div class="text-xs text-gray-600">详细的产品发货清单和数量</div>
          <Button variant="outline" size="sm" class="mt-2 w-full">
            <Download class="h-3 w-3 mr-1" />
            下载
          </Button>
        </div>
      </div>
    </div>

    <!-- 客户反馈 -->
    <div class="p-4 border border-gray-200 rounded-lg">
      <h4 class="font-medium text-gray-900 mb-3">客户反馈</h4>
      <div class="bg-gray-50 p-3 rounded text-sm text-gray-600">
        等待客户确认收货和质量反馈...
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button class="flex-1" @click="completeDelivery">
        <Truck class="h-4 w-4 mr-2" />
        确认发货
      </Button>
      <Button variant="outline" class="flex-1">
        <Phone class="h-4 w-4 mr-2" />
        联系客户
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle,
  ClipboardCheck,
  Check,
  Clock,
  Circle,
  FileText,
  Download,
  Truck,
  Phone
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'phase-completed': [phase: string];
}>();

const completeDelivery = () => {
  emit('phase-completed', 'delivered');
};
</script>
