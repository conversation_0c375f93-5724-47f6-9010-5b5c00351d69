<template>
  <div class="space-y-6">
    <!-- 阶段说明 -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <Play class="h-4 w-4 text-green-600" />
        <h4 class="font-medium text-green-900">执行监控</h4>
      </div>
      <p class="text-sm text-green-700">实时监控生产进度，跟踪质量指标，处理异常情况</p>
    </div>

    <!-- 总体进度 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-blue-900">总体执行进度</span>
        <span class="text-sm text-blue-700">{{ overallProgress }}%</span>
      </div>
      <div class="w-full bg-blue-200 rounded-full h-3">
        <div 
          class="bg-blue-600 h-3 rounded-full transition-all duration-300"
          :style="{ width: `${overallProgress}%` }"
        ></div>
      </div>
      <div class="flex justify-between text-xs text-blue-600 mt-2">
        <span>开始时间: 08:00</span>
        <span>预计完成: 17:00</span>
      </div>
    </div>

    <!-- 当前执行任务 -->
    <div class="p-4 border border-gray-200 rounded-lg">
      <h4 class="font-medium text-gray-900 mb-3 flex items-center">
        <Clock class="h-4 w-4 mr-2" />
        当前执行任务
      </h4>
      
      <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium text-blue-900">切割加工 - 5mm透明玻璃</span>
          <Badge variant="default" size="sm">75%</Badge>
        </div>
        <div class="grid grid-cols-2 gap-4 text-sm text-blue-700">
          <div>执行人员: 张师傅</div>
          <div>工作位置: 切割台#1</div>
          <div>开始时间: 08:00</div>
          <div>预计完成: 12:30</div>
        </div>
      </div>
    </div>

    <!-- 质量监控 -->
    <div>
      <h4 class="font-medium text-gray-800 mb-3">质量监控</h4>
      <div class="grid grid-cols-3 gap-4">
        <div class="text-center p-3 bg-green-50 border border-green-200 rounded-lg">
          <div class="text-2xl font-bold text-green-700">98.5%</div>
          <div class="text-xs text-green-600">合格率</div>
        </div>
        <div class="text-center p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <div class="text-2xl font-bold text-orange-700">2</div>
          <div class="text-xs text-orange-600">缺陷数</div>
        </div>
        <div class="text-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="text-2xl font-bold text-blue-700">1</div>
          <div class="text-xs text-blue-600">返工数</div>
        </div>
      </div>
    </div>

    <!-- 工单项进度 -->
    <div>
      <h4 class="font-medium text-gray-800 mb-3">工单项进度</h4>
      <div class="space-y-3">
        <div class="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          <div class="flex-1">
            <div class="text-sm font-medium text-green-900">5mm透明玻璃 1200x800 (180片)</div>
            <div class="text-xs text-green-700">切割完成，进入磨边工序</div>
          </div>
          <Badge variant="outline" size="sm">75%</Badge>
        </div>
        <div class="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <div class="flex-1">
            <div class="text-sm font-medium text-blue-900">5mm透明玻璃 1500x1200 (120片)</div>
            <div class="text-xs text-blue-700">切割进行中</div>
          </div>
          <Badge variant="default" size="sm">45%</Badge>
        </div>
        <div class="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
          <div class="flex-1">
            <div class="text-sm font-medium text-gray-900">8mm有色玻璃 2000x1400 (85片)</div>
            <div class="text-xs text-gray-700">等待切割</div>
          </div>
          <Badge variant="secondary" size="sm">0%</Badge>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button class="flex-1" @click="completeExecution">
        <CheckCircle class="h-4 w-4 mr-2" />
        完成执行，进入交付
      </Button>
      <Button variant="outline" class="flex-1">
        <AlertTriangle class="h-4 w-4 mr-2" />
        报告异常
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Play,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'phase-completed': [phase: string];
}>();

const overallProgress = ref(65);

const completeExecution = () => {
  emit('phase-completed', 'executing');
};
</script>
