<template>
  <div class="space-y-6">
    <!-- 阶段说明 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <ClipboardList class="h-4 w-4 text-blue-600" />
        <h4 class="font-medium text-blue-900">交付计划制定</h4>
      </div>
      <p class="text-sm text-blue-700">分析订单需求，制定可行的交付计划</p>
    </div>

    <!-- 计划制定步骤 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 原料需求分析 -->
      <div class="p-4 border border-gray-200 rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <h5 class="font-medium text-gray-900">原料需求分析</h5>
          <div class="w-6 h-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center text-xs">
            <Check class="h-4 w-4" />
          </div>
        </div>
        <div class="space-y-2 text-sm text-gray-600">
          <div class="flex justify-between">
            <span>透明玻璃 5mm</span>
            <span class="text-green-600">充足</span>
          </div>
          <div class="flex justify-between">
            <span>有色玻璃 8mm</span>
            <span class="text-green-600">充足</span>
          </div>
          <div class="flex justify-between">
            <span>型材铝合金</span>
            <span class="text-orange-600">紧张</span>
          </div>
        </div>
      </div>

      <!-- 产能评估 -->
      <div class="p-4 border border-gray-200 rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <h5 class="font-medium text-gray-900">产能评估</h5>
          <div class="w-6 h-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center text-xs">
            <Check class="h-4 w-4" />
          </div>
        </div>
        <div class="space-y-2 text-sm text-gray-600">
          <div class="flex justify-between">
            <span>切割工段</span>
            <span class="text-green-600">85%</span>
          </div>
          <div class="flex justify-between">
            <span>磨边工段</span>
            <span class="text-green-600">70%</span>
          </div>
          <div class="flex justify-between">
            <span>钢化工段</span>
            <span class="text-orange-600">95%</span>
          </div>
        </div>
      </div>

      <!-- 交期承诺 -->
      <div class="p-4 border border-gray-200 rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <h5 class="font-medium text-gray-900">交期承诺</h5>
          <div class="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs">
            3
          </div>
        </div>
        <div class="space-y-2 text-sm text-gray-600">
          <div class="flex justify-between">
            <span>承诺交期</span>
            <span class="font-medium">2025/9/4</span>
          </div>
          <div class="flex justify-between">
            <span>置信度</span>
            <span class="text-green-600">92%</span>
          </div>
          <div class="flex justify-between">
            <span>风险因素</span>
            <span class="text-orange-600">1项</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button class="flex-1" @click="completePlanning">
        <Calendar class="h-4 w-4 mr-2" />
        确认计划，进入排程
      </Button>
      <Button variant="outline" class="flex-1">
        <ArrowRight class="h-4 w-4 mr-2" />
        重新分析
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ClipboardList,
  Check,
  Calendar,
  ArrowRight
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'phase-completed': [phase: string];
}>();

const completePlanning = () => {
  emit('phase-completed', 'planning');
};
</script>
