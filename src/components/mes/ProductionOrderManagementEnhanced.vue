<template>
  <div class="production-order-management">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">生产工单管理</h1>
          <p class="text-sm text-gray-600 mt-1">管理和跟踪所有生产工单的执行状态</p>
        </div>
        
        <div class="flex items-center gap-3">
          <!-- 快速筛选 -->
          <Select v-model="quickFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="快速筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部工单</SelectItem>
              <SelectItem value="pending">待开始</SelectItem>
              <SelectItem value="in_progress">进行中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
              <SelectItem value="urgent">紧急工单</SelectItem>
            </SelectContent>
          </Select>
          
          <!-- 刷新按钮 -->
          <Button variant="outline" size="sm" @click="refreshData">
            <RefreshCw class="w-4 h-4" :class="{ 'animate-spin': loading }" />
          </Button>
          
          <!-- 新建工单按钮 -->
          <Button @click="showCreateDialog = true" class="bg-blue-600 hover:bg-blue-700">
            <Plus class="w-4 h-4 mr-2" />
            新建工单
          </Button>
        </div>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">总工单数</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.total }}</p>
            </div>
            <div class="p-2 bg-blue-100 rounded-lg">
              <FileText class="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">进行中</p>
              <p class="text-2xl font-bold text-orange-600">{{ stats.inProgress }}</p>
            </div>
            <div class="p-2 bg-orange-100 rounded-lg">
              <Play class="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">已完成</p>
              <p class="text-2xl font-bold text-green-600">{{ stats.completed }}</p>
            </div>
            <div class="p-2 bg-green-100 rounded-lg">
              <CheckCircle class="w-6 h-6 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">紧急工单</p>
              <p class="text-2xl font-bold text-red-600">{{ stats.urgent }}</p>
            </div>
            <div class="p-2 bg-red-100 rounded-lg">
              <AlertTriangle class="w-6 h-6 text-red-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
    
    <!-- 搜索和筛选 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <div class="flex items-center gap-4">
          <div class="flex-1">
            <Input
              v-model="searchQuery"
              placeholder="搜索工单号、客户名称..."
              class="max-w-md"
            >
              <template #prefix>
                <Search class="w-4 h-4 text-gray-400" />
              </template>
            </Input>
          </div>
          
          <Select v-model="statusFilter">
            <SelectTrigger class="w-32">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="pending">待开始</SelectItem>
              <SelectItem value="in_progress">进行中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
              <SelectItem value="cancelled">已取消</SelectItem>
            </SelectContent>
          </Select>
          
          <Select v-model="priorityFilter">
            <SelectTrigger class="w-32">
              <SelectValue placeholder="优先级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部优先级</SelectItem>
              <SelectItem value="urgent">紧急</SelectItem>
              <SelectItem value="high">高</SelectItem>
              <SelectItem value="normal">普通</SelectItem>
              <SelectItem value="low">低</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" @click="resetFilters">
            <X class="w-4 h-4 mr-2" />
            重置
          </Button>
        </div>
      </CardContent>
    </Card>
    
    <!-- 工单列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>工单列表</span>
          <Badge variant="outline">{{ filteredOrders.length }} 个工单</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-0">
        <div v-if="loading" class="p-8 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-sm text-gray-600">加载工单数据...</p>
        </div>
        
        <div v-else-if="filteredOrders.length === 0" class="p-8 text-center text-gray-500">
          <FileText class="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p class="text-sm font-medium mb-2">暂无工单数据</p>
          <p class="text-xs">点击"新建工单"开始创建第一个工单</p>
        </div>
        
        <div v-else class="divide-y">
          <div 
            v-for="order in filteredOrders" 
            :key="order.id"
            class="p-4 hover:bg-gray-50 transition-colors cursor-pointer"
            @click="selectOrder(order)"
          >
            <div class="flex items-center justify-between">
              <!-- 左侧信息 -->
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <h3 class="font-medium text-gray-900">{{ order.workOrderNumber }}</h3>
                  <Badge :variant="getStatusVariant(order.status)">
                    {{ getStatusText(order.status) }}
                  </Badge>
                  <Badge :variant="getPriorityVariant(order.priority)">
                    {{ getPriorityText(order.priority) }}
                  </Badge>
                </div>
                
                <div class="flex items-center gap-4 text-sm text-gray-600">
                  <span class="flex items-center gap-1">
                    <Users class="w-3 h-3" />
                    {{ order.customerName }}
                  </span>
                  <span class="flex items-center gap-1">
                    <Package class="w-3 h-3" />
                    {{ order.items.length }}个产品
                  </span>
                  <span class="flex items-center gap-1">
                    <Calendar class="w-3 h-3" />
                    {{ formatDate(order.plannedStartDate) }}
                  </span>
                </div>
              </div>
              
              <!-- 右侧操作 -->
              <div class="flex items-center gap-2">
                <Button size="sm" variant="outline" @click.stop="viewOrder(order)">
                  <Eye class="w-3 h-3 mr-1" />
                  查看
                </Button>
                
                <Button 
                  v-if="order.status === 'pending'" 
                  size="sm" 
                  @click.stop="startOrder(order)"
                  class="bg-green-600 hover:bg-green-700"
                >
                  <Play class="w-3 h-3 mr-1" />
                  开始
                </Button>
                
                <Button 
                  v-if="order.status === 'in_progress'" 
                  size="sm" 
                  variant="outline"
                  @click.stop="pauseOrder(order)"
                >
                  <Pause class="w-3 h-3 mr-1" />
                  暂停
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="ghost">
                      <MoreHorizontal class="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem @click="editOrder(order)">
                      <Edit class="w-3 h-3 mr-2" />
                      编辑
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="duplicateOrder(order)">
                      <Copy class="w-3 h-3 mr-2" />
                      复制
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem @click="deleteOrder(order)" class="text-red-600">
                      <Trash2 class="w-3 h-3 mr-2" />
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            
            <!-- 进度条 -->
            <div v-if="order.status === 'in_progress'" class="mt-3">
              <div class="flex items-center justify-between text-xs text-gray-600 mb-1">
                <span>生产进度</span>
                <span>{{ order.progress || 0 }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-1.5">
                <div 
                  class="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                  :style="{ width: `${order.progress || 0}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    
    <!-- 新建工单对话框 -->
    <ProductionOrderCreationDialog
      :open="showCreateDialog"
      @update:open="showCreateDialog = $event"
      @order-created="handleOrderCreated"
    />
    
    <!-- 工单详情对话框 -->
    <Dialog :open="showDetailDialog" @update:open="showDetailDialog = $event">
      <DialogContent class="max-w-4xl">
        <DialogHeader>
          <DialogTitle>工单详情</DialogTitle>
        </DialogHeader>
        
        <div v-if="selectedOrder" class="space-y-6">
          <!-- 工单基本信息 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-gray-700">工单号</label>
              <p class="text-sm text-gray-900">{{ selectedOrder.workOrderNumber }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700">客户名称</label>
              <p class="text-sm text-gray-900">{{ selectedOrder.customerName }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700">状态</label>
              <Badge :variant="getStatusVariant(selectedOrder.status)">
                {{ getStatusText(selectedOrder.status) }}
              </Badge>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700">优先级</label>
              <Badge :variant="getPriorityVariant(selectedOrder.priority)">
                {{ getPriorityText(selectedOrder.priority) }}
              </Badge>
            </div>
          </div>
          
          <!-- 工单项目列表 -->
          <div>
            <h4 class="font-medium mb-3">生产项目</h4>
            <div class="space-y-2">
              <div 
                v-for="item in selectedOrder.items" 
                :key="item.id"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div>
                  <p class="font-medium text-sm">{{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm</p>
                  <p class="text-xs text-gray-600">{{ item.specifications.glassType }}</p>
                </div>
                <div class="text-right">
                  <p class="font-medium text-sm">{{ item.quantity }}片</p>
                  <p class="text-xs text-gray-600">{{ item.currentStatus }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Plus,
  RefreshCw,
  FileText,
  Play,
  CheckCircle,
  AlertTriangle,
  Search,
  X,
  Users,
  Package,
  Calendar,
  Eye,
  Pause,
  MoreHorizontal,
  Edit,
  Copy,
  Trash2,
} from 'lucide-vue-next'

import ProductionOrderCreationDialog from './ProductionOrderCreationDialog.vue'
import { mesService } from '@/services/mesService'
import type { ProductionOrder } from '@/types/mes-validation'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedOrder = ref<ProductionOrder | null>(null)

// 筛选和搜索
const searchQuery = ref('')
const statusFilter = ref('all')
const priorityFilter = ref('all')
const quickFilter = ref('all')

// 工单数据
const orders = ref<ProductionOrder[]>([])

// 统计数据
const stats = computed(() => {
  const total = orders.value.length
  const inProgress = orders.value.filter(o => o.status === 'in_progress').length
  const completed = orders.value.filter(o => o.status === 'completed').length
  const urgent = orders.value.filter(o => o.priority === 'urgent').length
  
  return { total, inProgress, completed, urgent }
})

// 筛选后的工单
const filteredOrders = computed(() => {
  let filtered = orders.value
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(order => 
      order.workOrderNumber.toLowerCase().includes(query) ||
      order.customerName.toLowerCase().includes(query)
    )
  }
  
  // 状态筛选
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(order => order.status === statusFilter.value)
  }
  
  // 优先级筛选
  if (priorityFilter.value !== 'all') {
    filtered = filtered.filter(order => order.priority === priorityFilter.value)
  }
  
  // 快速筛选
  if (quickFilter.value !== 'all') {
    switch (quickFilter.value) {
      case 'pending':
        filtered = filtered.filter(order => order.status === 'pending')
        break
      case 'in_progress':
        filtered = filtered.filter(order => order.status === 'in_progress')
        break
      case 'completed':
        filtered = filtered.filter(order => order.status === 'completed')
        break
      case 'urgent':
        filtered = filtered.filter(order => order.priority === 'urgent')
        break
    }
  }
  
  return filtered
})

// 方法
const loadOrders = async () => {
  loading.value = true
  try {
    orders.value = await mesService.getProductionOrders()
  } catch (error) {
    console.error('加载工单失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadOrders()
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = 'all'
  priorityFilter.value = 'all'
  quickFilter.value = 'all'
}

const selectOrder = (order: ProductionOrder) => {
  selectedOrder.value = order
  showDetailDialog.value = true
}

const viewOrder = (order: ProductionOrder) => {
  selectOrder(order)
}

const startOrder = async (order: ProductionOrder) => {
  try {
    await mesService.updateProductionOrderStatus(order.id, 'in_progress')
    await loadOrders()
  } catch (error) {
    console.error('启动工单失败:', error)
  }
}

const pauseOrder = async (order: ProductionOrder) => {
  try {
    await mesService.updateProductionOrderStatus(order.id, 'paused')
    await loadOrders()
  } catch (error) {
    console.error('暂停工单失败:', error)
  }
}

const editOrder = (order: ProductionOrder) => {
  // 编辑工单逻辑
  console.log('编辑工单:', order.workOrderNumber)
}

const duplicateOrder = (order: ProductionOrder) => {
  // 复制工单逻辑
  console.log('复制工单:', order.workOrderNumber)
}

const deleteOrder = async (order: ProductionOrder) => {
  if (confirm(`确定要删除工单 ${order.workOrderNumber} 吗？`)) {
    try {
      await mesService.deleteProductionOrder(order.id)
      await loadOrders()
    } catch (error) {
      console.error('删除工单失败:', error)
    }
  }
}

const handleOrderCreated = (orderIds: string[]) => {
  console.log('工单创建成功:', orderIds)
  loadOrders()
}

// 工具方法
const getStatusVariant = (status: string) => {
  switch (status) {
    case 'pending': return 'outline'
    case 'in_progress': return 'default'
    case 'completed': return 'secondary'
    case 'cancelled': return 'destructive'
    default: return 'outline'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待开始'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

const getPriorityVariant = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'destructive'
    case 'high': return 'default'
    case 'normal': return 'secondary'
    case 'low': return 'outline'
    default: return 'secondary'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'urgent': return '紧急'
    case 'high': return '高'
    case 'normal': return '普通'
    case 'low': return '低'
    default: return '普通'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 生命周期
onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.production-order-management {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem;
}

.page-header {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.stats-grid .card {
  transition: all 0.2s ease;
}

.stats-grid .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .production-order-management {
    padding: 1rem;
  }
  
  .page-header .flex {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>