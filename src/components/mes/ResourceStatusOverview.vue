<template>
  <div class="px-6 py-4">
    <h3 class="font-medium text-gray-900 mb-4 flex items-center">
      <Activity class="h-4 w-4 mr-2" />
      资源状态总览
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 库存状态 -->
      <div class="bg-white border border-gray-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-gray-800 flex items-center">
            <Package class="h-4 w-4 mr-1" />
            库存状态
          </h4>
          <Badge :variant="getInventoryStatusVariant()" size="sm">
            {{ getInventoryStatusText() }}
          </Badge>
        </div>
        
        <div v-if="isLoading" class="text-center py-2 text-gray-500 text-sm">
          加载中...
        </div>
        <div v-else class="space-y-2">
          <div class="flex justify-between items-center text-xs">
            <span class="text-gray-600">原片库存</span>
            <span class="font-medium" :class="getStockLevelClass(resourceData?.inventory?.rawGlass || 0)">
              {{ resourceData?.inventory?.rawGlass || 0 }}片
            </span>
          </div>
          <div class="flex justify-between items-center text-xs">
            <span class="text-gray-600">型材库存</span>
            <span class="font-medium" :class="getStockLevelClass(resourceData?.inventory?.profiles || 0)">
              {{ resourceData?.inventory?.profiles || 0 }}米
            </span>
          </div>
          <div class="flex justify-between items-center text-xs">
            <span class="text-gray-600">五金配件</span>
            <span class="font-medium" :class="getStockLevelClass(resourceData?.inventory?.hardware || 0)">
              {{ resourceData?.inventory?.hardware || 0 }}套
            </span>
          </div>
          <div class="flex justify-between items-center text-xs">
            <span class="text-gray-600">余料可用</span>
            <span class="font-medium text-green-600">
              {{ resourceData?.inventory?.wasteGlass || 0 }}片
            </span>
          </div>
        </div>
      </div>

      <!-- 设备状态 -->
      <div class="bg-white border border-gray-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-gray-800 flex items-center">
            <Settings class="h-4 w-4 mr-1" />
            设备负荷
          </h4>
          <Badge :variant="getEquipmentStatusVariant()" size="sm">
            {{ getEquipmentStatusText() }}
          </Badge>
        </div>
        
        <div v-if="isLoading" class="text-center py-2 text-gray-500 text-sm">
          加载中...
        </div>
        <div v-else class="space-y-3">
          <div v-for="equipment in resourceData?.equipment || []" :key="equipment.name" class="space-y-1">
            <div class="flex justify-between items-center text-xs">
              <span class="text-gray-600">{{ equipment.name }}</span>
              <span class="font-medium" :class="getLoadClass(equipment.loadRate)">
                {{ Math.round(equipment.loadRate * 100) }}%
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-1">
              <div 
                class="h-1 rounded-full transition-all duration-300"
                :class="getLoadBarClass(equipment.loadRate)"
                :style="{ width: `${Math.round(equipment.loadRate * 100)}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 人员配置 -->
      <div class="bg-white border border-gray-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-gray-800 flex items-center">
            <Users class="h-4 w-4 mr-1" />
            人员配置
          </h4>
          <Badge :variant="getStaffStatusVariant()" size="sm">
            {{ getStaffStatusText() }}
          </Badge>
        </div>
        
        <div v-if="isLoading" class="text-center py-2 text-gray-500 text-sm">
          加载中...
        </div>
        <div v-else class="space-y-2">
          <div class="flex justify-between items-center text-xs">
            <span class="text-gray-600">在岗人员</span>
            <span class="font-medium text-green-600">
              {{ resourceData?.staff?.onDuty || 0 }}/{{ resourceData?.staff?.total || 0 }}
            </span>
          </div>
          <div class="flex justify-between items-center text-xs">
            <span class="text-gray-600">技能匹配</span>
            <span class="font-medium text-blue-600">
              {{ Math.round((resourceData?.staff?.skillMatch || 0) * 100) }}%
            </span>
          </div>
          <div class="flex justify-between items-center text-xs">
            <span class="text-gray-600">班组配置</span>
            <span class="font-medium text-gray-700">
              {{ resourceData?.staff?.teams || 0 }}个班组
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 关键风险提示 -->
    <div v-if="hasRisks" class="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
      <div class="flex items-center gap-2 mb-2">
        <AlertTriangle class="h-4 w-4 text-orange-600" />
        <span class="text-sm font-medium text-orange-900">关键风险提示</span>
      </div>
      <div class="space-y-1">
        <div v-for="risk in risks" :key="risk.type" class="text-xs text-orange-700">
          • {{ risk.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Activity, Package, Settings, Users, AlertTriangle } from 'lucide-vue-next';
import { productionReleaseService } from '@/services/productionReleaseService';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();

// 资源数据状态
const resourceData = ref<any>(null);
const isLoading = ref(false);

// 加载资源状态数据
const loadResourceData = async () => {
  if (!props.workOrder?.id) return;

  isLoading.value = true;
  try {
    // 模拟资源状态数据
    resourceData.value = {
      inventory: {
        rawGlass: 1250,
        profiles: 850,
        hardware: 320,
        wasteGlass: 45
      },
      equipment: [
        { name: '切割台', loadRate: 0.75, status: 'normal' },
        { name: '钢化炉', loadRate: 0.85, status: 'high' },
        { name: '磨边机', loadRate: 0.45, status: 'normal' },
        { name: '中空线', loadRate: 0.65, status: 'normal' }
      ],
      staff: {
        onDuty: 28,
        total: 32,
        skillMatch: 0.92,
        teams: 4
      }
    };
  } catch (error) {
    console.error('加载资源状态数据失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 计算风险提示
const risks = computed(() => {
  if (!resourceData.value) return [];
  
  const riskList = [];
  
  // 库存风险
  if (resourceData.value.inventory.rawGlass < 500) {
    riskList.push({ type: 'inventory', message: '原片库存不足，建议及时补货' });
  }
  
  // 设备负荷风险
  const overloadedEquipment = resourceData.value.equipment.filter(eq => eq.loadRate > 0.8);
  if (overloadedEquipment.length > 0) {
    riskList.push({ 
      type: 'equipment', 
      message: `${overloadedEquipment.map(eq => eq.name).join('、')}负荷过高，可能影响交期` 
    });
  }
  
  // 人员配置风险
  if (resourceData.value.staff.onDuty / resourceData.value.staff.total < 0.8) {
    riskList.push({ type: 'staff', message: '在岗人员不足，建议调配人力资源' });
  }
  
  return riskList;
});

const hasRisks = computed(() => risks.value.length > 0);

// 状态判断方法
const getInventoryStatusVariant = () => {
  if (!resourceData.value) return 'secondary';
  const rawGlass = resourceData.value.inventory.rawGlass;
  if (rawGlass < 500) return 'destructive';
  if (rawGlass < 1000) return 'outline';
  return 'default';
};

const getInventoryStatusText = () => {
  if (!resourceData.value) return '检查中';
  const rawGlass = resourceData.value.inventory.rawGlass;
  if (rawGlass < 500) return '库存不足';
  if (rawGlass < 1000) return '库存紧张';
  return '库存充足';
};

const getEquipmentStatusVariant = () => {
  if (!resourceData.value) return 'secondary';
  const overloaded = resourceData.value.equipment.some(eq => eq.loadRate > 0.8);
  if (overloaded) return 'outline';
  return 'default';
};

const getEquipmentStatusText = () => {
  if (!resourceData.value) return '检查中';
  const overloaded = resourceData.value.equipment.some(eq => eq.loadRate > 0.8);
  if (overloaded) return '负荷较高';
  return '运行正常';
};

const getStaffStatusVariant = () => {
  if (!resourceData.value) return 'secondary';
  const ratio = resourceData.value.staff.onDuty / resourceData.value.staff.total;
  if (ratio < 0.8) return 'outline';
  return 'default';
};

const getStaffStatusText = () => {
  if (!resourceData.value) return '检查中';
  const ratio = resourceData.value.staff.onDuty / resourceData.value.staff.total;
  if (ratio < 0.8) return '人员不足';
  return '配置正常';
};

// 样式辅助方法
const getStockLevelClass = (level: number) => {
  if (level < 500) return 'text-red-600';
  if (level < 1000) return 'text-orange-600';
  return 'text-green-600';
};

const getLoadClass = (rate: number) => {
  if (rate > 0.8) return 'text-red-600';
  if (rate > 0.6) return 'text-orange-600';
  return 'text-green-600';
};

const getLoadBarClass = (rate: number) => {
  if (rate > 0.8) return 'bg-red-500';
  if (rate > 0.6) return 'bg-orange-500';
  return 'bg-green-500';
};

// 监听工单变化
watch(() => props.workOrder, () => {
  if (props.workOrder) {
    loadResourceData();
  }
}, { immediate: true });

onMounted(() => {
  if (props.workOrder) {
    loadResourceData();
  }
});
</script>
