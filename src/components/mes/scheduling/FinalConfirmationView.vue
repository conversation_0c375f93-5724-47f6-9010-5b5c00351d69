<template>
  <div class="h-full p-6 bg-gray-50 overflow-y-auto">
    <div v-if="!finalSchedule || !comparisonData" class="flex items-center justify-center h-full">
      <div class="text-center space-y-4">
        <Loader2 class="h-12 w-12 text-gray-400 mx-auto animate-spin" />
        <p class="text-lg font-medium text-gray-700">正在生成最终排产方案...</p>
        <p class="text-sm text-gray-500">请稍候，系统正在重构计划并计算优化指标。</p>
      </div>
    </div>

    <div v-else class="space-y-6">
      <!-- 1. 关键指标对比区 -->
      <section>
        <h2 class="text-xl font-bold text-gray-800 mb-4">关键指标对比 (优化前 vs. 优化后)</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ComparisonCard
            title="预计总工时 (小时)"
            :before="comparisonData.duration.before"
            :after="comparisonData.duration.after"
            unit="小时"
            :invert-colors="true"
          />
          <ComparisonCard
            title="预估材料成本 (元)"
            :before="comparisonData.materialCost.before"
            :after="comparisonData.materialCost.after"
            unit="元"
            :invert-colors="true"
            :is-currency="true"
          />
          <ComparisonCard
            title="平均利用率 (%)"
            :before="comparisonData.utilizationRate.before"
            :after="comparisonData.utilizationRate.after"
            unit="%"
          />
          <!-- <ComparisonCard
            title="切割任务数"
            :before="comparisonData.taskCount.cutting.before"
            :after="comparisonData.taskCount.cutting.after"
            unit="个"
            :invert-colors="true"
          /> -->
        </div>
      </section>

      <!-- 2. 最终排产方案总览 (占位符) -->
      <section>
        <h2 class="text-xl font-bold text-gray-800 mb-4">最终排产方案总览</h2>
        <div class="bg-white p-6 rounded-lg border h-96 flex items-center justify-center">
          <div class="text-center">
            <BarChart3 class="h-12 w-12 text-gray-300 mx-auto" />
            <p class="mt-4 text-gray-500">最终排产甘特图 (开发中)</p>
            <p class="text-sm text-gray-400">这里将显示包含“扇出”依赖的最终甘特图。</p>
          </div>
        </div>
      </section>

      <!-- 3. 待生成任务清单 -->
      <section>
        <h2 class="text-xl font-bold text-gray-800 mb-4">待生成任务清单</h2>
        <div class="bg-white rounded-lg border overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[250px]">任务名称</TableHead>
                <TableHead>所属批次</TableHead>
                <TableHead>执行设备</TableHead>
                <TableHead>预计开始时间</TableHead>
                <TableHead>预计结束时间</TableHead>
              </TableRow>
            </TableHeader>
            <template v-for="(group, productName) in groupedTasks" :key="productName">
              <TableBody>
                <TableRow class="bg-gray-50 hover:bg-gray-100">
                  <TableCell colspan="5" class="font-semibold text-gray-700">
                    <Package class="h-4 w-4 inline-block mr-2 text-gray-500" />
                    最终产品: {{ productName }}
                  </TableCell>
                </TableRow>
                <TableRow v-for="task in group" :key="task.id">
                  <TableCell class="font-medium">{{ task.name }}</TableCell>
                  <TableCell>{{ task.batchName }}</TableCell>
                  <TableCell>{{ task.equipment }}</TableCell>
                  <TableCell>{{ formatDateTime(task.startTime) }}</TableCell>
                  <TableCell>{{ formatDateTime(task.endTime) }}</TableCell>
                </TableRow>
              </TableBody>
            </template>
          </Table>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue';
import { useSchedulingStore } from '@/stores/schedulingStore';
import { Loader2, BarChart3, Package } from 'lucide-vue-next';
import ComparisonCard from './ComparisonCard.vue';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const schedulingStore = useSchedulingStore();
const { finalSchedule, comparisonData } = schedulingStore;

const groupedTasks = computed(() => {
  if (!finalSchedule?.tasks) return {};
  return finalSchedule.tasks.reduce((acc: Record<string, any[]>, task: any) => {
    const key = task.productName || '未分配产品';
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(task);
    return acc;
  }, {} as Record<string, any[]>);
});

const formatDateTime = (dateTimeString: string) => {
  return new Date(dateTimeString).toLocaleString('zh-CN', { 
    month: '2-digit', 
    day: '2-digit', 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false
  });
};

onMounted(() => {
  if (!finalSchedule) {
    schedulingStore.generateFinalSchedule();
  }
});
</script>
