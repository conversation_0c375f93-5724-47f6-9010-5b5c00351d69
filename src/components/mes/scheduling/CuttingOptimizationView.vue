<template>
  <div class="h-full bg-gray-50">
    <!-- 阶段状态指示器 -->
    <div class="bg-white border-b px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">切割优化阶段</h2>
          <p class="text-sm text-gray-600 mt-1">第三方系统优化 - 数据导出与结果导入</p>
        </div>
        <div class="flex items-center space-x-3">
          <Badge :variant="getStatusVariant(currentStatus)" class="px-3 py-1">
            <component :is="getStatusIcon(currentStatus)" class="w-4 h-4 mr-1" />
            {{ getStatusText(currentStatus) }}
          </Badge>
          <div class="text-sm text-gray-500">
            {{ getProgressText() }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 p-6">
      <div class="max-w-6xl mx-auto">
        <!-- 流程步骤指示器 -->
        <div class="mb-8">
          <div class="flex items-center justify-between">
            <template v-for="(step, index) in optimizationSteps" :key="step.id">
              <!-- 步骤圆圈 -->
              <div class="flex flex-col items-center">
                <div
                  class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300"
                  :class="getStepCircleClass(step.id)"
                >
                  <component
                    :is="step.icon"
                    class="w-5 h-5"
                    :class="getStepIconClass(step.id)"
                  />
                </div>
                <div class="mt-2 text-center">
                  <div class="text-sm font-medium" :class="getStepTextClass(step.id)">
                    {{ step.title }}
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    {{ step.description }}
                  </div>
                </div>
              </div>

              <!-- 连接线 -->
              <div
                v-if="index < optimizationSteps.length - 1"
                class="flex-1 h-0.5 mx-4 transition-all duration-300"
                :class="getStepConnectionClass(index)"
              />
            </template>
          </div>
        </div>

        <!-- 动态内容区域 -->
        <div class="bg-white rounded-lg shadow-sm border">
          <!-- 数据导出阶段 -->
          <div v-if="currentStep === 'export'" class="p-6">
            <CuttingDataExportPanel
              :export-data="exportData"
              :export-status="exportStatus"
              @export-data="handleExportData"
              @download-file="handleDownloadFile"
            />
          </div>

          <!-- 等待处理阶段 -->
          <div v-else-if="currentStep === 'waiting'" class="p-6">
            <CuttingWaitingPanel
              :export-data="exportData"
              :estimated-time="estimatedProcessingTime"
              @check-status="handleCheckStatus"
              @cancel-optimization="handleCancelOptimization"
            />
          </div>

          <!-- 结果导入阶段 -->
          <div v-else-if="currentStep === 'import'" class="p-6">
            <CuttingResultImportPanel
              :import-status="importStatus"
              :validation-result="validationResult"
              @import-result="handleImportResult"
              @retry-import="handleRetryImport"
            />
          </div>

          <!-- 结果验证阶段 -->
          <div v-else-if="currentStep === 'validation'" class="p-6">
            <CuttingValidationPanel
              :cutting-result="cuttingResult"
              :validation-result="validationResult"
              :comparison-data="comparisonData"
              @approve-result="handleApproveResult"
              @reject-result="handleRejectResult"
              @request-modification="handleRequestModification"
            />
          </div>

          <!-- 完成阶段 -->
          <div v-else-if="currentStep === 'completed'" class="p-6">
            <CuttingCompletedPanel
              :cutting-result="cuttingResult"
              :improvements="improvements"
              @proceed-to-final="handleProceedToFinal"
              @export-report="handleExportReport"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  Upload, Download, Clock, CheckCircle, AlertTriangle,
  FileText, Scissors, RefreshCw, ArrowRight
} from 'lucide-vue-next';
import { Badge } from '@/components/ui/badge';
import CuttingDataExportPanel from './CuttingDataExportPanel.vue';
import CuttingWaitingPanel from './CuttingWaitingPanel.vue';
import CuttingResultImportPanel from './CuttingResultImportPanel.vue';
import CuttingValidationPanel from './CuttingValidationPanel.vue';
import CuttingCompletedPanel from './CuttingCompletedPanel.vue';
import type {
  CuttingExportData,
  CuttingImportResult,
  ValidationResult,
  ComparisonData
} from '@/types/scheduling';

interface Props {
  exportStatus: 'idle' | 'exporting' | 'exported' | 'error';
  importStatus: 'idle' | 'importing' | 'validating' | 'imported' | 'error';
  exportData?: CuttingExportData;
  cuttingResult?: CuttingImportResult;
  validationResult?: ValidationResult;
  comparisonData?: ComparisonData;
}

interface Emits {
  (e: 'export-data'): void;
  (e: 'import-result', file: File): void;
  (e: 'approve-result'): void;
  (e: 'reject-result', reason: string): void;
  (e: 'proceed-to-final'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 优化步骤定义
const optimizationSteps = [
  {
    id: 'export',
    title: '数据导出',
    description: '导出切割数据',
    icon: Download
  },
  {
    id: 'waiting',
    title: '等待处理',
    description: '第三方系统处理',
    icon: Clock
  },
  {
    id: 'import',
    title: '结果导入',
    description: '导入优化结果',
    icon: Upload
  },
  {
    id: 'validation',
    title: '结果验证',
    description: '验证和审核',
    icon: CheckCircle
  },
  {
    id: 'completed',
    title: '优化完成',
    description: '准备确认',
    icon: ArrowRight
  }
];

// 当前步骤计算
const currentStep = computed(() => {
  if (props.cuttingResult && props.validationResult?.valid) {
    return 'completed';
  } else if (props.cuttingResult) {
    return 'validation';
  } else if (props.exportData && props.importStatus === 'idle') {
    return 'waiting';
  } else if (props.exportData) {
    return 'import';
  } else {
    return 'export';
  }
});

// 当前状态计算
const currentStatus = computed(() => {
  if (props.exportStatus === 'error' || props.importStatus === 'error') {
    return 'error';
  } else if (props.importStatus === 'importing' || props.importStatus === 'validating') {
    return 'processing';
  } else if (props.exportStatus === 'exporting') {
    return 'exporting';
  } else if (currentStep.value === 'completed') {
    return 'completed';
  } else if (currentStep.value === 'waiting') {
    return 'waiting';
  } else {
    return 'ready';
  }
});

// 预估处理时间
const estimatedProcessingTime = ref(30); // 分钟

// 改进指标
const improvements = computed(() => {
  if (!props.cuttingResult) return null;
  return props.cuttingResult.improvements;
});

// 样式计算函数
const getStatusVariant = (status: string) => {
  const variants = {
    ready: 'secondary',
    exporting: 'default',
    waiting: 'outline',
    processing: 'default',
    completed: 'default',
    error: 'destructive'
  };
  return variants[status as keyof typeof variants] || 'secondary';
};

const getStatusIcon = (status: string) => {
  const icons = {
    ready: FileText,
    exporting: Download,
    waiting: Clock,
    processing: RefreshCw,
    completed: CheckCircle,
    error: AlertTriangle
  };
  return icons[status as keyof typeof icons] || FileText;
};

const getStatusText = (status: string) => {
  const texts = {
    ready: '准备就绪',
    exporting: '导出中',
    waiting: '等待处理',
    processing: '处理中',
    completed: '已完成',
    error: '出现错误'
  };
  return texts[status as keyof typeof texts] || '未知状态';
};

const getProgressText = () => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === currentStep.value);
  const total = optimizationSteps.length;
  return `${currentIndex + 1}/${total}`;
};

const getStepCircleClass = (stepId: string) => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === currentStep.value);
  const stepIndex = optimizationSteps.findIndex(step => step.id === stepId);

  if (stepIndex < currentIndex) {
    return 'border-green-500 bg-green-500 text-white';
  } else if (stepIndex === currentIndex) {
    return 'border-blue-500 bg-blue-500 text-white shadow-lg';
  } else {
    return 'border-gray-300 bg-white text-gray-400';
  }
};

const getStepIconClass = (stepId: string) => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === currentStep.value);
  const stepIndex = optimizationSteps.findIndex(step => step.id === stepId);

  if (stepIndex <= currentIndex) {
    return 'text-white';
  } else {
    return 'text-gray-400';
  }
};

const getStepTextClass = (stepId: string) => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === currentStep.value);
  const stepIndex = optimizationSteps.findIndex(step => step.id === stepId);

  if (stepIndex === currentIndex) {
    return 'text-blue-600';
  } else if (stepIndex < currentIndex) {
    return 'text-green-600';
  } else {
    return 'text-gray-500';
  }
};

const getStepConnectionClass = (index: number) => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === currentStep.value);

  if (index < currentIndex) {
    return 'bg-green-500';
  } else if (index === currentIndex - 1) {
    return 'bg-gradient-to-r from-green-500 to-gray-300';
  } else {
    return 'bg-gray-300';
  }
};

// 事件处理函数
const handleExportData = () => {
  emit('export-data');
};

const handleDownloadFile = () => {
  // 下载导出文件的逻辑
  console.log('下载导出文件');
};

const handleCheckStatus = () => {
  // 检查第三方系统处理状态
  console.log('检查处理状态');
};

const handleCancelOptimization = () => {
  // 取消优化任务
  console.log('取消优化任务');
};

const handleImportResult = (file: File) => {
  emit('import-result', file);
};

const handleRetryImport = () => {
  // 重试导入
  console.log('重试导入');
};

const handleApproveResult = () => {
  emit('approve-result');
};

const handleRejectResult = (reason: string) => {
  emit('reject-result', reason);
};

const handleRequestModification = () => {
  // 请求修改优化结果
  console.log('请求修改');
};

const handleProceedToFinal = () => {
  emit('proceed-to-final');
};

const handleExportReport = () => {
  // 导出优化报告
  console.log('导出优化报告');
};
</script>
