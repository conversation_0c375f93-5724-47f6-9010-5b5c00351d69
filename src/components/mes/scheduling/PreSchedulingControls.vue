<template>
  <div class="bg-white rounded-lg border overflow-hidden">
    <!-- 标题 -->
    <div class="p-4 border-b bg-gray-50">
      <h3 class="font-semibold text-gray-900">预排产控制</h3>
      <p class="text-sm text-gray-500">配置排产参数并开始计算</p>
    </div>
    
    <div class="p-4 space-y-4">
      <!-- 选中批次概览 -->
      <div class="space-y-2">
        <div class="text-sm font-medium text-gray-700">已选择批次</div>
        <div class="bg-blue-50 rounded-lg p-3">
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div class="text-blue-700">
              <span class="font-medium">{{ selectedBatches.length }}</span> 个批次
            </div>
            <div class="text-blue-700">
              <span class="font-medium">{{ totalQuantity }}</span> 片
            </div>
            <div class="text-blue-700">
              <span class="font-medium">{{ totalHours.toFixed(1) }}</span> 小时
            </div>
            <div class="text-blue-700">
              <span class="font-medium">{{ customerCount }}</span> 家客户
            </div>
          </div>
        </div>
      </div>
      
      <!-- 排产策略 -->
      <div class="space-y-2">
        <Label class="text-sm font-medium text-gray-700">排产策略</Label>
        <Select v-model="schedulingStrategy">
          <SelectTrigger>
            <SelectValue placeholder="选择排产策略" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="priority">优先级优先</SelectItem>
            <SelectItem value="delivery">交期优先</SelectItem>
            <SelectItem value="efficiency">效率优先</SelectItem>
            <SelectItem value="balanced">平衡策略</SelectItem>
          </SelectContent>
        </Select>
        <div class="text-xs text-gray-500">
          {{ getStrategyDescription() }}
        </div>
      </div>
      
      <!-- 约束条件 -->
      <div class="space-y-3">
        <Label class="text-sm font-medium text-gray-700">约束条件</Label>
        
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <Label class="text-sm text-gray-600">工作时间</Label>
            <div class="flex items-center space-x-2">
              <Input
                v-model.number="constraints.startHour"
                type="number"
                min="6"
                max="12"
                class="w-16 h-8 text-sm"
              />
              <span class="text-sm text-gray-500">-</span>
              <Input
                v-model.number="constraints.endHour"
                type="number"
                min="16"
                max="22"
                class="w-16 h-8 text-sm"
              />
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <Label class="text-sm text-gray-600">缓冲时间</Label>
            <div class="flex items-center space-x-2">
              <Input
                v-model.number="constraints.bufferTime"
                type="number"
                min="0"
                max="120"
                class="w-20 h-8 text-sm"
              />
              <span class="text-sm text-gray-500">分钟</span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <Label class="text-sm text-gray-600">允许加班</Label>
            <!-- <Switch v-model="constraints.allowOvertime" /> -->
            <input type="checkbox" v-model="constraints.allowOvertime" class="rounded" />
          </div>
          
          <div class="flex items-center justify-between">
            <Label class="text-sm text-gray-600">周末生产</Label>
            <!-- <Switch v-model="constraints.allowWeekend" /> -->
            <input type="checkbox" v-model="constraints.allowWeekend" class="rounded" />
          </div>
        </div>
      </div>
      
      <!-- 优化目标权重 -->
      <div class="space-y-3">
        <Label class="text-sm font-medium text-gray-700">优化目标权重</Label>
        
        <div class="space-y-3">
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm text-gray-600">交期达成</span>
              <span class="text-sm font-medium">{{ objectives.delivery }}%</span>
            </div>
            <!-- <Slider
              v-model="objectives.delivery"
              :max="100"
              :step="5"
              class="w-full"
            /> -->
            <input
              type="range"
              v-model="objectives.delivery[0]"
              min="0"
              max="100"
              step="5"
              class="w-full"
            />
          </div>
          
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm text-gray-600">成本优化</span>
              <span class="text-sm font-medium">{{ objectives.cost }}%</span>
            </div>
            <!-- <Slider
              v-model="objectives.cost"
              :max="100"
              :step="5"
              class="w-full"
            /> -->
            <input
              type="range"
              v-model="objectives.cost[0]"
              min="0"
              max="100"
              step="5"
              class="w-full"
            />
          </div>
          
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm text-gray-600">设备效率</span>
              <span class="text-sm font-medium">{{ objectives.efficiency }}%</span>
            </div>
            <!-- <Slider
              v-model="objectives.efficiency"
              :max="100"
              :step="5"
              class="w-full"
            /> -->
            <input
              type="range"
              v-model="objectives.efficiency[0]"
              min="0"
              max="100"
              step="5"
              class="w-full"
            />
          </div>
        </div>
        
        <div class="text-xs text-gray-500">
          权重总和: {{ totalWeight }}% (建议保持在100%左右)
        </div>
      </div>
      
      <!-- 预估信息 -->
      <div v-if="estimatedInfo" class="space-y-2">
        <Label class="text-sm font-medium text-gray-700">预估信息</Label>
        <div class="bg-gray-50 rounded-lg p-3 space-y-1">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">预计工期:</span>
            <span class="font-medium">{{ estimatedInfo.duration }} 天</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">设备利用率:</span>
            <span class="font-medium">{{ estimatedInfo.utilization }}%</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">预计成本:</span>
            <span class="font-medium">¥{{ estimatedInfo.cost.toLocaleString() }}</span>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div v-if="showStartButton" class="space-y-2 pt-2">
        <Button
          @click="handleStartScheduling"
          :disabled="selectedBatches.length === 0 || isCalculating"
          class="w-full"
        >
          <div v-if="isCalculating" class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>计算中...</span>
          </div>
          <div v-else class="flex items-center space-x-2">
            <Play class="h-4 w-4" />
            <span>开始预排产</span>
          </div>
        </Button>

        <Button
          variant="outline"
          @click="handleQuickEstimate"
          :disabled="selectedBatches.length === 0"
          class="w-full"
        >
          <Calculator class="h-4 w-4 mr-2" />
          快速预估
        </Button>

        <Button
          variant="ghost"
          @click="handleReset"
          class="w-full"
        >
          <RotateCcw class="h-4 w-4 mr-2" />
          重置配置
        </Button>
      </div>

      <!-- 简化操作按钮（当在向导中时） -->
      <div v-else class="space-y-2 pt-2">
        <Button
          variant="outline"
          @click="handleQuickEstimate"
          :disabled="selectedBatches.length === 0"
          class="w-full"
        >
          <Calculator class="h-4 w-4 mr-2" />
          快速预估
        </Button>

        <Button
          variant="ghost"
          @click="handleReset"
          class="w-full"
        >
          <RotateCcw class="h-4 w-4 mr-2" />
          重置配置
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Play, Calculator, RotateCcw } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
// import { Switch } from '@/components/ui/switch';
// import { Slider } from '@/components/ui/slider';
import type { OptimizedBatch } from '@/types/production-order-creation';

interface Props {
  selectedBatches: OptimizedBatch[];
  showStartButton?: boolean;
}

interface Emits {
  (e: 'start-scheduling'): void;
}

const props = withDefaults(defineProps<Props>(), {
  showStartButton: true
});
const emit = defineEmits<Emits>();

// 响应式状态
const isCalculating = ref(false);
const schedulingStrategy = ref('priority');

const constraints = ref({
  startHour: 8,
  endHour: 18,
  bufferTime: 30,
  allowOvertime: false,
  allowWeekend: false
});

const objectives = ref({
  delivery: [80],
  cost: [60],
  efficiency: [70]
});

const estimatedInfo = ref<{
  duration: number;
  utilization: number;
  cost: number;
} | null>(null);

// 计算属性
const totalQuantity = computed(() => {
  return props.selectedBatches.reduce((sum, batch) => sum + batch.totalQuantity, 0);
});

const totalHours = computed(() => {
  return props.selectedBatches.reduce((sum, batch) => sum + (batch.estimatedDuration / 60), 0);
});

const customerCount = computed(() => {
  const customers = new Set(
    props.selectedBatches.flatMap(batch => 
      batch.items.map(item => item.customerName)
    )
  );
  return customers.size;
});

const totalWeight = computed(() => {
  return objectives.value.delivery[0] + objectives.value.cost[0] + objectives.value.efficiency[0];
});

// 方法
const getStrategyDescription = () => {
  const descriptions = {
    priority: '按批次优先级排序，紧急订单优先安排',
    delivery: '按交期要求排序，确保按时交付',
    efficiency: '优化设备利用率，提高生产效率',
    balanced: '平衡各项指标，综合最优方案'
  };
  
  return descriptions[schedulingStrategy.value as keyof typeof descriptions] || '';
};

const handleStartScheduling = () => {
  isCalculating.value = true;
  emit('start-scheduling');
  
  // 模拟计算完成后重置状态
  setTimeout(() => {
    isCalculating.value = false;
  }, 15000);
};

const handleQuickEstimate = () => {
  // 快速预估逻辑
  const avgDurationPerBatch = totalHours.value / props.selectedBatches.length;
  const estimatedDuration = Math.ceil(totalHours.value / 8); // 按8小时工作日计算
  const estimatedUtilization = 75 + Math.random() * 15;
  const estimatedCost = totalQuantity.value * 45; // 简化成本计算
  
  estimatedInfo.value = {
    duration: estimatedDuration,
    utilization: Math.round(estimatedUtilization),
    cost: estimatedCost
  };
};

const handleReset = () => {
  schedulingStrategy.value = 'priority';
  constraints.value = {
    startHour: 8,
    endHour: 18,
    bufferTime: 30,
    allowOvertime: false,
    allowWeekend: false
  };
  objectives.value = {
    delivery: [80],
    cost: [60],
    efficiency: [70]
  };
  estimatedInfo.value = null;
};

// 监听选中批次变化，自动更新预估信息
watch(() => props.selectedBatches.length, () => {
  if (props.selectedBatches.length > 0 && estimatedInfo.value) {
    handleQuickEstimate();
  }
});
</script>
