<template>
  <div class="space-y-4">
    <!-- 当前阶段信息 -->
    <div class="space-y-2">
      <div class="text-sm font-medium text-gray-700">当前阶段</div>
      <div class="bg-blue-50 rounded-lg p-3">
        <div class="text-sm font-medium text-blue-900">{{ getPhaseTitle() }}</div>
        <div class="text-xs text-blue-700 mt-1">{{ getPhaseDescription() }}</div>
      </div>
    </div>
    
    <!-- 选中批次信息 -->
    <div v-if="selectedBatches.length > 0" class="space-y-2">
      <div class="text-sm font-medium text-gray-700">选中批次</div>
      <div class="space-y-2">
        <div
          v-for="batch in selectedBatches.slice(0, 3)"
          :key="batch.id"
          class="bg-gray-50 rounded p-2"
        >
          <div class="text-sm font-medium text-gray-900">{{ batch.name }}</div>
          <div class="text-xs text-gray-500">{{ batch.totalQuantity }} 片</div>
        </div>
        <div v-if="selectedBatches.length > 3" class="text-xs text-gray-500 text-center">
          还有 {{ selectedBatches.length - 3 }} 个批次...
        </div>
      </div>
    </div>
    
    <!-- 关键指标 -->
    <div class="space-y-2">
      <div class="text-sm font-medium text-gray-700">关键指标</div>
      <div class="space-y-2">
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">总工期</span>
          <span class="font-medium">{{ metrics.totalDuration.toFixed(1) }} 天</span>
        </div>
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">设备利用率</span>
          <span class="font-medium">{{ metrics.equipmentUtilization.toFixed(0) }}%</span>
        </div>
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">交期达成率</span>
          <span class="font-medium">{{ metrics.deliveryAchievement.toFixed(0) }}%</span>
        </div>
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">原片利用率</span>
          <span class="font-medium">{{ metrics.materialUtilization.toFixed(0) }}%</span>
        </div>
      </div>

      <!-- 数据完整性面板 - 在非预排产阶段显示 -->
      <!-- <div v-if="currentPhase !== 'pre-scheduling'" class="mt-4">
        <DataIntegrityPanel />
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SchedulingPhase, SchedulingMetrics } from '@/types/scheduling';
import type { OptimizedBatch } from '@/types/production-order-creation';
import DataIntegrityPanel from './DataIntegrityPanel.vue';

interface Props {
  currentPhase: SchedulingPhase;
  selectedBatches: OptimizedBatch[];
  metrics: SchedulingMetrics;
}

const props = defineProps<Props>();

const getPhaseTitle = () => {
  switch (props.currentPhase) {
    case 'pre-scheduling':
      return '预排产阶段';
    case 'cutting-optimization':
      return '切割优化阶段';
    case 'final-confirmation':
      return '最终确认阶段';
    default:
      return '排产规划';
  }
};

const getPhaseDescription = () => {
  switch (props.currentPhase) {
    case 'pre-scheduling':
      return '基于标准工时和设备产能进行初步排产';
    case 'cutting-optimization':
      return '等待第三方切割优化系统完成数据处理';
    case 'final-confirmation':
      return '基于切割优化结果确认最终排产方案';
    default:
      return '选择批次开始排产规划';
  }
};
</script>
