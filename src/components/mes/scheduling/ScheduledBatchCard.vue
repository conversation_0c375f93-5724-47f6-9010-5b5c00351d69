<template>
  <div class="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow">
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center space-x-3">
        <div class="font-medium text-gray-900">{{ batch.name }}</div>
        <Badge :variant="getPriorityVariant()" class="text-xs">
          {{ getPriorityLabel() }}
        </Badge>
      </div>
      
      <Button variant="ghost" size="sm" @click="$emit('view-details', batch.id)">
        <MoreHorizontal class="h-4 w-4" />
      </Button>
    </div>
    
    <div class="grid grid-cols-2 gap-4 mb-3">
      <div class="space-y-2">
        <div class="text-sm">
          <span class="text-gray-500">开始时间:</span>
          <span class="ml-2 font-medium">{{ formatDateTime(batch.scheduledStartTime) }}</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-500">结束时间:</span>
          <span class="ml-2 font-medium">{{ formatDateTime(batch.scheduledEndTime) }}</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-500">预计成本:</span>
          <span class="ml-2 font-medium">¥{{ batch.estimatedCost.toLocaleString() }}</span>
        </div>
      </div>
      
      <div class="space-y-2">
        <div class="text-sm">
          <span class="text-gray-500">数量:</span>
          <span class="ml-2 font-medium">{{ batch.totalQuantity }} 片</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-500">工期:</span>
          <span class="ml-2 font-medium">{{ formatDuration(getEstimatedDuration()) }}</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-500">利用率:</span>
          <span class="ml-2 font-medium">{{ batch.utilization.toFixed(1) }}%</span>
        </div>
      </div>
    </div>
    
    <div class="space-y-2">
      <div class="text-sm text-gray-600">分配资源:</div>
      <div class="flex flex-wrap gap-2">
        <span
          v-for="resource in batch.assignedResources"
          :key="resource.resourceId"
          class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
        >
          {{ resource.resourceName }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { MoreHorizontal } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { ScheduledBatch } from '@/types/scheduling';

interface Props {
  batch: ScheduledBatch;
}

interface Emits {
  (e: 'view-details', batchId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const getPriorityVariant = () => {
  switch (props.batch.priority) {
    case 'urgent': return 'destructive';
    case 'high': return 'default';
    case 'normal': return 'secondary';
    case 'low': return 'outline';
    default: return 'secondary';
  }
};

const getPriorityLabel = () => {
  switch (props.batch.priority) {
    case 'urgent': return '紧急';
    case 'high': return '高';
    case 'normal': return '普通';
    case 'low': return '低';
    default: return '普通';
  }
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getEstimatedDuration = () => {
  // 从 estimatedTime 或其他属性获取预估时间
  if ((props.batch as any).estimatedDuration) {
    return (props.batch as any).estimatedDuration;
  }
  if (props.batch.estimatedTime) {
    return props.batch.estimatedTime;
  }
  // 默认估算时间
  return props.batch.items.length * 60; // 每个项目1小时
};

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);

  if (hours > 0) {
    return `${hours}h${mins > 0 ? ` ${mins}m` : ''}`;
  } else {
    return `${mins}m`;
  }
};
</script>
