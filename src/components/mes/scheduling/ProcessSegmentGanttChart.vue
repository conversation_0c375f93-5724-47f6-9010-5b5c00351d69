<template>
  <div class="h-full flex flex-col bg-white border rounded-lg overflow-hidden">
    <!-- 甘特图头部 -->
    <div class="p-4 border-b bg-gray-50">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="font-medium text-gray-900">工艺段排产甘特图</h4>
          <p class="text-sm text-gray-500">
            <span v-if="ganttData && ganttData.timeRange">
              时间范围: {{ formatDate(ganttData.timeRange.start) }} - {{ formatDate(ganttData.timeRange.end) }}
            </span>
            <span v-else>暂无数据</span>
          </p>
        </div>
        
        <div class="flex items-center space-x-2">
          <!-- 缩放控制 -->
          <Button variant="outline" size="sm" @click="zoomIn">
            <ZoomIn class="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" @click="zoomOut">
            <ZoomOut class="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" @click="resetZoom">
            <RotateCcw class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
    
    <!-- 甘特图主体 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 空状态 -->
      <div v-if="!ganttData || !ganttData.resources || ganttData.resources.length === 0"
           class="flex-1 flex items-center justify-center">
        <div class="text-center">
          <div class="text-gray-400 mb-2">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div class="text-lg font-medium text-gray-900 mb-1">暂无甘特图数据</div>
          <div class="text-sm text-gray-500">请先完成预排产计算</div>
          <!-- 调试信息 -->
          <div v-if="isDevelopment" class="mt-4 text-xs text-gray-400">
            <div>调试信息:</div>
            <div>ganttData: {{ !!ganttData }}</div>
            <div>resources: {{ ganttData?.resources?.length || 0 }}</div>
            <div>tasks: {{ ganttData?.tasks?.length || 0 }}</div>
          </div>
        </div>
      </div>
      
      <!-- 甘特图内容 -->
      <template v-else>
        <!-- 左侧资源列表 -->
        <div class="w-80 border-r bg-gray-50 overflow-y-auto">
          <div class="sticky top-0 bg-gray-100 border-b p-3">
            <div class="text-sm font-medium text-gray-700">
              {{ getResourceTypeLabel() }}
            </div>
          </div>
          <div class="divide-y">
            <div
              v-for="resource in ganttData.resources"
              :key="resource.id"
              class="p-3 hover:bg-gray-100 cursor-pointer transition-colors"
              @click="selectResource(resource.id)"
              :class="selectedResourceId === resource.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <div class="font-medium text-sm text-gray-900">{{ resource.name }}</div>
                  <!-- 工艺段状态指示器 -->
                  <Badge 
                    v-if="resource.type === 'process_segment' && resource.status"
                    :variant="getStatusVariant(resource.status)"
                    class="text-xs"
                  >
                    {{ getStatusLabel(resource.status) }}
                  </Badge>
                </div>
                <!-- 下钻按钮 -->
                <Button
                  v-if="resource.type === 'process_segment' && enableDrillDown"
                  variant="ghost"
                  size="sm"
                  @click.stop="drillDown(resource.id)"
                  class="opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronRight class="h-4 w-4" />
                </Button>
              </div>
              
              <div class="text-xs text-gray-500 mt-1">
                利用率: {{ resource.utilization?.toFixed(0) || 0 }}%
                <span v-if="resource.type === 'process_segment' && resource.equipmentIds">
                  · {{ resource.equipmentIds.length }} 台设备
                </span>
              </div>
              
              <!-- 利用率进度条 -->
              <div class="w-full h-1.5 bg-gray-200 rounded-full mt-2">
                <div 
                  class="h-full rounded-full transition-all"
                  :class="getUtilizationColor(resource.utilization || 0)"
                  :style="{ width: `${resource.utilization || 0}%` }"
                />
              </div>
              
              <!-- 工艺段额外信息 -->
              <div v-if="resource.type === 'process_segment'" class="mt-2 text-xs text-gray-500">
                <div class="flex justify-between">
                  <span>总产能: {{ resource.totalCapacity || 0 }}</span>
                  <span>可用: {{ resource.availableCapacity || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧时间轴和任务条 -->
        <div class="flex-1 overflow-auto" ref="ganttContainer">
          <div class="min-w-full">
            <!-- 时间轴头部 -->
            <div class="sticky top-0 bg-white border-b z-10">
              <div class="h-12 flex items-center" :style="{ width: `${timelineWidth}px` }">
                <div
                  v-for="(timeSlot, index) in timeSlots"
                  :key="index"
                  class="border-r border-gray-200 px-2 text-xs text-gray-600 flex-shrink-0"
                  :style="{ width: `${timeSlotWidth}px` }"
                >
                  {{ timeSlot.label }}
                </div>
              </div>
            </div>
            
            <!-- 任务条区域 -->
            <div class="relative">
              <div
                v-for="resource in ganttData.resources"
                :key="resource.id"
                class="h-16 border-b border-gray-100 relative group"
                :style="{ width: `${timelineWidth}px` }"
              >
                <!-- 时间网格线 -->
                <div class="absolute inset-0 flex">
                  <div
                    v-for="(timeSlot, index) in timeSlots"
                    :key="index"
                    class="border-r border-gray-100 flex-shrink-0"
                    :style="{ width: `${timeSlotWidth}px` }"
                  />
                </div>
                
                <!-- 任务条 -->
                <div
                  v-for="task in getTasksForResource(resource.id)"
                  :key="task.id"
                  class="absolute top-2 h-12 rounded cursor-pointer transition-all hover:shadow-md"
                  :class="getTaskColor(task.status, resource.type)"
                  :style="getTaskStyle(task)"
                  @click="handleTaskClick(task)"
                  @mouseenter="showTaskTooltip(task, $event)"
                  @mouseleave="hideTaskTooltip"
                >
                  <div class="px-3 py-2 text-xs text-white font-medium truncate">
                    <div class="font-semibold">{{ task.name }}</div>
                    <div class="opacity-90 mt-1">{{ formatDuration(task.duration) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    
    <!-- 任务详情弹窗 -->
    <div
      v-if="tooltipTask"
      class="fixed z-50 bg-gray-900 text-white p-3 rounded-lg shadow-lg pointer-events-none"
      :style="tooltipStyle"
    >
      <div class="font-medium">{{ tooltipTask.name }}</div>
      <div class="text-sm opacity-90 mt-1">
        工艺段: {{ tooltipTask.workstation }}
      </div>
      <div class="text-sm opacity-90">
        时间: {{ formatTime(tooltipTask.start) }} - {{ formatTime(tooltipTask.end) }}
      </div>
      <div class="text-sm opacity-90">
        工期: {{ formatDuration(tooltipTask.duration) }}
      </div>
    </div>

    <!-- 下钻详情对话框 -->
    <Dialog v-model:open="showDrillDown">
      <DialogContent class="max-w-6xl w-full h-[80vh]">
        <DialogHeader>
          <DialogTitle>{{ drillDownData?.parentResourceId }} - 详细视图</DialogTitle>
          <DialogDescription>
            查看工艺段内部的设备分配和任务详情
          </DialogDescription>
        </DialogHeader>
        <div class="h-[calc(80vh-100px)] overflow-hidden">
          <GanttChart
            v-if="drillDownGanttData"
            :gantt-data="drillDownGanttData"
            :editable="false"
          />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ZoomIn, ZoomOut, RotateCcw, ChevronRight } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';

import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import GanttChart from './GanttChart.vue';
import type { 
  GanttChartData, 
  GanttTask, 
  GanttViewConfiguration,
  GanttDrillDownData,
  ScheduledBatch 
} from '@/types/scheduling';
import { processSegmentGanttService } from '@/services/processSegmentGanttService';
import { debugGanttTime } from '@/utils/ganttTimeDebugger';

interface Props {
  scheduledBatches: ScheduledBatch[];
  editable?: boolean;
  enableDrillDown?: boolean;
}

interface Emits {
  (e: 'task-click', task: GanttTask): void;
  (e: 'task-update', task: GanttTask): void;
}

const props = withDefaults(defineProps<Props>(), {
  editable: false,
  enableDrillDown: true
});

const emit = defineEmits<Emits>();

// 响应式状态
const selectedResourceId = ref<string | null>(null);
const zoomLevel = ref(1);
const ganttContainer = ref<HTMLElement>();
const tooltipTask = ref<GanttTask | null>(null);
const tooltipStyle = ref({});
const showDrillDown = ref(false);
const drillDownData = ref<GanttDrillDownData | null>(null);

// 甘特图数据
const ganttData = ref<GanttChartData | null>(null);

// 计算属性
const timeSlotWidth = computed(() => 80 * zoomLevel.value);
const timelineWidth = computed(() => timeSlots.value.length * timeSlotWidth.value);

const timeSlots = computed(() => {
  if (!ganttData.value || !ganttData.value.timeRange) {
    return [];
  }
  
  const start = new Date(ganttData.value.timeRange.start);
  const end = new Date(ganttData.value.timeRange.end);
  const slots = [];
  
  const current = new Date(start);
  while (current <= end) {
    slots.push({
      date: new Date(current),
      label: formatTimeSlot(current)
    });
    current.setHours(current.getHours() + 4); // 4小时间隔
  }
  
  return slots;
});

const drillDownGanttData = computed(() => {
  if (!drillDownData.value) return null;

  return {
    resources: drillDownData.value.childResources,
    tasks: drillDownData.value.childTasks,
    timeRange: ganttData.value?.timeRange || { start: '', end: '' }
  };
});

// 开发环境检查
const isDevelopment = computed(() => {
  return import.meta.env.DEV;
});

// 方法
const loadGanttData = async () => {
  console.log('🔄 开始加载甘特图数据...', {
    batchesCount: props.scheduledBatches?.length || 0
  });

  if (!props.scheduledBatches || props.scheduledBatches.length === 0) {
    console.log('❌ 没有排产批次数据');
    ganttData.value = null;
    return;
  }

  try {
    console.log('📊 转换为工艺段视图...');
    ganttData.value = await processSegmentGanttService.convertToProcessSegmentView(props.scheduledBatches);
    console.log('✅ 工艺段甘特图数据生成完成:', {
      resources: ganttData.value?.resources?.length || 0,
      tasks: ganttData.value?.tasks?.length || 0,
      timeRange: ganttData.value?.timeRange
    });

    // 调试时间数据
    if (ganttData.value && import.meta.env.DEV) {
      try {
        await debugGanttTime(props.scheduledBatches, ganttData.value);
      } catch (error) {
        console.warn('调试工具执行失败:', error);
      }
    }
  } catch (error) {
    console.error('加载甘特图数据失败:', error);
    ganttData.value = null;
  }
};

const selectResource = (resourceId: string) => {
  selectedResourceId.value = selectedResourceId.value === resourceId ? null : resourceId;
};

const drillDown = async (resourceId: string) => {
  try {
    drillDownData.value = await processSegmentGanttService.getDrillDownData(resourceId, props.scheduledBatches);
    showDrillDown.value = true;
  } catch (error) {
    console.error('获取下钻数据失败:', error);
  }
};

const getTasksForResource = (resourceId: string): GanttTask[] => {
  if (!ganttData.value) return [];
  return ganttData.value.tasks.filter(task => task.resourceId === resourceId);
};

const getResourceTypeLabel = (): string => {
  return '工艺段';
};

const getStatusVariant = (status: string) => {
  const variants = {
    normal: 'secondary',
    bottleneck: 'destructive',
    idle: 'outline'
  };
  return variants[status as keyof typeof variants] || 'secondary';
};

const getStatusLabel = (status: string): string => {
  const labels = {
    normal: '正常',
    bottleneck: '瓶颈',
    idle: '空闲'
  };
  return labels[status as keyof typeof labels] || status;
};

const getUtilizationColor = (utilization: number): string => {
  if (utilization > 90) return 'bg-red-500';
  if (utilization > 70) return 'bg-yellow-500';
  if (utilization > 30) return 'bg-green-500';
  return 'bg-gray-400';
};

const getTaskColor = (status: string, resourceType?: string): string => {
  const baseColors = {
    planned: 'bg-blue-500 hover:bg-blue-600',
    'in-progress': 'bg-green-500 hover:bg-green-600',
    completed: 'bg-gray-500 hover:bg-gray-600',
    delayed: 'bg-red-500 hover:bg-red-600'
  };
  
  // 工艺段任务使用更深的颜色
  if (resourceType === 'process_segment') {
    return baseColors[status as keyof typeof baseColors] || baseColors.planned;
  }
  
  return baseColors[status as keyof typeof baseColors] || baseColors.planned;
};

const getTaskStyle = (task: GanttTask) => {
  if (!ganttData.value?.timeRange) return {};

  const startTime = new Date(task.start);
  const endTime = new Date(task.end);
  const rangeStart = new Date(ganttData.value.timeRange.start);
  const rangeEnd = new Date(ganttData.value.timeRange.end);

  const totalDuration = rangeEnd.getTime() - rangeStart.getTime();
  const taskStart = startTime.getTime() - rangeStart.getTime();
  const taskDuration = endTime.getTime() - startTime.getTime();

  // 确保任务在有效范围内
  const leftPercent = Math.max(0, Math.min(100, (taskStart / totalDuration) * 100));
  const widthPercent = Math.max(1, Math.min(100 - leftPercent, (taskDuration / totalDuration) * 100));

  // 调试信息
  if (leftPercent < 0 || leftPercent > 100 || widthPercent <= 0) {
    console.warn('任务定位异常:', {
      taskId: task.id,
      taskTime: { start: task.start, end: task.end },
      rangeTime: { start: ganttData.value.timeRange.start, end: ganttData.value.timeRange.end },
      calculated: { leftPercent, widthPercent },
      durations: { total: totalDuration, taskStart, taskDuration }
    });
  }

  return {
    left: `${leftPercent}%`,
    width: `${widthPercent}%`,
    minWidth: '60px'
  };
};

const handleTaskClick = (task: GanttTask) => {
  emit('task-click', task);
};

const showTaskTooltip = (task: GanttTask, event: MouseEvent) => {
  tooltipTask.value = task;
  tooltipStyle.value = {
    left: `${event.clientX + 10}px`,
    top: `${event.clientY - 10}px`
  };
};

const hideTaskTooltip = () => {
  tooltipTask.value = null;
};

const zoomIn = () => {
  zoomLevel.value = Math.min(3, zoomLevel.value + 0.2);
};

const zoomOut = () => {
  zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.2);
};

const resetZoom = () => {
  zoomLevel.value = 1;
};

// 格式化函数
const formatDate = (dateStr: string): string => {
  return new Date(dateStr).toLocaleDateString('zh-CN');
};

const formatTime = (dateStr: string): string => {
  return new Date(dateStr).toLocaleString('zh-CN');
};

const formatTimeSlot = (date: Date): string => {
  return date.toLocaleDateString('zh-CN', { 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit'
  });
};

const formatDuration = (hours: number): string => {
  if (hours < 1) {
    return `${Math.round(hours * 60)}分钟`;
  }
  if (hours < 24) {
    return `${hours.toFixed(1)}小时`;
  }
  return `${(hours / 24).toFixed(1)}天`;
};

// 监听器
watch(() => props.scheduledBatches, loadGanttData, { immediate: true });

// 生命周期
onMounted(() => {
  loadGanttData();
});
</script>

<style scoped>
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
</style>
