<template>
  <div class="h-full flex flex-col bg-white border rounded-lg overflow-hidden">
    <!-- 甘特图头部 -->
    <div class="p-4 border-b bg-gray-50">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="font-medium text-gray-900">排产甘特图</h4>
          <p class="text-sm text-gray-500">
            <span v-if="ganttData && ganttData.timeRange">
              时间范围: {{ formatDate(ganttData.timeRange.start) }} - {{ formatDate(ganttData.timeRange.end) }}
            </span>
            <span v-else>暂无数据</span>
          </p>
        </div>
        
        <div class="flex items-center space-x-2">
          <Button variant="outline" size="sm" @click="zoomIn">
            <ZoomIn class="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" @click="zoomOut">
            <ZoomOut class="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" @click="resetZoom">
            <RotateCcw class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
    
    <!-- 甘特图主体 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 空状态 -->
      <div v-if="!ganttData || !ganttData.resources || ganttData.resources.length === 0" 
           class="flex-1 flex items-center justify-center">
        <div class="text-center">
          <div class="text-gray-400 mb-2">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div class="text-lg font-medium text-gray-900 mb-1">暂无甘特图数据</div>
          <div class="text-sm text-gray-500">请先完成预排产计算</div>
        </div>
      </div>
      
      <!-- 甘特图内容 -->
      <template v-else>
        <!-- 左侧资源列表 -->
        <div class="w-64 border-r bg-gray-50 overflow-y-auto">
          <div class="sticky top-0 bg-gray-100 border-b p-3">
            <div class="text-sm font-medium text-gray-700">资源/设备</div>
          </div>
          <div class="divide-y">
            <div
              v-for="resource in ganttData.resources"
              :key="resource.id"
              class="p-3 hover:bg-gray-100 cursor-pointer"
              @click="selectResource(resource.id)"
              :class="selectedResourceId === resource.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''"
            >
              <div class="font-medium text-sm text-gray-900">{{ resource.name }}</div>
              <div class="text-xs text-gray-500 mt-1">
                利用率: {{ resource.utilization?.toFixed(0) || 0 }}%
              </div>
              <div class="w-full h-1.5 bg-gray-200 rounded-full mt-2">
                <div 
                  class="h-full rounded-full transition-all"
                  :class="getUtilizationColor(resource.utilization || 0)"
                  :style="{ width: `${resource.utilization || 0}%` }"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧时间轴和任务条 -->
        <div class="flex-1 overflow-auto" ref="ganttContainer">
          <div class="min-w-full">
            <!-- 时间轴头部 -->
            <div class="sticky top-0 bg-white border-b z-10">
              <div class="h-12 flex items-center" :style="{ width: `${timelineWidth}px` }">
                <div
                  v-for="(timeSlot, index) in timeSlots"
                  :key="index"
                  class="border-r border-gray-200 px-2 text-xs text-gray-600 flex-shrink-0"
                  :style="{ width: `${timeSlotWidth}px` }"
                >
                  {{ timeSlot.label }}
                </div>
              </div>
            </div>
            
            <!-- 任务条区域 -->
            <div class="relative">
              <div
                v-for="resource in ganttData.resources"
                :key="resource.id"
                class="h-12 border-b border-gray-100 relative"
                :style="{ width: `${timelineWidth}px` }"
              >
                <!-- 时间网格线 -->
                <div class="absolute inset-0 flex">
                  <div
                    v-for="(timeSlot, index) in timeSlots"
                    :key="index"
                    class="border-r border-gray-100 flex-shrink-0"
                    :style="{ width: `${timeSlotWidth}px` }"
                  />
                </div>
                
                <!-- 任务条 -->
                <div
                  v-for="task in getTasksForResource(resource.id)"
                  :key="task.id"
                  class="absolute top-1 h-10 rounded cursor-pointer transition-all hover:shadow-md"
                  :class="getTaskColor(task.status)"
                  :style="getTaskStyle(task)"
                  @click="handleTaskClick(task)"
                  @mouseenter="showTaskTooltip(task, $event)"
                  @mouseleave="hideTaskTooltip"
                >
                  <div class="px-2 py-1 text-xs text-white font-medium truncate">
                    {{ task.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    
    <!-- 任务详情弹窗 -->
    <div
      v-if="tooltipTask"
      class="fixed z-50 bg-gray-900 text-white p-3 rounded-lg shadow-lg pointer-events-none"
      :style="tooltipStyle"
    >
      <div class="font-medium">{{ tooltipTask.name }}</div>
      <div class="text-sm opacity-90 mt-1">
        工作站: {{ tooltipTask.workstation }}
      </div>
      <div class="text-sm opacity-90">
        时间: {{ formatTime(tooltipTask.start) }} - {{ formatTime(tooltipTask.end) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ZoomIn, ZoomOut, RotateCcw } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import type { GanttChartData, GanttTask } from '@/types/scheduling';

interface Props {
  ganttData: GanttChartData;
  editable?: boolean;
}

interface Emits {
  (e: 'task-click', task: GanttTask): void;
  (e: 'task-update', task: GanttTask): void;
}

const props = withDefaults(defineProps<Props>(), {
  editable: false
});

const emit = defineEmits<Emits>();

// 响应式状态
const selectedResourceId = ref<string | null>(null);
const zoomLevel = ref(1);
const ganttContainer = ref<HTMLElement>();
const tooltipTask = ref<GanttTask | null>(null);
const tooltipStyle = ref({});

// 计算属性
const timeSlotWidth = computed(() => 80 * zoomLevel.value);
const timelineWidth = computed(() => timeSlots.value.length * timeSlotWidth.value);

const timeSlots = computed(() => {
  if (!props.ganttData || !props.ganttData.timeRange) {
    return [];
  }
  
  const start = new Date(props.ganttData.timeRange.start);
  const end = new Date(props.ganttData.timeRange.end);
  const slots = [];
  
  const current = new Date(start);
  while (current <= end) {
    slots.push({
      date: new Date(current),
      label: formatTimeSlot(current)
    });
    current.setHours(current.getHours() + 4); // 4小时间隔
  }
  
  return slots;
});

// 方法
const getTasksForResource = (resourceId: string) => {
  if (!props.ganttData || !props.ganttData.tasks) {
    return [];
  }
  return props.ganttData.tasks.filter(task => task.resourceId === resourceId);
};

const getTaskStyle = (task: GanttTask) => {
  if (!props.ganttData || !props.ganttData.timeRange) {
    return { left: '0px', width: '0px' };
  }
  
  const startTime = new Date(task.start);
  const endTime = new Date(task.end);
  const timelineStart = new Date(props.ganttData.timeRange.start);
  
  const startOffset = (startTime.getTime() - timelineStart.getTime()) / (1000 * 60 * 60 * 4) * timeSlotWidth.value;
  const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 4) * timeSlotWidth.value;
  
  return {
    left: `${Math.max(0, startOffset)}px`,
    width: `${Math.max(20, duration)}px`
  };
};

const getTaskColor = (status: string) => {
  const colors = {
    planned: 'bg-blue-500',
    in_progress: 'bg-green-500',
    completed: 'bg-gray-500',
    delayed: 'bg-red-500'
  };
  return colors[status as keyof typeof colors] || 'bg-blue-500';
};

const getUtilizationColor = (utilization: number) => {
  if (utilization >= 90) return 'bg-red-500';
  if (utilization >= 75) return 'bg-yellow-500';
  return 'bg-green-500';
};

const selectResource = (resourceId: string) => {
  selectedResourceId.value = selectedResourceId.value === resourceId ? null : resourceId;
};

const handleTaskClick = (task: GanttTask) => {
  emit('task-click', task);
};

const showTaskTooltip = (task: GanttTask, event: MouseEvent) => {
  tooltipTask.value = task;
  tooltipStyle.value = {
    left: `${event.clientX + 10}px`,
    top: `${event.clientY - 10}px`
  };
};

const hideTaskTooltip = () => {
  tooltipTask.value = null;
};

const zoomIn = () => {
  zoomLevel.value = Math.min(2, zoomLevel.value + 0.2);
};

const zoomOut = () => {
  zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.2);
};

const resetZoom = () => {
  zoomLevel.value = 1;
};

// 格式化函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

const formatTimeSlot = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { 
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>
