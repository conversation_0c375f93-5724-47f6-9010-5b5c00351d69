<script setup lang="ts">
const props = defineProps<{ flows: { name: string; count: number }[] }>()
</script>

<template>
  <div class="border rounded p-3">
    <div class="font-medium mb-2">工段流转演示（占位）</div>
    <div class="flex items-center gap-4">
      <template v-for="(f, idx) in props.flows" :key="idx">
        <div class="px-3 py-1 bg-gray-100 rounded">{{ f.name }}：{{ f.count }}片</div>
        <div v-if="idx < props.flows.length - 1">→</div>
      </template>
    </div>
  </div>
</template>

