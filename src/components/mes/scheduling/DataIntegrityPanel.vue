<template>
  <div class="bg-white rounded-lg border overflow-hidden">
    <div class="p-4 border-b bg-gray-50">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="font-semibold text-gray-900">数据完整性报告</h3>
          <p class="text-sm text-gray-500">外键关联性和数据一致性验证</p>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          @click="refreshIntegrity"
          :disabled="loading"
        >
          <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': loading }" />
          刷新检查
        </Button>
      </div>
    </div>
    
    <div class="p-4 space-y-4">
      <!-- 整体状态 -->
      <div v-if="integrityStatus" class="space-y-4">
        <!-- 验证结果概览 -->
        <div class="flex items-center space-x-4 p-3 rounded-lg"
             :class="integrityStatus.isValid ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
          <div class="flex-shrink-0">
            <CheckCircle v-if="integrityStatus.isValid" class="h-6 w-6 text-green-600" />
            <AlertTriangle v-else class="h-6 w-6 text-red-600" />
          </div>
          
          <div class="flex-1">
            <div class="font-medium" :class="integrityStatus.isValid ? 'text-green-900' : 'text-red-900'">
              {{ integrityStatus.isValid ? '数据完整性验证通过' : '发现数据完整性问题' }}
            </div>
            <div class="text-sm" :class="integrityStatus.isValid ? 'text-green-700' : 'text-red-700'">
              {{ integrityStatus.summary }}
            </div>
          </div>
          
          <div class="text-right">
            <div class="text-lg font-bold" :class="integrityStatus.isValid ? 'text-green-600' : 'text-red-600'">
              {{ integrityStatus.passedChecks }}/{{ integrityStatus.totalChecks }}
            </div>
            <div class="text-xs text-gray-500">通过率</div>
          </div>
        </div>
        
        <!-- 关联性统计 -->
        <div v-if="integrityStatus.relationshipReport" class="grid grid-cols-2 gap-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div class="text-sm text-blue-600 mb-1">完整追溯性</div>
            <div class="text-2xl font-bold text-blue-900">
              {{ integrityStatus.relationshipReport.batchesWithFullTraceability }}
            </div>
            <div class="text-xs text-blue-600">
              / {{ integrityStatus.relationshipReport.totalBatches }} 个批次
            </div>
          </div>
          
          <div class="bg-green-50 border border-green-200 rounded-lg p-3">
            <div class="text-sm text-green-600 mb-1">追溯率</div>
            <div class="text-2xl font-bold text-green-900">
              {{ (integrityStatus.relationshipReport.traceabilityRate * 100).toFixed(1) }}%
            </div>
            <div class="text-xs text-green-600">数据关联完整度</div>
          </div>
        </div>
        
        <!-- 外键关联矩阵 -->
        <div v-if="integrityStatus.relationshipReport" class="space-y-2">
          <h4 class="font-medium text-gray-900">外键关联统计</h4>
          <div class="grid grid-cols-1 gap-2">
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span class="text-sm text-gray-600">生产工单ID关联</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    class="h-full bg-blue-500 transition-all"
                    :style="{ width: `${getPercentage('hasProductionOrderId')}%` }"
                  />
                </div>
                <span class="text-sm font-medium">
                  {{ integrityStatus.relationshipReport.relationshipMatrix.hasProductionOrderId }}
                </span>
              </div>
            </div>
            
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span class="text-sm text-gray-600">工单号关联</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    class="h-full bg-green-500 transition-all"
                    :style="{ width: `${getPercentage('hasWorkOrderNumber')}%` }"
                  />
                </div>
                <span class="text-sm font-medium">
                  {{ integrityStatus.relationshipReport.relationshipMatrix.hasWorkOrderNumber }}
                </span>
              </div>
            </div>
            
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span class="text-sm text-gray-600">客户订单关联</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    class="h-full bg-purple-500 transition-all"
                    :style="{ width: `${getPercentage('hasCustomerOrderId')}%` }"
                  />
                </div>
                <span class="text-sm font-medium">
                  {{ integrityStatus.relationshipReport.relationshipMatrix.hasCustomerOrderId }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 问题列表 -->
        <div v-if="integrityStatus.issues && integrityStatus.issues.length > 0" class="space-y-2">
          <h4 class="font-medium text-gray-900">发现的问题</h4>
          <div class="space-y-2 max-h-40 overflow-y-auto">
            <div 
              v-for="issue in integrityStatus.issues.slice(0, 5)"
              :key="`${issue.entity}-${issue.entityId}-${issue.field}`"
              class="p-2 rounded border-l-4"
              :class="getSeverityClass(issue.severity)"
            >
              <div class="flex items-center justify-between">
                <div class="font-medium text-sm">{{ issue.description }}</div>
                <Badge :variant="getSeverityVariant(issue.severity)" class="text-xs">
                  {{ getSeverityText(issue.severity) }}
                </Badge>
              </div>
              <div v-if="issue.suggestion" class="text-xs text-gray-600 mt-1">
                建议: {{ issue.suggestion }}
              </div>
            </div>
          </div>
          
          <div v-if="integrityStatus.issues.length > 5" class="text-center">
            <Button variant="ghost" size="sm" class="text-xs">
              查看全部 {{ integrityStatus.issues.length }} 个问题
            </Button>
          </div>
        </div>
        
        <!-- 最后检查时间 -->
        <div v-if="integrityStatus.lastCheck" class="text-xs text-gray-500 text-center pt-2 border-t">
          最后检查: {{ formatDateTime(integrityStatus.lastCheck) }}
        </div>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else class="text-center py-8">
        <Database class="h-12 w-12 text-gray-300 mx-auto mb-3" />
        <div class="text-lg font-medium text-gray-900">暂无数据完整性报告</div>
        <div class="text-sm text-gray-500 mb-4">点击刷新检查按钮开始验证</div>
        <Button @click="refreshIntegrity" :disabled="loading">
          开始检查
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { RefreshCw, CheckCircle, AlertTriangle, Database } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useSchedulingStore } from '@/stores/schedulingStore';

const schedulingStore = useSchedulingStore();
const loading = ref(false);

const integrityStatus = computed(() => {
  return schedulingStore.getDataIntegrityStatus();
});

const refreshIntegrity = async () => {
  loading.value = true;
  try {
    await schedulingStore.validateDataIntegrity();
  } finally {
    loading.value = false;
  }
};

const getPercentage = (field: string) => {
  if (!integrityStatus.value?.relationshipReport) return 0;
  const total = integrityStatus.value.relationshipReport.totalBatches;
  const count = integrityStatus.value.relationshipReport.relationshipMatrix[field];
  return total > 0 ? (count / total) * 100 : 0;
};

const getSeverityClass = (severity: string) => {
  const classes = {
    critical: 'bg-red-50 border-red-500',
    high: 'bg-orange-50 border-orange-500',
    medium: 'bg-yellow-50 border-yellow-500',
    low: 'bg-blue-50 border-blue-500'
  };
  return classes[severity as keyof typeof classes] || classes.medium;
};

const getSeverityVariant = (severity: string): "default" | "outline" | "destructive" | "secondary" => {
  const variants: Record<string, "default" | "outline" | "destructive" | "secondary"> = {
    critical: 'destructive',
    high: 'destructive',
    medium: 'default',
    low: 'secondary'
  };
  return variants[severity] || 'default';
};

const getSeverityText = (severity: string) => {
  const texts = {
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低'
  };
  return texts[severity as keyof typeof texts] || '中';
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

onMounted(() => {
  // 组件加载时自动检查一次
  if (!integrityStatus.value) {
    refreshIntegrity();
  }
});
</script>
