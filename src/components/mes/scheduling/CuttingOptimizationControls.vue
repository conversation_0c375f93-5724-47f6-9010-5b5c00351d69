<template>
  <div class="h-full">
    <!-- 使用新的切割优化视图 -->
    <CuttingOptimizationView
      :export-status="exportStatus"
      :import-status="importStatus"
      :export-data="exportData"
      :cutting-result="cuttingResult"
      :validation-result="validationResult"
      :comparison-data="comparisonData"
      @export-data="$emit('export-data')"
      @import-result="$emit('import-result', $event)"
      @approve-result="$emit('approve-result')"
      @reject-result="$emit('reject-result', $event)"
      @proceed-to-final="$emit('proceed-to-final')"
    />
  </div>
</template>

<script setup lang="ts">
import CuttingOptimizationView from './CuttingOptimizationView.vue';
import type {
  CuttingExportData,
  CuttingImportResult,
  ValidationResult,
  ComparisonData
} from '@/types/scheduling';

interface Props {
  exportStatus: 'idle' | 'exporting' | 'exported' | 'error';
  importStatus: 'idle' | 'importing' | 'validating' | 'imported' | 'error';
  exportData?: CuttingExportData;
  cuttingResult?: CuttingImportResult;
  validationResult?: ValidationResult;
  comparisonData?: ComparisonData;
}

interface Emits {
  (e: 'export-data'): void;
  (e: 'import-result', file: File): void;
  (e: 'approve-result'): void;
  (e: 'reject-result', reason: string): void;
  (e: 'proceed-to-final'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
</script>
