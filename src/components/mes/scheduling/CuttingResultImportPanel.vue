<template>
  <div class="space-y-6">
    <!-- 标题和描述 -->
    <div class="text-center">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">导入优化结果</h3>
      <p class="text-sm text-gray-600">上传第三方系统返回的切割优化结果文件</p>
    </div>

    <!-- 文件上传区域 -->
    <div v-if="importStatus === 'idle'" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
      <div class="space-y-4">
        <Upload class="h-12 w-12 text-gray-400 mx-auto" />
        <div>
          <h4 class="text-lg font-medium text-gray-900 mb-2">选择结果文件</h4>
          <p class="text-sm text-gray-600 mb-4">
            支持 Excel (.xlsx) 和 JSON (.json) 格式
          </p>
        </div>
        
        <!-- 文件选择按钮 -->
        <div class="space-y-3">
          <input
            ref="fileInput"
            type="file"
            accept=".xlsx,.json"
            @change="handleFileSelect"
            class="hidden"
          />
          <Button @click="$refs.fileInput?.click()" size="lg" class="px-8">
            <Upload class="h-4 w-4 mr-2" />
            选择文件
          </Button>
          <div class="text-xs text-gray-500">
            或将文件拖拽到此区域
          </div>
        </div>
      </div>
    </div>

    <!-- 导入进行中 -->
    <div v-else-if="importStatus === 'importing'" class="border border-blue-200 bg-blue-50 rounded-lg p-6">
      <div class="text-center space-y-4">
        <div class="flex items-center justify-center">
          <RefreshCw class="h-8 w-8 text-blue-500 animate-spin" />
        </div>
        <div>
          <h4 class="text-lg font-medium text-blue-900 mb-2">正在导入文件...</h4>
          <p class="text-sm text-blue-700">
            正在解析文件内容并验证数据格式
          </p>
        </div>
        <div class="w-64 mx-auto">
          <div class="w-full bg-blue-200 rounded-full h-2">
            <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" :style="{ width: `${importProgress}%` }"></div>
          </div>
          <div class="text-xs text-blue-600 mt-1">{{ importProgress }}%</div>
        </div>
      </div>
    </div>

    <!-- 验证进行中 -->
    <div v-else-if="importStatus === 'validating'" class="border border-yellow-200 bg-yellow-50 rounded-lg p-6">
      <div class="text-center space-y-4">
        <div class="flex items-center justify-center">
          <CheckCircle class="h-8 w-8 text-yellow-500 animate-pulse" />
        </div>
        <div>
          <h4 class="text-lg font-medium text-yellow-900 mb-2">正在验证结果...</h4>
          <p class="text-sm text-yellow-700">
            正在检查数据完整性和业务逻辑合理性
          </p>
        </div>
        
        <!-- 验证步骤 -->
        <div class="text-left max-w-md mx-auto">
          <div class="space-y-2">
            <div v-for="step in validationSteps" :key="step.id" class="flex items-center space-x-2 text-sm">
              <component 
                :is="getValidationIcon(step.id)" 
                class="h-4 w-4"
                :class="getValidationIconClass(step.id)"
              />
              <span :class="getValidationTextClass(step.id)">{{ step.title }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导入成功 -->
    <div v-else-if="importStatus === 'imported'" class="border border-green-200 bg-green-50 rounded-lg p-6">
      <div class="flex items-start space-x-4">
        <CheckCircle class="h-8 w-8 text-green-600 flex-shrink-0" />
        <div class="flex-1">
          <h4 class="text-lg font-medium text-green-900 mb-2">导入成功</h4>
          <p class="text-sm text-green-700 mb-4">
            切割优化结果已成功导入并通过验证
          </p>
          
          <!-- 验证结果摘要 -->
          <div v-if="validationResult" class="bg-white rounded border p-4">
            <h5 class="text-sm font-medium text-gray-900 mb-3">验证结果摘要</h5>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="space-y-1">
                <div class="flex justify-between">
                  <span class="text-gray-600">数据完整性:</span>
                  <Badge variant="outline" class="text-green-700 border-green-300">通过</Badge>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">格式验证:</span>
                  <Badge variant="outline" class="text-green-700 border-green-300">通过</Badge>
                </div>
              </div>
              <div class="space-y-1">
                <div class="flex justify-between">
                  <span class="text-gray-600">业务逻辑:</span>
                  <Badge variant="outline" class="text-green-700 border-green-300">通过</Badge>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">约束检查:</span>
                  <Badge variant="outline" class="text-green-700 border-green-300">通过</Badge>
                </div>
              </div>
            </div>
            
            <!-- 警告信息 -->
            <div v-if="validationResult.warnings && validationResult.warnings.length > 0" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
              <div class="flex items-start space-x-2">
                <AlertTriangle class="h-4 w-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div class="text-sm">
                  <div class="font-medium text-yellow-800 mb-1">注意事项</div>
                  <ul class="text-yellow-700 space-y-1">
                    <li v-for="warning in validationResult.warnings" :key="warning.code">
                      • {{ warning.message }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导入错误 -->
    <div v-else-if="importStatus === 'error'" class="border border-red-200 bg-red-50 rounded-lg p-6">
      <div class="flex items-start space-x-4">
        <AlertTriangle class="h-8 w-8 text-red-600 flex-shrink-0" />
        <div class="flex-1">
          <h4 class="text-lg font-medium text-red-900 mb-2">导入失败</h4>
          <p class="text-sm text-red-700 mb-4">
            文件导入过程中出现错误，请检查文件格式和内容
          </p>
          
          <!-- 错误详情 -->
          <div v-if="validationResult && validationResult.errors" class="bg-white rounded border p-4 mb-4">
            <h5 class="text-sm font-medium text-gray-900 mb-3">错误详情</h5>
            <div class="space-y-2">
              <div v-for="error in validationResult.errors" :key="error.code" class="text-sm">
                <div class="font-medium text-red-800">{{ error.field || '文件格式' }}</div>
                <div class="text-red-600">{{ error.message }}</div>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <Button @click="$emit('retry-import')" variant="outline" size="sm">
              <RefreshCw class="h-4 w-4 mr-2" />
              重新导入
            </Button>
            <Button variant="outline" size="sm">
              <HelpCircle class="h-4 w-4 mr-2" />
              获取帮助
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件格式说明 -->
    <div class="bg-gray-50 rounded-lg p-4">
      <h5 class="text-sm font-medium text-gray-900 mb-2">文件格式要求</h5>
      <div class="text-sm text-gray-600 space-y-1">
        <div class="font-medium">Excel 格式 (.xlsx):</div>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li>包含"切割方案"、"物料使用"、"时间估算"等工作表</li>
          <li>数据格式需与导出模板保持一致</li>
        </ul>
        <div class="font-medium mt-3">JSON 格式 (.json):</div>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li>标准JSON结构，包含完整的优化结果数据</li>
          <li>必须包含校验码以确保数据完整性</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Upload, RefreshCw, CheckCircle, AlertTriangle, 
  HelpCircle, Clock, FileCheck, Shield
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { ValidationResult } from '@/types/scheduling';

interface Props {
  importStatus: 'idle' | 'importing' | 'validating' | 'imported' | 'error';
  validationResult?: ValidationResult;
}

interface Emits {
  (e: 'import-result', file: File): void;
  (e: 'retry-import'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 导入进度
const importProgress = ref(0);

// 验证步骤
const validationSteps = [
  { id: 'format', title: '文件格式检查' },
  { id: 'structure', title: '数据结构验证' },
  { id: 'business', title: '业务逻辑检查' },
  { id: 'constraints', title: '约束条件验证' }
];

// 当前验证步骤
const currentValidationStep = ref('format');

// 文件选择处理
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    emit('import-result', file);
  }
};

// 验证图标和样式
const getValidationIcon = (stepId: string) => {
  const icons = {
    format: FileCheck,
    structure: CheckCircle,
    business: Shield,
    constraints: Clock
  };
  return icons[stepId as keyof typeof icons] || CheckCircle;
};

const getValidationIconClass = (stepId: string) => {
  const currentIndex = validationSteps.findIndex(s => s.id === currentValidationStep.value);
  const stepIndex = validationSteps.findIndex(s => s.id === stepId);
  
  if (stepIndex < currentIndex) {
    return 'text-green-600';
  } else if (stepIndex === currentIndex) {
    return 'text-yellow-600';
  } else {
    return 'text-gray-400';
  }
};

const getValidationTextClass = (stepId: string) => {
  const currentIndex = validationSteps.findIndex(s => s.id === currentValidationStep.value);
  const stepIndex = validationSteps.findIndex(s => s.id === stepId);
  
  if (stepIndex < currentIndex) {
    return 'text-green-600';
  } else if (stepIndex === currentIndex) {
    return 'text-yellow-600';
  } else {
    return 'text-gray-500';
  }
};

// 模拟导入进度
if (props.importStatus === 'importing') {
  const interval = setInterval(() => {
    importProgress.value += 15;
    if (importProgress.value >= 100) {
      clearInterval(interval);
    }
  }, 300);
}

// 模拟验证步骤推进
if (props.importStatus === 'validating') {
  let stepIndex = 0;
  const stepInterval = setInterval(() => {
    if (stepIndex < validationSteps.length) {
      currentValidationStep.value = validationSteps[stepIndex].id;
      stepIndex++;
    } else {
      clearInterval(stepInterval);
    }
  }, 1000);
}
</script>
