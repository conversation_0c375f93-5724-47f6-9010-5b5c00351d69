<template>
  <div class="bg-white rounded-lg border overflow-hidden flex flex-col h-full">
    <!-- 标题栏 -->
    <div class="p-4 border-b bg-gray-50">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="font-semibold text-gray-900">待排产批次池</h3>
          <p class="text-sm text-gray-500">
            共 {{ batches.length }} 个批次，已选择 {{ selectedBatchIds.length }} 个
          </p>
        </div>
        
        <!-- 批量操作 -->
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            @click="selectAll"
            :disabled="loading"
          >
            全选
          </Button>
          <Button
            variant="outline"
            size="sm"
            @click="clearSelection"
            :disabled="selectedBatchIds.length === 0"
          >
            清空
          </Button>
        </div>
      </div>
      
      <!-- 筛选器 -->
      <div class="mt-3 flex items-center space-x-2">
        <Select v-model="priorityFilter">
          <SelectTrigger class="w-32">
            <SelectValue placeholder="优先级" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部优先级</SelectItem>
            <SelectItem value="urgent">紧急</SelectItem>
            <SelectItem value="high">高</SelectItem>
            <SelectItem value="normal">普通</SelectItem>
            <SelectItem value="low">低</SelectItem>
          </SelectContent>
        </Select>
        
        <Select v-model="statusFilter">
          <SelectTrigger class="w-32">
            <SelectValue placeholder="状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="ready">就绪</SelectItem>
            <SelectItem value="pending">等待</SelectItem>
            <SelectItem value="blocked">阻塞</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <!-- 批次列表 -->
    <div class="flex-1 overflow-y-auto p-2">
      <div v-if="loading" class="flex items-center justify-center h-32">
        <div class="flex items-center space-x-2 text-gray-500">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
          <span class="text-sm">加载批次数据...</span>
        </div>
      </div>
      
      <div v-else-if="filteredBatches.length === 0" class="flex items-center justify-center h-32">
        <div class="text-center text-gray-500">
          <Package class="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p class="text-sm">暂无可排产批次</p>
        </div>
      </div>
      
      <div v-else class="space-y-2">
        <BatchCard
          v-for="batch in filteredBatches"
          :key="batch.id"
          :batch="batch"
          :selected="selectedBatchIds.includes(batch.id)"
          @toggle-select="toggleBatchSelection"
          @view-details="handleViewDetails"
        />
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div v-if="selectedBatchIds.length > 0" class="p-4 border-t bg-blue-50">
      <div class="text-sm">
        <div class="font-medium text-blue-900 mb-1">选中批次统计</div>
        <div class="grid grid-cols-2 gap-2 text-blue-700">
          <div>总数量: {{ selectedTotalQuantity }} 片</div>
          <div>预计工时: {{ selectedTotalHours.toFixed(1) }} 小时</div>
          <div>涉及客户: {{ selectedCustomerCount }} 家</div>
          <div>平均利用率: {{ selectedAvgUtilization.toFixed(1) }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Package } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import BatchCard from './BatchCard.vue';
import type { OptimizedBatch } from '@/types/production-order-creation';

interface Props {
  batches: OptimizedBatch[];
  loading?: boolean;
}

interface Emits {
  (e: 'batch-select', batchIds: string[]): void;
  (e: 'view-details', batch: OptimizedBatch): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 响应式状态
const selectedBatchIds = ref<string[]>([]);
const priorityFilter = ref('all');
const statusFilter = ref('all');

// 计算属性
const filteredBatches = computed(() => {
  let filtered = props.batches;
  
  // 按优先级筛选
  if (priorityFilter.value !== 'all') {
    filtered = filtered.filter(batch => batch.priority === priorityFilter.value);
  }
  
  // 按状态筛选（这里简化处理，实际应该有状态字段）
  if (statusFilter.value !== 'all') {
    // 模拟状态筛选逻辑
    filtered = filtered.filter(batch => {
      if (statusFilter.value === 'ready') return batch.utilization > 80;
      if (statusFilter.value === 'pending') return batch.utilization >= 60 && batch.utilization <= 80;
      if (statusFilter.value === 'blocked') return batch.utilization < 60;
      return true;
    });
  }
  
  return filtered;
});

const selectedBatches = computed(() => {
  return props.batches.filter(batch => selectedBatchIds.value.includes(batch.id));
});

const selectedTotalQuantity = computed(() => {
  return selectedBatches.value.reduce((sum, batch) => sum + batch.totalQuantity, 0);
});

const selectedTotalHours = computed(() => {
  return selectedBatches.value.reduce((sum, batch) => {
    const duration = (batch as any).estimatedDuration || batch.estimatedTime || (batch.items.length * 60);
    return sum + (duration / 60);
  }, 0);
});

const selectedCustomerCount = computed(() => {
  const customers = new Set(
    selectedBatches.value.flatMap(batch => 
      batch.items.map(item => item.customerName)
    )
  );
  return customers.size;
});

const selectedAvgUtilization = computed(() => {
  if (selectedBatches.value.length === 0) return 0;
  const totalUtilization = selectedBatches.value.reduce((sum, batch) => sum + batch.utilization, 0);
  return totalUtilization / selectedBatches.value.length;
});

// 方法
const toggleBatchSelection = (batchId: string) => {
  const index = selectedBatchIds.value.indexOf(batchId);
  if (index > -1) {
    selectedBatchIds.value.splice(index, 1);
  } else {
    selectedBatchIds.value.push(batchId);
  }
  
  emit('batch-select', selectedBatchIds.value);
};

const selectAll = () => {
  selectedBatchIds.value = filteredBatches.value.map(batch => batch.id);
  emit('batch-select', selectedBatchIds.value);
};

const clearSelection = () => {
  selectedBatchIds.value = [];
  emit('batch-select', selectedBatchIds.value);
};

const handleViewDetails = (batch: OptimizedBatch) => {
  emit('view-details', batch);
};
</script>
