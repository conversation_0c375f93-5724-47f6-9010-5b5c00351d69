<template>
  <div class="space-y-4">
    <div class="text-lg font-medium text-gray-900">资源利用率分析</div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div
        v-for="resource in resources"
        :key="resource.id"
        class="bg-white border rounded-lg p-4"
      >
        <div class="flex items-center justify-between mb-3">
          <div class="font-medium text-gray-900">{{ resource.name }}</div>
          <div class="text-sm text-gray-500">{{ resource.type }}</div>
        </div>
        
        <div class="space-y-2">
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">利用率</span>
            <span class="font-medium">{{ resource.utilization.toFixed(1) }}%</span>
          </div>
          
          <div class="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
            <div 
              class="h-full transition-all duration-300"
              :class="getUtilizationColor(resource.utilization)"
              :style="{ width: `${resource.utilization}%` }"
            />
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">产能</span>
            <span class="font-medium">{{ resource.capacity }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GanttResource } from '@/types/scheduling';

interface Props {
  resources: GanttResource[];
  timeRange: {
    start: string;
    end: string;
  };
}

const props = defineProps<Props>();

const getUtilizationColor = (utilization: number) => {
  if (utilization >= 85) return 'bg-green-500';
  if (utilization >= 70) return 'bg-yellow-500';
  return 'bg-red-500';
};
</script>
