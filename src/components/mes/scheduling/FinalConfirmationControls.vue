<template>
  <div class="space-y-6">
    <!-- 优化结果对比 -->
    <div class="space-y-4">
      <div class="text-lg font-medium text-gray-900">优化结果对比</div>

      <div v-if="comparisonData" class="bg-white border rounded-lg overflow-hidden">
        <div class="p-4">
          <div class="grid grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-sm text-gray-500 mb-1">总工时 (小时)</div>
              <div class="flex items-center justify-center space-x-2">
                <span class="text-lg font-medium text-gray-600">{{ comparisonData.duration.before }}</span>
                <ArrowRight class="h-4 w-4 text-gray-400" />
                <span class="text-lg font-bold text-green-600">{{ comparisonData.duration.after }}</span>
              </div>
              <div class="text-xs text-green-600 mt-1">
                {{ formatImprovement(comparisonData.duration.after - comparisonData.duration.before, '小时', true) }}
              </div>
            </div>

            <div class="text-center">
              <div class="text-sm text-gray-500 mb-1">材料成本 (元)</div>
              <div class="flex items-center justify-center space-x-2">
                <span class="text-lg font-medium text-gray-600">{{ comparisonData.materialCost.before.toLocaleString() }}</span>
                <ArrowRight class="h-4 w-4 text-gray-400" />
                <span class="text-lg font-bold text-green-600">{{ comparisonData.materialCost.after.toLocaleString() }}</span>
              </div>
              <div class="text-xs text-green-600 mt-1">
                {{ formatImprovement(comparisonData.materialCost.after - comparisonData.materialCost.before, '元', true) }}
              </div>
            </div>

            <div class="text-center">
              <div class="text-sm text-gray-500 mb-1">平均利用率 (%)</div>
              <div class="flex items-center justify-center space-x-2">
                <span class="text-lg font-medium text-gray-600">{{ comparisonData.utilizationRate.before }}%</span>
                <ArrowRight class="h-4 w-4 text-gray-400" />
                <span class="text-lg font-bold text-green-600">{{ comparisonData.utilizationRate.after }}%</span>
              </div>
              <div class="text-xs text-green-600 mt-1">
                {{ formatImprovement(comparisonData.utilizationRate.after - comparisonData.utilizationRate.before, '%') }}
              </div>
            </div>

            <div class="text-center">
              <div class="text-sm text-gray-500 mb-1">切割任务数</div>
              <div class="flex items-center justify-center space-x-2">
                <span class="text-lg font-medium text-gray-600">{{ comparisonData.taskCount.cutting.before }}</span>
                <ArrowRight class="h-4 w-4 text-gray-400" />
                <span class="text-lg font-bold text-green-600">{{ comparisonData.taskCount.cutting.after }}</span>
              </div>
              <div class="text-xs text-green-600 mt-1">
                {{ formatImprovement(comparisonData.taskCount.cutting.after - comparisonData.taskCount.cutting.before, '个', true) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
        <BarChart3 class="h-12 w-12 text-gray-300 mx-auto mb-3" />
        <div class="text-lg font-medium text-gray-900">等待切割优化结果</div>
        <div class="text-sm text-gray-500">导入切割优化结果后将显示对比分析</div>
      </div>
    </div>

    <!-- 最终指标 -->
    <div class="space-y-4">
      <div class="text-lg font-medium text-gray-900">最终排产指标</div>

      <div class="grid grid-cols-2 gap-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="text-sm text-blue-600 mb-1">交期达成率</div>
          <div class="text-2xl font-bold text-blue-900">96%</div>
          <div class="text-xs text-blue-600 mt-1">较预估提升 1%</div>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="text-sm text-green-600 mb-1">原片利用率</div>
          <div class="text-2xl font-bold text-green-900">89%</div>
          <div class="text-xs text-green-600 mt-1">较预估提升 4%</div>
        </div>

        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div class="text-sm text-orange-600 mb-1">设备利用率</div>
          <div class="text-2xl font-bold text-orange-900">84%</div>
          <div class="text-xs text-orange-600 mt-1">较预估提升 6%</div>
        </div>

        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div class="text-sm text-purple-600 mb-1">总工期</div>
          <div class="text-2xl font-bold text-purple-900">5.1天</div>
          <div class="text-xs text-purple-600 mt-1">较预估缩短 0.4天</div>
        </div>
      </div>
    </div>

    <!-- 确认操作 -->
    <div class="space-y-4">
      <div class="text-lg font-medium text-gray-900">确认操作</div>

      <div class="bg-gray-50 border rounded-lg p-4">
        <div class="flex items-center space-x-3 mb-4">
          <CheckCircle class="h-5 w-5 text-green-600" />
          <div class="text-sm font-medium text-gray-900">排产方案已优化完成</div>
        </div>

        <div class="text-sm text-gray-600 mb-4">
          基于第三方切割优化结果，系统已更新排产计划。请确认最终方案并下发生产执行。
        </div>

        <div class="flex items-center space-x-3">
          <Button variant="outline">
            <Download class="h-4 w-4 mr-2" />
            导出计划
          </Button>
          <Button variant="outline">
            <Eye class="h-4 w-4 mr-2" />
            预览详情
          </Button>
          <Button @click="$emit('confirm-schedule')" class="bg-green-600 hover:bg-green-700">
            <Check class="h-4 w-4 mr-2" />
            确认并下发
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckCircle, ArrowRight, BarChart3, Download, Eye, Check } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';

interface Props {
  comparisonData: any;
}

interface Emits {
  (e: 'confirm-schedule'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formatImprovement = (value: number, unit: string, lowerIsBetter = false) => {
  if (value === 0) return `无变化`;
  
  const isImprovement = lowerIsBetter ? value < 0 : value > 0;
  const prefix = value > 0 ? '+' : '';
  const formattedValue = value.toFixed(2).replace(/\.00$/, '');

  if (isImprovement) {
    return `优化 ${prefix}${formattedValue}${unit}`;
  } else {
    return `变差 ${prefix}${formattedValue}${unit}`;
  }
};
</script>
