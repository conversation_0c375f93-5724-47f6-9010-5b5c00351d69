<template>
  <div class="h-full flex flex-col min-h-0">
    <!-- 向导头部 -->
    <div class="flex-shrink-0 p-6 border-b">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">排产向导</h2>
          <p class="text-sm text-gray-500">选择批次并配置排产参数，开始智能排产</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div
              class="flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all"
              :class="currentStep >= 1 ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-400'"
            >
              <Package class="w-4 h-4" />
            </div>
            <span class="text-sm font-medium" :class="currentStep >= 1 ? 'text-blue-600' : 'text-gray-400'">
              批次选择
            </span>
          </div>

          <div class="w-8 h-0.5" :class="currentStep >= 2 ? 'bg-blue-500' : 'bg-gray-300'"></div>

          <div class="flex items-center space-x-2">
            <div
              class="flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all"
              :class="currentStep >= 2 ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-400'"
            >
              <Settings class="w-4 h-4" />
            </div>
            <span class="text-sm font-medium" :class="currentStep >= 2 ? 'text-blue-600' : 'text-gray-400'">
              排产配置
            </span>
          </div>
        </div>
      </div>

      <!-- Tab 导航 -->
      <div class="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          @click="currentStep = 1"
          class="flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all"
          :class="currentStep === 1
            ? 'bg-white text-blue-600 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'"
        >
          <Package class="w-4 h-4" />
          <span>批次选择</span>
          <Badge v-if="selectedBatchIds.length > 0" variant="secondary" class="ml-1">
            {{ selectedBatchIds.length }}
          </Badge>
        </button>

        <button
          @click="currentStep = 2"
          :disabled="selectedBatchIds.length === 0"
          class="flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all"
          :class="currentStep === 2
            ? 'bg-white text-blue-600 shadow-sm'
            : selectedBatchIds.length === 0
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-600 hover:text-gray-900'"
        >
          <Settings class="w-4 h-4" />
          <span>排产配置</span>
        </button>
      </div>
    </div>
    
    <!-- 向导内容区域 -->
    <div class="flex-1 min-h-0 overflow-hidden">
      <!-- 步骤1: 批次选择 -->
      <div v-if="currentStep === 1" class="h-full overflow-hidden">
        <BatchPoolDialog
          :batches="batches"
          :loading="loading"
          :initial-selected-ids="selectedBatchIds"
          @batch-select="handleBatchSelect"
          :show-footer="false"
        />
      </div>

      <!-- 步骤2: 排产配置 -->
      <div v-if="currentStep === 2" class="h-full flex flex-col">
        <div v-if="selectedBatchIds.length === 0" class="flex-1 flex items-center justify-center">
          <div class="text-center">
            <Package class="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <div class="text-lg font-medium text-gray-900">请先选择批次</div>
            <div class="text-sm text-gray-500 mb-4">返回第一步选择要排产的批次</div>
            <Button @click="currentStep = 1" variant="outline">
              <ArrowLeft class="h-4 w-4 mr-2" />
              返回批次选择
            </Button>
          </div>
        </div>

        <div v-else class="flex-1 overflow-y-auto p-6">
          <PreSchedulingControls
            :selected-batches="selectedBatches"
            :show-start-button="false"
          />
        </div>
      </div>
    </div>
    
    <!-- 向导底部操作栏 -->
    <div class="flex-shrink-0 p-6 border-t bg-gray-50">
      <div class="flex items-center justify-between">
        <!-- 左侧：步骤导航 -->
        <div class="flex items-center space-x-3">
          <Button
            v-if="currentStep > 1"
            variant="outline"
            @click="currentStep = Math.max(1, currentStep - 1)"
          >
            <ArrowLeft class="h-4 w-4 mr-2" />
            上一步
          </Button>

          <Button
            v-if="currentStep < 2 && selectedBatchIds.length > 0"
            @click="currentStep = Math.min(2, currentStep + 1)"
          >
            下一步
            <ArrowRight class="h-4 w-4 ml-2" />
          </Button>
        </div>

        <!-- 中间：选中批次统计 -->
        <div v-if="selectedBatchIds.length > 0" class="text-sm text-gray-600 flex-shrink-0">
          已选择 <span class="font-medium text-blue-600">{{ selectedBatchIds.length }}</span> 个批次，
          共 <span class="font-medium text-blue-600">{{ totalQuantity }}</span> 片，
          预计 <span class="font-medium text-blue-600">{{ totalHours.toFixed(1) }}</span> 小时
        </div>

        <!-- 右侧：主要操作 -->
        <div class="flex items-center space-x-3 flex-shrink-0">
          <Button
            variant="outline"
            @click="$emit('close')"
          >
            取消
          </Button>

          <Button
            @click="handleStartScheduling"
            :disabled="selectedBatchIds.length === 0 || isScheduling"
            class="min-w-[120px]"
          >
            <div v-if="isScheduling" class="flex items-center space-x-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>计算中...</span>
            </div>
            <div v-else class="flex items-center space-x-2">
              <Play class="h-4 w-4" />
              <span>开始排产</span>
            </div>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Package, Settings, ArrowLeft, ArrowRight, Play } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import BatchPoolDialog from './BatchPoolDialog.vue';
import PreSchedulingControls from './PreSchedulingControls.vue';
import type { OptimizedBatch } from '@/types/production-order-creation';

interface Props {
  batches: OptimizedBatch[];
  loading?: boolean;
  initialSelectedIds?: string[];
  isScheduling?: boolean;
}

interface Emits {
  (e: 'batch-select', batchIds: string[]): void;
  (e: 'start-scheduling'): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  initialSelectedIds: () => [],
  isScheduling: false
});

const emit = defineEmits<Emits>();

// 响应式状态
const currentStep = ref(1);
const selectedBatchIds = ref<string[]>([...props.initialSelectedIds]);

// 计算属性
const selectedBatches = computed(() => {
  return props.batches.filter(batch => selectedBatchIds.value.includes(batch.id));
});

const totalQuantity = computed(() => {
  return selectedBatches.value.reduce((sum, batch) => sum + batch.totalQuantity, 0);
});

const totalHours = computed(() => {
  return selectedBatches.value.reduce((sum, batch) => {
    const duration = (batch as any).estimatedDuration || batch.estimatedTime || (batch.items.length * 60);
    return sum + (duration / 60);
  }, 0);
});

// 事件处理
const handleBatchSelect = (batchIds: string[]) => {
  selectedBatchIds.value = batchIds;
  emit('batch-select', batchIds);
};

const handleStartScheduling = () => {
  emit('start-scheduling');
};

// 监听选中批次变化，自动切换到第二步
watch(() => selectedBatchIds.value.length, (newLength, oldLength) => {
  if (oldLength === 0 && newLength > 0 && currentStep.value === 1) {
    // 延迟切换，给用户一点时间看到选择结果
    setTimeout(() => {
      if (selectedBatchIds.value.length > 0) {
        currentStep.value = 2;
      }
    }, 800);
  }
});

// 监听外部 isScheduling 状态变化
watch(() => props.isScheduling, (isScheduling) => {
  if (!isScheduling) {
    // 排产完成后关闭向导
    setTimeout(() => {
      emit('close');
    }, 1000);
  }
});
</script>
