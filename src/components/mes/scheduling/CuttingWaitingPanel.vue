<template>
  <div class="space-y-6">
    <!-- 标题和描述 -->
    <div class="text-center">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">等待第三方系统处理</h3>
      <p class="text-sm text-gray-600">切割优化计算正在进行中，请耐心等待</p>
    </div>

    <!-- 处理状态卡片 -->
    <div class="border border-blue-200 bg-blue-50 rounded-lg p-6">
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex-shrink-0">
          <div class="relative">
            <Clock class="h-12 w-12 text-blue-500" />
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
          </div>
        </div>
        <div class="flex-1">
          <h4 class="text-lg font-medium text-blue-900 mb-1">切割优化进行中</h4>
          <p class="text-sm text-blue-700">
            第三方系统正在分析您的切割需求并计算最优方案
          </p>
        </div>
        <div class="text-right">
          <div class="text-2xl font-bold text-blue-600">{{ formatTime(elapsedTime) }}</div>
          <div class="text-xs text-blue-600">已用时间</div>
        </div>
      </div>

      <!-- 进度指示器 -->
      <div class="space-y-4">
        <div class="flex items-center justify-between text-sm">
          <span class="text-blue-700">处理进度</span>
          <span class="text-blue-600">预计还需 {{ formatTime(remainingTime) }}</span>
        </div>
        <div class="w-full bg-blue-200 rounded-full h-3">
          <div 
            class="bg-blue-500 h-3 rounded-full transition-all duration-1000 ease-out"
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        <div class="text-xs text-blue-600 text-center">
          {{ progressPercentage }}% 完成
        </div>
      </div>
    </div>

    <!-- 导出数据摘要 -->
    <div v-if="exportData" class="bg-white border rounded-lg p-4">
      <h5 class="text-sm font-medium text-gray-900 mb-3">处理数据摘要</h5>
      <div class="grid grid-cols-2 gap-4">
        <div class="space-y-2">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">导出时间:</span>
            <span class="font-medium">{{ formatDateTime(exportData.exportTime) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">批次数量:</span>
            <span class="font-medium">{{ exportData.batchCount }} 个</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">切割需求:</span>
            <span class="font-medium">{{ exportData.batches.length }} 项</span>
          </div>
        </div>
        <div class="space-y-2">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">可用原片:</span>
            <span class="font-medium">{{ exportData.availableMaterials.length }} 种</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">数据格式:</span>
            <span class="font-medium">{{ exportData.metadata.format.toUpperCase() }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">文件版本:</span>
            <span class="font-medium">v{{ exportData.metadata.version }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理阶段指示器 -->
    <div class="bg-gray-50 rounded-lg p-4">
      <h5 class="text-sm font-medium text-gray-900 mb-3">处理阶段</h5>
      <div class="space-y-3">
        <div v-for="(stage, index) in processingStages" :key="stage.id" class="flex items-center space-x-3">
          <div 
            class="flex items-center justify-center w-6 h-6 rounded-full border-2 transition-all duration-300"
            :class="getStageClass(stage.id, index)"
          >
            <component 
              :is="stage.icon" 
              class="w-3 h-3"
              :class="getStageIconClass(stage.id, index)"
            />
          </div>
          <div class="flex-1">
            <div class="text-sm font-medium" :class="getStageTextClass(stage.id, index)">
              {{ stage.title }}
            </div>
            <div class="text-xs text-gray-500">{{ stage.description }}</div>
          </div>
          <div v-if="stage.id === currentProcessingStage" class="text-xs text-blue-600 font-medium">
            进行中...
          </div>
          <div v-else-if="isStageCompleted(index)" class="text-xs text-green-600 font-medium">
            已完成
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <Button @click="$emit('check-status')" variant="outline" size="sm">
          <RefreshCw class="h-4 w-4 mr-2" />
          检查状态
        </Button>
        <Button variant="outline" size="sm">
          <HelpCircle class="h-4 w-4 mr-2" />
          联系支持
        </Button>
      </div>
      <Button @click="$emit('cancel-optimization')" variant="outline" size="sm">
        <X class="h-4 w-4 mr-2" />
        取消优化
      </Button>
    </div>

    <!-- 提示信息 -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-start space-x-3">
        <Info class="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
        <div class="text-sm text-yellow-800">
          <p class="font-medium mb-1">温馨提示</p>
          <ul class="space-y-1 text-xs">
            <li>• 优化计算通常需要 20-60 分钟，具体时间取决于数据复杂度</li>
            <li>• 您可以关闭此页面，系统会在优化完成后发送通知</li>
            <li>• 如果超过预期时间，请联系技术支持检查处理状态</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { 
  Clock, RefreshCw, HelpCircle, X, Info,
  FileText, Calculator, CheckCircle, Download
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import type { CuttingExportData } from '@/types/scheduling';

interface Props {
  exportData?: CuttingExportData;
  estimatedTime: number; // 预估时间（分钟）
}

interface Emits {
  (e: 'check-status'): void;
  (e: 'cancel-optimization'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 时间相关状态
const elapsedTime = ref(0); // 已用时间（秒）
const startTime = ref(Date.now());

// 处理阶段定义
const processingStages = [
  {
    id: 'parsing',
    title: '数据解析',
    description: '解析导入的切割需求数据',
    icon: FileText
  },
  {
    id: 'calculating',
    title: '算法计算',
    description: '执行切割优化算法',
    icon: Calculator
  },
  {
    id: 'optimizing',
    title: '方案优化',
    description: '优化切割布局和顺序',
    icon: CheckCircle
  },
  {
    id: 'generating',
    title: '结果生成',
    description: '生成优化结果文件',
    icon: Download
  }
];

// 当前处理阶段
const currentProcessingStage = ref('parsing');

// 计算属性
const remainingTime = computed(() => {
  const estimatedSeconds = props.estimatedTime * 60;
  const remaining = Math.max(0, estimatedSeconds - elapsedTime.value);
  return remaining;
});

const progressPercentage = computed(() => {
  const estimatedSeconds = props.estimatedTime * 60;
  const progress = Math.min(100, (elapsedTime.value / estimatedSeconds) * 100);
  return Math.round(progress);
});

// 样式计算函数
const getStageClass = (stageId: string, index: number) => {
  const currentIndex = processingStages.findIndex(s => s.id === currentProcessingStage.value);
  
  if (index < currentIndex) {
    return 'border-green-500 bg-green-500';
  } else if (index === currentIndex) {
    return 'border-blue-500 bg-blue-500';
  } else {
    return 'border-gray-300 bg-white';
  }
};

const getStageIconClass = (stageId: string, index: number) => {
  const currentIndex = processingStages.findIndex(s => s.id === currentProcessingStage.value);
  
  if (index <= currentIndex) {
    return 'text-white';
  } else {
    return 'text-gray-400';
  }
};

const getStageTextClass = (stageId: string, index: number) => {
  const currentIndex = processingStages.findIndex(s => s.id === currentProcessingStage.value);
  
  if (index === currentIndex) {
    return 'text-blue-600';
  } else if (index < currentIndex) {
    return 'text-green-600';
  } else {
    return 'text-gray-500';
  }
};

const isStageCompleted = (index: number) => {
  const currentIndex = processingStages.findIndex(s => s.id === currentProcessingStage.value);
  return index < currentIndex;
};

// 工具函数
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 定时器
let timer: number | null = null;

onMounted(() => {
  // 启动计时器
  timer = setInterval(() => {
    elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000);
    
    // 模拟处理阶段推进
    const progress = progressPercentage.value;
    if (progress > 75) {
      currentProcessingStage.value = 'generating';
    } else if (progress > 50) {
      currentProcessingStage.value = 'optimizing';
    } else if (progress > 25) {
      currentProcessingStage.value = 'calculating';
    } else {
      currentProcessingStage.value = 'parsing';
    }
  }, 1000);
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>
