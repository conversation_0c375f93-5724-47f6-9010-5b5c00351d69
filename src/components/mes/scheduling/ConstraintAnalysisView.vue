<template>
  <div class="space-y-4">
    <div class="text-lg font-medium text-gray-900">约束条件分析</div>
    
    <div v-if="constraints.length === 0" class="text-center py-8">
      <CheckCircle class="h-12 w-12 text-green-500 mx-auto mb-3" />
      <div class="text-lg font-medium text-gray-900">无约束冲突</div>
      <div class="text-sm text-gray-500">当前排产方案未发现约束条件冲突</div>
    </div>
    
    <div v-else class="space-y-3">
      <div
        v-for="constraint in constraints"
        :key="constraint.description"
        class="border rounded-lg p-4"
        :class="getConstraintBorderClass(constraint.severity)"
      >
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0 mt-0.5">
            <component 
              :is="getConstraintIcon(constraint.type)"
              class="h-5 w-5"
              :class="getConstraintIconClass(constraint.severity)"
            />
          </div>
          
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-2">
              <div class="font-medium text-gray-900">{{ getConstraintTypeLabel(constraint.type) }}</div>
              <Badge :variant="getConstraintSeverityVariant(constraint.severity)">
                {{ getConstraintSeverityLabel(constraint.severity) }}
              </Badge>
            </div>
            
            <div class="text-sm text-gray-600 mb-3">{{ constraint.description }}</div>
            
            <div class="text-xs text-gray-500">
              影响批次: {{ constraint.affectedBatches.length }} 个
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckCircle, AlertTriangle, Settings, Package, Clock } from 'lucide-vue-next';
import { Badge } from '@/components/ui/badge';
import type { SchedulingConstraint, ScheduledBatch } from '@/types/scheduling';

interface Props {
  constraints: SchedulingConstraint[];
  batches: ScheduledBatch[];
}

const props = defineProps<Props>();

const getConstraintIcon = (type: string) => {
  const icons = {
    equipment: Settings,
    material: Package,
    delivery: Clock,
    capacity: AlertTriangle
  };
  
  return icons[type as keyof typeof icons] || AlertTriangle;
};

const getConstraintIconClass = (severity: string) => {
  const classes = {
    low: 'text-yellow-500',
    medium: 'text-orange-500',
    high: 'text-red-500'
  };
  
  return classes[severity as keyof typeof classes] || classes.medium;
};

const getConstraintBorderClass = (severity: string) => {
  const classes = {
    low: 'border-yellow-200 bg-yellow-50',
    medium: 'border-orange-200 bg-orange-50',
    high: 'border-red-200 bg-red-50'
  };
  
  return classes[severity as keyof typeof classes] || classes.medium;
};

const getConstraintTypeLabel = (type: string) => {
  const labels = {
    equipment: '设备约束',
    material: '物料约束',
    delivery: '交期约束',
    capacity: '产能约束'
  };
  
  return labels[type as keyof typeof labels] || type;
};

const getConstraintSeverityLabel = (severity: string) => {
  const labels = {
    low: '低',
    medium: '中',
    high: '高'
  };
  
  return labels[severity as keyof typeof labels] || severity;
};

const getConstraintSeverityVariant = (severity: string): "default" | "outline" | "destructive" | "secondary" => {
  const variants: Record<string, "default" | "outline" | "destructive" | "secondary"> = {
    low: 'secondary',
    medium: 'default',
    high: 'destructive'
  };

  return variants[severity] || 'default';
};
</script>
