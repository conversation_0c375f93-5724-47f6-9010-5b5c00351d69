<script setup lang="ts">
const props = defineProps<{ traditional: number; optimized: number }>()
</script>

<template>
  <div class="border rounded p-3">
    <div class="font-medium mb-2">效率指标对比</div>
    <div>传统平均设备利用率：{{ props.traditional }}%</div>
    <div>智能平均设备利用率：{{ props.optimized }}%</div>
    <div class="font-bold text-green-600">提升：{{ (props.optimized - props.traditional).toFixed(2) }}%</div>
  </div>
</template>

