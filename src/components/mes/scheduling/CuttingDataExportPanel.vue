<template>
  <div class="space-y-6">
    <!-- 标题和描述 -->
    <div class="text-center">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">数据导出</h3>
      <p class="text-sm text-gray-600">将预排产数据导出为第三方切割优化系统可识别的格式</p>
    </div>

    <!-- 导出状态显示 -->
    <div v-if="exportStatus === 'idle'" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
      <Download class="h-12 w-12 text-gray-300 mx-auto mb-3" />
      <div class="text-lg font-medium text-gray-900 mb-2">准备导出切割数据</div>
      <div class="text-sm text-gray-500 mb-4">
        系统将自动整理排产数据，生成标准格式的切割需求文件
      </div>
      <Button @click="$emit('export-data')" size="lg" class="px-8">
        <Download class="h-4 w-4 mr-2" />
        开始导出
      </Button>
    </div>

    <!-- 导出进行中 -->
    <div v-else-if="exportStatus === 'exporting'" class="text-center py-8 border border-blue-200 bg-blue-50 rounded-lg">
      <div class="flex items-center justify-center mb-4">
        <RefreshCw class="h-8 w-8 text-blue-500 animate-spin" />
      </div>
      <div class="text-lg font-medium text-blue-900 mb-2">正在导出数据...</div>
      <div class="text-sm text-blue-700">
        正在整理批次信息、物料规格和约束条件
      </div>
      <div class="mt-4 w-64 mx-auto">
        <div class="w-full bg-blue-200 rounded-full h-2">
          <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" :style="{ width: `${exportProgress}%` }"></div>
        </div>
        <div class="text-xs text-blue-600 mt-1">{{ exportProgress }}%</div>
      </div>
    </div>

    <!-- 导出完成 -->
    <div v-else-if="exportStatus === 'exported' && exportData" class="border border-green-200 bg-green-50 rounded-lg p-6">
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
          <CheckCircle class="h-8 w-8 text-green-600" />
        </div>
        <div class="flex-1">
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-lg font-medium text-green-900">数据导出完成</h4>
            <Badge variant="outline" class="text-green-700 border-green-300">
              {{ exportData.batchCount }} 个批次
            </Badge>
          </div>
          
          <!-- 导出信息 -->
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="space-y-2">
              <div class="text-sm">
                <span class="text-gray-600">导出时间:</span>
                <span class="ml-2 font-medium">{{ formatDateTime(exportData.exportTime) }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-600">数据格式:</span>
                <span class="ml-2 font-medium">Excel + JSON</span>
              </div>
            </div>
            <div class="space-y-2">
              <div class="text-sm">
                <span class="text-gray-600">文件大小:</span>
                <span class="ml-2 font-medium">{{ formatFileSize(estimatedFileSize) }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-600">校验码:</span>
                <span class="ml-2 font-mono text-xs">{{ exportData.metadata.checksum }}</span>
              </div>
            </div>
          </div>

          <!-- 导出内容摘要 -->
          <div class="bg-white rounded border p-4 mb-4">
            <h5 class="text-sm font-medium text-gray-900 mb-3">导出内容摘要</h5>
            <div class="grid grid-cols-3 gap-4 text-sm">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ exportData.batches.length }}</div>
                <div class="text-gray-600">切割需求</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ exportData.availableMaterials.length }}</div>
                <div class="text-gray-600">可用原片</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-orange-600">{{ Object.keys(exportData.constraints).length }}</div>
                <div class="text-gray-600">约束条件</div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center space-x-3">
            <Button @click="$emit('download-file')" variant="outline" size="sm">
              <Download class="h-4 w-4 mr-2" />
              下载文件
            </Button>
            <Button @click="$emit('export-data')" variant="outline" size="sm">
              <RefreshCw class="h-4 w-4 mr-2" />
              重新导出
            </Button>
            <Button @click="copyToClipboard" variant="outline" size="sm">
              <Copy class="h-4 w-4 mr-2" />
              复制路径
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出错误 -->
    <div v-else-if="exportStatus === 'error'" class="border border-red-200 bg-red-50 rounded-lg p-6">
      <div class="flex items-start space-x-4">
        <AlertTriangle class="h-8 w-8 text-red-600 flex-shrink-0" />
        <div class="flex-1">
          <h4 class="text-lg font-medium text-red-900 mb-2">导出失败</h4>
          <p class="text-sm text-red-700 mb-4">
            数据导出过程中出现错误，请检查数据完整性后重试
          </p>
          <div class="flex items-center space-x-3">
            <Button @click="$emit('export-data')" variant="outline" size="sm">
              <RefreshCw class="h-4 w-4 mr-2" />
              重试导出
            </Button>
            <Button variant="outline" size="sm">
              <HelpCircle class="h-4 w-4 mr-2" />
              获取帮助
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作说明 -->
    <div class="bg-gray-50 rounded-lg p-4">
      <h5 class="text-sm font-medium text-gray-900 mb-2">操作说明</h5>
      <ol class="text-sm text-gray-600 space-y-1 list-decimal list-inside">
        <li>点击"开始导出"按钮，系统将自动整理排产数据</li>
        <li>导出完成后，下载生成的Excel和JSON格式文件</li>
        <li>将文件发送给第三方切割优化系统进行处理</li>
        <li>等待第三方系统完成优化计算并返回结果文件</li>
      </ol>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Download, CheckCircle, RefreshCw, AlertTriangle, 
  Copy, HelpCircle 
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { CuttingExportData } from '@/types/scheduling';

interface Props {
  exportStatus: 'idle' | 'exporting' | 'exported' | 'error';
  exportData?: CuttingExportData;
}

interface Emits {
  (e: 'export-data'): void;
  (e: 'download-file'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 导出进度
const exportProgress = ref(0);

// 预估文件大小
const estimatedFileSize = computed(() => {
  if (!props.exportData) return 0;
  // 简单估算：每个批次约1KB，基础数据约10KB
  return (props.exportData.batchCount * 1024) + (10 * 1024);
});

// 工具函数
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN');
};

const formatFileSize = (bytes: number) => {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
};

const copyToClipboard = async () => {
  if (props.exportData) {
    const path = `/exports/${props.exportData.exportId}`;
    await navigator.clipboard.writeText(path);
    // 这里可以添加toast提示
  }
};

// 模拟导出进度
if (props.exportStatus === 'exporting') {
  const interval = setInterval(() => {
    exportProgress.value += 10;
    if (exportProgress.value >= 100) {
      clearInterval(interval);
    }
  }, 200);
}
</script>
