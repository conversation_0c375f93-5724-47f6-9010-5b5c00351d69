<template>
  <div 
    class="border rounded-lg p-3 transition-all duration-200 cursor-pointer hover:shadow-md"
    :class="[
      selected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white hover:border-gray-300',
      getPriorityBorderClass()
    ]"
    @click="$emit('toggle-select', batch.id)"
  >
    <!-- 批次头部 -->
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center space-x-2">
        <!-- 选择框 -->
        <div 
          class="w-4 h-4 border-2 rounded flex items-center justify-center"
          :class="selected ? 'border-blue-500 bg-blue-500' : 'border-gray-300'"
        >
          <Check v-if="selected" class="w-3 h-3 text-white" />
        </div>
        
        <!-- 批次名称 -->
        <div class="font-medium text-gray-900">{{ batch.name }}</div>
        
        <!-- 优先级标签 -->
        <Badge :variant="getPriorityVariant()" class="text-xs">
          {{ getPriorityLabel() }}
        </Badge>
      </div>
      
      <!-- 更多操作 -->
      <Button
        variant="ghost"
        size="sm"
        @click.stop="$emit('view-details', batch)"
        class="h-6 w-6 p-0"
      >
        <MoreHorizontal class="h-3 w-3" />
      </Button>
    </div>
    
    <!-- 批次信息 -->
    <div class="space-y-2">
      <!-- 规格信息 -->
      <div class="flex items-center space-x-4 text-sm text-gray-600">
        <div class="flex items-center space-x-1">
          <Ruler class="h-3 w-3" />
          <span>{{ getSpecificationText() }}</span>
        </div>
        <div class="flex items-center space-x-1">
          <Package class="h-3 w-3" />
          <span>{{ batch.totalQuantity }} 片</span>
        </div>
      </div>
      
      <!-- 工艺信息 -->
      <div class="flex items-center space-x-2 text-sm text-gray-600">
        <Settings class="h-3 w-3" />
        <div class="flex flex-wrap gap-1">
          <span
            v-for="workstation in getWorkstationList().slice(0, 3)"
            :key="workstation"
            class="px-1.5 py-0.5 bg-gray-100 rounded text-xs"
          >
            {{ getWorkstationName(workstation) }}
          </span>
          <span
            v-if="getWorkstationList().length > 3"
            class="px-1.5 py-0.5 bg-gray-100 rounded text-xs"
          >
            +{{ getWorkstationList().length - 3 }}
          </span>
        </div>
      </div>
      
      <!-- 客户信息 -->
      <div class="flex items-center space-x-2 text-sm text-gray-600">
        <Users class="h-3 w-3" />
        <span>{{ getCustomerText() }}</span>
      </div>
      
      <!-- 时间和利用率 -->
      <div class="flex items-center justify-between text-sm">
        <div class="flex items-center space-x-1 text-gray-600">
          <Clock class="h-3 w-3" />
          <span>{{ formatDuration(getEstimatedDuration()) }}</span>
        </div>
        
        <div class="flex items-center space-x-2">
          <div class="text-xs text-gray-500">利用率</div>
          <div class="flex items-center space-x-1">
            <div class="w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden">
              <div 
                class="h-full transition-all duration-300"
                :class="getUtilizationColorClass()"
                :style="{ width: `${batch.utilization}%` }"
              />
            </div>
            <span class="text-xs font-medium" :class="getUtilizationTextClass()">
              {{ batch.utilization.toFixed(0) }}%
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 状态指示器 -->
    <div class="mt-2 flex items-center justify-between">
      <div class="flex items-center space-x-1">
        <div 
          class="w-2 h-2 rounded-full"
          :class="getStatusIndicatorClass()"
        />
        <span class="text-xs text-gray-500">{{ getStatusText() }}</span>
      </div>
      
      <!-- 交期提醒 -->
      <div v-if="isUrgentDelivery()" class="flex items-center space-x-1 text-red-600">
        <AlertTriangle class="h-3 w-3" />
        <span class="text-xs">紧急交期</span>
      </div>
    </div>

    <!-- 外键关联信息 -->
    <div v-if="batch.sourceWorkOrderNumber || batch.sourceCustomerOrderNumber"
         class="mt-2 pt-2 border-t border-gray-100">
      <div class="text-xs text-gray-500 space-y-1">
        <div v-if="batch.sourceWorkOrderNumber" class="flex items-center justify-between">
          <span>工单号:</span>
          <span class="text-blue-600 font-mono">{{ batch.sourceWorkOrderNumber }}</span>
        </div>
        <div v-if="batch.sourceCustomerOrderNumber" class="flex items-center justify-between">
          <span>客户订单:</span>
          <span class="text-green-600 font-mono text-xs">{{ batch.sourceCustomerOrderNumber }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  Check, 
  MoreHorizontal, 
  Ruler, 
  Package, 
  Settings, 
  Users, 
  Clock, 
  AlertTriangle 
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { OptimizedBatch } from '@/types/production-order-creation';

interface Props {
  batch: OptimizedBatch;
  selected: boolean;
}

interface Emits {
  (e: 'toggle-select', batchId: string): void;
  (e: 'view-details', batch: OptimizedBatch): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性
const getPriorityVariant = () => {
  switch (props.batch.priority) {
    case 'urgent': return 'destructive';
    case 'high': return 'default';
    case 'normal': return 'secondary';
    case 'low': return 'outline';
    default: return 'secondary';
  }
};

const getPriorityLabel = () => {
  switch (props.batch.priority) {
    case 'urgent': return '紧急';
    case 'high': return '高';
    case 'normal': return '普通';
    case 'low': return '低';
    default: return '普通';
  }
};

const getPriorityBorderClass = () => {
  if (!props.selected) return '';
  
  switch (props.batch.priority) {
    case 'urgent': return 'border-l-4 border-l-red-500';
    case 'high': return 'border-l-4 border-l-orange-500';
    case 'normal': return 'border-l-4 border-l-blue-500';
    case 'low': return 'border-l-4 border-l-gray-500';
    default: return 'border-l-4 border-l-blue-500';
  }
};

const getSpecificationText = () => {
  if (props.batch.items.length === 0) return '无规格信息';
  
  const firstItem = props.batch.items[0];
  const specs = firstItem.specifications;
  
  return `${specs.thickness}mm ${specs.glassType || '玻璃'} ${specs.length}×${specs.width}`;
};

const getCustomerText = () => {
  const customers = [...new Set(props.batch.items.map(item => item.customerName))];
  
  if (customers.length === 1) {
    return customers[0];
  } else if (customers.length <= 3) {
    return customers.join(', ');
  } else {
    return `${customers.slice(0, 2).join(', ')} 等${customers.length}家`;
  }
};

const getWorkstationList = () => {
  // 从 batch.workstation 或其他属性获取工作站列表
  if (props.batch.workstation) {
    return [props.batch.workstation];
  }
  // 如果有 workstations 属性，使用它
  if ((props.batch as any).workstations) {
    return (props.batch as any).workstations;
  }
  // 默认返回基于工艺流程的工作站
  return props.batch.processFlow?.map(step => step.workstation) || ['cutting'];
};

const getEstimatedDuration = () => {
  // 从 estimatedTime 或其他属性获取预估时间
  if ((props.batch as any).estimatedDuration) {
    return (props.batch as any).estimatedDuration;
  }
  if (props.batch.estimatedTime) {
    return props.batch.estimatedTime;
  }
  // 默认估算时间
  return props.batch.items.length * 60; // 每个项目1小时
};

const getWorkstationName = (workstation: string) => {
  const workstationNames: Record<string, string> = {
    'cutting': '切割',
    'edging': '磨边',
    'tempering': '钢化',
    'laminating': '夹胶',
    'insulating': '中空',
    'quality': '质检',
    'packaging': '包装'
  };

  return workstationNames[workstation] || workstation;
};

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  
  if (hours > 0) {
    return `${hours}h${mins > 0 ? ` ${mins}m` : ''}`;
  } else {
    return `${mins}m`;
  }
};

const getUtilizationColorClass = () => {
  if (props.batch.utilization >= 85) return 'bg-green-500';
  if (props.batch.utilization >= 70) return 'bg-yellow-500';
  return 'bg-red-500';
};

const getUtilizationTextClass = () => {
  if (props.batch.utilization >= 85) return 'text-green-600';
  if (props.batch.utilization >= 70) return 'text-yellow-600';
  return 'text-red-600';
};

const getStatusIndicatorClass = () => {
  if (props.batch.utilization >= 80) return 'bg-green-500';
  if (props.batch.utilization >= 60) return 'bg-yellow-500';
  return 'bg-red-500';
};

const getStatusText = () => {
  if (props.batch.utilization >= 80) return '就绪';
  if (props.batch.utilization >= 60) return '等待';
  return '阻塞';
};

const isUrgentDelivery = () => {
  return props.batch.priority === 'urgent' || 
         props.batch.items.some(item => {
           if (!item.deliveryDate) return false;
           const deliveryDate = new Date(item.deliveryDate);
           const daysUntilDelivery = (deliveryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24);
           return daysUntilDelivery <= 3;
         });
};
</script>
