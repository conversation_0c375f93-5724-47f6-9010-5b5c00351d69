<template>
  <div class="space-y-6">
    <!-- 完成状态标题 -->
    <div class="text-center">
      <div class="flex items-center justify-center mb-4">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle class="h-10 w-10 text-green-600" />
        </div>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">切割优化完成</h3>
      <p class="text-sm text-gray-600">
        第三方系统优化已完成，结果已通过验证，可以进入最终确认阶段
      </p>
    </div>

    <!-- 优化成果总览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <TrendingUp class="h-6 w-6 text-white" />
          </div>
          <div>
            <div class="text-sm text-blue-700">利用率提升</div>
            <div class="text-xl font-bold text-blue-900">
              +{{ improvements?.utilizationImproved || 0 }}%
            </div>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
            <DollarSign class="h-6 w-6 text-white" />
          </div>
          <div>
            <div class="text-sm text-green-700">成本节约</div>
            <div class="text-xl font-bold text-green-900">
              ¥{{ formatCurrency(improvements?.costSaved || 0) }}
            </div>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
            <Clock class="h-6 w-6 text-white" />
          </div>
          <div>
            <div class="text-sm text-purple-700">时间节约</div>
            <div class="text-xl font-bold text-purple-900">
              {{ improvements?.timeSaved || 0 }}h
            </div>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
            <Package class="h-6 w-6 text-white" />
          </div>
          <div>
            <div class="text-sm text-orange-700">原片节约</div>
            <div class="text-xl font-bold text-orange-900">
              {{ improvements?.materialSaved || 0 }}张
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优化结果摘要 -->
    <div class="bg-white border rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">优化结果摘要</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 切割方案统计 -->
        <div>
          <h5 class="text-sm font-medium text-gray-700 mb-3">切割方案统计</h5>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">总方案数:</span>
              <span class="font-medium">{{ cuttingResult?.cuttingPlans?.length || 0 }} 个</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">平均利用率:</span>
              <span class="font-medium">{{ calculateAverageUtilization() }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">最高利用率:</span>
              <span class="font-medium">{{ calculateMaxUtilization() }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">总切割件数:</span>
              <span class="font-medium">{{ calculateTotalPieces() }} 件</span>
            </div>
          </div>
        </div>

        <!-- 物料使用统计 -->
        <div>
          <h5 class="text-sm font-medium text-gray-700 mb-3">物料使用统计</h5>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">使用原片:</span>
              <span class="font-medium">{{ cuttingResult?.materialUsage?.length || 0 }} 种</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">总使用量:</span>
              <span class="font-medium">{{ calculateTotalMaterialUsed() }} 张</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">总成本:</span>
              <span class="font-medium">¥{{ formatCurrency(calculateTotalCost()) }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">废料总量:</span>
              <span class="font-medium">{{ calculateTotalWaste() }} m²</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 时间估算 -->
    <div class="bg-white border rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">生产时间估算</h4>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center p-4 bg-gray-50 rounded-lg">
          <div class="text-2xl font-bold text-gray-900 mb-1">
            {{ formatTime(calculateTotalCuttingTime()) }}
          </div>
          <div class="text-sm text-gray-600">切割时间</div>
        </div>
        <div class="text-center p-4 bg-gray-50 rounded-lg">
          <div class="text-2xl font-bold text-gray-900 mb-1">
            {{ formatTime(calculateTotalSetupTime()) }}
          </div>
          <div class="text-sm text-gray-600">准备时间</div>
        </div>
        <div class="text-center p-4 bg-gray-50 rounded-lg">
          <div class="text-2xl font-bold text-gray-900 mb-1">
            {{ formatTime(calculateTotalTime()) }}
          </div>
          <div class="text-sm text-gray-600">总时间</div>
        </div>
      </div>
    </div>

    <!-- 下一步操作 -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">下一步操作</h4>
      <div class="space-y-4">
        <div class="text-sm text-gray-700">
          切割优化已完成，您可以选择以下操作：
        </div>
        
        <div class="flex flex-wrap items-center gap-3">
          <Button @click="$emit('proceed-to-final')" size="lg" class="px-8">
            <ArrowRight class="h-4 w-4 mr-2" />
            进入最终确认
          </Button>
          <Button @click="$emit('export-report')" variant="outline" size="lg">
            <FileText class="h-4 w-4 mr-2" />
            导出优化报告
          </Button>
          <Button variant="outline" size="lg">
            <Share class="h-4 w-4 mr-2" />
            分享结果
          </Button>
        </div>
        
        <div class="text-xs text-gray-500">
          进入最终确认后，系统将生成正式的生产指令和作业文档
        </div>
      </div>
    </div>

    <!-- 操作历史 -->
    <div class="bg-white border rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">操作历史</h4>
      <div class="space-y-3">
        <div class="flex items-center space-x-3 text-sm">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          <span class="text-gray-600">{{ formatDateTime(cuttingResult?.importTime) }}</span>
          <span class="text-gray-900">切割优化结果导入完成</span>
        </div>
        <div class="flex items-center space-x-3 text-sm">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span class="text-gray-600">{{ formatDateTime(getExportTime()) }}</span>
          <span class="text-gray-900">切割数据导出完成</span>
        </div>
        <div class="flex items-center space-x-3 text-sm">
          <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
          <span class="text-gray-600">{{ formatDateTime(getPreScheduleTime()) }}</span>
          <span class="text-gray-900">预排产计算完成</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  CheckCircle, TrendingUp, DollarSign, Clock, Package,
  ArrowRight, FileText, Share
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import type { CuttingImportResult, CuttingImprovements } from '@/types/scheduling';

interface Props {
  cuttingResult?: CuttingImportResult;
  improvements?: CuttingImprovements;
}

interface Emits {
  (e: 'proceed-to-final'): void;
  (e: 'export-report'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算函数
const calculateAverageUtilization = () => {
  if (!props.cuttingResult?.cuttingPlans) return 0;
  const total = props.cuttingResult.cuttingPlans.reduce((sum, plan) => sum + plan.layout.utilization, 0);
  return Math.round(total / props.cuttingResult.cuttingPlans.length);
};

const calculateMaxUtilization = () => {
  if (!props.cuttingResult?.cuttingPlans) return 0;
  return Math.max(...props.cuttingResult.cuttingPlans.map(plan => plan.layout.utilization));
};

const calculateTotalPieces = () => {
  if (!props.cuttingResult?.cuttingPlans) return 0;
  return props.cuttingResult.cuttingPlans.reduce((sum, plan) => sum + plan.layout.pieces.length, 0);
};

const calculateTotalMaterialUsed = () => {
  if (!props.cuttingResult?.materialUsage) return 0;
  return props.cuttingResult.materialUsage.reduce((sum, usage) => sum + usage.usedQuantity, 0);
};

const calculateTotalCost = () => {
  if (!props.cuttingResult?.materialUsage) return 0;
  return props.cuttingResult.materialUsage.reduce((sum, usage) => sum + usage.cost, 0);
};

const calculateTotalWaste = () => {
  if (!props.cuttingResult?.materialUsage) return 0;
  return props.cuttingResult.materialUsage.reduce((sum, usage) => sum + usage.wasteAmount, 0);
};

const calculateTotalCuttingTime = () => {
  if (!props.cuttingResult?.timeEstimates) return 0;
  return props.cuttingResult.timeEstimates.reduce((sum, estimate) => sum + estimate.cuttingTime, 0);
};

const calculateTotalSetupTime = () => {
  if (!props.cuttingResult?.timeEstimates) return 0;
  return props.cuttingResult.timeEstimates.reduce((sum, estimate) => sum + estimate.setupTime, 0);
};

const calculateTotalTime = () => {
  if (!props.cuttingResult?.timeEstimates) return 0;
  return props.cuttingResult.timeEstimates.reduce((sum, estimate) => sum + estimate.totalTime, 0);
};

// 工具函数
const formatCurrency = (amount: number) => {
  return amount.toLocaleString('zh-CN');
};

const formatTime = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
};

const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '--';
  return new Date(dateTime).toLocaleString('zh-CN');
};

const getExportTime = () => {
  // 这里应该从store或props中获取导出时间
  return new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(); // 2小时前
};

const getPreScheduleTime = () => {
  // 这里应该从store或props中获取预排产时间
  return new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(); // 3小时前
};
</script>
