<template>
  <div class="space-y-6">
    <!-- 标题和描述 -->
    <div class="text-center">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">结果验证与审核</h3>
      <p class="text-sm text-gray-600">审核切割优化结果，确认是否符合生产要求</p>
    </div>

    <!-- 验证状态总览 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 验证状态 -->
      <div class="bg-white border rounded-lg p-4">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <CheckCircle class="h-8 w-8 text-green-600" />
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900">验证状态</div>
            <div class="text-lg font-bold text-green-600">通过验证</div>
          </div>
        </div>
      </div>

      <!-- 改进指标 -->
      <div class="bg-white border rounded-lg p-4">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <TrendingUp class="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900">利用率提升</div>
            <div class="text-lg font-bold text-blue-600">
              +{{ cuttingResult?.improvements?.utilizationImproved || 0 }}%
            </div>
          </div>
        </div>
      </div>

      <!-- 成本节约 -->
      <div class="bg-white border rounded-lg p-4">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <DollarSign class="h-8 w-8 text-green-600" />
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900">成本节约</div>
            <div class="text-lg font-bold text-green-600">
              ¥{{ formatCurrency(cuttingResult?.improvements?.costSaved || 0) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对比分析 -->
    <div v-if="comparisonData" class="bg-white border rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">优化前后对比</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div v-for="(metric, key) in comparisonData" :key="key" class="text-center">
          <div class="text-sm text-gray-600 mb-1">{{ getMetricLabel(key) }}</div>
          <div class="space-y-1">
            <div class="text-lg font-bold text-gray-900">
              {{ formatMetricValue(metric.actual, metric.unit) }}
            </div>
            <div class="text-xs text-gray-500">
              优化前: {{ formatMetricValue(metric.estimated, metric.unit) }}
            </div>
            <div 
              class="text-sm font-medium"
              :class="metric.improvement >= 0 ? 'text-green-600' : 'text-red-600'"
            >
              {{ metric.improvement >= 0 ? '+' : '' }}{{ metric.improvementPercentage.toFixed(1) }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 切割方案预览 -->
    <div v-if="cuttingResult?.cuttingPlans" class="bg-white border rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">切割方案预览</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div 
          v-for="plan in cuttingResult.cuttingPlans.slice(0, 6)" 
          :key="plan.planId"
          class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
          @click="selectPlan(plan)"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="text-sm font-medium text-gray-900">
              原片 {{ plan.materialId }}
            </div>
            <Badge variant="outline" :class="getUtilizationBadgeClass(plan.layout.utilization)">
              {{ plan.layout.utilization }}%
            </Badge>
          </div>
          
          <!-- 简化的切割布局图 -->
          <div class="bg-gray-50 rounded border h-24 mb-3 relative overflow-hidden">
            <div class="absolute inset-1 bg-blue-100 rounded">
              <!-- 模拟切割件布局 -->
              <div 
                v-for="(piece, index) in plan.layout.pieces.slice(0, 4)" 
                :key="piece.pieceId"
                class="absolute bg-blue-300 border border-blue-400 rounded-sm"
                :style="getPieceStyle(piece, index)"
              >
                <div class="text-xs text-blue-800 p-1 truncate">
                  {{ piece.batchId }}
                </div>
              </div>
            </div>
          </div>
          
          <div class="text-xs text-gray-600 space-y-1">
            <div class="flex justify-between">
              <span>切割件数:</span>
              <span>{{ plan.layout.pieces.length }}</span>
            </div>
            <div class="flex justify-between">
              <span>废料率:</span>
              <span>{{ plan.layout.wastePercentage }}%</span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-if="cuttingResult.cuttingPlans.length > 6" class="text-center mt-4">
        <Button variant="outline" size="sm">
          查看全部 {{ cuttingResult.cuttingPlans.length }} 个方案
        </Button>
      </div>
    </div>

    <!-- 验证详情 -->
    <div v-if="validationResult" class="bg-white border rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">验证详情</h4>
      
      <!-- 验证通过项 -->
      <div class="space-y-3 mb-4">
        <div class="flex items-center space-x-2 text-sm text-green-700">
          <CheckCircle class="h-4 w-4" />
          <span>数据完整性检查通过</span>
        </div>
        <div class="flex items-center space-x-2 text-sm text-green-700">
          <CheckCircle class="h-4 w-4" />
          <span>业务逻辑验证通过</span>
        </div>
        <div class="flex items-center space-x-2 text-sm text-green-700">
          <CheckCircle class="h-4 w-4" />
          <span>约束条件符合要求</span>
        </div>
        <div class="flex items-center space-x-2 text-sm text-green-700">
          <CheckCircle class="h-4 w-4" />
          <span>成本效益分析合理</span>
        </div>
      </div>

      <!-- 警告信息 -->
      <div v-if="validationResult.warnings && validationResult.warnings.length > 0" class="bg-yellow-50 border border-yellow-200 rounded p-4">
        <div class="flex items-start space-x-2">
          <AlertTriangle class="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div>
            <div class="text-sm font-medium text-yellow-800 mb-2">需要注意的问题</div>
            <ul class="text-sm text-yellow-700 space-y-1">
              <li v-for="warning in validationResult.warnings" :key="warning.code">
                • {{ warning.message }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 审核操作 -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h4 class="text-lg font-medium text-gray-900 mb-4">审核决定</h4>
      <div class="space-y-4">
        <div class="text-sm text-gray-600">
          请仔细审核上述优化结果，确认是否符合生产要求和质量标准。
        </div>
        
        <div class="flex items-center space-x-4">
          <Button @click="handleApprove" size="lg" class="px-8">
            <CheckCircle class="h-4 w-4 mr-2" />
            批准结果
          </Button>
          <Button @click="handleReject" variant="outline" size="lg">
            <X class="h-4 w-4 mr-2" />
            拒绝结果
          </Button>
          <Button @click="handleRequestModification" variant="outline" size="lg">
            <Edit class="h-4 w-4 mr-2" />
            请求修改
          </Button>
        </div>
        
        <div class="text-xs text-gray-500">
          批准后将进入最终确认阶段，生成正式的生产指令
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { 
  CheckCircle, TrendingUp, DollarSign, AlertTriangle, 
  X, Edit 
} from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { 
  CuttingImportResult, 
  ValidationResult, 
  ComparisonData,
  CuttingPlan,
  CuttingPiece
} from '@/types/scheduling';

interface Props {
  cuttingResult?: CuttingImportResult;
  validationResult?: ValidationResult;
  comparisonData?: ComparisonData;
}

interface Emits {
  (e: 'approve-result'): void;
  (e: 'reject-result', reason: string): void;
  (e: 'request-modification'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 选中的切割方案
const selectedPlan = ref<CuttingPlan | null>(null);

// 工具函数
const formatCurrency = (amount: number) => {
  return amount.toLocaleString('zh-CN');
};

const getMetricLabel = (key: string) => {
  const labels = {
    materialUsage: '原片使用',
    utilization: '利用率',
    duration: '切割时间',
    totalDuration: '总工期',
    cost: '总成本'
  };
  return labels[key as keyof typeof labels] || key;
};

const formatMetricValue = (value: number, unit: string) => {
  if (unit === '%') {
    return `${value.toFixed(1)}%`;
  } else if (unit === '元') {
    return `¥${value.toLocaleString()}`;
  } else if (unit === '小时') {
    return `${value.toFixed(1)}h`;
  } else if (unit === '天') {
    return `${value.toFixed(1)}天`;
  }
  return `${value} ${unit}`;
};

const getUtilizationBadgeClass = (utilization: number) => {
  if (utilization >= 85) {
    return 'text-green-700 border-green-300';
  } else if (utilization >= 75) {
    return 'text-blue-700 border-blue-300';
  } else {
    return 'text-orange-700 border-orange-300';
  }
};

const getPieceStyle = (piece: CuttingPiece, index: number) => {
  // 简化的布局计算，实际应该根据真实尺寸计算
  const colors = ['bg-blue-300', 'bg-green-300', 'bg-yellow-300', 'bg-purple-300'];
  const positions = [
    { left: '5%', top: '10%', width: '40%', height: '35%' },
    { left: '50%', top: '10%', width: '45%', height: '35%' },
    { left: '5%', top: '50%', width: '30%', height: '40%' },
    { left: '40%', top: '50%', width: '55%', height: '40%' }
  ];
  
  const position = positions[index] || positions[0];
  return {
    ...position,
    backgroundColor: index < 4 ? undefined : '#e5e7eb'
  };
};

// 事件处理
const selectPlan = (plan: CuttingPlan) => {
  selectedPlan.value = plan;
  // 这里可以打开详细的切割方案查看器
};

const handleApprove = () => {
  emit('approve-result');
};

const handleReject = () => {
  const reason = prompt('请输入拒绝原因:');
  if (reason) {
    emit('reject-result', reason);
  }
};

const handleRequestModification = () => {
  emit('request-modification');
};
</script>
