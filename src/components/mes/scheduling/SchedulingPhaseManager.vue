<template>
  <div class="flex items-center space-x-4">
    <!-- 阶段指示器 -->
    <div class="flex items-center space-x-2">
      <template v-for="(phase, index) in phases" :key="phase.id">
        <!-- 阶段圆圈 -->
        <div 
          class="flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 cursor-pointer"
          :class="getPhaseCircleClass(phase.id, index)"
          @click="handlePhaseClick(phase.id)"
        >
          <component 
            :is="phase.icon" 
            class="w-4 h-4"
            :class="getPhaseIconClass(phase.id)"
          />
        </div>
        
        <!-- 连接线 -->
        <div 
          v-if="index < phases.length - 1"
          class="w-12 h-0.5 transition-all duration-300"
          :class="getConnectionLineClass(index)"
        />
      </template>
    </div>
    
    <!-- 当前阶段信息 -->
    <div class="flex flex-col">
      <div class="text-sm font-medium text-gray-900">
        {{ getCurrentPhaseInfo().title }}
      </div>
      <div class="text-xs text-gray-500">
        {{ getCurrentPhaseInfo().description }}
      </div>
    </div>
    
    <!-- 进度百分比 -->
    <div class="flex items-center space-x-2">
      <div class="text-sm font-medium text-gray-600">
        {{ getProgressPercentage() }}%
      </div>
      <div class="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
        <div 
          class="h-full bg-blue-500 transition-all duration-500 ease-out"
          :style="{ width: `${getProgressPercentage()}%` }"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Calendar, Scissors, CheckCircle } from 'lucide-vue-next';
import type { SchedulingPhase } from '@/types/scheduling';

interface Props {
  currentPhase: SchedulingPhase;
}

interface Emits {
  (e: 'phase-change', phase: SchedulingPhase): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 阶段定义
const phases = [
  {
    id: 'pre-scheduling' as SchedulingPhase,
    title: '预排产',
    description: '基于标准工时估算',
    icon: Calendar
  },
  {
    id: 'cutting-optimization' as SchedulingPhase,
    title: '切割优化',
    description: '第三方系统优化',
    icon: Scissors
  },
  {
    id: 'final-confirmation' as SchedulingPhase,
    title: '最终确认',
    description: '确认排产方案',
    icon: CheckCircle
  }
];

// 计算当前阶段索引
const currentPhaseIndex = computed(() => {
  return phases.findIndex(phase => phase.id === props.currentPhase);
});

// 获取阶段圆圈样式
const getPhaseCircleClass = (phaseId: SchedulingPhase, index: number) => {
  const isCurrent = phaseId === props.currentPhase;
  const isCompleted = index < currentPhaseIndex.value;
  const isAccessible = index <= currentPhaseIndex.value;
  
  if (isCurrent) {
    return 'border-blue-500 bg-blue-500 text-white shadow-lg scale-110';
  } else if (isCompleted) {
    return 'border-green-500 bg-green-500 text-white hover:scale-105';
  } else if (isAccessible) {
    return 'border-gray-300 bg-white text-gray-400 hover:border-gray-400';
  } else {
    return 'border-gray-200 bg-gray-50 text-gray-300 cursor-not-allowed';
  }
};

// 获取阶段图标样式
const getPhaseIconClass = (phaseId: SchedulingPhase) => {
  const isCurrent = phaseId === props.currentPhase;
  const index = phases.findIndex(phase => phase.id === phaseId);
  const isCompleted = index < currentPhaseIndex.value;
  
  if (isCurrent || isCompleted) {
    return 'text-white';
  } else {
    return 'text-gray-400';
  }
};

// 获取连接线样式
const getConnectionLineClass = (index: number) => {
  const isCompleted = index < currentPhaseIndex.value;
  const isCurrent = index === currentPhaseIndex.value - 1;
  
  if (isCompleted) {
    return 'bg-green-500';
  } else if (isCurrent) {
    return 'bg-gradient-to-r from-green-500 to-gray-300';
  } else {
    return 'bg-gray-300';
  }
};

// 获取当前阶段信息
const getCurrentPhaseInfo = () => {
  return phases.find(phase => phase.id === props.currentPhase) || phases[0];
};

// 获取进度百分比
const getProgressPercentage = () => {
  const totalPhases = phases.length;
  const currentIndex = currentPhaseIndex.value;
  
  if (currentIndex === -1) return 0;
  
  // 每个阶段占总进度的1/3，当前阶段算50%完成
  const baseProgress = (currentIndex / totalPhases) * 100;
  const currentPhaseProgress = (1 / totalPhases) * 50; // 当前阶段进行中算50%
  
  return Math.min(100, Math.round(baseProgress + currentPhaseProgress));
};

// 处理阶段点击
const handlePhaseClick = (phaseId: SchedulingPhase) => {
  const clickedIndex = phases.findIndex(phase => phase.id === phaseId);
  const currentIndex = currentPhaseIndex.value;
  
  // 只允许点击当前阶段或之前的阶段
  if (clickedIndex <= currentIndex) {
    emit('phase-change', phaseId);
  }
};
</script>

<style scoped>
/* 添加一些自定义动画效果 */
.phase-circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.phase-circle:hover {
  transform: scale(1.05);
}

.progress-bar {
  background: linear-gradient(90deg, #3b82f6 0%, #10b981 100%);
}
</style>
