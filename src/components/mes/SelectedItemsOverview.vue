<template>
  <div class="selected-items-overview">
    <div class="flex items-center justify-between">
      <!-- 基础统计 -->
      <div class="flex items-center gap-4">
        <div class="stat-item-compact">
          <span class="stat-value-compact text-blue-600">{{ formatNumber(selectedItems.length) }}</span>
          <span class="stat-label-compact">个订单项</span>
        </div>
        <div class="stat-item-compact">
          <span class="stat-value-compact text-green-600">{{ formatNumber(totalQuantity) }}</span>
          <span class="stat-label-compact">片</span>
        </div>
        <div class="stat-item-compact">
          <span class="stat-value-compact text-purple-600">{{ formatNumber(uniqueCustomers) }}</span>
          <span class="stat-label-compact">个客户</span>
        </div>
        <div class="stat-item-compact">
          <span class="stat-value-compact text-orange-600">¥{{ formatCurrency(estimatedValue) }}</span>
          <span class="stat-label-compact">预估价值</span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex items-center gap-2">
        <Button size="sm" variant="outline" @click="showDetails = !showDetails">
          {{ showDetails ? '收起' : '详情' }}
          <ChevronDown class="w-3 h-3 ml-1 transition-transform" :class="{ 'rotate-180': showDetails }" />
        </Button>
      </div>
    </div>
    
    <!-- 详细信息 -->
    <div v-if="showDetails" class="mt-4 pt-4 border-t">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <div 
          v-for="item in selectedItems" 
          :key="item.id"
          class="overview-item p-3 bg-gray-50 rounded-lg"
        >
          <div class="font-medium text-sm">{{ item.orderNumber }}</div>
          <div class="text-xs text-gray-600 mt-1">{{ item.customerName }}</div>
          <div class="text-xs text-gray-500 mt-1">
            {{ item.specifications.length }}×{{ item.specifications.width }}mm × {{ item.selectedQuantity }}片
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { ChevronDown } from 'lucide-vue-next'
import type { SelectedOrderItem } from '@/types/production-order-creation'

interface Props {
  selectedItems: SelectedOrderItem[]
  totalQuantity: number
  uniqueCustomers: number
  estimatedValue: number
}

defineProps<Props>()
const showDetails = ref(false)

// 工具方法
const formatNumber = (value: any): string => {
  if (value === null || value === undefined || isNaN(Number(value))) {
    return '0'
  }
  return String(Math.round(Number(value)))
}

const formatCurrency = (value: any): string => {
  const num = Number(value)
  if (isNaN(num)) return '0'
  return num.toLocaleString('zh-CN')
}
</script>

<style scoped>
.stat-item-compact {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}
.stat-value-compact {
  font-size: 1rem;
  font-weight: 600;
}
.stat-label-compact {
  font-size: 0.75rem;
  color: #6b7280;
}
.overview-item {
  border: 1px solid #e5e7eb;
}
</style>