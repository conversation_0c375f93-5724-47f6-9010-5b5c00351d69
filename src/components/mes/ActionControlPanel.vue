<template>
  <div class="h-full flex flex-col">
    <!-- 头部 -->
    <div class="p-4 border-b bg-white">
      <h3 class="font-medium text-sm">操作控制</h3>
    </div>
    
    <!-- 状态概览 -->
    <div class="p-4 space-y-4">
      <!-- 选择状态 -->
      <div class="space-y-3">
        <div class="text-xs font-medium text-gray-700">选择状态</div>
        <div class="space-y-2">
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600">订单项</span>
            <Badge variant="secondary" class="text-xs">{{ selectedItemsCount }}项</Badge>
          </div>
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600">预估批次</span>
            <Badge variant="outline" class="text-xs">{{ estimatedBatches }}个</Badge>
          </div>
        </div>
      </div>
      
      <!-- 验证状态 -->
      <div class="space-y-3">
        <div class="text-xs font-medium text-gray-700">验证状态</div>
        <div class="space-y-2">
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-600">总问题</span>
            <Badge 
              :variant="validationSummary.totalIssues > 0 ? 'destructive' : 'default'" 
              class="text-xs"
            >
              {{ validationSummary.totalIssues }}
            </Badge>
          </div>
          <div v-if="validationSummary.errors > 0" class="flex items-center justify-between text-xs">
            <span class="text-red-600">错误</span>
            <Badge variant="destructive" class="text-xs">{{ validationSummary.errors }}</Badge>
          </div>
          <div v-if="validationSummary.warnings > 0" class="flex items-center justify-between text-xs">
            <span class="text-yellow-600">警告</span>
            <Badge variant="secondary" class="text-xs">{{ validationSummary.warnings }}</Badge>
          </div>
        </div>
      </div>
      
      <!-- 关键问题提示 -->
      <div v-if="validationSummary.criticalIssues.length > 0" class="p-3 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-center gap-2 text-xs font-medium text-red-700 mb-2">
          <AlertTriangle class="w-3 h-3" />
          关键问题
        </div>
        <div class="space-y-1">
          <div 
            v-for="issue in validationSummary.criticalIssues.slice(0, 2)" 
            :key="issue.id"
            class="text-xs text-red-600"
          >
            {{ issue.title }}
          </div>
          <div v-if="validationSummary.criticalIssues.length > 2" class="text-xs text-red-500">
            +{{ validationSummary.criticalIssues.length - 2 }}个其他问题
          </div>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="flex-1 flex flex-col justify-end p-4 space-y-3">
      <!-- 预览按钮 -->
      <Button 
        variant="outline" 
        size="sm"
        @click="handlePreviewOrder"
        :disabled="selectedItemsCount === 0"
        class="w-full"
      >
        <Eye class="w-3 h-3 mr-2" />
        预览工单
      </Button>
      
      <!-- 保存草稿按钮 -->
      <Button 
        variant="ghost" 
        size="sm"
        @click="handleSaveDraft"
        :disabled="selectedItemsCount === 0"
        class="w-full"
      >
        <Save class="w-3 h-3 mr-2" />
        保存草稿
      </Button>
      
      <!-- 分隔线 -->
      <div class="border-t my-2"></div>
      
      <!-- 创建工单按钮 -->
      <Button 
        size="sm"
        @click="handleCreateOrder"
        :disabled="!canCreate || isCreating"
        class="w-full"
      >
        <div v-if="isCreating" class="flex items-center gap-2">
          <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
          创建中...
        </div>
        <div v-else class="flex items-center gap-2">
          <Plus class="w-3 h-3" />
          创建工单
        </div>
      </Button>
      
      <!-- 取消按钮 -->
      <Button 
        variant="outline" 
        size="sm"
        @click="handleCancel"
        :disabled="isCreating"
        class="w-full"
      >
        <X class="w-3 h-3 mr-2" />
        取消
      </Button>
      
      <!-- 创建提示 -->
      <div class="text-xs text-gray-500 text-center mt-2">
        <div v-if="!canCreate && selectedItemsCount > 0" class="text-red-500">
          请解决所有错误后再创建
        </div>
        <div v-else-if="selectedItemsCount === 0" class="text-gray-400">
          请先选择订单项
        </div>
        <div v-else class="text-green-600">
          ✓ 可以创建工单
        </div>
      </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="border-t p-4 bg-gray-50">
      <div class="text-xs font-medium text-gray-700 mb-3">快捷操作</div>
      <div class="space-y-2">
        <Button 
          variant="ghost" 
          size="sm"
          @click="handleQuickOptimize"
          :disabled="selectedItemsCount === 0"
          class="w-full justify-start text-xs"
        >
          <Zap class="w-3 h-3 mr-2" />
          快速优化
        </Button>
        <Button 
          variant="ghost" 
          size="sm"
          @click="handleClearAll"
          :disabled="selectedItemsCount === 0"
          class="w-full justify-start text-xs"
        >
          <RotateCcw class="w-3 h-3 mr-2" />
          清空选择
        </Button>
        <Button 
          variant="ghost" 
          size="sm"
          @click="handleLoadDraft"
          class="w-full justify-start text-xs"
        >
          <FolderOpen class="w-3 h-3 mr-2" />
          加载草稿
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  AlertTriangle,
  Eye,
  Save,
  Plus,
  X,
  Zap,
  RotateCcw,
  FolderOpen,
} from 'lucide-vue-next'

import type { 
  ActionControlPanelProps,
  ActionControlPanelEvents
} from '@/types/production-order-creation'

// Props
const props = defineProps<ActionControlPanelProps>()

// Events
const emit = defineEmits<ActionControlPanelEvents>()

// 事件处理
const handlePreviewOrder = () => {
  emit('preview-order')
}

const handleSaveDraft = () => {
  emit('save-draft')
}

const handleCreateOrder = () => {
  emit('create-order')
}

const handleCancel = () => {
  emit('cancel')
}

const handleQuickOptimize = () => {
  // 触发快速优化
  // 这里可以发送特定的优化请求事件
  console.log('快速优化')
}

const handleClearAll = () => {
  // 清空所有选择
  console.log('清空选择')
}

const handleLoadDraft = () => {
  // 加载草稿
  console.log('加载草稿')
}
</script>