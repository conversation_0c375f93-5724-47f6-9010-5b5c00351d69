<template>
  <div class="space-y-2">
    <!-- 单行搜索和筛选 -->
    <div class="flex items-center gap-2">
      <!-- 搜索框 -->
      <div class="relative flex-1">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <Input
          :value="searchQuery"
          @input="handleSearchInput"
          @keydown.enter="handleSearchSubmit"
          placeholder="搜索订单号、客户名称、产品规格..."
          class="pl-10 pr-10 h-8 text-sm"
        />
        <Button
          v-if="searchQuery"
          variant="ghost"
          size="sm"
          class="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          @click="clearSearch"
        >
          <X class="w-3 h-3" />
        </Button>
      </div>
      
      <!-- 订单状态筛选 -->
      <Select :value="statusFilter" @update:value="handleStatusFilterChange">
        <SelectTrigger class="h-8 w-32 text-xs">
          <SelectValue placeholder="状态" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">全部状态</SelectItem>
          <SelectItem value="confirmed">已确认</SelectItem>
          <SelectItem value="ready">待转换</SelectItem>
          <SelectItem value="partial">部分转换</SelectItem>
        </SelectContent>
      </Select>
      
      <!-- 工艺类型筛选 -->
      <Select :value="processTypeFilter" @update:value="handleProcessTypeFilterChange">
        <SelectTrigger class="h-8 w-32 text-xs">
          <SelectValue placeholder="工艺" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">全部工艺</SelectItem>
          <SelectItem value="cutting">切割</SelectItem>
          <SelectItem value="tempering">钢化</SelectItem>
          <SelectItem value="laminating">夹胶</SelectItem>
          <SelectItem value="insulating">中空</SelectItem>
        </SelectContent>
      </Select>
      
      <!-- 高级筛选按钮 -->
      <Button 
        variant="outline" 
        size="sm" 
        @click="toggleAdvancedFilters"
        class="h-8 px-3 text-xs"
      >
        <Filter class="w-3 h-3 mr-1" />
        筛选
        <ChevronDown 
          class="w-3 h-3 ml-1 transition-transform" 
          :class="{ 'rotate-180': showAdvancedFilters }"
        />
      </Button>
      
      <!-- 筛选结果统计 -->
      <div class="text-xs text-gray-500 whitespace-nowrap">
        <span v-if="filteredCount !== totalCount">
          {{ filteredCount }}/{{ totalCount }}
        </span>
        <span v-else>
          {{ totalCount }}项
        </span>
      </div>
    </div>
    
    <!-- 快速搜索建议 -->
    <div v-if="showSearchSuggestions && searchSuggestions.length > 0" 
         class="bg-white border rounded-lg shadow-lg absolute z-10 w-full mt-1">
      <div class="p-2 border-b text-xs font-medium text-gray-600">搜索建议</div>
      <div class="max-h-32 overflow-y-auto">
        <div 
          v-for="suggestion in searchSuggestions" 
          :key="suggestion.id"
          class="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm"
          @click="applySuggestion(suggestion)"
        >
          <div class="font-medium">{{ suggestion.text }}</div>
          <div class="text-xs text-gray-500">{{ suggestion.category }}</div>
        </div>
      </div>
    </div>
    
    <!-- 高级筛选 -->
    <div v-if="showAdvancedFilters" class="space-y-2 p-3 bg-gray-50 rounded-lg border">
      <div class="flex items-center justify-between">
        <span class="text-xs font-medium text-gray-700">高级筛选</span>
        <Button variant="ghost" size="sm" @click="clearAllFilters" class="h-6 text-xs">
          清空筛选
        </Button>
      </div>
      
      <!-- 客户筛选 -->
      <div class="grid grid-cols-2 gap-2">
        <Select :value="customerFilter" @update:value="handleCustomerFilterChange">
          <SelectTrigger class="h-7 text-xs">
            <SelectValue placeholder="客户" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部客户</SelectItem>
            <SelectItem 
              v-for="customer in availableCustomers" 
              :key="customer.id" 
              :value="customer.id"
            >
              {{ customer.name }} ({{ customer.orderCount }})
            </SelectItem>
          </SelectContent>
        </Select>
        
        <!-- 规格范围筛选 -->
        <Select :value="sizeRangeFilter" @update:value="handleSizeRangeFilterChange">
          <SelectTrigger class="h-7 text-xs">
            <SelectValue placeholder="规格范围" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部规格</SelectItem>
            <SelectItem value="small">小规格 (≤1000mm)</SelectItem>
            <SelectItem value="medium">中规格 (1000-2000mm)</SelectItem>
            <SelectItem value="large">大规格 (>2000mm)</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <!-- 厚度筛选 -->
      <div class="flex items-center gap-2">
        <span class="text-xs text-gray-600 w-12">厚度:</span>
        <div class="flex gap-1 flex-wrap">
          <Button
            v-for="thickness in availableThicknesses"
            :key="thickness"
            :variant="selectedThicknesses.includes(thickness) ? 'default' : 'outline'"
            size="sm"
            class="h-6 px-2 text-xs"
            @click="toggleThickness(thickness)"
          >
            {{ thickness }}mm
          </Button>
        </div>
      </div>
      
      <!-- 交期筛选 -->
      <div class="grid grid-cols-2 gap-2">
        <div>
          <Label class="text-xs text-gray-600">交期从</Label>
          <Input
            type="date"
            :value="deliveryDateFrom"
            @input="handleDeliveryDateFromChange"
            class="h-7 text-xs"
          />
        </div>
        <div>
          <Label class="text-xs text-gray-600">交期到</Label>
          <Input
            type="date"
            :value="deliveryDateTo"
            @input="handleDeliveryDateToChange"
            class="h-7 text-xs"
          />
        </div>
      </div>
    </div>
    
    <!-- 活跃筛选标签 -->
    <div v-if="activeFilters.length > 0" class="flex flex-wrap gap-1">
      <Badge
        v-for="filter in activeFilters"
        :key="filter.key"
        variant="secondary"
        class="text-xs cursor-pointer hover:bg-gray-200"
        @click="removeFilter(filter.key)"
      >
        {{ filter.label }}
        <X class="w-3 h-3 ml-1" />
      </Badge>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Search,
  X,
  Filter,
  ChevronDown,
  Scissors,
  Flame,
  Layers,
  Square,
} from 'lucide-vue-next'

import type { CustomerOrder } from '@/types/mes-validation'

// Props
interface Props {
  searchQuery: string
  statusFilter: string
  processTypeFilter: string
  customerFilter: string
  availableOrders: CustomerOrder[]
  filteredCount: number
  totalCount: number
}

// Events
interface Emits {
  (e: 'search-changed', query: string): void
  (e: 'filter-changed', filterType: string, value: string): void
  (e: 'advanced-filter-changed', filters: Record<string, any>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const showAdvancedFilters = ref(false)
const showSearchSuggestions = ref(false)
const sizeRangeFilter = ref('all')
const selectedThicknesses = ref<number[]>([])
const deliveryDateFrom = ref('')
const deliveryDateTo = ref('')

// 搜索建议
const searchSuggestions = ref<Array<{
  id: string
  text: string
  category: string
  value: string
}>>([])

// 计算属性
const availableCustomers = computed(() => {
  const customerMap = new Map()
  props.availableOrders.forEach(order => {
    if (customerMap.has(order.customerName)) {
      customerMap.get(order.customerName).orderCount++
    } else {
      customerMap.set(order.customerName, {
        id: order.customerName,
        name: order.customerName,
        orderCount: 1
      })
    }
  })
  return Array.from(customerMap.values())
})

const availableThicknesses = computed(() => {
  const thicknesses = new Set<number>()
  props.availableOrders.forEach(order => {
    order.items.forEach(item => {
      thicknesses.add(item.specifications.thickness)
    })
  })
  return Array.from(thicknesses).sort((a, b) => a - b)
})

const activeFilters = computed(() => {
  const filters = []
  
  if (props.searchQuery) {
    filters.push({ key: 'search', label: `搜索: ${props.searchQuery}` })
  }
  
  if (props.statusFilter !== 'all') {
    filters.push({ key: 'status', label: `状态: ${getStatusText(props.statusFilter)}` })
  }
  
  if (props.processTypeFilter !== 'all') {
    filters.push({ key: 'processType', label: `工艺: ${getProcessTypeText(props.processTypeFilter)}` })
  }
  
  if (props.customerFilter !== 'all') {
    filters.push({ key: 'customer', label: `客户: ${props.customerFilter}` })
  }
  
  if (sizeRangeFilter.value !== 'all') {
    filters.push({ key: 'sizeRange', label: `规格: ${getSizeRangeText(sizeRangeFilter.value)}` })
  }
  
  if (selectedThicknesses.value.length > 0) {
    filters.push({ key: 'thickness', label: `厚度: ${selectedThicknesses.value.join(', ')}mm` })
  }
  
  if (deliveryDateFrom.value) {
    filters.push({ key: 'dateFrom', label: `交期从: ${deliveryDateFrom.value}` })
  }
  
  if (deliveryDateTo.value) {
    filters.push({ key: 'dateTo', label: `交期到: ${deliveryDateTo.value}` })
  }
  
  return filters
})

// 事件处理
const handleSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const query = target.value
  emit('search-changed', query)
  
  // 生成搜索建议
  if (query.length > 1) {
    generateSearchSuggestions(query)
    showSearchSuggestions.value = true
  } else {
    showSearchSuggestions.value = false
  }
}

const handleSearchSubmit = () => {
  showSearchSuggestions.value = false
}

const clearSearch = () => {
  emit('search-changed', '')
  showSearchSuggestions.value = false
}

const handleStatusFilterChange = (value: string) => {
  emit('filter-changed', 'status', value)
}

const handleProcessTypeFilterChange = (value: string) => {
  emit('filter-changed', 'processType', value)
}

const handleCustomerFilterChange = (value: string) => {
  emit('filter-changed', 'customer', value)
}

const handleSizeRangeFilterChange = (value: string) => {
  sizeRangeFilter.value = value
  emitAdvancedFilters()
}

const handleDeliveryDateFromChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  deliveryDateFrom.value = target.value
  emitAdvancedFilters()
}

const handleDeliveryDateToChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  deliveryDateTo.value = target.value
  emitAdvancedFilters()
}

const toggleThickness = (thickness: number) => {
  const index = selectedThicknesses.value.indexOf(thickness)
  if (index > -1) {
    selectedThicknesses.value.splice(index, 1)
  } else {
    selectedThicknesses.value.push(thickness)
  }
  emitAdvancedFilters()
}

const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

const clearAllFilters = () => {
  emit('search-changed', '')
  emit('filter-changed', 'status', 'all')
  emit('filter-changed', 'processType', 'all')
  emit('filter-changed', 'customer', 'all')
  sizeRangeFilter.value = 'all'
  selectedThicknesses.value = []
  deliveryDateFrom.value = ''
  deliveryDateTo.value = ''
  emitAdvancedFilters()
}

const removeFilter = (filterKey: string) => {
  switch (filterKey) {
    case 'search':
      clearSearch()
      break
    case 'status':
      handleStatusFilterChange('all')
      break
    case 'processType':
      handleProcessTypeFilterChange('all')
      break
    case 'customer':
      handleCustomerFilterChange('all')
      break
    case 'sizeRange':
      handleSizeRangeFilterChange('all')
      break
    case 'thickness':
      selectedThicknesses.value = []
      emitAdvancedFilters()
      break
    case 'dateFrom':
      deliveryDateFrom.value = ''
      emitAdvancedFilters()
      break
    case 'dateTo':
      deliveryDateTo.value = ''
      emitAdvancedFilters()
      break
  }
}

const applySuggestion = (suggestion: any) => {
  emit('search-changed', suggestion.value)
  showSearchSuggestions.value = false
}

// 工具方法
const emitAdvancedFilters = () => {
  const filters = {
    sizeRange: sizeRangeFilter.value,
    thicknesses: selectedThicknesses.value,
    deliveryDateFrom: deliveryDateFrom.value,
    deliveryDateTo: deliveryDateTo.value
  }
  emit('advanced-filter-changed', filters)
}

const generateSearchSuggestions = (query: string) => {
  const suggestions: Array<{
    id: string
    text: string
    category: string
    value: string
  }> = []
  const lowerQuery = query.toLowerCase()
  
  // 订单号建议
  props.availableOrders.forEach(order => {
    if (order.orderNumber.toLowerCase().includes(lowerQuery)) {
      suggestions.push({
        id: `order-${order.id}`,
        text: order.orderNumber,
        category: '订单号',
        value: order.orderNumber
      })
    }
  })
  
  // 客户名称建议
  const customers = new Set()
  props.availableOrders.forEach(order => {
    if (order.customerName.toLowerCase().includes(lowerQuery) && !customers.has(order.customerName)) {
      customers.add(order.customerName)
      suggestions.push({
        id: `customer-${order.customerName}`,
        text: order.customerName,
        category: '客户名称',
        value: order.customerName
      })
    }
  })
  
  // 产品规格建议
  const specs = new Set()
  props.availableOrders.forEach(order => {
    order.items.forEach(item => {
      const spec = `${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}`
      if (spec.includes(query) && !specs.has(spec)) {
        specs.add(spec)
        suggestions.push({
          id: `spec-${spec}`,
          text: spec + 'mm',
          category: '产品规格',
          value: spec
        })
      }
    })
  })
  
  searchSuggestions.value = suggestions.slice(0, 8) // 限制建议数量
}

const getStatusCount = (status: string): number => {
  return props.availableOrders.filter(order => order.status === status).length
}

const getProcessTypeCount = (processType: string): number => {
  return props.availableOrders.filter(order => 
    order.items.some(item => 
      (item as any).processFlow?.some((step: any) => 
        step.stepName.toLowerCase().includes(processType)
      )
    )
  ).length
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'confirmed': return '已确认'
    case 'ready': return '待转换'
    case 'partial': return '部分转换'
    default: return status
  }
}

const getProcessTypeText = (processType: string): string => {
  switch (processType) {
    case 'cutting': return '切割'
    case 'tempering': return '钢化'
    case 'laminating': return '夹胶'
    case 'insulating': return '中空'
    default: return processType
  }
}

const getSizeRangeText = (range: string): string => {
  switch (range) {
    case 'small': return '小规格'
    case 'medium': return '中规格'
    case 'large': return '大规格'
    default: return range
  }
}

// 点击外部关闭搜索建议
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    showSearchSuggestions.value = false
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 监听器
watch(() => props.searchQuery, (newQuery) => {
  if (!newQuery) {
    showSearchSuggestions.value = false
  }
})
</script>