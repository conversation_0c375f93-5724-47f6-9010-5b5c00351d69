<script setup lang="ts">
import { ref } from 'vue'
const choice = ref('')
const comment = ref('')
function submit() {
  // 原型占位：暂存到本地
  console.log('feedback', { choice: choice.value, comment: comment.value })
  choice.value = ''
  comment.value = ''
}
</script>

<template>
  <div class="border rounded p-3 space-y-2">
    <div class="font-medium">用户反馈</div>
    <select v-model="choice" class="border rounded p-1 w-full">
      <option value="">选择您的意见</option>
      <option value="ok">符合预期</option>
      <option value="improve">需要改进</option>
      <option value="other">其他</option>
    </select>
    <textarea v-model="comment" placeholder="补充意见..." class="border rounded p-2 w-full" rows="3"></textarea>
    <button class="px-3 py-1 bg-blue-600 text-white rounded" @click="submit">提交</button>
  </div>
</template>

<style scoped>
</style>

