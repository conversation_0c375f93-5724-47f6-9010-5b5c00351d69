<template>
  <div class="step-indicator">
    <div class="flex items-center justify-center mb-6">
      <!-- 第一步 -->
      <div 
        class="step-item cursor-pointer transition-all duration-300"
        :class="{ 
          'active': currentStep === 1, 
          'completed': currentStep > 1,
          'clickable': canNavigateToStep(1)
        }"
        @click="handleStepClick(1)"
      >
        <div class="step-circle">
          <CheckCircle v-if="currentStep > 1" class="w-5 h-5 text-white" />
          <span v-else class="text-sm font-medium">1</span>
        </div>
        <div class="step-content">
          <div class="step-label">选择订单项</div>
          <div class="step-description">从可用订单中选择需要生产的订单项</div>
        </div>
      </div>
      
      <!-- 连接线 -->
      <div 
        class="step-connector transition-all duration-500"
        :class="{ 'active': currentStep > 1 }"
      ></div>
      
      <!-- 第二步 -->
      <div 
        class="step-item cursor-pointer transition-all duration-300"
        :class="{ 
          'active': currentStep === 2,
          'clickable': canNavigateToStep(2)
        }"
        @click="handleStepClick(2)"
      >
        <div class="step-circle">
          <span class="text-sm font-medium">2</span>
        </div>
        <div class="step-content">
          <div class="step-label">优化批次配置</div>
          <div class="step-description">生成最优批次方案并配置工单参数</div>
        </div>
      </div>
    </div>
    
    <!-- 进度条 -->
    <div class="progress-bar mb-4">
      <div class="progress-track">
        <div 
          class="progress-fill transition-all duration-500 ease-out"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
      <div class="progress-text">
        第 {{ currentStep }} 步，共 {{ totalSteps }} 步
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CheckCircle } from 'lucide-vue-next'

// Props
interface Props {
  currentStep: 1 | 2
  totalSteps?: number
  completedSteps?: number[]
  canNavigate?: boolean
  stepValidation?: Record<number, boolean>
}

// Events
interface Emits {
  (e: 'step-clicked', step: 1 | 2): void
  (e: 'navigation-requested', step: 1 | 2): void
}

const props = withDefaults(defineProps<Props>(), {
  totalSteps: 2,
  completedSteps: () => [],
  canNavigate: true,
  stepValidation: () => ({})
})

const emit = defineEmits<Emits>()

// 计算属性
const progressPercentage = computed(() => {
  return (props.currentStep / props.totalSteps) * 100
})

const canNavigateToStep = (step: number): boolean => {
  if (!props.canNavigate) return false
  
  // 总是可以回到第一步
  if (step === 1) return true
  
  // 第二步需要第一步完成
  if (step === 2) {
    return props.completedSteps.includes(1) || props.stepValidation[1] === true
  }
  
  return false
}

// 事件处理
const handleStepClick = (step: 1 | 2) => {
  if (!canNavigateToStep(step)) return
  
  emit('step-clicked', step)
  
  if (step !== props.currentStep) {
    emit('navigation-requested', step)
  }
}
</script>

<style scoped>
.step-indicator {
  padding: 1rem 0;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  position: relative;
}

.step-item.clickable:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
}

.step-item.active {
  background-color: #dbeafe;
}

.step-item.completed {
  background-color: #dcfce7;
}

.step-circle {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.step-item .step-circle {
  background-color: #e5e7eb;
  color: #6b7280;
  border: 2px solid #e5e7eb;
}

.step-item.active .step-circle {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.step-item.completed .step-circle {
  background-color: #10b981;
  color: white;
  border-color: #10b981;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.step-label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
}

.step-item.active .step-label {
  color: #1d4ed8;
}

.step-item.completed .step-label {
  color: #059669;
}

.step-description {
  font-size: 0.75rem;
  color: #6b7280;
  max-width: 200px;
  line-height: 1.3;
}

.step-connector {
  width: 4rem;
  height: 2px;
  background-color: #e5e7eb;
  margin: 0 1rem;
  position: relative;
  overflow: hidden;
}

.step-connector::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  transition: left 0.5s ease;
}

.step-connector.active {
  background-color: #10b981;
}

.step-connector.active::before {
  left: 100%;
}

.progress-bar {
  text-align: center;
}

.progress-track {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 2px;
}

.progress-text {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .step-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .step-content {
    align-items: center;
  }
  
  .step-description {
    max-width: 150px;
  }
  
  .step-connector {
    width: 2rem;
    margin: 0 0.5rem;
  }
}

/* 动画效果 */
@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

.step-item.active .step-circle::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid #3b82f6;
  animation: pulse-ring 2s infinite;
}
</style>