<template>
  <div class="order-confirmation-step">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 左侧：订单摘要 -->
      <div class="space-y-6">
        <!-- 选中订单项摘要 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-sm flex items-center gap-2">
              <Package class="w-4 h-4" />
              订单项摘要
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div 
                v-for="item in selectedOrderItems" 
                :key="item.id"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div class="flex-1">
                  <div class="font-medium text-sm">{{ item.orderNumber }}</div>
                  <div class="text-xs text-gray-600">{{ item.customerName }}</div>
                  <div class="text-xs text-gray-500 mt-1">
                    {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                  </div>
                </div>
                <div class="text-right">
                  <div class="font-medium text-sm">{{ item.selectedQuantity }}片</div>
                  <div class="text-xs text-gray-500">/ {{ item.totalQuantity }}片</div>
                </div>
              </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="mt-4 pt-4 border-t grid grid-cols-3 gap-4 text-center">
              <div>
                <div class="text-lg font-bold text-blue-600">{{ selectedOrderItems.length }}</div>
                <div class="text-xs text-gray-600">订单项</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-600">{{ totalQuantity }}</div>
                <div class="text-xs text-gray-600">总数量</div>
              </div>
              <div>
                <div class="text-lg font-bold text-purple-600">{{ uniqueCustomers }}</div>
                <div class="text-xs text-gray-600">客户数</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <!-- 批次优化结果 -->
        <Card v-if="batchOptimization">
          <CardHeader>
            <CardTitle class="text-sm flex items-center gap-2">
              <Zap class="w-4 h-4" />
              批次优化结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="text-center p-3 bg-green-50 rounded-lg">
                <div class="text-lg font-bold text-green-700">+{{ batchOptimization.efficiency }}%</div>
                <div class="text-xs text-green-600">效率提升</div>
              </div>
              <div class="text-center p-3 bg-blue-50 rounded-lg">
                <div class="text-lg font-bold text-blue-700">{{ batchOptimization.timeSaved }}h</div>
                <div class="text-xs text-blue-600">节省工时</div>
              </div>
            </div>
            
            <div class="space-y-2">
              <div 
                v-for="(batch, index) in batchOptimization.batches" 
                :key="index"
                class="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
              >
                <span>批次 {{ index + 1 }}</span>
                <div class="flex items-center gap-2">
                  <span class="text-gray-600">{{ batch.items?.length || 0 }}项</span>
                  <span class="text-blue-600 font-medium">
                    {{ batch.items?.reduce((sum: number, item: any) => sum + item.selectedQuantity, 0) || 0 }}片
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <!-- 右侧：工单配置和成本预估 -->
      <div class="space-y-6">
        <!-- 工单配置 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-sm flex items-center gap-2">
              <Settings class="w-4 h-4" />
              工单配置
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">优先级</span>
                <Badge :variant="getPriorityVariant(workOrderConfig.priority)">
                  {{ getPriorityText(workOrderConfig.priority) }}
                </Badge>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">计划开始时间</span>
                <span class="text-sm font-medium">{{ formatDate(workOrderConfig.plannedStartDate) }}</span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">预估完成时间</span>
                <span class="text-sm font-medium">{{ formatDate(workOrderConfig.estimatedEndDate) }}</span>
              </div>
              
              <div v-if="workOrderConfig.scheduleRecommendation" class="p-3 bg-blue-50 rounded-lg">
                <div class="flex items-start gap-2">
                  <Lightbulb class="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div class="text-sm">
                    <div class="font-medium text-blue-800">智能建议</div>
                    <div class="text-blue-700 mt-1">{{ workOrderConfig.scheduleRecommendation }}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <!-- 成本预估 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-sm flex items-center gap-2">
              <Calculator class="w-4 h-4" />
              成本预估
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">材料成本</span>
                <span class="text-sm font-medium">¥{{ materialCost.toLocaleString() }}</span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">人工成本</span>
                <span class="text-sm font-medium">¥{{ laborCost.toLocaleString() }}</span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">设备成本</span>
                <span class="text-sm font-medium">¥{{ equipmentCost.toLocaleString() }}</span>
              </div>
              
              <div class="border-t pt-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium text-gray-800">预估总成本</span>
                  <span class="text-lg font-bold text-green-600">¥{{ estimatedCost.toLocaleString() }}</span>
                </div>
              </div>
              
              <div class="text-xs text-gray-500 text-center">
                * 成本预估仅供参考，实际成本可能有所差异
              </div>
            </div>
          </CardContent>
        </Card>
        
        <!-- 时间预估 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-sm flex items-center gap-2">
              <Clock class="w-4 h-4" />
              时间预估
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">预估工时</span>
                <span class="text-sm font-medium">{{ estimatedDuration }}小时</span>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">预估工期</span>
                <span class="text-sm font-medium">{{ Math.ceil(estimatedDuration / 8) }}天</span>
              </div>
              
              <div v-if="batchOptimization" class="p-3 bg-green-50 rounded-lg">
                <div class="text-sm text-green-800">
                  <div class="font-medium">优化效果</div>
                  <div class="mt-1">相比单独生产，预计节省 {{ batchOptimization.timeSaved }} 小时</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
    
    <!-- 底部操作 -->
    <div class="mt-6 pt-6 border-t">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          确认信息无误后，点击"创建工单"开始生产
        </div>
        
        <div class="flex items-center gap-3">
          <Button variant="outline" @click="$emit('save-draft')">
            <Save class="w-4 h-4 mr-2" />
            保存草稿
          </Button>
          
          <Button variant="outline" @click="$emit('prev-step')">
            <ArrowLeft class="w-4 h-4 mr-2" />
            上一步
          </Button>
          
          <Button @click="$emit('create-order')" class="bg-green-600 hover:bg-green-700">
            <Plus class="w-4 h-4 mr-2" />
            创建工单
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Package,
  Zap,
  Settings,
  Calculator,
  Clock,
  Lightbulb,
  Save,
  ArrowLeft,
  Plus,
} from 'lucide-vue-next'

import type { SelectedOrderItem, BatchOptimizationResult } from '@/types/production-order-creation'

interface Props {
  selectedOrderItems: SelectedOrderItem[]
  batchOptimization: BatchOptimizationResult | null
  workOrderConfig: {
    priority: string
    plannedStartDate: string
    estimatedEndDate: string
    scheduleRecommendation: string
  }
  estimatedCost: number
  estimatedDuration: number
}

interface Emits {
  (e: 'prev-step'): void
  (e: 'create-order'): void
  (e: 'save-draft'): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// 计算属性
const totalQuantity = computed(() => {
  return props.selectedOrderItems.reduce((sum, item) => sum + item.selectedQuantity, 0)
})

const uniqueCustomers = computed(() => {
  const customers = new Set(props.selectedOrderItems.map(item => item.customerName))
  return customers.size
})

const materialCost = computed(() => {
  return Math.round(props.estimatedCost * 0.6) // 材料成本约占60%
})

const laborCost = computed(() => {
  return Math.round(props.estimatedCost * 0.25) // 人工成本约占25%
})

const equipmentCost = computed(() => {
  return Math.round(props.estimatedCost * 0.15) // 设备成本约占15%
})

// 工具方法
const getPriorityVariant = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'destructive'
    case 'high': return 'default'
    case 'normal': return 'secondary'
    case 'low': return 'outline'
    default: return 'secondary'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'urgent': return '紧急'
    case 'high': return '高'
    case 'normal': return '普通'
    case 'low': return '低'
    default: return '普通'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>

<style scoped>
.order-confirmation-step {
  max-width: 1200px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .grid-cols-1.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>