<template>
  <div class="work-order-config space-y-4">
    <!-- 优先级设置 -->
    <div>
      <Label class="text-sm font-medium">优先级</Label>
      <Select :value="priority" @update:value="handlePriorityChange">
        <SelectTrigger class="mt-1">
          <SelectValue placeholder="选择优先级" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="urgent">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 bg-red-500 rounded-full"></div>
              紧急
            </div>
          </SelectItem>
          <SelectItem value="high">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
              高
            </div>
          </SelectItem>
          <SelectItem value="normal">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              普通
            </div>
          </SelectItem>
          <SelectItem value="low">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 bg-gray-500 rounded-full"></div>
              低
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
    
    <!-- 计划开始时间 -->
    <div>
      <Label class="text-sm font-medium">计划开始时间</Label>
      <Input 
        type="date" 
        :value="plannedStartDate"
        @input="handleStartDateChange"
        :min="new Date().toISOString().split('T')[0]"
        class="mt-1"
      />
    </div>
    
    <!-- 预估完成时间 -->
    <div>
      <Label class="text-sm font-medium">预估完成时间</Label>
      <Input 
        type="date" 
        :value="estimatedEndDate"
        readonly
        class="mt-1 bg-gray-50"
      />
    </div>
    
    <!-- 智能建议 -->
    <div v-if="scheduleRecommendation" class="p-3 bg-blue-50 rounded-lg">
      <div class="flex items-start gap-2">
        <Lightbulb class="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
        <div class="text-sm">
          <div class="font-medium text-blue-800">智能建议</div>
          <div class="text-blue-700 mt-1">{{ scheduleRecommendation }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Lightbulb } from 'lucide-vue-next'

interface Props {
  priority: string
  plannedStartDate: string
  estimatedEndDate: string
  scheduleRecommendation: string
}

interface Emits {
  (e: 'priority-changed', priority: string): void
  (e: 'schedule-changed', schedule: { startDate: string; endDate: string }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handlePriorityChange = (priority: string) => {
  emit('priority-changed', priority)
}

const handleStartDateChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('schedule-changed', {
    startDate: target.value,
    endDate: props.estimatedEndDate
  })
}
</script>