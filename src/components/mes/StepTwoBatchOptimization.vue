<template>
  <div class="step-two-container h-full flex flex-col">
    <!-- 已选项概览 -->
    <div class="selected-overview p-3 border-b bg-white">
      <SelectedItemsOverview
        :selected-items="selectedOrderItems"
        :total-quantity="totalSelectedQuantity"
        :unique-customers="uniqueCustomers"
        :estimated-value="estimatedValue"
      />
    </div>
    
    <!-- 主要内容区域 -->
    <div class="flex-1 flex min-h-0">
      <!-- 批次优化面板 (60%) -->
      <div class="flex-1 batch-optimization-section">
        <div class="h-full overflow-y-auto">
          <BatchOptimizationPanel
            :selected-order-items="selectedOrderItems"
            :batch-optimization="batchOptimization"
            :process-conflicts="processConflicts"
            :validation-results="validationResults"
            :loading="loading"
            @batch-modified="handleBatchModified"
            @conflict-resolved="handleConflictResolved"
            @optimization-requested="handleOptimizationRequested"
            @batch-details-requested="handleBatchDetailsRequested"
          />
        </div>
      </div>
      
      <!-- 配置面板 (40%) -->
      <div class="w-96 config-section border-l bg-gray-50">
        <div class="h-full overflow-y-auto">
          <!-- 工艺兼容性检查 -->
          <div class="p-4 border-b">
            <h3 class="font-medium text-sm mb-3 flex items-center gap-2">
              <Settings class="w-4 h-4" />
              工艺兼容性检查
            </h3>
            <ProcessCompatibilityChecker
              :selected-order-items="selectedOrderItems"
              :auto-check="true"
              @conflict-resolved="handleConflictResolved"
              @conflict-ignored="handleConflictIgnored"
              @grouping-applied="handleGroupingApplied"
            />
          </div>
          
          <!-- 工单配置 -->
          <div class="p-4 border-b">
            <h3 class="font-medium text-sm mb-3 flex items-center gap-2">
              <FileText class="w-4 h-4" />
              工单配置
            </h3>
            <WorkOrderConfiguration
              :priority="workOrderPriority"
              :planned-start-date="plannedStartDate"
              :estimated-end-date="estimatedEndDate"
              :schedule-recommendation="scheduleRecommendation"
              @priority-changed="handlePriorityChanged"
              @schedule-changed="handleScheduleChanged"
            />
          </div>
          
          <!-- 批次统计 -->
          <div class="p-4">
            <h3 class="font-medium text-sm mb-3 flex items-center gap-2">
              <BarChart3 class="w-4 h-4" />
              批次统计
            </h3>
            <BatchStatistics
              :batch-optimization="batchOptimization"
              :selected-items="selectedOrderItems"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="step-footer p-4 border-t bg-white">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          将创建 <span class="font-medium text-green-600">{{ formatNumber(optimizedBatchesCount) }}</span> 个批次，
          预计节省 <span class="font-medium text-green-600">{{ formatNumber(estimatedTimeSaved) }}</span>小时
        </div>
        <div class="flex items-center gap-3">
          <Button variant="outline" @click="$emit('prev-step')">
            <ArrowLeft class="w-4 h-4 mr-2" />
            上一步
          </Button>
          <Button 
            @click="$emit('create-order')" 
            :disabled="!canCreate"
            :loading="isCreating"
            class="bg-green-600 hover:bg-green-700"
          >
            <Plus class="w-4 h-4 mr-2" />
            创建工单
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '@/components/ui/button'
import {
  ArrowLeft,
  Plus,
  Settings,
  FileText,
  BarChart3,
} from 'lucide-vue-next'

import BatchOptimizationPanel from './BatchOptimizationPanel.vue'
import ProcessCompatibilityChecker from './ProcessCompatibilityChecker.vue'
import SelectedItemsOverview from './SelectedItemsOverview.vue'
import WorkOrderConfiguration from './WorkOrderConfiguration.vue'
import BatchStatistics from './BatchStatistics.vue'

import type { 
  SelectedOrderItem, 
  BatchOptimizationResult,
  ProcessConflict,
  ValidationResult,
  BatchConfiguration
} from '@/types/production-order-creation'

// Props
interface Props {
  selectedOrderItems: SelectedOrderItem[]
  batchOptimization: BatchOptimizationResult | null
  processConflicts: ProcessConflict[]
  validationResults: ValidationResult[]
  workOrderPriority: string
  plannedStartDate: string
  estimatedEndDate: string
  scheduleRecommendation: string
  loading: boolean
  isCreating: boolean
  canCreate: boolean
}

// Events
interface Emits {
  (e: 'batch-modified', batchId: string, config: BatchConfiguration): void
  (e: 'conflict-resolved', conflictId: string): void
  (e: 'optimization-requested'): void
  (e: 'priority-changed', priority: string): void
  (e: 'schedule-changed', schedule: { startDate: string; endDate: string }): void
  (e: 'prev-step'): void
  (e: 'create-order'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const totalSelectedQuantity = computed(() => {
  return props.selectedOrderItems.reduce((sum, item) => sum + item.selectedQuantity, 0)
})

const uniqueCustomers = computed(() => {
  const customers = new Set(props.selectedOrderItems.map(item => item.customerName))
  return customers.size
})

const estimatedValue = computed(() => {
  return props.selectedOrderItems.reduce((sum, item) => {
    // 确保所有值都是有效数字
    const length = item.specifications?.length || 1000
    const width = item.specifications?.width || 1000
    const quantity = item.selectedQuantity || 0
    
    const basePrice = length * width * 0.001 * 50 // 50元/平米
    return sum + (basePrice * quantity)
  }, 0)
})

const optimizedBatchesCount = computed(() => {
  return props.batchOptimization?.batches?.length || 1
})

const estimatedTimeSaved = computed(() => {
  const timeSaved = props.batchOptimization?.timeSaved
  return (typeof timeSaved === 'number' && !isNaN(timeSaved)) ? timeSaved : 0
})

// 事件处理
const handleBatchModified = (batchId: string, config: BatchConfiguration) => {
  emit('batch-modified', batchId, config)
}

const handleConflictResolved = (conflictId: string) => {
  emit('conflict-resolved', conflictId)
}

const handleConflictIgnored = (conflictId: string) => {
  // 处理冲突忽略
  console.log('冲突已忽略:', conflictId)
}

const handleGroupingApplied = (groupId: string, itemIds: string[]) => {
  // 处理分组应用
  console.log('应用分组:', groupId, itemIds)
}

const handleOptimizationRequested = () => {
  emit('optimization-requested')
}

const handleBatchDetailsRequested = (batchId: string) => {
  // 处理批次详情请求
  
}

const handlePriorityChanged = (priority: string) => {
  emit('priority-changed', priority)
}

const handleScheduleChanged = (schedule: { startDate: string; endDate: string }) => {
  emit('schedule-changed', schedule)
}

// 工具方法
const formatNumber = (value: any): string => {
  if (value === null || value === undefined || isNaN(Number(value))) {
    return '0'
  }
  return String(Math.round(Number(value)))
}
</script>

<style scoped>
.step-two-container {
  background: #fafafa;
}

.selected-overview {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.batch-optimization-section {
  background: white;
}

.config-section {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  border-left: 1px solid #e2e8f0;
}

.step-footer {
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

/* 配置面板样式 */
.config-section h3 {
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.config-section > div {
  background: white;
  margin: 0 -1px;
}

.config-section > div:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.config-section > div:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .config-section {
    width: 320px;
  }
}

@media (max-width: 768px) {
  .flex-1.flex {
    flex-direction: column;
  }
  
  .config-section {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e2e8f0;
    max-height: 400px;
  }
  
  .step-footer .flex {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .step-footer .text-sm {
    text-align: center;
  }
}
</style>