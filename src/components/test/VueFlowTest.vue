<template>
  <div class="h-96 w-full border rounded-lg">
    <VueFlow
      v-model:nodes="nodes"
      v-model:edges="edges"
      fit-view-on-init
      class="vue-flow-test"
    >
      <Panel :position="PanelPosition.TopLeft">
        <div class="text-sm font-medium">Vue Flow 测试</div>
      </Panel>
    </VueFlow>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { VueFlow, Panel, PanelPosition } from '@vue-flow/core';
import type { Node, Edge } from '@vue-flow/core';

// 简单的测试数据
const nodes = ref<Node[]>([
  {
    id: '1',
    type: 'input',
    position: { x: 100, y: 100 },
    data: { label: '开始节点' },
  },
  {
    id: '2',
    type: 'default',
    position: { x: 300, y: 100 },
    data: { label: '处理节点' },
  },
  {
    id: '3',
    type: 'output',
    position: { x: 500, y: 100 },
    data: { label: '结束节点' },
  },
]);

const edges = ref<Edge[]>([
  {
    id: 'e1-2',
    source: '1',
    target: '2',
  },
  {
    id: 'e2-3',
    source: '2',
    target: '3',
    animated: true,
  },
]);
</script>

<style>
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';

.vue-flow-test {
  background-color: #f9fafb;
}
</style>
