<template>
  <div class="p-6 space-y-6">
    <div class="space-y-4">
      <h2 class="text-2xl font-bold">Shadcn Vue 组件演示</h2>
      <p class="text-muted-foreground">
        演示 Dialog、<PERSON><PERSON>、<PERSON><PERSON>、Tooltip 组件的最佳实践用法
      </p>
    </div>

    <!-- Dialog 演示 -->
    <Card>
      <CardHeader>
        <CardTitle>Dialog 对话框</CardTitle>
        <CardDescription>
          模态对话框组件，用于显示重要信息或收集用户输入
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- 基础 Dialog -->
        <div class="flex gap-4">
          <Dialog>
            <DialogTrigger as-child>
              <Button variant="outline">基础对话框</Button>
            </DialogTrigger>
            <DialogContent class="sm:max-w-[425px] !max-h-[90vh]">
              <DialogHeader>
                <DialogTitle>编辑资料</DialogTitle>
                <DialogDescription>
                  在这里修改您的个人资料信息。完成后点击保存。
                </DialogDescription>
              </DialogHeader>
              <div class="grid gap-4 py-4">
                <div class="grid grid-cols-4 items-center gap-4">
                  <Label for="name" class="text-right">姓名</Label>
                  <Input id="name" value="张三" class="col-span-3" />
                </div>
                <div class="grid grid-cols-4 items-center gap-4">
                  <Label for="email" class="text-right">邮箱</Label>
                  <Input id="email" value="<EMAIL>" class="col-span-3" />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">保存更改</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <!-- 带表单的 Dialog -->
          <Dialog>
            <DialogTrigger as-child>
              <Button>表单对话框</Button>
            </DialogTrigger>
            <DialogContent class="sm:max-w-[425px] !max-h-[90vh]">
              <DialogHeader>
                <DialogTitle>创建新项目</DialogTitle>
                <DialogDescription>
                  填写项目信息创建新的项目。
                </DialogDescription>
              </DialogHeader>
              <form @submit.prevent="handleSubmit" class="space-y-4">
                <div class="space-y-2">
                  <Label for="project-name">项目名称</Label>
                  <Input id="project-name" placeholder="输入项目名称" />
                </div>
                <div class="space-y-2">
                  <Label for="project-desc">项目描述</Label>
                  <Textarea id="project-desc" placeholder="输入项目描述" />
                </div>
                <DialogFooter>
                  <Button type="submit">创建项目</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>

    <!-- Drawer 演示 -->
    <Card>
      <CardHeader>
        <CardTitle>Drawer 抽屉</CardTitle>
        <CardDescription>
          从屏幕边缘滑出的面板，适合移动端或侧边栏场景
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="flex gap-4">
          <Drawer>
            <DrawerTrigger as-child>
              <Button variant="outline">右侧抽屉</Button>
            </DrawerTrigger>
            <DrawerContent>
              <div class="mx-auto w-full max-w-sm">
                <DrawerHeader>
                  <DrawerTitle>设置</DrawerTitle>
                  <DrawerDescription>
                    调整您的应用设置
                  </DrawerDescription>
                </DrawerHeader>
                <div class="p-4 pb-0">
                  <div class="space-y-4">
                    <div class="flex items-center justify-between">
                      <Label>暗色模式</Label>
                      <Button variant="outline" size="sm">切换</Button>
                    </div>
                    <div class="flex items-center justify-between">
                      <Label>通知</Label>
                      <Button variant="outline" size="sm">开启</Button>
                    </div>
                  </div>
                </div>
                <DrawerFooter>
                  <Button>保存设置</Button>
                  <DrawerClose as-child>
                    <Button variant="outline">取消</Button>
                  </DrawerClose>
                </DrawerFooter>
              </div>
            </DrawerContent>
          </Drawer>
        </div>
      </CardContent>
    </Card>

    <!-- Sonner Toast 演示 -->
    <Card>
      <CardHeader>
        <CardTitle>Sonner Toast 通知</CardTitle>
        <CardDescription>
          优雅的通知组件，支持多种类型和自定义操作
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="flex flex-wrap gap-4">
          <Button @click="showSuccessToast" variant="default">
            成功通知
          </Button>
          <Button @click="showErrorToast" variant="destructive">
            错误通知
          </Button>
          <Button @click="showWarningToast" variant="outline">
            警告通知
          </Button>
          <Button @click="showInfoToast" variant="secondary">
            信息通知
          </Button>
          <Button @click="showActionToast" variant="outline">
            带操作通知
          </Button>
          <Button @click="showPromiseToast" variant="outline">
            Promise 通知
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Tooltip 演示 -->
    <Card>
      <CardHeader>
        <CardTitle>Tooltip 工具提示</CardTitle>
        <CardDescription>
          鼠标悬停或键盘焦点时显示的提示信息
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="flex flex-wrap gap-4">
          <Tooltip>
            <TooltipTrigger as-child>
              <Button variant="outline">基础提示</Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>这是一个基础的工具提示</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger as-child>
              <Button variant="outline">
                <span>带图标</span>
                <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>这个按钮包含了一个信息图标</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger as-child>
              <Button variant="outline">延迟显示</Button>
            </TooltipTrigger>
            <TooltipContent>
              <div class="space-y-1">
                <p class="font-semibold">详细信息</p>
                <p class="text-sm">这是一个包含多行内容的工具提示</p>
                <p class="text-xs text-muted-foreground">支持富文本内容</p>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </CardContent>
    </Card>

    <!-- 组合使用演示 -->
    <Card>
      <CardHeader>
        <CardTitle>组合使用</CardTitle>
        <CardDescription>
          演示多个组件的组合使用场景
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="flex gap-4">
          <!-- Dialog 中使用 Toast -->
          <Dialog>
            <DialogTrigger as-child>
              <Button variant="outline">Dialog + Toast</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>确认操作</DialogTitle>
                <DialogDescription>
                  此操作将会删除选中的项目，确定要继续吗？
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" @click="handleCancel">取消</Button>
                <Button variant="destructive" @click="handleConfirmDelete">
                  确认删除
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <!-- Tooltip + Dialog -->
          <Tooltip>
            <TooltipTrigger as-child>
              <Dialog>
                <DialogTrigger as-child>
                  <Button variant="outline">Tooltip + Dialog</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>高级设置</DialogTitle>
                    <DialogDescription>
                      配置高级选项和参数
                    </DialogDescription>
                  </DialogHeader>
                  <div class="space-y-4">
                    <div class="flex items-center space-x-2">
                      <input type="checkbox" id="advanced" />
                      <Label for="advanced">启用高级模式</Label>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button>保存设置</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </TooltipTrigger>
            <TooltipContent>
              <p>点击打开高级设置对话框</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { toast } from 'vue-sonner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'

// Toast 通知方法
const showSuccessToast = () => {
  toast.success('操作成功！', {
    description: '您的更改已保存',
  })
}

const showErrorToast = () => {
  toast.error('操作失败！', {
    description: '请检查网络连接后重试',
  })
}

const showWarningToast = () => {
  toast.warning('注意！', {
    description: '此操作可能会影响系统性能',
  })
}

const showInfoToast = () => {
  toast.info('提示信息', {
    description: '系统将在5分钟后进行维护',
  })
}

const showActionToast = () => {
  toast('新消息', {
    description: '您有一条新的系统通知',
    action: {
      label: '查看',
      onClick: () => console.log('查看消息'),
    },
  })
}

const showPromiseToast = () => {
  const promise = new Promise<{ name: string }>((resolve) => {
    setTimeout(() => resolve({ name: '数据' }), 2000)
  })

  toast.promise(promise, {
    loading: '正在加载数据...',
    success: (data: { name: string }) => `${data.name}加载成功！`,
    error: '加载失败',
  })
}

// 表单提交处理
const handleSubmit = () => {
  toast.success('项目创建成功！')
}

// 取消操作
const handleCancel = () => {
  toast.info('操作已取消')
}

// 确认删除
const handleConfirmDelete = () => {
  toast.success('删除成功！', {
    description: '选中的项目已被删除',
  })
}
</script>
