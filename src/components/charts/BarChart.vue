<template>
  <div class="chart-container" :style="{ height: height }">
    <v-chart
      :option="chartOption"
      :theme="theme"
      autoresize
      class="w-full h-full"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { useAppStore } from '@/stores/app'

// 注册必要的组件
use([
  Canvas<PERSON>enderer,
  Bar<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

interface ChartData {
  name: string
  data: number[]
}

interface Props {
  title?: string
  data: ChartData[]
  xAxisData: string[]
  height?: string
  showLegend?: boolean
  horizontal?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  height: '300px',
  showLegend: true,
  horizontal: false
})

const appStore = useAppStore()

// 根据主题设置图表主题
const theme = computed(() => {
  return appStore.currentTheme === 'dark' ? 'dark' : undefined
})

// 图表配置
const chartOption = computed(() => ({
  title: {
    text: props.title,
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    show: props.showLegend,
    bottom: 0,
    data: props.data.map(item => item.name)
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: props.showLegend ? '15%' : '3%',
    containLabel: true
  },
  xAxis: {
    type: props.horizontal ? 'value' : 'category',
    data: props.horizontal ? undefined : props.xAxisData,
    axisLine: {
      lineStyle: {
        color: appStore.currentTheme === 'dark' ? '#4a5568' : '#e2e8f0'
      }
    }
  },
  yAxis: {
    type: props.horizontal ? 'category' : 'value',
    data: props.horizontal ? props.xAxisData : undefined,
    axisLine: {
      lineStyle: {
        color: appStore.currentTheme === 'dark' ? '#4a5568' : '#e2e8f0'
      }
    }
  },
  series: props.data.map((item, index) => ({
    name: item.name,
    type: 'bar',
    data: item.data,
    itemStyle: {
      borderRadius: props.horizontal ? [0, 4, 4, 0] : [4, 4, 0, 0]
    }
  }))
}))
</script>

<style scoped>
.chart-container {
  width: 100%;
}
</style>
