<template>
  <div class="chart-container" :style="{ height: height }">
    <v-chart
      :option="chartOption"
      :theme="theme"
      autoresize
      class="w-full h-full"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { useAppStore } from '@/stores/app'

// 注册必要的组件
use([
  Canvas<PERSON>enderer,
  Pie<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

interface ChartDataItem {
  name: string
  value: number
}

interface Props {
  title?: string
  data: ChartDataItem[]
  height?: string
  showLegend?: boolean
  radius?: string | [string, string]
  center?: [string, string]
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  height: '300px',
  showLegend: true,
  radius: '70%',
  center: () => ['50%', '50%']
})

const appStore = useAppStore()

// 根据主题设置图表主题
const theme = computed(() => {
  return appStore.currentTheme === 'dark' ? 'dark' : undefined
})

// 图表配置
const chartOption = computed(() => ({
  title: {
    text: props.title,
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    show: props.showLegend,
    bottom: 0,
    data: props.data.map(item => item.name)
  },
  series: [
    {
      name: props.title || '数据分布',
      type: 'pie',
      radius: props.radius,
      center: props.center,
      data: props.data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      itemStyle: {
        borderRadius: 4,
        borderColor: appStore.currentTheme === 'dark' ? '#1a202c' : '#ffffff',
        borderWidth: 2
      },
      label: {
        show: true,
        formatter: '{b}: {d}%'
      }
    }
  ]
}))
</script>

<style scoped>
.chart-container {
  width: 100%;
}
</style>
