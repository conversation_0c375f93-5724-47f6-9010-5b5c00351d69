<template>
  <div class="space-y-6">
    <!-- 设备分配概览 -->
    <div class="grid grid-cols-4 gap-4">
      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <Settings class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">分配设备</div>
            <div class="font-semibold">{{ assignedEquipments.length }} 台</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <Play class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">运行设备</div>
            <div class="font-semibold">{{ runningEquipmentCount }} 台</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 rounded-lg">
            <TrendingUp class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">综合效率</div>
            <div class="font-semibold">{{ overallEfficiency }}%</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-red-100 rounded-lg">
            <AlertTriangle class="w-5 h-5 text-red-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">故障设备</div>
            <div class="font-semibold">{{ faultEquipmentCount }} 台</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 已分配设备列表 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">已分配设备</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="openEquipmentSelector"
        >
          <Plus class="w-4 h-4 mr-2" />
          分配设备
        </Button>
      </div>

      <div v-if="assignedEquipments.length === 0" class="text-sm text-muted-foreground text-center py-8 border rounded-lg">
        <Settings class="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
        该工作中心尚未分配任何设备
      </div>

      <div v-else class="space-y-3 max-h-64 overflow-y-auto list-scroll-area">
        <Card
          v-for="equipment in assignedEquipments"
          :key="equipment.id"
          class="p-4"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div :class="['p-2 rounded-lg', getEquipmentStatusColor(equipment.status)]">
                <component :is="getEquipmentIcon(equipment.status)" class="w-5 h-5" />
              </div>
              <div>
                <div class="font-medium">{{ equipment.name }}</div>
                <div class="text-sm text-muted-foreground">{{ equipment.model }} • {{ equipment.id }}</div>
              </div>
            </div>
            
            <div class="flex items-center gap-2">
              <Badge :variant="getStatusBadgeVariant(equipment.status)">
                {{ getStatusLabel(equipment.status) }}
              </Badge>
              <Badge variant="outline">{{ equipment.location }}</Badge>
              
              <Button
                v-if="isEditing"
                variant="ghost"
                size="sm"
                @click="removeEquipment(equipment.id)"
              >
                <Unlink class="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div class="mt-3 grid grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-muted-foreground">效率:</span>
              <span class="ml-1 font-medium">{{ equipment.parameters?.efficiency || 0 }}%</span>
            </div>
            <div>
              <span class="text-muted-foreground">功率:</span>
              <span class="ml-1 font-medium">{{ equipment.parameters?.power || 0 }} kW</span>
            </div>
            <div>
              <span class="text-muted-foreground">产能贡献:</span>
              <span class="ml-1 font-medium">{{ calculateCapacityContribution(equipment) }}%</span>
            </div>
            <div>
              <span class="text-muted-foreground">利用率:</span>
              <span class="ml-1 font-medium">{{ calculateUtilization(equipment) }}%</span>
            </div>
          </div>

          <!-- 设备详细参数 -->
          <div v-if="equipment.parameters" class="mt-3 pt-3 border-t">
            <div class="grid grid-cols-2 gap-4 text-xs">
              <div v-if="equipment.parameters.max_width">
                <span class="text-muted-foreground">最大宽度:</span>
                <span class="ml-1">{{ equipment.parameters.max_width }}mm</span>
              </div>
              <div v-if="equipment.parameters.max_height">
                <span class="text-muted-foreground">最大高度:</span>
                <span class="ml-1">{{ equipment.parameters.max_height }}mm</span>
              </div>
              <div v-if="equipment.parameters.speed">
                <span class="text-muted-foreground">速度:</span>
                <span class="ml-1">{{ equipment.parameters.speed }} m/min</span>
              </div>
              <div v-if="equipment.parameters.area">
                <span class="text-muted-foreground">面积:</span>
                <span class="ml-1">{{ equipment.parameters.area }} m²</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 设备选择器对话框 -->
    <Dialog v-model:open="isEquipmentSelectorOpen">
      <DialogContent class="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>选择设备</DialogTitle>
        </DialogHeader>
        
        <div class="space-y-4">
          <!-- 搜索和筛选 -->
          <div class="flex gap-2">
            <div class="flex-1 relative">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                v-model="equipmentSearchQuery"
                placeholder="搜索设备名称或型号..."
                class="pl-10"
              />
            </div>
            <Select v-model="equipmentTypeFilter">
              <SelectTrigger class="w-40">
                <SelectValue placeholder="设备类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="cutting">切割设备</SelectItem>
                <SelectItem value="edging">磨边设备</SelectItem>
                <SelectItem value="tempering">钢化设备</SelectItem>
                <SelectItem value="drilling">钻孔设备</SelectItem>
                <SelectItem value="washing">清洗设备</SelectItem>
                <SelectItem value="assembly">装配设备</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 可选设备列表 -->
          <div class="max-h-96 overflow-y-auto">
            <div v-if="filteredAvailableEquipments.length === 0" class="text-center py-8 text-muted-foreground">
              没有找到可分配的设备
            </div>
            
            <div v-else class="space-y-2">
              <div
                v-for="equipment in filteredAvailableEquipments"
                :key="equipment.id"
                class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
              >
                <div class="flex items-center gap-3">
                  <div :class="['p-2 rounded-lg', getEquipmentStatusColor(equipment.status)]">
                    <component :is="getEquipmentIcon(equipment.status)" class="w-4 h-4" />
                  </div>
                  <div>
                    <div class="font-medium">{{ equipment.name }}</div>
                    <div class="text-sm text-muted-foreground">{{ equipment.model }} • {{ equipment.location }}</div>
                  </div>
                </div>
                
                <div class="flex items-center gap-2">
                  <Badge :variant="getStatusBadgeVariant(equipment.status)">
                    {{ getStatusLabel(equipment.status) }}
                  </Badge>
                  <Button
                    size="sm"
                    @click="assignEquipment(equipment)"
                    :disabled="equipment.status === 'fault'"
                  >
                    分配
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isEquipmentSelectorOpen = false">
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 设备能力约束配置 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">设备能力约束</Label>

      <div v-if="assignedEquipments.length === 0" class="text-center py-8 text-muted-foreground">
        暂无设备可配置能力约束
      </div>

      <div v-else class="space-y-4">
        <Card
          v-for="equipment in assignedEquipments"
          :key="equipment.id"
          class="p-4"
        >
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div :class="['p-2 rounded-lg', getEquipmentStatusColor(equipment.status)]">
                  <component :is="getEquipmentIcon(equipment.status)" class="w-5 h-5" />
                </div>
                <div>
                  <div class="font-medium">{{ equipment.name }}</div>
                  <div class="text-sm text-muted-foreground">{{ equipment.model }}</div>
                </div>
              </div>
              <Button
                v-if="isEditing"
                variant="outline"
                size="sm"
                @click="configureEquipmentCapability(equipment)"
              >
                <Settings class="w-4 h-4 mr-2" />
                配置能力
              </Button>
            </div>

            <!-- 尺寸约束展示 -->
            <div class="grid grid-cols-3 gap-4 text-sm">
              <div class="space-y-1">
                <div class="text-muted-foreground">尺寸限制</div>
                <div class="font-medium">
                  {{ getEquipmentSizeLimit(equipment) }}
                </div>
              </div>
              <div class="space-y-1">
                <div class="text-muted-foreground">最优规格</div>
                <div class="font-medium">
                  {{ getOptimalSizeRange(equipment) }}
                </div>
              </div>
              <div class="space-y-1">
                <div class="text-muted-foreground">支持工艺</div>
                <div class="font-medium">
                  {{ getSupportedProcesses(equipment) }}
                </div>
              </div>
            </div>

            <!-- 能力匹配度分析 -->
            <div class="space-y-2">
              <div class="text-sm font-medium">规格覆盖能力</div>
              <div class="grid grid-cols-4 gap-2 text-xs">
                <div class="text-center p-2 bg-green-50 rounded">
                  <div class="font-medium text-green-700">小规格</div>
                  <div class="text-green-600">{{ getCapabilityCoverage(equipment, 'small') }}%</div>
                </div>
                <div class="text-center p-2 bg-blue-50 rounded">
                  <div class="font-medium text-blue-700">中规格</div>
                  <div class="text-blue-600">{{ getCapabilityCoverage(equipment, 'medium') }}%</div>
                </div>
                <div class="text-center p-2 bg-orange-50 rounded">
                  <div class="font-medium text-orange-700">大规格</div>
                  <div class="text-orange-600">{{ getCapabilityCoverage(equipment, 'large') }}%</div>
                </div>
                <div class="text-center p-2 bg-red-50 rounded">
                  <div class="font-medium text-red-700">超大规格</div>
                  <div class="text-red-600">{{ getCapabilityCoverage(equipment, 'extra_large') }}%</div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 智能分配策略 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">智能分配策略</Label>

      <Card class="p-4">
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="allocationStrategy">分配策略</Label>
              <Select v-model="allocationStrategy" :disabled="!isEditing">
                <SelectTrigger>
                  <SelectValue placeholder="选择分配策略" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="best_fit">最佳匹配</SelectItem>
                  <SelectItem value="load_balance">负载均衡</SelectItem>
                  <SelectItem value="efficiency_first">效率优先</SelectItem>
                  <SelectItem value="cost_optimal">成本最优</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div class="space-y-2">
              <Label for="sizeThreshold">大小规格阈值</Label>
              <Input
                id="sizeThreshold"
                v-model.number="sizeThreshold"
                :disabled="!isEditing"
                type="number"
                placeholder="2.0"
                class="text-sm"
              />
              <p class="text-xs text-muted-foreground">面积阈值(m²)，超过此值使用大规格设备</p>
            </div>
          </div>

          <div class="space-y-2">
            <Label>优化目标权重</Label>
            <div class="grid grid-cols-4 gap-4">
              <div class="space-y-1">
                <Label class="text-xs">能力匹配</Label>
                <Input
                  v-model.number="optimizationWeights.capability"
                  :disabled="!isEditing"
                  type="number"
                  min="0"
                  max="100"
                  class="h-8 text-xs"
                />
              </div>
              <div class="space-y-1">
                <Label class="text-xs">效率</Label>
                <Input
                  v-model.number="optimizationWeights.efficiency"
                  :disabled="!isEditing"
                  type="number"
                  min="0"
                  max="100"
                  class="h-8 text-xs"
                />
              </div>
              <div class="space-y-1">
                <Label class="text-xs">成本</Label>
                <Input
                  v-model.number="optimizationWeights.cost"
                  :disabled="!isEditing"
                  type="number"
                  min="0"
                  max="100"
                  class="h-8 text-xs"
                />
              </div>
              <div class="space-y-1">
                <Label class="text-xs">可用性</Label>
                <Input
                  v-model.number="optimizationWeights.availability"
                  :disabled="!isEditing"
                  type="number"
                  min="0"
                  max="100"
                  class="h-8 text-xs"
                />
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 排产支持分析 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">排产支持分析</Label>

      <div class="grid grid-cols-2 gap-4">
        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">设备互补性分析</div>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span>规格覆盖互补度</span>
                <span class="font-medium">{{ complementaryCoverage }}%</span>
              </div>
              <div class="flex justify-between">
                <span>负载分担能力</span>
                <span class="font-medium">{{ loadSharingCapability }}%</span>
              </div>
              <div class="flex justify-between">
                <span>故障冗余度</span>
                <span class="font-medium">{{ faultRedundancy }}%</span>
              </div>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">排产建议</div>
            <div class="space-y-1 text-sm">
              <div
                v-for="(suggestion, index) in schedulingSuggestions"
                :key="index"
                class="flex items-start gap-2"
              >
                <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>{{ suggestion }}</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Settings, Plus, Play, TrendingUp, AlertTriangle, Unlink, Search,
  Wrench, Square, Pause
} from 'lucide-vue-next';
import type { WorkCenter, Equipment } from '@/types/masterdata';
import { masterDataService } from '@/services/masterDataService';

interface Props {
  workCenter?: WorkCenter | null;
  isEditing: boolean;
}

interface Emits {
  (e: 'update-equipment', data: { equipmentIds: string[] }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const assignedEquipments = ref<Equipment[]>([]);
const availableEquipments = ref<Equipment[]>([]);
const isEquipmentSelectorOpen = ref(false);
const equipmentSearchQuery = ref('');
const equipmentTypeFilter = ref('all');

// 智能分配策略配置
const allocationStrategy = ref('best_fit');
const sizeThreshold = ref(2.0);
const optimizationWeights = ref({
  capability: 40,
  efficiency: 30,
  cost: 20,
  availability: 10
});

// 分析数据
const complementaryCoverage = ref(85);
const loadSharingCapability = ref(92);
const faultRedundancy = ref(78);
const schedulingSuggestions = ref([
  '小规格订单(≤2m²)优先分配给1号设备',
  '大规格订单(>2m²)优先分配给2号设备',
  '批量订单考虑设备负载均衡',
  '紧急订单选择当前空闲设备'
]);

// 计算属性
const runningEquipmentCount = computed(() => {
  return assignedEquipments.value.filter(eq => eq.status === 'running').length;
});

const faultEquipmentCount = computed(() => {
  return assignedEquipments.value.filter(eq => eq.status === 'fault').length;
});

const overallEfficiency = computed(() => {
  if (assignedEquipments.value.length === 0) return 0;
  const totalEfficiency = assignedEquipments.value.reduce((sum, eq) => 
    sum + (eq.parameters?.efficiency || 0), 0
  );
  return Math.round(totalEfficiency / assignedEquipments.value.length);
});

const theoreticalCapacity = computed(() => {
  return assignedEquipments.value.reduce((sum, eq) => {
    const baseCapacity = calculateEquipmentCapacity(eq);
    return sum + baseCapacity;
  }, 0);
});

const actualCapacity = computed(() => {
  return assignedEquipments.value.reduce((sum, eq) => {
    const baseCapacity = calculateEquipmentCapacity(eq);
    const efficiency = (eq.parameters?.efficiency || 100) / 100;
    const statusMultiplier = eq.status === 'running' ? 1 : eq.status === 'idle' ? 0.5 : 0;
    return sum + (baseCapacity * efficiency * statusMultiplier);
  }, 0);
});

const filteredAvailableEquipments = computed(() => {
  let filtered = availableEquipments.value;
  
  // 按搜索关键词筛选
  if (equipmentSearchQuery.value) {
    const query = equipmentSearchQuery.value.toLowerCase();
    filtered = filtered.filter(eq => 
      eq.name.toLowerCase().includes(query) || 
      eq.model.toLowerCase().includes(query)
    );
  }
  
  // 按设备类型筛选
  if (equipmentTypeFilter.value !== 'all') {
    filtered = filtered.filter(eq => {
      const type = getEquipmentType(eq.model);
      return type === equipmentTypeFilter.value;
    });
  }
  
  return filtered;
});

// 方法
const getEquipmentIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    running: Play,
    idle: Pause,
    fault: Square,
    maintenance: Wrench
  };
  return iconMap[status] || Pause;
};

const getEquipmentStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    running: 'bg-green-100 text-green-600',
    idle: 'bg-gray-100 text-gray-600',
    fault: 'bg-red-100 text-red-600',
    maintenance: 'bg-yellow-100 text-yellow-600'
  };
  return colorMap[status] || 'bg-gray-100 text-gray-600';
};

const getStatusBadgeVariant = (status: string) => {
  const variantMap: Record<string, string> = {
    running: 'default',
    idle: 'secondary',
    fault: 'destructive',
    maintenance: 'outline'
  };
  return variantMap[status] || 'secondary';
};

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    running: '运行中',
    idle: '空闲',
    fault: '故障',
    maintenance: '维护中'
  };
  return labelMap[status] || '未知';
};

const getEquipmentType = (model: string): string => {
  if (model.includes('CUT')) return 'cutting';
  if (model.includes('EDGE')) return 'edging';
  if (model.includes('TEMP')) return 'tempering';
  if (model.includes('DRILL')) return 'drilling';
  if (model.includes('WASH')) return 'washing';
  if (model.includes('ASSEM')) return 'assembly';
  return 'other';
};

const calculateEquipmentCapacity = (equipment: Equipment): number => {
  const params = equipment.parameters || {};
  
  // 根据设备类型计算基础产能
  if (params.speed) {
    return params.speed * 8; // 8小时工作制
  } else if (params.area) {
    return params.area * 3; // 钢化炉按炉次计算
  } else if (params.max_holes_per_minute) {
    return params.max_holes_per_minute * 60 * 8; // 钻孔设备
  } else {
    return 100; // 默认产能
  }
};

const calculateCapacityContribution = (equipment: Equipment): number => {
  if (theoreticalCapacity.value === 0) return 0;
  const equipmentCapacity = calculateEquipmentCapacity(equipment);
  return Math.round((equipmentCapacity / theoreticalCapacity.value) * 100);
};

const calculateUtilization = (equipment: Equipment): number => {
  // 简化的利用率计算
  const efficiency = equipment.parameters?.efficiency || 100;
  const statusMultiplier = equipment.status === 'running' ? 1 : 
                          equipment.status === 'idle' ? 0.3 : 0;
  return Math.round(efficiency * statusMultiplier / 100 * 100);
};

const openEquipmentSelector = () => {
  isEquipmentSelectorOpen.value = true;
  loadAvailableEquipments();
};

const assignEquipment = (equipment: Equipment) => {
  assignedEquipments.value.push(equipment);
  availableEquipments.value = availableEquipments.value.filter(eq => eq.id !== equipment.id);
  
  // 更新工作中心的设备ID列表
  const equipmentIds = assignedEquipments.value.map(eq => eq.id);
  emit('update-equipment', { equipmentIds });
};

const removeEquipment = (equipmentId: string) => {
  const equipment = assignedEquipments.value.find(eq => eq.id === equipmentId);
  if (equipment) {
    assignedEquipments.value = assignedEquipments.value.filter(eq => eq.id !== equipmentId);
    availableEquipments.value.push(equipment);
    
    // 更新工作中心的设备ID列表
    const equipmentIds = assignedEquipments.value.map(eq => eq.id);
    emit('update-equipment', { equipmentIds });
  }
};

const loadAssignedEquipments = async () => {
  if (!props.workCenter) return;
  
  try {
    const equipments = await masterDataService.getWorkCenterEquipments(props.workCenter.id);
    assignedEquipments.value = equipments;
  } catch (error) {
    console.error('加载分配设备失败:', error);
  }
};

const loadAvailableEquipments = async () => {
  try {
    const allEquipments = await masterDataService.getEquipments();
    const assignedIds = assignedEquipments.value.map(eq => eq.id);
    availableEquipments.value = allEquipments.filter(eq => !assignedIds.includes(eq.id));
  } catch (error) {
    console.error('加载可用设备失败:', error);
  }
};

// 新增的设备能力相关方法
const getEquipmentSizeLimit = (equipment: Equipment): string => {
  const params = equipment.parameters || {};
  if (params.max_width && params.max_height) {
    return `${params.max_width}×${params.max_height}mm`;
  }
  return '未配置';
};

const getOptimalSizeRange = (equipment: Equipment): string => {
  // 基于设备型号推断最优规格范围
  if (equipment.model.includes('1830')) {
    return '500-1600×300-1600mm';
  } else if (equipment.model.includes('3660')) {
    return '800-2400×1000-3500mm';
  }
  return '未配置';
};

const getSupportedProcesses = (equipment: Equipment): string => {
  // 基于设备类型返回支持的工艺
  if (equipment.model.includes('EDGE')) {
    return '直边、斜边、圆边';
  }
  return '标准磨边';
};

const getCapabilityCoverage = (equipment: Equipment, sizeCategory: string): number => {
  // 模拟不同规格的覆盖能力
  const coverageMap: Record<string, Record<string, number>> = {
    'EQ-EDGE-001': { small: 100, medium: 95, large: 60, extra_large: 0 },
    'EQ-EDGE-002': { small: 80, medium: 100, large: 100, extra_large: 85 }
  };

  return coverageMap[equipment.id]?.[sizeCategory] || 0;
};

const configureEquipmentCapability = (equipment: Equipment) => {
  // 打开设备能力配置对话框
  console.log('配置设备能力:', equipment.name);
  // 这里可以打开一个专门的设备能力配置对话框
};

// 生命周期
onMounted(async () => {
  await loadAssignedEquipments();
});
</script>
