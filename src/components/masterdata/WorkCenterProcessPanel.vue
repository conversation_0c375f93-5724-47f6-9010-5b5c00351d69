<template>
  <div class="space-y-6">
    <!-- 工序分配概览 -->
    <div class="grid grid-cols-3 gap-4">
      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <Cog class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">分配工序</div>
            <div class="font-semibold">{{ assignedProcesses.length }} 个</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <CheckCircle class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">启用工序</div>
            <div class="font-semibold">{{ activeProcessCount }} 个</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 rounded-lg">
            <Clock class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">平均工时</div>
            <div class="font-semibold">{{ averageProcessTime }} 分钟</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 已分配工序列表 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">已分配工序</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="openProcessSelector"
        >
          <Plus class="w-4 h-4 mr-2" />
          分配工序
        </Button>
      </div>

      <div v-if="assignedProcesses.length === 0" class="text-sm text-muted-foreground text-center py-8 border rounded-lg">
        <Cog class="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
        该工作中心尚未分配任何工序
      </div>

      <div v-else class="space-y-3 max-h-64 overflow-y-auto list-scroll-area">
        <Card
          v-for="process in assignedProcesses"
          :key="process.id"
          class="p-4"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div :class="['p-2 rounded-lg', getProcessTypeColor(process.category)]">
                <component :is="getProcessIcon(process.category)" class="w-5 h-5" />
              </div>
              <div>
                <div class="font-medium">{{ process.name }}</div>
                <div class="text-sm text-muted-foreground">{{ process.id }} • {{ process.category }}</div>
              </div>
            </div>
            
            <div class="flex items-center gap-2">
              <Badge :variant="process.isActive ? 'default' : 'secondary'">
                {{ process.isActive ? '启用' : '禁用' }}
              </Badge>
              <Badge variant="outline">{{ process.standardTime }}分钟</Badge>
              
              <Button
                v-if="isEditing"
                variant="ghost"
                size="sm"
                @click="removeProcess(process.id)"
              >
                <Unlink class="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div class="mt-3 grid grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-muted-foreground">标准工时:</span>
              <span class="ml-1 font-medium">{{ process.standardTime }} 分钟</span>
            </div>
            <div>
              <span class="text-muted-foreground">设置时间:</span>
              <span class="ml-1 font-medium">{{ process.setupTime || 0 }} 分钟</span>
            </div>
            <div>
              <span class="text-muted-foreground">质量要求:</span>
              <span class="ml-1 font-medium">{{ process.qualityLevel || 'A' }}级</span>
            </div>
            <div>
              <span class="text-muted-foreground">优先级:</span>
              <span class="ml-1 font-medium">{{ getPriorityLabel(process.priority) }}</span>
            </div>
          </div>

          <div v-if="process.description" class="mt-2 text-sm text-muted-foreground">
            {{ process.description }}
          </div>
        </Card>
      </div>
    </div>

    <!-- 工序能力匹配分析 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">工序能力匹配分析</Label>
      
      <div class="grid grid-cols-2 gap-4">
        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">匹配度分析</div>
            <div class="space-y-2">
              <div
                v-for="process in assignedProcesses"
                :key="process.id"
                class="flex items-center justify-between"
              >
                <span class="text-sm">{{ process.name }}</span>
                <div class="flex items-center gap-2">
                  <div class="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      :class="['h-2 rounded-full', getMatchingColor(calculateMatching(process))]"
                      :style="{ width: `${calculateMatching(process)}%` }"
                    ></div>
                  </div>
                  <span class="text-sm font-medium w-8">{{ calculateMatching(process) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">改进建议</div>
            <div class="space-y-1">
              <div
                v-for="(suggestion, index) in improvementSuggestions"
                :key="index"
                class="text-sm flex items-start gap-2"
              >
                <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>{{ suggestion }}</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 工序排程优先级 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">工序排程优先级</Label>
      
      <div v-if="assignedProcesses.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无工序可排序
      </div>

      <div v-else class="space-y-2">
        <div
          v-for="(process, index) in sortedProcesses"
          :key="process.id"
          class="flex items-center justify-between p-3 border rounded-lg"
        >
          <div class="flex items-center gap-3">
            <div class="flex flex-col gap-1">
              <Button
                v-if="isEditing && index > 0"
                variant="ghost"
                size="sm"
                class="h-4 w-4 p-0"
                @click="moveProcessUp(index)"
              >
                <ChevronUp class="w-3 h-3" />
              </Button>
              <Button
                v-if="isEditing && index < sortedProcesses.length - 1"
                variant="ghost"
                size="sm"
                class="h-4 w-4 p-0"
                @click="moveProcessDown(index)"
              >
                <ChevronDown class="w-3 h-3" />
              </Button>
            </div>
            
            <div class="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">
              {{ index + 1 }}
            </div>
            
            <div>
              <div class="font-medium">{{ process.name }}</div>
              <div class="text-sm text-muted-foreground">{{ process.standardTime }}分钟</div>
            </div>
          </div>
          
          <div class="flex items-center gap-2">
            <Badge :variant="getPriorityBadgeVariant(process.priority)">
              {{ getPriorityLabel(process.priority) }}
            </Badge>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  Cog, CheckCircle, Clock, Plus, Unlink, ChevronUp, ChevronDown,
  Settings, Wrench, Factory, Scissors
} from 'lucide-vue-next';
import type { WorkCenter, ProcessStep } from '@/types/masterdata';
import { masterDataService } from '@/services/masterDataService';

interface Props {
  workCenter?: WorkCenter | null;
  isEditing: boolean;
}

interface Emits {
  (e: 'update-processes', data: { processIds: string[] }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const assignedProcesses = ref<ProcessStep[]>([]);

// 计算属性
const activeProcessCount = computed(() => {
  return assignedProcesses.value.filter(p => p.isActive).length;
});

const averageProcessTime = computed(() => {
  if (assignedProcesses.value.length === 0) return 0;
  const totalTime = assignedProcesses.value.reduce((sum, p) => sum + (p.standardTime || 0), 0);
  return Math.round(totalTime / assignedProcesses.value.length);
});

const sortedProcesses = computed(() => {
  return [...assignedProcesses.value].sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) - 
           (priorityOrder[a.priority as keyof typeof priorityOrder] || 0);
  });
});

const improvementSuggestions = computed(() => {
  const suggestions = [];
  
  if (assignedProcesses.value.length > 5) {
    suggestions.push('工序数量较多，建议优化工序流程');
  }
  
  const lowMatchingProcesses = assignedProcesses.value.filter(p => calculateMatching(p) < 80);
  if (lowMatchingProcesses.length > 0) {
    suggestions.push('部分工序匹配度较低，建议调整设备配置');
  }
  
  if (suggestions.length === 0) {
    suggestions.push('当前工序配置良好，建议定期评估优化');
  }
  
  return suggestions;
});

// 方法
const getProcessIcon = (category: string) => {
  const iconMap: Record<string, any> = {
    cutting: Scissors,
    processing: Settings,
    assembly: Factory,
    quality: CheckCircle
  };
  return iconMap[category] || Cog;
};

const getProcessTypeColor = (category: string) => {
  const colorMap: Record<string, string> = {
    cutting: 'bg-blue-100 text-blue-600',
    processing: 'bg-green-100 text-green-600',
    assembly: 'bg-orange-100 text-orange-600',
    quality: 'bg-purple-100 text-purple-600'
  };
  return colorMap[category] || 'bg-gray-100 text-gray-600';
};

const getPriorityLabel = (priority: string) => {
  const labelMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低'
  };
  return labelMap[priority] || '中';
};

const getPriorityBadgeVariant = (priority: string) => {
  const variantMap: Record<string, string> = {
    high: 'destructive',
    medium: 'default',
    low: 'secondary'
  };
  return variantMap[priority] || 'default';
};

const calculateMatching = (process: ProcessStep): number => {
  // 简化的匹配度计算
  // 实际应该基于工作中心能力和工序要求
  return Math.floor(Math.random() * 20) + 80; // 80-100%
};

const getMatchingColor = (matching: number): string => {
  if (matching >= 90) return 'bg-green-500';
  if (matching >= 80) return 'bg-yellow-500';
  return 'bg-red-500';
};

const openProcessSelector = () => {
  // 打开工序选择器
  console.log('打开工序选择器');
};

const removeProcess = (processId: string) => {
  assignedProcesses.value = assignedProcesses.value.filter(p => p.id !== processId);
  const processIds = assignedProcesses.value.map(p => p.id);
  emit('update-processes', { processIds });
};

const moveProcessUp = (index: number) => {
  if (index > 0) {
    const processes = [...assignedProcesses.value];
    [processes[index], processes[index - 1]] = [processes[index - 1], processes[index]];
    assignedProcesses.value = processes;
  }
};

const moveProcessDown = (index: number) => {
  if (index < assignedProcesses.value.length - 1) {
    const processes = [...assignedProcesses.value];
    [processes[index], processes[index + 1]] = [processes[index + 1], processes[index]];
    assignedProcesses.value = processes;
  }
};

const loadAssignedProcesses = async () => {
  if (!props.workCenter) return;
  
  try {
    const processes = await masterDataService.getWorkCenterProcessSteps(props.workCenter.id);
    assignedProcesses.value = processes;
  } catch (error) {
    console.error('加载分配工序失败:', error);
  }
};

// 生命周期
onMounted(async () => {
  await loadAssignedProcesses();
});
</script>
