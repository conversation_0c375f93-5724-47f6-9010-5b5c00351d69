<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { VueFlow, useVueFlow, Panel, PanelPosition } from '@vue-flow/core';
import type { Node, Edge, Connection } from '@vue-flow/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Save, Trash2, GitBranch, Warehouse } from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { ProcessSegment, WipWarehouse, Routing, ProductFamily } from '@/types/masterdata';

// Props
interface Props {
  routing?: Routing | null;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  routing: null,
  readonly: false,
});

// Emits
const emit = defineEmits<{
  save: [data: Routing];
  cancel: [];
}>();

// Store
const masterDataStore = useMasterDataStore();

// Vue Flow instance
const { onConnect, addEdges, removeNodes } = useVueFlow();

// Reactive state
const nodes = ref<Node[]>([]);
const edges = ref<Edge[]>([]);
const localRouting = ref<Partial<Routing>>({ name: '', description: '', productFamilyId: '' });

const selectedNodeId = ref<string | null>(null);
const showEntitySelector = ref(false);
const selectedNodeType = ref<'ProcessSegment' | 'WipWarehouse' | null>(null);

// Node types for the palette
const nodeTypes = [
  {
    type: 'ProcessSegment',
    label: '工艺段',
    icon: GitBranch,
    color: 'bg-purple-500',
  },
  {
    type: 'WipWarehouse',
    label: 'WIP仓库',
    icon: Warehouse,
    color: 'bg-orange-500',
  },
];

// --- Methods ---

const loadRouting = (routing: Routing | null) => {
  if (!routing) {
    nodes.value = [];
    edges.value = [];
    localRouting.value = { name: '', description: '', productFamilyId: '', version: 1, isActive: true };
    return;
  }

  localRouting.value = { ...routing };

  const newNodes: Node[] = routing.nodes.map(node => {
    const entity = node.type === 'ProcessSegment'
      ? masterDataStore.getProcessSegmentById(node.entityId)
      : masterDataStore.getWipWarehouseById(node.entityId);

    return {
      id: node.nodeId,
      type: 'default',
      position: node.position,
      data: {
        label: entity?.name || '未知节点',
        type: node.type,
        entityId: node.entityId,
      },
    };
  });

  const newEdges: Edge[] = routing.edges.map((edge, index) => ({
    id: `edge-${index}`,
    source: edge.sourceNodeId,
    target: edge.targetNodeId,
  }));

  nodes.value = newNodes;
  edges.value = newEdges;
};

const openEntitySelector = (type: 'ProcessSegment' | 'WipWarehouse') => {
  if (props.readonly) return;
  selectedNodeType.value = type;
  showEntitySelector.value = true;
};

const addNodeWithEntity = (entity: ProcessSegment | WipWarehouse) => {
  if (!selectedNodeType.value) return;

  const newNode: Node = {
    id: `node_${Date.now()}`,
    type: 'default',
    position: { x: Math.random() * 400 + 100, y: Math.random() * 200 + 50 },
    data: {
      label: entity.name,
      type: selectedNodeType.value,
      entityId: entity.id,
    },
  };

  nodes.value.push(newNode);
  showEntitySelector.value = false;
  selectedNodeType.value = null;
  toast.success(`已添加节点: ${entity.name}`);
};

const onConnectHandler = (connection: Connection) => {
  if (props.readonly) return;
  const edge: Edge = {
    id: `edge_${Date.now()}`,
    source: connection.source!,
    target: connection.target!,
    type: 'default',
  };
  addEdges([edge]);
};

const save = () => {
  if (!localRouting.value.name || !localRouting.value.productFamilyId) {
    toast.error('请输入工艺路线名称并选择一个产品族');
    return;
  }

  const finalRouting: Routing = {
    ...localRouting.value,
    id: props.routing?.id || `rt_${Date.now()}`,
    name: localRouting.value.name,
    productFamilyId: localRouting.value.productFamilyId,
    version: props.routing?.version || 1,
    isActive: props.routing?.isActive === false ? false : true,
    nodes: nodes.value.map(node => ({
      nodeId: node.id,
      type: node.data.type,
      entityId: node.data.entityId,
      position: node.position,
    })),
    edges: edges.value.map(edge => ({
      sourceNodeId: edge.source,
      targetNodeId: edge.target,
    })),
  };

  emit('save', finalRouting);
};

const deleteSelectedNode = () => {
  if (selectedNodeId.value) {
    removeNodes([selectedNodeId.value]);
    selectedNodeId.value = null;
    toast.success('节点已删除');
  }
};

onConnect(onConnectHandler);

onMounted(async () => {
  await Promise.all([
    masterDataStore.fetchProcessSegments(),
    masterDataStore.fetchWipWarehouses(),
    masterDataStore.fetchProductFamilies(),
  ]);
  
  if (props.routing) {
    loadRouting(props.routing);
  } else {
    loadRouting(null);
  }
});

watch(() => props.routing, (newVal) => {
  loadRouting(newVal);
});
</script>

<template>
  <div class="h-full flex flex-col">
    <div class="p-4 border-b grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <Label for="routing-name">工艺路线名称 *</Label>
        <Input id="routing-name" v-model="localRouting.name" :disabled="readonly" />
      </div>
      <div>
        <Label for="product-family">关联产品族 *</Label>
        <Select v-model="localRouting.productFamilyId" :disabled="readonly">
          <SelectTrigger>
            <SelectValue placeholder="请选择产品族" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="pf in masterDataStore.productFamilies" :key="pf.id" :value="pf.id">
              {{ pf.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label for="routing-desc">描述</Label>
        <Input id="routing-desc" v-model="localRouting.description" :disabled="readonly" />
      </div>
    </div>
    <div class="flex-1 flex">
      <div v-if="!readonly" class="w-64 border-r bg-muted/30 p-4 space-y-4">
        <h3 class="font-semibold mb-3">节点工具箱</h3>
        <div class="space-y-2">
          <div v-for="nodeType in nodeTypes" :key="nodeType.type"
            class="p-3 border rounded-lg cursor-pointer hover:bg-accent"
            @click="openEntitySelector(nodeType.type as 'ProcessSegment' | 'WipWarehouse')">
            <div class="flex items-center gap-3">
              <div :class="[nodeType.color, 'p-2 rounded text-white']">
                <component :is="nodeType.icon" class="h-4 w-4" />
              </div>
              <div class="font-medium text-sm">{{ nodeType.label }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-1 relative">
        <VueFlow v-model:nodes="nodes" v-model:edges="edges" :nodes-draggable="!readonly"
          :nodes-connectable="!readonly" :elements-selectable="!readonly" fit-view-on-init>
          <Panel v-if="!readonly" :position="PanelPosition.TopRight">
            <Button size="sm" variant="outline" @click="deleteSelectedNode" :disabled="!selectedNodeId">
              <Trash2 class="h-4 w-4" />
            </Button>
          </Panel>
        </VueFlow>

        <div v-if="showEntitySelector" class="absolute inset-0 bg-black/50 flex items-center justify-center z-50"
          @click="showEntitySelector = false">
          <Card class="w-96 max-h-[60vh] overflow-hidden flex flex-col" @click.stop>
            <CardHeader>
              <CardTitle>选择{{ selectedNodeType === 'ProcessSegment' ? '工艺段' : 'WIP仓库' }}</CardTitle>
            </CardHeader>
            <CardContent class="flex-1 overflow-y-auto">
              <div class="space-y-2">
                <template v-if="selectedNodeType === 'ProcessSegment'">
                  <div v-if="!masterDataStore.processSegments.length" class="text-center py-4 text-muted-foreground">无可用工艺段</div>
                  <div v-for="segment in masterDataStore.processSegments" :key="segment.id"
                    class="p-3 border rounded-lg cursor-pointer hover:bg-accent"
                    @click="addNodeWithEntity(segment)">
                    <div class="font-medium">{{ segment.name }}</div>
                    <div class="text-sm text-muted-foreground">{{ segment.id }}</div>
                  </div>
                </template>
                <template v-if="selectedNodeType === 'WipWarehouse'">
                  <div v-if="!masterDataStore.wipWarehouses.length" class="text-center py-4 text-muted-foreground">无可用WIP仓库</div>
                  <div v-for="wh in masterDataStore.wipWarehouses" :key="wh.id"
                    class="p-3 border rounded-lg cursor-pointer hover:bg-accent"
                    @click="addNodeWithEntity(wh)">
                    <div class="font-medium">{{ wh.name }}</div>
                  </div>
                </template>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    <div class="p-4 border-t flex justify-end gap-2">
      <Button variant="outline" @click="emit('cancel')">取消</Button>
      <Button v-if="!readonly" @click="save">
        <Save class="h-4 w-4 mr-2" />
        保存
      </Button>
    </div>
  </div>
</template>