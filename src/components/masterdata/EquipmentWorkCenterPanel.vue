<template>
  <div class="space-y-6">
    <!-- 当前关联的工作中心 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">关联的工作中心</Label>
      
      <div v-if="associatedWorkCenters.length === 0" class="text-sm text-muted-foreground text-center py-8 border rounded-lg">
        <Factory class="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
        该设备尚未分配到任何工作中心
      </div>

      <div v-else class="space-y-3">
        <Card
          v-for="workCenter in associatedWorkCenters"
          :key="workCenter.id"
          class="p-4"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-blue-100 rounded-lg">
                <Factory class="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <div class="font-medium">{{ workCenter.name }}</div>
                <div class="text-sm text-muted-foreground">{{ workCenter.id }}</div>
              </div>
            </div>
            
            <div class="flex items-center gap-2">
              <Badge variant="outline">{{ workCenter.location }}</Badge>
              <Badge :variant="getWorkCenterStatusBadge(workCenter)">
                {{ getWorkCenterStatus(workCenter) }}
              </Badge>
              
              <Button
                v-if="isEditing"
                variant="ghost"
                size="sm"
                @click="removeFromWorkCenter(workCenter.id)"
              >
                <Unlink class="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div class="mt-3 grid grid-cols-3 gap-4 text-sm">
            <div>
              <span class="text-muted-foreground">产能:</span>
              <span class="ml-1 font-medium">{{ workCenter.capacity || '-' }}</span>
            </div>
            <div>
              <span class="text-muted-foreground">效率:</span>
              <span class="ml-1 font-medium">{{ workCenter.efficiency }}%</span>
            </div>
            <div>
              <span class="text-muted-foreground">负责人:</span>
              <span class="ml-1 font-medium">{{ workCenter.supervisor || '-' }}</span>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 添加到工作中心 -->
    <div v-if="isEditing" class="space-y-4">
      <Label class="text-base font-medium">添加到工作中心</Label>
      
      <div class="flex gap-2">
        <Select v-model="selectedWorkCenter">
          <SelectTrigger class="flex-1">
            <SelectValue placeholder="选择工作中心" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="workCenter in availableWorkCenters"
              :key="workCenter.id"
              :value="workCenter.id"
            >
              <div class="flex items-center justify-between w-full">
                <span>{{ workCenter.name }}</span>
                <Badge variant="outline" class="ml-2">{{ workCenter.location }}</Badge>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
        
        <Button
          @click="addToWorkCenter"
          :disabled="!selectedWorkCenter"
        >
          <Link class="w-4 h-4 mr-2" />
          添加
        </Button>
      </div>
    </div>

    <!-- 工作中心产能影响分析 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">产能影响分析</Label>
      
      <div v-if="capacityAnalysis.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无产能分析数据
      </div>

      <div v-else class="space-y-3 max-h-64 overflow-y-auto list-scroll-area">
        <Card
          v-for="analysis in capacityAnalysis"
          :key="analysis.workCenterId"
          class="p-4"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="font-medium">{{ analysis.workCenterName }}</div>
            <Badge :variant="getImpactBadge(analysis.impact)">
              {{ getImpactLabel(analysis.impact) }}
            </Badge>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="text-sm text-muted-foreground">设备贡献度</div>
              <div class="flex items-center gap-2">
                <div class="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-blue-500 h-2 rounded-full"
                    :style="{ width: `${analysis.contribution}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ analysis.contribution }}%</span>
              </div>
            </div>

            <div class="space-y-2">
              <div class="text-sm text-muted-foreground">状态影响</div>
              <div class="text-sm">
                <span v-if="equipment?.status === 'running'" class="text-green-600">
                  正常运行，无影响
                </span>
                <span v-else-if="equipment?.status === 'maintenance'" class="text-yellow-600">
                  维护中，产能下降 {{ analysis.capacityLoss }}%
                </span>
                <span v-else-if="equipment?.status === 'fault'" class="text-red-600">
                  故障停机，产能下降 {{ analysis.capacityLoss }}%
                </span>
                <span v-else class="text-gray-600">
                  空闲状态
                </span>
              </div>
            </div>
          </div>

          <div class="mt-3 pt-3 border-t">
            <div class="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span class="text-muted-foreground">当前产能:</span>
                <span class="ml-1 font-medium">{{ analysis.currentCapacity }}</span>
              </div>
              <div>
                <span class="text-muted-foreground">最大产能:</span>
                <span class="ml-1 font-medium">{{ analysis.maxCapacity }}</span>
              </div>
              <div>
                <span class="text-muted-foreground">利用率:</span>
                <span class="ml-1 font-medium">{{ analysis.utilization }}%</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 分配的标准工序 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">相关标准工序</Label>
      
      <div v-if="relatedProcessSteps.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无相关工序
      </div>

      <div v-else class="space-y-2">
        <Card
          v-for="processStep in relatedProcessSteps"
          :key="processStep.id"
          class="p-3"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="p-1.5 bg-green-100 rounded">
                <Settings class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <div class="font-medium text-sm">{{ processStep.name }}</div>
                <div class="text-xs text-muted-foreground">{{ processStep.id }}</div>
              </div>
            </div>
            
            <div class="flex items-center gap-2">
              <Badge variant="outline">{{ processStep.category }}</Badge>
              <Badge :variant="processStep.isActive ? 'default' : 'secondary'">
                {{ processStep.isActive ? '启用' : '禁用' }}
              </Badge>
            </div>
          </div>

          <div class="mt-2 text-xs text-muted-foreground">
            {{ processStep.description }}
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Factory, Link, Unlink, Settings } from 'lucide-vue-next';
import type { Equipment, WorkCenter, ProcessStep } from '@/types/masterdata';
import { masterDataService } from '@/services/masterDataService';

interface CapacityAnalysis {
  workCenterId: string;
  workCenterName: string;
  contribution: number;
  impact: 'high' | 'medium' | 'low';
  capacityLoss: number;
  currentCapacity: number;
  maxCapacity: number;
  utilization: number;
}

interface Props {
  equipment?: Equipment | null;
  isEditing: boolean;
}

interface Emits {
  (e: 'update-associations', associations: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const associatedWorkCenters = ref<WorkCenter[]>([]);
const availableWorkCenters = ref<WorkCenter[]>([]);
const relatedProcessSteps = ref<ProcessStep[]>([]);
const selectedWorkCenter = ref('');
const capacityAnalysis = ref<CapacityAnalysis[]>([]);

// 计算属性
const getWorkCenterStatusBadge = (workCenter: WorkCenter) => {
  // 根据工作中心的设备状态确定徽章样式
  return 'default';
};

const getWorkCenterStatus = (workCenter: WorkCenter) => {
  return '正常运行';
};

const getImpactBadge = (impact: string) => {
  const badgeMap: Record<string, string> = {
    high: 'destructive',
    medium: 'outline',
    low: 'secondary'
  };
  return badgeMap[impact] || 'default';
};

const getImpactLabel = (impact: string) => {
  const labelMap: Record<string, string> = {
    high: '高影响',
    medium: '中等影响',
    low: '低影响'
  };
  return labelMap[impact] || impact;
};

// 方法
const loadAssociatedWorkCenters = async () => {
  if (!props.equipment) return;
  
  try {
    const workCenters = await masterDataService.getEquipmentWorkCenters(props.equipment.id);
    associatedWorkCenters.value = workCenters;
  } catch (error) {
    console.error('加载关联工作中心失败:', error);
  }
};

const loadAvailableWorkCenters = async () => {
  try {
    const allWorkCenters = await masterDataService.getWorkCenters();
    const associatedIds = associatedWorkCenters.value.map(wc => wc.id);
    availableWorkCenters.value = allWorkCenters.filter(wc => !associatedIds.includes(wc.id));
  } catch (error) {
    console.error('加载可用工作中心失败:', error);
  }
};

const loadRelatedProcessSteps = async () => {
  if (associatedWorkCenters.value.length === 0) return;
  
  try {
    const allSteps: ProcessStep[] = [];
    for (const workCenter of associatedWorkCenters.value) {
      const steps = await masterDataService.getWorkCenterProcessSteps(workCenter.id);
      allSteps.push(...steps);
    }
    
    // 去重
    const uniqueSteps = allSteps.filter((step, index, self) => 
      index === self.findIndex(s => s.id === step.id)
    );
    
    relatedProcessSteps.value = uniqueSteps;
  } catch (error) {
    console.error('加载相关工序失败:', error);
  }
};

const loadCapacityAnalysis = async () => {
  if (!props.equipment || associatedWorkCenters.value.length === 0) return;
  
  try {
    const analysis: CapacityAnalysis[] = [];
    
    for (const workCenter of associatedWorkCenters.value) {
      const capacity = await masterDataService.calculateWorkCenterCapacity(workCenter.id);
      const equipmentCount = workCenter.equipmentIds.length;
      const contribution = equipmentCount > 0 ? Math.round(100 / equipmentCount) : 0;
      
      let capacityLoss = 0;
      let impact: 'high' | 'medium' | 'low' = 'low';
      
      if (props.equipment.status === 'fault') {
        capacityLoss = contribution;
        impact = contribution > 50 ? 'high' : contribution > 25 ? 'medium' : 'low';
      } else if (props.equipment.status === 'maintenance') {
        capacityLoss = Math.round(contribution * 0.8);
        impact = capacityLoss > 40 ? 'high' : capacityLoss > 20 ? 'medium' : 'low';
      }
      
      analysis.push({
        workCenterId: workCenter.id,
        workCenterName: workCenter.name,
        contribution,
        impact,
        capacityLoss,
        currentCapacity: Math.round(capacity.availableCapacity),
        maxCapacity: Math.round(capacity.totalCapacity),
        utilization: Math.round(capacity.utilizationRate)
      });
    }
    
    capacityAnalysis.value = analysis;
  } catch (error) {
    console.error('加载产能分析失败:', error);
  }
};

const addToWorkCenter = async () => {
  if (!selectedWorkCenter.value || !props.equipment) return;
  
  try {
    // 这里应该调用API将设备添加到工作中心
    // 暂时模拟添加
    const workCenter = availableWorkCenters.value.find(wc => wc.id === selectedWorkCenter.value);
    if (workCenter) {
      associatedWorkCenters.value.push(workCenter);
      availableWorkCenters.value = availableWorkCenters.value.filter(wc => wc.id !== selectedWorkCenter.value);
      selectedWorkCenter.value = '';
      
      // 重新加载相关数据
      await loadRelatedProcessSteps();
      await loadCapacityAnalysis();
      
      emit('update-associations', {
        action: 'add',
        workCenterId: workCenter.id,
        equipmentId: props.equipment.id
      });
    }
  } catch (error) {
    console.error('添加到工作中心失败:', error);
  }
};

const removeFromWorkCenter = async (workCenterId: string) => {
  if (!props.equipment) return;
  
  try {
    const workCenter = associatedWorkCenters.value.find(wc => wc.id === workCenterId);
    if (workCenter) {
      associatedWorkCenters.value = associatedWorkCenters.value.filter(wc => wc.id !== workCenterId);
      availableWorkCenters.value.push(workCenter);
      
      // 重新加载相关数据
      await loadRelatedProcessSteps();
      await loadCapacityAnalysis();
      
      emit('update-associations', {
        action: 'remove',
        workCenterId,
        equipmentId: props.equipment.id
      });
    }
  } catch (error) {
    console.error('从工作中心移除失败:', error);
  }
};

// 生命周期
onMounted(async () => {
  await loadAssociatedWorkCenters();
  await loadAvailableWorkCenters();
  await loadRelatedProcessSteps();
  await loadCapacityAnalysis();
});
</script>
