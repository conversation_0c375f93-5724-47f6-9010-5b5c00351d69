<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { VueFlow, useVueFlow, Panel, PanelPosition } from '@vue-flow/core';
import type { Node, Edge, Connection } from '@vue-flow/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Save, Trash2, Wrench, Package, X
} from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { ProcessStep, WipBuffer, ProcessSegment, ProcessSegmentNode } from '@/types/masterdata';

// Props
interface Props {
  processSegment?: ProcessSegment | null;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  processSegment: null,
  readonly: false,
});

// Emits
const emit = defineEmits<{
  save: [data: ProcessSegment];
  cancel: [];
}>();

// Store
const masterDataStore = useMasterDataStore();

// Vue Flow instance
const { onConnect, addEdges, removeNodes, findNode, onNodeClick, getSelectedNodes } = useVueFlow();

// Reactive state
const nodes = ref<Node[]>([]);
const edges = ref<Edge[]>([]);
const localProcessSegment = ref<Partial<ProcessSegment>>({ name: '', description: '' });

const selectedNodeId = ref<string | null>(null);
const showEntitySelector = ref(false);
const selectedNodeType = ref<'ProcessStep' | 'WipBuffer' | null>(null);

// Node types for the palette
const nodeTypes = [
  {
    type: 'ProcessStep',
    label: '工序节点',
    icon: Wrench,
    color: 'bg-blue-500',
    description: '生产工序节点'
  },
  {
    type: 'WipBuffer',
    label: 'WIP缓冲区',
    icon: Package,
    color: 'bg-green-500',
    description: '在制品缓冲区'
  },
];

// --- Methods ---

// Load data from prop into editor
const loadProcessSegment = (segment: ProcessSegment | null) => {
  if (!segment) {
    nodes.value = [];
    edges.value = [];
    localProcessSegment.value = { name: '', description: '' };
    return;
  }

  localProcessSegment.value = { ...segment };

  const newNodes: Node[] = segment.nodes.map(node => {
    const entity = node.type === 'ProcessStep'
      ? masterDataStore.getProcessStepById(node.entityId)
      : masterDataStore.getWipBufferById(node.entityId);

    return {
      id: node.nodeId,
      type: 'default', // Use default node type for simplicity
      position: node.position,
      data: {
        label: entity?.name || '未知节点',
        type: node.type,
        entityId: node.entityId,
      },
    };
  });

  const newEdges: Edge[] = segment.edges.map((edge, index) => ({
    id: `edge-${index}`,
    source: edge.sourceNodeId,
    target: edge.targetNodeId,
  }));

  nodes.value = newNodes;
  edges.value = newEdges;
};

const openEntitySelector = (type: 'ProcessStep' | 'WipBuffer') => {
  if (props.readonly) return;
  selectedNodeType.value = type;
  showEntitySelector.value = true;
};

const addNodeWithEntity = (entity: ProcessStep | WipBuffer) => {
  if (!selectedNodeType.value) return;

  const newNode: Node = {
    id: `node_${Date.now()}`, // Use a simpler unique ID
    type: 'default',
    position: { x: Math.random() * 400 + 100, y: Math.random() * 200 + 50 },
    data: {
      label: entity.name,
      type: selectedNodeType.value,
      entityId: entity.id,
    },
    // 添加一些样式来区分不同类型的节点
    style: {
      backgroundColor: selectedNodeType.value === 'ProcessStep' ? '#dbeafe' : '#dcfce7',
      border: `2px solid ${selectedNodeType.value === 'ProcessStep' ? '#3b82f6' : '#22c55e'}`,
      borderRadius: '8px',
      padding: '10px',
      minWidth: '120px',
      textAlign: 'center'
    }
  };

  nodes.value.push(newNode);
  showEntitySelector.value = false;
  selectedNodeType.value = null;
  toast.success(`已添加节点: ${entity.name}`);
};

const onConnectHandler = (connection: Connection) => {
  if (props.readonly) return;
  const edge: Edge = {
    id: `edge_${Date.now()}`,
    source: connection.source!,
    target: connection.target!,
    type: 'default',
  };
  addEdges([edge]);
};

const save = () => {
  if (!localProcessSegment.value.name) {
    toast.error('请输入工艺段名称');
    return;
  }

  const finalSegment: ProcessSegment = {
    ...localProcessSegment.value,
    id: props.processSegment?.id || `seg_${Date.now()}`,
    name: localProcessSegment.value.name,
    description: localProcessSegment.value.description,
    nodes: nodes.value.map(node => ({
      nodeId: node.id,
      type: node.data.type,
      entityId: node.data.entityId,
      position: node.position,
    })),
    edges: edges.value.map(edge => ({
      sourceNodeId: edge.source,
      targetNodeId: edge.target,
    })),
  };

  emit('save', finalSegment);
};

const deleteSelectedNode = () => {
  if (selectedNodeId.value) {
    removeNodes([selectedNodeId.value]);
    selectedNodeId.value = null;
    toast.success('节点已删除');
  }
};

// Handle node selection
const handleNodeClick = (event: any) => {
  selectedNodeId.value = event.node.id;
};

// Get currently selected nodes using Vue Flow's getSelectedNodes
const selectedNodes = computed(() => {
  return getSelectedNodes.value;
});

// Setup connections and events
onConnect(onConnectHandler);
onNodeClick(handleNodeClick);

// Lifecycle
onMounted(async () => {
  await Promise.all([
    masterDataStore.fetchProcessSteps(),
    masterDataStore.fetchWipBuffers()
  ]);
  
  if (props.processSegment) {
    loadProcessSegment(props.processSegment);
  }
});

watch(() => props.processSegment, (newVal) => {
  loadProcessSegment(newVal);
});

</script>

<template>
  <div class="h-full flex flex-col">
    <div class="p-4 border-b grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <Label for="segment-name">工艺段名称 *</Label>
        <Input
          id="segment-name"
          v-model="localProcessSegment.name"
          placeholder="例如：冷加工段"
          :disabled="readonly"
        />
      </div>
      <div class="col-span-2">
        <Label for="segment-desc">工艺段描述</Label>
        <Input
          id="segment-desc"
          v-model="localProcessSegment.description"
          placeholder="描述这个工艺段的用途"
          :disabled="readonly"
        />
      </div>
    </div>
    <div class="flex-1 flex">
      <!-- Node Palette -->
      <div v-if="!readonly" class="w-64 border-r bg-muted/30 p-4 space-y-4">
        <div>
          <h3 class="font-semibold mb-3">节点工具箱</h3>
          <div class="space-y-2">
            <div
              v-for="nodeType in nodeTypes"
              :key="nodeType.type"
              class="p-3 border rounded-lg cursor-pointer hover:bg-accent transition-colors"
              @click="openEntitySelector(nodeType.type as 'ProcessStep' | 'WipBuffer')"
            >
              <div class="flex items-center gap-3">
                <div :class="[nodeType.color, 'p-2 rounded text-white']">
                  <component :is="nodeType.icon" class="h-4 w-4" />
                </div>
                <div>
                  <div class="font-medium text-sm">{{ nodeType.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Editor Area -->
      <div class="flex-1 relative">
        <VueFlow
          v-model:nodes="nodes"
          v-model:edges="edges"
          :nodes-draggable="!readonly"
          :nodes-connectable="!readonly"
          :elements-selectable="!readonly"
          :zoom-on-scroll="true"
          :zoom-on-pinch="true"
          :pan-on-scroll="false"
          :pan-on-drag="true"
          fit-view-on-init
          class="vue-flow-container"
        >
          <Panel v-if="!readonly" :position="PanelPosition.TopRight" class="space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="deleteSelectedNode"
              :disabled="!selectedNodeId"
              title="删除选中节点"
            >
              <Trash2 class="h-4 w-4" />
            </Button>
          </Panel>

          <!-- 添加一些调试信息 -->
          <Panel v-if="!readonly" :position="PanelPosition.BottomLeft" class="text-xs text-muted-foreground">
            <div>节点数: {{ nodes.length }}</div>
            <div>连接数: {{ edges.length }}</div>
            <div v-if="selectedNodeId">选中: {{ selectedNodeId }}</div>
          </Panel>
        </VueFlow>

        <!-- Entity Selector Dialog -->
        <div
          v-if="showEntitySelector"
          class="absolute inset-0 bg-black/50 flex items-center justify-center z-50"
          @click="showEntitySelector = false"
        >
          <Card class="w-96 max-h-[60vh] overflow-hidden flex flex-col" @click.stop>
            <CardHeader>
              <CardTitle>选择{{ selectedNodeType === 'ProcessStep' ? '工序' : 'WIP缓冲区' }}</CardTitle>
            </CardHeader>
            <CardContent class="flex-1 overflow-y-auto">
              <div class="space-y-2">
                <template v-if="selectedNodeType === 'ProcessStep'">
                  <div v-if="masterDataStore.processSteps.length === 0" class="text-center py-4 text-muted-foreground">加载中或无可用工序</div>
                  <div
                    v-for="step in masterDataStore.processSteps"
                    :key="step.id"
                    class="p-3 border rounded-lg cursor-pointer hover:bg-accent transition-colors"
                    @click="addNodeWithEntity(step)"
                  >
                    <div class="font-medium">{{ step.name }}</div>
                    <div class="text-sm text-muted-foreground">{{ step.id }}</div>
                  </div>
                </template>
                <template v-if="selectedNodeType === 'WipBuffer'">
                   <div v-if="masterDataStore.wipBuffers.length === 0" class="text-center py-4 text-muted-foreground">加载中或无可用缓冲区</div>
                  <div
                    v-for="buffer in masterDataStore.wipBuffers"
                    :key="buffer.id"
                    class="p-3 border rounded-lg cursor-pointer hover:bg-accent transition-colors"
                    @click="addNodeWithEntity(buffer)"
                  >
                    <div class="font-medium">{{ buffer.name }}</div>
                    <div class="text-sm text-muted-foreground">{{ buffer.id }}</div>
                  </div>
                </template>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    <div class="p-4 border-t flex justify-end gap-2">
        <Button variant="outline" @click="emit('cancel')">取消</Button>
        <Button v-if="!readonly" @click="save">
          <Save class="h-4 w-4 mr-2" />
          保存
        </Button>
    </div>
  </div>
</template>

<style>
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';

.vue-flow__node-default {
  border-radius: 0.5rem;
  border: 2px solid #e5e7eb;
  background-color: white;
  padding: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.vue-flow__node-default.selected {
  border-color: hsl(var(--primary));
}

.vue-flow__node-default:hover {
  border-color: #d1d5db;
}

.vue-flow-container {
  background-color: #fafafa;
}

.vue-flow__edge-default {
  stroke: #6b7280;
  stroke-width: 2px;
}

.vue-flow__edge-default.selected {
  stroke: hsl(var(--primary));
  stroke-width: 3px;
}

.vue-flow__controls {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}
</style>