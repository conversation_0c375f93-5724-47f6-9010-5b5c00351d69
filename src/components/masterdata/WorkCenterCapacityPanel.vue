<template>
  <div class="space-y-6">
    <!-- 产能配置概览 -->
    <div class="grid grid-cols-3 gap-4">
      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <Factory class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">最大产能</div>
            <div class="font-semibold">{{ localData.maxCapacity || 0 }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <TrendingUp class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">当前产能</div>
            <div class="font-semibold">{{ localData.currentCapacity || 0 }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 rounded-lg">
            <BarChart3 class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">利用率</div>
            <div class="font-semibold">{{ Math.round(localData.utilizationRate || 0) }}%</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 基础产能配置 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">基础产能配置</Label>
      
      <div class="grid grid-cols-3 gap-4">
        <div class="space-y-2">
          <Label for="maxCapacity">最大产能</Label>
          <div class="flex gap-2">
            <Input
              id="maxCapacity"
              v-model.number="localData.maxCapacity"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="100"
            />
            <Select v-model="localData.capacityUnit" :disabled="!isEditing">
              <SelectTrigger class="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pieces">件/h</SelectItem>
                <SelectItem value="area">m²/h</SelectItem>
                <SelectItem value="length">m/h</SelectItem>
                <SelectItem value="weight">kg/h</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div class="space-y-2">
          <Label for="efficiency">标准效率 (%)</Label>
          <Input
            id="efficiency"
            v-model.number="localData.efficiency"
            :disabled="!isEditing"
            type="number"
            min="0"
            max="100"
            placeholder="95"
          />
        </div>

        <div class="space-y-2">
          <Label for="setupTime">换线时间 (分钟)</Label>
          <Input
            id="setupTime"
            v-model.number="localData.setupTime"
            :disabled="!isEditing"
            type="number"
            min="0"
            placeholder="30"
          />
        </div>
      </div>
    </div>

    <!-- 班次配置 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">班次配置</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addShift"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加班次
        </Button>
      </div>

      <div v-if="shiftConfig.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无班次配置
      </div>

      <div v-else class="space-y-3 max-h-64 overflow-y-auto list-scroll-area">
        <Card
          v-for="(shift, index) in shiftConfig"
          :key="index"
          class="p-4"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-2">
              <Badge :variant="getShiftBadgeVariant(shift.type)">
                {{ getShiftTypeLabel(shift.type) }}
              </Badge>
              <span class="font-medium">{{ shift.name }}</span>
            </div>
            <Button
              v-if="isEditing"
              variant="ghost"
              size="sm"
              @click="removeShift(index)"
            >
              <Trash2 class="w-4 h-4" />
            </Button>
          </div>

          <div class="grid grid-cols-4 gap-4">
            <div class="space-y-2">
              <Label class="text-xs">班次名称</Label>
              <Input
                v-model="shift.name"
                :disabled="!isEditing"
                placeholder="早班"
                class="h-8"
              />
            </div>

            <div class="space-y-2">
              <Label class="text-xs">开始时间</Label>
              <Input
                v-model="shift.startTime"
                :disabled="!isEditing"
                type="time"
                class="h-8"
              />
            </div>

            <div class="space-y-2">
              <Label class="text-xs">结束时间</Label>
              <Input
                v-model="shift.endTime"
                :disabled="!isEditing"
                type="time"
                class="h-8"
              />
            </div>

            <div class="space-y-2">
              <Label class="text-xs">产能系数</Label>
              <Input
                v-model.number="shift.capacityMultiplier"
                :disabled="!isEditing"
                type="number"
                min="0"
                max="2"
                step="0.1"
                placeholder="1.0"
                class="h-8"
              />
            </div>
          </div>

          <div class="mt-3 grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label class="text-xs">工作日</Label>
              <div class="flex gap-1">
                <Button
                  v-for="day in weekDays"
                  :key="day.value"
                  :variant="shift.workingDays?.includes(day.value) ? 'default' : 'outline'"
                  size="sm"
                  class="h-6 w-8 p-0 text-xs"
                  :disabled="!isEditing"
                  @click="toggleWorkingDay(shift, day.value)"
                >
                  {{ day.label }}
                </Button>
              </div>
            </div>

            <div class="space-y-2">
              <Label class="text-xs">班次产能</Label>
              <div class="text-sm font-medium">
                {{ calculateShiftCapacity(shift) }} {{ localData.capacityUnit || '件/h' }}
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 瓶颈分析 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">瓶颈分析</Label>
      
      <div class="grid grid-cols-2 gap-4">
        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">当前瓶颈</div>
            <div class="space-y-2">
              <div v-if="!bottleneckAnalysis.currentBottleneck" class="text-sm text-muted-foreground">
                暂无瓶颈识别
              </div>
              <div v-else class="flex items-center gap-2">
                <AlertTriangle class="w-4 h-4 text-orange-500" />
                <span class="text-sm">{{ bottleneckAnalysis.currentBottleneck }}</span>
              </div>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">改进建议</div>
            <div class="space-y-1">
              <div v-if="bottleneckAnalysis.suggestions?.length === 0" class="text-sm text-muted-foreground">
                暂无改进建议
              </div>
              <div v-else>
                <div
                  v-for="(suggestion, index) in bottleneckAnalysis.suggestions"
                  :key="index"
                  class="text-sm flex items-start gap-2"
                >
                  <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span>{{ suggestion }}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 产能计算公式 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">产能计算公式</Label>
      
      <div class="space-y-3">
        <div class="space-y-2">
          <Label for="capacityFormula">工作中心产能公式</Label>
          <Textarea
            id="capacityFormula"
            v-model="localData.capacityFormula"
            :disabled="!isEditing"
            placeholder="例如：SUM(设备产能 * 设备效率 * 状态系数) * 工作中心效率"
            rows="2"
          />
          <p class="text-xs text-muted-foreground">
            用于计算工作中心综合产能的公式
          </p>
        </div>

        <div class="space-y-2">
          <Label for="utilizationFormula">利用率计算公式</Label>
          <Textarea
            id="utilizationFormula"
            v-model="localData.utilizationFormula"
            :disabled="!isEditing"
            placeholder="例如：实际产能 / 最大产能 * 100"
            rows="2"
          />
          <p class="text-xs text-muted-foreground">
            用于计算工作中心利用率的公式
          </p>
        </div>
      </div>
    </div>

    <!-- 产能限制因素 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">产能限制因素</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addLimitingFactor"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加因素
        </Button>
      </div>

      <div v-if="limitingFactors.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无产能限制因素
      </div>

      <div v-else class="space-y-2 max-h-48 overflow-y-auto list-scroll-area">
        <div
          v-for="(factor, index) in limitingFactors"
          :key="index"
          class="flex items-center gap-2 p-3 border rounded-lg"
        >
          <Select v-model="factor.type" :disabled="!isEditing" class="w-32">
            <SelectTrigger>
              <SelectValue placeholder="类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="equipment">设备限制</SelectItem>
              <SelectItem value="material">物料限制</SelectItem>
              <SelectItem value="labor">人力限制</SelectItem>
              <SelectItem value="space">空间限制</SelectItem>
              <SelectItem value="quality">质量限制</SelectItem>
            </SelectContent>
          </Select>
          
          <Input
            v-model="factor.description"
            :disabled="!isEditing"
            placeholder="限制因素描述"
            class="flex-1"
          />
          
          <Input
            v-model.number="factor.impact"
            :disabled="!isEditing"
            type="number"
            min="0"
            max="100"
            placeholder="影响度%"
            class="w-24"
          />
          
          <Button
            v-if="isEditing"
            variant="ghost"
            size="sm"
            @click="removeLimitingFactor(index)"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, Factory, TrendingUp, BarChart3, AlertTriangle } from 'lucide-vue-next';
import type { WorkCenter } from '@/types/masterdata';

interface CapacityConfig {
  maxCapacity: number;
  currentCapacity: number;
  utilizationRate: number;
  efficiency: number;
  capacityUnit: string;
  setupTime: number;
  capacityFormula: string;
  utilizationFormula: string;
  shiftConfig: ShiftConfig[];
  limitingFactors: LimitingFactor[];
  bottleneckAnalysis: BottleneckAnalysis;
}

interface ShiftConfig {
  name: string;
  type: string;
  startTime: string;
  endTime: string;
  workingDays: string[];
  capacityMultiplier: number;
}

interface LimitingFactor {
  type: string;
  description: string;
  impact: number;
}

interface BottleneckAnalysis {
  currentBottleneck?: string;
  suggestions?: string[];
}

interface Props {
  modelValue: CapacityConfig;
  workCenter?: WorkCenter | null;
  isEditing: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: CapacityConfig): void;
  (e: 'validate', errors: Record<string, string>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localData = ref<CapacityConfig>({ ...props.modelValue });
const isUpdating = ref(false);

// 班次配置和限制因素
const shiftConfig = ref<ShiftConfig[]>([]);
const limitingFactors = ref<LimitingFactor[]>([]);

// 瓶颈分析
const bottleneckAnalysis = computed(() => {
  return localData.value.bottleneckAnalysis || {
    currentBottleneck: '设备效率不均衡',
    suggestions: [
      '优化设备维护计划',
      '平衡各设备负载',
      '增加关键设备数量'
    ]
  };
});

// 工作日配置
const weekDays = [
  { value: 'mon', label: '一' },
  { value: 'tue', label: '二' },
  { value: 'wed', label: '三' },
  { value: 'thu', label: '四' },
  { value: 'fri', label: '五' },
  { value: 'sat', label: '六' },
  { value: 'sun', label: '日' }
];

// 方法
const getShiftBadgeVariant = (type: string) => {
  const variantMap: Record<string, string> = {
    day: 'default',
    night: 'secondary',
    continuous: 'outline'
  };
  return variantMap[type] || 'default';
};

const getShiftTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    day: '白班',
    night: '夜班',
    continuous: '连续班'
  };
  return labelMap[type] || type;
};

const addShift = () => {
  shiftConfig.value.push({
    name: `班次${shiftConfig.value.length + 1}`,
    type: 'day',
    startTime: '08:00',
    endTime: '16:00',
    workingDays: ['mon', 'tue', 'wed', 'thu', 'fri'],
    capacityMultiplier: 1.0
  });
  updateShiftConfig();
};

const removeShift = (index: number) => {
  shiftConfig.value.splice(index, 1);
  updateShiftConfig();
};

const toggleWorkingDay = (shift: ShiftConfig, day: string) => {
  if (!shift.workingDays) {
    shift.workingDays = [];
  }
  
  const index = shift.workingDays.indexOf(day);
  if (index > -1) {
    shift.workingDays.splice(index, 1);
  } else {
    shift.workingDays.push(day);
  }
  
  updateShiftConfig();
};

const calculateShiftCapacity = (shift: ShiftConfig): number => {
  const baseCapacity = localData.value.maxCapacity || 0;
  const multiplier = shift.capacityMultiplier || 1;
  const workingDaysCount = shift.workingDays?.length || 5;
  
  // 计算班次工作时长
  const startTime = new Date(`2000-01-01T${shift.startTime}:00`);
  const endTime = new Date(`2000-01-01T${shift.endTime}:00`);
  let hours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
  if (hours < 0) hours += 24; // 跨天班次
  
  return Math.round(baseCapacity * multiplier * (workingDaysCount / 7) * (hours / 8));
};

const addLimitingFactor = () => {
  limitingFactors.value.push({
    type: 'equipment',
    description: '',
    impact: 0
  });
  updateLimitingFactors();
};

const removeLimitingFactor = (index: number) => {
  limitingFactors.value.splice(index, 1);
  updateLimitingFactors();
};

const updateShiftConfig = () => {
  localData.value.shiftConfig = shiftConfig.value;
  updateModelValue();
};

const updateLimitingFactors = () => {
  localData.value.limitingFactors = limitingFactors.value;
  updateModelValue();
};

const updateModelValue = () => {
  if (isUpdating.value) return;
  emit('update:modelValue', { ...localData.value });
};

// 监听器
watch(localData, () => {
  updateModelValue();
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  if (isUpdating.value) return;
  
  isUpdating.value = true;
  localData.value = { ...newValue };
  
  // 初始化班次配置和限制因素
  if (newValue.shiftConfig) {
    shiftConfig.value = [...newValue.shiftConfig];
  } else {
    shiftConfig.value = [];
  }
  
  if (newValue.limitingFactors) {
    limitingFactors.value = [...newValue.limitingFactors];
  } else {
    limitingFactors.value = [];
  }
  
  nextTick(() => {
    isUpdating.value = false;
  });
}, { deep: true });

// 初始化
if (props.modelValue?.shiftConfig) {
  shiftConfig.value = [...props.modelValue.shiftConfig];
}
if (props.modelValue?.limitingFactors) {
  limitingFactors.value = [...props.modelValue.limitingFactors];
}
</script>
