<template>
  <div class="space-y-6">
    <!-- 维护状态概览 -->
    <div class="grid grid-cols-3 gap-4">
      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <CheckCircle class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">维护状态</div>
            <div class="font-semibold">{{ maintenanceStatus }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <Calendar class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">下次维护</div>
            <div class="font-semibold">{{ nextMaintenanceDate || '未设置' }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 rounded-lg">
            <Clock class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">距离维护</div>
            <div class="font-semibold">{{ daysUntilMaintenance }}</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 维护计划 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">维护计划</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addMaintenancePlan"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加计划
        </Button>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label for="lastMaintenance">最后维护日期</Label>
          <Input
            id="lastMaintenance"
            v-model="localData.lastMaintenanceDate"
            :disabled="!isEditing"
            type="date"
          />
        </div>

        <div class="space-y-2">
          <Label for="nextMaintenance">下次维护日期</Label>
          <Input
            id="nextMaintenance"
            v-model="localData.nextMaintenanceDate"
            :disabled="!isEditing"
            type="date"
          />
        </div>
      </div>

      <div class="space-y-2">
        <Label for="maintenanceInterval">维护周期 (天)</Label>
        <Input
          id="maintenanceInterval"
          v-model.number="localData.maintenanceInterval"
          :disabled="!isEditing"
          type="number"
          min="1"
          placeholder="90"
        />
        <p class="text-xs text-muted-foreground">设备的标准维护周期</p>
      </div>
    </div>

    <!-- 维护项目清单 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">维护项目清单</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addMaintenanceItem"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加项目
        </Button>
      </div>

      <div v-if="maintenanceItems.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无维护项目
      </div>

      <div v-else class="space-y-3">
        <div
          v-for="(item, index) in maintenanceItems"
          :key="index"
          class="p-4 border rounded-lg space-y-3"
        >
          <div class="flex items-center justify-between">
            <Input
              v-model="item.name"
              :disabled="!isEditing"
              placeholder="维护项目名称"
              class="flex-1 mr-2"
            />
            <Button
              v-if="isEditing"
              variant="ghost"
              size="sm"
              @click="removeMaintenanceItem(index)"
            >
              <Trash2 class="w-4 h-4" />
            </Button>
          </div>

          <div class="grid grid-cols-3 gap-2">
            <div class="space-y-1">
              <Label class="text-xs">维护类型</Label>
              <Select v-model="item.type" :disabled="!isEditing">
                <SelectTrigger class="h-8">
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">日常维护</SelectItem>
                  <SelectItem value="weekly">周维护</SelectItem>
                  <SelectItem value="monthly">月维护</SelectItem>
                  <SelectItem value="quarterly">季度维护</SelectItem>
                  <SelectItem value="annual">年度维护</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div class="space-y-1">
              <Label class="text-xs">预计时长 (小时)</Label>
              <Input
                v-model.number="item.duration"
                :disabled="!isEditing"
                type="number"
                min="0"
                step="0.5"
                class="h-8"
              />
            </div>

            <div class="space-y-1">
              <Label class="text-xs">优先级</Label>
              <Select v-model="item.priority" :disabled="!isEditing">
                <SelectTrigger class="h-8">
                  <SelectValue placeholder="优先级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">低</SelectItem>
                  <SelectItem value="medium">中</SelectItem>
                  <SelectItem value="high">高</SelectItem>
                  <SelectItem value="critical">紧急</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Textarea
            v-model="item.description"
            :disabled="!isEditing"
            placeholder="维护项目描述和注意事项"
            rows="2"
          />
        </div>
      </div>
    </div>

    <!-- 维护记录 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">维护记录</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addMaintenanceRecord"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加记录
        </Button>
      </div>

      <div v-if="maintenanceRecords.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无维护记录
      </div>

      <div v-else class="space-y-3 max-h-64 overflow-y-auto list-scroll-area">
        <div
          v-for="(record, index) in maintenanceRecords"
          :key="index"
          class="p-4 border rounded-lg"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-2">
              <Badge :variant="getMaintenanceTypeBadge(record.type)">
                {{ getMaintenanceTypeLabel(record.type) }}
              </Badge>
              <span class="text-sm font-medium">{{ record.date }}</span>
            </div>
            <Button
              v-if="isEditing"
              variant="ghost"
              size="sm"
              @click="removeMaintenanceRecord(index)"
            >
              <Trash2 class="w-4 h-4" />
            </Button>
          </div>

          <div class="grid grid-cols-2 gap-4 mb-3">
            <div class="space-y-2">
              <Label class="text-xs">维护日期</Label>
              <Input
                v-model="record.date"
                :disabled="!isEditing"
                type="date"
                class="h-8"
              />
            </div>
            <div class="space-y-2">
              <Label class="text-xs">维护人员</Label>
              <Input
                v-model="record.technician"
                :disabled="!isEditing"
                placeholder="维护人员姓名"
                class="h-8"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label class="text-xs">维护内容</Label>
            <Textarea
              v-model="record.description"
              :disabled="!isEditing"
              placeholder="详细描述维护内容和发现的问题"
              rows="2"
            />
          </div>

          <div class="grid grid-cols-2 gap-4 mt-3">
            <div class="space-y-2">
              <Label class="text-xs">维护费用 (元)</Label>
              <Input
                v-model.number="record.cost"
                :disabled="!isEditing"
                type="number"
                min="0"
                step="0.01"
                class="h-8"
              />
            </div>
            <div class="space-y-2">
              <Label class="text-xs">维护时长 (小时)</Label>
              <Input
                v-model.number="record.duration"
                :disabled="!isEditing"
                type="number"
                min="0"
                step="0.5"
                class="h-8"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, CheckCircle, Calendar, Clock } from 'lucide-vue-next';
import type { Equipment } from '@/types/masterdata';

interface MaintenanceItem {
  name: string;
  type: string;
  duration: number;
  priority: string;
  description: string;
}

interface MaintenanceRecord {
  date: string;
  type: string;
  technician: string;
  description: string;
  cost: number;
  duration: number;
}

interface Props {
  equipment?: Equipment | null;
  isEditing: boolean;
}

interface Emits {
  (e: 'update-maintenance', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localData = ref({
  lastMaintenanceDate: props.equipment?.lastMaintenanceDate || '',
  nextMaintenanceDate: props.equipment?.nextMaintenanceDate || '',
  maintenanceInterval: 90
});

const maintenanceItems = ref<MaintenanceItem[]>([]);
const maintenanceRecords = ref<MaintenanceRecord[]>([]);

// 计算属性
const maintenanceStatus = computed(() => {
  if (!localData.value.nextMaintenanceDate) return '未设置';
  
  const nextDate = new Date(localData.value.nextMaintenanceDate);
  const today = new Date();
  const diffDays = Math.ceil((nextDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) return '逾期维护';
  if (diffDays <= 7) return '即将维护';
  return '正常';
});

const nextMaintenanceDate = computed(() => {
  return localData.value.nextMaintenanceDate || null;
});

const daysUntilMaintenance = computed(() => {
  if (!localData.value.nextMaintenanceDate) return '未设置';
  
  const nextDate = new Date(localData.value.nextMaintenanceDate);
  const today = new Date();
  const diffDays = Math.ceil((nextDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) return `逾期 ${Math.abs(diffDays)} 天`;
  if (diffDays === 0) return '今天';
  return `${diffDays} 天`;
});

// 方法
const addMaintenancePlan = () => {
  // 自动计算下次维护日期
  if (localData.value.lastMaintenanceDate && localData.value.maintenanceInterval) {
    const lastDate = new Date(localData.value.lastMaintenanceDate);
    const nextDate = new Date(lastDate.getTime() + localData.value.maintenanceInterval * 24 * 60 * 60 * 1000);
    localData.value.nextMaintenanceDate = nextDate.toISOString().split('T')[0];
  }
};

const addMaintenanceItem = () => {
  maintenanceItems.value.push({
    name: '',
    type: 'monthly',
    duration: 2,
    priority: 'medium',
    description: ''
  });
};

const removeMaintenanceItem = (index: number) => {
  maintenanceItems.value.splice(index, 1);
};

const addMaintenanceRecord = () => {
  maintenanceRecords.value.push({
    date: new Date().toISOString().split('T')[0],
    type: 'routine',
    technician: '',
    description: '',
    cost: 0,
    duration: 0
  });
};

const removeMaintenanceRecord = (index: number) => {
  maintenanceRecords.value.splice(index, 1);
};

const getMaintenanceTypeBadge = (type: string) => {
  const badgeMap: Record<string, string> = {
    routine: 'default',
    emergency: 'destructive',
    preventive: 'secondary',
    corrective: 'outline'
  };
  return badgeMap[type] || 'default';
};

const getMaintenanceTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    routine: '例行维护',
    emergency: '紧急维修',
    preventive: '预防性维护',
    corrective: '纠正性维护'
  };
  return labelMap[type] || type;
};

// 监听器
watch(localData, () => {
  emit('update-maintenance', {
    lastMaintenanceDate: localData.value.lastMaintenanceDate,
    nextMaintenanceDate: localData.value.nextMaintenanceDate,
    maintenanceInterval: localData.value.maintenanceInterval,
    maintenanceItems: maintenanceItems.value,
    maintenanceRecords: maintenanceRecords.value
  });
}, { deep: true });
</script>
