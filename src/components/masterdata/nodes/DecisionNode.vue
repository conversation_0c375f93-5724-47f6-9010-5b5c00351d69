<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core';
import { Badge } from '@/components/ui/badge';
import { GitBranch, HelpCircle } from 'lucide-vue-next';

interface Props {
  data: {
    label: string;
    type: string;
    properties: {
      condition?: string;
      description?: string;
    };
  };
  selected?: boolean;
}

const props = defineProps<Props>();
</script>

<template>
  <div class="decision-node">
    <!-- Input Handle -->
    <Handle
      type="target"
      :position="Position.Left"
      class="w-3 h-3 !bg-yellow-500"
    />

    <!-- Node Content -->
    <div class="p-3 min-w-[140px]">
      <div class="flex items-center gap-2 mb-2">
        <div class="p-1 bg-yellow-500 rounded text-white">
          <GitBranch class="h-4 w-4" />
        </div>
        <div class="font-medium text-sm">{{ data.label }}</div>
      </div>

      <div class="space-y-1 text-xs text-muted-foreground">
        <div v-if="data.properties.condition" class="flex items-center gap-1">
          <HelpCircle class="h-3 w-3" />
          <span class="truncate">{{ data.properties.condition }}</span>
        </div>
      </div>

      <Badge variant="outline" class="mt-2 text-xs">
        决策点
      </Badge>
    </div>

    <!-- Output Handles -->
    <Handle
      type="source"
      :position="Position.Right"
      :style="{ top: '30%' }"
      class="w-3 h-3 !bg-green-500"
      id="yes"
    />
    <Handle
      type="source"
      :position="Position.Right"
      :style="{ top: '70%' }"
      class="w-3 h-3 !bg-red-500"
      id="no"
    />
  </div>
</template>

<style scoped>
.decision-node {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  position: relative;
}

.decision-node:hover {
  border-color: #eab308;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.decision-node::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 25%;
  width: 0;
  height: 0;
  border-left: 6px solid #10b981;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.decision-node::before {
  content: '';
  position: absolute;
  right: -8px;
  top: 65%;
  width: 0;
  height: 0;
  border-left: 6px solid #ef4444;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}
</style>
