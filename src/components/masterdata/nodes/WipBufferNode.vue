<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core';
import { Badge } from '@/components/ui/badge';
import { Package, Layers, Info } from 'lucide-vue-next';

interface Props {
  data: {
    label: string;
    type: string;
    properties: {
      capacity?: number;
      unit?: string;
      bufferType?: string;
      description?: string;
    };
  };
  selected?: boolean;
}

const props = defineProps<Props>();

const getUnitLabel = (unit: string) => {
  const units = {
    'piece': '件',
    'rack': '架',
    'm²': '平方米',
    'kg': '千克'
  };
  return units[unit as keyof typeof units] || unit;
};

const getBufferTypeLabel = (type: string) => {
  const types = {
    'fifo': 'FIFO',
    'lifo': 'LIFO',
    'priority': '优先级'
  };
  return types[type as keyof typeof types] || type;
};
</script>

<template>
  <div class="wip-buffer-node">
    <!-- Input Handle -->
    <Handle
      type="target"
      :position="Position.Left"
      class="w-3 h-3 !bg-green-500"
    />

    <!-- Node Content -->
    <div class="p-3 min-w-[160px]">
      <div class="flex items-center gap-2 mb-2">
        <div class="p-1 bg-green-500 rounded text-white">
          <Package class="h-4 w-4" />
        </div>
        <div class="font-medium text-sm">{{ data.label }}</div>
      </div>

      <div class="space-y-1 text-xs text-muted-foreground">
        <div v-if="data.properties.capacity" class="flex items-center gap-1">
          <Layers class="h-3 w-3" />
          <span>{{ data.properties.capacity }} {{ getUnitLabel(data.properties.unit || 'piece') }}</span>
        </div>
        <div v-if="data.properties.bufferType" class="flex items-center gap-1">
          <Info class="h-3 w-3" />
          <span>{{ getBufferTypeLabel(data.properties.bufferType) }}</span>
        </div>
      </div>

      <Badge variant="outline" class="mt-2 text-xs">
        缓冲区
      </Badge>
    </div>

    <!-- Output Handle -->
    <Handle
      type="source"
      :position="Position.Right"
      class="w-3 h-3 !bg-green-500"
    />
  </div>
</template>

<style scoped>
.wip-buffer-node {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.wip-buffer-node:hover {
  border-color: #10b981;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
