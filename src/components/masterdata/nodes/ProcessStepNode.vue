<script setup lang="ts">
import { <PERSON><PERSON>, Position } from '@vue-flow/core';
import { Badge } from '@/components/ui/badge';
import { Wrench, Clock, Settings } from 'lucide-vue-next';

interface Props {
  data: {
    label: string;
    type: string;
    properties: {
      processingTime?: number;
      setupTime?: number;
      workCenter?: string;
      description?: string;
    };
  };
  selected?: boolean;
}

const props = defineProps<Props>();
</script>

<template>
  <div class="process-step-node">
    <!-- Input Handle -->
    <Handle
      type="target"
      :position="Position.Left"
      class="w-3 h-3 !bg-blue-500"
    />

    <!-- Node Content -->
    <div class="p-3 min-w-[160px]">
      <div class="flex items-center gap-2 mb-2">
        <div class="p-1 bg-blue-500 rounded text-white">
          <Wrench class="h-4 w-4" />
        </div>
        <div class="font-medium text-sm">{{ data.label }}</div>
      </div>

      <div class="space-y-1 text-xs text-muted-foreground">
        <div v-if="data.properties.processingTime" class="flex items-center gap-1">
          <Clock class="h-3 w-3" />
          <span>{{ data.properties.processingTime }}分钟</span>
        </div>
        <div v-if="data.properties.workCenter" class="flex items-center gap-1">
          <Settings class="h-3 w-3" />
          <span>{{ data.properties.workCenter }}</span>
        </div>
      </div>

      <Badge variant="outline" class="mt-2 text-xs">
        工序节点
      </Badge>
    </div>

    <!-- Output Handle -->
    <Handle
      type="source"
      :position="Position.Right"
      class="w-3 h-3 !bg-blue-500"
    />
  </div>
</template>

<style scoped>
.process-step-node {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.process-step-node:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
