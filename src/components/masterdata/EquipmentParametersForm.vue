<template>
  <div class="space-y-6">
    <!-- 产能参数配置 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">产能参数</Label>
        <Badge variant="outline">{{ equipmentTypeLabel }}</Badge>
      </div>

      <!-- 通用参数 -->
      <div class="grid grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label for="efficiency">设备效率 (%)</Label>
          <Input
            id="efficiency"
            v-model.number="localParameters.efficiency"
            :disabled="!isEditing"
            type="number"
            min="0"
            max="100"
            placeholder="95"
          />
          <p class="text-xs text-muted-foreground">设备的综合效率百分比</p>
        </div>

        <div class="space-y-2">
          <Label for="power">功率 (kW)</Label>
          <Input
            id="power"
            v-model.number="localParameters.power"
            :disabled="!isEditing"
            type="number"
            min="0"
            placeholder="15"
          />
          <p class="text-xs text-muted-foreground">设备额定功率</p>
        </div>
      </div>

      <!-- 设备类型特定参数 -->
      <div v-if="equipmentType === 'cutting'" class="space-y-4">
        <h4 class="font-medium text-sm">切割设备参数</h4>
        <div class="grid grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label for="max_width">最大宽度 (mm)</Label>
            <Input
              id="max_width"
              v-model.number="localParameters.max_width"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="2440"
            />
          </div>
          <div class="space-y-2">
            <Label for="max_height">最大高度 (mm)</Label>
            <Input
              id="max_height"
              v-model.number="localParameters.max_height"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="3660"
            />
          </div>
          <div class="space-y-2">
            <Label for="cutting_speed">切割速度 (m/min)</Label>
            <Input
              id="cutting_speed"
              v-model.number="localParameters.speed"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="120"
            />
          </div>
        </div>
      </div>

      <div v-else-if="equipmentType === 'edging'" class="space-y-4">
        <h4 class="font-medium text-sm">磨边设备参数</h4>
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="edging_speed">磨边速度 (m/min)</Label>
            <Input
              id="edging_speed"
              v-model.number="localParameters.speed"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="15"
            />
          </div>
          <div class="space-y-2">
            <Label for="max_thickness">最大厚度 (mm)</Label>
            <Input
              id="max_thickness"
              v-model.number="localParameters.max_thickness"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="19"
            />
          </div>
        </div>
      </div>

      <div v-else-if="equipmentType === 'tempering'" class="space-y-4">
        <h4 class="font-medium text-sm">钢化设备参数</h4>
        <div class="grid grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label for="furnace_area">炉膛面积 (m²)</Label>
            <Input
              id="furnace_area"
              v-model.number="localParameters.area"
              :disabled="!isEditing"
              type="number"
              min="0"
              step="0.01"
              placeholder="10.08"
            />
          </div>
          <div class="space-y-2">
            <Label for="cycle_time">炉次时间 (秒)</Label>
            <Input
              id="cycle_time"
              v-model.number="localParameters.cycle_time"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="1800"
            />
          </div>
          <div class="space-y-2">
            <Label for="max_temp">最高温度 (°C)</Label>
            <Input
              id="max_temp"
              v-model.number="localParameters.max_temperature"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="700"
            />
          </div>
        </div>
      </div>

      <div v-else-if="equipmentType === 'drilling'" class="space-y-4">
        <h4 class="font-medium text-sm">钻孔设备参数</h4>
        <div class="grid grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label for="max_holes_per_minute">每分钟钻孔数</Label>
            <Input
              id="max_holes_per_minute"
              v-model.number="localParameters.max_holes_per_minute"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="30"
            />
          </div>
          <div class="space-y-2">
            <Label for="max_diameter">最大孔径 (mm)</Label>
            <Input
              id="max_diameter"
              v-model.number="localParameters.max_diameter"
              :disabled="!isEditing"
              type="number"
              min="0"
              placeholder="50"
            />
          </div>
          <div class="space-y-2">
            <Label for="precision">钻孔精度 (mm)</Label>
            <Input
              id="precision"
              v-model.number="localParameters.precision"
              :disabled="!isEditing"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.1"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 产能计算公式 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">产能计算公式</Label>
      
      <div class="space-y-3">
        <div class="space-y-2">
          <Label for="capacity_formula">产能公式</Label>
          <Textarea
            id="capacity_formula"
            v-model="localParameters.capacity_formula"
            :disabled="!isEditing"
            placeholder="例如：(max_width * max_height * efficiency / 100) / cycle_time"
            rows="2"
          />
          <p class="text-xs text-muted-foreground">
            用于计算设备理论产能的公式，可使用上述参数变量
          </p>
        </div>

        <div class="space-y-2">
          <Label for="throughput_formula">产量公式</Label>
          <Textarea
            id="throughput_formula"
            v-model="localParameters.throughput_formula"
            :disabled="!isEditing"
            placeholder="例如：speed * efficiency / 100 * working_hours"
            rows="2"
          />
          <p class="text-xs text-muted-foreground">
            用于计算实际产量的公式
          </p>
        </div>
      </div>
    </div>

    <!-- 产能预览 -->
    <div v-if="capacityPreview" class="space-y-4">
      <Label class="text-base font-medium">产能预览</Label>
      
      <div class="grid grid-cols-3 gap-4">
        <Card class="p-4">
          <div class="text-sm text-muted-foreground">理论产能</div>
          <div class="text-2xl font-bold">{{ capacityPreview.theoretical }}</div>
          <div class="text-xs text-muted-foreground">{{ capacityPreview.unit }}/小时</div>
        </Card>
        
        <Card class="p-4">
          <div class="text-sm text-muted-foreground">实际产能</div>
          <div class="text-2xl font-bold">{{ capacityPreview.actual }}</div>
          <div class="text-xs text-muted-foreground">{{ capacityPreview.unit }}/小时</div>
        </Card>
        
        <Card class="p-4">
          <div class="text-sm text-muted-foreground">效率比</div>
          <div class="text-2xl font-bold">{{ capacityPreview.efficiency }}%</div>
          <div class="text-xs text-muted-foreground">实际/理论</div>
        </Card>
      </div>
    </div>

    <!-- 自定义参数 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">自定义参数</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addCustomParameter"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加参数
        </Button>
      </div>

      <div v-if="customParameters.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无自定义参数
      </div>

      <div v-else class="space-y-2 max-h-48 overflow-y-auto list-scroll-area">
        <div
          v-for="(param, index) in customParameters"
          :key="index"
          class="flex items-center gap-2 p-3 border rounded-lg"
        >
          <Input
            v-model="param.name"
            :disabled="!isEditing"
            placeholder="参数名称"
            class="flex-1"
          />
          <Input
            v-model="param.value"
            :disabled="!isEditing"
            placeholder="参数值"
            class="flex-1"
          />
          <Input
            v-model="param.unit"
            :disabled="!isEditing"
            placeholder="单位"
            class="w-20"
          />
          <Button
            v-if="isEditing"
            variant="ghost"
            size="sm"
            @click="removeCustomParameter(index)"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Plus, Trash2 } from 'lucide-vue-next';

interface Props {
  modelValue: Record<string, any>;
  equipmentType: string;
  isEditing: boolean;
  errors: Record<string, string>;
}

interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void;
  (e: 'validate', errors: Record<string, string>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localParameters = ref<Record<string, any>>({ ...props.modelValue });
const customParameters = ref<Array<{ name: string; value: string; unit: string }>>([]);
const isUpdating = ref(false);

// 计算属性
const equipmentTypeLabel = computed(() => {
  const typeMap: Record<string, string> = {
    cutting: '切割设备',
    edging: '磨边设备',
    tempering: '钢化设备',
    washing: '清洗设备',
    assembly: '装配设备',
    drilling: '钻孔设备',
    other: '其他设备'
  };
  return typeMap[props.equipmentType] || '未知类型';
});

const capacityPreview = computed(() => {
  if (!localParameters.value.efficiency) return null;
  
  // 简化的产能计算示例
  const efficiency = localParameters.value.efficiency || 100;
  let theoretical = 0;
  let unit = '件';
  
  switch (props.equipmentType) {
    case 'cutting':
      theoretical = (localParameters.value.speed || 120) * 60; // 每小时切割长度
      unit = 'm';
      break;
    case 'tempering':
      theoretical = (localParameters.value.area || 10) * 3; // 每小时钢化面积
      unit = 'm²';
      break;
    case 'drilling':
      theoretical = (localParameters.value.max_holes_per_minute || 30) * 60; // 每小时钻孔数
      unit = '孔';
      break;
    default:
      theoretical = 100;
  }
  
  const actual = Math.round(theoretical * efficiency / 100);
  
  return {
    theoretical: Math.round(theoretical),
    actual,
    efficiency: Math.round((actual / theoretical) * 100),
    unit
  };
});

// 方法
const addCustomParameter = () => {
  customParameters.value.push({ name: '', value: '', unit: '' });
};

const removeCustomParameter = (index: number) => {
  customParameters.value.splice(index, 1);
  updateCustomParameters();
};

const updateCustomParameters = () => {
  localParameters.value.custom = customParameters.value;
  updateModelValue();
};

const updateModelValue = () => {
  if (isUpdating.value) return;
  emit('update:modelValue', { ...localParameters.value });
};

// 监听器
watch(localParameters, () => {
  updateModelValue();
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  if (isUpdating.value) return;
  
  isUpdating.value = true;
  localParameters.value = { ...newValue };
  
  // 初始化自定义参数
  if (newValue.custom) {
    customParameters.value = [...newValue.custom];
  } else {
    customParameters.value = [];
  }
  
  nextTick(() => {
    isUpdating.value = false;
  });
}, { deep: true });

// 初始化
if (props.modelValue.custom) {
  customParameters.value = [...props.modelValue.custom];
}
</script>
