<template>
  <div class="space-y-6">
    <!-- 实时状态监控 -->
    <div class="grid grid-cols-4 gap-4">
      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div :class="['p-2 rounded-lg', getStatusColorClass(equipment?.status)]">
            <component :is="getStatusIcon(equipment?.status)" class="w-5 h-5" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">设备状态</div>
            <div class="font-semibold">{{ getStatusLabel(equipment?.status) }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <Zap class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">运行时间</div>
            <div class="font-semibold">{{ runningTime }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <TrendingUp class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">效率</div>
            <div class="font-semibold">{{ equipment?.parameters?.efficiency || 0 }}%</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 rounded-lg">
            <AlertTriangle class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">告警数量</div>
            <div class="font-semibold">{{ alertCount }}</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 状态控制 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">状态控制</Label>
      
      <div class="flex gap-2">
        <Button
          v-for="status in statusOptions"
          :key="status.value"
          :variant="equipment?.status === status.value ? 'default' : 'outline'"
          size="sm"
          @click="changeStatus(status.value)"
          :disabled="!canChangeStatus(status.value)"
        >
          <component :is="status.icon" class="w-4 h-4 mr-2" />
          {{ status.label }}
        </Button>
      </div>

      <div v-if="statusChangeReason" class="space-y-2">
        <Label for="statusReason">状态变更原因</Label>
        <Textarea
          id="statusReason"
          v-model="statusChangeReason"
          placeholder="请输入状态变更的原因..."
          rows="2"
        />
      </div>
    </div>

    <!-- 实时参数监控 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">实时参数监控</Label>
      
      <div class="grid grid-cols-2 gap-4">
        <Card class="p-4">
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">功率消耗</span>
              <Badge variant="outline">{{ currentPower }} kW</Badge>
            </div>
            <div class="space-y-1">
              <div class="flex justify-between text-xs">
                <span>0 kW</span>
                <span>{{ equipment?.parameters?.power || 100 }} kW</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="bg-blue-500 h-2 rounded-full"
                  :style="{ width: `${powerPercentage}%` }"
                ></div>
              </div>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">温度</span>
              <Badge variant="outline">{{ currentTemperature }}°C</Badge>
            </div>
            <div class="space-y-1">
              <div class="flex justify-between text-xs">
                <span>0°C</span>
                <span>{{ maxTemperature }}°C</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  :class="['h-2 rounded-full', getTemperatureColor()]"
                  :style="{ width: `${temperaturePercentage}%` }"
                ></div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <div class="grid grid-cols-3 gap-4">
        <Card class="p-3">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ vibrationLevel }}</div>
            <div class="text-xs text-muted-foreground">振动水平</div>
          </div>
        </Card>

        <Card class="p-3">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ noiseLevel }}</div>
            <div class="text-xs text-muted-foreground">噪音水平 (dB)</div>
          </div>
        </Card>

        <Card class="p-3">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ productionCount }}</div>
            <div class="text-xs text-muted-foreground">今日产量</div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 告警信息 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">告警信息</Label>
        <Button variant="outline" size="sm" @click="refreshAlerts">
          <RefreshCw class="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>

      <div v-if="alerts.length === 0" class="text-sm text-muted-foreground text-center py-8 border rounded-lg">
        <CheckCircle class="w-8 h-8 mx-auto mb-2 text-green-500" />
        设备运行正常，无告警信息
      </div>

      <div v-else class="space-y-2">
        <Card
          v-for="alert in alerts"
          :key="alert.id"
          class="p-3"
          :class="getAlertCardClass(alert.level)"
        >
          <div class="flex items-start justify-between">
            <div class="flex items-start gap-3">
              <component
                :is="getAlertIcon(alert.level)"
                :class="['w-5 h-5 mt-0.5', getAlertIconClass(alert.level)]"
              />
              <div>
                <div class="font-medium text-sm">{{ alert.title }}</div>
                <div class="text-xs text-muted-foreground mt-1">{{ alert.description }}</div>
                <div class="text-xs text-muted-foreground mt-1">{{ alert.timestamp }}</div>
              </div>
            </div>
            
            <div class="flex items-center gap-2">
              <Badge :variant="getAlertBadgeVariant(alert.level)">
                {{ getAlertLevelLabel(alert.level) }}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                @click="acknowledgeAlert(alert.id)"
              >
                <Check class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 性能图表 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">性能趋势</Label>
      
      <Card class="p-4">
        <div class="h-64 flex items-center justify-center text-muted-foreground">
          <div class="text-center">
            <BarChart3 class="w-12 h-12 mx-auto mb-2" />
            <div>性能图表</div>
            <div class="text-sm">显示设备运行效率、产量等趋势数据</div>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import {
  Play, Pause, Square, Wrench, Zap, TrendingUp, AlertTriangle,
  RefreshCw, CheckCircle, Check, BarChart3, AlertCircle, XCircle
} from 'lucide-vue-next';
import type { Equipment } from '@/types/masterdata';

interface Alert {
  id: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  description: string;
  timestamp: string;
}

interface Props {
  equipment?: Equipment | null;
}

interface Emits {
  (e: 'status-change', status: Equipment['status']): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const statusChangeReason = ref('');
const alerts = ref<Alert[]>([]);
const currentPower = ref(0);
const currentTemperature = ref(25);
const vibrationLevel = ref('正常');
const noiseLevel = ref(45);
const productionCount = ref(0);
const runningTime = ref('0小时');
const alertCount = ref(0);

// 模拟数据更新定时器
let dataUpdateTimer: NodeJS.Timeout | null = null;

// 状态选项
const statusOptions = [
  { value: 'running', label: '运行', icon: Play },
  { value: 'idle', label: '空闲', icon: Pause },
  { value: 'maintenance', label: '维护', icon: Wrench },
  { value: 'fault', label: '故障', icon: Square }
];

// 计算属性
const powerPercentage = computed(() => {
  const maxPower = props.equipment?.parameters?.power || 100;
  return Math.min((currentPower.value / maxPower) * 100, 100);
});

const maxTemperature = computed(() => {
  return props.equipment?.parameters?.max_temperature || 100;
});

const temperaturePercentage = computed(() => {
  return Math.min((currentTemperature.value / maxTemperature.value) * 100, 100);
});

// 方法
const getStatusIcon = (status?: string) => {
  const iconMap: Record<string, any> = {
    running: Play,
    idle: Pause,
    fault: XCircle,
    maintenance: Wrench
  };
  return iconMap[status || 'idle'] || Pause;
};

const getStatusColorClass = (status?: string) => {
  const colorMap: Record<string, string> = {
    running: 'bg-green-100 text-green-600',
    idle: 'bg-gray-100 text-gray-600',
    fault: 'bg-red-100 text-red-600',
    maintenance: 'bg-yellow-100 text-yellow-600'
  };
  return colorMap[status || 'idle'] || 'bg-gray-100 text-gray-600';
};

const getStatusLabel = (status?: string) => {
  const labelMap: Record<string, string> = {
    running: '运行中',
    idle: '空闲',
    fault: '故障',
    maintenance: '维护中'
  };
  return labelMap[status || 'idle'] || '未知';
};

const getTemperatureColor = () => {
  if (temperaturePercentage.value > 80) return 'bg-red-500';
  if (temperaturePercentage.value > 60) return 'bg-yellow-500';
  return 'bg-green-500';
};

const canChangeStatus = (newStatus: string) => {
  // 简单的状态变更规则
  const currentStatus = props.equipment?.status;
  if (currentStatus === 'fault' && newStatus !== 'maintenance') return false;
  return true;
};

const changeStatus = (newStatus: Equipment['status']) => {
  if (!canChangeStatus(newStatus)) return;
  
  statusChangeReason.value = '';
  emit('status-change', newStatus);
  
  // 模拟状态变更后的参数调整
  updateSimulatedData();
};

const getAlertIcon = (level: string) => {
  const iconMap: Record<string, any> = {
    info: CheckCircle,
    warning: AlertTriangle,
    error: AlertCircle,
    critical: XCircle
  };
  return iconMap[level] || AlertCircle;
};

const getAlertIconClass = (level: string) => {
  const classMap: Record<string, string> = {
    info: 'text-blue-500',
    warning: 'text-yellow-500',
    error: 'text-orange-500',
    critical: 'text-red-500'
  };
  return classMap[level] || 'text-gray-500';
};

const getAlertCardClass = (level: string) => {
  const classMap: Record<string, string> = {
    info: 'border-blue-200',
    warning: 'border-yellow-200',
    error: 'border-orange-200',
    critical: 'border-red-200'
  };
  return classMap[level] || '';
};

const getAlertBadgeVariant = (level: string) => {
  const variantMap: Record<string, string> = {
    info: 'default',
    warning: 'outline',
    error: 'secondary',
    critical: 'destructive'
  };
  return variantMap[level] || 'default';
};

const getAlertLevelLabel = (level: string) => {
  const labelMap: Record<string, string> = {
    info: '信息',
    warning: '警告',
    error: '错误',
    critical: '严重'
  };
  return labelMap[level] || level;
};

const acknowledgeAlert = (alertId: string) => {
  alerts.value = alerts.value.filter(alert => alert.id !== alertId);
  alertCount.value = alerts.value.length;
};

const refreshAlerts = () => {
  // 模拟刷新告警
  generateMockAlerts();
};

const generateMockAlerts = () => {
  const mockAlerts: Alert[] = [];
  
  if (props.equipment?.status === 'fault') {
    mockAlerts.push({
      id: 'alert-1',
      level: 'critical',
      title: '设备故障',
      description: '设备出现机械故障，需要立即维修',
      timestamp: new Date().toLocaleString()
    });
  }
  
  if (props.equipment?.status === 'maintenance') {
    mockAlerts.push({
      id: 'alert-2',
      level: 'warning',
      title: '维护模式',
      description: '设备正在进行计划维护',
      timestamp: new Date().toLocaleString()
    });
  }
  
  if (currentTemperature.value > 80) {
    mockAlerts.push({
      id: 'alert-3',
      level: 'warning',
      title: '温度过高',
      description: `设备温度达到 ${currentTemperature.value}°C，请注意监控`,
      timestamp: new Date().toLocaleString()
    });
  }
  
  alerts.value = mockAlerts;
  alertCount.value = mockAlerts.length;
};

const updateSimulatedData = () => {
  const status = props.equipment?.status;
  
  switch (status) {
    case 'running':
      currentPower.value = Math.random() * (props.equipment?.parameters?.power || 100) * 0.8 + 20;
      currentTemperature.value = Math.random() * 30 + 40;
      productionCount.value += Math.floor(Math.random() * 5);
      break;
    case 'idle':
      currentPower.value = Math.random() * 10 + 5;
      currentTemperature.value = Math.random() * 10 + 25;
      break;
    case 'maintenance':
    case 'fault':
      currentPower.value = 0;
      currentTemperature.value = Math.random() * 5 + 20;
      break;
  }
  
  generateMockAlerts();
};

const startDataUpdate = () => {
  dataUpdateTimer = setInterval(() => {
    updateSimulatedData();
  }, 5000); // 每5秒更新一次
};

const stopDataUpdate = () => {
  if (dataUpdateTimer) {
    clearInterval(dataUpdateTimer);
    dataUpdateTimer = null;
  }
};

// 生命周期
onMounted(() => {
  updateSimulatedData();
  startDataUpdate();
});

onUnmounted(() => {
  stopDataUpdate();
});
</script>
