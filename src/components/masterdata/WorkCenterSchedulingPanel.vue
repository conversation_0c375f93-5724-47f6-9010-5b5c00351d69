<template>
  <div class="space-y-6">
    <!-- 排程状态概览 -->
    <div class="grid grid-cols-4 gap-4">
      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <Calendar class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">今日任务</div>
            <div class="font-semibold">{{ todayTasks }} 个</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <CheckCircle class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">已完成</div>
            <div class="font-semibold">{{ completedTasks }} 个</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 rounded-lg">
            <Clock class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">进行中</div>
            <div class="font-semibold">{{ inProgressTasks }} 个</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-red-100 rounded-lg">
            <AlertTriangle class="w-5 h-5 text-red-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">延期任务</div>
            <div class="font-semibold">{{ delayedTasks }} 个</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 排程规则配置 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">排程规则配置</Label>
      
      <div class="grid grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label for="schedulingStrategy">排程策略</Label>
          <Select v-model="schedulingStrategy" :disabled="!isEditing">
            <SelectTrigger>
              <SelectValue placeholder="选择排程策略" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="fifo">先进先出 (FIFO)</SelectItem>
              <SelectItem value="priority">优先级排程</SelectItem>
              <SelectItem value="shortest">最短作业优先</SelectItem>
              <SelectItem value="deadline">交期优先</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div class="space-y-2">
          <Label for="bufferTime">缓冲时间 (分钟)</Label>
          <Input
            id="bufferTime"
            v-model.number="bufferTime"
            :disabled="!isEditing"
            type="number"
            min="0"
            placeholder="15"
          />
        </div>
      </div>
    </div>

    <!-- 当前排程计划 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">当前排程计划</Label>
      
      <div class="max-h-64 overflow-y-auto list-scroll-area">
        <div v-if="scheduledTasks.length === 0" class="text-center py-8 text-muted-foreground">
          暂无排程任务
        </div>
        
        <div v-else class="space-y-2">
          <Card
            v-for="task in scheduledTasks"
            :key="task.id"
            class="p-3"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">
                  {{ task.sequence }}
                </div>
                <div>
                  <div class="font-medium text-sm">{{ task.orderNumber }}</div>
                  <div class="text-xs text-muted-foreground">{{ task.processName }}</div>
                </div>
              </div>
              
              <div class="flex items-center gap-2">
                <Badge :variant="getTaskStatusBadge(task.status)">
                  {{ getTaskStatusLabel(task.status) }}
                </Badge>
                <div class="text-xs text-muted-foreground">
                  {{ task.plannedStartTime }} - {{ task.plannedEndTime }}
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calendar, CheckCircle, Clock, AlertTriangle, ChevronUp, ChevronDown
} from 'lucide-vue-next';
import type { WorkCenter } from '@/types/masterdata';

interface ScheduledTask {
  id: string;
  sequence: number;
  orderNumber: string;
  processName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'delayed';
  plannedStartTime: string;
  plannedEndTime: string;
  actualStartTime?: string;
  actualEndTime?: string;
}

interface Props {
  workCenter?: WorkCenter | null;
  isEditing: boolean;
}

interface Emits {
  (e: 'update-schedule', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const schedulingStrategy = ref('priority');
const bufferTime = ref(15);
const scheduledTasks = ref<ScheduledTask[]>([]);

// 模拟数据
const todayTasks = ref(8);
const completedTasks = ref(3);
const inProgressTasks = ref(2);
const delayedTasks = ref(1);

// 方法
const getTaskStatusBadge = (status: string) => {
  const badgeMap: Record<string, string> = {
    pending: 'secondary',
    in_progress: 'default',
    completed: 'outline',
    delayed: 'destructive'
  };
  return badgeMap[status] || 'secondary';
};

const getTaskStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成',
    delayed: '延期'
  };
  return labelMap[status] || status;
};

const moveProcessUp = (index: number) => {
  if (index > 0) {
    const tasks = [...scheduledTasks.value];
    [tasks[index], tasks[index - 1]] = [tasks[index - 1], tasks[index]];
    // 更新序号
    tasks.forEach((task, i) => task.sequence = i + 1);
    scheduledTasks.value = tasks;
  }
};

const moveProcessDown = (index: number) => {
  if (index < scheduledTasks.value.length - 1) {
    const tasks = [...scheduledTasks.value];
    [tasks[index], tasks[index + 1]] = [tasks[index + 1], tasks[index]];
    // 更新序号
    tasks.forEach((task, i) => task.sequence = i + 1);
    scheduledTasks.value = tasks;
  }
};

const loadScheduledTasks = () => {
  // 模拟排程任务数据
  scheduledTasks.value = [
    {
      id: '1',
      sequence: 1,
      orderNumber: 'SO-2025-001',
      processName: '玻璃切割',
      status: 'completed',
      plannedStartTime: '08:00',
      plannedEndTime: '09:30'
    },
    {
      id: '2',
      sequence: 2,
      orderNumber: 'SO-2025-002',
      processName: '玻璃磨边',
      status: 'in_progress',
      plannedStartTime: '09:30',
      plannedEndTime: '11:00'
    },
    {
      id: '3',
      sequence: 3,
      orderNumber: 'SO-2025-003',
      processName: '玻璃钻孔',
      status: 'pending',
      plannedStartTime: '11:00',
      plannedEndTime: '12:30'
    }
  ];
};

// 生命周期
onMounted(() => {
  loadScheduledTasks();
});
</script>
