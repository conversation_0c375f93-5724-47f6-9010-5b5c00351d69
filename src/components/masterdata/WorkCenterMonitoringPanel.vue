<template>
  <div class="space-y-6">
    <!-- 实时监控概览 -->
    <div class="grid grid-cols-4 gap-4">
      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <Activity class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">运行状态</div>
            <div class="font-semibold">{{ workCenterStatus }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <TrendingUp class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">当前效率</div>
            <div class="font-semibold">{{ currentEfficiency }}%</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 rounded-lg">
            <BarChart3 class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">产能利用率</div>
            <div class="font-semibold">{{ utilizationRate }}%</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-purple-100 rounded-lg">
            <Target class="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">今日产量</div>
            <div class="font-semibold">{{ todayProduction }}</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 设备运行状态 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">设备运行状态</Label>
      
      <div class="grid grid-cols-2 gap-4">
        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">设备状态分布</div>
            <div class="space-y-2">
              <div
                v-for="status in equipmentStatusDistribution"
                :key="status.status"
                class="flex items-center justify-between"
              >
                <div class="flex items-center gap-2">
                  <div :class="['w-3 h-3 rounded-full', getStatusColor(status.status)]"></div>
                  <span class="text-sm">{{ getStatusLabel(status.status) }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <div class="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      :class="['h-2 rounded-full', getStatusColor(status.status)]"
                      :style="{ width: `${status.percentage}%` }"
                    ></div>
                  </div>
                  <span class="text-sm font-medium w-8">{{ status.count }}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">产能趋势</div>
            <div class="h-32 flex items-center justify-center text-muted-foreground">
              <div class="text-center">
                <TrendingUp class="w-8 h-8 mx-auto mb-2" />
                <div class="text-sm">产能趋势图表</div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 实时任务监控 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">实时任务监控</Label>
      
      <div class="max-h-64 overflow-y-auto list-scroll-area">
        <div v-if="currentTasks.length === 0" class="text-center py-8 text-muted-foreground">
          当前无正在执行的任务
        </div>
        
        <div v-else class="space-y-3">
          <Card
            v-for="task in currentTasks"
            :key="task.id"
            class="p-4"
          >
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">
                  {{ task.sequence }}
                </div>
                <div>
                  <div class="font-medium">{{ task.orderNumber }}</div>
                  <div class="text-sm text-muted-foreground">{{ task.processName }}</div>
                </div>
              </div>
              
              <div class="flex items-center gap-2">
                <Badge :variant="getTaskStatusBadge(task.status)">
                  {{ getTaskStatusLabel(task.status) }}
                </Badge>
              </div>
            </div>

            <!-- 进度条 -->
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span>进度</span>
                <span>{{ task.progress }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${task.progress}%` }"
                ></div>
              </div>
            </div>

            <div class="mt-3 grid grid-cols-3 gap-4 text-xs">
              <div>
                <span class="text-muted-foreground">计划时间:</span>
                <span class="ml-1">{{ task.plannedDuration }}分钟</span>
              </div>
              <div>
                <span class="text-muted-foreground">已用时间:</span>
                <span class="ml-1">{{ task.actualDuration }}分钟</span>
              </div>
              <div>
                <span class="text-muted-foreground">剩余时间:</span>
                <span class="ml-1">{{ task.remainingTime }}分钟</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>

    <!-- 性能指标 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">性能指标</Label>
      
      <div class="grid grid-cols-3 gap-4">
        <Card class="p-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ oeeScore }}%</div>
            <div class="text-sm text-muted-foreground">OEE综合效率</div>
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ onTimeDelivery }}%</div>
            <div class="text-sm text-muted-foreground">准时交付率</div>
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-orange-600">{{ qualityRate }}%</div>
            <div class="text-sm text-muted-foreground">质量合格率</div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calendar, CheckCircle, Clock, AlertTriangle, Activity, TrendingUp, 
  BarChart3, Target
} from 'lucide-vue-next';
import type { WorkCenter } from '@/types/masterdata';

interface Props {
  workCenter?: WorkCenter | null;
}

const props = defineProps<Props>();

// 响应式数据
const schedulingStrategy = ref('priority');
const bufferTime = ref(15);
const currentTasks = ref<any[]>([]);

// 模拟实时数据
const workCenterStatus = ref('正常运行');
const currentEfficiency = ref(92);
const utilizationRate = ref(78);
const todayProduction = ref(156);
const oeeScore = ref(85);
const onTimeDelivery = ref(94);
const qualityRate = ref(98);

// 设备状态分布
const equipmentStatusDistribution = ref([
  { status: 'running', count: 3, percentage: 60 },
  { status: 'idle', count: 1, percentage: 20 },
  { status: 'maintenance', count: 1, percentage: 20 },
  { status: 'fault', count: 0, percentage: 0 }
]);

// 方法
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    running: 'bg-green-500',
    idle: 'bg-gray-400',
    maintenance: 'bg-yellow-500',
    fault: 'bg-red-500'
  };
  return colorMap[status] || 'bg-gray-400';
};

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    running: '运行中',
    idle: '空闲',
    maintenance: '维护中',
    fault: '故障'
  };
  return labelMap[status] || status;
};

const getTaskStatusBadge = (status: string) => {
  const badgeMap: Record<string, string> = {
    pending: 'secondary',
    in_progress: 'default',
    completed: 'outline',
    delayed: 'destructive'
  };
  return badgeMap[status] || 'secondary';
};

const getTaskStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成',
    delayed: '延期'
  };
  return labelMap[status] || status;
};

const loadCurrentTasks = () => {
  // 模拟当前任务数据
  currentTasks.value = [
    {
      id: '1',
      sequence: 1,
      orderNumber: 'SO-2025-002',
      processName: '玻璃磨边',
      status: 'in_progress',
      progress: 65,
      plannedDuration: 90,
      actualDuration: 58,
      remainingTime: 32
    },
    {
      id: '2',
      sequence: 2,
      orderNumber: 'SO-2025-003',
      processName: '玻璃钻孔',
      status: 'pending',
      progress: 0,
      plannedDuration: 45,
      actualDuration: 0,
      remainingTime: 45
    }
  ];
};

// 生命周期
onMounted(() => {
  loadCurrentTasks();
});
</script>
