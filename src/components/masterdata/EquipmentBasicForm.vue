<template>
  <div class="space-y-6">
    <div class="grid grid-cols-2 gap-4">
      <!-- 设备名称 -->
      <div class="space-y-2">
        <Label for="name" class="required">设备名称</Label>
        <Input
          id="name"
          v-model="localData.name"
          :disabled="!isEditing"
          placeholder="请输入设备名称"
          :class="{ 'border-destructive': errors.name }"
          @blur="validateField('name')"
        />
        <p v-if="errors.name" class="text-sm text-destructive">{{ errors.name }}</p>
      </div>

      <!-- 设备型号 -->
      <div class="space-y-2">
        <Label for="model" class="required">设备型号</Label>
        <Input
          id="model"
          v-model="localData.model"
          :disabled="!isEditing"
          placeholder="请输入设备型号"
          :class="{ 'border-destructive': errors.model }"
          @blur="validateField('model')"
        />
        <p v-if="errors.model" class="text-sm text-destructive">{{ errors.model }}</p>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <!-- 设备状态 -->
      <div class="space-y-2">
        <Label for="status">设备状态</Label>
        <Select v-model="localData.status" :disabled="!isEditing">
          <SelectTrigger>
            <SelectValue placeholder="选择设备状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="option in statusOptions" :key="option.value" :value="option.value">
              <div class="flex items-center gap-2">
                <div :class="['w-2 h-2 rounded-full', getStatusColor(option.value)]"></div>
                {{ option.label }}
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 设备位置 -->
      <div class="space-y-2">
        <Label for="location">设备位置</Label>
        <Input
          id="location"
          v-model="localData.location"
          :disabled="!isEditing"
          placeholder="请输入设备位置"
        />
      </div>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <!-- 资产编号 -->
      <div class="space-y-2">
        <Label for="assetNumber">资产编号</Label>
        <Input
          id="assetNumber"
          v-model="localData.assetNumber"
          :disabled="!isEditing"
          placeholder="请输入资产编号"
          :class="{ 'border-destructive': errors.assetNumber }"
          @blur="validateField('assetNumber')"
        />
        <p v-if="errors.assetNumber" class="text-sm text-destructive">{{ errors.assetNumber }}</p>
      </div>

      <!-- 设备类型 -->
      <div class="space-y-2">
        <Label for="equipmentType">设备类型</Label>
        <Select v-model="localData.equipmentType" :disabled="!isEditing">
          <SelectTrigger>
            <SelectValue placeholder="选择设备类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="type in equipmentTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <!-- 安装日期 -->
      <div class="space-y-2">
        <Label for="installDate">安装日期</Label>
        <Input
          id="installDate"
          v-model="localData.installDate"
          :disabled="!isEditing"
          type="date"
        />
      </div>

      <!-- 制造商 -->
      <div class="space-y-2">
        <Label for="manufacturer">制造商</Label>
        <Input
          id="manufacturer"
          v-model="localData.manufacturer"
          :disabled="!isEditing"
          placeholder="请输入制造商"
        />
      </div>
    </div>

    <!-- 设备描述 -->
    <div class="space-y-2">
      <Label for="description">设备描述</Label>
      <Textarea
        id="description"
        v-model="localData.description"
        :disabled="!isEditing"
        placeholder="请输入设备描述"
        rows="3"
      />
    </div>

    <!-- 技术规格 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">技术规格</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addSpecification"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加规格
        </Button>
      </div>

      <div v-if="specifications.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无技术规格信息
      </div>

      <div v-else class="space-y-2 max-h-48 overflow-y-auto list-scroll-area">
        <div
          v-for="(spec, index) in specifications"
          :key="index"
          class="flex items-center gap-2 p-3 border rounded-lg"
        >
          <Input
            v-model="spec.name"
            :disabled="!isEditing"
            placeholder="规格名称"
            class="flex-1"
          />
          <Input
            v-model="spec.value"
            :disabled="!isEditing"
            placeholder="规格值"
            class="flex-1"
          />
          <Input
            v-model="spec.unit"
            :disabled="!isEditing"
            placeholder="单位"
            class="w-20"
          />
          <Button
            v-if="isEditing"
            variant="ghost"
            size="sm"
            @click="removeSpecification(index)"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2 } from 'lucide-vue-next';
import type { Equipment } from '@/types/masterdata';

interface Props {
  modelValue: Partial<Equipment>;
  isEditing: boolean;
  errors: Record<string, string>;
}

interface Emits {
  (e: 'update:modelValue', value: Partial<Equipment>): void;
  (e: 'validate', errors: Record<string, string>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localData = ref<Partial<Equipment>>({ ...props.modelValue });
const isUpdating = ref(false);

// 技术规格
const specifications = ref<Array<{ name: string; value: string; unit: string }>>([]);

// 选项配置
const statusOptions = [
  { value: 'running', label: '运行中' },
  { value: 'idle', label: '空闲' },
  { value: 'fault', label: '故障' },
  { value: 'maintenance', label: '维护中' }
];

const equipmentTypes = [
  { value: 'cutting', label: '切割设备' },
  { value: 'edging', label: '磨边设备' },
  { value: 'tempering', label: '钢化设备' },
  { value: 'washing', label: '清洗设备' },
  { value: 'assembly', label: '装配设备' },
  { value: 'drilling', label: '钻孔设备' },
  { value: 'other', label: '其他设备' }
];

// 计算属性
const errors = computed(() => props.errors);

// 方法
const getStatusColor = (status: string) => {
  switch (status) {
    case 'running': return 'bg-green-500';
    case 'idle': return 'bg-gray-400';
    case 'fault': return 'bg-red-500';
    case 'maintenance': return 'bg-yellow-500';
    default: return 'bg-gray-400';
  }
};

const validateField = (fieldName: string) => {
  const newErrors = { ...errors.value };
  
  switch (fieldName) {
    case 'name':
      if (!localData.value.name?.trim()) {
        newErrors.name = '设备名称不能为空';
      } else {
        delete newErrors.name;
      }
      break;
      
    case 'model':
      if (!localData.value.model?.trim()) {
        newErrors.model = '设备型号不能为空';
      } else {
        delete newErrors.model;
      }
      break;
      
    case 'assetNumber':
      if (localData.value.assetNumber && !/^[A-Za-z0-9-]+$/.test(localData.value.assetNumber)) {
        newErrors.assetNumber = '资产编号格式不正确';
      } else {
        delete newErrors.assetNumber;
      }
      break;
  }
  
  emit('validate', newErrors);
};

const addSpecification = () => {
  specifications.value.push({ name: '', value: '', unit: '' });
};

const removeSpecification = (index: number) => {
  specifications.value.splice(index, 1);
  updateSpecifications();
};

const updateSpecifications = () => {
  if (!localData.value.parameters) {
    localData.value.parameters = {};
  }
  localData.value.parameters.specifications = specifications.value;
  updateModelValue();
};

const updateModelValue = () => {
  if (isUpdating.value) return;
  emit('update:modelValue', { ...localData.value });
};

// 监听器
watch(localData, () => {
  updateModelValue();
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  if (isUpdating.value) return;
  
  isUpdating.value = true;
  localData.value = { ...newValue };
  
  // 初始化技术规格
  if (newValue.parameters?.specifications) {
    specifications.value = [...newValue.parameters.specifications];
  } else {
    specifications.value = [];
  }
  
  nextTick(() => {
    isUpdating.value = false;
  });
}, { deep: true });

// 初始化
if (props.modelValue.parameters?.specifications) {
  specifications.value = [...props.modelValue.parameters.specifications];
}
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
