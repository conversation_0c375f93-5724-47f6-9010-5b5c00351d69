<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-[900px] max-h-[90vh] w-[90vw] overflow-hidden flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle>
          {{ isEditing ? '编辑设备' : '设备详情' }}
          <span v-if="equipment" class="text-sm font-normal text-muted-foreground ml-2">
            {{ equipment.id }}
          </span>
        </DialogTitle>
      </DialogHeader>

      <!-- 标签页导航 -->
      <div class="border-b flex-shrink-0">
        <nav class="flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === tab.id
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            ]"
          >
            <component :is="tab.icon" class="w-4 h-4 inline mr-2" />
            {{ tab.label }}
          </button>
        </nav>
      </div>

      <!-- 标签页内容 -->
      <div class="flex-1 overflow-y-auto py-4 min-h-0 equipment-dialog-scroll">
        <!-- 基础信息 -->
        <div v-if="activeTab === 'basic'" class="space-y-4">
          <EquipmentBasicForm
            v-model="formData"
            :is-editing="isEditing"
            :errors="validationErrors.basic"
            @validate="validateBasicInfo"
          />
        </div>

        <!-- 产能参数 -->
        <div v-if="activeTab === 'parameters'" class="space-y-4">
          <EquipmentParametersForm
            v-model="formData.parameters"
            :equipment-type="getEquipmentType(formData.model)"
            :is-editing="isEditing"
            :errors="validationErrors.parameters"
            @validate="validateParameters"
          />
        </div>

        <!-- 维护管理 -->
        <div v-if="activeTab === 'maintenance'" class="space-y-4">
          <EquipmentMaintenancePanel
            :equipment="equipment"
            :is-editing="isEditing"
            @update-maintenance="updateMaintenanceInfo"
          />
        </div>

        <!-- 工作中心关联 -->
        <div v-if="activeTab === 'workcenters'" class="space-y-4">
          <EquipmentWorkCenterPanel
            :equipment="equipment"
            :is-editing="isEditing"
            @update-associations="updateWorkCenterAssociations"
          />
        </div>

        <!-- 运行监控 -->
        <div v-if="activeTab === 'monitoring'" class="space-y-4">
          <EquipmentMonitoringPanel
            :equipment="equipment"
            @status-change="handleStatusChange"
          />
        </div>

        <!-- 历史记录 -->
        <div v-if="activeTab === 'history'" class="space-y-4">
          <EquipmentHistoryPanel
            :equipment="equipment"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between pt-4 border-t flex-shrink-0">
        <div class="flex gap-2">
          <Button
            v-if="!isEditing && equipment"
            variant="outline"
            @click="toggleEdit"
          >
            <Edit class="w-4 h-4 mr-2" />
            编辑
          </Button>
          <Button
            v-if="equipment"
            variant="outline"
            @click="duplicateEquipment"
          >
            <Copy class="w-4 h-4 mr-2" />
            复制
          </Button>
        </div>

        <div class="flex gap-2">
          <Button variant="outline" @click="closeDialog">
            取消
          </Button>
          <Button
            v-if="isEditing"
            @click="saveChanges"
            :disabled="loading || !isFormValid"
          >
            <Save class="w-4 h-4 mr-2" />
            保存
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Edit, Copy, Save, Info, Settings, Wrench, Factory, Activity, History } from 'lucide-vue-next';
import type { Equipment } from '@/types/masterdata';
import { toast } from 'vue-sonner';

// 子组件导入（稍后创建）
import EquipmentBasicForm from './EquipmentBasicForm.vue';
import EquipmentParametersForm from './EquipmentParametersForm.vue';
import EquipmentMaintenancePanel from './EquipmentMaintenancePanel.vue';
import EquipmentWorkCenterPanel from './EquipmentWorkCenterPanel.vue';
import EquipmentMonitoringPanel from './EquipmentMonitoringPanel.vue';
import EquipmentHistoryPanel from './EquipmentHistoryPanel.vue';

interface Props {
  equipment?: Equipment | null;
  open: boolean;
  mode?: 'view' | 'edit' | 'create';
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'save', equipment: Equipment): void;
  (e: 'duplicate', equipment: Equipment): void;
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'view'
});

const emit = defineEmits<Emits>();

// 响应式数据
const activeTab = ref('basic');
const isEditing = ref(false);
const loading = ref(false);
const formData = ref<Partial<Equipment>>({});
const validationErrors = ref({
  basic: {},
  parameters: {},
  maintenance: {},
  workcenters: {}
});

// 标签页配置
const tabs = [
  { id: 'basic', label: '基础信息', icon: Info },
  { id: 'parameters', label: '产能参数', icon: Settings },
  { id: 'maintenance', label: '维护管理', icon: Wrench },
  { id: 'workcenters', label: '工作中心', icon: Factory },
  { id: 'monitoring', label: '运行监控', icon: Activity },
  { id: 'history', label: '历史记录', icon: History }
];

// 计算属性
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
});

const isFormValid = computed(() => {
  return Object.values(validationErrors.value).every(errors => 
    Object.keys(errors).length === 0
  );
});

// 方法
const initializeForm = () => {
  if (props.equipment) {
    formData.value = { ...props.equipment };
  } else {
    formData.value = {
      name: '',
      model: '',
      status: 'idle',
      location: '',
      assetNumber: '',
      parameters: {},
      description: ''
    };
  }
  
  // 重置验证错误
  validationErrors.value = {
    basic: {},
    parameters: {},
    maintenance: {},
    workcenters: {}
  };
};

const toggleEdit = () => {
  isEditing.value = !isEditing.value;
  if (isEditing.value) {
    initializeForm();
  }
};

const closeDialog = () => {
  isEditing.value = false;
  activeTab.value = 'basic';
  emit('update:open', false);
};

const saveChanges = async () => {
  if (!isFormValid.value) {
    toast.error('请修正表单错误后再保存');
    return;
  }

  loading.value = true;
  try {
    emit('save', formData.value as Equipment);
    isEditing.value = false;
    toast.success('设备信息保存成功');
  } catch (error) {
    toast.error('保存失败，请重试');
  } finally {
    loading.value = false;
  }
};

const duplicateEquipment = () => {
  if (props.equipment) {
    const duplicated = {
      ...props.equipment,
      id: '', // 清空ID，让系统生成新的
      name: `${props.equipment.name} - 副本`,
      assetNumber: '', // 清空资产编号
    };
    emit('duplicate', duplicated);
  }
};

const getEquipmentType = (model: string): string => {
  if (model.includes('CUT')) return 'cutting';
  if (model.includes('EDGE')) return 'edging';
  if (model.includes('TEMP')) return 'tempering';
  if (model.includes('WASH')) return 'washing';
  if (model.includes('ASSEM')) return 'assembly';
  if (model.includes('DRILL')) return 'drilling';
  return 'other';
};

// 验证方法
const validateBasicInfo = (errors: any) => {
  validationErrors.value.basic = errors;
};

const validateParameters = (errors: any) => {
  validationErrors.value.parameters = errors;
};

// 事件处理
const updateMaintenanceInfo = (maintenanceData: any) => {
  formData.value = { ...formData.value, ...maintenanceData };
};

const updateWorkCenterAssociations = (associations: any) => {
  // 处理工作中心关联更新
  console.log('工作中心关联更新:', associations);
};

const handleStatusChange = (newStatus: Equipment['status']) => {
  if (formData.value) {
    formData.value.status = newStatus;
  }
};

// 监听器
watch(() => props.open, (newValue) => {
  if (newValue) {
    initializeForm();
    isEditing.value = props.mode === 'edit' || props.mode === 'create';
  }
});

watch(() => props.mode, (newMode) => {
  isEditing.value = newMode === 'edit' || newMode === 'create';
});

// 生命周期
onMounted(() => {
  if (props.open) {
    initializeForm();
  }
});
</script>
