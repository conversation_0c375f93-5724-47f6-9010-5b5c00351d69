<template>
  <div class="space-y-6">
    <!-- 生产工单选择 -->
    <Card class="p-6">
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <Label class="text-base font-medium">选择生产工单进行排产分析</Label>
          <div class="flex gap-2">
            <Button @click="loadProductionOrders" variant="outline" size="sm">
              <RefreshCw class="w-4 h-4 mr-2" />
              刷新工单
            </Button>
            <Button @click="generateMockWorkOrder" variant="outline" size="sm">
              <Plus class="w-4 h-4 mr-2" />
              生成测试工单
            </Button>
          </div>
        </div>

        <!-- 工单选择 -->
        <div class="space-y-3">
          <Label>可用生产工单</Label>
          <div v-if="availableWorkOrders.length === 0" class="text-center py-8 text-muted-foreground border rounded-lg">
            <FileText class="w-8 h-8 mx-auto mb-2" />
            暂无可用的生产工单
          </div>
          <div v-else class="space-y-2 max-h-64 overflow-y-auto">
            <div
              v-for="workOrder in availableWorkOrders"
              :key="workOrder.id"
              :class="[
                'p-4 border rounded-lg cursor-pointer transition-all',
                selectedWorkOrder?.id === workOrder.id
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-200 hover:border-gray-300'
              ]"
              @click="selectWorkOrder(workOrder)"
            >
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-medium">{{ workOrder.workOrderNumber }}</div>
                  <div class="text-sm text-muted-foreground">{{ workOrder.customerName }}</div>
                </div>
                <div class="text-right">
                  <Badge :variant="getStatusBadgeVariant(workOrder.status)">
                    {{ getStatusLabel(workOrder.status) }}
                  </Badge>
                  <div class="text-xs text-muted-foreground mt-1">
                    {{ workOrder.items.length }} 个工单项
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>

    <!-- 选中工单的详细信息 -->
    <div v-if="selectedWorkOrder" class="space-y-4">
      <Card class="p-6">
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <Label class="text-base font-medium">工单详情：{{ selectedWorkOrder.workOrderNumber }}</Label>
            <div class="flex gap-2">
              <Button @click="analyzeWorkOrderScheduling" :disabled="loading">
                <Zap class="w-4 h-4 mr-2" />
                智能排产分析
              </Button>
              <Button @click="testBatchOptimization" variant="outline" size="sm" :disabled="loading">
                测试批次优化
              </Button>
            </div>
          </div>

          <!-- 工单基本信息 -->
          <div class="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <div class="text-sm text-muted-foreground">客户名称</div>
              <div class="font-medium">{{ selectedWorkOrder.customerName }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">优先级</div>
              <Badge :variant="getPriorityBadgeVariant(selectedWorkOrder.priority)">
                {{ getPriorityLabel(selectedWorkOrder.priority) }}
              </Badge>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">计划开始</div>
              <div class="font-medium">{{ formatDate(selectedWorkOrder.plannedStartDate) }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">计划结束</div>
              <div class="font-medium">{{ formatDate(selectedWorkOrder.plannedEndDate) }}</div>
            </div>
          </div>

          <!-- 工单项列表 -->
          <div class="space-y-3">
            <Label>工单项明细 ({{ selectedWorkOrder.items.length }} 项)</Label>
            <div class="space-y-2 max-h-64 overflow-y-auto list-scroll-area">
              <div
                v-for="(item, index) in selectedWorkOrder.items"
                :key="item.id"
                class="p-3 border rounded-lg"
              >
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <div class="w-6 h-6 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-xs font-bold">
                      {{ index + 1 }}
                    </div>
                    <div class="font-medium">工单项 {{ item.id }}</div>
                  </div>
                  <div class="text-sm font-medium">{{ item.quantity }} 片</div>
                </div>

                <div class="grid grid-cols-4 gap-4 text-sm">
                  <div>
                    <span class="text-muted-foreground">规格:</span>
                    <span class="ml-1 font-medium">
                      {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                    </span>
                  </div>
                  <div>
                    <span class="text-muted-foreground">玻璃类型:</span>
                    <span class="ml-1 font-medium">{{ getGlassTypeLabel(item.specifications.glassType) }}</span>
                  </div>
                  <div>
                    <span class="text-muted-foreground">面积:</span>
                    <span class="ml-1 font-medium">{{ calculateArea(item.specifications) }} m²</span>
                  </div>
                  <div>
                    <span class="text-muted-foreground">工序数:</span>
                    <span class="ml-1 font-medium">{{ item.processFlow.length }} 道</span>
                  </div>
                </div>

                <!-- 工艺流程 -->
                <div class="mt-2 flex flex-wrap gap-1">
                  <Badge
                    v-for="step in item.processFlow"
                    :key="step.stepName"
                    variant="outline"
                    class="text-xs"
                  >
                    {{ step.stepName }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>


    <!-- 批次分析和设备分配结果 -->
    <div v-if="batchAnalysisResult" class="space-y-6">
      <!-- 批次优化结果 -->
      <Card class="p-6">
        <div class="space-y-4">
          <Label class="text-base font-medium">批次优化分析</Label>

          <!-- 优化统计 -->
          <div class="grid grid-cols-4 gap-4">
            <div class="text-center p-4 bg-green-50 rounded-lg">
              <div class="text-2xl font-bold text-green-600">{{ batchAnalysisResult.optimizedBatches.length }}</div>
              <div class="text-sm text-green-700">优化批次</div>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
              <div class="text-2xl font-bold text-blue-600">{{ batchAnalysisResult.efficiencyImprovement }}%</div>
              <div class="text-sm text-blue-700">效率提升</div>
            </div>
            <div class="text-center p-4 bg-orange-50 rounded-lg">
              <div class="text-2xl font-bold text-orange-600">{{ Math.round(batchAnalysisResult.timeSaved / 60) }}h</div>
              <div class="text-sm text-orange-700">节省工时</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
              <div class="text-2xl font-bold text-purple-600">{{ batchAnalysisResult.totalQuantity }}</div>
              <div class="text-sm text-purple-700">总数量(片)</div>
            </div>
          </div>

          <!-- 批次详情 -->
          <div class="space-y-3">
            <Label>批次分组详情</Label>
            <div class="space-y-3 max-h-64 overflow-y-auto list-scroll-area">
              <div
                v-for="(batch, index) in batchAnalysisResult.optimizedBatches"
                :key="batch.id"
                class="border rounded-lg p-4"
              >
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-bold">
                      {{ index + 1 }}
                    </div>
                    <div>
                      <div class="font-medium">{{ batch.name }}</div>
                      <div class="text-sm text-muted-foreground">{{ batch.workstationGroup }}</div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2">
                    <Badge :variant="getPriorityBadgeVariant(batch.priority)">
                      {{ getPriorityLabel(batch.priority) }}
                    </Badge>
                    <Badge variant="outline">{{ batch.totalQuantity }} 片</Badge>
                  </div>
                </div>

                <div class="grid grid-cols-3 gap-4 text-sm mb-3">
                  <div>
                    <span class="text-muted-foreground">预计时间:</span>
                    <span class="ml-1 font-medium">{{ batch.estimatedDuration || batch.estimatedTime || 0 }} 分钟</span>
                  </div>
                  <div>
                    <span class="text-muted-foreground">设备利用率:</span>
                    <span class="ml-1 font-medium">{{ Math.round(batch.utilization || 0) }}%</span>
                  </div>
                  <div>
                    <span class="text-muted-foreground">工单项数:</span>
                    <span class="ml-1 font-medium">{{ batch.items?.length || 0 }} 项</span>
                  </div>
                </div>

                <!-- 批次中的工单项 -->
                <div class="space-y-2">
                  <div class="text-xs text-muted-foreground">包含工单项:</div>
                  <div class="flex flex-wrap gap-1">
                    <Badge
                      v-for="item in (batch.items || [])"
                      :key="item.id"
                      variant="secondary"
                      class="text-xs"
                    >
                      {{ item.specifications?.length || 0 }}×{{ item.specifications?.width || 0 }}×{{ item.specifications?.thickness || 0 }}mm ({{ item.selectedQuantity || item.quantity || 0 }}片)
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <!-- 设备分配分析 -->
      <div class="space-y-4">
        <Label class="text-base font-medium">设备分配分析结果</Label>

        <div class="grid gap-4">
          <div
            v-for="(batchEquipment, batchIndex) in batchAnalysisResult.equipmentAllocations"
            :key="batchEquipment.batchId"
            class="space-y-4"
          >
            <Card class="p-4">
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-bold">
                      {{ batchIndex + 1 }}
                    </div>
                    <div>
                      <div class="font-medium">{{ batchEquipment.batchName }}</div>
                      <div class="text-sm text-muted-foreground">{{ batchEquipment.processType }}</div>
                    </div>
                  </div>
                  <Badge variant="outline">{{ batchEquipment.totalQuantity }} 片</Badge>
                </div>

                <!-- 设备分配结果 -->
                <div class="space-y-3">
                  <div
                    v-for="(allocation, index) in batchEquipment.equipmentMatches"
                    :key="allocation.equipmentId"
                    :class="[
                      'p-3 border rounded-lg',
                      index === 0 ? 'border-green-200 bg-green-50' : 'border-gray-200'
                    ]"
                  >
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center gap-3">
                        <div class="flex items-center gap-2">
                          <div v-if="index === 0" class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                            推荐
                          </div>
                          <div v-else class="w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold">
                            {{ index + 1 }}
                          </div>
                        </div>
                        <div>
                          <div class="font-medium">{{ allocation.equipmentName }}</div>
                          <div class="text-sm text-muted-foreground">{{ allocation.equipmentId }}</div>
                        </div>
                      </div>

                      <div class="flex items-center gap-4">
                        <div class="text-right">
                          <div class="text-lg font-bold" :class="getScoreColor(allocation.overallScore)">
                            {{ allocation.overallScore }}
                          </div>
                          <div class="text-xs text-muted-foreground">综合评分</div>
                        </div>
                        <Badge :variant="index === 0 ? 'default' : 'secondary'">
                          {{ index === 0 ? '最佳选择' : '备选方案' }}
                        </Badge>
                      </div>
                    </div>

                    <!-- 详细评分 -->
                    <div class="grid grid-cols-4 gap-4 mb-3">
                      <div class="text-center">
                        <div class="text-sm font-semibold" :class="getScoreColor(allocation.capabilityScore)">
                          {{ allocation.capabilityScore }}
                        </div>
                        <div class="text-xs text-muted-foreground">能力匹配</div>
                      </div>
                      <div class="text-center">
                        <div class="text-sm font-semibold" :class="getScoreColor(allocation.efficiencyScore)">
                          {{ allocation.efficiencyScore }}
                        </div>
                        <div class="text-xs text-muted-foreground">效率评分</div>
                      </div>
                      <div class="text-center">
                        <div class="text-sm font-semibold" :class="getScoreColor(allocation.costScore)">
                          {{ allocation.costScore }}
                        </div>
                        <div class="text-xs text-muted-foreground">成本评分</div>
                      </div>
                      <div class="text-center">
                        <div class="text-sm font-semibold" :class="getScoreColor(allocation.availabilityScore)">
                          {{ allocation.availabilityScore }}
                        </div>
                        <div class="text-xs text-muted-foreground">可用性</div>
                      </div>
                    </div>

                    <!-- 约束检查结果 -->
                    <div class="space-y-2 mb-3">
                      <div class="text-sm font-medium">约束检查结果</div>
                      <div class="grid grid-cols-2 gap-2">
                        <div
                          v-for="check in allocation.constraintChecks"
                          :key="check.constraintId"
                          :class="[
                            'flex items-center gap-2 p-2 rounded text-xs',
                            check.passed ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                          ]"
                        >
                          <component :is="check.passed ? CheckCircle : XCircle" class="w-3 h-3" />
                          <span>{{ check.message }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- 处理预测 -->
                    <div class="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span class="text-muted-foreground">预计加工时间:</span>
                        <span class="ml-1 font-medium">{{ allocation.estimatedProcessingTime }} 分钟</span>
                      </div>
                      <div>
                        <span class="text-muted-foreground">换线时间:</span>
                        <span class="ml-1 font-medium">{{ allocation.estimatedSetupTime }} 分钟</span>
                      </div>
                      <div>
                        <span class="text-muted-foreground">预计成本:</span>
                        <span class="ml-1 font-medium">¥{{ allocation.estimatedCost }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>

    <!-- 排产建议 -->
    <div v-if="schedulingDecision" class="space-y-4">
      <Label class="text-base font-medium">智能排产建议</Label>
      
      <div class="grid grid-cols-2 gap-4">
        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">排产建议</div>
            <div class="space-y-2 text-sm">
              <div>
                <span class="text-muted-foreground">建议开始时间:</span>
                <span class="ml-1 font-medium">{{ formatTime(schedulingDecision.schedulingSuggestion.preferredStartTime) }}</span>
              </div>
              <div>
                <span class="text-muted-foreground">预计完成时间:</span>
                <span class="ml-1 font-medium">{{ formatTime(schedulingDecision.schedulingSuggestion.estimatedCompletionTime) }}</span>
              </div>
              <div>
                <span class="text-muted-foreground">批量处理机会:</span>
                <span class="ml-1 font-medium">{{ schedulingDecision.schedulingSuggestion.batchingOpportunities.join(', ') }}</span>
              </div>
            </div>
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">优化建议</div>
            <div class="space-y-1 text-sm">
              <div
                v-for="advice in getAllOptimizationAdvice(schedulingDecision.optimizationAdvice)"
                :key="advice"
                class="flex items-start gap-2"
              >
                <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>{{ advice }}</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, Plus, FileText, Zap, CheckCircle, XCircle } from 'lucide-vue-next';
import { EquipmentCapabilityService } from '@/services/equipmentCapabilityService';
import { batchOptimizationService } from '@/services/batchOptimizationService';
import { mesService } from '@/services/mesService';
import type { OrderItemRequirement, EquipmentMatchResult, SchedulingDecisionData } from '@/types/equipmentCapability';
import type { SelectedOrderItem, OptimizedBatch } from '@/types/production-order-creation';

// 生产工单接口定义
interface ProductionOrder {
  id: string;
  workOrderNumber: string;
  customerOrderId: string;
  customerOrderNumber: string;
  customerName: string;
  items: ProductionOrderItem[];
  priority: string;
  status: string;
  plannedStartDate: string;
  plannedEndDate: string;
  createdAt: string;
  updatedAt: string;
}

interface ProductionOrderItem {
  id: string;
  productionOrderId: string;
  customerOrderItemId: string;
  specifications: {
    length: number;
    width: number;
    thickness: number;
    glassType: string;
    color: string;
  };
  quantity: number;
  processFlow: Array<{
    stepName: string;
    workstation: string;
    estimatedDuration: number;
    constraints: any;
    status: string;
    workstationGroup: string;
  }>;
  currentStatus: string;
  currentWorkstation: string;
}

// 批次分析结果接口
interface BatchAnalysisResult {
  optimizedBatches: OptimizedBatch[];
  efficiencyImprovement: number;
  timeSaved: number;
  totalQuantity: number;
  equipmentAllocations: BatchEquipmentAllocation[];
}

interface BatchEquipmentAllocation {
  batchId: string;
  batchName: string;
  processType: string;
  totalQuantity: number;
  equipmentMatches: EquipmentMatchResult[];
}

// 响应式数据
const loading = ref(false);
const availableWorkOrders = ref<ProductionOrder[]>([]);
const selectedWorkOrder = ref<ProductionOrder | null>(null);
const batchAnalysisResult = ref<BatchAnalysisResult | null>(null);

// 方法
const loadProductionOrders = async () => {
  try {
    const orders = await mesService.getProductionOrders();
    // 过滤出待排产或进行中的工单
    availableWorkOrders.value = orders.filter(order =>
      ['pending', 'in_progress'].includes(order.status)
    );
  } catch (error) {
    console.error('加载生产工单失败:', error);
  }
};

const generateMockWorkOrder = () => {
  const mockOrder: ProductionOrder = {
    id: `WO-${Date.now()}`,
    workOrderNumber: `WO-2025-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
    customerOrderId: `CO-${Date.now()}`,
    customerOrderNumber: `CO-2025-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
    customerName: ['华润置地', '万科集团', '碧桂园', '恒大集团', '中海地产'][Math.floor(Math.random() * 5)],
    priority: ['low', 'medium', 'high', 'urgent'][Math.floor(Math.random() * 4)],
    status: 'pending',
    plannedStartDate: new Date().toISOString(),
    plannedEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    items: generateMockOrderItems()
  };

  availableWorkOrders.value.unshift(mockOrder);
};

const generateMockOrderItems = (): ProductionOrderItem[] => {
  const specs = [
    { length: 1800, width: 1200, thickness: 6, glassType: 'float_glass', color: 'clear' },
    { length: 2400, width: 1800, thickness: 8, glassType: 'tempered_glass', color: 'clear' },
    { length: 2200, width: 3200, thickness: 10, glassType: 'laminated_glass', color: 'clear' },
    { length: 1500, width: 1000, thickness: 5, glassType: 'float_glass', color: 'tinted' },
    { length: 2400, width: 3600, thickness: 12, glassType: 'tempered_glass', color: 'clear' }
  ];

  const items: ProductionOrderItem[] = [];
  const itemCount = Math.floor(Math.random() * 5) + 2; // 2-6个工单项

  for (let i = 0; i < itemCount; i++) {
    const spec = specs[Math.floor(Math.random() * specs.length)];
    items.push({
      id: `WOI-${Date.now()}-${i}`,
      productionOrderId: '',
      customerOrderItemId: `COI-${Date.now()}-${i}`,
      specifications: spec,
      quantity: Math.floor(Math.random() * 80) + 20, // 20-100片
      processFlow: [
        {
          stepName: '切割',
          workstation: 'cutting',
          estimatedDuration: 30,
          constraints: {},
          status: 'pending',
          workstationGroup: 'cutting'
        },
        {
          stepName: '磨边',
          workstation: 'edging',
          estimatedDuration: 45,
          constraints: {},
          status: 'pending',
          workstationGroup: 'edging'
        },
        {
          stepName: '钢化',
          workstation: 'tempering',
          estimatedDuration: 60,
          constraints: {},
          status: 'pending',
          workstationGroup: 'tempering'
        }
      ],
      currentStatus: 'pending',
      currentWorkstation: 'cutting'
    });
  }

  return items;
};

const selectWorkOrder = (workOrder: ProductionOrder) => {
  selectedWorkOrder.value = workOrder;
  batchAnalysisResult.value = null; // 清除之前的分析结果
};

const testBatchOptimization = async () => {
  if (!selectedWorkOrder.value) return;

  loading.value = true;

  try {
    console.log('测试批次优化服务...');

    const selectedItems: SelectedOrderItem[] = selectedWorkOrder.value.items.map(item => ({
      id: item.id,
      customerOrderItemId: item.customerOrderItemId,
      customerName: selectedWorkOrder.value!.customerName,
      specifications: item.specifications,
      quantity: item.quantity,
      selectedQuantity: item.quantity,
      processFlow: item.processFlow,
      currentStatus: item.currentStatus,
      currentWorkstation: item.currentWorkstation
    }));

    console.log('输入数据:', selectedItems);

    const result = await batchOptimizationService.optimizeBatches(selectedItems);

    console.log('批次优化服务返回结果:', result);
    console.log('结果类型:', typeof result);
    console.log('结果键:', Object.keys(result));

    if (result.batches) {
      console.log('批次数组:', result.batches);
      console.log('批次数组长度:', result.batches.length);
      result.batches.forEach((batch, index) => {
        console.log(`批次 ${index}:`, batch);
        console.log(`批次 ${index} 键:`, Object.keys(batch));
      });
    }

  } catch (error) {
    console.error('批次优化测试失败:', error);
  } finally {
    loading.value = false;
  }
};

const analyzeWorkOrderScheduling = async () => {
  if (!selectedWorkOrder.value) return;

  loading.value = true;

  try {
    console.log('开始分析工单:', selectedWorkOrder.value.workOrderNumber);

    // 1. 首先进行批次优化
    const selectedItems: SelectedOrderItem[] = selectedWorkOrder.value.items.map(item => ({
      id: item.id,
      customerOrderItemId: item.customerOrderItemId,
      customerName: selectedWorkOrder.value!.customerName, // 添加客户名称
      specifications: item.specifications,
      quantity: item.quantity,
      selectedQuantity: item.quantity,
      processFlow: item.processFlow,
      currentStatus: item.currentStatus,
      currentWorkstation: item.currentWorkstation
    }));

    console.log('准备进行批次优化的工单项:', selectedItems);

    const batchOptimizationResult = await batchOptimizationService.optimizeBatches(selectedItems);

    console.log('批次优化结果:', batchOptimizationResult); // 调试日志

    // 2. 为每个批次进行设备分配分析
    const equipmentAllocations: BatchEquipmentAllocation[] = [];

    // 检查批次数据是否存在
    if (!batchOptimizationResult.batches || !Array.isArray(batchOptimizationResult.batches)) {
      console.error('批次优化结果中没有有效的批次数据:', batchOptimizationResult);

      // 创建一个简化的分析结果，直接分析工单项
      const equipmentAllocations: BatchEquipmentAllocation[] = [];

      // 为每个工单项创建一个简单的设备分配分析
      for (let i = 0; i < selectedItems.length; i++) {
        const item = selectedItems[i];

        // 构建订单项要求
        const requirement = {
          orderItemId: item.id,
          dimensions: {
            length: item.specifications.length,
            width: item.specifications.width,
            thickness: item.specifications.thickness,
            area: (item.specifications.length * item.specifications.width) / 1000000,
            perimeter: 2 * (item.specifications.length + item.specifications.width)
          },
          materialType: 'float_glass',
          glassType: item.specifications.glassType,
          edgeType: 'straight',
          precisionLevel: 'standard' as any,
          surfaceQuality: 'A' as any,
          quantity: item.quantity,
          requiredDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          priority: selectedWorkOrder.value.priority as any
        };

        // 获取设备匹配结果
        const matches = await EquipmentCapabilityService.matchEquipmentForOrderItem(
          requirement,
          'WC-EDGE-001'
        );

        equipmentAllocations.push({
          batchId: `ITEM-${i + 1}`,
          batchName: `工单项 ${i + 1}`,
          processType: '磨边加工',
          totalQuantity: item.quantity,
          equipmentMatches: matches
        });
      }

      // 创建简化的分析结果
      batchAnalysisResult.value = {
        optimizedBatches: selectedItems.map((item, index) => ({
          id: `ITEM-${index + 1}`,
          name: `工单项 ${index + 1}`,
          workstationGroup: 'edging',
          priority: selectedWorkOrder.value!.priority,
          totalQuantity: item.quantity,
          estimatedDuration: 60,
          utilization: 85,
          items: [item]
        })),
        efficiencyImprovement: 0,
        timeSaved: 0,
        totalQuantity: selectedItems.reduce((sum, item) => sum + item.quantity, 0),
        equipmentAllocations
      };

      return;
    }

    for (const batch of batchOptimizationResult.batches) {
      console.log('处理批次:', batch); // 调试日志

      // 找到磨边工序的批次
      if (batch.workstationGroup === 'edging') {
        const batchEquipmentMatches: EquipmentMatchResult[] = [];

        // 分析批次中的所有工单项，找到代表性规格
        const representativeItem = findRepresentativeItem(batch.items);
        console.log('代表性工单项:', representativeItem); // 调试日志

        if (representativeItem) {
          // 构建订单项要求
          const requirement: OrderItemRequirement = {
            orderItemId: representativeItem.id,
            dimensions: {
              length: representativeItem.specifications.length,
              width: representativeItem.specifications.width,
              thickness: representativeItem.specifications.thickness,
              area: (representativeItem.specifications.length * representativeItem.specifications.width) / 1000000,
              perimeter: 2 * (representativeItem.specifications.length + representativeItem.specifications.width)
            },
            materialType: 'float_glass',
            glassType: representativeItem.specifications.glassType,
            edgeType: 'straight', // 默认直边
            precisionLevel: 'standard',
            surfaceQuality: 'A',
            quantity: batch.totalQuantity,
            requiredDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            priority: selectedWorkOrder.value.priority as any
          };

          // 获取设备匹配结果
          const matches = await EquipmentCapabilityService.matchEquipmentForOrderItem(
            requirement,
            'WC-EDGE-001' // 磨边工作中心ID
          );

          batchEquipmentMatches.push(...matches);
        }

        equipmentAllocations.push({
          batchId: batch.id,
          batchName: batch.name,
          processType: '磨边加工',
          totalQuantity: batch.totalQuantity,
          equipmentMatches: batchEquipmentMatches
        });
      }
    }

    // 3. 构建完整的分析结果
    batchAnalysisResult.value = {
      optimizedBatches: batchOptimizationResult.batches, // 使用正确的字段名
      efficiencyImprovement: batchOptimizationResult.efficiency || 0, // 使用正确的字段名
      timeSaved: batchOptimizationResult.timeSaved || 0, // timeSaved已经是分钟
      totalQuantity: batchOptimizationResult.totalQuantity || 0,
      equipmentAllocations
    };

  } catch (error) {
    console.error('工单排产分析失败:', error);
  } finally {
    loading.value = false;
  }
};

// 找到批次中的代表性工单项（用于设备匹配分析）
const findRepresentativeItem = (items: any[]): any | null => {
  if (!items || items.length === 0) return null;

  // 选择数量最大的工单项作为代表性项目
  return items.reduce((prev, current) => {
    const prevQuantity = prev.selectedQuantity || prev.quantity || 0;
    const currentQuantity = current.selectedQuantity || current.quantity || 0;
    return currentQuantity > prevQuantity ? current : prev;
  });
};

// 辅助方法
const getStatusBadgeVariant = (status: string) => {
  const variantMap: Record<string, string> = {
    pending: 'secondary',
    in_progress: 'default',
    completed: 'outline',
    cancelled: 'destructive'
  };
  return variantMap[status] || 'secondary';
};

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  };
  return labelMap[status] || status;
};

const getPriorityBadgeVariant = (priority: string) => {
  const variantMap: Record<string, string> = {
    low: 'secondary',
    medium: 'default',
    high: 'destructive',
    urgent: 'destructive'
  };
  return variantMap[priority] || 'default';
};

const getPriorityLabel = (priority: string) => {
  const labelMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  };
  return labelMap[priority] || priority;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getGlassTypeLabel = (glassType: string) => {
  const labelMap: Record<string, string> = {
    float_glass: '浮法玻璃',
    tempered_glass: '钢化玻璃',
    laminated_glass: '夹胶玻璃',
    insulated_glass: '中空玻璃'
  };
  return labelMap[glassType] || glassType;
};

const calculateArea = (specifications: any): string => {
  const area = (specifications.length * specifications.width) / 1000000;
  return area.toFixed(2);
};

const getScoreColor = (score: number): string => {
  if (score >= 90) return 'text-green-600';
  if (score >= 70) return 'text-blue-600';
  if (score >= 50) return 'text-orange-600';
  return 'text-red-600';
};

// 生命周期
onMounted(() => {
  loadProductionOrders();
});
</script>
