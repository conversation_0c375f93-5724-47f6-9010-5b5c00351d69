<template>
  <div class="space-y-6">
    <!-- 历史记录筛选 -->
    <div class="flex items-center gap-4">
      <div class="flex-1">
        <Select v-model="selectedRecordType">
          <SelectTrigger>
            <SelectValue placeholder="选择记录类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部记录</SelectItem>
            <SelectItem value="status">状态变更</SelectItem>
            <SelectItem value="maintenance">维护记录</SelectItem>
            <SelectItem value="fault">故障记录</SelectItem>
            <SelectItem value="production">生产记录</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div class="flex gap-2">
        <Input
          v-model="dateRange.start"
          type="date"
          class="w-40"
        />
        <span class="self-center text-muted-foreground">至</span>
        <Input
          v-model="dateRange.end"
          type="date"
          class="w-40"
        />
      </div>

      <Button variant="outline" @click="exportHistory">
        <Download class="w-4 h-4 mr-2" />
        导出
      </Button>
    </div>

    <!-- 历史记录统计 -->
    <div class="grid grid-cols-4 gap-4">
      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-blue-100 rounded-lg">
            <Activity class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">总运行时间</div>
            <div class="font-semibold">{{ totalRunningTime }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-green-100 rounded-lg">
            <CheckCircle class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">正常运行率</div>
            <div class="font-semibold">{{ uptime }}%</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-red-100 rounded-lg">
            <AlertTriangle class="w-5 h-5 text-red-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">故障次数</div>
            <div class="font-semibold">{{ faultCount }}</div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-orange-100 rounded-lg">
            <Wrench class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <div class="text-sm text-muted-foreground">维护次数</div>
            <div class="font-semibold">{{ maintenanceCount }}</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 历史记录时间线 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">历史记录时间线</Label>
      
      <div v-if="filteredHistory.length === 0" class="text-sm text-muted-foreground text-center py-8 border rounded-lg">
        <History class="w-8 h-8 mx-auto mb-2" />
        暂无历史记录
      </div>

      <div v-else class="space-y-3 max-h-80 overflow-y-auto pr-2 timeline-scroll">
        <div
          v-for="record in filteredHistory"
          :key="record.id"
          class="relative pl-8 pb-4"
        >
          <!-- 时间线连接线 -->
          <div
            v-if="record !== filteredHistory[filteredHistory.length - 1]"
            class="absolute left-3 top-8 w-0.5 h-full bg-gray-200"
          ></div>
          
          <!-- 时间线节点 -->
          <div
            :class="[
              'absolute left-0 top-2 w-6 h-6 rounded-full border-2 bg-white flex items-center justify-center',
              getRecordBorderColor(record.type)
            ]"
          >
            <component
              :is="getRecordIcon(record.type)"
              :class="['w-3 h-3', getRecordIconColor(record.type)]"
            />
          </div>

          <!-- 记录内容 -->
          <Card class="p-4">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                  <Badge :variant="getRecordBadgeVariant(record.type)">
                    {{ getRecordTypeLabel(record.type) }}
                  </Badge>
                  <span class="text-sm text-muted-foreground">{{ record.timestamp }}</span>
                </div>
                
                <div class="font-medium mb-1">{{ record.title }}</div>
                <div class="text-sm text-muted-foreground">{{ record.description }}</div>
                
                <div v-if="record.details" class="mt-3 space-y-2">
                  <div
                    v-for="(value, key) in record.details"
                    :key="key"
                    class="flex justify-between text-sm"
                  >
                    <span class="text-muted-foreground">{{ getDetailLabel(key) }}:</span>
                    <span class="font-medium">{{ value }}</span>
                  </div>
                </div>
              </div>

              <div class="flex items-center gap-2 ml-4">
                <Button
                  variant="ghost"
                  size="sm"
                  @click="viewRecordDetail(record)"
                >
                  <Eye class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>

    <!-- 性能趋势图表 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">性能趋势分析</Label>
      
      <div class="grid grid-cols-2 gap-4">
        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">运行效率趋势</div>
            <div class="h-32 flex items-center justify-center text-muted-foreground">
              <div class="text-center">
                <TrendingUp class="w-8 h-8 mx-auto mb-2" />
                <div class="text-sm">效率趋势图表</div>
              </div>
            </div>
          </div>
        </Card>

        <Card class="p-4">
          <div class="space-y-3">
            <div class="font-medium text-sm">故障频率分析</div>
            <div class="h-32 flex items-center justify-center text-muted-foreground">
              <div class="text-center">
                <BarChart3 class="w-8 h-8 mx-auto mb-2" />
                <div class="text-sm">故障分析图表</div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 维护建议 -->
    <div class="space-y-4">
      <Label class="text-base font-medium">维护建议</Label>
      
      <div v-if="maintenanceRecommendations.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无维护建议
      </div>

      <div v-else class="space-y-3">
        <Card
          v-for="recommendation in maintenanceRecommendations"
          :key="recommendation.id"
          class="p-4"
        >
          <div class="flex items-start gap-3">
            <div :class="['p-2 rounded-lg', getRecommendationColorClass(recommendation.priority)]">
              <component :is="getRecommendationIcon(recommendation.priority)" class="w-5 h-5" />
            </div>
            
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-2">
                <div class="font-medium">{{ recommendation.title }}</div>
                <Badge :variant="getRecommendationBadgeVariant(recommendation.priority)">
                  {{ getRecommendationPriorityLabel(recommendation.priority) }}
                </Badge>
              </div>
              
              <div class="text-sm text-muted-foreground mb-2">{{ recommendation.description }}</div>
              
              <div class="flex items-center gap-4 text-xs text-muted-foreground">
                <span>预计时间: {{ recommendation.estimatedTime }}</span>
                <span>预计费用: ¥{{ recommendation.estimatedCost }}</span>
                <span>建议日期: {{ recommendation.suggestedDate }}</span>
              </div>
            </div>

            <Button variant="outline" size="sm">
              采纳建议
            </Button>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Download, Activity, CheckCircle, AlertTriangle, Wrench, History, Eye,
  TrendingUp, BarChart3, Play, Pause, Square, Settings, Info, AlertCircle
} from 'lucide-vue-next';
import type { Equipment } from '@/types/masterdata';

interface HistoryRecord {
  id: string;
  type: 'status' | 'maintenance' | 'fault' | 'production';
  title: string;
  description: string;
  timestamp: string;
  details?: Record<string, any>;
}

interface MaintenanceRecommendation {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedTime: string;
  estimatedCost: number;
  suggestedDate: string;
}

interface Props {
  equipment?: Equipment | null;
}

const props = defineProps<Props>();

// 响应式数据
const selectedRecordType = ref('all');
const dateRange = ref({
  start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  end: new Date().toISOString().split('T')[0]
});

const historyRecords = ref<HistoryRecord[]>([]);
const maintenanceRecommendations = ref<MaintenanceRecommendation[]>([]);

// 统计数据
const totalRunningTime = ref('1,245小时');
const uptime = ref(96.5);
const faultCount = ref(3);
const maintenanceCount = ref(12);

// 计算属性
const filteredHistory = computed(() => {
  let filtered = historyRecords.value;
  
  if (selectedRecordType.value !== 'all') {
    filtered = filtered.filter(record => record.type === selectedRecordType.value);
  }
  
  // 按时间筛选
  filtered = filtered.filter(record => {
    const recordDate = new Date(record.timestamp).toISOString().split('T')[0];
    return recordDate >= dateRange.value.start && recordDate <= dateRange.value.end;
  });
  
  return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
});

// 方法
const getRecordIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    status: Play,
    maintenance: Wrench,
    fault: AlertTriangle,
    production: Settings
  };
  return iconMap[type] || Info;
};

const getRecordIconColor = (type: string) => {
  const colorMap: Record<string, string> = {
    status: 'text-blue-600',
    maintenance: 'text-orange-600',
    fault: 'text-red-600',
    production: 'text-green-600'
  };
  return colorMap[type] || 'text-gray-600';
};

const getRecordBorderColor = (type: string) => {
  const colorMap: Record<string, string> = {
    status: 'border-blue-200',
    maintenance: 'border-orange-200',
    fault: 'border-red-200',
    production: 'border-green-200'
  };
  return colorMap[type] || 'border-gray-200';
};

const getRecordBadgeVariant = (type: string) => {
  const variantMap: Record<string, string> = {
    status: 'default',
    maintenance: 'outline',
    fault: 'destructive',
    production: 'secondary'
  };
  return variantMap[type] || 'default';
};

const getRecordTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    status: '状态变更',
    maintenance: '维护记录',
    fault: '故障记录',
    production: '生产记录'
  };
  return labelMap[type] || type;
};

const getDetailLabel = (key: string) => {
  const labelMap: Record<string, string> = {
    duration: '持续时间',
    cost: '费用',
    technician: '操作人员',
    reason: '原因',
    solution: '解决方案',
    production: '产量',
    efficiency: '效率'
  };
  return labelMap[key] || key;
};

const getRecommendationIcon = (priority: string) => {
  const iconMap: Record<string, any> = {
    low: Info,
    medium: AlertCircle,
    high: AlertTriangle,
    critical: AlertTriangle
  };
  return iconMap[priority] || Info;
};

const getRecommendationColorClass = (priority: string) => {
  const classMap: Record<string, string> = {
    low: 'bg-blue-100 text-blue-600',
    medium: 'bg-yellow-100 text-yellow-600',
    high: 'bg-orange-100 text-orange-600',
    critical: 'bg-red-100 text-red-600'
  };
  return classMap[priority] || 'bg-gray-100 text-gray-600';
};

const getRecommendationBadgeVariant = (priority: string) => {
  const variantMap: Record<string, string> = {
    low: 'secondary',
    medium: 'outline',
    high: 'default',
    critical: 'destructive'
  };
  return variantMap[priority] || 'default';
};

const getRecommendationPriorityLabel = (priority: string) => {
  const labelMap: Record<string, string> = {
    low: '低优先级',
    medium: '中优先级',
    high: '高优先级',
    critical: '紧急'
  };
  return labelMap[priority] || priority;
};

const viewRecordDetail = (record: HistoryRecord) => {
  // 显示记录详情
  console.log('查看记录详情:', record);
};

const exportHistory = () => {
  // 导出历史记录
  console.log('导出历史记录');
};

const loadHistoryData = () => {
  // 模拟历史记录数据
  historyRecords.value = [
    {
      id: '1',
      type: 'status',
      title: '设备启动',
      description: '设备从空闲状态切换到运行状态',
      timestamp: '2025-01-18T08:00:00Z',
      details: {
        duration: '8小时',
        operator: '张师傅'
      }
    },
    {
      id: '2',
      type: 'maintenance',
      title: '例行维护',
      description: '进行设备的例行维护检查',
      timestamp: '2025-01-17T16:00:00Z',
      details: {
        duration: '2小时',
        cost: 500,
        technician: '李师傅'
      }
    },
    {
      id: '3',
      type: 'fault',
      title: '传感器故障',
      description: '温度传感器出现异常读数',
      timestamp: '2025-01-16T14:30:00Z',
      details: {
        duration: '1.5小时',
        cost: 800,
        solution: '更换温度传感器'
      }
    }
  ];

  // 模拟维护建议
  maintenanceRecommendations.value = [
    {
      id: '1',
      title: '更换润滑油',
      description: '设备运行时间已达到润滑油更换周期，建议及时更换以保证设备正常运行',
      priority: 'medium',
      estimatedTime: '1小时',
      estimatedCost: 200,
      suggestedDate: '2025-01-25'
    },
    {
      id: '2',
      title: '校准传感器',
      description: '温度传感器读数存在轻微偏差，建议进行校准',
      priority: 'low',
      estimatedTime: '0.5小时',
      estimatedCost: 100,
      suggestedDate: '2025-01-30'
    }
  ];
};

// 生命周期
onMounted(() => {
  loadHistoryData();
});
</script>
