<template>
  <div class="space-y-6">
    <div class="grid grid-cols-2 gap-4">
      <!-- 工作中心名称 -->
      <div class="space-y-2">
        <Label for="name" class="required">工作中心名称</Label>
        <Input
          id="name"
          v-model="localData.name"
          :disabled="!isEditing"
          placeholder="请输入工作中心名称"
          :class="{ 'border-destructive': errors.name }"
          @blur="validateField('name')"
        />
        <p v-if="errors.name" class="text-sm text-destructive">{{ errors.name }}</p>
      </div>

      <!-- 工作中心编码 -->
      <div class="space-y-2">
        <Label for="code">工作中心编码</Label>
        <Input
          id="code"
          v-model="localData.code"
          :disabled="!isEditing"
          placeholder="自动生成或手动输入"
          :class="{ 'border-destructive': errors.code }"
          @blur="validateField('code')"
        />
        <p v-if="errors.code" class="text-sm text-destructive">{{ errors.code }}</p>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <!-- 工作中心类型 -->
      <div class="space-y-2">
        <Label for="type">工作中心类型</Label>
        <Select v-model="localData.type" :disabled="!isEditing">
          <SelectTrigger>
            <SelectValue placeholder="选择工作中心类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="type in workCenterTypes" :key="type.value" :value="type.value">
              <div class="flex items-center gap-2">
                <component :is="type.icon" class="w-4 h-4" />
                {{ type.label }}
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 位置 -->
      <div class="space-y-2">
        <Label for="location">位置</Label>
        <Select v-model="localData.location" :disabled="!isEditing">
          <SelectTrigger>
            <SelectValue placeholder="选择位置" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="location in availableLocations" :key="location.value" :value="location.value">
              {{ location.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <div class="grid grid-cols-3 gap-4">
      <!-- 基础产能 -->
      <div class="space-y-2">
        <Label for="capacity">基础产能</Label>
        <div class="flex gap-2">
          <Input
            id="capacity"
            v-model.number="localData.capacity"
            :disabled="!isEditing"
            type="number"
            min="0"
            placeholder="100"
          />
          <Select v-model="localData.capacityUnit" :disabled="!isEditing">
            <SelectTrigger class="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pieces">件/小时</SelectItem>
              <SelectItem value="area">m²/小时</SelectItem>
              <SelectItem value="length">m/小时</SelectItem>
              <SelectItem value="weight">kg/小时</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <!-- 效率 -->
      <div class="space-y-2">
        <Label for="efficiency">标准效率 (%)</Label>
        <Input
          id="efficiency"
          v-model.number="localData.efficiency"
          :disabled="!isEditing"
          type="number"
          min="0"
          max="100"
          placeholder="95"
        />
      </div>

      <!-- 优先级 -->
      <div class="space-y-2">
        <Label for="priority">优先级</Label>
        <Select v-model="localData.priority" :disabled="!isEditing">
          <SelectTrigger>
            <SelectValue placeholder="选择优先级" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="high">高</SelectItem>
            <SelectItem value="medium">中</SelectItem>
            <SelectItem value="low">低</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <!-- 成本中心 -->
      <div class="space-y-2">
        <Label for="costCenter">成本中心</Label>
        <Select v-model="localData.costCenter" :disabled="!isEditing">
          <SelectTrigger>
            <SelectValue placeholder="选择成本中心" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="center in costCenters" :key="center.value" :value="center.value">
              {{ center.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 负责人 -->
      <div class="space-y-2">
        <Label for="supervisor">负责人</Label>
        <Select v-model="localData.supervisor" :disabled="!isEditing">
          <SelectTrigger>
            <SelectValue placeholder="选择负责人" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="supervisor in supervisors" :key="supervisor.value" :value="supervisor.value">
              <div class="flex items-center gap-2">
                <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs">
                  {{ supervisor.label.charAt(0) }}
                </div>
                {{ supervisor.label }}
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <div class="space-y-2">
      <!-- 产能日历 -->
      <Label for="calendarId">产能日历</Label>
      <Select v-model="localData.calendarId" :disabled="!isEditing">
        <SelectTrigger>
          <SelectValue placeholder="选择产能日历" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem v-for="calendar in availableCalendars" :key="calendar.value" :value="calendar.value">
            <div class="flex items-center justify-between w-full">
              <span>{{ calendar.label }}</span>
              <Badge variant="outline" class="ml-2">{{ calendar.workingHours }}h</Badge>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>

    <!-- 工作中心描述 -->
    <div class="space-y-2">
      <Label for="description">工作中心描述</Label>
      <Textarea
        id="description"
        v-model="localData.description"
        :disabled="!isEditing"
        placeholder="请输入工作中心的详细描述，包括主要功能、工艺特点等"
        rows="3"
      />
    </div>

    <!-- 工艺能力 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">工艺能力</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addCapability"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加能力
        </Button>
      </div>

      <div v-if="capabilities.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无工艺能力信息
      </div>

      <div v-else class="space-y-2 max-h-48 overflow-y-auto list-scroll-area">
        <div
          v-for="(capability, index) in capabilities"
          :key="index"
          class="flex items-center gap-2 p-3 border rounded-lg"
        >
          <Select v-model="capability.type" :disabled="!isEditing" class="flex-1">
            <SelectTrigger>
              <SelectValue placeholder="工艺类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cutting">切割</SelectItem>
              <SelectItem value="edging">磨边</SelectItem>
              <SelectItem value="tempering">钢化</SelectItem>
              <SelectItem value="drilling">钻孔</SelectItem>
              <SelectItem value="washing">清洗</SelectItem>
              <SelectItem value="assembly">装配</SelectItem>
            </SelectContent>
          </Select>
          
          <Input
            v-model="capability.specification"
            :disabled="!isEditing"
            placeholder="规格要求"
            class="flex-1"
          />
          
          <Input
            v-model="capability.capacity"
            :disabled="!isEditing"
            placeholder="产能"
            class="w-24"
          />
          
          <Button
            v-if="isEditing"
            variant="ghost"
            size="sm"
            @click="removeCapability(index)"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 质量标准 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-base font-medium">质量标准</Label>
        <Button
          v-if="isEditing"
          variant="outline"
          size="sm"
          @click="addQualityStandard"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加标准
        </Button>
      </div>

      <div v-if="qualityStandards.length === 0" class="text-sm text-muted-foreground text-center py-4">
        暂无质量标准信息
      </div>

      <div v-else class="space-y-2 max-h-48 overflow-y-auto list-scroll-area">
        <div
          v-for="(standard, index) in qualityStandards"
          :key="index"
          class="flex items-center gap-2 p-3 border rounded-lg"
        >
          <Input
            v-model="standard.name"
            :disabled="!isEditing"
            placeholder="标准名称"
            class="flex-1"
          />
          
          <Input
            v-model="standard.requirement"
            :disabled="!isEditing"
            placeholder="要求"
            class="flex-1"
          />
          
          <Select v-model="standard.level" :disabled="!isEditing" class="w-24">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="A">A级</SelectItem>
              <SelectItem value="B">B级</SelectItem>
              <SelectItem value="C">C级</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            v-if="isEditing"
            variant="ghost"
            size="sm"
            @click="removeQualityStandard(index)"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, Factory, Settings, Cog, Wrench } from 'lucide-vue-next';
import type { WorkCenter } from '@/types/masterdata';

interface Props {
  modelValue: Partial<WorkCenter>;
  isEditing: boolean;
  errors: Record<string, string>;
}

interface Emits {
  (e: 'update:modelValue', value: Partial<WorkCenter>): void;
  (e: 'validate', errors: Record<string, string>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localData = ref<Partial<WorkCenter>>({ ...props.modelValue });
const isUpdating = ref(false);

// 工艺能力和质量标准
const capabilities = ref<Array<{ type: string; specification: string; capacity: string }>>([]);
const qualityStandards = ref<Array<{ name: string; requirement: string; level: string }>>([]);

// 选项配置
const workCenterTypes = [
  { value: 'production', label: '生产工作中心', icon: Factory },
  { value: 'assembly', label: '装配工作中心', icon: Settings },
  { value: 'quality', label: '质检工作中心', icon: Cog },
  { value: 'maintenance', label: '维护工作中心', icon: Wrench }
];

const availableLocations = [
  { value: '车间A区', label: '车间A区' },
  { value: '车间B区', label: '车间B区' },
  { value: '车间C区', label: '车间C区' },
  { value: '车间D区', label: '车间D区' }
];

const costCenters = [
  { value: 'CC-PROD-001', label: 'CC-PROD-001 - 生产成本中心1' },
  { value: 'CC-PROD-002', label: 'CC-PROD-002 - 生产成本中心2' },
  { value: 'CC-PROD-003', label: 'CC-PROD-003 - 生产成本中心3' },
  { value: 'CC-QC-001', label: 'CC-QC-001 - 质检成本中心' }
];

const supervisors = [
  { value: '张师傅', label: '张师傅' },
  { value: '李师傅', label: '李师傅' },
  { value: '王师傅', label: '王师傅' },
  { value: '赵师傅', label: '赵师傅' },
  { value: '陈师傅', label: '陈师傅' },
  { value: '刘师傅', label: '刘师傅' }
];

const availableCalendars = [
  { value: 'CAL-STANDARD', label: '标准工作日历', workingHours: '8' },
  { value: 'CAL-CONTINUOUS', label: '连续生产日历', workingHours: '24' },
  { value: 'CAL-TWO-SHIFT', label: '两班制日历', workingHours: '16' },
  { value: 'CAL-THREE-SHIFT', label: '三班制日历', workingHours: '24' }
];

// 计算属性
const errors = computed(() => props.errors);

// 方法
const validateField = (fieldName: string) => {
  const newErrors = { ...errors.value };
  
  switch (fieldName) {
    case 'name':
      if (!localData.value.name?.trim()) {
        newErrors.name = '工作中心名称不能为空';
      } else {
        delete newErrors.name;
      }
      break;
      
    case 'code':
      if (localData.value.code && !/^[A-Za-z0-9-]+$/.test(localData.value.code)) {
        newErrors.code = '工作中心编码格式不正确';
      } else {
        delete newErrors.code;
      }
      break;
  }
  
  emit('validate', newErrors);
};

const addCapability = () => {
  capabilities.value.push({ type: '', specification: '', capacity: '' });
};

const removeCapability = (index: number) => {
  capabilities.value.splice(index, 1);
  updateCapabilities();
};

const addQualityStandard = () => {
  qualityStandards.value.push({ name: '', requirement: '', level: 'A' });
};

const removeQualityStandard = (index: number) => {
  qualityStandards.value.splice(index, 1);
  updateQualityStandards();
};

const updateCapabilities = () => {
  if (!localData.value.capabilities) {
    localData.value.capabilities = [];
  }
  localData.value.capabilities = capabilities.value;
  updateModelValue();
};

const updateQualityStandards = () => {
  if (!localData.value.qualityStandards) {
    localData.value.qualityStandards = [];
  }
  localData.value.qualityStandards = qualityStandards.value;
  updateModelValue();
};

const updateModelValue = () => {
  if (isUpdating.value) return;
  emit('update:modelValue', { ...localData.value });
};

// 监听器
watch(localData, () => {
  updateModelValue();
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  if (isUpdating.value) return;
  
  isUpdating.value = true;
  localData.value = { ...newValue };
  
  // 初始化工艺能力和质量标准
  if (newValue.capabilities) {
    capabilities.value = [...newValue.capabilities];
  } else {
    capabilities.value = [];
  }
  
  if (newValue.qualityStandards) {
    qualityStandards.value = [...newValue.qualityStandards];
  } else {
    qualityStandards.value = [];
  }
  
  nextTick(() => {
    isUpdating.value = false;
  });
}, { deep: true });

// 初始化
if (props.modelValue.capabilities) {
  capabilities.value = [...props.modelValue.capabilities];
}
if (props.modelValue.qualityStandards) {
  qualityStandards.value = [...props.modelValue.qualityStandards];
}
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
