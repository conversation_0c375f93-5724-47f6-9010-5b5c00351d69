<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-[1000px] max-h-[90vh] w-[90vw] overflow-hidden flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle>
          {{ isEditing ? '编辑工作中心' : '工作中心详情' }}
          <span v-if="workCenter" class="text-sm font-normal text-muted-foreground ml-2">
            {{ workCenter.id }}
          </span>
        </DialogTitle>
      </DialogHeader>

      <!-- 标签页导航 -->
      <div class="border-b flex-shrink-0">
        <nav class="flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === tab.id
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            ]"
          >
            <component :is="tab.icon" class="w-4 h-4 inline mr-2" />
            {{ tab.label }}
          </button>
        </nav>
      </div>

      <!-- 标签页内容 -->
      <div class="flex-1 overflow-y-auto py-4 min-h-0 equipment-dialog-scroll">
        <!-- 基础信息 -->
        <div v-if="activeTab === 'basic'" class="space-y-4">
          <WorkCenterBasicForm
            v-model="formData"
            :is-editing="isEditing"
            :errors="validationErrors.basic"
            @validate="validateBasicInfo"
          />
        </div>

        <!-- 设备管理 -->
        <div v-if="activeTab === 'equipment'" class="space-y-4">
          <WorkCenterEquipmentPanel
            :work-center="workCenter"
            :is-editing="isEditing"
            @update-equipment="updateEquipmentAssignment"
          />
        </div>

        <!-- 产能配置 -->
        <div v-if="activeTab === 'capacity'" class="space-y-4">
          <WorkCenterCapacityPanel
            v-model="formData.capacityConfig"
            :work-center="workCenter"
            :is-editing="isEditing"
            @validate="validateCapacity"
          />
        </div>

        <!-- 工序分配 -->
        <div v-if="activeTab === 'processes'" class="space-y-4">
          <WorkCenterProcessPanel
            :work-center="workCenter"
            :is-editing="isEditing"
            @update-processes="updateProcessAssignment"
          />
        </div>

        <!-- 排程管理 -->
        <div v-if="activeTab === 'scheduling'" class="space-y-4">
          <WorkCenterSchedulingPanel
            :work-center="workCenter"
            :is-editing="isEditing"
            @update-schedule="updateScheduleConfig"
          />
        </div>

        <!-- 性能监控 -->
        <div v-if="activeTab === 'monitoring'" class="space-y-4">
          <WorkCenterMonitoringPanel
            :work-center="workCenter"
          />
        </div>

        <!-- 智能排产演示 -->
        <div v-if="activeTab === 'intelligent'" class="space-y-4">
          <IntelligentSchedulingDemo />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between pt-4 border-t flex-shrink-0">
        <div class="flex gap-2">
          <Button
            v-if="!isEditing && workCenter"
            variant="outline"
            @click="toggleEdit"
          >
            <Edit class="w-4 h-4 mr-2" />
            编辑
          </Button>
          <Button
            v-if="workCenter"
            variant="outline"
            @click="duplicateWorkCenter"
          >
            <Copy class="w-4 h-4 mr-2" />
            复制
          </Button>
        </div>

        <div class="flex gap-2">
          <Button variant="outline" @click="closeDialog">
            取消
          </Button>
          <Button
            v-if="isEditing"
            @click="saveChanges"
            :disabled="loading || !isFormValid"
          >
            <Save class="w-4 h-4 mr-2" />
            保存
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Edit, Copy, Save, Info, Settings, Factory, Cog, Calendar, Activity, Zap } from 'lucide-vue-next';
import type { WorkCenter } from '@/types/masterdata';
import { toast } from 'vue-sonner';

// 子组件导入（稍后创建）
import WorkCenterBasicForm from './WorkCenterBasicForm.vue';
import WorkCenterEquipmentPanel from './WorkCenterEquipmentPanel.vue';
import WorkCenterCapacityPanel from './WorkCenterCapacityPanel.vue';
import WorkCenterProcessPanel from './WorkCenterProcessPanel.vue';
import WorkCenterSchedulingPanel from './WorkCenterSchedulingPanel.vue';
import WorkCenterMonitoringPanel from './WorkCenterMonitoringPanel.vue';
import IntelligentSchedulingDemo from './IntelligentSchedulingDemo.vue';

interface Props {
  workCenter?: WorkCenter | null;
  open: boolean;
  mode?: 'view' | 'edit' | 'create';
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'save', workCenter: WorkCenter): void;
  (e: 'duplicate', workCenter: WorkCenter): void;
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'view'
});

const emit = defineEmits<Emits>();

// 响应式数据
const activeTab = ref('basic');
const isEditing = ref(false);
const loading = ref(false);
const formData = ref<Partial<WorkCenter>>({});
const validationErrors = ref({
  basic: {},
  capacity: {},
  equipment: {},
  processes: {}
});

// 标签页配置
const tabs = [
  { id: 'basic', label: '基础信息', icon: Info },
  { id: 'equipment', label: '设备管理', icon: Settings },
  { id: 'capacity', label: '产能配置', icon: Factory },
  { id: 'processes', label: '工序分配', icon: Cog },
  { id: 'scheduling', label: '排程管理', icon: Calendar },
  { id: 'monitoring', label: '性能监控', icon: Activity },
  { id: 'intelligent', label: '智能排产', icon: Zap }
];

// 计算属性
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
});

const isFormValid = computed(() => {
  return Object.values(validationErrors.value).every(errors => 
    Object.keys(errors).length === 0
  );
});

// 方法
const initializeForm = () => {
  if (props.workCenter) {
    formData.value = { 
      ...props.workCenter,
      capacityConfig: props.workCenter.capacityConfig || {
        maxCapacity: props.workCenter.capacity || 100,
        currentCapacity: 0,
        utilizationRate: 0,
        shiftConfig: [],
        bottleneckAnalysis: {}
      }
    };
  } else {
    formData.value = {
      name: '',
      equipmentIds: [],
      calendarId: 'CAL-STANDARD',
      location: '',
      capacity: 100,
      efficiency: 95,
      costCenter: '',
      supervisor: '',
      description: '',
      capacityConfig: {
        maxCapacity: 100,
        currentCapacity: 0,
        utilizationRate: 0,
        shiftConfig: [],
        bottleneckAnalysis: {}
      }
    };
  }
  
  // 重置验证错误
  validationErrors.value = {
    basic: {},
    capacity: {},
    equipment: {},
    processes: {}
  };
};

const toggleEdit = () => {
  isEditing.value = !isEditing.value;
  if (isEditing.value) {
    initializeForm();
  }
};

const closeDialog = () => {
  isEditing.value = false;
  activeTab.value = 'basic';
  emit('update:open', false);
};

const saveChanges = async () => {
  if (!isFormValid.value) {
    toast.error('请修正表单错误后再保存');
    return;
  }

  loading.value = true;
  try {
    emit('save', formData.value as WorkCenter);
    isEditing.value = false;
    toast.success('工作中心信息保存成功');
  } catch (error) {
    toast.error('保存失败，请重试');
  } finally {
    loading.value = false;
  }
};

const duplicateWorkCenter = () => {
  if (props.workCenter) {
    const duplicated = {
      ...props.workCenter,
      id: '', // 清空ID，让系统生成新的
      name: `${props.workCenter.name} - 副本`,
    };
    emit('duplicate', duplicated);
  }
};

// 验证方法
const validateBasicInfo = (errors: any) => {
  validationErrors.value.basic = errors;
};

const validateCapacity = (errors: any) => {
  validationErrors.value.capacity = errors;
};

// 事件处理
const updateEquipmentAssignment = (equipmentData: any) => {
  formData.value.equipmentIds = equipmentData.equipmentIds;
};

const updateProcessAssignment = (processData: any) => {
  // 处理工序分配更新
  console.log('工序分配更新:', processData);
};

const updateScheduleConfig = (scheduleData: any) => {
  // 处理排程配置更新
  console.log('排程配置更新:', scheduleData);
};

// 监听器
watch(() => props.open, (newValue) => {
  if (newValue) {
    initializeForm();
    isEditing.value = props.mode === 'edit' || props.mode === 'create';
  }
});

watch(() => props.mode, (newMode) => {
  isEditing.value = newMode === 'edit' || newMode === 'create';
});

// 生命周期
onMounted(() => {
  if (props.open) {
    initializeForm();
  }
});
</script>
