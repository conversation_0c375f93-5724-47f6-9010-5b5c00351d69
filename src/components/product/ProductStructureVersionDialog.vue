<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[80vw] h-[80vh] max-w-none max-h-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <History class="w-5 h-5" />
          版本历史
          <span v-if="structure" class="text-base font-normal text-gray-600">
            - {{ structure.name }}
          </span>
        </DialogTitle>
        <DialogDescription v-if="structure">
          查看和管理产品结构的版本变更历史，支持版本对比和回滚操作
        </DialogDescription>
      </DialogHeader>

      <div v-if="structure" class="flex-1 overflow-hidden">
        <div class="h-full flex">
          <!-- 左侧：版本列表 -->
          <div class="w-1/3 border-r overflow-auto">
            <div class="p-4 border-b bg-gray-50">
              <h3 class="font-medium">版本列表</h3>
              <p class="text-sm text-gray-600">共 {{ structure.versionHistory?.length || 0 }} 个版本</p>
            </div>
            
            <div class="space-y-1 p-2">
              <div
                v-for="version in sortedVersions"
                :key="version.id"
                :class="[
                  'p-3 rounded-lg cursor-pointer transition-colors',
                  selectedVersion?.id === version.id 
                    ? 'bg-blue-100 border border-blue-300' 
                    : 'hover:bg-gray-50'
                ]"
                @click="selectVersion(version)"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2">
                    <span class="font-medium">v{{ version.versionNumber }}</span>
                    <Badge v-if="version.isCurrent" variant="default" class="text-xs">当前</Badge>
                  </div>
                  <span class="text-xs text-gray-500">
                    {{ formatDate(version.changeDate) }}
                  </span>
                </div>
                
                <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                  {{ version.changeDescription }}
                </p>
                
                <div class="flex items-center gap-2 mt-2 text-xs text-gray-500">
                  <span>{{ version.changedBy }}</span>
                  <span>•</span>
                  <span>{{ version.changeRecords?.length || 0 }} 个变更</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：版本详情 -->
          <div class="flex-1 overflow-auto">
            <div v-if="selectedVersion" class="h-full">
              <div class="p-4 border-b bg-gray-50">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-medium">版本 {{ selectedVersion.versionNumber }}</h3>
                    <p class="text-sm text-gray-600">{{ selectedVersion.changeDescription }}</p>
                  </div>
                  <div class="flex gap-2">
                    <Button
                      v-if="!selectedVersion.isCurrent"
                      variant="outline"
                      size="sm"
                      @click="compareWithCurrent"
                    >
                      <GitCompare class="w-4 h-4 mr-2" />
                      对比当前版本
                    </Button>
                    <Button
                      v-if="!selectedVersion.isCurrent"
                      variant="outline"
                      size="sm"
                      @click="restoreVersion"
                    >
                      <RotateCcw class="w-4 h-4 mr-2" />
                      恢复此版本
                    </Button>
                  </div>
                </div>
              </div>

              <div class="p-4 space-y-4">
                <!-- 版本基本信息 -->
                <Card>
                  <CardHeader>
                    <CardTitle class="text-base">版本信息</CardTitle>
                  </CardHeader>
                  <CardContent class="space-y-3">
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <Label class="text-sm text-gray-600">变更日期</Label>
                        <p class="font-medium">{{ formatDateTime(selectedVersion.changeDate) }}</p>
                      </div>
                      <div>
                        <Label class="text-sm text-gray-600">变更人</Label>
                        <p class="font-medium">{{ selectedVersion.changedBy }}</p>
                      </div>
                    </div>
                    
                    <div>
                      <Label class="text-sm text-gray-600">变更类型</Label>
                      <Badge variant="outline">{{ selectedVersion.changeType }}</Badge>
                    </div>

                    <div v-if="selectedVersion.approvedBy">
                      <Label class="text-sm text-gray-600">审批信息</Label>
                      <div class="text-sm">
                        <p>审批人: {{ selectedVersion.approvedBy }}</p>
                        <p>审批日期: {{ formatDateTime(selectedVersion.approvedDate || '') }}</p>
                        <p v-if="selectedVersion.approvalComments">审批意见: {{ selectedVersion.approvalComments }}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <!-- 变更记录 -->
                <Card>
                  <CardHeader>
                    <CardTitle class="text-base">变更记录</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div v-if="selectedVersion.changeRecords && selectedVersion.changeRecords.length > 0" class="space-y-3">
                      <div v-for="record in selectedVersion.changeRecords" :key="record.id" class="p-3 border rounded-lg">
                        <div class="flex items-center gap-2 mb-2">
                          <Badge :variant="getChangeTypeVariant(record.changeType)">
                            {{ getChangeTypeText(record.changeType) }}
                          </Badge>
                          <Badge :variant="getOperationTypeVariant(record.operationType)">
                            {{ getOperationTypeText(record.operationType) }}
                          </Badge>
                        </div>
                        
                        <p class="text-sm font-medium">{{ record.targetName }}</p>
                        <p class="text-sm text-gray-600">{{ record.description }}</p>
                        
                        <div class="text-xs text-gray-500 mt-2">
                          {{ formatDateTime(record.changeTime) }} - {{ record.changedBy }}
                        </div>
                      </div>
                    </div>
                    <div v-else class="text-center py-4 text-gray-500">
                      暂无变更记录
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            <div v-else class="flex items-center justify-center h-full text-gray-500">
              <div class="text-center">
                <History class="w-12 h-12 text-gray-300 mx-auto mb-2" />
                <p>请选择一个版本查看详情</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter class="border-t pt-4">
        <Button variant="outline" @click="$emit('update:open', false)">
          关闭
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { History, GitCompare, RotateCcw } from 'lucide-vue-next';

import type { 
  ProductStructure, 
  VersionHistory,
  ChangeType,
  OperationType
} from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  structure?: ProductStructure | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:open': [open: boolean];
  'restore-version': [structure: ProductStructure, version: VersionHistory];
}>();

// 响应式数据
const selectedVersion = ref<VersionHistory | null>(null);

// 计算属性
const sortedVersions = computed(() => {
  if (!props.structure) return [];
  return [...props.structure.versionHistory].sort((a, b) => 
    new Date(b.changeDate).getTime() - new Date(a.changeDate).getTime()
  );
});

// 方法
const selectVersion = (version: VersionHistory) => {
  selectedVersion.value = version;
};

const compareWithCurrent = () => {
  if (!selectedVersion.value || !props.structure) return;
  console.log('对比版本:', selectedVersion.value.versionNumber);
  // 这里可以实现版本对比功能
};

const restoreVersion = () => {
  if (!selectedVersion.value || !props.structure) return;
  
  if (confirm(`确定要恢复到版本 ${selectedVersion.value.versionNumber} 吗？`)) {
    emit('restore-version', props.structure, selectedVersion.value);
  }
};

// 工具函数
const getChangeTypeText = (type: ChangeType): string => {
  const typeMap = {
    component: '组件',
    assembly: '构件',
    parameter: '参数',
    constraint: '约束',
    configuration: '配置'
  };
  return typeMap[type] || type;
};

const getChangeTypeVariant = (type: ChangeType) => {
  const variantMap = {
    component: 'default',
    assembly: 'secondary',
    parameter: 'outline',
    constraint: 'destructive',
    configuration: 'default'
  };
  return variantMap[type] || 'default';
};

const getOperationTypeText = (type: OperationType): string => {
  const typeMap = {
    create: '创建',
    update: '更新',
    delete: '删除'
  };
  return typeMap[type] || type;
};

const getOperationTypeVariant = (type: OperationType) => {
  const variantMap = {
    create: 'default',
    update: 'secondary',
    delete: 'destructive'
  };
  return variantMap[type] || 'default';
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

const formatDateTime = (dateString: string): string => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString('zh-CN');
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
