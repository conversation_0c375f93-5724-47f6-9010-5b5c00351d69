<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[90vw] h-[90vh] max-w-none max-h-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Package class="w-5 h-5" />
          {{ isEditing ? '编辑产品结构' : '新建产品结构' }}
          <Badge v-if="formData.productType" :variant="getProductTypeVariant(formData.productType)">
            {{ getProductTypeText(formData.productType) }}
          </Badge>
        </DialogTitle>
        <DialogDescription>
          {{ isEditing ? '修改产品结构定义，包括基本信息、根构件、参数配置和约束条件' : '创建新的产品结构，定义产品的层级关系和配置选项' }}
        </DialogDescription>
      </DialogHeader>

      <div class="flex-1 overflow-hidden">
        <Tabs v-model="activeTab" class="h-full flex flex-col">
          <TabsList class="grid w-full grid-cols-5">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="structure">结构定义</TabsTrigger>
            <TabsTrigger value="parameters">产品参数</TabsTrigger>
            <TabsTrigger value="configuration">配置选项</TabsTrigger>
            <TabsTrigger value="validation">验证预览</TabsTrigger>
          </TabsList>

          <!-- 基本信息 -->
          <TabsContent value="basic" class="flex-1 overflow-auto p-4">
            <div class="space-y-6 max-w-2xl">
              <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                  <Label for="code">结构编码 *</Label>
                  <Input
                    id="code"
                    v-model="formData.code"
                    placeholder="输入结构编码"
                    :class="{ 'border-red-500': errors.code }"
                  />
                  <p v-if="errors.code" class="text-sm text-red-500">{{ errors.code }}</p>
                </div>

                <div class="space-y-2">
                  <Label for="name">结构名称 *</Label>
                  <Input
                    id="name"
                    v-model="formData.name"
                    placeholder="输入结构名称"
                    :class="{ 'border-red-500': errors.name }"
                  />
                  <p v-if="errors.name" class="text-sm text-red-500">{{ errors.name }}</p>
                </div>
              </div>

              <div class="space-y-2">
                <Label for="description">结构描述</Label>
                <Textarea
                  id="description"
                  v-model="formData.description"
                  placeholder="输入结构描述"
                  rows="3"
                />
              </div>

              <div class="grid grid-cols-3 gap-4">
                <div class="space-y-2">
                  <Label for="product-type">产品类型 *</Label>
                  <Select v-model="formData.productType">
                    <SelectTrigger :class="{ 'border-red-500': errors.productType }">
                      <SelectValue placeholder="选择产品类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="partition">隔断</SelectItem>
                      <SelectItem value="window">窗户</SelectItem>
                      <SelectItem value="door">门</SelectItem>
                      <SelectItem value="curtain_wall">幕墙</SelectItem>
                      <SelectItem value="other">其他</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="errors.productType" class="text-sm text-red-500">{{ errors.productType }}</p>
                </div>

                <div class="space-y-2">
                  <Label for="category">产品类别 *</Label>
                  <Input
                    id="category"
                    v-model="formData.category"
                    placeholder="输入产品类别"
                    :class="{ 'border-red-500': errors.category }"
                  />
                  <p v-if="errors.category" class="text-sm text-red-500">{{ errors.category }}</p>
                </div>

                <div class="space-y-2">
                  <Label for="sub-category">产品子类别</Label>
                  <Input
                    id="sub-category"
                    v-model="formData.subCategory"
                    placeholder="输入产品子类别"
                  />
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                  <Label for="status">状态</Label>
                  <Select v-model="formData.status">
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">草稿</SelectItem>
                      <SelectItem value="active">活跃</SelectItem>
                      <SelectItem value="deprecated">已弃用</SelectItem>
                      <SelectItem value="archived">已归档</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div class="space-y-2">
                  <Label for="applications">应用场景</Label>
                  <Input
                    id="applications"
                    v-model="applicationsText"
                    placeholder="输入应用场景，用逗号分隔"
                  />
                </div>
              </div>

              <div class="space-y-2">
                <Label for="tags">标签</Label>
                <Input
                  id="tags"
                  v-model="tagsText"
                  placeholder="输入标签，用逗号分隔"
                />
              </div>
            </div>
          </TabsContent>

          <!-- 结构定义 -->
          <TabsContent value="structure" class="flex-1 overflow-auto p-4">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium">根构件定义</h3>
                <Button variant="outline" @click="selectRootAssembly">
                  <Search class="w-4 h-4 mr-2" />
                  选择构件
                </Button>
              </div>

              <Card v-if="formData.rootAssembly" class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="font-medium">{{ formData.rootAssembly.assemblyName }}</h4>
                    <p class="text-sm text-gray-600">{{ formData.rootAssembly.assemblyCode }}</p>
                  </div>
                  <Button variant="ghost" size="sm" @click="editRootAssembly">
                    <Edit class="w-4 h-4" />
                  </Button>
                </div>
              </Card>

              <div v-else class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Package class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-500">请选择根构件</p>
                <Button variant="outline" @click="selectRootAssembly" class="mt-2">
                  选择构件
                </Button>
              </div>
            </div>
          </TabsContent>

          <!-- 产品参数 -->
          <TabsContent value="parameters" class="flex-1 overflow-auto p-4">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium">产品级参数</h3>
                <Button variant="outline" @click="addProductParameter">
                  <Plus class="w-4 h-4 mr-2" />
                  添加参数
                </Button>
              </div>

              <div v-if="!formData.productParameters || formData.productParameters.length === 0" class="text-center py-8 text-gray-500">
                暂无产品参数
              </div>

              <div v-else class="space-y-3">
                <Card v-for="(param, index) in formData.productParameters" :key="param.id" class="p-4">
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">{{ param.displayName }}</span>
                        <Badge variant="outline">{{ param.type }}</Badge>
                        <Badge v-if="param.required" variant="destructive" class="text-xs">必填</Badge>
                      </div>
                      <p class="text-sm text-gray-600 mt-1">{{ param.description }}</p>
                    </div>
                    <div class="flex items-center gap-2">
                      <Button variant="ghost" size="sm" @click="editProductParameter(index)">
                        <Edit class="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" @click="removeProductParameter(index)">
                        <Trash2 class="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </TabsContent>

          <!-- 配置选项 -->
          <TabsContent value="configuration" class="flex-1 overflow-auto p-4">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium">配置选项</h3>
                <Button variant="outline" @click="addConfigurationOption">
                  <Plus class="w-4 h-4 mr-2" />
                  添加配置
                </Button>
              </div>

              <div v-if="!formData.configurationOptions || formData.configurationOptions.length === 0" class="text-center py-8 text-gray-500">
                暂无配置选项
              </div>

              <div v-else class="space-y-3">
                <Card v-for="(option, index) in formData.configurationOptions" :key="option.id" class="p-4">
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">{{ option.displayName }}</span>
                        <Badge variant="outline">{{ option.type }}</Badge>
                        <Badge v-if="option.required" variant="destructive" class="text-xs">必选</Badge>
                      </div>
                      <p class="text-sm text-gray-600 mt-1">{{ option.description }}</p>
                      <p class="text-xs text-gray-500 mt-1">{{ option.choices?.length || 0 }} 个选项</p>
                    </div>
                    <div class="flex items-center gap-2">
                      <Button variant="ghost" size="sm" @click="editConfigurationOption(index)">
                        <Edit class="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" @click="removeConfigurationOption(index)">
                        <Trash2 class="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </TabsContent>

          <!-- 验证预览 -->
          <TabsContent value="validation" class="flex-1 overflow-auto p-4">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium">结构验证</h3>
                <Button variant="outline" @click="validateStructure">
                  <CheckCircle class="w-4 h-4 mr-2" />
                  立即验证
                </Button>
              </div>

              <div v-if="!previewValidationResult" class="text-center py-8 text-gray-500">
                点击"立即验证"查看结构验证结果
              </div>

              <div v-else class="space-y-4">
                <!-- 验证摘要 -->
                <Card class="p-4">
                  <div class="grid grid-cols-4 gap-4 text-center">
                    <div>
                      <div class="text-2xl font-bold text-red-600">{{ previewValidationResult.summary.totalErrors }}</div>
                      <div class="text-sm text-gray-600">错误</div>
                    </div>
                    <div>
                      <div class="text-2xl font-bold text-yellow-600">{{ previewValidationResult.summary.totalWarnings }}</div>
                      <div class="text-sm text-gray-600">警告</div>
                    </div>
                    <div>
                      <div class="text-2xl font-bold text-blue-600">{{ previewValidationResult.summary.totalSuggestions }}</div>
                      <div class="text-sm text-gray-600">建议</div>
                    </div>
                    <div>
                      <div class="text-2xl font-bold text-green-600">{{ previewValidationResult.isValid ? '通过' : '失败' }}</div>
                      <div class="text-sm text-gray-600">状态</div>
                    </div>
                  </div>
                </Card>

                <!-- 验证详情 -->
                <div v-if="previewValidationResult.errors && previewValidationResult.errors.length > 0" class="space-y-2">
                  <h4 class="font-medium text-red-600">错误信息</h4>
                  <Card v-for="error in previewValidationResult.errors" :key="error.id" class="p-3">
                    <div class="flex items-start gap-3">
                      <AlertTriangle class="w-4 h-4 text-red-500 mt-0.5" />
                      <div class="flex-1">
                        <p class="text-sm font-medium">{{ error.message }}</p>
                        <p class="text-xs text-gray-500">{{ error.location.objectType }} - {{ error.location.objectId }}</p>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <DialogFooter class="border-t pt-4">
        <div class="flex items-center justify-between w-full">
          <div class="flex items-center gap-2">
            <Badge v-if="formData.status" :variant="getStatusVariant(formData.status)">
              {{ getStatusText(formData.status) }}
            </Badge>
            <span v-if="isEditing" class="text-sm text-gray-500">
              版本 v{{ formData.version }}
            </span>
          </div>
          
          <div class="flex gap-2">
            <Button variant="outline" @click="$emit('cancel')">
              取消
            </Button>
            <Button @click="handleSave" :disabled="!isFormValid">
              {{ isEditing ? '更新' : '创建' }}
            </Button>
          </div>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { 
  Package, 
  Edit, 
  Plus, 
  Trash2, 
  Search, 
  CheckCircle, 
  AlertTriangle 
} from 'lucide-vue-next';

import type { 
  ProductStructure, 
  ProductType,
  ComponentParameter,
  ConfigurationOption,
  StructureValidationResult
} from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  structure?: ProductStructure | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:open': [open: boolean];
  'save': [structure: ProductStructure];
  'cancel': [];
}>();

// 响应式数据
const activeTab = ref('basic');
const previewValidationResult = ref<StructureValidationResult | null>(null);

// 表单数据
const formData = reactive<Partial<ProductStructure>>({
  code: '',
  name: '',
  description: '',
  productType: 'other',
  category: '',
  subCategory: '',
  status: 'draft',
  productParameters: [],
  productConstraints: [],
  configurationOptions: [],
  applications: [],
  tags: [],
  properties: {}
});

// 表单验证错误
const errors = reactive({
  code: '',
  name: '',
  productType: '',
  category: ''
});

// 辅助字段
const applicationsText = ref('');
const tagsText = ref('');

// 计算属性
const isEditing = computed(() => !!props.structure?.id);

const isFormValid = computed(() => {
  return formData.code && formData.name && formData.productType && formData.category;
});

// 工具函数
const getProductTypeText = (type: ProductType): string => {
  const typeMap = {
    partition: '隔断',
    window: '窗户', 
    door: '门',
    curtain_wall: '幕墙',
    other: '其他'
  };
  return typeMap[type] || type;
};

const getProductTypeVariant = (type: ProductType) => {
  const variantMap = {
    partition: 'default',
    window: 'secondary',
    door: 'outline', 
    curtain_wall: 'destructive',
    other: 'secondary'
  };
  return variantMap[type] || 'default';
};

const getStatusText = (status: string): string => {
  const statusMap = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return statusMap[status] || status;
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'default';
};

// 方法
const resetForm = () => {
  Object.assign(formData, {
    code: '',
    name: '',
    description: '',
    productType: 'other',
    category: '',
    subCategory: '',
    status: 'draft',
    productParameters: [],
    productConstraints: [],
    configurationOptions: [],
    applications: [],
    tags: [],
    properties: {}
  });
  applicationsText.value = '';
  tagsText.value = '';
  previewValidationResult.value = null;
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });
};

const validateForm = (): boolean => {
  let isValid = true;
  
  // 重置错误
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });

  if (!formData.code) {
    errors.code = '请输入结构编码';
    isValid = false;
  }

  if (!formData.name) {
    errors.name = '请输入结构名称';
    isValid = false;
  }

  if (!formData.productType) {
    errors.productType = '请选择产品类型';
    isValid = false;
  }

  if (!formData.category) {
    errors.category = '请输入产品类别';
    isValid = false;
  }

  return isValid;
};

const handleSave = () => {
  if (!validateForm()) {
    activeTab.value = 'basic';
    return;
  }

  // 处理应用场景和标签
  formData.applications = applicationsText.value
    .split(',')
    .map(app => app.trim())
    .filter(app => app.length > 0);
    
  formData.tags = tagsText.value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);

  emit('save', formData as ProductStructure);
};

// 占位方法（后续实现）
const selectRootAssembly = () => {
  console.log('选择根构件');
};

const editRootAssembly = () => {
  console.log('编辑根构件');
};

const addProductParameter = () => {
  console.log('添加产品参数');
};

const editProductParameter = (index: number) => {
  console.log('编辑产品参数:', index);
};

const removeProductParameter = (index: number) => {
  formData.productParameters?.splice(index, 1);
};

const addConfigurationOption = () => {
  console.log('添加配置选项');
};

const editConfigurationOption = (index: number) => {
  console.log('编辑配置选项:', index);
};

const removeConfigurationOption = (index: number) => {
  formData.configurationOptions?.splice(index, 1);
};

const validateStructure = () => {
  console.log('验证结构');
  // 这里可以调用验证服务
};

// 监听props变化
watch(() => props.structure, (newStructure) => {
  if (newStructure) {
    Object.assign(formData, newStructure);
    applicationsText.value = newStructure.applications?.join(', ') || '';
    tagsText.value = newStructure.tags?.join(', ') || '';
  } else {
    resetForm();
  }
}, { immediate: true });
</script>

<style scoped>
/* 自定义样式 */
</style>
