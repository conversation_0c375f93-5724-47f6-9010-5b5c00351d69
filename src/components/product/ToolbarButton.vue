<template>
  <div class="toolbar-button-wrapper">
    <button
      :class="[
        'toolbar-button',
        'flex flex-col items-center justify-center',
        'w-12 h-10 rounded border transition-all duration-150',
        'hover:bg-gray-200 hover:border-gray-400',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50',
        {
          'bg-blue-100 border-blue-400 text-blue-700': active,
          'bg-white border-gray-300 text-gray-700': !active,
          'opacity-50 cursor-not-allowed': disabled,
          'hover:bg-gray-100': disabled
        }
      ]"
      :disabled="disabled"
      :title="`${label}${shortcut ? ` (${shortcut})` : ''}`"
      @click="handleClick"
    >
      <component :is="getIcon(icon)" class="w-4 h-4 mb-0.5" />
      <span class="text-xs leading-none">{{ label }}</span>
    </button>
  </div>
</template>

<script setup lang="ts">
import {
  FileText,
  FolderO<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>o,
  <PERSON><PERSON>ointer,
  Move,
  Square,
  Layers,
  ZoomIn,
  ZoomOut,
  Maximize2,
  RotateCcw,
  CheckCircle,
  HelpCircle,
  Plus,
  Trash2,
  Copy,
  Edit,
  Settings
} from 'lucide-vue-next';

// Props
interface Props {
  icon: string;
  label: string;
  shortcut?: string;
  active?: boolean;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  active: false,
  disabled: false
});

// Emits
const emit = defineEmits<{
  'click': [];
}>();

// 方法
const getIcon = (iconName: string) => {
  const iconMap = {
    FileText,
    FolderOpen,
    Save,
    Undo,
    Redo,
    MousePointer,
    Move,
    Square,
    Layers,
    ZoomIn,
    ZoomOut,
    Maximize2,
    RotateCcw,
    CheckCircle,
    HelpCircle,
    Plus,
    Trash2,
    Copy,
    Edit,
    Settings
  };
  return iconMap[iconName] || Settings;
};

const handleClick = () => {
  if (!props.disabled) {
    emit('click');
  }
};
</script>

<style scoped>
.toolbar-button {
  min-width: 48px;
  user-select: none;
}

.toolbar-button:active:not(:disabled) {
  transform: translateY(1px);
}

.toolbar-button:disabled {
  pointer-events: none;
}

/* 工具提示样式 */
.toolbar-button-wrapper {
  position: relative;
}

.toolbar-button:hover::after {
  content: attr(title);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}
</style>
