<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-[1400px] w-[95vw] h-[90vh] p-0 flex flex-col overflow-hidden">
      <DialogHeader class="px-6 py-4 border-b">
        <div class="flex items-center justify-between">
          <div>
            <DialogTitle class="text-xl font-semibold">
              产品结构编辑器
            </DialogTitle>
            <DialogDescription class="mt-1">
              {{ structure?.name }} ({{ structure?.code }})
            </DialogDescription>
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="toggleFullscreen"
              :title="isFullscreen ? '退出全屏' : '全屏模式'"
            >
              <Maximize2 v-if="!isFullscreen" class="w-4 h-4" />
              <Minimize2 v-else class="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              @click="showShortcutsHelp = !showShortcutsHelp"
              title="快捷键帮助"
            >
              <Keyboard class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </DialogHeader>

      <div class="flex-1 flex overflow-hidden min-h-0">
        <!-- 主要编辑区域 -->
        <div class="flex-1 flex flex-col min-h-0 overflow-hidden">
          <!-- 工具栏 -->
          <div class="flex items-center justify-between p-4 border-b bg-white flex-shrink-0">
            <div class="flex items-center gap-4">
              <!-- 搜索框 -->
              <div class="relative">
                <Search class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  v-model="searchQuery"
                  placeholder="搜索节点名称、编码..."
                  class="pl-10 w-80"
                />
              </div>

              <!-- 搜索结果导航 -->
              <div v-if="searchResults.length > 0" class="flex items-center gap-2 text-sm text-gray-600">
                <span>{{ currentSearchIndex + 1 }}/{{ searchResults.length }}</span>
                <Button
                  variant="outline"
                  size="sm"
                  @click="previousSearchResult"
                  :disabled="searchResults.length === 0"
                  class="h-8 px-2"
                >
                  <ChevronUp class="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  @click="nextSearchResult"
                  :disabled="searchResults.length === 0"
                  class="h-8 px-2"
                >
                  <ChevronDown class="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div class="flex items-center gap-2">
              <!-- 选择操作 -->
              <Button
                variant="outline"
                size="sm"
                @click="clearSelection"
                :disabled="selectedNodeIds.size === 0"
              >
                清除选择 ({{ selectedNodeIds.size }})
              </Button>

              <!-- 展开/折叠 -->
              <Button
                variant="outline"
                size="sm"
                @click="expandAll"
              >
                <ChevronDown class="w-4 h-4 mr-1" />
                全部展开
              </Button>
              <Button
                variant="outline"
                size="sm"
                @click="collapseAll"
              >
                <ChevronUp class="w-4 h-4 mr-1" />
                全部折叠
              </Button>
            </div>
          </div>

          <!-- 树状表格视图 -->
          <div class="flex-1 overflow-auto min-h-0 table-container">
            <table class="w-full">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">
                    名称
                  </th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">
                    编码
                  </th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">
                    类型
                  </th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">
                    数量
                  </th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">
                    描述
                  </th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <tr
                  v-for="node in visibleNodes"
                  :key="node.id"
                  :class="[
                    'hover:bg-gray-50 transition-colors cursor-pointer',
                    { 'bg-blue-50 border-l-4 border-blue-500': selectedNodeIds.has(node.id) },
                    { 'bg-yellow-50': isNodeHighlighted(node) }
                  ]"
                  @click="handleRowClick(node.id, $event)"
                  @dblclick="handleNodeEdit(node.id)"
                  @contextmenu="handleRowContextMenu(node.id, $event)"
                >
                  <!-- 名称列 -->
                  <td class="px-4 py-3">
                    <div class="flex items-center" :style="{ paddingLeft: `${node.level * 20}px` }">
                      <!-- 展开/折叠按钮 -->
                      <button
                        v-if="hasNodeChildren(node.id)"
                        @click.stop="handleNodeToggle(node.id)"
                        class="w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded mr-2"
                      >
                        <ChevronRight
                          :class="[
                            'w-3 h-3 transition-transform',
                            { 'rotate-90': expandedNodes.has(node.id) }
                          ]"
                        />
                      </button>
                      <div v-else class="w-4 h-4 mr-2"></div>

                      <!-- 节点图标 -->
                      <component
                        :is="getNodeIcon(node.type)"
                        :class="['w-4 h-4 mr-2', getNodeIconColor(node.type)]"
                      />

                      <!-- 节点名称 -->
                      <div class="flex-1 min-w-0">
                        <div class="font-medium text-sm">{{ node.name }}</div>
                      </div>
                    </div>
                  </td>

                  <!-- 编码列 -->
                  <td class="px-4 py-3 text-sm">
                    <code v-if="node.code" class="text-xs bg-gray-100 px-2 py-1 rounded">
                      {{ node.code }}
                    </code>
                  </td>

                  <!-- 类型列 -->
                  <td class="px-4 py-3">
                    <span :class="getTypeBadgeClass(node.type)" class="px-2 py-1 rounded text-xs font-medium">
                      {{ getNodeTypeLabel(node.type) }}
                    </span>
                  </td>

                  <!-- 数量列 -->
                  <td class="px-4 py-3 text-sm text-center">
                    {{ node.quantity || 1 }}
                  </td>

                  <!-- 描述列 -->
                  <td class="px-4 py-3 text-sm text-gray-600">
                    <div class="truncate max-w-xs" :title="node.description">
                      {{ node.description || '-' }}
                    </div>
                  </td>

                  <!-- 操作列 -->
                  <td class="px-4 py-3">
                    <div class="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        @click.stop="handleNodeEdit(node.id)"
                        class="h-7 w-7 p-0"
                        title="编辑"
                      >
                        <Edit class="w-3 h-3" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        @click.stop="handleAddChildNode(node.id)"
                        class="h-7 w-7 p-0"
                        title="添加子节点"
                      >
                        <Plus class="w-3 h-3" />
                      </Button>

                      <Button
                        v-if="node.type !== 'product'"
                        variant="ghost"
                        size="sm"
                        @click.stop="handleNodeDelete(node.id)"
                        class="h-7 w-7 p-0 text-red-600 hover:text-red-700"
                        title="删除"
                      >
                        <Trash2 class="w-3 h-3" />
                      </Button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- 空状态 -->
            <div v-if="visibleNodes.length === 0" class="text-center py-12 text-gray-500">
              <Package class="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p class="text-lg font-medium mb-2">暂无结构数据</p>
              <p class="text-sm">请先选择或创建产品结构</p>
            </div>
          </div>
        </div>



        <!-- 右侧属性面板 -->
        <div class="w-80 border-l bg-white flex flex-col min-h-0">
          <div class="p-4 border-b">
            <div class="flex items-center justify-between">
              <h3 class="font-medium">属性编辑</h3>
              <div v-if="selectedNodeIds.size > 0" class="text-sm text-gray-500">
                已选择 {{ selectedNodeIds.size }} 个节点
              </div>
            </div>
          </div>

          <div class="flex-1 overflow-y-auto p-4">
            <NodePropertyPanel
              v-if="selectedNodeInfo"
              :node="selectedNodeInfo"
              :is-multiple="selectedNodeIds.size > 1"
              @update="handleNodeUpdate"
            />
            <div v-else class="text-center py-8 text-gray-500">
              <MousePointer class="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p class="text-sm">选择节点查看和编辑属性</p>
              <p class="text-xs mt-2">支持多选进行批量编辑</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="flex items-center justify-between p-4 border-t bg-gray-50">
        <div class="flex items-center gap-4 text-sm text-gray-600">
          <!-- 状态信息 -->
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1">
              <div
                class="w-2 h-2 rounded-full"
                :class="{
                  'bg-green-500': !hasUnsavedChanges && !loading,
                  'bg-orange-500': hasUnsavedChanges,
                  'bg-blue-500': loading
                }"
              ></div>
              <span>{{ statusMessage }}</span>
            </div>

            <div v-if="autoSaveEnabled && hasUnsavedChanges" class="text-xs text-gray-500">
              自动保存已启用
            </div>
          </div>

          <!-- 历史记录信息 -->
          <div v-if="history.operations.value.length > 0" class="flex items-center gap-2">
            <span>操作: {{ history.operations.value.length }}</span>
            <Button
              variant="ghost"
              size="sm"
              @click="history.undo()"
              :disabled="!history.canUndo.value"
              class="h-6 px-2 text-xs"
              title="撤销 (Ctrl+Z)"
            >
              撤销
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click="history.redo()"
              :disabled="!history.canRedo.value"
              class="h-6 px-2 text-xs"
              title="重做 (Ctrl+Y)"
            >
              重做
            </Button>
          </div>

          <!-- 最后修改时间 -->
          <div>最后修改: {{ formatDate(structure?.updatedAt) }}</div>
        </div>

        <div class="flex items-center gap-2">
          <!-- 设置按钮 -->
          <Button
            variant="ghost"
            size="sm"
            @click="showSettingsPanel = !showSettingsPanel"
            :class="{ 'bg-gray-200': showSettingsPanel }"
            title="设置"
          >
            <Settings class="w-4 h-4" />
          </Button>

          <!-- 导入导出 -->
          <Button variant="outline" size="sm" @click="exportStructure" title="导出结构">
            <Download class="w-4 h-4 mr-1" />
            导出
          </Button>

          <!-- 主要操作按钮 -->
          <Button variant="outline" @click="handleCancel">
            取消
          </Button>
          <Button
            variant="outline"
            @click="handleValidate"
            :disabled="validating"
          >
            <CheckCircle class="w-4 h-4 mr-1" />
            {{ validating ? '验证中...' : '验证结构' }}
          </Button>
          <Button
            @click="handleSave"
            :disabled="!canSave"
            :class="{ 'animate-pulse': autoSaveEnabled && hasUnsavedChanges }"
          >
            <Save class="w-4 h-4 mr-1" />
            {{ saving ? '保存中...' : '保存更改' }}
          </Button>
        </div>
      </div>
    </DialogContent>

    <!-- 快捷键帮助对话框 -->
    <Dialog v-model:open="showShortcutsHelp">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>键盘快捷键</DialogTitle>
          <DialogDescription>
            AUTOCAD风格的快捷键设计，提高操作效率
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-6">
            <div class="space-y-3">
              <h4 class="font-medium text-gray-900">编辑操作</h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span>撤销</span>
                  <kbd class="kbd">Ctrl+Z</kbd>
                </div>
                <div class="flex justify-between">
                  <span>重做</span>
                  <kbd class="kbd">Ctrl+Y</kbd>
                </div>
                <div class="flex justify-between">
                  <span>保存</span>
                  <kbd class="kbd">Ctrl+S</kbd>
                </div>
                <div class="flex justify-between">
                  <span>删除</span>
                  <kbd class="kbd">Delete</kbd>
                </div>
              </div>
            </div>

            <div class="space-y-3">
              <h4 class="font-medium text-gray-900">选择操作</h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span>全选</span>
                  <kbd class="kbd">Ctrl+A</kbd>
                </div>
                <div class="flex justify-between">
                  <span>多选</span>
                  <kbd class="kbd">Ctrl+点击</kbd>
                </div>
                <div class="flex justify-between">
                  <span>取消选择</span>
                  <kbd class="kbd">Esc</kbd>
                </div>
                <div class="flex justify-between">
                  <span>搜索</span>
                  <kbd class="kbd">Ctrl+F</kbd>
                </div>
              </div>
            </div>
          </div>

          <div class="pt-4 border-t">
            <h4 class="font-medium text-gray-900 mb-2">鼠标操作</h4>
            <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div>• 单击选择节点</div>
              <div>• 双击编辑节点</div>
              <div>• 右键显示菜单</div>
              <div>• Ctrl+点击多选</div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Maximize2,
  Minimize2,
  MousePointer,
  CheckCircle,
  Save,
  Search,
  ChevronUp,
  ChevronDown,
  ChevronRight,
  Keyboard,
  Package,
  Layers,
  Square,
  Settings,
  Edit,
  Plus,
  Trash2,
  Download
} from 'lucide-vue-next';

import NodePropertyPanel from './NodePropertyPanel.vue';

import { useOperationHistory } from '@/composables/useOperationHistory';
import { useAutoCADShortcuts } from '@/composables/useKeyboardShortcuts';

import type { ProductStructure } from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  structure: ProductStructure | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:open': [open: boolean];
  'save': [structure: ProductStructure];
  'validate': [structure: ProductStructure];
}>();

// 响应式数据
const isFullscreen = ref(false);
const hasUnsavedChanges = ref(false);
const saving = ref(false);
const validating = ref(false);
const autoSaveEnabled = ref(true);
const showShortcutsHelp = ref(false);
const showSettingsPanel = ref(false);
const loading = ref(false);

// 搜索相关
const searchQuery = ref('');
const searchResults = ref<any[]>([]);
const currentSearchIndex = ref(-1);

// 选择和展开状态
const selectedNodeIds = ref<Set<string>>(new Set());
const expandedNodes = ref<Set<string>>(new Set());

// 节点数据
const treeNodes = ref<any[]>([]);

// 使用composables
const history = useOperationHistory();

// 键盘快捷键
useAutoCADShortcuts({
  save: () => canSave.value && handleSave(),
  undo: () => history.canUndo.value && handleUndo(),
  redo: () => history.canRedo.value && handleRedo(),
  delete: () => deleteSelectedNodes(),
  copy: () => {}, // 暂未实现
  paste: () => {}, // 暂未实现
  selectAll: () => selectAllNodes(),
  deselect: () => clearSelection(),
  zoomIn: () => {}, // 简化版不需要
  zoomOut: () => {}, // 简化版不需要
  fitView: () => {}, // 简化版不需要
  resetView: () => {}, // 简化版不需要
  validate: () => handleValidate(),
  help: () => { showShortcutsHelp.value = true; },
  newFile: () => {}, // 暂未实现
  openFile: () => {}, // 暂未实现
  selectTool: () => {}, // 暂未实现
  moveTool: () => {}, // 暂未实现
  componentTool: () => {}, // 暂未实现
  assemblyTool: () => {} // 暂未实现
});

// 计算属性

const selectedNodeInfo = computed(() => {
  const selectedIds = Array.from(selectedNodeIds.value);
  if (selectedIds.length === 0) return null;

  if (selectedIds.length === 1) {
    return treeNodes.value.find(node => node.id === selectedIds[0]);
  }

  // 多选时返回汇总信息
  return {
    id: 'multiple',
    name: `已选择 ${selectedIds.length} 个节点`,
    type: 'multiple',
    isMultiple: true,
    selectedIds
  };
});

const canSave = computed(() => {
  return hasUnsavedChanges.value && !saving.value;
});

const statusMessage = computed(() => {
  if (saving.value) return '正在保存...';
  if (validating.value) return '正在验证...';
  if (hasUnsavedChanges.value) return '有未保存的更改';
  return '已保存';
});

// 树状表格相关计算属性
const visibleNodes = computed(() => {
  if (!searchQuery.value.trim()) {
    return getVisibleTreeNodes();
  }

  // 搜索模式下显示所有匹配的节点
  const query = searchQuery.value.toLowerCase();
  return treeNodes.value.filter(node =>
    node.name.toLowerCase().includes(query) ||
    (node.code && node.code.toLowerCase().includes(query)) ||
    (node.description && node.description.toLowerCase().includes(query))
  );
});

const getVisibleTreeNodes = () => {
  const visibleNodes = [];

  const addVisibleChildren = (parentId: string | null) => {
    const children = treeNodes.value.filter(node => node.parentId === parentId);

    children.forEach(child => {
      visibleNodes.push(child);

      // 如果节点展开，递归添加子节点
      if (expandedNodes.value.has(child.id)) {
        addVisibleChildren(child.id);
      }
    });
  };

  addVisibleChildren(null);
  return visibleNodes;
};

// 方法
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.();
  } else {
    document.exitFullscreen?.();
  }
};

// 搜索功能
const performSearch = () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = [];
    currentSearchIndex.value = -1;
    return;
  }

  const query = searchQuery.value.toLowerCase();
  searchResults.value = treeNodes.value.filter(node =>
    node.name.toLowerCase().includes(query) ||
    (node.code && node.code.toLowerCase().includes(query)) ||
    (node.description && node.description.toLowerCase().includes(query))
  );

  currentSearchIndex.value = searchResults.value.length > 0 ? 0 : -1;

  // 高亮第一个结果
  if (searchResults.value.length > 0) {
    highlightSearchResult(searchResults.value[0]);
  }
};

const nextSearchResult = () => {
  if (searchResults.value.length === 0) return;

  currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length;
  highlightSearchResult(searchResults.value[currentSearchIndex.value]);
};

const previousSearchResult = () => {
  if (searchResults.value.length === 0) return;

  currentSearchIndex.value = currentSearchIndex.value <= 0
    ? searchResults.value.length - 1
    : currentSearchIndex.value - 1;
  highlightSearchResult(searchResults.value[currentSearchIndex.value]);
};

const highlightSearchResult = (node: any) => {
  // 展开到该节点
  expandToNode(node.id);
  // 选中该节点
  selectedNodeIds.value.clear();
  selectedNodeIds.value.add(node.id);
};

// 选择和展开操作
const clearSelection = () => {
  selectedNodeIds.value.clear();
};

const exportStructure = () => {
  if (!props.structure) return;

  const dataStr = JSON.stringify(props.structure, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `${props.structure.name || 'structure'}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// 表格行处理方法
const handleRowClick = (nodeId: string, event: MouseEvent) => {
  const multiSelect = event.ctrlKey || event.metaKey;
  handleNodeSelect(nodeId, multiSelect);
};

const handleRowContextMenu = (nodeId: string, event: MouseEvent) => {
  event.preventDefault();
  handleNodeContextMenu(nodeId, event.clientX, event.clientY);
};

const handleAddChildNode = (parentId: string) => {
  const parentNode = treeNodes.value.find(n => n.id === parentId);
  if (!parentNode) return;

  let nodeType = 'component';
  if (parentNode.type === 'product') {
    nodeType = 'assembly';
  } else if (parentNode.type === 'assembly') {
    nodeType = 'component';
  }

  handleNodeAdd(parentId, nodeType);
};

const hasNodeChildren = (nodeId: string) => {
  return treeNodes.value.some(node => node.parentId === nodeId);
};

const isNodeHighlighted = (node: any) => {
  if (!searchQuery.value.trim()) return false;

  const query = searchQuery.value.toLowerCase();
  return node.name.toLowerCase().includes(query) ||
         (node.code && node.code.toLowerCase().includes(query)) ||
         (node.description && node.description.toLowerCase().includes(query));
};

const getNodeIcon = (type: string) => {
  const iconMap = {
    product: Package,
    assembly: Layers,
    component: Square,
    parameter: Settings
  };
  return iconMap[type] || Package;
};

const getNodeIconColor = (type: string) => {
  return {
    product: 'text-blue-600',
    assembly: 'text-green-600',
    component: 'text-orange-600',
    parameter: 'text-purple-600'
  }[type] || 'text-gray-600';
};

const getTypeBadgeClass = (type: string) => {
  const classMap = {
    product: 'bg-blue-100 text-blue-800',
    assembly: 'bg-green-100 text-green-800',
    component: 'bg-orange-100 text-orange-800',
    parameter: 'bg-purple-100 text-purple-800'
  };
  return classMap[type] || 'bg-gray-100 text-gray-800';
};

// 数据转换和初始化
const convertTreeToStructure = (): ProductStructure => {
  if (!props.structure) throw new Error('没有原始结构数据');

  // 基于树节点数据重构产品结构
  const updatedStructure: ProductStructure = {
    ...props.structure,
    updatedAt: new Date().toISOString(),
    version: props.structure.version + 1
  };

  // 重构节点层级关系
  const rootNode = treeNodes.value.find(n => n.type === 'product');
  if (rootNode) {
    updatedStructure.name = rootNode.name;
    updatedStructure.code = rootNode.code;
    updatedStructure.description = rootNode.description;

    // 重构根构件
    const rootAssembly = treeNodes.value.find(n => n.parentId === rootNode.id && n.type === 'assembly');
    if (rootAssembly) {
      updatedStructure.rootAssembly = reconstructAssemblyFromTree(rootAssembly.id);
    }
  }

  return updatedStructure;
};

const reconstructAssemblyFromTree = (assemblyNodeId: string): any => {
  const assemblyNode = treeNodes.value.find(n => n.id === assemblyNodeId);
  if (!assemblyNode) return null;

  const childNodes = treeNodes.value.filter(n => n.parentId === assemblyNodeId);
  const assemblyNodes = childNodes.filter(n => n.type === 'assembly');
  const componentNodes = childNodes.filter(n => n.type === 'component');

  return {
    id: assemblyNode.id,
    assemblyId: assemblyNode.assemblyId || assemblyNode.id,
    assemblyCode: assemblyNode.code || '',
    assemblyName: assemblyNode.name,
    assemblyVersion: 1,
    instanceName: assemblyNode.name,
    quantity: assemblyNode.quantity || 1,
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    parameterValues: assemblyNode.parameterValues || {},
    optional: assemblyNode.optional || false,
    alternatives: [],
    properties: assemblyNode.properties || {},
    componentInstances: componentNodes.map(node => ({
      id: node.id,
      componentId: node.componentId || node.id,
      componentCode: node.code || '',
      componentName: node.name,
      instanceName: node.name,
      quantity: node.quantity || 1,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      parameterValues: node.parameterValues || {},
      optional: node.optional || false,
      alternatives: [],
      properties: node.properties || {}
    })),
    subAssemblies: assemblyNodes.map(node => reconstructAssemblyFromTree(node.id))
  };
};

const convertStructureToTree = (structure: ProductStructure) => {
  if (!structure) return;

  const nodes = [];

  // 创建根节点
  const rootNode = {
    id: structure.id,
    name: structure.name,
    code: structure.code,
    type: 'product',
    level: 0,
    parentId: null,
    description: structure.description,
    expanded: true
  };
  nodes.push(rootNode);

  // 处理根构件
  if (structure.rootAssembly) {
    processAssemblyForTree(structure.rootAssembly, structure.id, nodes, 1);
  }

  treeNodes.value = nodes;

  // 初始化展开状态
  expandedNodes.value.clear();
  expandedNodes.value.add(structure.id);
  if (structure.rootAssembly) {
    expandedNodes.value.add(structure.rootAssembly.id);
  }
};

const processAssemblyForTree = (assembly: any, parentId: string, nodes: any[], level: number) => {
  const assemblyNode = {
    id: assembly.id,
    name: assembly.assemblyName || assembly.instanceName || '构件',
    code: assembly.assemblyCode || assembly.assemblyId || '',
    type: 'assembly',
    level,
    parentId,
    description: assembly.description || '',
    quantity: assembly.quantity || 1,
    optional: assembly.optional || false,
    parameterValues: assembly.parameterValues || {},
    properties: assembly.properties || {}
  };
  nodes.push(assemblyNode);

  // 处理组件实例
  if (assembly.componentInstances) {
    assembly.componentInstances.forEach((component: any) => {
      const componentNode = {
        id: component.id,
        name: component.componentName || component.instanceName || '组件',
        code: component.componentCode || component.componentId || '',
        type: 'component',
        level: level + 1,
        parentId: assembly.id,
        description: component.description || '',
        quantity: component.quantity || 1,
        optional: component.optional || false,
        parameterValues: component.parameterValues || {},
        properties: component.properties || {}
      };
      nodes.push(componentNode);
    });
  }

  // 处理子构件
  if (assembly.subAssemblies) {
    assembly.subAssemblies.forEach((subAssembly: any) => {
      processAssemblyForTree(subAssembly, assembly.id, nodes, level + 1);
    });
  }
};

const selectAllNodes = () => {
  selectedNodeIds.value.clear();
  treeNodes.value.forEach(node => {
    selectedNodeIds.value.add(node.id);
  });
};

const expandAll = () => {
  expandedNodes.value.clear();
  treeNodes.value.forEach(node => {
    expandedNodes.value.add(node.id);
  });
};

const collapseAll = () => {
  expandedNodes.value.clear();
  // 保持根节点展开
  if (props.structure) {
    expandedNodes.value.add(props.structure.id);
  }
};

const expandToNode = (nodeId: string) => {
  const node = treeNodes.value.find(n => n.id === nodeId);
  if (!node) return;

  // 展开到该节点的路径
  let currentNode = node;
  while (currentNode && currentNode.parentId) {
    expandedNodes.value.add(currentNode.parentId);
    currentNode = treeNodes.value.find(n => n.id === currentNode.parentId);
  }
};

// 节点操作处理
const handleNodeSelect = (nodeId: string, multiSelect = false) => {
  if (multiSelect) {
    if (selectedNodeIds.value.has(nodeId)) {
      selectedNodeIds.value.delete(nodeId);
    } else {
      selectedNodeIds.value.add(nodeId);
    }
  } else {
    selectedNodeIds.value.clear();
    selectedNodeIds.value.add(nodeId);
  }

  // 展开到该节点
  if (!multiSelect) {
    expandToNode(nodeId);
  }
};

const handleNodeToggle = (nodeId: string) => {
  if (expandedNodes.value.has(nodeId)) {
    expandedNodes.value.delete(nodeId);
  } else {
    expandedNodes.value.add(nodeId);
  }
};

const handleNodeAdd = (parentId: string, nodeType: string) => {
  const parentNode = treeNodes.value.find(n => n.id === parentId);
  if (!parentNode) return;

  const newNode = createNewNode(nodeType, parentNode);
  treeNodes.value.push(newNode);

  // 记录操作
  history.recordNodeAdd(newNode, `添加${getNodeTypeLabel(nodeType)}: ${newNode.name}`);
  markUnsavedChanges();

  // 选中新节点
  selectedNodeIds.value.clear();
  selectedNodeIds.value.add(newNode.id);

  // 展开父节点
  expandedNodes.value.add(parentId);
};

const handleNodeEdit = (nodeId: string) => {
  // 选中节点进行编辑
  selectedNodeIds.value.clear();
  selectedNodeIds.value.add(nodeId);
};

const handleNodeDelete = (nodeId: string) => {
  const node = treeNodes.value.find(n => n.id === nodeId);
  if (!node) return;

  // 确认删除
  if (confirm(`确定要删除节点"${node.name}"吗？`)) {
    // 删除节点及其子节点
    const nodesToDelete = findNodeAndChildren(nodeId);

    nodesToDelete.forEach(n => {
      const index = treeNodes.value.findIndex(node => node.id === n.id);
      if (index !== -1) {
        treeNodes.value.splice(index, 1);
      }
    });

    // 记录操作
    history.recordNodeDelete(node, `删除${getNodeTypeLabel(node.type)}: ${node.name}`);
    markUnsavedChanges();

    // 清除选择
    selectedNodeIds.value.delete(nodeId);
  }
};

const deleteSelectedNodes = () => {
  const selectedIds = Array.from(selectedNodeIds.value);
  if (selectedIds.length === 0) return;

  if (confirm(`确定要删除选中的 ${selectedIds.length} 个节点吗？`)) {
    selectedIds.forEach(id => {
      const index = treeNodes.value.findIndex(n => n.id === id);
      if (index !== -1) {
        const node = treeNodes.value[index];
        history.recordNodeDelete(node, `删除${getNodeTypeLabel(node.type)}: ${node.name}`);
        treeNodes.value.splice(index, 1);
      }
    });

    selectedNodeIds.value.clear();
    markUnsavedChanges();
  }
};

const handleNodeUpdate = (nodeData: any) => {
  const nodeIndex = treeNodes.value.findIndex(n => n.id === nodeData.id);
  if (nodeIndex === -1) return;

  const oldData = { ...treeNodes.value[nodeIndex] };
  treeNodes.value[nodeIndex] = { ...nodeData };

  // 记录操作
  history.recordNodeModify(oldData, nodeData, `修改${getNodeTypeLabel(nodeData.type)}: ${nodeData.name}`);
  markUnsavedChanges();
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleNodeContextMenu = (nodeId: string, _x: number, _y: number) => {
  selectedNodeIds.value.clear();
  selectedNodeIds.value.add(nodeId);
  // 这里可以显示上下文菜单
  // TODO: 实现上下文菜单功能
};

// 历史操作
const handleUndo = () => {
  const operation = history.undo();
  if (operation) {
    applyHistoryOperation(operation, false);
    markUnsavedChanges();
  }
};

const handleRedo = () => {
  const operation = history.redo();
  if (operation) {
    applyHistoryOperation(operation, true);
    markUnsavedChanges();
  }
};

// 数据操作
const markUnsavedChanges = () => {
  hasUnsavedChanges.value = true;
};

const handleSave = async () => {
  if (!props.structure) return;

  saving.value = true;

  try {
    const updatedStructure = convertTreeToStructure();
    emit('save', updatedStructure);
    hasUnsavedChanges.value = false;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('保存失败:', error);
    alert('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

const handleValidate = async () => {
  if (!props.structure) return;

  validating.value = true;

  try {
    const updatedStructure = convertTreeToStructure();
    emit('validate', updatedStructure);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('验证失败:', error);
    alert('验证失败，请检查结构数据');
  } finally {
    validating.value = false;
  }
};

const handleCancel = () => {
  if (hasUnsavedChanges.value) {
    if (confirm('有未保存的更改，确定要关闭吗？')) {
      emit('update:open', false);
    }
  } else {
    emit('update:open', false);
  }
};

// 工具函数
const createNewNode = (nodeType: string, parentNode: any) => {
  const nodeId = `${nodeType}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  const baseNode = {
    id: nodeId,
    name: `新${getNodeTypeLabel(nodeType)}`,
    type: nodeType,
    level: parentNode.level + 1,
    parentId: parentNode.id,
    createdAt: new Date().toISOString()
  };

  // 根据类型设置特定属性
  switch (nodeType) {
    case 'assembly':
      return {
        ...baseNode,
        code: `ASM_${Date.now()}`,
        description: '',
        quantity: 1,
        optional: false
      };
    case 'component':
      return {
        ...baseNode,
        code: `COMP_${Date.now()}`,
        description: '',
        quantity: 1,
        optional: false,
        parameters: []
      };
    case 'parameter':
      return {
        ...baseNode,
        dataType: 'string',
        defaultValue: '',
        required: false
      };
    default:
      return baseNode;
  }
};

const findNodeAndChildren = (nodeId: string): any[] => {
  const result = [];
  const node = treeNodes.value.find(n => n.id === nodeId);

  if (node) {
    result.push(node);

    // 递归查找子节点
    const findChildren = (parentId: string) => {
      treeNodes.value.forEach(n => {
        if (n.parentId === parentId) {
          result.push(n);
          findChildren(n.id);
        }
      });
    };

    findChildren(nodeId);
  }

  return result;
};

const getNodeTypeLabel = (type: string): string => {
  const typeMap = {
    product: '产品',
    assembly: '构件',
    component: '组件',
    parameter: '参数'
  };
  return typeMap[type] || type;
};

const formatDate = (dateString?: string): string => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN');
};

// 历史操作应用
const applyHistoryOperation = (operation: any, isRedo: boolean) => {
  switch (operation.type) {
    case 'add':
      if (isRedo) {
        // 重新添加节点
        if (operation.data.after) {
          treeNodes.value.push(operation.data.after);
        }
      } else {
        // 移除节点
        if (operation.data.nodeIds) {
          operation.data.nodeIds.forEach((nodeId: string) => {
            const index = treeNodes.value.findIndex(n => n.id === nodeId);
            if (index !== -1) {
              treeNodes.value.splice(index, 1);
            }
          });
        }
      }
      break;

    case 'delete':
      if (isRedo) {
        // 删除节点
        if (operation.data.nodeIds) {
          operation.data.nodeIds.forEach((nodeId: string) => {
            const index = treeNodes.value.findIndex(n => n.id === nodeId);
            if (index !== -1) {
              treeNodes.value.splice(index, 1);
            }
          });
        }
      } else {
        // 恢复节点
        if (operation.data.before) {
          treeNodes.value.push(operation.data.before);
        }
      }
      break;

    case 'modify':
      if (operation.data.nodeIds && operation.data.nodeIds.length > 0) {
        const nodeId = operation.data.nodeIds[0];
        const nodeIndex = treeNodes.value.findIndex(n => n.id === nodeId);
        if (nodeIndex !== -1) {
          const targetData = isRedo ? operation.data.after : operation.data.before;
          treeNodes.value[nodeIndex] = { ...targetData };
        }
      }
      break;
  }
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange);

  // 初始化树数据
  if (props.structure) {
    convertStructureToTree(props.structure);
  }
});

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});

// 监听器
watch(() => props.structure, (newStructure) => {
  if (newStructure) {
    convertStructureToTree(newStructure);
    hasUnsavedChanges.value = false;
    history.clearHistory();

    // 重置搜索状态
    searchQuery.value = '';
    searchResults.value = [];
    currentSearchIndex.value = -1;
    selectedNodeIds.value.clear();
  }
});

watch(() => props.open, (isOpen) => {
  if (!isOpen) {
    // 弹窗关闭时的清理
    selectedNodeIds.value.clear();
    searchQuery.value = '';
    searchResults.value = [];
  }
});



// 监听搜索查询变化
watch(searchQuery, () => {
  performSearch();
});
</script>

<style scoped>
.dialog-content {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

:fullscreen .dialog-content {
  max-width: 100vw;
  width: 100vw;
  height: 100vh;
  max-height: 100vh;
}

/* 确保表格容器能够充分利用高度 */
.flex-1 {
  flex: 1;
  min-height: 0;
}

/* 表格容器样式优化 */
.table-container {
  height: 100%;
  min-height: 400px;
  max-height: calc(90vh - 200px); /* 减去头部和底部的高度 */
}

/* 全屏模式下的表格容器 */
:fullscreen .table-container {
  max-height: calc(100vh - 200px);
}

/* 确保对话框内容区域正确布局 */
.dialog-content > div {
  flex: 1;
  min-height: 0;
}

.kbd {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  font-family: monospace;
  background-color: #e5e7eb;
  color: #374151;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

/* 搜索高亮 */
.search-highlight {
  background-color: #fef3c7;
  padding: 0 2px;
  border-radius: 2px;
}

/* 状态指示器 */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-saved {
  background-color: #10b981;
}

.status-unsaved {
  background-color: #f59e0b;
}

.status-loading {
  background-color: #3b82f6;
  animation: pulse 2s infinite;
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 表格样式优化 */
.structure-tree-table table {
  border-collapse: separate;
  border-spacing: 0;
}

.structure-tree-table th {
  border-bottom: 2px solid #e5e7eb;
}

.structure-tree-table td {
  border-bottom: 1px solid #f3f4f6;
}

/* 选中行样式 */
.structure-tree-table tr.selected {
  background-color: #eff6ff;
  border-left: 4px solid #3b82f6;
}

/* 悬停效果 */
.structure-tree-table tr:hover {
  background-color: #f9fafb;
}

/* 操作按钮组 */
.action-buttons {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.structure-tree-table tr:hover .action-buttons {
  opacity: 1;
}
</style>
