import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ProductStructureVisualEditor from '../ProductStructureVisualEditor.vue';
import type { ProductStructure } from '@/types/product-structure';

// Mock ECharts
vi.mock('vue-echarts', () => ({
  default: {
    name: 'VChart',
    template: '<div class="mock-chart"></div>',
    props: ['option', 'theme', 'autoresize'],
    emits: ['click', 'contextmenu']
  }
}));

// Mock services
vi.mock('@/services/productService', () => ({
  componentService: {
    getComponents: vi.fn().mockResolvedValue([
      {
        id: 'comp1',
        code: 'FRAME001',
        name: '铝合金框架',
        componentType: 'frame',
        description: '标准铝合金窗框',
        parameters: []
      }
    ])
  }
}));

// Mock stores
vi.mock('@/stores/productStructureStore', () => ({
  useProductStructureStore: vi.fn(() => ({
    structures: [],
    loading: false
  }))
}));

// Mock composables
vi.mock('@/composables/useDragDropManager', () => ({
  useDragDropManager: vi.fn(() => ({
    dragState: { isDragging: false, dragType: null, dragData: null, dropZone: null, ghostPosition: { x: 0, y: 0 } },
    registerDropZone: vi.fn(),
    startComponentDrag: vi.fn(),
    startNodeDrag: vi.fn(),
    handleDragEnter: vi.fn(),
    handleDragOver: vi.fn(),
    handleDragLeave: vi.fn(),
    handleDrop: vi.fn(),
    calculateNodePosition: vi.fn(),
    autoLayout: vi.fn()
  }))
}));

vi.mock('@/composables/useKeyboardShortcuts', () => ({
  useAutoCADShortcuts: vi.fn()
}));

describe('ProductStructureVisualEditor', () => {
  let mockStructure: ProductStructure;

  beforeEach(() => {
    mockStructure = {
      id: 'struct1',
      code: 'WIN001',
      name: '标准窗户结构',
      description: '标准铝合金窗户产品结构',
      version: 1,
      status: 'active',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      createdBy: 'user1',
      updatedBy: 'user1',
      rootAssembly: {
        id: 'asm1',
        assemblyId: 'asm1',
        assemblyCode: 'WIN_FRAME',
        assemblyName: '窗框构件',
        assemblyVersion: 1,
        instanceName: '主窗框',
        quantity: 1,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        parameterValues: {},
        optional: false,
        alternatives: [],
        properties: {}
      },
      parameters: [],
      constraints: [],
      validationRules: [],
      metadata: {},
      tags: []
    };
  });

  it('应该正确渲染可视化编辑器', () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure
      }
    });

    expect(wrapper.find('.product-structure-visual-editor').exists()).toBe(true);
    expect(wrapper.find('.component-library-panel').exists()).toBe(true);
    expect(wrapper.find('.visual-canvas').exists()).toBe(true);
    expect(wrapper.find('.property-panel').exists()).toBe(true);
  });

  it('应该显示正确的节点和连接数量', async () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure
      }
    });

    // 等待组件初始化
    await wrapper.vm.$nextTick();

    // 检查工具栏显示的统计信息
    const toolbar = wrapper.findComponent({ name: 'VisualEditorToolbar' });
    expect(toolbar.props('nodeCount')).toBeGreaterThan(0);
  });

  it('应该响应工具切换', async () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure
      }
    });

    const toolbar = wrapper.findComponent({ name: 'VisualEditorToolbar' });
    
    // 模拟工具切换
    await toolbar.vm.$emit('tool-change', 'move');
    
    expect(wrapper.vm.activeTool).toBe('move');
  });

  it('应该响应布局变更', async () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure
      }
    });

    const toolbar = wrapper.findComponent({ name: 'VisualEditorToolbar' });
    
    // 模拟布局切换
    await toolbar.vm.$emit('layout-change', 'force');
    
    expect(wrapper.vm.layoutType).toBe('force');
  });

  it('应该正确处理保存操作', async () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure
      }
    });

    const toolbar = wrapper.findComponent({ name: 'VisualEditorToolbar' });
    
    // 模拟保存操作
    await toolbar.vm.$emit('save');
    
    // 检查是否触发了保存事件
    expect(wrapper.emitted('save')).toBeTruthy();
  });

  it('应该正确处理验证操作', async () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure
      }
    });

    const toolbar = wrapper.findComponent({ name: 'VisualEditorToolbar' });
    
    // 模拟验证操作
    await toolbar.vm.$emit('validate');
    
    // 检查是否触发了验证事件
    expect(wrapper.emitted('validate')).toBeTruthy();
  });

  it('应该支持撤销和重做操作', async () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure
      }
    });

    // 初始状态应该不能撤销和重做
    expect(wrapper.vm.canUndo).toBe(false);
    expect(wrapper.vm.canRedo).toBe(false);

    // 模拟添加操作历史
    wrapper.vm.recordOperation('add_node', { id: 'test', name: 'Test Node' });
    
    // 现在应该可以撤销
    expect(wrapper.vm.canUndo).toBe(true);
    expect(wrapper.vm.canRedo).toBe(false);

    // 执行撤销
    wrapper.vm.undo();
    
    // 现在应该可以重做
    expect(wrapper.vm.canUndo).toBe(false);
    expect(wrapper.vm.canRedo).toBe(true);
  });

  it('应该正确转换产品结构为可视化数据', async () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure
      }
    });

    // 等待数据转换完成
    await wrapper.vm.$nextTick();

    // 检查是否生成了可视化节点
    expect(wrapper.vm.visualNodes.length).toBeGreaterThan(0);
    
    // 检查根节点
    const rootNode = wrapper.vm.visualNodes.find(n => n.type === 'product');
    expect(rootNode).toBeDefined();
    expect(rootNode.name).toBe(mockStructure.name);
  });

  it('应该支持只读模式', () => {
    const wrapper = mount(ProductStructureVisualEditor, {
      props: {
        structure: mockStructure,
        readonly: true
      }
    });

    // 在只读模式下，某些操作应该被禁用
    const toolbar = wrapper.findComponent({ name: 'VisualEditorToolbar' });
    expect(toolbar.exists()).toBe(true);
    
    // 属性面板应该是只读的
    const propertyEditor = wrapper.findComponent({ name: 'NodePropertyEditor' });
    if (propertyEditor.exists()) {
      expect(propertyEditor.props('readonly')).toBe(true);
    }
  });
});
