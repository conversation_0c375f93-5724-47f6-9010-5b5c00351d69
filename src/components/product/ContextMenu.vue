<template>
  <div
    v-if="open"
    ref="menuRef"
    class="context-menu fixed z-50 bg-white border rounded-lg shadow-lg py-1 min-w-48"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
    @click.stop
  >
    <div
      v-for="item in items"
      :key="item.id"
      class="context-menu-item px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 flex items-center gap-2 transition-colors"
      :class="{ 'text-red-600 hover:bg-red-50': item.danger }"
      @click="handleItemClick(item.id)"
    >
      <component :is="getIcon(item.icon)" class="w-4 h-4" />
      <span>{{ item.label }}</span>
    </div>
  </div>
  
  <!-- 背景遮罩 -->
  <div
    v-if="open"
    class="fixed inset-0 z-40"
    @click="$emit('update:open', false)"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import {
  Edit,
  Copy,
  Trash2,
  Plus,
  Layers,
  Settings,
  Eye,
  EyeOff,
  Lock,
  Unlock
} from 'lucide-vue-next';

// Props
interface ContextMenuItem {
  id: string;
  label: string;
  icon: string;
  danger?: boolean;
  disabled?: boolean;
}

interface Props {
  open: boolean;
  position: { x: number; y: number };
  items: ContextMenuItem[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:open': [open: boolean];
  'item-click': [itemId: string];
}>();

// 响应式数据
const menuRef = ref<HTMLElement>();

// 方法
const getIcon = (iconName: string) => {
  const iconMap = {
    Edit,
    Copy,
    Trash2,
    Plus,
    Layers,
    Settings,
    Eye,
    EyeOff,
    Lock,
    Unlock
  };
  return iconMap[iconName] || Settings;
};

const handleItemClick = (itemId: string) => {
  emit('item-click', itemId);
  emit('update:open', false);
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    emit('update:open', false);
  }
};

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.context-menu {
  animation: contextMenuFadeIn 0.15s ease-out;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.context-menu-item:active {
  transform: scale(0.98);
}
</style>
