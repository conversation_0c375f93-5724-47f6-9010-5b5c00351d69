<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">约束条件</h3>
        <p class="text-sm text-gray-600">定义组件参数之间的约束关系</p>
      </div>
      <Button @click="addConstraint" class="bg-blue-600 hover:bg-blue-700">
        <Plus class="w-4 h-4 mr-2" />
        添加约束
      </Button>
    </div>

    <!-- 约束列表 -->
    <div v-if="localConstraints.length > 0" class="space-y-4">
      <div
        v-for="(constraint, index) in localConstraints"
        :key="constraint.id || index"
        class="border rounded-lg p-4 space-y-4"
      >
        <!-- 约束头部 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <Badge :variant="getConstraintTypeVariant(constraint.type)">
              {{ getConstraintTypeText(constraint.type) }}
            </Badge>
            <Badge :variant="getSeverityVariant(constraint.severity)">
              {{ getSeverityText(constraint.severity) }}
            </Badge>
            <Badge v-if="!constraint.enabled" variant="outline" class="text-xs">
              已禁用
            </Badge>
          </div>
          <div class="flex items-center gap-2">
            <Button @click="testConstraint(index)" variant="outline" size="sm">
              <Play class="w-4 h-4" />
            </Button>
            <Button @click="duplicateConstraint(index)" variant="ghost" size="sm">
              <Copy class="w-4 h-4" />
            </Button>
            <Button @click="removeConstraint(index)" variant="ghost" size="sm" class="text-red-600">
              <Trash2 class="w-4 h-4" />
            </Button>
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label :for="`constraint-name-${index}`" class="required">约束名称</Label>
            <Input
              :id="`constraint-name-${index}`"
              v-model="constraint.name"
              placeholder="约束名称"
              :class="{ 'border-red-500': getConstraintError(index, 'name') }"
              @blur="validateConstraint(index)"
            />
            <p v-if="getConstraintError(index, 'name')" class="text-sm text-red-600">
              {{ getConstraintError(index, 'name') }}
            </p>
          </div>
          
          <div class="space-y-2">
            <Label :for="`constraint-type-${index}`" class="required">约束类型</Label>
            <Select v-model="constraint.type">
              <SelectTrigger :id="`constraint-type-${index}`">
                <SelectValue placeholder="选择约束类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dimension">尺寸约束</SelectItem>
                <SelectItem value="material">材料约束</SelectItem>
                <SelectItem value="process">工艺约束</SelectItem>
                <SelectItem value="compatibility">兼容性约束</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 约束表达式 -->
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <Label :for="`constraint-expression-${index}`" class="required">约束表达式</Label>
            <div class="flex items-center gap-2">
              <Button @click="showExpressionHelper(index)" variant="outline" size="sm">
                <HelpCircle class="w-4 h-4 mr-1" />
                帮助
              </Button>
              <Button @click="validateExpression(index)" variant="outline" size="sm">
                <CheckCircle class="w-4 h-4 mr-1" />
                验证
              </Button>
            </div>
          </div>
          <Textarea
            :id="`constraint-expression-${index}`"
            v-model="constraint.expression"
            placeholder="如: width / height >= 0.5 && width / height <= 3.0"
            rows="3"
            class="font-mono"
            :class="{ 'border-red-500': getConstraintError(index, 'expression') }"
            @blur="validateConstraint(index)"
          />
          <p v-if="getConstraintError(index, 'expression')" class="text-sm text-red-600">
            {{ getConstraintError(index, 'expression') }}
          </p>
          
          <!-- 可用参数提示 -->
          <div v-if="availableParameters.length > 0" class="text-sm text-gray-600">
            <p class="mb-1">可用参数:</p>
            <div class="flex flex-wrap gap-1">
              <Badge
                v-for="param in availableParameters"
                :key="param.name"
                variant="outline"
                class="text-xs cursor-pointer"
                @click="insertParameter(index, param.name)"
              >
                {{ param.name }} ({{ param.displayName }})
              </Badge>
            </div>
          </div>
        </div>

        <!-- 错误信息和严重程度 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label :for="`constraint-error-message-${index}`" class="required">错误信息</Label>
            <Textarea
              :id="`constraint-error-message-${index}`"
              v-model="constraint.errorMessage"
              placeholder="约束不满足时显示的错误信息"
              rows="2"
              :class="{ 'border-red-500': getConstraintError(index, 'errorMessage') }"
            />
            <p v-if="getConstraintError(index, 'errorMessage')" class="text-sm text-red-600">
              {{ getConstraintError(index, 'errorMessage') }}
            </p>
          </div>
          
          <div class="space-y-2">
            <Label :for="`constraint-severity-${index}`">严重程度</Label>
            <Select v-model="constraint.severity">
              <SelectTrigger :id="`constraint-severity-${index}`">
                <SelectValue placeholder="选择严重程度" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="error">错误 - 阻止保存</SelectItem>
                <SelectItem value="warning">警告 - 允许保存但提示</SelectItem>
                <SelectItem value="info">信息 - 仅提示</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 约束描述 -->
        <div class="space-y-2">
          <Label :for="`constraint-description-${index}`">约束描述</Label>
          <Textarea
            :id="`constraint-description-${index}`"
            v-model="constraint.description"
            placeholder="详细描述约束的目的和作用"
            rows="2"
          />
        </div>

        <!-- 自动修复配置 -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2">
            <Checkbox
              :id="`constraint-auto-fix-${index}`"
              v-model:checked="constraint.autoFix.enabled"
            />
            <Label :for="`constraint-auto-fix-${index}`">启用自动修复</Label>
          </div>
          
          <div v-if="constraint.autoFix?.enabled" class="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6">
            <div class="space-y-2">
              <Label :for="`constraint-fix-message-${index}`">修复提示</Label>
              <Input
                :id="`constraint-fix-message-${index}`"
                v-model="constraint.autoFix.fixMessage"
                placeholder="自动修复的提示信息"
              />
            </div>
            
            <div class="space-y-2">
              <Label :for="`constraint-fix-expression-${index}`">修复表达式</Label>
              <Input
                :id="`constraint-fix-expression-${index}`"
                v-model="constraint.autoFix.fixExpression"
                placeholder="如: height = width / 2.0"
                class="font-mono"
              />
            </div>
          </div>
        </div>

        <!-- 约束状态 -->
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <Checkbox
              :id="`constraint-enabled-${index}`"
              v-model:checked="constraint.enabled"
            />
            <Label :for="`constraint-enabled-${index}`">启用约束</Label>
          </div>
        </div>

        <!-- 测试结果 -->
        <div v-if="testResults[index]" class="mt-4 p-3 rounded-lg" :class="testResults[index].success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
          <div class="flex items-center gap-2 mb-2">
            <CheckCircle v-if="testResults[index].success" class="w-4 h-4 text-green-600" />
            <XCircle v-else class="w-4 h-4 text-red-600" />
            <span class="text-sm font-medium" :class="testResults[index].success ? 'text-green-800' : 'text-red-800'">
              {{ testResults[index].success ? '约束表达式有效' : '约束表达式无效' }}
            </span>
          </div>
          <p class="text-sm" :class="testResults[index].success ? 'text-green-700' : 'text-red-700'">
            {{ testResults[index].message }}
          </p>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-12">
      <Shield class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <p class="text-gray-600 mb-4">暂无约束条件</p>
      <Button @click="addConstraint" class="bg-blue-600 hover:bg-blue-700">
        <Plus class="w-4 h-4 mr-2" />
        添加第一个约束
      </Button>
    </div>

    <!-- 表达式帮助对话框 -->
    <ExpressionHelperDialog
      v-model:open="helperDialogOpen"
      :parameters="availableParameters"
      @insert="insertExpressionHelper"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { 
  Plus, Copy, Trash2, Shield, Play, HelpCircle, CheckCircle, XCircle
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import ExpressionHelperDialog from './ExpressionHelperDialog.vue';
import type { ComponentConstraint, ComponentParameter, ConstraintType, SeverityLevel } from '@/types/product-structure';

// Props
interface Props {
  modelValue: ComponentConstraint[];
  parameters: ComponentParameter[];
  errors: any[];
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: ComponentConstraint[]): void;
  (e: 'validate', errors: any[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localConstraints = ref<ComponentConstraint[]>([...props.modelValue]);
const constraintErrors = ref<Record<number, Record<string, string>>>({});
const testResults = ref<Record<number, { success: boolean; message: string }>>({});
const helperDialogOpen = ref(false);
const currentConstraintIndex = ref(-1);

// 计算属性
const availableParameters = computed(() => props.parameters || []);

// 方法
const addConstraint = () => {
  const newConstraint: ComponentConstraint = {
    id: `constraint-${Date.now()}`,
    name: '',
    type: 'dimension',
    expression: '',
    errorMessage: '',
    severity: 'error',
    enabled: true,
    description: '',
    autoFix: {
      enabled: false,
      fixMessage: '',
      fixExpression: ''
    }
  };
  
  localConstraints.value.push(newConstraint);
};

const removeConstraint = (index: number) => {
  localConstraints.value.splice(index, 1);
  delete constraintErrors.value[index];
  delete testResults.value[index];
  
  // 重新索引错误和测试结果
  const newErrors: Record<number, Record<string, string>> = {};
  const newTestResults: Record<number, { success: boolean; message: string }> = {};
  
  Object.entries(constraintErrors.value).forEach(([key, value]) => {
    const numKey = parseInt(key);
    if (numKey > index) {
      newErrors[numKey - 1] = value;
    } else if (numKey < index) {
      newErrors[numKey] = value;
    }
  });
  
  Object.entries(testResults.value).forEach(([key, value]) => {
    const numKey = parseInt(key);
    if (numKey > index) {
      newTestResults[numKey - 1] = value;
    } else if (numKey < index) {
      newTestResults[numKey] = value;
    }
  });
  
  constraintErrors.value = newErrors;
  testResults.value = newTestResults;
};

const duplicateConstraint = (index: number) => {
  const original = localConstraints.value[index];
  const duplicated: ComponentConstraint = {
    ...original,
    id: `constraint-${Date.now()}`,
    name: `${original.name}_copy`
  };
  
  localConstraints.value.splice(index + 1, 0, duplicated);
};

const validateConstraint = (index: number) => {
  const constraint = localConstraints.value[index];
  const errors: Record<string, string> = {};
  
  if (!constraint.name?.trim()) {
    errors.name = '约束名称不能为空';
  }
  
  if (!constraint.expression?.trim()) {
    errors.expression = '约束表达式不能为空';
  }
  
  if (!constraint.errorMessage?.trim()) {
    errors.errorMessage = '错误信息不能为空';
  }
  
  constraintErrors.value[index] = errors;
  
  // 发出验证事件
  const allErrors = Object.values(constraintErrors.value).flat();
  emit('validate', allErrors);
};

const validateExpression = (index: number) => {
  const constraint = localConstraints.value[index];
  
  try {
    // 简单的表达式语法检查
    const expression = constraint.expression;
    
    // 检查基本语法
    if (!expression.trim()) {
      testResults.value[index] = {
        success: false,
        message: '表达式不能为空'
      };
      return;
    }
    
    // 检查括号匹配
    let openParens = 0;
    for (const char of expression) {
      if (char === '(') openParens++;
      if (char === ')') openParens--;
      if (openParens < 0) break;
    }
    
    if (openParens !== 0) {
      testResults.value[index] = {
        success: false,
        message: '括号不匹配'
      };
      return;
    }
    
    // 检查参数引用
    const paramRefs = expression.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];
    const availableParamNames = availableParameters.value.map(p => p.name);
    const invalidRefs = paramRefs.filter(ref => 
      !availableParamNames.includes(ref) && 
      !['sin', 'cos', 'tan', 'log', 'exp', 'sqrt', 'abs', 'min', 'max', 'PI', 'E'].includes(ref)
    );
    
    if (invalidRefs.length > 0) {
      testResults.value[index] = {
        success: false,
        message: `未定义的参数引用: ${invalidRefs.join(', ')}`
      };
      return;
    }
    
    testResults.value[index] = {
      success: true,
      message: '表达式语法正确'
    };
    
  } catch (error) {
    testResults.value[index] = {
      success: false,
      message: `表达式解析错误: ${error.message}`
    };
  }
};

const testConstraint = (index: number) => {
  validateExpression(index);
  
  // 这里可以添加更复杂的测试逻辑，比如使用示例数据测试约束
  const constraint = localConstraints.value[index];
  if (testResults.value[index]?.success) {
    // 使用参数默认值进行测试
    const testValues: Record<string, any> = {};
    availableParameters.value.forEach(param => {
      if (param.defaultValue !== undefined) {
        testValues[param.name] = param.defaultValue;
      } else {
        // 为不同类型提供测试值
        switch (param.type) {
          case 'number':
            testValues[param.name] = param.minValue || 1;
            break;
          case 'string':
            testValues[param.name] = 'test';
            break;
          case 'boolean':
            testValues[param.name] = true;
            break;
          default:
            testValues[param.name] = 'test';
        }
      }
    });
    
    // 这里可以调用约束求解服务进行实际测试
    console.log('测试约束:', constraint.expression, '使用值:', testValues);
  }
};

const showExpressionHelper = (index: number) => {
  currentConstraintIndex.value = index;
  helperDialogOpen.value = true;
};

const insertParameter = (constraintIndex: number, parameterName: string) => {
  const constraint = localConstraints.value[constraintIndex];
  const textarea = document.getElementById(`constraint-expression-${constraintIndex}`) as HTMLTextAreaElement;
  
  if (textarea) {
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = constraint.expression;
    
    constraint.expression = text.substring(0, start) + parameterName + text.substring(end);
    
    // 重新设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + parameterName.length, start + parameterName.length);
    }, 0);
  }
};

const insertExpressionHelper = (expression: string) => {
  if (currentConstraintIndex.value >= 0) {
    const constraint = localConstraints.value[currentConstraintIndex.value];
    constraint.expression = expression;
  }
};

const getConstraintError = (index: number, field: string) => {
  return constraintErrors.value[index]?.[field];
};

const getConstraintTypeText = (type: ConstraintType) => {
  const textMap = {
    dimension: '尺寸',
    material: '材料',
    process: '工艺',
    compatibility: '兼容性'
  };
  return textMap[type] || type;
};

const getConstraintTypeVariant = (type: ConstraintType) => {
  const variantMap = {
    dimension: 'default' as const,
    material: 'secondary' as const,
    process: 'outline' as const,
    compatibility: 'destructive' as const
  };
  return variantMap[type] || 'secondary';
};

const getSeverityText = (severity: SeverityLevel) => {
  const textMap = {
    error: '错误',
    warning: '警告',
    info: '信息'
  };
  return textMap[severity] || severity;
};

const getSeverityVariant = (severity: SeverityLevel) => {
  const variantMap = {
    error: 'destructive' as const,
    warning: 'secondary' as const,
    info: 'outline' as const
  };
  return variantMap[severity] || 'outline';
};

// 防止循环更新的标志
const isUpdating = ref(false);

// 监听器
watch(localConstraints, (newValue) => {
  if (isUpdating.value) return;
  emit('update:modelValue', newValue);
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  if (isUpdating.value) return;

  isUpdating.value = true;
  localConstraints.value = [...newValue];

  nextTick(() => {
    isUpdating.value = false;
  });
}, { deep: true });
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
