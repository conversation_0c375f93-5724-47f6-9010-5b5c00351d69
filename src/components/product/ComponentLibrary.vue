<template>
  <div class="component-library">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">组件库管理</h1>
        <p class="text-gray-600 mt-1">管理产品结构中的组件定义</p>
      </div>
      <div class="flex items-center gap-3">
        <Button @click="showStatistics = !showStatistics" variant="outline">
          <BarChart3 class="w-4 h-4 mr-2" />
          统计信息
        </Button>
        <Button @click="openCreateDialog" class="bg-blue-600 hover:bg-blue-700">
          <Plus class="w-4 h-4 mr-2" />
          新建组件
        </Button>
      </div>
    </div>

    <!-- 统计信息面板 -->
    <div v-if="showStatistics" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <Package class="w-6 h-6 text-blue-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">总组件数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.total }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <CheckCircle class="w-6 h-6 text-green-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">活跃组件</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.byStatus.active || 0 }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <Clock class="w-6 h-6 text-yellow-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">最近创建</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.recentlyCreated }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <Edit class="w-6 h-6 text-purple-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">最近更新</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.recentlyUpdated }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 筛选和搜索 -->
    <ComponentFilters
      v-model:filters="filters"
      v-model:sort="sortOptions"
      @filter="handleFilter"
      @sort="handleSort"
    />

    <!-- 批量操作工具栏 -->
    <div v-if="selectedComponents.length > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <span class="text-sm text-blue-700">已选择 {{ selectedComponents.length }} 个组件</span>
        </div>
        <div class="flex items-center gap-2">
          <Button @click="batchUpdateStatus" variant="outline" size="sm">
            <Settings class="w-4 h-4 mr-1" />
            批量状态
          </Button>
          <Button @click="batchAddTags" variant="outline" size="sm">
            <Tag class="w-4 h-4 mr-1" />
            添加标签
          </Button>
          <Button @click="batchDelete" variant="destructive" size="sm">
            <Trash2 class="w-4 h-4 mr-1" />
            批量删除
          </Button>
          <Button @click="clearSelection" variant="ghost" size="sm">
            清除选择
          </Button>
        </div>
      </div>
    </div>

    <!-- 组件列表 -->
    <div class="bg-white rounded-lg border">
      <!-- 列表头部 -->
      <div class="flex items-center justify-between p-4 border-b">
        <div class="flex items-center gap-4">
          <Checkbox
            :checked="isAllSelected"
            @update:checked="toggleSelectAll"
          />
          <span class="text-sm text-gray-600">
            显示 {{ paginatedComponents.length }} / {{ totalComponents }} 个组件
          </span>
        </div>
        <div class="flex items-center gap-2">
          <Button @click="toggleViewMode" variant="outline" size="sm">
            <Grid3X3 v-if="viewMode === 'list'" class="w-4 h-4" />
            <List v-else class="w-4 h-4" />
          </Button>
        </div>
      </div>

      <!-- 组件内容 -->
      <div v-if="loading" class="p-8 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-gray-600 mt-2">加载中...</p>
      </div>

      <div v-else-if="paginatedComponents.length === 0" class="p-8 text-center">
        <Package class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p class="text-gray-600">暂无组件数据</p>
        <Button @click="openCreateDialog" class="mt-4">
          <Plus class="w-4 h-4 mr-2" />
          创建第一个组件
        </Button>
      </div>

      <div v-else>
        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="divide-y">
          <ComponentListItem
            v-for="component in paginatedComponents"
            :key="component.id"
            :component="component"
            :selected="selectedComponents.includes(component.id)"
            @select="toggleComponentSelection"
            @edit="editComponent"
            @duplicate="duplicateComponent"
            @delete="deleteComponent"
          />
        </div>

        <!-- 卡片视图 -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
          <ComponentCard
            v-for="component in paginatedComponents"
            :key="component.id"
            :component="component"
            :selected="selectedComponents.includes(component.id)"
            @select="toggleComponentSelection"
            @edit="editComponent"
            @duplicate="duplicateComponent"
            @delete="deleteComponent"
          />
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="flex items-center justify-between p-4 border-t">
        <div class="text-sm text-gray-600">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </div>
        <div class="flex items-center gap-2">
          <Button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage <= 1"
            variant="outline"
            size="sm"
          >
            <ChevronLeft class="w-4 h-4" />
          </Button>
          <Button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage >= totalPages"
            variant="outline"
            size="sm"
          >
            <ChevronRight class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 组件编辑对话框 -->
    <ComponentEditor
      v-model:open="editorOpen"
      :component="currentComponent"
      @save="handleSave"
    />

    <!-- 批量操作对话框 -->
    <BatchOperationDialog
      v-model:open="batchDialogOpen"
      :operation-type="batchOperationType"
      :selected-count="selectedComponents.length"
      @confirm="handleBatchOperation"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { 
  Plus, Package, BarChart3, CheckCircle, Clock, Edit, Settings, Tag, Trash2,
  Grid3X3, List, ChevronLeft, ChevronRight
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';

import ComponentFilters from './ComponentFilters.vue';
import ComponentListItem from './ComponentListItem.vue';
import ComponentCard from './ComponentCard.vue';
import ComponentEditor from './ComponentEditor.vue';
import BatchOperationDialog from './BatchOperationDialog.vue';

import { componentService } from '@/services/componentService';
import type { 
  Component, 
  ComponentFilters as IComponentFilters,
  ComponentSortOptions,
  ComponentStatistics,
  BatchOperationType
} from '@/services/componentService';

// 响应式数据
const loading = ref(false);
const showStatistics = ref(false);
const viewMode = ref<'list' | 'grid'>('list');
const editorOpen = ref(false);
const batchDialogOpen = ref(false);

const components = ref<Component[]>([]);
const statistics = ref<ComponentStatistics>({
  total: 0,
  byType: { frame: 0, glass: 0, hardware: 0, seal: 0, other: 0 },
  byStatus: {},
  recentlyCreated: 0,
  recentlyUpdated: 0
});

const filters = ref<IComponentFilters>({});
const sortOptions = ref<ComponentSortOptions>({
  field: 'updatedAt',
  direction: 'desc'
});

const selectedComponents = ref<string[]>([]);
const currentComponent = ref<Component | null>(null);
const batchOperationType = ref<BatchOperationType>('delete');

// 分页
const currentPage = ref(1);
const pageSize = ref(20);
const totalComponents = ref(0);

// 计算属性
const paginatedComponents = computed(() => components.value);
const totalPages = computed(() => Math.ceil(totalComponents.value / pageSize.value));
const isAllSelected = computed(() => 
  components.value.length > 0 && selectedComponents.value.length === components.value.length
);

// 方法
const loadComponents = async () => {
  loading.value = true;
  try {
    const result = await componentService.getComponents(
      filters.value,
      sortOptions.value,
      { page: currentPage.value, pageSize: pageSize.value }
    );
    
    components.value = result.components;
    totalComponents.value = result.total;
  } catch (error) {
    console.error('加载组件失败:', error);
  } finally {
    loading.value = false;
  }
};

const loadStatistics = async () => {
  try {
    statistics.value = await componentService.getStatistics();
  } catch (error) {
    console.error('加载统计信息失败:', error);
  }
};

const handleFilter = () => {
  currentPage.value = 1;
  loadComponents();
};

const handleSort = () => {
  loadComponents();
};

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'list' ? 'grid' : 'list';
};

const toggleComponentSelection = (componentId: string) => {
  const index = selectedComponents.value.indexOf(componentId);
  if (index > -1) {
    selectedComponents.value.splice(index, 1);
  } else {
    selectedComponents.value.push(componentId);
  }
};

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedComponents.value = [];
  } else {
    selectedComponents.value = components.value.map(c => c.id);
  }
};

const clearSelection = () => {
  selectedComponents.value = [];
};

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    loadComponents();
  }
};

const openCreateDialog = () => {
  currentComponent.value = null;
  editorOpen.value = true;
};

const editComponent = (component: Component) => {
  currentComponent.value = component;
  editorOpen.value = true;
};

const duplicateComponent = async (component: Component) => {
  try {
    await componentService.duplicateComponent(component.id);
    await loadComponents();
    await loadStatistics();
  } catch (error) {
    console.error('复制组件失败:', error);
  }
};

const deleteComponent = async (component: Component) => {
  if (confirm(`确定要删除组件 "${component.name}" 吗？`)) {
    try {
      await componentService.deleteComponent(component.id);
      await loadComponents();
      await loadStatistics();
    } catch (error) {
      console.error('删除组件失败:', error);
    }
  }
};

const handleSave = async (component: Component) => {
  try {
    if (component.id && components.value.find(c => c.id === component.id)) {
      await componentService.updateComponent(component.id, component);
    } else {
      await componentService.createComponent(component);
    }
    
    await loadComponents();
    await loadStatistics();
    editorOpen.value = false;
  } catch (error) {
    console.error('保存组件失败:', error);
  }
};

const batchUpdateStatus = () => {
  batchOperationType.value = 'updateStatus';
  batchDialogOpen.value = true;
};

const batchAddTags = () => {
  batchOperationType.value = 'addTags';
  batchDialogOpen.value = true;
};

const batchDelete = () => {
  batchOperationType.value = 'delete';
  batchDialogOpen.value = true;
};

const handleBatchOperation = async (params: any) => {
  try {
    await componentService.batchOperation({
      type: batchOperationType.value,
      componentIds: selectedComponents.value,
      params
    });
    
    selectedComponents.value = [];
    await loadComponents();
    await loadStatistics();
    batchDialogOpen.value = false;
  } catch (error) {
    console.error('批量操作失败:', error);
  }
};

// 生命周期
onMounted(() => {
  loadComponents();
  loadStatistics();
});

// 监听筛选条件变化
watch([filters, sortOptions], () => {
  currentPage.value = 1;
  loadComponents();
}, { deep: true });
</script>
