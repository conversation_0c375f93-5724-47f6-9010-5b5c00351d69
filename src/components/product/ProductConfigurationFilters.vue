<template>
  <div class="product-configuration-filters">
    <!-- 搜索栏 -->
    <div class="flex flex-col lg:flex-row gap-4 mb-4">
      <div class="flex-1">
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            v-model="searchValue"
            placeholder="搜索产品名称、描述或标签..."
            class="pl-10"
            @keyup.enter="$emit('search')"
          />
        </div>
      </div>
      <div class="flex gap-2">
        <Button @click="$emit('search')" class="px-6">
          <Search class="w-4 h-4 mr-2" />
          搜索
        </Button>
        <Button variant="outline" @click="$emit('reset')">
          <RotateCcw class="w-4 h-4 mr-2" />
          重置
        </Button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- 状态筛选 -->
      <div>
        <Label class="text-sm font-medium mb-2 block">状态</Label>
        <Select v-model="filtersValue.status" multiple>
          <SelectTrigger>
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">激活</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="deprecated">已弃用</SelectItem>
            <SelectItem value="archived">已归档</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 结构模板筛选 -->
      <div>
        <Label class="text-sm font-medium mb-2 block">结构模板</Label>
        <Select v-model="filtersValue.structureTemplate" multiple>
          <SelectTrigger>
            <SelectValue placeholder="选择结构模板" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem 
              v-for="template in structureTemplates" 
              :key="template.id" 
              :value="template.name"
            >
              {{ template.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>     
 <!-- 分类筛选 -->
      <div>
        <Label class="text-sm font-medium mb-2 block">产品分类</Label>
        <Select v-model="filtersValue.category" multiple>
          <SelectTrigger>
            <SelectValue placeholder="选择分类" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="中空玻璃">中空玻璃</SelectItem>
            <SelectItem value="夹胶玻璃">夹胶玻璃</SelectItem>
            <SelectItem value="钢化玻璃">钢化玻璃</SelectItem>
            <SelectItem value="单片玻璃">单片玻璃</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 标签筛选 -->
      <div>
        <Label class="text-sm font-medium mb-2 block">标签</Label>
        <Select v-model="filtersValue.tags" multiple>
          <SelectTrigger>
            <SelectValue placeholder="选择标签" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="常用">常用</SelectItem>
            <SelectItem value="节能">节能</SelectItem>
            <SelectItem value="安全">安全</SelectItem>
            <SelectItem value="高端">高端</SelectItem>
            <SelectItem value="经济">经济</SelectItem>
            <SelectItem value="Low-E">Low-E</SelectItem>
            <SelectItem value="钢化">钢化</SelectItem>
            <SelectItem value="防爆">防爆</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Search, RotateCcw } from 'lucide-vue-next';

import type { ProductSearchFilters, ProductStructureTemplate } from '@/types/product';

// Props
interface Props {
  filters: ProductSearchFilters;
  search: string;
  structureTemplates: ProductStructureTemplate[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:filters': [filters: ProductSearchFilters];
  'update:search': [search: string];
  'search': [];
  'filter': [];
  'reset': [];
}>();

// 计算属性
const searchValue = computed({
  get: () => props.search,
  set: (value: string) => emit('update:search', value)
});

const filtersValue = computed({
  get: () => props.filters,
  set: (value: ProductSearchFilters) => {
    emit('update:filters', value);
    emit('filter');
  }
});
</script>

<style scoped>
.product-configuration-filters {
  @apply space-y-4;
}
</style>