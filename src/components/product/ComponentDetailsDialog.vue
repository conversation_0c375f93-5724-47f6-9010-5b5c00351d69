<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-3">
          <div 
            class="w-8 h-8 rounded-lg flex items-center justify-center"
            :class="getTypeIconClass(component.componentType)"
          >
            <component :is="getTypeIcon(component.componentType)" class="w-4 h-4" />
          </div>
          {{ component.name }}
          <Badge :variant="getStatusVariant(component.status)">
            {{ getStatusText(component.status) }}
          </Badge>
        </DialogTitle>
        <DialogDescription>
          {{ component.description || '暂无描述' }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- 基本信息 -->
        <div>
          <h3 class="text-lg font-semibold mb-3">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium text-gray-600">组件编码</Label>
              <p class="text-sm text-gray-900 mt-1">{{ component.code }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-600">组件类型</Label>
              <p class="text-sm text-gray-900 mt-1">{{ getTypeText(component.componentType) }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-600">物料分类</Label>
              <p class="text-sm text-gray-900 mt-1">
                {{ component.materialCategoryName || '未分类' }}
                <span v-if="component.materialCategoryCode" class="text-gray-500">
                  ({{ component.materialCategoryCode }})
                </span>
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-600">版本</Label>
              <p class="text-sm text-gray-900 mt-1">v{{ component.version }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-600">可重用性</Label>
              <p class="text-sm text-gray-900 mt-1">
                {{ component.reusable ? '可重用' : '不可重用' }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-600">创建时间</Label>
              <p class="text-sm text-gray-900 mt-1">
                {{ formatDateTime(component.createdAt) }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-600">更新时间</Label>
              <p class="text-sm text-gray-900 mt-1">
                {{ formatDateTime(component.updatedAt) }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-600">创建者</Label>
              <p class="text-sm text-gray-900 mt-1">{{ component.createdBy }}</p>
            </div>
          </div>
        </div>

        <!-- 标签 -->
        <div v-if="component.tags.length > 0">
          <h3 class="text-lg font-semibold mb-3">标签</h3>
          <div class="flex flex-wrap gap-2">
            <Badge
              v-for="tag in component.tags"
              :key="tag"
              variant="secondary"
            >
              {{ tag }}
            </Badge>
          </div>
        </div>

        <!-- 计算公式 -->
        <div>
          <h3 class="text-lg font-semibold mb-3">计算公式</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium text-gray-600">数量公式</Label>
              <div class="mt-1 p-3 bg-gray-50 rounded-lg font-mono text-sm">
                {{ component.quantityFormula }}
              </div>
            </div>
            <div v-if="component.costFormula">
              <Label class="text-sm font-medium text-gray-600">成本公式</Label>
              <div class="mt-1 p-3 bg-gray-50 rounded-lg font-mono text-sm">
                {{ component.costFormula }}
              </div>
            </div>
          </div>
        </div>

        <!-- 参数定义 -->
        <div v-if="component.parameters.length > 0">
          <h3 class="text-lg font-semibold mb-3">参数定义 ({{ component.parameters.length }})</h3>
          <div class="space-y-3">
            <div
              v-for="parameter in component.parameters"
              :key="parameter.id"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                  <h4 class="font-medium">{{ parameter.displayName }}</h4>
                  <Badge variant="outline" class="text-xs">
                    {{ getParameterTypeText(parameter.type) }}
                  </Badge>
                  <Badge v-if="parameter.required" variant="destructive" class="text-xs">
                    必填
                  </Badge>
                </div>
                <div class="text-sm text-gray-500">
                  {{ parameter.name }}
                </div>
              </div>
              
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div v-if="parameter.unit">
                  <span class="text-gray-600">单位:</span>
                  <span class="ml-1">{{ parameter.unit }}</span>
                </div>
                <div v-if="parameter.defaultValue !== undefined">
                  <span class="text-gray-600">默认值:</span>
                  <span class="ml-1">{{ parameter.defaultValue }}</span>
                </div>
                <div v-if="parameter.minValue !== undefined">
                  <span class="text-gray-600">最小值:</span>
                  <span class="ml-1">{{ parameter.minValue }}</span>
                </div>
                <div v-if="parameter.maxValue !== undefined">
                  <span class="text-gray-600">最大值:</span>
                  <span class="ml-1">{{ parameter.maxValue }}</span>
                </div>
              </div>
              
              <div v-if="parameter.options && parameter.options.length > 0" class="mt-2">
                <span class="text-sm text-gray-600">选项:</span>
                <div class="flex flex-wrap gap-1 mt-1">
                  <Badge
                    v-for="option in parameter.options"
                    :key="option.value"
                    variant="outline"
                    class="text-xs"
                  >
                    {{ option.label }} ({{ option.value }})
                  </Badge>
                </div>
              </div>
              
              <div v-if="parameter.description" class="mt-2 text-sm text-gray-600">
                {{ parameter.description }}
              </div>
            </div>
          </div>
        </div>

        <!-- 约束条件 -->
        <div v-if="component.constraints.length > 0">
          <h3 class="text-lg font-semibold mb-3">约束条件 ({{ component.constraints.length }})</h3>
          <div class="space-y-3">
            <div
              v-for="constraint in component.constraints"
              :key="constraint.id"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                  <h4 class="font-medium">{{ constraint.name }}</h4>
                  <Badge :variant="getSeverityVariant(constraint.severity)" class="text-xs">
                    {{ getSeverityText(constraint.severity) }}
                  </Badge>
                  <Badge v-if="!constraint.enabled" variant="outline" class="text-xs">
                    已禁用
                  </Badge>
                </div>
                <Badge variant="secondary" class="text-xs">
                  {{ getConstraintTypeText(constraint.type) }}
                </Badge>
              </div>
              
              <div class="mb-2">
                <Label class="text-sm font-medium text-gray-600">约束表达式</Label>
                <div class="mt-1 p-3 bg-gray-50 rounded-lg font-mono text-sm">
                  {{ constraint.expression }}
                </div>
              </div>
              
              <div class="mb-2">
                <Label class="text-sm font-medium text-gray-600">错误信息</Label>
                <p class="text-sm text-gray-900 mt-1">{{ constraint.errorMessage }}</p>
              </div>
              
              <div v-if="constraint.autoFix?.enabled" class="mt-2 p-3 bg-blue-50 rounded-lg">
                <div class="flex items-center gap-2 mb-1">
                  <Settings class="w-4 h-4 text-blue-600" />
                  <span class="text-sm font-medium text-blue-900">自动修复</span>
                </div>
                <p class="text-sm text-blue-800">{{ constraint.autoFix.fixMessage }}</p>
                <div class="mt-1 font-mono text-xs text-blue-700">
                  {{ constraint.autoFix.fixExpression }}
                </div>
              </div>
              
              <div v-if="constraint.description" class="mt-2 text-sm text-gray-600">
                {{ constraint.description }}
              </div>
            </div>
          </div>
        </div>

        <!-- 工艺要求 -->
        <div v-if="component.processRequirements.length > 0">
          <h3 class="text-lg font-semibold mb-3">工艺要求 ({{ component.processRequirements.length }})</h3>
          <div class="space-y-3">
            <div
              v-for="requirement in component.processRequirements"
              :key="requirement.id"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium">{{ requirement.name }}</h4>
                <Badge variant="secondary" class="text-xs">
                  {{ getProcessTypeText(requirement.processType) }}
                </Badge>
              </div>
              
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-2">
                <div v-if="requirement.estimatedTime">
                  <span class="text-gray-600">预计时间:</span>
                  <span class="ml-1">{{ requirement.estimatedTime }}分钟</span>
                </div>
                <div v-if="requirement.skillLevel">
                  <span class="text-gray-600">技能等级:</span>
                  <span class="ml-1">{{ getSkillLevelText(requirement.skillLevel) }}</span>
                </div>
                <div v-if="requirement.equipmentRequired">
                  <span class="text-gray-600">设备要求:</span>
                  <span class="ml-1">{{ requirement.equipmentRequired }}</span>
                </div>
                <div v-if="requirement.qualityStandard">
                  <span class="text-gray-600">质量标准:</span>
                  <span class="ml-1">{{ requirement.qualityStandard }}</span>
                </div>
              </div>
              
              <div v-if="requirement.description" class="text-sm text-gray-600">
                {{ requirement.description }}
              </div>
            </div>
          </div>
        </div>

        <!-- 扩展属性 -->
        <div v-if="Object.keys(component.properties).length > 0">
          <h3 class="text-lg font-semibold mb-3">扩展属性</h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <pre class="text-sm text-gray-900 whitespace-pre-wrap">{{ JSON.stringify(component.properties, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button @click="$emit('update:open', false)" variant="outline">
          关闭
        </Button>
        <Button @click="editComponent">
          <Edit class="w-4 h-4 mr-2" />
          编辑组件
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { 
  Square, Package, Wrench, Shield, HelpCircle, Settings, Edit
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import type { 
  Component, 
  ComponentType, 
  ParameterType, 
  ConstraintType, 
  SeverityLevel,
  ProcessType,
  SkillLevel
} from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  component: Component;
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'edit', component: Component): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 方法
const getTypeIcon = (type: ComponentType) => {
  const iconMap = {
    frame: Square,
    glass: Package,
    hardware: Wrench,
    seal: Shield,
    other: HelpCircle
  };
  return iconMap[type] || HelpCircle;
};

const getTypeIconClass = (type: ComponentType) => {
  const classMap = {
    frame: 'bg-blue-100 text-blue-600',
    glass: 'bg-green-100 text-green-600',
    hardware: 'bg-orange-100 text-orange-600',
    seal: 'bg-purple-100 text-purple-600',
    other: 'bg-gray-100 text-gray-600'
  };
  return classMap[type] || 'bg-gray-100 text-gray-600';
};

const getTypeText = (type: ComponentType) => {
  const textMap = {
    frame: '框料',
    glass: '玻璃',
    hardware: '五金',
    seal: '密封',
    other: '其他'
  };
  return textMap[type] || '其他';
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary' as const,
    active: 'default' as const,
    deprecated: 'destructive' as const,
    archived: 'outline' as const
  };
  return variantMap[status] || 'secondary';
};

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};

const getParameterTypeText = (type: ParameterType) => {
  const textMap = {
    number: '数值',
    string: '字符串',
    boolean: '布尔值',
    select: '选择',
    formula: '公式'
  };
  return textMap[type] || type;
};

const getConstraintTypeText = (type: ConstraintType) => {
  const textMap = {
    dimension: '尺寸',
    material: '材料',
    process: '工艺',
    compatibility: '兼容性'
  };
  return textMap[type] || type;
};

const getSeverityVariant = (severity: SeverityLevel) => {
  const variantMap = {
    error: 'destructive' as const,
    warning: 'secondary' as const,
    info: 'outline' as const
  };
  return variantMap[severity] || 'outline';
};

const getSeverityText = (severity: SeverityLevel) => {
  const textMap = {
    error: '错误',
    warning: '警告',
    info: '信息'
  };
  return textMap[severity] || severity;
};

const getProcessTypeText = (type: ProcessType) => {
  const textMap = {
    cutting: '切割',
    drilling: '钻孔',
    assembly: '装配',
    welding: '焊接',
    coating: '涂装',
    testing: '检测',
    other: '其他'
  };
  return textMap[type] || type;
};

const getSkillLevelText = (level: SkillLevel) => {
  const textMap = {
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家'
  };
  return textMap[level] || level;
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const editComponent = () => {
  emit('edit', props.component);
  emit('update:open', false);
};
</script>
