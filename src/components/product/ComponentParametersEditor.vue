<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">参数定义</h3>
        <p class="text-sm text-gray-600">定义组件的可配置参数</p>
      </div>
      <Button @click="addParameter" class="bg-blue-600 hover:bg-blue-700">
        <Plus class="w-4 h-4 mr-2" />
        添加参数
      </Button>
    </div>

    <!-- 参数列表 -->
    <div v-if="localParameters.length > 0" class="space-y-4">
      <div
        v-for="(parameter, index) in localParameters"
        :key="parameter.id || index"
        class="border rounded-lg p-4 space-y-4"
      >
        <!-- 参数头部 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <Badge :variant="getParameterTypeVariant(parameter.type)">
              {{ getParameterTypeText(parameter.type) }}
            </Badge>
            <Badge v-if="parameter.required" variant="destructive" class="text-xs">
              必填
            </Badge>
            <Badge v-if="!parameter.visible" variant="outline" class="text-xs">
              隐藏
            </Badge>
            <Badge v-if="!parameter.editable" variant="outline" class="text-xs">
              只读
            </Badge>
          </div>
          <div class="flex items-center gap-2">
            <Button @click="moveParameterUp(index)" :disabled="index === 0" variant="ghost" size="sm">
              <ChevronUp class="w-4 h-4" />
            </Button>
            <Button @click="moveParameterDown(index)" :disabled="index === localParameters.length - 1" variant="ghost" size="sm">
              <ChevronDown class="w-4 h-4" />
            </Button>
            <Button @click="duplicateParameter(index)" variant="ghost" size="sm">
              <Copy class="w-4 h-4" />
            </Button>
            <Button @click="removeParameter(index)" variant="ghost" size="sm" class="text-red-600">
              <Trash2 class="w-4 h-4" />
            </Button>
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label :for="`param-name-${index}`" class="required">参数名称</Label>
            <Input
              :id="`param-name-${index}`"
              v-model="parameter.name"
              placeholder="参数名称（英文）"
              :class="{ 'border-red-500': getParameterError(index, 'name') }"
              @blur="validateParameter(index)"
            />
            <p v-if="getParameterError(index, 'name')" class="text-sm text-red-600">
              {{ getParameterError(index, 'name') }}
            </p>
          </div>
          
          <div class="space-y-2">
            <Label :for="`param-display-name-${index}`" class="required">显示名称</Label>
            <Input
              :id="`param-display-name-${index}`"
              v-model="parameter.displayName"
              placeholder="显示名称（中文）"
              :class="{ 'border-red-500': getParameterError(index, 'displayName') }"
              @blur="validateParameter(index)"
            />
            <p v-if="getParameterError(index, 'displayName')" class="text-sm text-red-600">
              {{ getParameterError(index, 'displayName') }}
            </p>
          </div>
          
          <div class="space-y-2">
            <Label :for="`param-type-${index}`" class="required">参数类型</Label>
            <Select v-model="parameter.type" @update:model-value="onParameterTypeChange(index)">
              <SelectTrigger :id="`param-type-${index}`">
                <SelectValue placeholder="选择参数类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="number">数值</SelectItem>
                <SelectItem value="string">字符串</SelectItem>
                <SelectItem value="boolean">布尔值</SelectItem>
                <SelectItem value="select">选择</SelectItem>
                <SelectItem value="formula">公式</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 参数描述 -->
        <div class="space-y-2">
          <Label :for="`param-description-${index}`">参数描述</Label>
          <Textarea
            :id="`param-description-${index}`"
            v-model="parameter.description"
            placeholder="参数描述"
            rows="2"
          />
        </div>

        <!-- 数值类型特有配置 -->
        <div v-if="parameter.type === 'number'" class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="space-y-2">
            <Label :for="`param-unit-${index}`">单位</Label>
            <Input
              :id="`param-unit-${index}`"
              v-model="parameter.unit"
              placeholder="如: mm, kg"
            />
          </div>
          
          <div class="space-y-2">
            <Label :for="`param-min-${index}`">最小值</Label>
            <Input
              :id="`param-min-${index}`"
              v-model.number="parameter.minValue"
              type="number"
              placeholder="最小值"
            />
          </div>
          
          <div class="space-y-2">
            <Label :for="`param-max-${index}`">最大值</Label>
            <Input
              :id="`param-max-${index}`"
              v-model.number="parameter.maxValue"
              type="number"
              placeholder="最大值"
            />
          </div>
          
          <div class="space-y-2">
            <Label :for="`param-default-${index}`">默认值</Label>
            <Input
              :id="`param-default-${index}`"
              v-model.number="parameter.defaultValue"
              type="number"
              placeholder="默认值"
            />
          </div>
        </div>

        <!-- 字符串类型特有配置 -->
        <div v-else-if="parameter.type === 'string'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label :for="`param-default-string-${index}`">默认值</Label>
            <Input
              :id="`param-default-string-${index}`"
              v-model="parameter.defaultValue"
              placeholder="默认值"
            />
          </div>
          
          <div class="space-y-2">
            <Label :for="`param-max-length-${index}`">最大长度</Label>
            <Input
              :id="`param-max-length-${index}`"
              v-model.number="parameter.maxLength"
              type="number"
              placeholder="最大字符长度"
            />
          </div>
        </div>

        <!-- 布尔值类型特有配置 -->
        <div v-else-if="parameter.type === 'boolean'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label :for="`param-default-bool-${index}`">默认值</Label>
            <Select v-model="parameter.defaultValue">
              <SelectTrigger :id="`param-default-bool-${index}`">
                <SelectValue placeholder="选择默认值" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem :value="true">是 (true)</SelectItem>
                <SelectItem :value="false">否 (false)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 选择类型特有配置 -->
        <div v-else-if="parameter.type === 'select'" class="space-y-4">
          <div class="flex items-center justify-between">
            <Label>选项配置</Label>
            <Button @click="addOption(index)" variant="outline" size="sm">
              <Plus class="w-4 h-4 mr-1" />
              添加选项
            </Button>
          </div>
          
          <div v-if="parameter.options && parameter.options.length > 0" class="space-y-2">
            <div
              v-for="(option, optionIndex) in parameter.options"
              :key="optionIndex"
              class="flex items-center gap-3 p-3 border rounded-lg"
            >
              <Input
                v-model="option.value"
                placeholder="选项值"
                class="flex-1"
              />
              <Input
                v-model="option.label"
                placeholder="显示文本"
                class="flex-1"
              />
              <div class="flex items-center space-x-2">
                <Checkbox
                  :checked="parameter.defaultValue === option.value"
                  @update:checked="setDefaultOption(index, option.value)"
                />
                <Label class="text-sm">默认</Label>
              </div>
              <Button @click="removeOption(index, optionIndex)" variant="ghost" size="sm">
                <Trash2 class="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <p v-else class="text-sm text-gray-500">
            暂无选项，点击"添加选项"按钮添加可选值
          </p>
        </div>

        <!-- 公式类型特有配置 -->
        <div v-else-if="parameter.type === 'formula'" class="space-y-2">
          <Label :for="`param-formula-${index}`">公式表达式</Label>
          <Textarea
            :id="`param-formula-${index}`"
            v-model="parameter.defaultValue"
            placeholder="如: width * height / 1000000"
            rows="2"
            class="font-mono"
          />
          <p class="text-xs text-gray-500">
            可以使用其他参数名称和数学运算符 (+, -, *, /, (), sin, cos, sqrt 等)
          </p>
        </div>

        <!-- 参数属性 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label :for="`param-category-${index}`">参数分类</Label>
            <Select v-model="parameter.category">
              <SelectTrigger :id="`param-category-${index}`">
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dimension">尺寸</SelectItem>
                <SelectItem value="material">材料</SelectItem>
                <SelectItem value="process">工艺</SelectItem>
                <SelectItem value="appearance">外观</SelectItem>
                <SelectItem value="performance">性能</SelectItem>
                <SelectItem value="other">其他</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div class="flex items-center space-x-6">
            <div class="flex items-center space-x-2">
              <Checkbox
                :id="`param-required-${index}`"
                v-model:checked="parameter.required"
              />
              <Label :for="`param-required-${index}`" class="text-sm">必填</Label>
            </div>
            
            <div class="flex items-center space-x-2">
              <Checkbox
                :id="`param-visible-${index}`"
                v-model:checked="parameter.visible"
              />
              <Label :for="`param-visible-${index}`" class="text-sm">可见</Label>
            </div>
            
            <div class="flex items-center space-x-2">
              <Checkbox
                :id="`param-editable-${index}`"
                v-model:checked="parameter.editable"
              />
              <Label :for="`param-editable-${index}`" class="text-sm">可编辑</Label>
            </div>
          </div>
        </div>

        <!-- 验证规则 -->
        <div class="space-y-2">
          <Label>验证规则</Label>
          <div class="space-y-2">
            <div v-if="parameter.validationRules && parameter.validationRules.length > 0" class="space-y-1">
              <div
                v-for="(rule, ruleIndex) in parameter.validationRules"
                :key="ruleIndex"
                class="flex items-center gap-2"
              >
                <Input
                  v-model="parameter.validationRules[ruleIndex]"
                  placeholder="如: value > 0"
                  class="flex-1 font-mono text-sm"
                />
                <Button @click="removeValidationRule(index, ruleIndex)" variant="ghost" size="sm">
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>
            </div>
            <Button @click="addValidationRule(index)" variant="outline" size="sm">
              <Plus class="w-4 h-4 mr-1" />
              添加验证规则
            </Button>
          </div>
          <p class="text-xs text-gray-500">
            验证规则使用 JavaScript 表达式，可以使用 value 变量表示当前参数值
          </p>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-12">
      <Package class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <p class="text-gray-600 mb-4">暂无参数定义</p>
      <Button @click="addParameter" class="bg-blue-600 hover:bg-blue-700">
        <Plus class="w-4 h-4 mr-2" />
        添加第一个参数
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { 
  Plus, ChevronUp, ChevronDown, Copy, Trash2, Package
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import type { ComponentParameter, ParameterType, ParameterOption } from '@/types/product-structure';

// Props
interface Props {
  modelValue: ComponentParameter[];
  errors: any[];
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: ComponentParameter[]): void;
  (e: 'validate', errors: any[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localParameters = ref<ComponentParameter[]>([...props.modelValue]);
const parameterErrors = ref<Record<number, Record<string, string>>>({});

// 方法
const addParameter = () => {
  const newParameter: ComponentParameter = {
    id: `param-${Date.now()}`,
    name: '',
    displayName: '',
    type: 'string',
    required: false,
    visible: true,
    editable: true,
    category: 'other',
    description: '',
    validationRules: []
  };
  
  localParameters.value.push(newParameter);
};

const removeParameter = (index: number) => {
  localParameters.value.splice(index, 1);
  delete parameterErrors.value[index];
  
  // 重新索引错误
  const newErrors: Record<number, Record<string, string>> = {};
  Object.entries(parameterErrors.value).forEach(([key, value]) => {
    const numKey = parseInt(key);
    if (numKey > index) {
      newErrors[numKey - 1] = value;
    } else if (numKey < index) {
      newErrors[numKey] = value;
    }
  });
  parameterErrors.value = newErrors;
};

const duplicateParameter = (index: number) => {
  const original = localParameters.value[index];
  const duplicated: ComponentParameter = {
    ...original,
    id: `param-${Date.now()}`,
    name: `${original.name}_copy`,
    displayName: `${original.displayName} (副本)`
  };
  
  localParameters.value.splice(index + 1, 0, duplicated);
};

const moveParameterUp = (index: number) => {
  if (index > 0) {
    const temp = localParameters.value[index];
    localParameters.value[index] = localParameters.value[index - 1];
    localParameters.value[index - 1] = temp;
  }
};

const moveParameterDown = (index: number) => {
  if (index < localParameters.value.length - 1) {
    const temp = localParameters.value[index];
    localParameters.value[index] = localParameters.value[index + 1];
    localParameters.value[index + 1] = temp;
  }
};

const onParameterTypeChange = (index: number) => {
  const parameter = localParameters.value[index];
  
  // 清除类型特定的属性
  delete parameter.unit;
  delete parameter.minValue;
  delete parameter.maxValue;
  delete parameter.maxLength;
  delete parameter.options;
  parameter.defaultValue = undefined;
  
  // 根据新类型设置默认属性
  switch (parameter.type) {
    case 'number':
      parameter.defaultValue = 0;
      break;
    case 'boolean':
      parameter.defaultValue = false;
      break;
    case 'select':
      parameter.options = [];
      break;
  }
};

const addOption = (parameterIndex: number) => {
  const parameter = localParameters.value[parameterIndex];
  if (!parameter.options) {
    parameter.options = [];
  }
  
  parameter.options.push({
    value: '',
    label: ''
  });
};

const removeOption = (parameterIndex: number, optionIndex: number) => {
  const parameter = localParameters.value[parameterIndex];
  if (parameter.options) {
    parameter.options.splice(optionIndex, 1);
  }
};

const setDefaultOption = (parameterIndex: number, optionValue: string) => {
  const parameter = localParameters.value[parameterIndex];
  parameter.defaultValue = optionValue;
};

const addValidationRule = (index: number) => {
  const parameter = localParameters.value[index];
  if (!parameter.validationRules) {
    parameter.validationRules = [];
  }
  parameter.validationRules.push('');
};

const removeValidationRule = (parameterIndex: number, ruleIndex: number) => {
  const parameter = localParameters.value[parameterIndex];
  if (parameter.validationRules) {
    parameter.validationRules.splice(ruleIndex, 1);
  }
};

const validateParameter = (index: number) => {
  const parameter = localParameters.value[index];
  const errors: Record<string, string> = {};
  
  if (!parameter.name?.trim()) {
    errors.name = '参数名称不能为空';
  } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(parameter.name)) {
    errors.name = '参数名称必须以字母开头，只能包含字母、数字和下划线';
  } else {
    // 检查名称是否重复
    const duplicateIndex = localParameters.value.findIndex((p, i) => 
      i !== index && p.name === parameter.name
    );
    if (duplicateIndex !== -1) {
      errors.name = '参数名称不能重复';
    }
  }
  
  if (!parameter.displayName?.trim()) {
    errors.displayName = '显示名称不能为空';
  }
  
  parameterErrors.value[index] = errors;
  
  // 发出验证事件
  const allErrors = Object.values(parameterErrors.value).flat();
  emit('validate', allErrors);
};

const getParameterError = (index: number, field: string) => {
  return parameterErrors.value[index]?.[field];
};

const getParameterTypeText = (type: ParameterType) => {
  const textMap = {
    number: '数值',
    string: '字符串',
    boolean: '布尔值',
    select: '选择',
    formula: '公式'
  };
  return textMap[type] || type;
};

const getParameterTypeVariant = (type: ParameterType) => {
  const variantMap = {
    number: 'default' as const,
    string: 'secondary' as const,
    boolean: 'outline' as const,
    select: 'destructive' as const,
    formula: 'default' as const
  };
  return variantMap[type] || 'secondary';
};

// 防止循环更新的标志
const isUpdating = ref(false);

// 监听器
watch(localParameters, (newValue) => {
  if (isUpdating.value) return;
  emit('update:modelValue', newValue);
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  if (isUpdating.value) return;

  isUpdating.value = true;
  localParameters.value = [...newValue];

  nextTick(() => {
    isUpdating.value = false;
  });
}, { deep: true });
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
