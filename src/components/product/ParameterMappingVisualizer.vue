<template>
  <div class="bg-white border rounded-lg p-6">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-medium">参数透传关系图</h3>
        <p class="text-sm text-gray-600">可视化展示构件参数如何透传到组件实例</p>
      </div>
      <div class="flex items-center gap-2">
        <Badge variant="outline" class="text-sm">
          {{ assemblyParameters.length }} 个构件参数
        </Badge>
        <Badge variant="outline" class="text-sm">
          {{ componentInstances.length }} 个组件实例
        </Badge>
      </div>
    </div>

    <div v-if="assemblyParameters.length === 0 && componentInstances.length === 0" class="text-center py-12">
      <Settings class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无参数映射</h3>
      <p class="text-gray-600">添加构件参数和组件实例后，这里将显示参数透传关系</p>
    </div>

    <div v-else class="space-y-6">
      <!-- 构件参数列表 -->
      <div class="bg-blue-50 rounded-lg p-4">
        <h4 class="font-medium text-blue-900 mb-3 flex items-center gap-2">
          <Settings class="w-4 h-4" />
          构件参数 ({{ assemblyParameters.length }})
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          <div
            v-for="param in assemblyParameters"
            :key="param.id"
            class="bg-white rounded border p-3 hover:shadow-sm transition-shadow"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="font-medium text-sm">{{ param.displayName }}</span>
              <Badge variant="outline" class="text-xs">{{ getParameterTypeText(param.type) }}</Badge>
            </div>
            <div class="text-xs text-gray-600 mb-2">{{ param.name }}</div>
            <div v-if="param.description" class="text-xs text-gray-500 line-clamp-2">
              {{ param.description }}
            </div>
            
            <!-- 透传目标 -->
            <div class="mt-3 pt-2 border-t">
              <div class="text-xs text-gray-600 mb-1">透传到:</div>
              <div class="flex flex-wrap gap-1">
                <Badge
                  v-for="target in getParameterTargets(param)"
                  :key="target"
                  variant="secondary"
                  class="text-xs"
                >
                  {{ target }}
                </Badge>
                <span v-if="getParameterTargets(param).length === 0" class="text-xs text-gray-400">
                  未配置透传
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 组件实例列表 -->
      <div class="bg-green-50 rounded-lg p-4">
        <h4 class="font-medium text-green-900 mb-3 flex items-center gap-2">
          <Package class="w-4 h-4" />
          组件实例 ({{ componentInstances.length }})
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            v-for="instance in componentInstances"
            :key="instance.id"
            class="bg-white rounded border p-4 hover:shadow-sm transition-shadow"
          >
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Package class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <div class="font-medium">{{ instance.instanceName }}</div>
                <div class="text-sm text-gray-600">{{ instance.componentId }}</div>
              </div>
            </div>
            
            <!-- 接收的参数 -->
            <div class="space-y-2">
              <div class="text-xs text-gray-600">接收参数:</div>
              <div class="space-y-1">
                <div
                  v-for="param in getInstanceParameters(instance)"
                  :key="param.name"
                  class="flex items-center justify-between text-xs bg-gray-50 rounded px-2 py-1"
                >
                  <span>{{ param.name }}</span>
                  <Badge variant="outline" class="text-xs">{{ getParameterTypeText(param.type) }}</Badge>
                </div>
                <div v-if="getInstanceParameters(instance).length === 0" class="text-xs text-gray-400 px-2 py-1">
                  暂无参数配置
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 参数映射关系 -->
      <div class="bg-yellow-50 rounded-lg p-4">
        <h4 class="font-medium text-yellow-900 mb-3 flex items-center gap-2">
          <ArrowRightLeft class="w-4 h-4" />
          参数映射关系
        </h4>
        <div class="space-y-3">
          <div
            v-for="mapping in getParameterMappings()"
            :key="`${mapping.assemblyParam}-${mapping.instanceId}-${mapping.instanceParam}`"
            class="bg-white rounded border p-3 flex items-center justify-between"
          >
            <div class="flex items-center gap-4">
              <div class="text-sm">
                <span class="font-medium text-blue-700">{{ mapping.assemblyParam }}</span>
                <span class="text-gray-500 mx-2">→</span>
                <span class="font-medium text-green-700">{{ mapping.instanceName }}</span>
                <span class="text-gray-500">.</span>
                <span class="font-medium">{{ mapping.instanceParam }}</span>
              </div>
            </div>
            <Badge variant="outline" class="text-xs">{{ mapping.type }}</Badge>
          </div>
          <div v-if="getParameterMappings().length === 0" class="text-center py-4 text-gray-500">
            <ArrowRightLeft class="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p class="text-sm">暂无参数映射关系</p>
            <p class="text-xs">在参数配置中设置参数透传关系</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Settings, Package, ArrowRightLeft } from 'lucide-vue-next';
import type { ComponentParameter, ComponentInstance } from '@/types/product-structure';

// Props
interface Props {
  assemblyParameters: ComponentParameter[];
  componentInstances: ComponentInstance[];
}

const props = defineProps<Props>();

// 参数类型文本映射
const getParameterTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'string': '文本',
    'number': '数值',
    'boolean': '布尔',
    'select': '选择',
    'multiselect': '多选',
    'date': '日期',
    'datetime': '日期时间',
    'file': '文件',
    'color': '颜色',
    'range': '范围',
    'json': 'JSON',
    'array': '数组',
    'object': '对象'
  };
  return typeMap[type] || type;
};

// 获取参数的透传目标
const getParameterTargets = (param: ComponentParameter) => {
  const targets: string[] = [];
  
  props.componentInstances.forEach(instance => {
    // 检查参数绑定配置
    if (instance.parameterConfig?.parameterBindings) {
      Object.entries(instance.parameterConfig.parameterBindings).forEach(([instanceParam, binding]) => {
        if (binding.type === 'assembly_parameter' && binding.source === param.name) {
          targets.push(`${instance.instanceName}.${instanceParam}`);
        }
      });
    }
  });
  
  return targets;
};

// 获取组件实例的参数
const getInstanceParameters = (instance: ComponentInstance) => {
  // 这里应该从组件定义中获取参数，暂时返回空数组
  // TODO: 集成组件服务获取组件参数定义
  return [];
};

// 获取参数映射关系
const getParameterMappings = () => {
  const mappings: Array<{
    assemblyParam: string;
    instanceId: string;
    instanceName: string;
    instanceParam: string;
    type: string;
  }> = [];
  
  props.componentInstances.forEach(instance => {
    if (instance.parameterConfig?.parameterBindings) {
      Object.entries(instance.parameterConfig.parameterBindings).forEach(([instanceParam, binding]) => {
        if (binding.type === 'assembly_parameter') {
          const assemblyParam = props.assemblyParameters.find(p => p.name === binding.source);
          if (assemblyParam) {
            mappings.push({
              assemblyParam: assemblyParam.displayName,
              instanceId: instance.id,
              instanceName: instance.instanceName,
              instanceParam,
              type: assemblyParam.type
            });
          }
        }
      });
    }
  });
  
  return mappings;
};
</script>
