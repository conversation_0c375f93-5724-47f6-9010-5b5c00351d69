<template>
  <div class="visual-editor-toolbar">
    <!-- 主工具栏 -->
    <div class="main-toolbar flex items-center gap-1 p-2 bg-gray-100 border-b">
      <!-- 文件操作 -->
      <div class="toolbar-group">
        <ToolbarButton
          icon="FileText"
          label="新建"
          shortcut="Ctrl+N"
          @click="$emit('new')"
        />
        <ToolbarButton
          icon="FolderOpen"
          label="打开"
          shortcut="Ctrl+O"
          @click="$emit('open')"
        />
        <ToolbarButton
          icon="Save"
          label="保存"
          shortcut="Ctrl+S"
          :disabled="!hasChanges"
          @click="$emit('save')"
        />
      </div>

      <div class="toolbar-separator"></div>

      <!-- 编辑操作 -->
      <div class="toolbar-group">
        <ToolbarButton
          icon="Undo"
          label="撤销"
          shortcut="Ctrl+Z"
          :disabled="!canUndo"
          @click="$emit('undo')"
        />
        <ToolbarButton
          icon="Redo"
          label="重做"
          shortcut="Ctrl+Y"
          :disabled="!canRedo"
          @click="$emit('redo')"
        />
      </div>

      <div class="toolbar-separator"></div>

      <!-- 绘制工具 -->
      <div class="toolbar-group">
        <ToolbarButton
          icon="MousePointer"
          label="选择"
          shortcut="S"
          :active="activeTool === 'select'"
          @click="$emit('tool-change', 'select')"
        />
        <ToolbarButton
          icon="Move"
          label="移动"
          shortcut="M"
          :active="activeTool === 'move'"
          @click="$emit('tool-change', 'move')"
        />
        <ToolbarButton
          icon="Square"
          label="添加组件"
          shortcut="C"
          :active="activeTool === 'add-component'"
          @click="$emit('tool-change', 'add-component')"
        />
        <ToolbarButton
          icon="Layers"
          label="添加构件"
          shortcut="A"
          :active="activeTool === 'add-assembly'"
          @click="$emit('tool-change', 'add-assembly')"
        />
      </div>

      <div class="toolbar-separator"></div>

      <!-- 视图操作 -->
      <div class="toolbar-group">
        <ToolbarButton
          icon="ZoomIn"
          label="放大"
          shortcut="+"
          @click="$emit('zoom-in')"
        />
        <ToolbarButton
          icon="ZoomOut"
          label="缩小"
          shortcut="-"
          @click="$emit('zoom-out')"
        />
        <ToolbarButton
          icon="Maximize2"
          label="适应视图"
          shortcut="F"
          @click="$emit('fit-view')"
        />
        <ToolbarButton
          icon="RotateCcw"
          label="重置视图"
          shortcut="R"
          @click="$emit('reset-view')"
        />
      </div>

      <div class="toolbar-separator"></div>

      <!-- 布局选择 -->
      <div class="toolbar-group">
        <Select :model-value="currentLayout" @update:model-value="$emit('layout-change', $event)">
          <SelectTrigger class="w-32 h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="tree">树形布局</SelectItem>
            <SelectItem value="force">力导向布局</SelectItem>
            <SelectItem value="circular">环形布局</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div class="toolbar-separator"></div>

      <!-- 验证和帮助 -->
      <div class="toolbar-group">
        <ToolbarButton
          icon="CheckCircle"
          label="验证结构"
          shortcut="Ctrl+T"
          @click="$emit('validate')"
        />
        <ToolbarButton
          icon="HelpCircle"
          label="帮助"
          shortcut="F1"
          @click="$emit('help')"
        />
      </div>

      <!-- 右侧状态信息 -->
      <div class="flex-1"></div>
      <div class="toolbar-group text-sm text-gray-600">
        <span>节点: {{ nodeCount }}</span>
        <span class="mx-2">|</span>
        <span>连接: {{ edgeCount }}</span>
        <span class="mx-2">|</span>
        <span>缩放: {{ Math.round(zoom * 100) }}%</span>
      </div>
    </div>

    <!-- 快捷工具栏 -->
    <div class="quick-toolbar flex items-center gap-1 p-1 bg-gray-50 border-b text-xs">
      <div class="flex items-center gap-1">
        <span class="text-gray-500">快捷键:</span>
        <kbd class="kbd">Space</kbd>
        <span class="text-gray-500">平移</span>
        <kbd class="kbd">Del</kbd>
        <span class="text-gray-500">删除</span>
        <kbd class="kbd">Ctrl+D</kbd>
        <span class="text-gray-500">复制</span>
        <kbd class="kbd">Esc</kbd>
        <span class="text-gray-500">取消选择</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import ToolbarButton from './ToolbarButton.vue';

// Props
interface Props {
  activeTool: string;
  currentLayout: string;
  hasChanges: boolean;
  canUndo: boolean;
  canRedo: boolean;
  nodeCount: number;
  edgeCount: number;
  zoom: number;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'new': [];
  'open': [];
  'save': [];
  'undo': [];
  'redo': [];
  'tool-change': [tool: string];
  'zoom-in': [];
  'zoom-out': [];
  'fit-view': [];
  'reset-view': [];
  'layout-change': [layout: string];
  'validate': [];
  'help': [];
}>();
</script>

<style scoped>
.visual-editor-toolbar {
  @apply flex-shrink-0 bg-white border-b;
}

.main-toolbar {
  @apply min-h-12;
}

.quick-toolbar {
  @apply min-h-8;
}

.toolbar-group {
  @apply flex items-center gap-1;
}

.toolbar-separator {
  @apply w-px h-6 bg-gray-300 mx-2;
}

.kbd {
  @apply inline-flex items-center px-1.5 py-0.5 bg-gray-200 text-gray-700 text-xs font-mono rounded border;
}
</style>
