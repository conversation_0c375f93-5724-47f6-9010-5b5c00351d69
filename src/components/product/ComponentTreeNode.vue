<template>
  <div class="component-tree-node">
    <TreeNode
      :node="componentNode"
      :level="level"
      :selected="selectedNode === component.id"
      :expanded="false"
      @select="$emit('node-select', component.id)"
      @toggle="() => {}"
      @add="handleAddNode"
      @delete="$emit('node-delete', component.id)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import TreeNode from './TreeNode.vue';

// Props
interface Props {
  component: any; // ComponentInstance type
  level: number;
  selectedNode: string | null;
  expandedNodes: Set<string>;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'node-select': [nodeId: string];
  'node-toggle': [nodeId: string];
  'node-add': [data: { parentId: string; type: string }];
  'node-delete': [nodeId: string];
}>();

// 计算属性
const componentNode = computed(() => ({
  id: props.component.id,
  name: props.component.componentName || props.component.instanceName,
  code: props.component.componentCode,
  type: 'component',
  description: props.component.description,
  hasChildren: false // 组件通常没有子节点
}));

// 方法
const handleAddNode = (nodeId: string) => {
  // 组件可以添加参数
  emit('node-add', { parentId: nodeId, type: 'parameter' });
};
</script>

<style scoped>
.component-tree-node {
  /* 样式 */
}
</style>
