<template>
  <div class="product-configuration-table">
    <!-- 表格 -->
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 border-b">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              产品配置
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              结构模板
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              使用统计
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              平均成本
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              状态
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              最后更新
            </th>
            <th
              class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <template v-if="loading">
            <tr v-for="n in 5" :key="n" class="animate-pulse">
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2 mt-2"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 rounded w-20"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 rounded w-16"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 rounded w-20"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-6 bg-gray-200 rounded-full w-16"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 rounded w-24"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-8 bg-gray-200 rounded w-20"></div>
              </td>
            </tr>
          </template>

          <tr v-else-if="configurations.length === 0">
            <td colspan="7" class="px-6 py-12 text-center text-gray-500">
              <Package class="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p class="text-lg font-medium">暂无产品配置</p>
              <p class="text-sm">点击"新增配置"创建第一个产品配置</p>
            </td>
          </tr>

          <tr
            v-else
            v-for="config in configurations"
            :key="config.id"
            class="hover:bg-gray-50"
          >
            <td class="px-6 py-4">
              <div class="flex items-start">
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">
                    {{ config.name }}
                  </div>
                  <div class="text-sm text-gray-500 mt-1">
                    {{ config.description }}
                  </div>
                  <div class="flex flex-wrap gap-1 mt-2">
                    <Badge
                      v-for="tag in config.tags.slice(0, 3)"
                      :key="tag"
                      variant="secondary"
                      class="text-xs"
                    >
                      {{ tag }}
                    </Badge>
                    <Badge
                      v-if="config.tags.length > 3"
                      variant="outline"
                      class="text-xs"
                    >
                      +{{ config.tags.length - 3 }}
                    </Badge>
                  </div>
                </div>
              </div>
            </td>

            <td class="px-6 py-4">
              <div class="text-sm text-gray-900">
                {{ config.structureTemplateName }}
              </div>
              <div class="text-xs text-gray-500">{{ config.category }}</div>
            </td>

            <td class="px-6 py-4">
              <div class="text-sm font-medium text-gray-900">
                {{ config.usageCount }}次
              </div>
              <div class="text-xs text-gray-500">
                {{
                  config.lastUsedAt ? formatDate(config.lastUsedAt) : "未使用"
                }}
              </div>
            </td>

            <td class="px-6 py-4">
              <div class="text-sm font-medium text-gray-900">
                ¥{{ config.averageCost.toFixed(2) }}
              </div>
            </td>

            <td class="px-6 py-4">
              <Badge :variant="getStatusVariant(config.status)">
                {{ getStatusText(config.status) }}
              </Badge>
            </td>

            <td class="px-6 py-4">
              <div class="text-sm text-gray-900">
                {{ formatDate(config.updatedAt) }}
              </div>
              <div class="text-xs text-gray-500">v{{ config.version }}</div>
            </td>

            <td class="px-6 py-4 text-right">
              <div class="flex items-center justify-end gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('view-details', config)"
                >
                  <Eye class="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('edit', config)"
                >
                  <Edit class="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('delete', config)"
                  class="text-red-600 hover:text-red-700"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div
      v-if="!loading && configurations.length > 0"
      class="px-6 py-4 border-t"
    >
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} -
          {{
            Math.min(pagination.page * pagination.pageSize, pagination.total)
          }}
          条， 共 {{ pagination.total }} 条记录
        </div>
        <div class="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="pagination.page <= 1"
            @click="$emit('page-change', pagination.page - 1)"
          >
            <ChevronLeft class="w-4 h-4" />
          </Button>

          <div class="flex gap-1">
            <Button
              v-for="page in getPageNumbers()"
              :key="page"
              :variant="page === pagination.page ? 'default' : 'outline'"
              size="sm"
              class="w-8 h-8 p-0"
              @click="$emit('page-change', page)"
            >
              {{ page }}
            </Button>
          </div>

          <Button
            variant="outline"
            size="sm"
            :disabled="
              pagination.page >=
              Math.ceil(pagination.total / pagination.pageSize)
            "
            @click="$emit('page-change', pagination.page + 1)"
          >
            <ChevronRight class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Button } from "@/components/ui/button";
import Badge from "@/components/ui/badge/Badge.vue";
import {
  Package,
  Eye,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
} from "lucide-vue-next";

import type { ProductConfiguration, LifecycleStatus } from "@/types/product";

// Props
interface Props {
  configurations: ProductConfiguration[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  edit: [config: ProductConfiguration];
  delete: [config: ProductConfiguration];
  "view-details": [config: ProductConfiguration];
  "page-change": [page: number];
  "page-size-change": [pageSize: number];
}>();

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
};

// 获取状态样式
const getStatusVariant = (
  status: LifecycleStatus
): "default" | "secondary" | "destructive" | "outline" => {
  const variants: Record<
    LifecycleStatus,
    "default" | "secondary" | "destructive" | "outline"
  > = {
    active: "default",
    draft: "secondary",
    deprecated: "destructive",
    archived: "outline",
  };
  return variants[status] || "outline";
};

// 获取状态文本
const getStatusText = (status: LifecycleStatus) => {
  const texts = {
    active: "激活",
    draft: "草稿",
    deprecated: "已弃用",
    archived: "已归档",
  };
  return texts[status] || status;
};

// 获取分页页码
const getPageNumbers = () => {
  const totalPages = Math.ceil(
    props.pagination.total / props.pagination.pageSize
  );
  const currentPage = props.pagination.page;
  const pages: number[] = [];

  // 显示当前页前后各2页
  const start = Math.max(1, currentPage - 2);
  const end = Math.min(totalPages, currentPage + 2);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
};
</script>

<style scoped>
.product-configuration-table {
  @apply bg-white rounded-lg border;
}
</style>
