<template>
  <div class="tree-node">
    <div
      :class="[
        'flex items-center gap-2 p-2 rounded cursor-pointer transition-colors',
        'hover:bg-gray-100',
        { 'bg-blue-100 border border-blue-300': selected }
      ]"
      :style="{ paddingLeft: `${level * 16 + 8}px` }"
      @click="$emit('select')"
    >
      <!-- 展开/折叠按钮 -->
      <button
        v-if="node.hasChildren"
        @click.stop="$emit('toggle')"
        class="w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded"
      >
        <ChevronRight
          :class="[
            'w-3 h-3 transition-transform',
            { 'rotate-90': expanded }
          ]"
        />
      </button>
      <div v-else class="w-4 h-4"></div>

      <!-- 节点图标 -->
      <div class="flex-shrink-0">
        <component
          :is="getNodeIcon(node.type)"
          :class="[
            'w-4 h-4',
            getNodeIconColor(node.type)
          ]"
        />
      </div>

      <!-- 节点信息 -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2">
          <span class="font-medium text-sm truncate">{{ node.name }}</span>
          <Badge v-if="node.code" variant="outline" class="text-xs">
            {{ node.code }}
          </Badge>
        </div>
        <p v-if="node.description" class="text-xs text-gray-500 truncate mt-0.5">
          {{ node.description }}
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          variant="ghost"
          size="sm"
          @click.stop="$emit('add', node.id)"
          class="h-6 w-6 p-0"
          title="添加子节点"
        >
          <Plus class="w-3 h-3" />
        </Button>
        <Button
          v-if="node.type !== 'product'"
          variant="ghost"
          size="sm"
          @click.stop="$emit('delete', node.id)"
          class="h-6 w-6 p-0 text-red-600 hover:text-red-700"
          title="删除节点"
        >
          <Trash2 class="w-3 h-3" />
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  ChevronRight,
  Package,
  Layers,
  Square,
  Settings,
  Plus,
  Trash2
} from 'lucide-vue-next';

// Props
interface TreeNodeData {
  id: string;
  name: string;
  code?: string;
  type: string;
  description?: string;
  hasChildren: boolean;
}

interface Props {
  node: TreeNodeData;
  level: number;
  selected: boolean;
  expanded: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'select': [];
  'toggle': [];
  'add': [nodeId: string];
  'delete': [nodeId: string];
}>();

// 方法
const getNodeIcon = (type: string) => {
  const iconMap = {
    product: Package,
    assembly: Layers,
    component: Square,
    parameter: Settings
  };
  return iconMap[type] || Package;
};

const getNodeIconColor = (type: string) => {
  const colorMap = {
    product: 'text-blue-600',
    assembly: 'text-green-600',
    component: 'text-orange-600',
    parameter: 'text-purple-600'
  };
  return colorMap[type] || 'text-gray-600';
};
</script>

<style scoped>
.tree-node:hover .opacity-0 {
  opacity: 1;
}
</style>
