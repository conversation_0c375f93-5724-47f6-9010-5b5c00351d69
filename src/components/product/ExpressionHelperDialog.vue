<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>约束表达式帮助</DialogTitle>
        <DialogDescription>
          了解如何编写约束表达式，并使用模板快速创建常用约束
        </DialogDescription>
      </DialogHeader>

      <Tabs v-model="activeTab" class="w-full">
        <TabsList class="grid w-full grid-cols-3">
          <TabsTrigger value="syntax">语法说明</TabsTrigger>
          <TabsTrigger value="templates">约束模板</TabsTrigger>
          <TabsTrigger value="builder">表达式构建器</TabsTrigger>
        </TabsList>

        <!-- 语法说明 -->
        <TabsContent value="syntax" class="space-y-6">
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">支持的操作符</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle class="text-base">算术运算符</CardTitle>
                </CardHeader>
                <CardContent class="space-y-2">
                  <div class="flex justify-between">
                    <code>+</code>
                    <span class="text-sm text-gray-600">加法</span>
                  </div>
                  <div class="flex justify-between">
                    <code>-</code>
                    <span class="text-sm text-gray-600">减法</span>
                  </div>
                  <div class="flex justify-between">
                    <code>*</code>
                    <span class="text-sm text-gray-600">乘法</span>
                  </div>
                  <div class="flex justify-between">
                    <code>/</code>
                    <span class="text-sm text-gray-600">除法</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle class="text-base">比较运算符</CardTitle>
                </CardHeader>
                <CardContent class="space-y-2">
                  <div class="flex justify-between">
                    <code>==</code>
                    <span class="text-sm text-gray-600">等于</span>
                  </div>
                  <div class="flex justify-between">
                    <code>!=</code>
                    <span class="text-sm text-gray-600">不等于</span>
                  </div>
                  <div class="flex justify-between">
                    <code>&lt;</code>
                    <span class="text-sm text-gray-600">小于</span>
                  </div>
                  <div class="flex justify-between">
                    <code>&gt;</code>
                    <span class="text-sm text-gray-600">大于</span>
                  </div>
                  <div class="flex justify-between">
                    <code>&lt;=</code>
                    <span class="text-sm text-gray-600">小于等于</span>
                  </div>
                  <div class="flex justify-between">
                    <code>&gt;=</code>
                    <span class="text-sm text-gray-600">大于等于</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle class="text-base">逻辑运算符</CardTitle>
                </CardHeader>
                <CardContent class="space-y-2">
                  <div class="flex justify-between">
                    <code>&&</code>
                    <span class="text-sm text-gray-600">逻辑与</span>
                  </div>
                  <div class="flex justify-between">
                    <code>||</code>
                    <span class="text-sm text-gray-600">逻辑或</span>
                  </div>
                  <div class="flex justify-between">
                    <code>!</code>
                    <span class="text-sm text-gray-600">逻辑非</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle class="text-base">数学函数</CardTitle>
                </CardHeader>
                <CardContent class="space-y-2">
                  <div class="flex justify-between">
                    <code>abs(x)</code>
                    <span class="text-sm text-gray-600">绝对值</span>
                  </div>
                  <div class="flex justify-between">
                    <code>min(a,b)</code>
                    <span class="text-sm text-gray-600">最小值</span>
                  </div>
                  <div class="flex justify-between">
                    <code>max(a,b)</code>
                    <span class="text-sm text-gray-600">最大值</span>
                  </div>
                  <div class="flex justify-between">
                    <code>sqrt(x)</code>
                    <span class="text-sm text-gray-600">平方根</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div class="space-y-4">
            <h3 class="text-lg font-semibold">表达式示例</h3>
            <div class="space-y-3">
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm">{{ `width / height >= 0.5 && width / height <= 3.0` }}</code>
                <p class="text-sm text-gray-600 mt-1">宽高比约束：宽高比必须在0.5到3.0之间</p>
              </div>
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm">{{ `width * height >= 500000` }}</code>
                <p class="text-sm text-gray-600 mt-1">面积约束：面积不能小于0.5平方米</p>
              </div>
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm">{{ `(material == "aluminum" && thickness >= 1.5) || (material == "steel" && thickness >= 1.0)` }}</code>
                <p class="text-sm text-gray-600 mt-1">材料厚度约束：铝材厚度≥1.5mm，钢材厚度≥1.0mm</p>
              </div>
            </div>
          </div>
        </TabsContent>

        <!-- 约束模板 -->
        <TabsContent value="templates" class="space-y-6">
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">常用约束模板</h3>
            <div class="grid grid-cols-1 gap-4">
              <Card
                v-for="template in constraintTemplates"
                :key="template.id"
                class="cursor-pointer hover:shadow-md transition-shadow"
                @click="selectTemplate(template)"
              >
                <CardHeader>
                  <div class="flex items-center justify-between">
                    <CardTitle class="text-base">{{ template.name }}</CardTitle>
                    <Badge :variant="getTemplateTypeVariant(template.type)">
                      {{ getTemplateTypeText(template.type) }}
                    </Badge>
                  </div>
                  <CardDescription>{{ template.description }}</CardDescription>
                </CardHeader>
                <CardContent>
                  <code class="text-sm bg-gray-100 p-2 rounded block">{{ template.expression }}</code>
                  <div class="mt-2 text-sm text-gray-600">
                    <p><strong>适用场景:</strong> {{ template.useCase }}</p>
                    <p><strong>参数要求:</strong> {{ template.requiredParams.join(', ') }}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <!-- 表达式构建器 -->
        <TabsContent value="builder" class="space-y-6">
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">可视化表达式构建器</h3>
            
            <!-- 参数选择 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label>可用参数</Label>
                <div class="border rounded-lg p-3 max-h-40 overflow-y-auto">
                  <div class="grid grid-cols-2 gap-2">
                    <Button
                      v-for="param in parameters"
                      :key="param.name"
                      @click="addToExpression(param.name)"
                      variant="outline"
                      size="sm"
                      class="justify-start"
                    >
                      {{ param.name }}
                      <span class="text-xs text-gray-500 ml-1">({{ param.displayName }})</span>
                    </Button>
                  </div>
                </div>
              </div>
              
              <div class="space-y-2">
                <Label>操作符</Label>
                <div class="border rounded-lg p-3">
                  <div class="grid grid-cols-4 gap-2">
                    <Button
                      v-for="operator in operators"
                      :key="operator.symbol"
                      @click="addToExpression(operator.symbol)"
                      variant="outline"
                      size="sm"
                    >
                      {{ operator.symbol }}
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表达式编辑器 -->
            <div class="space-y-2">
              <Label for="expression-builder">构建的表达式</Label>
              <Textarea
                id="expression-builder"
                v-model="builtExpression"
                placeholder="在这里构建约束表达式..."
                rows="4"
                class="font-mono"
              />
              <div class="flex items-center gap-2">
                <Button @click="clearExpression" variant="outline" size="sm">
                  清空
                </Button>
                <Button @click="validateBuiltExpression" variant="outline" size="sm">
                  <CheckCircle class="w-4 h-4 mr-1" />
                  验证
                </Button>
              </div>
            </div>

            <!-- 验证结果 -->
            <div v-if="validationResult" class="p-3 rounded-lg" :class="validationResult.isValid ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
              <div class="flex items-center gap-2">
                <CheckCircle v-if="validationResult.isValid" class="w-4 h-4 text-green-600" />
                <XCircle v-else class="w-4 h-4 text-red-600" />
                <span class="text-sm font-medium" :class="validationResult.isValid ? 'text-green-800' : 'text-red-800'">
                  {{ validationResult.message }}
                </span>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <DialogFooter>
        <Button @click="$emit('update:open', false)" variant="outline">
          取消
        </Button>
        <Button @click="insertExpression" :disabled="!builtExpression.trim()">
          <Plus class="w-4 h-4 mr-2" />
          插入表达式
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Plus, CheckCircle, XCircle } from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import type { ComponentParameter } from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  parameters: ComponentParameter[];
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'insert', expression: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const activeTab = ref('syntax');
const builtExpression = ref('');
const validationResult = ref<{ isValid: boolean; message: string } | null>(null);

// 操作符列表
const operators = ref([
  { symbol: '+', name: '加法' },
  { symbol: '-', name: '减法' },
  { symbol: '*', name: '乘法' },
  { symbol: '/', name: '除法' },
  { symbol: '==', name: '等于' },
  { symbol: '!=', name: '不等于' },
  { symbol: '<', name: '小于' },
  { symbol: '>', name: '大于' },
  { symbol: '<=', name: '小于等于' },
  { symbol: '>=', name: '大于等于' },
  { symbol: '&&', name: '逻辑与' },
  { symbol: '||', name: '逻辑或' },
  { symbol: '(', name: '左括号' },
  { symbol: ')', name: '右括号' }
]);

// 约束模板
const constraintTemplates = ref([
  {
    id: 'aspect-ratio',
    name: '宽高比约束',
    type: 'dimension',
    description: '限制组件的宽高比在合理范围内',
    expression: 'width / height >= 0.5 && width / height <= 3.0',
    useCase: '窗户、门等矩形组件',
    requiredParams: ['width', 'height']
  },
  {
    id: 'min-area',
    name: '最小面积约束',
    type: 'dimension',
    description: '确保组件面积不小于指定值',
    expression: 'width * height >= 500000',
    useCase: '玻璃、板材等面积型组件',
    requiredParams: ['width', 'height']
  },
  {
    id: 'max-area',
    name: '最大面积约束',
    type: 'dimension',
    description: '限制组件面积不超过指定值',
    expression: 'width * height <= 4000000',
    useCase: '大型玻璃、板材等',
    requiredParams: ['width', 'height']
  },
  {
    id: 'material-thickness',
    name: '材料厚度约束',
    type: 'material',
    description: '根据材料类型限制厚度范围',
    expression: '(material == "aluminum" && thickness >= 1.5) || (material == "steel" && thickness >= 1.0)',
    useCase: '框料、板材等有厚度要求的组件',
    requiredParams: ['material', 'thickness']
  },
  {
    id: 'size-compatibility',
    name: '尺寸兼容性约束',
    type: 'compatibility',
    description: '确保组件尺寸与系列规格兼容',
    expression: 'width % 50 == 0 && height % 50 == 0',
    useCase: '标准化系列产品',
    requiredParams: ['width', 'height']
  },
  {
    id: 'glass-thickness-compatibility',
    name: '玻璃厚度兼容性',
    type: 'compatibility',
    description: '确保玻璃厚度与型材系列匹配',
    expression: '(profileSeries == "60" && glassThickness <= 6) || (profileSeries == "70" && glassThickness <= 8)',
    useCase: '铝合金门窗系统',
    requiredParams: ['profileSeries', 'glassThickness']
  },
  {
    id: 'opening-size-limit',
    name: '开启扇尺寸限制',
    type: 'process',
    description: '限制开启扇的最大尺寸以确保操作性',
    expression: 'openingType == "fixed" || (width <= 1800 && height <= 2000)',
    useCase: '门窗开启扇设计',
    requiredParams: ['openingType', 'width', 'height']
  }
]);

// 方法
const selectTemplate = (template: any) => {
  builtExpression.value = template.expression;
  activeTab.value = 'builder';
};

const addToExpression = (text: string) => {
  if (builtExpression.value && !builtExpression.value.endsWith(' ') && !text.startsWith(' ')) {
    builtExpression.value += ' ';
  }
  builtExpression.value += text;
  if (!text.endsWith(' ') && !['+', '-', '*', '/', '==', '!=', '<', '>', '<=', '>=', '&&', '||', '(', ')'].includes(text)) {
    builtExpression.value += ' ';
  }
};

const clearExpression = () => {
  builtExpression.value = '';
  validationResult.value = null;
};

const validateBuiltExpression = () => {
  try {
    const expression = builtExpression.value.trim();
    
    if (!expression) {
      validationResult.value = {
        isValid: false,
        message: '表达式不能为空'
      };
      return;
    }
    
    // 检查括号匹配
    let openParens = 0;
    for (const char of expression) {
      if (char === '(') openParens++;
      if (char === ')') openParens--;
      if (openParens < 0) break;
    }
    
    if (openParens !== 0) {
      validationResult.value = {
        isValid: false,
        message: '括号不匹配'
      };
      return;
    }
    
    // 检查参数引用
    const paramRefs = expression.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];
    const availableParamNames = props.parameters.map(p => p.name);
    const invalidRefs = paramRefs.filter(ref => 
      !availableParamNames.includes(ref) && 
      !['sin', 'cos', 'tan', 'log', 'exp', 'sqrt', 'abs', 'min', 'max', 'PI', 'E'].includes(ref)
    );
    
    if (invalidRefs.length > 0) {
      validationResult.value = {
        isValid: false,
        message: `未定义的参数引用: ${invalidRefs.join(', ')}`
      };
      return;
    }
    
    validationResult.value = {
      isValid: true,
      message: '表达式语法正确'
    };
    
  } catch (error) {
    validationResult.value = {
      isValid: false,
      message: `表达式解析错误: ${error.message}`
    };
  }
};

const insertExpression = () => {
  emit('insert', builtExpression.value);
  emit('update:open', false);
};

const getTemplateTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    dimension: '尺寸',
    material: '材料',
    process: '工艺',
    compatibility: '兼容性'
  };
  return textMap[type] || type;
};

const getTemplateTypeVariant = (type: string) => {
  const variantMap: Record<string, any> = {
    dimension: 'default',
    material: 'secondary',
    process: 'outline',
    compatibility: 'destructive'
  };
  return variantMap[type] || 'secondary';
};
</script>
