<template>
  <div class="assembly-parameter-editor">
    <div class="space-y-6">
      <!-- 参数列表 -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="text-lg">构件参数</CardTitle>
              <CardDescription>
                定义影响整个构件的参数，这些参数可以传播到组件实例
              </CardDescription>
            </div>
            <Button @click="addParameter" class="bg-blue-600 hover:bg-blue-700">
              <Plus class="w-4 h-4 mr-2" />
              添加参数
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="localParameters.length === 0" class="text-center py-12">
            <Settings class="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无构件参数</h3>
            <p class="text-gray-500">点击"添加参数"按钮开始定义构件参数</p>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="(param, index) in localParameters"
              :key="param.id"
              class="border rounded-lg p-4"
              :class="{
                'border-red-200 bg-red-50': hasParameterError(param.id),
                'border-yellow-200 bg-yellow-50': hasParameterWarning(param.id)
              }"
            >
              <div class="flex items-start justify-between mb-4">
                <div class="flex items-center gap-2">
                  <Badge :variant="getCategoryVariant(param.category)" class="text-xs">
                    {{ getCategoryText(param.category) }}
                  </Badge>
                  <Badge v-if="param.required" variant="destructive" class="text-xs">
                    必需
                  </Badge>
                </div>
                <div class="flex items-center gap-2">
                  <Button
                    @click="duplicateParameter(index)"
                    variant="ghost"
                    size="sm"
                    class="text-gray-500 hover:text-gray-700"
                  >
                    <Copy class="w-4 h-4" />
                  </Button>
                  <Button
                    @click="removeParameter(index)"
                    variant="ghost"
                    size="sm"
                    class="text-red-500 hover:text-red-700"
                  >
                    <Trash2 class="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <!-- 基本信息 -->
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <Label class="text-sm font-medium">
                    参数名称 <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    v-model="param.name"
                    placeholder="参数名称（英文）"
                    @input="validateParameter(param.id)"
                  />
                </div>
                <div>
                  <Label class="text-sm font-medium">
                    显示名称 <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    v-model="param.displayName"
                    placeholder="显示名称（中文）"
                    @input="validateParameter(param.id)"
                  />
                </div>
              </div>

              <div class="grid grid-cols-3 gap-4 mb-4">
                <div>
                  <Label class="text-sm font-medium">参数类型</Label>
                  <Select v-model="param.type" @update:model-value="onTypeChange(param)">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="number">数值</SelectItem>
                      <SelectItem value="string">字符串</SelectItem>
                      <SelectItem value="boolean">布尔值</SelectItem>
                      <SelectItem value="select">选择</SelectItem>
                      <SelectItem value="formula">公式</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label class="text-sm font-medium">参数分类</Label>
                  <Select v-model="param.category">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dimension">尺寸</SelectItem>
                      <SelectItem value="performance">性能</SelectItem>
                      <SelectItem value="material">材料</SelectItem>
                      <SelectItem value="process">工艺</SelectItem>
                      <SelectItem value="other">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label class="text-sm font-medium">单位</Label>
                  <Input
                    v-model="param.unit"
                    placeholder="mm, kg, ℃"
                  />
                </div>
              </div>

              <!-- 默认值和约束 -->
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <Label class="text-sm font-medium">默认值</Label>
                  <Input
                    v-if="param.type === 'string'"
                    v-model="param.defaultValue"
                    placeholder="默认值"
                  />
                  <Input
                    v-else-if="param.type === 'number'"
                    v-model.number="param.defaultValue"
                    type="number"
                    placeholder="默认值"
                  />
                  <Select
                    v-else-if="param.type === 'boolean'"
                    v-model="param.defaultValue"
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择默认值" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem :value="true">是</SelectItem>
                      <SelectItem :value="false">否</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    v-else-if="param.type === 'formula'"
                    v-model="param.formula"
                    placeholder="例: width * height"
                  />
                </div>
                <div class="flex items-center space-x-2 pt-6">
                  <Checkbox v-model="param.required" />
                  <Label class="text-sm">必需参数</Label>
                </div>
              </div>

              <!-- 数值范围（仅数值类型） -->
              <div v-if="param.type === 'number'" class="grid grid-cols-3 gap-4 mb-4">
                <div>
                  <Label class="text-sm font-medium">最小值</Label>
                  <Input
                    v-model.number="param.range.min"
                    type="number"
                    placeholder="最小值"
                  />
                </div>
                <div>
                  <Label class="text-sm font-medium">最大值</Label>
                  <Input
                    v-model.number="param.range.max"
                    type="number"
                    placeholder="最大值"
                  />
                </div>
                <div>
                  <Label class="text-sm font-medium">步长</Label>
                  <Input
                    v-model.number="param.range.step"
                    type="number"
                    placeholder="步长"
                  />
                </div>
              </div>

              <!-- 选项列表（仅选择类型） -->
              <div v-if="param.type === 'select'" class="mb-4">
                <Label class="text-sm font-medium">选项列表</Label>
                <div class="space-y-2 mt-2">
                  <div
                    v-for="(option, optIndex) in param.options"
                    :key="optIndex"
                    class="flex gap-2"
                  >
                    <Input
                      v-model="option.value"
                      placeholder="选项值"
                      class="flex-1"
                    />
                    <Input
                      v-model="option.label"
                      placeholder="显示文本"
                      class="flex-1"
                    />
                    <Button
                      @click="removeOption(param, optIndex)"
                      variant="ghost"
                      size="sm"
                      class="text-red-500"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                  <Button
                    @click="addOption(param)"
                    variant="outline"
                    size="sm"
                    class="w-full"
                  >
                    <Plus class="w-4 h-4 mr-2" />
                    添加选项
                  </Button>
                </div>
              </div>

              <!-- 描述 -->
              <div class="mb-4">
                <Label class="text-sm font-medium">描述</Label>
                <Textarea
                  v-model="param.description"
                  placeholder="参数描述"
                  rows="2"
                />
              </div>

              <!-- 传播规则 -->
              <div>
                <div class="flex items-center justify-between mb-2">
                  <Label class="text-sm font-medium">参数传播规则</Label>
                  <Button
                    @click="addPropagationRule(param)"
                    variant="outline"
                    size="sm"
                  >
                    <Plus class="w-4 h-4 mr-1" />
                    添加规则
                  </Button>
                </div>
                
                <div v-if="param.propagationRules.length === 0" class="text-sm text-gray-500 py-2">
                  暂无传播规则
                </div>
                
                <div v-else class="space-y-2">
                  <div
                    v-for="(rule, ruleIndex) in param.propagationRules"
                    :key="ruleIndex"
                    class="border rounded p-3 bg-gray-50"
                  >
                    <div class="grid grid-cols-3 gap-2 mb-2">
                      <Select v-model="rule.targetInstanceId">
                        <SelectTrigger>
                          <SelectValue placeholder="目标实例" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem
                            v-for="instance in availableInstances"
                            :key="instance.id"
                            :value="instance.id"
                          >
                            {{ instance.instanceName }}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <Input
                        v-model="rule.targetParameterName"
                        placeholder="目标参数名"
                      />
                      
                      <Select v-model="rule.propagationType">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="direct">直接传播</SelectItem>
                          <SelectItem value="formula">公式计算</SelectItem>
                          <SelectItem value="mapping">值映射</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div v-if="rule.propagationType === 'formula'" class="mb-2">
                      <Input
                        v-model="rule.expression"
                        placeholder="公式表达式，例: value * 2"
                      />
                    </div>
                    
                    <div class="flex justify-end">
                      <Button
                        @click="removePropagationRule(param, ruleIndex)"
                        variant="ghost"
                        size="sm"
                        class="text-red-500"
                      >
                        <Trash2 class="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 验证错误 -->
              <div v-if="hasParameterError(param.id)" class="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                <div class="flex items-start gap-2">
                  <AlertCircle class="w-4 h-4 text-red-600 flex-shrink-0 mt-0.5" />
                  <div class="text-sm text-red-700">
                    {{ getParameterError(param.id) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 参数依赖关系 -->
      <Card v-if="localParameters.length > 0">
        <CardHeader>
          <CardTitle class="text-lg">参数依赖关系</CardTitle>
          <CardDescription>
            显示参数之间的依赖关系，检查循环依赖
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div v-if="circularDependencies.length > 0" class="mb-4 p-3 bg-red-50 border border-red-200 rounded">
            <div class="flex items-start gap-2">
              <AlertCircle class="w-4 h-4 text-red-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 class="text-sm font-medium text-red-800">检测到循环依赖</h4>
                <ul class="text-sm text-red-700 mt-1">
                  <li v-for="dep in circularDependencies" :key="dep">{{ dep }}</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <div
              v-for="(deps, paramName) in parameterDependencies"
              :key="paramName"
              class="flex items-center gap-2 text-sm"
            >
              <span class="font-medium">{{ paramName }}:</span>
              <span v-if="deps.length === 0" class="text-gray-500">无依赖</span>
              <div v-else class="flex gap-1">
                <Badge v-for="dep in deps" :key="dep" variant="outline" class="text-xs">
                  {{ dep }}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  Plus,
  Copy,
  Trash2,
  X,
  Settings,
  AlertCircle
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import type {
  AssemblyParameter,
  ComponentInstance,
  ParameterOption
} from '@/types/product-structure';

import { assemblyParameterService } from '@/services/assemblyParameterService';

// Props
interface Props {
  parameters: AssemblyParameter[];
  availableInstances?: ComponentInstance[];
}

// Emits
interface Emits {
  (e: 'update', parameters: AssemblyParameter[]): void;
  (e: 'validate', isValid: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  availableInstances: () => []
});
const emit = defineEmits<Emits>();

// 响应式数据
const localParameters = ref<AssemblyParameter[]>([...props.parameters]);
const validationErrors = ref<Record<string, string>>({});

// 计算属性
const parameterDependencies = computed(() => {
  return assemblyParameterService.getParameterDependencies(localParameters.value);
});

const circularDependencies = computed(() => {
  return assemblyParameterService.checkCircularDependencies(localParameters.value);
});

// 方法
const addParameter = () => {
  const newParam = assemblyParameterService.createAssemblyParameter({
    name: `param_${localParameters.value.length + 1}`,
    displayName: `参数 ${localParameters.value.length + 1}`,
    type: 'string',
    category: 'other',
    defaultValue: '',
    required: false,
    range: {},
    options: [],
    propagationRules: []
  });
  
  localParameters.value.push(newParam);
  updateParameters();
};

const removeParameter = (index: number) => {
  localParameters.value.splice(index, 1);
  updateParameters();
};

const duplicateParameter = (index: number) => {
  const original = localParameters.value[index];
  const duplicate = assemblyParameterService.createAssemblyParameter({
    ...original,
    name: `${original.name}_copy`,
    displayName: `${original.displayName} 副本`
  });
  
  localParameters.value.splice(index + 1, 0, duplicate);
  updateParameters();
};

const onTypeChange = (param: AssemblyParameter) => {
  // 根据类型重置相关字段
  if (param.type === 'number') {
    if (!param.range) {
      param.range = {};
    }
  } else if (param.type === 'select') {
    if (!param.options) {
      param.options = [];
    }
  } else if (param.type === 'formula') {
    if (!param.formula) {
      param.formula = '';
    }
  }
  
  updateParameters();
};

const addOption = (param: AssemblyParameter) => {
  if (!param.options) {
    param.options = [];
  }
  param.options.push({ value: '', label: '' });
  updateParameters();
};

const removeOption = (param: AssemblyParameter, index: number) => {
  if (param.options) {
    param.options.splice(index, 1);
    updateParameters();
  }
};

const addPropagationRule = (param: AssemblyParameter) => {
  param.propagationRules.push({
    targetInstanceId: '',
    targetParameterName: '',
    propagationType: 'direct'
  });
  updateParameters();
};

const removePropagationRule = (param: AssemblyParameter, index: number) => {
  param.propagationRules.splice(index, 1);
  updateParameters();
};

const validateParameter = (paramId: string) => {
  const param = localParameters.value.find(p => p.id === paramId);
  if (!param) return;

  let error = '';

  if (!param.name || param.name.trim() === '') {
    error = '参数名称不能为空';
  } else if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(param.name)) {
    error = '参数名称只能包含字母、数字和下划线，且不能以数字开头';
  } else if (!param.displayName || param.displayName.trim() === '') {
    error = '显示名称不能为空';
  }

  // 检查名称唯一性
  const duplicates = localParameters.value.filter(p => p.name === param.name && p.id !== param.id);
  if (duplicates.length > 0) {
    error = '参数名称重复';
  }

  if (error) {
    validationErrors.value[paramId] = error;
  } else {
    delete validationErrors.value[paramId];
  }

  emit('validate', Object.keys(validationErrors.value).length === 0 && circularDependencies.value.length === 0);
};

const hasParameterError = (paramId: string) => {
  return !!validationErrors.value[paramId];
};

const hasParameterWarning = (paramId: string) => {
  // 可以添加警告逻辑
  return false;
};

const getParameterError = (paramId: string) => {
  return validationErrors.value[paramId];
};

const getCategoryVariant = (category: string) => {
  const variantMap = {
    dimension: 'default',
    performance: 'secondary',
    material: 'outline',
    process: 'destructive',
    other: 'secondary'
  };
  return variantMap[category] || 'secondary';
};

const getCategoryText = (category: string) => {
  const textMap = {
    dimension: '尺寸',
    performance: '性能',
    material: '材料',
    process: '工艺',
    other: '其他'
  };
  return textMap[category] || '其他';
};

const updateParameters = () => {
  emit('update', localParameters.value);
  
  // 验证所有参数
  localParameters.value.forEach(param => {
    validateParameter(param.id);
  });
};

// 监听props变化
watch(() => props.parameters, (newParams) => {
  localParameters.value = [...newParams];
}, { deep: true });

// 监听循环依赖
watch(circularDependencies, (deps) => {
  emit('validate', Object.keys(validationErrors.value).length === 0 && deps.length === 0);
});
</script>
