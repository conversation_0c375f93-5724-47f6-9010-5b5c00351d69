<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[95vw] h-[95vh] max-w-none max-h-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Layers class="w-5 h-5" />
          {{ isEditing ? '编辑构件' : '新建构件' }}
          <Badge v-if="formData.assemblyType" :variant="getTypeVariant(formData.assemblyType)">
            {{ getTypeText(formData.assemblyType) }}
          </Badge>
        </DialogTitle>
        <DialogDescription>
          {{ isEditing ? '设计产品结构 - 定义组件组合关系和参数透传配置，为工艺设计师提供清晰的结构基础' : '创建产品结构 - 通过组合组件和配置参数透传来构建完整的产品结构' }}
        </DialogDescription>
      </DialogHeader>

      <!-- 验证状态栏 -->
      <div v-if="validationResult" class="px-6 py-2 border-b">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <CheckCircle v-if="validationResult.isValid" class="w-4 h-4 text-green-600" />
            <AlertCircle v-else class="w-4 h-4 text-red-600" />
            <span class="text-sm font-medium">
              验证状态: {{ validationResult.isValid ? '通过' : '失败' }}
            </span>
            <Badge variant="outline" class="text-xs">
              {{ validationResult.summary.passedChecks }}/{{ validationResult.summary.totalChecks }} 项通过
            </Badge>
          </div>
          <Button @click="validateAssembly" variant="outline" size="sm">
            <RefreshCw class="w-4 h-4 mr-1" />
            重新验证
          </Button>
        </div>
      </div>

      <div class="flex-1 overflow-hidden">
        <Tabs v-model="activeTab" class="h-full flex flex-col">
          <TabsList class="grid w-full grid-cols-4">
            <TabsTrigger value="basic" class="flex items-center gap-2">
              <Info class="w-4 h-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="structure" class="flex items-center gap-2">
              <Package class="w-4 h-4" />
              结构设计
              <Badge v-if="formData.componentInstances?.length" variant="secondary" class="text-xs">
                {{ formData.componentInstances.length }}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="parameters" class="flex items-center gap-2">
              <Settings class="w-4 h-4" />
              参数配置
            </TabsTrigger>
            <TabsTrigger value="preview" class="flex items-center gap-2">
              <CheckCircle class="w-4 h-4" />
              结构预览
            </TabsTrigger>
          </TabsList>

          <div class="flex-1 overflow-hidden flex">
            <!-- 基本信息 -->
            <TabsContent value="basic" class="flex-1 overflow-hidden">
              <div class="h-full flex">
                <!-- 左侧表单 -->
                <div class="flex-1 overflow-y-auto p-6">
                  <div class="space-y-6">
                    <!-- 设计指南 -->
                    <AssemblyDesignGuide />

                    <!-- 基本信息卡片 -->
                    <Card>
                      <CardHeader>
                        <CardTitle class="text-lg">基本信息</CardTitle>
                        <CardDescription>
                          定义构件的基本属性和分类
                        </CardDescription>
                      </CardHeader>
                      <CardContent class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                          <div>
                            <Label class="text-sm font-medium">
                              构件名称 <span class="text-red-500">*</span>
                            </Label>
                            <Input
                              v-model="formData.name"
                              placeholder="请输入构件名称"
                              :class="{ 'border-red-500': hasError('name') }"
                              @blur="validateField('name')"
                            />
                            <p v-if="hasError('name')" class="text-sm text-red-600 mt-1">
                              {{ getError('name') }}
                            </p>
                          </div>
                          <div>
                            <Label class="text-sm font-medium">
                              构件编码 <span class="text-red-500">*</span>
                            </Label>
                            <Input
                              v-model="formData.code"
                              placeholder="请输入构件编码"
                              :class="{ 'border-red-500': hasError('code') }"
                              @blur="validateField('code')"
                            />
                            <p v-if="hasError('code')" class="text-sm text-red-600 mt-1">
                              {{ getError('code') }}
                            </p>
                          </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                          <div>
                            <Label class="text-sm font-medium">构件类型</Label>
                            <Select v-model="formData.assemblyType" @update:model-value="onAssemblyTypeChange">
                              <SelectTrigger>
                                <SelectValue placeholder="选择构件类型" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="frame_assembly">框架构件</SelectItem>
                                <SelectItem value="glass_assembly">玻璃构件</SelectItem>
                                <SelectItem value="hardware_assembly">五金构件</SelectItem>
                                <SelectItem value="complete_assembly">完整构件</SelectItem>
                              </SelectContent>
                            </Select>
                            <!-- 参数模板提示 -->
                            <div v-if="formData.assemblyType && !isEditing" class="mt-2">
                              <Button
                                @click="showParameterTemplate = true"
                                size="sm"
                                variant="outline"
                                class="text-xs"
                              >
                                <LayoutTemplate class="w-3 h-3 mr-1" />
                                使用参数模板
                              </Button>
                            </div>
                          </div>
                          <div>
                            <Label class="text-sm font-medium">状态</Label>
                            <Select v-model="formData.status">
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="draft">草稿</SelectItem>
                                <SelectItem value="active">激活</SelectItem>
                                <SelectItem value="inactive">停用</SelectItem>
                                <SelectItem value="deprecated">已弃用</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div>
                          <Label class="text-sm font-medium">描述</Label>
                          <Textarea
                            v-model="formData.description"
                            placeholder="请输入构件描述"
                            rows="3"
                          />
                        </div>

                        <div>
                          <Label class="text-sm font-medium">标签</Label>
                          <TagsInput
                            v-model="formData.tags"
                            placeholder="添加标签"
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <!-- 右侧预览 -->
                <div class="w-80 border-l bg-gray-50 p-4 overflow-y-auto">
                  <h3 class="font-medium mb-4">构件预览</h3>
                  <AssemblyPreview :assembly="formData" />
                </div>
              </div>
            </TabsContent>

            <!-- 结构设计 -->
            <TabsContent value="structure" class="flex-1 overflow-hidden">
              <div class="h-full flex">
                <!-- 左侧：组件管理 -->
                <div class="flex-1 flex flex-col border-r">
                  <div class="flex items-center justify-between p-4 border-b bg-gray-50">
                    <div>
                      <h3 class="font-medium">产品结构设计</h3>
                      <p class="text-sm text-gray-600">定义产品的组件组合关系，为工艺设计师提供清晰的结构基础</p>
                    </div>
                    <div class="flex items-center gap-2">
                      <Badge variant="outline" class="text-sm">
                        {{ (formData.componentInstances || []).length }} 个组件
                      </Badge>
                      <Button @click="showComponentSelector = true" class="bg-blue-600 hover:bg-blue-700">
                        <Plus class="w-4 h-4 mr-2" />
                        添加组件
                      </Button>
                    </div>
                  </div>

                  <div class="flex-1 overflow-y-auto p-4">
                    <div v-if="formData.componentInstances?.length === 0" class="text-center py-12">
                      <Package class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无组件</h3>
                      <p class="text-gray-600 mb-4">开始添加组件来构建产品结构</p>
                      <Button @click="showComponentSelector = true" variant="outline">
                        <Plus class="w-4 h-4 mr-2" />
                        添加第一个组件
                      </Button>
                    </div>

                    <div v-else class="space-y-3">
                      <div
                        v-for="instance in formData.componentInstances"
                        :key="instance.id"
                        class="bg-white border rounded-lg p-4 hover:shadow-sm transition-shadow"
                      >
                        <div class="flex items-center justify-between mb-3">
                          <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <Package class="w-4 h-4 text-blue-600" />
                            </div>
                            <div>
                              <h4 class="font-medium">{{ instance.instanceName }}</h4>
                              <p class="text-sm text-gray-600">{{ instance.componentId }}</p>
                            </div>
                          </div>
                          <div class="flex items-center gap-2">
                            <Badge variant="outline" class="text-xs">
                              实例ID: {{ instance.id.slice(-8) }}
                            </Badge>
                            <Button
                              @click="removeComponentInstance(instance.id)"
                              size="sm"
                              variant="ghost"
                              class="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <X class="w-4 h-4" />
                            </Button>
                          </div>
                        </div>

                        <div v-if="instance.description" class="text-sm text-gray-600 mb-3">
                          {{ instance.description }}
                        </div>

                        <div class="text-xs text-gray-500">
                          组件ID: {{ instance.componentId }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 右侧：结构预览 -->
                <div class="w-80 bg-gray-50 p-4 overflow-y-auto">
                  <h3 class="font-medium mb-4">结构预览</h3>
                  <AssemblyPreview :assembly="formData" />
                </div>
              </div>
              <div class="h-full flex flex-col">
                <div class="flex items-center justify-between p-4 border-b bg-gray-50">
                  <div>
                    <h3 class="font-medium">组件集合管理</h3>
                    <p class="text-sm text-gray-600">构件是组件的集合体，通过添加和配置组件实例来构建完整的产品单元</p>
                  </div>
                  <div class="flex items-center gap-2">
                    <Badge variant="outline" class="text-sm">
                      {{ (formData.componentInstances || []).length }} 个组件实例
                    </Badge>
                    <Button @click="showComponentSelector = true" class="bg-blue-600 hover:bg-blue-700">
                      <Plus class="w-4 h-4 mr-2" />
                      添加组件
                    </Button>
                  </div>
                </div>
                
                <div class="flex-1 overflow-y-auto p-4">
                  <div v-if="(formData.componentInstances || []).length === 0" class="text-center py-12">
                    <Package class="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暂无组件实例</h3>
                    <p class="text-gray-500">点击"添加组件"按钮开始构建组件集合</p>
                  </div>

                  <div v-else class="space-y-4">
                    <div
                      v-for="instance in formData.componentInstances"
                      :key="instance.id"
                      class="border rounded-lg p-4 bg-white"
                    >
                      <div class="flex items-start justify-between mb-3">
                        <div>
                          <h4 class="font-medium">{{ instance.instanceName }}</h4>
                          <p class="text-sm text-gray-600">{{ instance.componentName }} ({{ instance.componentCode }})</p>
                        </div>
                        <div class="flex items-center gap-2">
                          <Badge :variant="instance.status === 'active' ? 'default' : 'secondary'" class="text-xs">
                            {{ instance.status === 'active' ? '激活' : '停用' }}
                          </Badge>
                          <Button @click="editComponentInstance(instance)" variant="outline" size="sm">
                            编辑
                          </Button>
                          <Button @click="removeComponentInstance(instance.id)" variant="ghost" size="sm" class="text-red-500">
                            <X class="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      <div class="grid grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>数量: {{ instance.quantityConfig?.fixedQuantity || 1 }}{{ instance.quantityConfig?.unit || '个' }}</div>
                        <div>参数覆盖: {{ Object.keys(instance.parameterConfig?.parameterOverrides || {}).length }}项</div>
                        <div>{{ instance.optional ? '可选组件' : '必需组件' }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <!-- 参数配置 -->
            <TabsContent value="parameters" class="flex-1 overflow-hidden">
              <div class="h-full overflow-y-auto p-6">
                <!-- 参数完整性检查 -->
                <ParameterCompletenessChecker
                  v-if="formData.assemblyType"
                  :assembly-type="formData.assemblyType"
                  :current-parameters="formData.assemblyParameters || []"
                  :selected-components="selectedComponents"
                  :completeness-result="parameterCompletenessResult"
                  @add-parameter="addParameter"
                  @add-parameters="addParameters"
                  @recheck="checkParameterCompleteness"
                  @open-template="showParameterTemplate = true"
                  class="mb-6"
                />

                <!-- 参数透传配置 -->
                <div class="bg-white border rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <div>
                      <h3 class="text-lg font-medium">参数透传配置</h3>
                      <p class="text-sm text-gray-600">定义构件参数如何透传到组件实例，确保参数在结构中正确传递</p>
                    </div>
                    <Button
                      @click="showParameterTemplate = true"
                      variant="outline"
                      class="flex items-center gap-2"
                    >
                      <LayoutTemplate class="w-4 h-4" />
                      参数模板
                    </Button>
                  </div>

                  <AssemblyParameterEditor
                    :parameters="convertComponentToAssemblyParameters(formData.assemblyParameters || [])"
                    :available-instances="formData.componentInstances || []"
                    @update="updateAssemblyParameters"
                    @validate="validateAssemblyParameters"
                  />
                </div>

                <!-- 参数映射可视化 -->
                <div class="mt-6">
                  <ParameterMappingVisualizer
                    :assembly-parameters="formData.assemblyParameters || []"
                    :component-instances="formData.componentInstances || []"
                  />
                </div>
              </div>
            </TabsContent>

            <!-- 结构预览 -->
            <TabsContent value="preview" class="flex-1 overflow-hidden">
              <div class="h-full overflow-y-auto p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                  <!-- 结构树 -->
                  <div class="bg-white border rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h3 class="text-lg font-medium">产品结构树</h3>
                      <Badge variant="outline" class="text-sm">
                        {{ (formData.componentInstances || []).length }} 个组件
                      </Badge>
                    </div>
                    <AssemblyPreview :assembly="formData" />
                  </div>

                  <!-- 参数汇总 -->
                  <div class="bg-white border rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h3 class="text-lg font-medium">参数配置汇总</h3>
                      <Badge variant="outline" class="text-sm">
                        {{ (formData.assemblyParameters || []).length }} 个参数
                      </Badge>
                    </div>

                    <div class="space-y-3 max-h-96 overflow-y-auto">
                      <div v-if="(formData.assemblyParameters || []).length === 0" class="text-center py-8 text-gray-500">
                        <Settings class="w-8 h-8 mx-auto mb-2 text-gray-400" />
                        <p>暂无构件参数</p>
                        <p class="text-sm">在"参数配置"标签页中添加参数</p>
                      </div>
                      <div v-else>
                        <div
                          v-for="param in formData.assemblyParameters"
                          :key="param.id"
                          class="flex items-center justify-between p-3 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
                        >
                          <div class="flex-1 min-w-0">
                            <div class="font-medium truncate">{{ param.displayName }}</div>
                            <div class="text-sm text-gray-600 truncate">{{ param.name }}</div>
                            <div v-if="param.description" class="text-xs text-gray-500 mt-1 line-clamp-2">
                              {{ param.description }}
                            </div>
                          </div>
                          <div class="flex flex-col items-end gap-1 ml-3">
                            <Badge variant="outline" class="text-xs">
                              {{ getParameterTypeText(param.type) }}
                            </Badge>
                            <Badge v-if="param.required" variant="destructive" class="text-xs">
                              必需
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      <DialogFooter class="flex items-center justify-between">
        <!-- 验证状态 -->
        <div class="flex items-center gap-2">
          <div v-if="hasValidationErrors" class="flex items-center gap-2 text-red-600">
            <AlertCircle class="w-4 h-4" />
            <span class="text-sm">存在 {{ validationErrorCount }} 个错误</span>
          </div>
          <div v-else-if="hasValidationWarnings" class="flex items-center gap-2 text-yellow-600">
            <AlertTriangle class="w-4 h-4" />
            <span class="text-sm">存在 {{ validationWarningCount }} 个警告</span>
          </div>
          <div v-else class="flex items-center gap-2 text-green-600">
            <CheckCircle class="w-4 h-4" />
            <span class="text-sm">验证通过</span>
          </div>
        </div>
        
        <div class="flex items-center gap-2">
          <Button @click="$emit('update:open', false)" variant="outline">
            取消
          </Button>
          <Button @click="saveAsDraft" variant="outline" :disabled="saving">
            保存草稿
          </Button>
          <Button 
            @click="saveAssembly" 
            :disabled="hasValidationErrors || saving"
            class="bg-blue-600 hover:bg-blue-700"
          >
            <Save class="w-4 h-4 mr-2" />
            {{ saving ? '保存中...' : (isEditing ? '更新构件' : '创建构件') }}
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>

    <!-- 组件选择器对话框 -->
    <ComponentSelectorDialog
      :open="showComponentSelector"
      @update:open="showComponentSelector = $event"
      @select="addComponentInstance"
    />

    <!-- 参数模板对话框 -->
    <AssemblyParameterTemplateDialog
      :open="showParameterTemplate"
      :assembly-type="formData.assemblyType"
      @update:open="showParameterTemplate = $event"
      @apply="applyParameterTemplate"
    />
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import {
  Plus,
  Save,
  AlertTriangle,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Layers,
  Info,
  Settings,
  Package,
  // Workflow,
  LayoutTemplate,
  X
} from 'lucide-vue-next';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import AssemblyParameterEditor from './AssemblyParameterEditor.vue';
// import AssemblyProcessEditor from './AssemblyProcessEditor.vue';
import AssemblyPreview from './AssemblyPreview.vue';
import ComponentSelectorDialog from './ComponentSelectorDialog.vue';
import AssemblyParameterTemplateDialog from './AssemblyParameterTemplateDialog.vue';
import ParameterCompletenessChecker from './ParameterCompletenessChecker.vue';
import ParameterMappingVisualizer from './ParameterMappingVisualizer.vue';
import AssemblyDesignGuide from './AssemblyDesignGuide.vue';
import { TagsInput } from '@/components/ui/tags-input';

import type {
  Assembly,
  ComponentInstance,
  AssemblyParameter,
  AssemblyType,
  SimpleValidationItem,
  Component,
  ComponentParameter
} from '@/types/product-structure';

import { assemblyService } from '@/services/assemblyService';
import { type AssemblyValidationResult } from '@/services/assemblyValidationService';
import { assemblyTemplateService, type ParameterCompletenessResult } from '@/services/assemblyTemplateService';
import { componentService } from '@/services/componentService';

// Props
interface Props {
  open: boolean;
  assembly?: Assembly | null;
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'save', assembly: Assembly): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const activeTab = ref('basic');
const saving = ref(false);
const showComponentSelector = ref(false);
const showParameterTemplate = ref(false);
const validationResult = ref<AssemblyValidationResult | null>(null);
const parameterCompletenessResult = ref<ParameterCompletenessResult | null>(null);
const selectedComponents = ref<Component[]>([]);

// 表单数据
const formData = ref<Partial<Assembly>>({
  name: '',
  code: '',
  description: '',
  assemblyType: 'complete_assembly',
  componentInstances: [],
  subAssemblies: [],
  assemblyParameters: [],
  assemblyConstraints: [],
  assemblyProcess: {
    id: '',
    processName: '',
    description: '',
    steps: [],
    totalEstimatedTime: 0,
    requiredSkills: [],
    safetyRequirements: []
  },
  qualityRequirements: [],
  properties: {},
  tags: [],
  status: 'draft'
});

// 验证结果
const validationErrors = ref<Record<string, SimpleValidationItem[]>>({});

// 计算属性
const isEditing = computed(() => !!props.assembly);

const allValidationResults = computed(() => {
  const results: SimpleValidationItem[] = [];
  Object.values(validationErrors.value).forEach(errors => {
    results.push(...errors);
  });
  return results;
});

const hasValidationErrors = computed(() => {
  return allValidationResults.value.some(result => result.type === 'error');
});

const hasValidationWarnings = computed(() => {
  return allValidationResults.value.some(result => result.type === 'warning');
});

const validationErrorCount = computed(() => {
  return allValidationResults.value.filter(result => result.type === 'error').length;
});

const validationWarningCount = computed(() => {
  return allValidationResults.value.filter(result => result.type === 'warning').length;
});

// 方法
const initializeForm = () => {
  if (props.assembly) {
    formData.value = { ...props.assembly };
  } else {
    formData.value = {
      name: '',
      code: '',
      description: '',
      assemblyType: 'complete_assembly',
      componentInstances: [],
      subAssemblies: [],
      assemblyParameters: [],
      assemblyConstraints: [],
      // 移除工艺相关内容，专注于结构设计
      qualityRequirements: [],
      properties: {},
      tags: [],
      status: 'draft'
    };
  }
  
  // 清空验证错误
  validationErrors.value = {};
};



// const validateProcess = (errors: SimpleValidationItem[]) => {
//   validationErrors.value.process = errors;
// };

// 参数类型文本映射
const getParameterTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'string': '文本',
    'number': '数值',
    'boolean': '布尔',
    'select': '选择',
    'multiselect': '多选',
    'date': '日期',
    'datetime': '日期时间',
    'file': '文件',
    'color': '颜色',
    'range': '范围',
    'json': 'JSON',
    'array': '数组',
    'object': '对象'
  };
  return typeMap[type] || type;
};

// 组件实例管理方法
const editComponentInstance = (_instance: ComponentInstance) => {
  // 这里可以打开组件实例编辑对话框
  // TODO: 实现组件实例编辑功能
};

const removeComponentInstance = (instanceId: string) => {
  if (formData.value.componentInstances) {
    formData.value.componentInstances = formData.value.componentInstances.filter(
      instance => instance.id !== instanceId
    );
  }
};

// 类型转换函数
const convertAssemblyToComponentParameters = (assemblyParams: AssemblyParameter[]): ComponentParameter[] => {
  return assemblyParams.map(param => ({
    id: param.id,
    name: param.name,
    displayName: param.displayName,
    type: param.type,
    unit: param.unit,
    defaultValue: param.defaultValue,
    minValue: param.range?.min,
    maxValue: param.range?.max,
    options: param.options,
    required: param.required,
    description: param.description,
    category: param.category as any, // 类型转换
    visible: true,
    editable: true,
    validationRules: []
  }));
};

const convertComponentToAssemblyParameters = (componentParams: ComponentParameter[]): AssemblyParameter[] => {
  return componentParams.map(param => ({
    id: param.id,
    name: param.name,
    displayName: param.displayName,
    type: param.type as 'number' | 'string' | 'boolean' | 'select' | 'formula',
    category: mapParameterCategory(param.category),
    defaultValue: param.defaultValue,
    unit: param.unit,
    required: param.required,
    description: param.description,
    range: param.minValue !== undefined || param.maxValue !== undefined ? {
      min: param.minValue,
      max: param.maxValue
    } : undefined,
    options: param.options,
    formula: undefined,
    affectedInstances: [],
    propagationRules: []
  }));
};

const updateAssemblyParameters = (parameters: AssemblyParameter[]) => {
  // 转换AssemblyParameter为ComponentParameter存储
  formData.value.assemblyParameters = convertAssemblyToComponentParameters(parameters);
};

const validateAssemblyParameters = (isValid: boolean) => {
  if (isValid) {
    validationErrors.value.assemblyParams = [];
  } else {
    validationErrors.value.assemblyParams = [
      { type: 'error', message: '构件参数配置有误' }
    ];
  }
};

const getTypeVariant = (type: AssemblyType): "default" | "destructive" | "outline" | "secondary" => {
  const variantMap: Record<AssemblyType, "default" | "destructive" | "outline" | "secondary"> = {
    frame_assembly: 'default',
    glass_assembly: 'secondary',
    hardware_assembly: 'outline',
    complete_assembly: 'destructive'
  };
  return variantMap[type] || 'secondary';
};

const getTypeText = (type: AssemblyType) => {
  const textMap = {
    frame_assembly: '框架构件',
    glass_assembly: '玻璃构件',
    hardware_assembly: '五金构件',
    complete_assembly: '完整构件'
  };
  return textMap[type] || '未知类型';
};

const hasError = (field: string) => {
  return validationErrors.value[field]?.some(error => error.type === 'error') || false;
};

const getError = (field: string) => {
  const error = validationErrors.value[field]?.find(error => error.type === 'error');
  return error?.message || '';
};

const validateField = (field: string) => {
  const errors: SimpleValidationItem[] = [];

  switch (field) {
    case 'name':
      if (!formData.value.name || formData.value.name.trim() === '') {
        errors.push({ type: 'error', message: '构件名称不能为空', field });
      }
      break;
    case 'code':
      if (!formData.value.code || formData.value.code.trim() === '') {
        errors.push({ type: 'error', message: '构件编码不能为空', field });
      } else if (!/^[A-Z0-9_]+$/.test(formData.value.code)) {
        errors.push({ type: 'error', message: '构件编码只能包含大写字母、数字和下划线', field });
      }
      break;
  }

  validationErrors.value[field] = errors;
};

const validateAssembly = async () => {
  if (formData.value.id) {
    try {
      validationResult.value = await assemblyService.validateAssembly(formData.value.id);
    } catch (error) {
      console.error('验证构件失败:', error);
    }
  }
};

const addComponentInstance = (component: any) => {
  const newInstance: ComponentInstance = {
    id: `ci_${Date.now()}`,
    componentId: component.id,
    componentCode: component.code,
    componentName: component.name,
    instanceName: `${component.name}_实例`,
    description: '',

    parameterConfig: {
      parameterOverrides: {},
      parameterBindings: {},
      parameterFormulas: {},
      validationStatus: {}
    },

    quantityConfig: {
      fixedQuantity: 1,
      minQuantity: 1,
      unit: '个'
    },

    positionConfig: {
      position: { x: 0, y: 0, z: 0 },
      constraints: []
    },

    constraintConfig: {
      relationshipConstraints: [],
      parameterConstraints: [],
      geometryConstraints: []
    },

    optional: false,
    alternatives: [],
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  formData.value.componentInstances = formData.value.componentInstances || [];
  formData.value.componentInstances.push(newInstance);
  showComponentSelector.value = false;

  // 切换到组件实例标签页
  activeTab.value = 'components';

  // 重新检查参数完整性
  checkParameterCompleteness();
};



// 构件类型变化处理
const onAssemblyTypeChange = (_newType: AssemblyType) => {
  // 如果是新建构件且还没有参数，提示使用模板
  if (!isEditing.value && (!formData.value.assemblyParameters || formData.value.assemblyParameters.length === 0)) {
    // 延迟显示模板对话框，让用户选择是否使用模板
    nextTick(() => {
      checkParameterCompleteness();
    });
  }
};

// 参数分类映射函数
const mapParameterCategory = (category: string): 'dimension' | 'performance' | 'material' | 'process' | 'other' => {
  const categoryMap: Record<string, 'dimension' | 'performance' | 'material' | 'process' | 'other'> = {
    'dimensions': 'dimension',
    'performance': 'performance',
    'material': 'material',
    'process': 'process',
    'quality': 'other',
    'basic': 'other'
  };
  return categoryMap[category] || 'other';
};

// 检查参数完整性
const checkParameterCompleteness = async () => {
  if (!formData.value.assemblyType) return;

  try {
    // 获取当前选择的组件
    const components = await Promise.all(
      (formData.value.componentInstances || []).map(async (instance) => {
        return await componentService.getComponentById(instance.componentId);
      })
    );

    selectedComponents.value = components.filter(Boolean) as Component[];

    // 检查参数完整性
    parameterCompletenessResult.value = assemblyTemplateService.checkParameterCompleteness(
      formData.value.assemblyType,
      formData.value.assemblyParameters || [],
      selectedComponents.value
    );
  } catch (error) {
    console.error('检查参数完整性失败:', error);
  }
};

// 应用参数模板
const applyParameterTemplate = (parameters: ComponentParameter[], _allowCustomization: boolean) => {
  // 直接使用ComponentParameter，添加必要的属性
  const assemblyParameters: ComponentParameter[] = parameters.map(param => ({
    ...param,
    id: param.id || `param_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    visible: param.visible !== undefined ? param.visible : true,
    editable: param.editable !== undefined ? param.editable : true
  }));

  formData.value.assemblyParameters = assemblyParameters;

  // 切换到参数配置标签页
  activeTab.value = 'assembly-params';

  // 重新检查完整性
  checkParameterCompleteness();
};

// 添加单个参数
const addParameter = (parameter: ComponentParameter) => {
  const assemblyParameter: ComponentParameter = {
    ...parameter,
    id: parameter.id || `param_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    visible: parameter.visible !== undefined ? parameter.visible : true,
    editable: parameter.editable !== undefined ? parameter.editable : true
  };

  formData.value.assemblyParameters = formData.value.assemblyParameters || [];
  formData.value.assemblyParameters.push(assemblyParameter);

  // 重新检查完整性
  checkParameterCompleteness();
};

// 批量添加参数
const addParameters = (parameters: ComponentParameter[]) => {
  const assemblyParameters: ComponentParameter[] = parameters.map(param => ({
    ...param,
    id: param.id || `param_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    visible: param.visible !== undefined ? param.visible : true,
    editable: param.editable !== undefined ? param.editable : true
  }));

  formData.value.assemblyParameters = formData.value.assemblyParameters || [];
  formData.value.assemblyParameters.push(...assemblyParameters);

  // 重新检查完整性
  checkParameterCompleteness();
};

const saveAsDraft = async () => {
  formData.value.status = 'draft';
  await saveAssembly();
};

const saveAssembly = async () => {
  if (hasValidationErrors.value) {
    return;
  }

  saving.value = true;
  try {
    emit('save', formData.value as Assembly);
  } catch (error) {
    console.error('保存构件失败:', error);
  } finally {
    saving.value = false;
  }
};

// 监听props变化
watch(() => props.open, (open) => {
  if (open) {
    initializeForm();
    activeTab.value = 'basic';
    // 延迟检查参数完整性，确保表单数据已初始化
    nextTick(() => {
      if (formData.value.assemblyType) {
        checkParameterCompleteness();
      }
    });
  }
});

watch(() => props.assembly, () => {
  if (props.open) {
    initializeForm();
  }
});
</script>
