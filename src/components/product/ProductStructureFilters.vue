<template>
  <div class="product-structure-filters">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      <!-- 关键词搜索 -->
      <div class="space-y-2">
        <Label for="search">关键词搜索</Label>
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            id="search"
            v-model="search"
            placeholder="搜索名称、编码、描述..."
            class="pl-10"
            @keyup.enter="$emit('search')"
          />
        </div>
      </div>

      <!-- 产品类型筛选 -->
      <div class="space-y-2">
        <Label for="product-type">产品类型</Label>
        <Select v-model="selectedProductType" @update:model-value="handleProductTypeChange">
          <SelectTrigger>
            <SelectValue placeholder="选择产品类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="partition">隔断</SelectItem>
            <SelectItem value="window">窗户</SelectItem>
            <SelectItem value="door">门</SelectItem>
            <SelectItem value="curtain_wall">幕墙</SelectItem>
            <SelectItem value="other">其他</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 状态筛选 -->
      <div class="space-y-2">
        <Label for="status">状态</Label>
        <Select v-model="selectedStatus" @update:model-value="handleStatusChange">
          <SelectTrigger>
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="active">活跃</SelectItem>
            <SelectItem value="deprecated">已弃用</SelectItem>
            <SelectItem value="archived">已归档</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 类别筛选 -->
      <div class="space-y-2">
        <Label for="category">产品类别</Label>
        <Select v-model="selectedCategory" @update:model-value="handleCategoryChange">
          <SelectTrigger>
            <SelectValue placeholder="选择类别" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类别</SelectItem>
            <SelectItem value="防火窗">防火窗</SelectItem>
            <SelectItem value="防火隔断">防火隔断</SelectItem>
            <SelectItem value="玻璃幕墙">玻璃幕墙</SelectItem>
            <SelectItem value="铝合金门窗">铝合金门窗</SelectItem>
            <SelectItem value="钢质门窗">钢质门窗</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <!-- 高级筛选 -->
    <div class="border-t pt-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-gray-700">高级筛选</h3>
        <Button
          variant="ghost"
          size="sm"
          @click="showAdvancedFilters = !showAdvancedFilters"
        >
          {{ showAdvancedFilters ? '收起' : '展开' }}
        </Button>
      </div>

      <div v-if="showAdvancedFilters" class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- 创建时间范围 -->
        <div class="space-y-2">
          <Label>创建时间范围</Label>
          <div class="flex gap-2">
            <Input
              v-model="filters.createdDateRange.start"
              type="date"
              placeholder="开始日期"
              @change="$emit('filter')"
            />
            <Input
              v-model="filters.createdDateRange.end"
              type="date"
              placeholder="结束日期"
              @change="$emit('filter')"
            />
          </div>
        </div>

        <!-- 创建人筛选 -->
        <div class="space-y-2">
          <Label for="created-by">创建人</Label>
          <Select v-model="selectedCreatedBy" @update:model-value="handleCreatedByChange">
            <SelectTrigger>
              <SelectValue placeholder="选择创建人" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部创建人</SelectItem>
              <SelectItem value="engineer_001">张工程师</SelectItem>
              <SelectItem value="engineer_002">李工程师</SelectItem>
              <SelectItem value="engineer_003">王工程师</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- 应用场景筛选 -->
        <div class="space-y-2">
          <Label for="applications">应用场景</Label>
          <Select v-model="selectedApplication" @update:model-value="handleApplicationChange">
            <SelectTrigger>
              <SelectValue placeholder="选择应用场景" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部场景</SelectItem>
              <SelectItem value="commercial">商业建筑</SelectItem>
              <SelectItem value="residential">住宅建筑</SelectItem>
              <SelectItem value="industrial">工业建筑</SelectItem>
              <SelectItem value="public">公共建筑</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-between pt-4 border-t">
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="$emit('search')">
          <Search class="w-4 h-4 mr-2" />
          搜索
        </Button>
        <Button variant="outline" size="sm" @click="$emit('reset')">
          <RotateCcw class="w-4 h-4 mr-2" />
          重置
        </Button>
      </div>
      
      <div class="text-sm text-gray-500">
        找到 {{ resultCount }} 个结果
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Search, RotateCcw } from 'lucide-vue-next';

import type { ProductStructureFilters } from '@/types/product-structure';

// Props
interface Props {
  filters: ProductStructureFilters;
  search: string;
  resultCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  resultCount: 0
});

// Emits
const emit = defineEmits<{
  'update:filters': [filters: ProductStructureFilters];
  'update:search': [search: string];
  'search': [];
  'filter': [];
  'reset': [];
}>();

// 响应式数据
const showAdvancedFilters = ref(false);

// 单选值（用于Select组件）
const selectedProductType = ref('');
const selectedStatus = ref('');
const selectedCategory = ref('');
const selectedCreatedBy = ref('');
const selectedApplication = ref('');

// 监听筛选条件变化
const filters = reactive({
  ...props.filters,
  createdDateRange: props.filters.createdDateRange || { start: '', end: '' }
});

const search = ref(props.search);

// 处理筛选变化
const handleProductTypeChange = (value: string) => {
  filters.productType = value ? [value as any] : [];
  emit('update:filters', { ...filters });
  emit('filter');
};

const handleStatusChange = (value: string) => {
  filters.status = value ? [value as any] : [];
  emit('update:filters', { ...filters });
  emit('filter');
};

const handleCategoryChange = (value: string) => {
  filters.category = value ? [value] : [];
  emit('update:filters', { ...filters });
  emit('filter');
};

const handleCreatedByChange = (value: string) => {
  filters.createdBy = value ? [value] : [];
  emit('update:filters', { ...filters });
  emit('filter');
};

const handleApplicationChange = (value: string) => {
  filters.applications = value ? [value] : [];
  emit('update:filters', { ...filters });
  emit('filter');
};

// 同步数据到父组件（如果需要的话可以在watch中使用）
// const syncFilters = () => {
//   emit('update:filters', { ...filters });
// };

// const syncSearch = () => {
//   emit('update:search', search.value);
// };
</script>

<style scoped>
.product-structure-filters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
