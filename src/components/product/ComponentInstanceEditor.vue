<template>
  <div class="component-instance-editor">
    <div class="space-y-6">
      <!-- 基本信息 -->
      <Card>
        <CardHeader>
          <CardTitle class="text-lg flex items-center gap-2">
            <component :is="getComponentIcon(componentType)" class="w-5 h-5" />
            组件实例配置
          </CardTitle>
          <CardDescription>
            配置组件在构件中的具体实例
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">组件名称</Label>
              <Input :value="componentName" readonly class="bg-gray-50" />
            </div>
            <div>
              <Label class="text-sm font-medium">组件编码</Label>
              <Input :value="componentCode" readonly class="bg-gray-50" />
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">
                实例名称 <span class="text-red-500">*</span>
              </Label>
              <Input
                v-model="localInstance.instanceName"
                placeholder="请输入实例名称"
                :class="{ 'border-red-500': hasError('instanceName') }"
                @blur="validateField('instanceName')"
              />
              <p v-if="hasError('instanceName')" class="text-sm text-red-600 mt-1">
                {{ getError('instanceName') }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">状态</Label>
              <Select v-model="localInstance.status">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">激活</SelectItem>
                  <SelectItem value="inactive">停用</SelectItem>
                  <SelectItem value="deprecated">已弃用</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label class="text-sm font-medium">描述</Label>
            <Textarea
              v-model="localInstance.description"
              placeholder="请输入实例描述"
              rows="2"
            />
          </div>

          <div class="flex items-center space-x-2">
            <Checkbox
              v-model="localInstance.optional"
              @update:model-value="updateInstance"
            />
            <Label class="text-sm">可选组件</Label>
          </div>
        </CardContent>
      </Card>

      <!-- 数量配置 -->
      <Card>
        <CardHeader>
          <CardTitle class="text-lg">数量配置</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-3 gap-4">
            <div>
              <Label class="text-sm font-medium">固定数量</Label>
              <Input
                v-model.number="localInstance.quantityConfig.fixedQuantity"
                type="number"
                min="1"
                @input="updateInstance"
              />
            </div>
            <div>
              <Label class="text-sm font-medium">最小数量</Label>
              <Input
                v-model.number="localInstance.quantityConfig.minQuantity"
                type="number"
                min="0"
                @input="updateInstance"
              />
            </div>
            <div>
              <Label class="text-sm font-medium">最大数量</Label>
              <Input
                v-model.number="localInstance.quantityConfig.maxQuantity"
                type="number"
                min="1"
                @input="updateInstance"
              />
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">数量单位</Label>
              <Input
                v-model="localInstance.quantityConfig.unit"
                placeholder="个"
                @input="updateInstance"
              />
            </div>
            <div>
              <Label class="text-sm font-medium">数量公式</Label>
              <Input
                v-model="localInstance.quantityConfig.quantityFormula"
                placeholder="例: width * height / 1000"
                @input="updateInstance"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 参数配置 -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle class="text-lg">参数配置</CardTitle>
            <div class="flex items-center gap-2">
              <Button @click="resetToDefaults" variant="outline" size="sm">
                <RotateCcw class="w-4 h-4 mr-1" />
                重置默认值
              </Button>
              <Button @click="showParameterInheritance = !showParameterInheritance" variant="outline" size="sm">
                <Eye class="w-4 h-4 mr-1" />
                {{ showParameterInheritance ? '隐藏' : '显示' }}继承关系
              </Button>
            </div>
          </div>
          <CardDescription>
            配置组件实例的参数值，可以覆盖组件的默认参数
          </CardDescription>
        </CardHeader>
        <CardContent>
          <!-- 参数继承关系显示 -->
          <div v-if="showParameterInheritance && parameterInheritance" class="mb-4 p-3 bg-blue-50 rounded-lg">
            <h4 class="text-sm font-medium mb-2">参数继承关系</h4>
            <div class="grid grid-cols-3 gap-4 text-xs">
              <div>
                <span class="font-medium">继承参数:</span>
                <div class="mt-1">
                  <div v-for="(value, key) in parameterInheritance.inheritedValues" :key="key" class="text-gray-600">
                    {{ key }}: {{ value }}
                  </div>
                </div>
              </div>
              <div>
                <span class="font-medium">覆盖参数:</span>
                <div class="mt-1">
                  <div v-for="(value, key) in parameterInheritance.overriddenValues" :key="key" class="text-blue-600">
                    {{ key }}: {{ value }}
                  </div>
                </div>
              </div>
              <div>
                <span class="font-medium">最终值:</span>
                <div class="mt-1">
                  <div v-for="(value, key) in parameterInheritance.finalValues" :key="key" class="text-green-600">
                    {{ key }}: {{ value }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 参数编辑 -->
          <div v-if="componentParameters.length === 0" class="text-center py-8 text-gray-500">
            <Settings class="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p>此组件没有可配置的参数</p>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="param in componentParameters"
              :key="param.id"
              class="border rounded-lg p-4"
              :class="{
                'border-red-200 bg-red-50': hasParameterError(param.name),
                'border-yellow-200 bg-yellow-50': hasParameterWarning(param.name),
                'border-blue-200 bg-blue-50': isParameterOverridden(param.name)
              }"
            >
              <div class="flex items-start justify-between mb-2">
                <div>
                  <Label class="text-sm font-medium">
                    {{ param.displayName }}
                    <span v-if="param.required" class="text-red-500">*</span>
                  </Label>
                  <p v-if="param.description" class="text-xs text-gray-500 mt-1">
                    {{ param.description }}
                  </p>
                </div>
                <div class="flex items-center gap-2">
                  <Badge v-if="isParameterOverridden(param.name)" variant="secondary" class="text-xs">
                    已覆盖
                  </Badge>
                  <Button
                    v-if="isParameterOverridden(param.name)"
                    @click="resetParameter(param.name)"
                    variant="ghost"
                    size="sm"
                    class="text-gray-500 hover:text-gray-700"
                  >
                    <RotateCcw class="w-3 h-3" />
                  </Button>
                </div>
              </div>

              <!-- 参数输入控件 -->
              <div class="space-y-2">
                <Input
                  v-if="param.type === 'string' || param.type === 'number'"
                  v-model="localInstance.parameterConfig.parameterOverrides[param.name]"
                  :type="param.type === 'number' ? 'number' : 'text'"
                  :min="param.minValue"
                  :max="param.maxValue"
                  :placeholder="String(param.defaultValue)"
                  @input="validateParameter(param.name)"
                />

                <Select
                  v-else-if="param.type === 'select'"
                  v-model="localInstance.parameterConfig.parameterOverrides[param.name]"
                  @update:model-value="validateParameter(param.name)"
                >
                  <SelectTrigger>
                    <SelectValue :placeholder="String(param.defaultValue)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="option in param.options"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>

                <div v-else-if="param.type === 'boolean'" class="flex items-center space-x-2">
                  <Checkbox
                    v-model="localInstance.parameterConfig.parameterOverrides[param.name]"
                    @update:model-value="validateParameter(param.name)"
                  />
                  <Label class="text-sm">{{ param.displayName }}</Label>
                </div>

                <!-- 参数验证结果 -->
                <div v-if="hasParameterError(param.name)" class="text-sm text-red-600">
                  {{ getParameterError(param.name) }}
                </div>
                <div v-if="hasParameterWarning(param.name)" class="text-sm text-yellow-600">
                  {{ getParameterWarning(param.name) }}
                </div>

                <!-- 参数信息 -->
                <div class="flex items-center gap-4 text-xs text-gray-500">
                  <span>默认值: {{ param.defaultValue }}{{ param.unit }}</span>
                  <span v-if="param.type === 'number' && (param.minValue !== undefined || param.maxValue !== undefined)">
                    范围: {{ param.minValue || '无限制' }} ~ {{ param.maxValue || '无限制' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 位置配置 -->
      <Card v-if="showPositionConfig">
        <CardHeader>
          <CardTitle class="text-lg">位置配置</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-3 gap-4">
            <div>
              <Label class="text-sm font-medium">X坐标</Label>
              <Input
                v-model.number="localInstance.positionConfig.position.x"
                type="number"
                @input="updateInstance"
              />
            </div>
            <div>
              <Label class="text-sm font-medium">Y坐标</Label>
              <Input
                v-model.number="localInstance.positionConfig.position.y"
                type="number"
                @input="updateInstance"
              />
            </div>
            <div>
              <Label class="text-sm font-medium">Z坐标</Label>
              <Input
                v-model.number="localInstance.positionConfig.position.z"
                type="number"
                @input="updateInstance"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import {
  Settings,
  RotateCcw,
  Eye,
  Square,
  Package,
  Wrench,
  HelpCircle
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import type {
  ComponentInstance,
  Component,
  ComponentParameter,
  AssemblyParameter,
  ComponentType
} from '@/types/product-structure';

import { componentInstanceService, type ParameterInheritanceResult } from '@/services/componentInstanceService';

// Props
interface Props {
  instance: ComponentInstance;
  component?: Component;
  assemblyParameters?: AssemblyParameter[];
  showPositionConfig?: boolean;
}

// Emits
interface Emits {
  (e: 'update', instance: ComponentInstance): void;
  (e: 'validate', isValid: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  showPositionConfig: false
});
const emit = defineEmits<Emits>();

// 响应式数据
const localInstance = ref<ComponentInstance>({ ...props.instance });
const validationErrors = ref<Record<string, string>>({});
const showParameterInheritance = ref(false);
const parameterInheritance = ref<ParameterInheritanceResult | null>(null);

// 计算属性
const componentName = computed(() => props.component?.name || localInstance.value.componentName);
const componentCode = computed(() => props.component?.code || localInstance.value.componentCode);
const componentType = computed(() => props.component?.componentType || 'other');
const componentParameters = computed(() => props.component?.parameters || []);

const hasError = computed(() => (field: string) => !!validationErrors.value[field]);
const getError = computed(() => (field: string) => validationErrors.value[field]);

// 方法
const getComponentIcon = (type: ComponentType) => {
  const iconMap = {
    frame: Square,
    glass: Package,
    hardware: Wrench,
    seal: Package,
    other: HelpCircle
  };
  return iconMap[type] || HelpCircle;
};

const validateField = (field: string) => {
  switch (field) {
    case 'instanceName':
      if (!localInstance.value.instanceName || localInstance.value.instanceName.trim() === '') {
        validationErrors.value.instanceName = '实例名称不能为空';
      } else {
        delete validationErrors.value.instanceName;
      }
      break;
  }
  
  emit('validate', Object.keys(validationErrors.value).length === 0);
};

const updateInstance = () => {
  emit('update', localInstance.value);
};

const isParameterOverridden = (paramName: string) => {
  return localInstance.value.parameterConfig.parameterOverrides.hasOwnProperty(paramName);
};

const hasParameterError = (paramName: string) => {
  const validation = localInstance.value.parameterConfig.validationStatus[paramName];
  return validation && !validation.isValid;
};

const hasParameterWarning = (paramName: string) => {
  const validation = localInstance.value.parameterConfig.validationStatus[paramName];
  return validation && validation.warningMessage;
};

const getParameterError = (paramName: string) => {
  const validation = localInstance.value.parameterConfig.validationStatus[paramName];
  return validation?.errorMessage;
};

const getParameterWarning = (paramName: string) => {
  const validation = localInstance.value.parameterConfig.validationStatus[paramName];
  return validation?.warningMessage;
};

const validateParameter = (paramName: string) => {
  // 这里应该调用参数验证服务
  updateInstance();
};

const resetParameter = (paramName: string) => {
  delete localInstance.value.parameterConfig.parameterOverrides[paramName];
  updateInstance();
};

const resetToDefaults = () => {
  localInstance.value.parameterConfig.parameterOverrides = {};
  updateInstance();
};

const calculateParameterInheritance = () => {
  if (props.component) {
    parameterInheritance.value = componentInstanceService.calculateParameterInheritance(
      props.component,
      localInstance.value.parameterConfig.parameterOverrides,
      props.assemblyParameters || []
    );
  }
};

// 监听变化
watch(() => props.instance, (newInstance) => {
  localInstance.value = { ...newInstance };
}, { deep: true });

watch(() => localInstance.value.parameterConfig.parameterOverrides, () => {
  calculateParameterInheritance();
}, { deep: true });

// 组件挂载
onMounted(() => {
  calculateParameterInheritance();
  validateField('instanceName');
});
</script>
