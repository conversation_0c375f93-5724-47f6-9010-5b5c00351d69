<template>
  <Card class="mb-6">
    <CardContent class="p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 搜索框 -->
        <div class="lg:col-span-2">
          <Label for="search">搜索组件</Label>
          <div class="relative mt-1">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              id="search"
              v-model="localFilters.search"
              placeholder="搜索组件名称、编码或标签..."
              class="pl-10"
              @input="debounceFilter"
            />
          </div>
        </div>

        <!-- 组件类型筛选 -->
        <div>
          <Label for="componentType">组件类型</Label>
          <Select v-model="selectedComponentTypes" multiple>
            <SelectTrigger class="mt-1">
              <SelectValue placeholder="选择组件类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="frame">框料</SelectItem>
              <SelectItem value="glass">玻璃</SelectItem>
              <SelectItem value="hardware">五金</SelectItem>
              <SelectItem value="seal">密封</SelectItem>
              <SelectItem value="other">其他</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- 状态筛选 -->
        <div>
          <Label for="status">状态</Label>
          <Select v-model="selectedStatuses" multiple>
            <SelectTrigger class="mt-1">
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="draft">草稿</SelectItem>
              <SelectItem value="active">活跃</SelectItem>
              <SelectItem value="deprecated">已弃用</SelectItem>
              <SelectItem value="archived">已归档</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <!-- 高级筛选 -->
      <div v-if="showAdvanced" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4 pt-4 border-t">
        <!-- 物料分类 -->
        <div>
          <Label for="materialCategory">物料分类</Label>
          <Select v-model="selectedMaterialCategories" multiple>
            <SelectTrigger class="mt-1">
              <SelectValue placeholder="选择物料分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem 
                v-for="category in materialCategories" 
                :key="category.id" 
                :value="category.id"
              >
                {{ category.name }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- 标签筛选 -->
        <div>
          <Label for="tags">标签</Label>
          <div class="mt-1">
            <div class="flex flex-wrap gap-1 mb-2">
              <Badge
                v-for="tag in selectedTags"
                :key="tag"
                variant="secondary"
                class="cursor-pointer"
                @click="removeTag(tag)"
              >
                {{ tag }}
                <X class="w-3 h-3 ml-1" />
              </Badge>
            </div>
            <Input
              v-model="tagInput"
              placeholder="输入标签名称..."
              @keydown.enter.prevent="addTag"
              @keydown="handleTagInput"
            />
          </div>
        </div>

        <!-- 可重用性 -->
        <div>
          <Label>可重用性</Label>
          <div class="mt-1 space-y-2">
            <div class="flex items-center space-x-2">
              <Checkbox
                id="reusable-all"
                :checked="localFilters.reusable === undefined"
                @update:checked="setReusableFilter(undefined)"
              />
              <Label for="reusable-all" class="text-sm">全部</Label>
            </div>
            <div class="flex items-center space-x-2">
              <Checkbox
                id="reusable-yes"
                :checked="localFilters.reusable === true"
                @update:checked="setReusableFilter(true)"
              />
              <Label for="reusable-yes" class="text-sm">可重用</Label>
            </div>
            <div class="flex items-center space-x-2">
              <Checkbox
                id="reusable-no"
                :checked="localFilters.reusable === false"
                @update:checked="setReusableFilter(false)"
              />
              <Label for="reusable-no" class="text-sm">不可重用</Label>
            </div>
          </div>
        </div>

        <!-- 创建时间范围 -->
        <div>
          <Label for="createdDateRange">创建时间</Label>
          <div class="mt-1 grid grid-cols-2 gap-2">
            <Input
              v-model="createdDateStart"
              type="date"
              placeholder="开始日期"
            />
            <Input
              v-model="createdDateEnd"
              type="date"
              placeholder="结束日期"
            />
          </div>
        </div>

        <!-- 更新时间范围 -->
        <div>
          <Label for="updatedDateRange">更新时间</Label>
          <div class="mt-1 grid grid-cols-2 gap-2">
            <Input
              v-model="updatedDateStart"
              type="date"
              placeholder="开始日期"
            />
            <Input
              v-model="updatedDateEnd"
              type="date"
              placeholder="结束日期"
            />
          </div>
        </div>
      </div>

      <!-- 排序和操作按钮 -->
      <div class="flex items-center justify-between mt-4 pt-4 border-t">
        <div class="flex items-center gap-4">
          <!-- 排序选项 -->
          <div class="flex items-center gap-2">
            <Label class="text-sm">排序:</Label>
            <Select v-model="localSort.field">
              <SelectTrigger class="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">名称</SelectItem>
                <SelectItem value="code">编码</SelectItem>
                <SelectItem value="componentType">类型</SelectItem>
                <SelectItem value="createdAt">创建时间</SelectItem>
                <SelectItem value="updatedAt">更新时间</SelectItem>
              </SelectContent>
            </Select>
            <Button
              @click="toggleSortDirection"
              variant="outline"
              size="sm"
            >
              <ArrowUpDown class="w-4 h-4" />
              {{ localSort.direction === 'asc' ? '升序' : '降序' }}
            </Button>
          </div>
        </div>

        <div class="flex items-center gap-2">
          <Button @click="toggleAdvanced" variant="outline" size="sm">
            <Filter class="w-4 h-4 mr-1" />
            {{ showAdvanced ? '收起' : '高级筛选' }}
          </Button>
          <Button @click="resetFilters" variant="outline" size="sm">
            <RotateCcw class="w-4 h-4 mr-1" />
            重置
          </Button>
          <Button @click="applyFilters" size="sm">
            <Search class="w-4 h-4 mr-1" />
            应用筛选
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { debounce } from 'lodash-es';
import { 
  Search, Filter, RotateCcw, ArrowUpDown, X
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';

import type { ComponentFilters, ComponentSortOptions } from '@/services/componentService';

// Props
interface Props {
  filters: ComponentFilters;
  sort: ComponentSortOptions;
}

// Emits
interface Emits {
  (e: 'update:filters', filters: ComponentFilters): void;
  (e: 'update:sort', sort: ComponentSortOptions): void;
  (e: 'filter'): void;
  (e: 'sort'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const showAdvanced = ref(false);
const localFilters = ref<ComponentFilters>({ ...props.filters });
const localSort = ref<ComponentSortOptions>({ ...props.sort });

const selectedComponentTypes = ref<string[]>(props.filters.componentType || []);
const selectedStatuses = ref<string[]>(props.filters.status || []);
const selectedMaterialCategories = ref<string[]>(props.filters.materialCategoryId || []);
const selectedTags = ref<string[]>(props.filters.tags || []);
const tagInput = ref('');

const createdDateStart = ref(props.filters.createdDateRange?.[0] || '');
const createdDateEnd = ref(props.filters.createdDateRange?.[1] || '');
const updatedDateStart = ref(props.filters.updatedDateRange?.[0] || '');
const updatedDateEnd = ref(props.filters.updatedDateRange?.[1] || '');

// 模拟物料分类数据
const materialCategories = ref([
  { id: 'aluminum-frame', name: '铝合金框料' },
  { id: 'steel-frame', name: '钢制框料' },
  { id: 'tempered-glass', name: '钢化玻璃' },
  { id: 'laminated-glass', name: '夹胶玻璃' },
  { id: 'door-hardware', name: '门五金' },
  { id: 'window-hardware', name: '窗五金' },
  { id: 'rubber-seal', name: '橡胶密封' },
  { id: 'foam-seal', name: '泡沫密封' }
]);

// 防抖搜索
const debounceFilter = debounce(() => {
  applyFilters();
}, 300);

// 方法
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value;
};

const toggleSortDirection = () => {
  localSort.value.direction = localSort.value.direction === 'asc' ? 'desc' : 'asc';
  emit('update:sort', localSort.value);
  emit('sort');
};

const setReusableFilter = (value: boolean | undefined) => {
  localFilters.value.reusable = value;
};

const addTag = () => {
  const tag = tagInput.value.trim().replace(',', '');
  if (tag && !selectedTags.value.includes(tag)) {
    selectedTags.value.push(tag);
    tagInput.value = '';
  }
};

const handleTagInput = (event: KeyboardEvent) => {
  if (event.key === ',') {
    event.preventDefault();
    addTag();
  }
};

const removeTag = (tag: string) => {
  const index = selectedTags.value.indexOf(tag);
  if (index > -1) {
    selectedTags.value.splice(index, 1);
  }
};

const resetFilters = () => {
  localFilters.value = {};
  selectedComponentTypes.value = [];
  selectedStatuses.value = [];
  selectedMaterialCategories.value = [];
  selectedTags.value = [];
  tagInput.value = '';
  createdDateStart.value = '';
  createdDateEnd.value = '';
  updatedDateStart.value = '';
  updatedDateEnd.value = '';
  
  localSort.value = {
    field: 'updatedAt',
    direction: 'desc'
  };
  
  applyFilters();
};

const applyFilters = () => {
  // 构建筛选条件
  const filters: ComponentFilters = {
    ...localFilters.value,
    componentType: selectedComponentTypes.value.length > 0 ? selectedComponentTypes.value as any : undefined,
    status: selectedStatuses.value.length > 0 ? selectedStatuses.value as any : undefined,
    materialCategoryId: selectedMaterialCategories.value.length > 0 ? selectedMaterialCategories.value : undefined,
    tags: selectedTags.value.length > 0 ? selectedTags.value : undefined
  };

  // 处理日期范围
  if (createdDateStart.value && createdDateEnd.value) {
    filters.createdDateRange = [createdDateStart.value, createdDateEnd.value];
  }
  if (updatedDateStart.value && updatedDateEnd.value) {
    filters.updatedDateRange = [updatedDateStart.value, updatedDateEnd.value];
  }

  emit('update:filters', filters);
  emit('filter');
};

// 监听排序变化
watch(localSort, (newSort) => {
  emit('update:sort', newSort);
  emit('sort');
}, { deep: true });

// 监听多选变化
watch([selectedComponentTypes, selectedStatuses, selectedMaterialCategories, selectedTags], () => {
  applyFilters();
}, { deep: true });

// 监听日期范围变化
watch([createdDateStart, createdDateEnd, updatedDateStart, updatedDateEnd], () => {
  applyFilters();
});

// 监听props变化
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters };
}, { deep: true });

watch(() => props.sort, (newSort) => {
  localSort.value = { ...newSort };
}, { deep: true });
</script>
