<template>
  <Card class="w-full">
    <CardHeader class="pb-3">
      <div class="flex items-center gap-2">
        <component :is="getTypeIcon(assembly.assemblyType)" class="w-5 h-5 text-gray-600" />
        <div class="flex-1 min-w-0">
          <CardTitle class="text-base truncate">
            {{ assembly.name || '未命名构件' }}
          </CardTitle>
          <CardDescription class="text-sm">
            {{ assembly.code || '未设置编码' }}
          </CardDescription>
        </div>
        <Badge :variant="getStatusVariant(assembly.status)" class="text-xs">
          {{ getStatusText(assembly.status) }}
        </Badge>
      </div>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- 构件类型 -->
      <div class="flex items-center justify-between text-sm">
        <span class="text-gray-500">类型</span>
        <span class="font-medium">{{ getTypeText(assembly.assemblyType) }}</span>
      </div>

      <!-- 描述 -->
      <div v-if="assembly.description" class="text-sm">
        <span class="text-gray-500 block mb-1">描述</span>
        <p class="text-gray-900 line-clamp-2">{{ assembly.description }}</p>
      </div>

      <!-- 统计信息 -->
      <div class="grid grid-cols-2 gap-3 text-sm">
        <div class="flex items-center gap-2">
          <Package class="w-4 h-4 text-blue-500" />
          <div>
            <p class="text-gray-500">组件实例</p>
            <p class="font-medium">{{ (assembly.componentInstances || []).length }}</p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <Layers class="w-4 h-4 text-green-500" />
          <div>
            <p class="text-gray-500">子构件</p>
            <p class="font-medium">{{ (assembly.subAssemblies || []).length }}</p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <Settings class="w-4 h-4 text-orange-500" />
          <div>
            <p class="text-gray-500">参数</p>
            <p class="font-medium">{{ (assembly.assemblyParameters || []).length }}</p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <CheckCircle class="w-4 h-4 text-purple-500" />
          <div>
            <p class="text-gray-500">质检项</p>
            <p class="font-medium">{{ (assembly.qualityRequirements || []).length }}</p>
          </div>
        </div>
      </div>

      <!-- 装配工艺信息 -->
      <div v-if="assembly.assemblyProcess" class="border-t pt-3">
        <div class="flex items-center justify-between text-sm mb-2">
          <span class="text-gray-500">装配工艺</span>
          <span class="font-medium">{{ assembly.assemblyProcess.processName || '未设置' }}</span>
        </div>
        <div class="grid grid-cols-2 gap-2 text-xs">
          <div class="flex items-center gap-1">
            <Clock class="w-3 h-3 text-gray-400" />
            <span class="text-gray-500">{{ assembly.assemblyProcess.totalEstimatedTime || 0 }}分钟</span>
          </div>
          <div class="flex items-center gap-1">
            <List class="w-3 h-3 text-gray-400" />
            <span class="text-gray-500">{{ (assembly.assemblyProcess.steps || []).length }}个步骤</span>
          </div>
        </div>
      </div>

      <!-- 标签 -->
      <div v-if="assembly.tags && assembly.tags.length > 0" class="border-t pt-3">
        <p class="text-sm text-gray-500 mb-2">标签</p>
        <div class="flex flex-wrap gap-1">
          <Badge
            v-for="tag in assembly.tags.slice(0, 3)"
            :key="tag"
            variant="secondary"
            class="text-xs"
          >
            {{ tag }}
          </Badge>
          <Badge v-if="assembly.tags.length > 3" variant="outline" class="text-xs">
            +{{ assembly.tags.length - 3 }}
          </Badge>
        </div>
      </div>

      <!-- 扩展属性 -->
      <div v-if="assembly.properties && Object.keys(assembly.properties).length > 0" class="border-t pt-3">
        <p class="text-sm text-gray-500 mb-2">扩展属性</p>
        <div class="space-y-1">
          <div
            v-for="(value, key) in Object.entries(assembly.properties).slice(0, 3)"
            :key="key"
            class="flex justify-between text-xs"
          >
            <span class="text-gray-500 truncate">{{ key }}:</span>
            <span class="font-medium truncate ml-2">{{ value }}</span>
          </div>
          <div v-if="Object.keys(assembly.properties).length > 3" class="text-xs text-gray-400">
            +{{ Object.keys(assembly.properties).length - 3 }} 个属性
          </div>
        </div>
      </div>

      <!-- 完整性指示器 -->
      <div class="border-t pt-3">
        <div class="flex items-center justify-between text-sm">
          <span class="text-gray-500">完整性</span>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1">
              <div
                class="w-2 h-2 rounded-full"
                :class="getCompletenessColor(completenessScore)"
              ></div>
              <span class="text-xs font-medium">{{ completenessScore }}%</span>
            </div>
          </div>
        </div>
        <div class="mt-2 bg-gray-200 rounded-full h-1.5">
          <div
            class="h-1.5 rounded-full transition-all duration-300"
            :class="getCompletenessBarColor(completenessScore)"
            :style="{ width: `${completenessScore}%` }"
          ></div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  Package,
  Layers,
  Settings,
  CheckCircle,
  Clock,
  List,
  Square,
  Wrench,
  HelpCircle
} from 'lucide-vue-next';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import type { Assembly, AssemblyType } from '@/types/product-structure';

// Props
interface Props {
  assembly: Partial<Assembly>;
}

const props = defineProps<Props>();

// 计算属性
const completenessScore = computed(() => {
  let score = 0;
  const maxScore = 100;

  // 基本信息 (30分)
  if (props.assembly.name) score += 10;
  if (props.assembly.code) score += 10;
  if (props.assembly.description) score += 10;

  // 组件实例 (25分)
  if (props.assembly.componentInstances && props.assembly.componentInstances.length > 0) {
    score += 25;
  }

  // 装配工艺 (25分)
  if (props.assembly.assemblyProcess && props.assembly.assemblyProcess.steps && props.assembly.assemblyProcess.steps.length > 0) {
    score += 25;
  }

  // 质量要求 (10分)
  if (props.assembly.qualityRequirements && props.assembly.qualityRequirements.length > 0) {
    score += 10;
  }

  // 参数定义 (10分)
  if (props.assembly.assemblyParameters && props.assembly.assemblyParameters.length > 0) {
    score += 10;
  }

  return Math.min(score, maxScore);
});

// 方法
const getTypeIcon = (type?: AssemblyType) => {
  if (!type) return HelpCircle;
  
  const iconMap = {
    frame_assembly: Square,
    glass_assembly: Package,
    hardware_assembly: Wrench,
    complete_assembly: Layers
  };
  return iconMap[type] || HelpCircle;
};

const getTypeText = (type?: AssemblyType) => {
  if (!type) return '未设置';
  
  const textMap = {
    frame_assembly: '框架构件',
    glass_assembly: '玻璃构件',
    hardware_assembly: '五金构件',
    complete_assembly: '完整构件'
  };
  return textMap[type] || '未知类型';
};

const getStatusVariant = (status?: string) => {
  if (!status) return 'secondary';
  
  const variantMap = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'secondary';
};

const getStatusText = (status?: string) => {
  if (!status) return '草稿';
  
  const textMap = {
    draft: '草稿',
    active: '激活',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};

const getCompletenessColor = (score: number) => {
  if (score >= 80) return 'bg-green-500';
  if (score >= 60) return 'bg-yellow-500';
  if (score >= 40) return 'bg-orange-500';
  return 'bg-red-500';
};

const getCompletenessBarColor = (score: number) => {
  if (score >= 80) return 'bg-green-500';
  if (score >= 60) return 'bg-yellow-500';
  if (score >= 40) return 'bg-orange-500';
  return 'bg-red-500';
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
