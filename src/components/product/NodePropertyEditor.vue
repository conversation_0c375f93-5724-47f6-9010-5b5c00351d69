<template>
  <div class="node-property-editor">
    <div v-if="node">
      <!-- 节点基本信息 -->
      <div class="space-y-4">
        <div class="flex items-center gap-2 pb-3 border-b">
          <component :is="getNodeIcon(node.type)" class="w-5 h-5 text-gray-600" />
          <div class="flex-1">
            <h4 class="font-medium">{{ node.name }}</h4>
            <p class="text-sm text-gray-500">{{ getNodeTypeLabel(node.type) }}</p>
          </div>
          <Badge :variant="getStatusVariant(node.status)">
            {{ getStatusLabel(node.status) }}
          </Badge>
        </div>

        <!-- 基本属性编辑 -->
        <div class="space-y-3">
          <div>
            <Label for="node-name">节点名称</Label>
            <Input
              id="node-name"
              v-model="editableNode.name"
              placeholder="输入节点名称"
              :disabled="readonly"
            />
          </div>

          <div>
            <Label for="node-description">描述</Label>
            <Textarea
              id="node-description"
              v-model="editableNode.description"
              placeholder="输入节点描述"
              rows="2"
              :disabled="readonly"
            />
          </div>

          <div v-if="node.type === 'component'">
            <Label for="node-quantity">数量</Label>
            <div class="flex gap-2">
              <Input
                id="node-quantity"
                v-model.number="editableNode.data.quantity"
                type="number"
                min="1"
                :disabled="readonly"
              />
              <Select v-model="editableNode.data.quantityType" :disabled="readonly">
                <SelectTrigger class="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fixed">固定</SelectItem>
                  <SelectItem value="formula">公式</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div v-if="node.type === 'component' && editableNode.data.quantityType === 'formula'">
            <Label for="quantity-formula">数量公式</Label>
            <Input
              id="quantity-formula"
              v-model="editableNode.data.quantityFormula"
              placeholder="例如: width / 100"
              :disabled="readonly"
            />
          </div>
        </div>

        <!-- 参数配置 -->
        <div v-if="nodeParameters.length > 0" class="space-y-3">
          <div class="flex items-center justify-between">
            <Label class="text-base font-medium">参数配置</Label>
            <Button v-if="!readonly" variant="outline" size="sm" @click="addParameter">
              <Plus class="w-4 h-4 mr-1" />
              添加
            </Button>
          </div>

          <div class="space-y-2">
            <div
              v-for="(param, index) in nodeParameters"
              :key="param.id"
              class="parameter-item p-3 border rounded-lg"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium">{{ param.displayName }}</span>
                  <Badge variant="outline" class="text-xs">{{ param.type }}</Badge>
                </div>
                <Button
                  v-if="!readonly"
                  variant="ghost"
                  size="sm"
                  @click="removeParameter(index)"
                >
                  <X class="w-4 h-4" />
                </Button>
              </div>

              <div class="space-y-2">
                <div v-if="param.type === 'number'">
                  <Input
                    v-model.number="param.defaultValue"
                    type="number"
                    :min="param.minValue"
                    :max="param.maxValue"
                    :disabled="readonly"
                    class="text-sm"
                  />
                  <div class="flex justify-between text-xs text-gray-500 mt-1">
                    <span>最小: {{ param.minValue }}</span>
                    <span>最大: {{ param.maxValue }}</span>
                  </div>
                </div>

                <div v-else-if="param.type === 'select'">
                  <Select v-model="param.defaultValue" :disabled="readonly">
                    <SelectTrigger class="text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="option in param.options"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div v-else>
                  <Input
                    v-model="param.defaultValue"
                    :disabled="readonly"
                    class="text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 约束条件 -->
        <div v-if="nodeConstraints.length > 0" class="space-y-3">
          <div class="flex items-center justify-between">
            <Label class="text-base font-medium">约束条件</Label>
            <Button v-if="!readonly" variant="outline" size="sm" @click="addConstraint">
              <Plus class="w-4 h-4 mr-1" />
              添加
            </Button>
          </div>

          <div class="space-y-2">
            <div
              v-for="(constraint, index) in nodeConstraints"
              :key="constraint.id"
              class="constraint-item p-3 border rounded-lg"
            >
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium">{{ constraint.name }}</span>
                <div class="flex items-center gap-2">
                  <Badge :variant="getSeverityVariant(constraint.severity)" class="text-xs">
                    {{ constraint.severity }}
                  </Badge>
                  <Button
                    v-if="!readonly"
                    variant="ghost"
                    size="sm"
                    @click="removeConstraint(index)"
                  >
                    <X class="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <Input
                v-model="constraint.expression"
                placeholder="输入约束表达式"
                :disabled="readonly"
                class="text-sm font-mono"
              />
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="!readonly" class="flex gap-2 pt-4 border-t">
          <Button @click="applyChanges" :disabled="!hasNodeChanges">
            <Check class="w-4 h-4 mr-1" />
            应用
          </Button>
          <Button variant="outline" @click="resetChanges">
            <RotateCcw class="w-4 h-4 mr-1" />
            重置
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import {
  Package,
  Layers,
  Square,
  Wrench,
  Plus,
  X,
  Check,
  RotateCcw
} from 'lucide-vue-next';

import type { 
  VisualNode, 
  ComponentParameter, 
  ComponentConstraint 
} from '@/types/product-structure';

// Props
interface Props {
  node: VisualNode;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

// Emits
const emit = defineEmits<{
  'update': [node: VisualNode];
  'delete': [nodeId: string];
}>();

// 响应式数据
const editableNode = ref<VisualNode>({ ...props.node });
const originalNode = ref<VisualNode>({ ...props.node });

// 计算属性
const nodeParameters = computed(() => {
  return editableNode.value.data?.parameters || [];
});

const nodeConstraints = computed(() => {
  return editableNode.value.data?.constraints || [];
});

const hasNodeChanges = computed(() => {
  return JSON.stringify(editableNode.value) !== JSON.stringify(originalNode.value);
});

// 方法
const getNodeIcon = (type: string) => {
  const iconMap = {
    product: Package,
    assembly: Layers,
    component: Square,
    parameter: Wrench
  };
  return iconMap[type] || Package;
};

const getNodeTypeLabel = (type: string) => {
  const labelMap = {
    product: '产品结构',
    assembly: '构件',
    component: '组件',
    parameter: '参数'
  };
  return labelMap[type] || type;
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    active: 'default',
    inactive: 'secondary',
    error: 'destructive',
    warning: 'outline'
  };
  return variantMap[status] || 'default';
};

const getStatusLabel = (status: string) => {
  const labelMap = {
    active: '活跃',
    inactive: '非活跃',
    error: '错误',
    warning: '警告'
  };
  return labelMap[status] || status;
};

const getSeverityVariant = (severity: string) => {
  const variantMap = {
    error: 'destructive',
    warning: 'outline',
    info: 'secondary'
  };
  return variantMap[severity] || 'default';
};

const addParameter = () => {
  if (!editableNode.value.data.parameters) {
    editableNode.value.data.parameters = [];
  }

  const newParameter: ComponentParameter = {
    id: `param_${Date.now()}`,
    name: `param_${editableNode.value.data.parameters.length + 1}`,
    displayName: '新参数',
    type: 'string',
    category: 'other',
    defaultValue: '',
    required: false,
    visible: true,
    editable: true,
    description: '',
    validationRules: []
  };

  editableNode.value.data.parameters.push(newParameter);
};

const removeParameter = (index: number) => {
  if (editableNode.value.data.parameters) {
    editableNode.value.data.parameters.splice(index, 1);
  }
};

const addConstraint = () => {
  if (!editableNode.value.data.constraints) {
    editableNode.value.data.constraints = [];
  }

  const newConstraint: ComponentConstraint = {
    id: `constraint_${Date.now()}`,
    name: '新约束',
    type: 'dimension',
    expression: '',
    errorMessage: '约束验证失败',
    severity: 'warning',
    enabled: true,
    description: ''
  };

  editableNode.value.data.constraints.push(newConstraint);
};

const removeConstraint = (index: number) => {
  if (editableNode.value.data.constraints) {
    editableNode.value.data.constraints.splice(index, 1);
  }
};

const applyChanges = () => {
  emit('update', editableNode.value);
  originalNode.value = { ...editableNode.value };
};

const resetChanges = () => {
  editableNode.value = { ...originalNode.value };
};

// 监听节点变化
watch(() => props.node, (newNode) => {
  editableNode.value = { ...newNode };
  originalNode.value = { ...newNode };
}, { deep: true });
</script>

<style scoped>
.parameter-item,
.constraint-item {
  @apply transition-all duration-200;
}

.parameter-item:hover,
.constraint-item:hover {
  @apply shadow-sm border-blue-200;
}
</style>
