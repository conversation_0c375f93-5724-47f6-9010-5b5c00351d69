<template>
  <div class="h-full flex flex-col">
    <!-- 节点信息头部 -->
    <div class="flex-shrink-0 p-4 border-b bg-gray-50">
      <div class="flex items-center gap-3">
        <component
          :is="getNodeIcon(node.type)"
          :class="['w-5 h-5', getNodeIconColor(node.type)]"
        />
        <div class="flex-1 min-w-0">
          <h3 class="font-medium text-sm truncate">{{ node.name }}</h3>
          <p class="text-xs text-gray-500">{{ getNodeTypeLabel(node.type) }}</p>
        </div>
        <Badge :variant="node.type === 'product' ? 'default' : 'secondary'">
          {{ node.code || '未设置' }}
        </Badge>
      </div>
    </div>

    <!-- 参数配置区域 -->
    <div class="flex-1 overflow-y-auto">
      <div class="p-4 space-y-6">

        <!-- 基本信息 -->
        <div class="space-y-4">
          <h4 class="font-medium text-sm text-gray-900 flex items-center gap-2">
            <Info class="w-4 h-4" />
            基本信息
          </h4>

          <div class="grid grid-cols-1 gap-4">
            <div>
              <Label for="node-name">名称 *</Label>
              <Input
                id="node-name"
                v-model="localNode.name"
                placeholder="输入节点名称"
                @blur="handleFieldUpdate('name', localNode.name)"
              />
            </div>

            <div>
              <Label for="node-code">编码</Label>
              <Input
                id="node-code"
                v-model="localNode.code"
                placeholder="输入节点编码"
                @blur="handleFieldUpdate('code', localNode.code)"
              />
            </div>

            <div>
              <Label for="node-description">描述</Label>
              <Textarea
                id="node-description"
                v-model="localNode.description"
                placeholder="输入节点描述"
                rows="3"
                @blur="handleFieldUpdate('description', localNode.description)"
              />
            </div>
          </div>
        </div>

        <!-- 数量配置 -->
        <div class="space-y-4">
          <h4 class="font-medium text-sm text-gray-900 flex items-center gap-2">
            <Calculator class="w-4 h-4" />
            数量配置
          </h4>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label for="node-quantity">数量</Label>
              <Input
                id="node-quantity"
                type="number"
                v-model.number="localNode.quantity"
                min="1"
                @blur="handleFieldUpdate('quantity', localNode.quantity)"
              />
            </div>

            <div>
              <Label for="node-unit">单位</Label>
              <Select v-model="localNode.unit" @update:model-value="handleFieldUpdate('unit', $event)">
                <SelectTrigger>
                  <SelectValue placeholder="选择单位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pcs">件</SelectItem>
                  <SelectItem value="m">米</SelectItem>
                  <SelectItem value="m2">平方米</SelectItem>
                  <SelectItem value="kg">千克</SelectItem>
                  <SelectItem value="set">套</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <!-- 构件/组件库选择 -->
        <div v-if="node.type === 'assembly' || node.type === 'component'" class="space-y-4">
          <div class="flex items-center justify-between">
            <h4 class="font-medium text-sm text-gray-900 flex items-center gap-2">
              <Package class="w-4 h-4" />
              {{ node.type === 'assembly' ? '构件库' : '组件库' }}
            </h4>
            <Button
              variant="outline"
              size="sm"
              @click="showComponentLibrary = true"
            >
              <Search class="w-4 h-4 mr-1" />
              选择现有{{ node.type === 'assembly' ? '构件' : '组件' }}
            </Button>
          </div>

          <div v-if="localNode.libraryItemId" class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-sm text-blue-900">{{ localNode.libraryItemName }}</div>
                <div class="text-xs text-blue-700">来源: {{ localNode.libraryItemSource || '标准库' }}</div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                @click="unlinkFromLibrary"
                class="text-blue-600 hover:text-blue-800"
              >
                <X class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        <!-- 物料映射 (仅组件) -->
        <div v-if="node.type === 'component'" class="space-y-4">
          <h4 class="font-medium text-sm text-gray-900 flex items-center gap-2">
            <Package class="w-4 h-4" />
            物料映射
          </h4>

          <div class="space-y-3">
            <div>
              <Label for="material-category">物料分类 *</Label>
              <Select
                v-model="localNode.materialCategoryId"
                @update:model-value="handleMaterialCategoryChange"
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择物料分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cat_stainless_steel_profile">不锈钢型材</SelectItem>
                  <SelectItem value="cat_aluminum_profile">铝合金型材</SelectItem>
                  <SelectItem value="cat_fire_glass">防火玻璃</SelectItem>
                  <SelectItem value="cat_sealing_material">密封材料</SelectItem>
                  <SelectItem value="cat_hardware">五金配件</SelectItem>
                  <SelectItem value="cat_insulation">保温材料</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label for="quantity-formula">数量计算公式</Label>
              <div class="space-y-2">
                <Textarea
                  id="quantity-formula"
                  v-model="localNode.quantityFormula"
                  placeholder="例如: Math.ceil(partition_width / 1000) * 2"
                  rows="2"
                  @blur="handleFieldUpdate('quantityFormula', localNode.quantityFormula)"
                />
                <p class="text-xs text-gray-500">
                  可使用参数变量，如 partition_width, partition_height 等
                </p>
              </div>
            </div>

            <div>
              <Label for="cost-formula">成本计算公式</Label>
              <div class="space-y-2">
                <Textarea
                  id="cost-formula"
                  v-model="localNode.costFormula"
                  placeholder="例如: quantity * unit_price * material_factor"
                  rows="2"
                  @blur="handleFieldUpdate('costFormula', localNode.costFormula)"
                />
                <p class="text-xs text-gray-500">
                  用于计算该组件的成本，支持复杂公式
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 参数化配置 -->
        <div v-if="node.type !== 'product'" class="space-y-4">
          <div class="flex items-center justify-between">
            <h4 class="font-medium text-sm text-gray-900 flex items-center gap-2">
              <Settings class="w-4 h-4" />
              参数化配置
            </h4>
            <Button
              variant="outline"
              size="sm"
              @click="showParameterLibrary = true"
            >
              <Plus class="w-4 h-4 mr-1" />
              添加参数
            </Button>
          </div>

          <!-- 参数列表 -->
          <div class="space-y-3">
            <div
              v-for="(param, index) in localNode.parameters || []"
              :key="param.id || index"
              class="p-3 border rounded-lg space-y-3"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <component :is="getParameterIcon(param.type)" class="w-4 h-4 text-blue-600" />
                  <span class="font-medium text-sm">{{ param.displayName || param.name }}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="removeParameter(index)"
                  class="h-6 w-6 p-0 text-red-600"
                >
                  <X class="w-3 h-3" />
                </Button>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <div>
                  <Label>参数名称</Label>
                  <Input
                    v-model="param.name"
                    placeholder="参数名称"
                    @blur="updateParameter(index, param)"
                  />
                </div>
                <div>
                  <Label>显示名称</Label>
                  <Input
                    v-model="param.displayName"
                    placeholder="用户友好的显示名称"
                    @blur="updateParameter(index, param)"
                  />
                </div>
              </div>

              <div class="grid grid-cols-3 gap-3">
                <div>
                  <Label>参数类型</Label>
                  <Select v-model="param.type" @update:model-value="updateParameter(index, param)">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dimension">尺寸</SelectItem>
                      <SelectItem value="material">材质</SelectItem>
                      <SelectItem value="feature">功能</SelectItem>
                      <SelectItem value="option">选项</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>输入类型</Label>
                  <Select v-model="param.inputType" @update:model-value="updateParameter(index, param)">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="number">数字</SelectItem>
                      <SelectItem value="select">单选</SelectItem>
                      <SelectItem value="multiSelect">多选</SelectItem>
                      <SelectItem value="boolean">开关</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>单位</Label>
                  <Input
                    v-model="param.unit"
                    placeholder="mm, kg, 等"
                    @blur="updateParameter(index, param)"
                  />
                </div>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <div>
                  <Label>默认值</Label>
                  <Input
                    v-model="param.defaultValue"
                    placeholder="默认值"
                    @blur="updateParameter(index, param)"
                  />
                </div>
                <div>
                  <Label>是否必填</Label>
                  <div class="flex items-center space-x-2 mt-2">
                    <Toggle
                      :pressed="param.required"
                      @update:pressed="param.required = $event; updateParameter(index, param)"
                    />
                    <span class="text-sm">{{ param.required ? '必填' : '可选' }}</span>
                  </div>
                </div>
              </div>

              <!-- 数值范围设置 -->
              <div v-if="param.inputType === 'number'" class="grid grid-cols-2 gap-3">
                <div>
                  <Label>最小值</Label>
                  <Input
                    type="number"
                    v-model.number="param.minValue"
                    placeholder="最小值"
                    @blur="updateParameter(index, param)"
                  />
                </div>
                <div>
                  <Label>最大值</Label>
                  <Input
                    type="number"
                    v-model.number="param.maxValue"
                    placeholder="最大值"
                    @blur="updateParameter(index, param)"
                  />
                </div>
              </div>

              <!-- 选项配置 -->
              <div v-if="param.inputType === 'select' || param.inputType === 'multiSelect'" class="space-y-2">
                <div class="flex items-center justify-between">
                  <Label>选项配置</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    @click="addParameterOption(index)"
                  >
                    <Plus class="w-3 h-3 mr-1" />
                    添加选项
                  </Button>
                </div>

                <div class="space-y-2">
                  <div
                    v-for="(option, optionIndex) in param.options || []"
                    :key="optionIndex"
                    class="flex items-center gap-2 p-2 border rounded"
                  >
                    <Input
                      v-model="option.value"
                      placeholder="选项值"
                      class="flex-1"
                      @blur="updateParameter(index, param)"
                    />
                    <Input
                      v-model="option.label"
                      placeholder="显示标签"
                      class="flex-1"
                      @blur="updateParameter(index, param)"
                    />
                    <Input
                      type="number"
                      v-model.number="option.additionalCost"
                      placeholder="额外成本"
                      class="w-20"
                      @blur="updateParameter(index, param)"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="removeParameterOption(index, optionIndex)"
                      class="h-6 w-6 p-0 text-red-600"
                    >
                      <X class="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>

              <div>
                <Label>帮助文本</Label>
                <Input
                  v-model="param.helpText"
                  placeholder="为用户提供的帮助说明"
                  @blur="updateParameter(index, param)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 约束条件 -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <h4 class="font-medium text-sm text-gray-900 flex items-center gap-2">
              <Shield class="w-4 h-4" />
              约束条件
            </h4>
            <Button
              variant="outline"
              size="sm"
              @click="addConstraint"
            >
              <Plus class="w-4 h-4 mr-1" />
              添加约束
            </Button>
          </div>

          <div class="space-y-3">
            <div
              v-for="(constraint, index) in localNode.constraints || []"
              :key="constraint.id || index"
              class="p-3 border rounded-lg space-y-3"
            >
              <div class="flex items-center justify-between">
                <Input
                  v-model="constraint.name"
                  placeholder="约束名称"
                  class="flex-1 mr-2"
                  @blur="updateConstraint(index, constraint)"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  @click="removeConstraint(index)"
                  class="h-6 w-6 p-0 text-red-600"
                >
                  <X class="w-3 h-3" />
                </Button>
              </div>

              <div>
                <Label>约束表达式</Label>
                <Textarea
                  v-model="constraint.expression"
                  placeholder="例如: width >= 1000 && width <= 6000"
                  rows="2"
                  @blur="updateConstraint(index, constraint)"
                />
              </div>

              <div class="grid grid-cols-2 gap-3">
                <div>
                  <Label>错误信息</Label>
                  <Input
                    v-model="constraint.errorMessage"
                    placeholder="约束违反时的提示信息"
                    @blur="updateConstraint(index, constraint)"
                  />
                </div>
                <div>
                  <Label>严重程度</Label>
                  <Select v-model="constraint.severity" @update:model-value="updateConstraint(index, constraint)">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="error">错误</SelectItem>
                      <SelectItem value="warning">警告</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex-shrink-0 p-4 border-t bg-gray-50">
      <div class="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          @click="resetChanges"
          :disabled="!hasChanges"
        >
          重置
        </Button>
        <Button
          size="sm"
          @click="saveChanges"
          :disabled="!hasChanges"
        >
          保存更改
        </Button>
      </div>
    </div>

    <!-- 参数库弹窗 -->
    <Dialog v-model:open="showParameterLibrary">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>参数库</DialogTitle>
          <DialogDescription>
            选择预定义的参数或创建新参数
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          <!-- 参数搜索 -->
          <div class="relative">
            <Search class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              v-model="parameterSearchQuery"
              placeholder="搜索参数..."
              class="pl-10"
            />
          </div>

          <!-- 参数分类 -->
          <div class="flex gap-2 flex-wrap">
            <Button
              v-for="category in parameterCategories"
              :key="category.value"
              :variant="selectedParameterCategory === category.value ? 'default' : 'outline'"
              size="sm"
              @click="selectedParameterCategory = category.value"
            >
              {{ category.label }}
            </Button>
          </div>

          <!-- 预定义参数列表 -->
          <div class="max-h-60 overflow-y-auto space-y-2">
            <div
              v-for="param in filteredParameterLibrary"
              :key="param.id"
              class="p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
              @click="selectParameterFromLibrary(param)"
            >
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-medium text-sm">{{ param.displayName }}</div>
                  <div class="text-xs text-gray-500">{{ param.description }}</div>
                  <div class="text-xs text-blue-600 mt-1">
                    {{ param.inputType }} | {{ param.unit || '无单位' }}
                    <span v-if="param.minValue !== undefined || param.maxValue !== undefined">
                      | 范围: {{ param.minValue || '∞' }} - {{ param.maxValue || '∞' }}
                    </span>
                  </div>
                </div>
                <Badge variant="secondary">{{ param.type }}</Badge>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showParameterLibrary = false">
            取消
          </Button>
          <Button @click="createNewParameter">
            创建新参数
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 构件/组件库弹窗 -->
    <Dialog v-model:open="showComponentLibrary">
      <DialogContent class="max-w-4xl">
        <DialogHeader>
          <DialogTitle>{{ node.type === 'assembly' ? '构件库' : '组件库' }}</DialogTitle>
          <DialogDescription>
            选择现有的{{ node.type === 'assembly' ? '构件' : '组件' }}或创建新的
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          <!-- 搜索和筛选 -->
          <div class="flex gap-4">
            <div class="relative flex-1">
              <Search class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                v-model="componentSearchQuery"
                placeholder="搜索构件/组件..."
                class="pl-10"
              />
            </div>
            <Select v-model="selectedComponentCategory">
              <SelectTrigger class="w-48">
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分类</SelectItem>
                <SelectItem value="frame">框架系统</SelectItem>
                <SelectItem value="glass">玻璃系统</SelectItem>
                <SelectItem value="sealing">密封系统</SelectItem>
                <SelectItem value="hardware">五金系统</SelectItem>
                <SelectItem value="insulation">保温系统</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 构件/组件列表 -->
          <div class="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
            <div
              v-for="item in filteredComponentLibrary"
              :key="item.id"
              class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
              @click="selectComponentFromLibrary(item)"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="font-medium text-sm">{{ item.name }}</div>
                  <div class="text-xs text-gray-500 mt-1">{{ item.description }}</div>
                  <div class="flex items-center gap-2 mt-2">
                    <Badge variant="outline" class="text-xs">{{ item.category }}</Badge>
                    <span class="text-xs text-gray-500">{{ item.code }}</span>
                  </div>

                  <!-- 预览参数 -->
                  <div v-if="item.parameters && item.parameters.length > 0" class="mt-2">
                    <div class="text-xs text-gray-600 mb-1">包含参数:</div>
                    <div class="flex flex-wrap gap-1">
                      <span
                        v-for="param in item.parameters.slice(0, 3)"
                        :key="param.name"
                        class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded"
                      >
                        {{ param.displayName }}
                      </span>
                      <span v-if="item.parameters.length > 3" class="text-xs text-gray-500">
                        +{{ item.parameters.length - 3 }}个
                      </span>
                    </div>
                  </div>
                </div>

                <div class="text-right">
                  <div class="text-xs text-gray-500">{{ item.source || '标准库' }}</div>
                  <div v-if="item.estimatedCost" class="text-xs text-green-600 mt-1">
                    约 ¥{{ item.estimatedCost }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showComponentLibrary = false">
            取消
          </Button>
          <Button @click="createNewComponent">
            创建新{{ node.type === 'assembly' ? '构件' : '组件' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Toggle } from '@/components/ui/toggle';
import { Badge } from '@/components/ui/badge';
import {
  Info, Calculator, Settings, Package, Shield, Plus, X, Search,
  Ruler, Palette, Zap, ToggleLeft, Box, Layers, Component
} from 'lucide-vue-next';

interface NodeData {
  id: string;
  name: string;
  code?: string;
  description?: string;
  type: string;
  quantity?: number;
  unit?: string;
  parameters?: any[];
  constraints?: any[];
  properties?: Record<string, any>;
  materialCategoryId?: string;
  quantityFormula?: string;
  costFormula?: string;
  libraryItemId?: string;
  libraryItemName?: string;
  libraryItemSource?: string;
}

interface Props {
  node: NodeData;
  isMultiple?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  update: [nodeData: NodeData];
}>();

// 响应式数据
const localNode = ref<NodeData>({ ...props.node });
const originalNode = ref<NodeData>({ ...props.node });
const showParameterLibrary = ref(false);
const showComponentLibrary = ref(false);
const parameterSearchQuery = ref('');
const selectedParameterCategory = ref('all');
const componentSearchQuery = ref('');
const selectedComponentCategory = ref('all');

// 参数分类
const parameterCategories = [
  { value: 'all', label: '全部' },
  { value: 'dimension', label: '尺寸' },
  { value: 'material', label: '材质' },
  { value: 'feature', label: '功能' },
  { value: 'option', label: '选项' }
];

// 预定义参数库 - 针对玻璃深加工企业
const parameterLibrary = ref([
  // 尺寸参数
  {
    id: 'width',
    name: 'width',
    displayName: '宽度',
    type: 'dimension',
    inputType: 'number',
    unit: 'mm',
    defaultValue: 1000,
    minValue: 100,
    maxValue: 10000,
    required: true,
    description: '产品宽度尺寸，适用于防火窗、隔断等',
    category: 'dimension'
  },
  {
    id: 'height',
    name: 'height',
    displayName: '高度',
    type: 'dimension',
    inputType: 'number',
    unit: 'mm',
    defaultValue: 1000,
    minValue: 100,
    maxValue: 10000,
    required: true,
    description: '产品高度尺寸',
    category: 'dimension'
  },
  {
    id: 'thickness',
    name: 'thickness',
    displayName: '厚度',
    type: 'dimension',
    inputType: 'number',
    unit: 'mm',
    defaultValue: 10,
    minValue: 1,
    maxValue: 100,
    required: true,
    description: '玻璃或型材厚度',
    category: 'dimension'
  },
  {
    id: 'glass_thickness',
    name: 'glass_thickness',
    displayName: '玻璃厚度',
    type: 'dimension',
    inputType: 'select',
    unit: 'mm',
    defaultValue: '6',
    required: true,
    description: '防火玻璃厚度规格',
    category: 'dimension',
    options: [
      { value: '6', label: '6mm', additionalCost: 0 },
      { value: '8', label: '8mm', additionalCost: 50 },
      { value: '10', label: '10mm', additionalCost: 100 },
      { value: '12', label: '12mm', additionalCost: 150 }
    ]
  },
  // 材质参数
  {
    id: 'frame_material',
    name: 'frame_material',
    displayName: '框架材质',
    type: 'material',
    inputType: 'select',
    defaultValue: 'stainless_steel',
    required: true,
    description: '框架使用的材质类型',
    category: 'material',
    options: [
      { value: 'stainless_steel', label: '不锈钢', additionalCost: 0 },
      { value: 'aluminum', label: '铝合金', additionalCost: -20 },
      { value: 'steel', label: '碳钢', additionalCost: -50 }
    ]
  },
  {
    id: 'glass_type',
    name: 'glass_type',
    displayName: '玻璃类型',
    type: 'material',
    inputType: 'select',
    defaultValue: 'fire_resistant',
    required: true,
    description: '玻璃的功能类型',
    category: 'material',
    options: [
      { value: 'fire_resistant', label: '防火玻璃', additionalCost: 0 },
      { value: 'tempered', label: '钢化玻璃', additionalCost: -30 },
      { value: 'laminated', label: '夹胶玻璃', additionalCost: 20 },
      { value: 'insulated', label: '中空玻璃', additionalCost: 80 }
    ]
  },
  // 功能参数
  {
    id: 'fire_rating',
    name: 'fire_rating',
    displayName: '防火等级',
    type: 'feature',
    inputType: 'select',
    defaultValue: '60min',
    required: true,
    description: '防火窗的防火时间等级',
    category: 'feature',
    options: [
      { value: '30min', label: '30分钟', additionalCost: 0 },
      { value: '60min', label: '60分钟', additionalCost: 100 },
      { value: '90min', label: '90分钟', additionalCost: 200 },
      { value: '120min', label: '120分钟', additionalCost: 300 }
    ]
  },
  {
    id: 'opening_method',
    name: 'opening_method',
    displayName: '开启方式',
    type: 'feature',
    inputType: 'select',
    defaultValue: 'fixed',
    required: true,
    description: '窗户的开启方式',
    category: 'feature',
    options: [
      { value: 'fixed', label: '固定窗', additionalCost: 0 },
      { value: 'casement', label: '平开窗', additionalCost: 150 },
      { value: 'sliding', label: '推拉窗', additionalCost: 200 },
      { value: 'top_hung', label: '上悬窗', additionalCost: 180 }
    ]
  },
  // 选项参数
  {
    id: 'surface_treatment',
    name: 'surface_treatment',
    displayName: '表面处理',
    type: 'option',
    inputType: 'select',
    defaultValue: 'anodized',
    required: false,
    description: '型材表面处理工艺',
    category: 'option',
    options: [
      { value: 'anodized', label: '阳极氧化', additionalCost: 0 },
      { value: 'powder_coating', label: '粉末喷涂', additionalCost: 30 },
      { value: 'fluorocarbon', label: '氟碳喷涂', additionalCost: 80 },
      { value: 'wood_grain', label: '木纹转印', additionalCost: 120 }
    ]
  }
]);

// 构件/组件库数据 - 针对玻璃深加工企业
const componentLibrary = ref([
  // 框架系统构件
  {
    id: 'frame_main_vertical',
    name: '主立柱框架',
    code: 'FMV-001',
    description: '防火窗主立柱，承重结构',
    category: 'frame',
    type: 'assembly',
    source: '标准库',
    estimatedCost: 280,
    parameters: [
      { name: 'height', displayName: '高度', type: 'dimension', inputType: 'number', unit: 'mm', required: true },
      { name: 'profile_type', displayName: '型材规格', type: 'material', inputType: 'select', required: true }
    ]
  },
  {
    id: 'frame_horizontal',
    name: '横梁框架',
    code: 'FH-001',
    description: '防火窗横梁，连接立柱',
    category: 'frame',
    type: 'assembly',
    source: '标准库',
    estimatedCost: 180,
    parameters: [
      { name: 'width', displayName: '宽度', type: 'dimension', inputType: 'number', unit: 'mm', required: true },
      { name: 'profile_type', displayName: '型材规格', type: 'material', inputType: 'select', required: true }
    ]
  },
  // 玻璃系统组件
  {
    id: 'fire_glass_panel',
    name: '防火玻璃面板',
    code: 'FGP-001',
    description: '符合国标的防火玻璃',
    category: 'glass',
    type: 'component',
    source: '标准库',
    estimatedCost: 450,
    parameters: [
      { name: 'width', displayName: '宽度', type: 'dimension', inputType: 'number', unit: 'mm', required: true },
      { name: 'height', displayName: '高度', type: 'dimension', inputType: 'number', unit: 'mm', required: true },
      { name: 'thickness', displayName: '厚度', type: 'dimension', inputType: 'select', unit: 'mm', required: true },
      { name: 'fire_rating', displayName: '防火等级', type: 'feature', inputType: 'select', required: true }
    ]
  },
  // 密封系统组件
  {
    id: 'sealing_strip',
    name: '密封胶条',
    code: 'SS-001',
    description: '防火密封胶条，耐高温',
    category: 'sealing',
    type: 'component',
    source: '标准库',
    estimatedCost: 25,
    parameters: [
      { name: 'length', displayName: '长度', type: 'dimension', inputType: 'number', unit: 'mm', required: true },
      { name: 'material_type', displayName: '材质类型', type: 'material', inputType: 'select', required: true }
    ]
  },
  // 五金系统组件
  {
    id: 'hinge_set',
    name: '铰链组',
    code: 'HS-001',
    description: '防火窗专用铰链，承重型',
    category: 'hardware',
    type: 'component',
    source: '标准库',
    estimatedCost: 120,
    parameters: [
      { name: 'load_capacity', displayName: '承重能力', type: 'feature', inputType: 'select', unit: 'kg', required: true },
      { name: 'material', displayName: '材质', type: 'material', inputType: 'select', required: true }
    ]
  }
]);

// 计算属性
const hasChanges = computed(() => {
  return JSON.stringify(localNode.value) !== JSON.stringify(originalNode.value);
});

const filteredParameterLibrary = computed(() => {
  let filtered = parameterLibrary.value;

  if (selectedParameterCategory.value !== 'all') {
    filtered = filtered.filter(p => p.category === selectedParameterCategory.value);
  }

  if (parameterSearchQuery.value.trim()) {
    const query = parameterSearchQuery.value.toLowerCase();
    filtered = filtered.filter(p =>
      p.displayName.toLowerCase().includes(query) ||
      p.description.toLowerCase().includes(query)
    );
  }

  return filtered;
});

const filteredComponentLibrary = computed(() => {
  let filtered = componentLibrary.value;

  if (selectedComponentCategory.value !== 'all') {
    filtered = filtered.filter(c => c.category === selectedComponentCategory.value);
  }

  if (componentSearchQuery.value.trim()) {
    const query = componentSearchQuery.value.toLowerCase();
    filtered = filtered.filter(c =>
      c.name.toLowerCase().includes(query) ||
      c.description.toLowerCase().includes(query) ||
      c.code.toLowerCase().includes(query)
    );
  }

  return filtered;
});

// 方法
const getNodeIcon = (type: string) => {
  const iconMap = {
    product: Box,
    assembly: Layers,
    component: Component,
    default: Package
  };
  return iconMap[type] || iconMap.default;
};

const getNodeIconColor = (type: string) => {
  const colorMap = {
    product: 'text-blue-600',
    assembly: 'text-green-600',
    component: 'text-orange-600',
    default: 'text-gray-600'
  };
  return colorMap[type] || colorMap.default;
};

const getNodeTypeLabel = (type: string) => {
  const labelMap = {
    product: '产品',
    assembly: '构件',
    component: '组件'
  };
  return labelMap[type] || type;
};

const getParameterIcon = (type: string) => {
  const iconMap = {
    dimension: Ruler,
    material: Palette,
    feature: Zap,
    option: ToggleLeft,
    default: Settings
  };
  return iconMap[type] || iconMap.default;
};

// 字段更新处理
const handleFieldUpdate = (field: string, value: any) => {
  (localNode.value as any)[field] = value;
};

const handleMaterialCategoryChange = (categoryId: string) => {
  localNode.value.materialCategoryId = categoryId;
};

// 参数管理
const updateParameter = (index: number, param: any) => {
  if (!localNode.value.parameters) localNode.value.parameters = [];
  localNode.value.parameters[index] = { ...param };
};

const removeParameter = (index: number) => {
  if (!localNode.value.parameters) return;
  localNode.value.parameters.splice(index, 1);
};

const addParameterOption = (paramIndex: number) => {
  const param = localNode.value.parameters?.[paramIndex];
  if (!param) return;

  if (!param.options) param.options = [];
  param.options.push({
    value: '',
    label: '',
    additionalCost: 0
  });
};

const removeParameterOption = (paramIndex: number, optionIndex: number) => {
  const param = localNode.value.parameters?.[paramIndex];
  if (!param?.options) return;

  param.options.splice(optionIndex, 1);
  updateParameter(paramIndex, param);
};

// 约束管理
const addConstraint = () => {
  if (!localNode.value.constraints) localNode.value.constraints = [];

  const newConstraint = {
    id: `const_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    name: '',
    expression: '',
    errorMessage: '',
    severity: 'error'
  };

  localNode.value.constraints.push(newConstraint);
};

const updateConstraint = (index: number, constraint: any) => {
  if (!localNode.value.constraints) localNode.value.constraints = [];
  localNode.value.constraints[index] = { ...constraint };
};

const removeConstraint = (index: number) => {
  if (!localNode.value.constraints) return;
  localNode.value.constraints.splice(index, 1);
};

// 参数库相关方法
const selectParameterFromLibrary = (param: any) => {
  if (!localNode.value.parameters) localNode.value.parameters = [];

  const newParam = {
    ...param,
    id: `param_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  };

  localNode.value.parameters.push(newParam);
  showParameterLibrary.value = false;
};

const createNewParameter = () => {
  if (!localNode.value.parameters) localNode.value.parameters = [];

  const newParam = {
    id: `param_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    name: '',
    displayName: '',
    type: 'dimension',
    inputType: 'number',
    required: false,
    defaultValue: '',
    helpText: ''
  };

  localNode.value.parameters.push(newParam);
  showParameterLibrary.value = false;
};

// 构件/组件库相关方法
const unlinkFromLibrary = () => {
  localNode.value.libraryItemId = undefined;
  localNode.value.libraryItemName = undefined;
  localNode.value.libraryItemSource = undefined;
};

const selectComponentFromLibrary = (item: any) => {
  // 从库中选择构件/组件，复制其配置
  localNode.value.libraryItemId = item.id;
  localNode.value.libraryItemName = item.name;
  localNode.value.libraryItemSource = item.source;

  // 复制参数配置
  if (item.parameters) {
    localNode.value.parameters = item.parameters.map((param: any) => ({
      ...param,
      id: `param_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    }));
  }

  // 如果是组件，复制物料映射信息
  if (item.type === 'component' && item.materialCategoryId) {
    localNode.value.materialCategoryId = item.materialCategoryId;
    localNode.value.quantityFormula = item.quantityFormula;
    localNode.value.costFormula = item.costFormula;
  }

  showComponentLibrary.value = false;
};

const createNewComponent = () => {
  // 创建新的构件/组件，保持当前配置
  showComponentLibrary.value = false;
};

// 保存和重置
const saveChanges = () => {
  emit('update', { ...localNode.value });
  originalNode.value = { ...localNode.value };
};

const resetChanges = () => {
  localNode.value = { ...originalNode.value };
};

// 监听props变化
watch(() => props.node, (newNode) => {
  localNode.value = { ...newNode };
  originalNode.value = { ...newNode };
}, { deep: true });
</script>