<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[90vw] h-[90vh] max-w-none max-h-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Package class="w-5 h-5" />
          产品结构详情
          <Badge v-if="structure" :variant="getProductTypeVariant(structure.productType)">
            {{ getProductTypeText(structure.productType) }}
          </Badge>
          <Badge v-if="structure" :variant="getStatusVariant(structure.status)">
            {{ getStatusText(structure.status) }}
          </Badge>
        </DialogTitle>
        <DialogDescription v-if="structure">
          {{ structure.description || '暂无描述' }}
        </DialogDescription>
      </DialogHeader>

      <div v-if="structure" class="flex-1 overflow-hidden">
        <Tabs v-model="activeTab" class="h-full flex flex-col">
          <TabsList class="grid w-full grid-cols-6">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="structure">结构树</TabsTrigger>
            <TabsTrigger value="parameters">参数</TabsTrigger>
            <TabsTrigger value="configuration">配置</TabsTrigger>
            <TabsTrigger value="validation">验证</TabsTrigger>
            <TabsTrigger value="versions">版本</TabsTrigger>
          </TabsList>

          <!-- 概览 -->
          <TabsContent value="overview" class="flex-1 overflow-auto p-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- 基本信息 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg">基本信息</CardTitle>
                </CardHeader>
                <CardContent class="space-y-3">
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <Label class="text-sm text-gray-600">结构编码</Label>
                      <p class="font-medium">{{ structure.code }}</p>
                    </div>
                    <div>
                      <Label class="text-sm text-gray-600">结构名称</Label>
                      <p class="font-medium">{{ structure.name }}</p>
                    </div>
                  </div>
                  
                  <div class="grid grid-cols-3 gap-4">
                    <div>
                      <Label class="text-sm text-gray-600">产品类型</Label>
                      <p class="font-medium">{{ getProductTypeText(structure.productType) }}</p>
                    </div>
                    <div>
                      <Label class="text-sm text-gray-600">产品类别</Label>
                      <p class="font-medium">{{ structure.category }}</p>
                    </div>
                    <div>
                      <Label class="text-sm text-gray-600">子类别</Label>
                      <p class="font-medium">{{ structure.subCategory || '-' }}</p>
                    </div>
                  </div>

                  <div>
                    <Label class="text-sm text-gray-600">应用场景</Label>
                    <div class="flex flex-wrap gap-1 mt-1">
                      <Badge v-for="app in structure.applications" :key="app" variant="outline" class="text-xs">
                        {{ app }}
                      </Badge>
                      <span v-if="!structure.applications || structure.applications.length === 0" class="text-sm text-gray-400">暂无</span>
                    </div>
                  </div>

                  <div>
                    <Label class="text-sm text-gray-600">标签</Label>
                    <div class="flex flex-wrap gap-1 mt-1">
                      <Badge v-for="tag in structure.tags" :key="tag" variant="secondary" class="text-xs">
                        {{ tag }}
                      </Badge>
                      <span v-if="!structure.tags || structure.tags.length === 0" class="text-sm text-gray-400">暂无</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- 统计信息 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg">统计信息</CardTitle>
                </CardHeader>
                <CardContent class="space-y-4">
                  <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                      <div class="text-2xl font-bold text-blue-600">{{ structure.productParameters?.length || 0 }}</div>
                      <div class="text-sm text-gray-600">产品参数</div>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                      <div class="text-2xl font-bold text-green-600">{{ structure.configurationOptions?.length || 0 }}</div>
                      <div class="text-sm text-gray-600">配置选项</div>
                    </div>
                  </div>

                  <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                      <div class="text-2xl font-bold text-purple-600">{{ structure.productConstraints?.length || 0 }}</div>
                      <div class="text-sm text-gray-600">约束条件</div>
                    </div>
                    <div class="text-center p-3 bg-orange-50 rounded-lg">
                      <div class="text-2xl font-bold text-orange-600">{{ structure.versionHistory?.length || 0 }}</div>
                      <div class="text-sm text-gray-600">版本历史</div>
                    </div>
                  </div>

                  <div class="pt-3 border-t">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <Label class="text-gray-600">创建时间</Label>
                        <p>{{ formatDateTime(structure.createdAt) }}</p>
                      </div>
                      <div>
                        <Label class="text-gray-600">更新时间</Label>
                        <p>{{ formatDateTime(structure.updatedAt) }}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <!-- 结构树 -->
          <TabsContent value="structure" class="flex-1 overflow-auto p-4">
            <Card>
              <CardHeader>
                <CardTitle class="text-lg">产品结构树</CardTitle>
              </CardHeader>
              <CardContent>
                <div v-if="structure.rootAssembly" class="space-y-4">
                  <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <Package class="w-6 h-6 text-blue-600" />
                    <div>
                      <h4 class="font-medium">{{ structure.rootAssembly.assemblyName }}</h4>
                      <p class="text-sm text-gray-600">{{ structure.rootAssembly.assemblyCode }}</p>
                    </div>
                    <Badge variant="outline">根构件</Badge>
                  </div>
                  
                  <!-- 这里可以展示更详细的结构树 -->
                  <div class="text-center py-8 text-gray-500">
                    <Package class="w-12 h-12 text-gray-300 mx-auto mb-2" />
                    <p>结构树可视化组件待实现</p>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  暂无根构件定义
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 参数 -->
          <TabsContent value="parameters" class="flex-1 overflow-auto p-4">
            <Card>
              <CardHeader>
                <CardTitle class="text-lg">产品参数</CardTitle>
              </CardHeader>
              <CardContent>
                <div v-if="structure.productParameters && structure.productParameters.length > 0" class="space-y-3">
                  <div v-for="param in structure.productParameters" :key="param.id" class="p-3 border rounded-lg">
                    <div class="flex items-center justify-between">
                      <div>
                        <div class="flex items-center gap-2">
                          <span class="font-medium">{{ param.displayName }}</span>
                          <Badge variant="outline">{{ param.type }}</Badge>
                          <Badge v-if="param.required" variant="destructive" class="text-xs">必填</Badge>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">{{ param.description }}</p>
                        <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                          <span>默认值: {{ param.defaultValue || '-' }}</span>
                          <span v-if="param.unit">单位: {{ param.unit }}</span>
                          <span>分类: {{ param.category }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  暂无产品参数
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 配置选项 -->
          <TabsContent value="configuration" class="flex-1 overflow-auto p-4">
            <Card>
              <CardHeader>
                <CardTitle class="text-lg">配置选项</CardTitle>
              </CardHeader>
              <CardContent>
                <div v-if="structure.configurationOptions && structure.configurationOptions.length > 0" class="space-y-3">
                  <div v-for="option in structure.configurationOptions" :key="option.id" class="p-3 border rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">{{ option.displayName }}</span>
                        <Badge variant="outline">{{ option.type }}</Badge>
                        <Badge v-if="option.required" variant="destructive" class="text-xs">必选</Badge>
                      </div>
                    </div>
                    <p class="text-sm text-gray-600 mb-2">{{ option.description }}</p>
                    <div class="text-xs text-gray-500">
                      {{ option.choices?.length || 0 }} 个选项
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  暂无配置选项
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 验证结果 -->
          <TabsContent value="validation" class="flex-1 overflow-auto p-4">
            <Card>
              <CardHeader>
                <CardTitle class="text-lg">验证结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div v-if="structure.lastValidationResult" class="space-y-4">
                  <!-- 验证摘要 -->
                  <div class="grid grid-cols-4 gap-4 text-center">
                    <div class="p-3 bg-red-50 rounded-lg">
                      <div class="text-2xl font-bold text-red-600">{{ structure.lastValidationResult.summary.totalErrors }}</div>
                      <div class="text-sm text-gray-600">错误</div>
                    </div>
                    <div class="p-3 bg-yellow-50 rounded-lg">
                      <div class="text-2xl font-bold text-yellow-600">{{ structure.lastValidationResult.summary.totalWarnings }}</div>
                      <div class="text-sm text-gray-600">警告</div>
                    </div>
                    <div class="p-3 bg-blue-50 rounded-lg">
                      <div class="text-2xl font-bold text-blue-600">{{ structure.lastValidationResult.summary.totalSuggestions }}</div>
                      <div class="text-sm text-gray-600">建议</div>
                    </div>
                    <div class="p-3 bg-green-50 rounded-lg">
                      <div class="text-2xl font-bold text-green-600">{{ structure.lastValidationResult.isValid ? '通过' : '失败' }}</div>
                      <div class="text-sm text-gray-600">状态</div>
                    </div>
                  </div>

                  <div class="text-sm text-gray-500">
                    最后验证时间: {{ formatDateTime(structure.lastValidationTime || '') }}
                  </div>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  <CheckCircle class="w-12 h-12 text-gray-300 mx-auto mb-2" />
                  <p>暂无验证结果</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 版本历史 -->
          <TabsContent value="versions" class="flex-1 overflow-auto p-4">
            <Card>
              <CardHeader>
                <CardTitle class="text-lg">版本历史</CardTitle>
              </CardHeader>
              <CardContent>
                <div v-if="structure.versionHistory && structure.versionHistory.length > 0" class="space-y-3">
                  <div v-for="version in structure.versionHistory" :key="version.id" class="p-3 border rounded-lg">
                    <div class="flex items-center justify-between">
                      <div>
                        <div class="flex items-center gap-2">
                          <span class="font-medium">版本 {{ version.versionNumber }}</span>
                          <Badge v-if="version.isCurrent" variant="default" class="text-xs">当前版本</Badge>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">{{ version.changeDescription }}</p>
                        <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                          <span>{{ formatDateTime(version.changeDate) }}</span>
                          <span>{{ version.changedBy }}</span>
                          <span>{{ version.changeRecords?.length || 0 }} 个变更</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  <History class="w-12 h-12 text-gray-300 mx-auto mb-2" />
                  <p>暂无版本历史</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <DialogFooter class="border-t pt-4">
        <Button variant="outline" @click="$emit('update:open', false)">
          关闭
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Package, CheckCircle, History } from 'lucide-vue-next';

import type { ProductStructure, ProductType } from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  structure?: ProductStructure | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:open': [open: boolean];
}>();

// 响应式数据
const activeTab = ref('overview');

// 工具函数
const getProductTypeText = (type: ProductType): string => {
  const typeMap = {
    partition: '隔断',
    window: '窗户',
    door: '门',
    curtain_wall: '幕墙',
    other: '其他'
  };
  return typeMap[type] || type;
};

const getProductTypeVariant = (type: ProductType) => {
  const variantMap = {
    partition: 'default',
    window: 'secondary',
    door: 'outline',
    curtain_wall: 'destructive',
    other: 'secondary'
  };
  return variantMap[type] || 'default';
};

const getStatusText = (status: string): string => {
  const statusMap = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return statusMap[status] || status;
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'default';
};

const formatDateTime = (dateString: string): string => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString('zh-CN');
};
</script>

<style scoped>
/* 自定义样式 */
</style>
