<template>
  <div 
    class="flex items-center p-4 hover:bg-gray-50 transition-colors"
    :class="{ 'bg-blue-50': selected }"
  >
    <!-- 选择框 -->
    <Checkbox
      :checked="selected"
      @update:checked="$emit('select', component.id)"
      class="mr-4"
    />

    <!-- 组件图标 -->
    <div class="flex-shrink-0 mr-4">
      <div 
        class="w-10 h-10 rounded-lg flex items-center justify-center"
        :class="getTypeIconClass(component.componentType)"
      >
        <component :is="getTypeIcon(component.componentType)" class="w-5 h-5" />
      </div>
    </div>

    <!-- 主要信息 -->
    <div class="flex-1 min-w-0">
      <div class="flex items-center gap-3 mb-1">
        <h3 class="text-sm font-medium text-gray-900 truncate">
          {{ component.name }}
        </h3>
        <Badge :variant="getStatusVariant(component.status)">
          {{ getStatusText(component.status) }}
        </Badge>
        <Badge v-if="!component.reusable" variant="outline">
          不可重用
        </Badge>
      </div>
      
      <div class="flex items-center gap-4 text-xs text-gray-500">
        <span>编码: {{ component.code }}</span>
        <span>类型: {{ getTypeText(component.componentType) }}</span>
        <span v-if="component.materialCategoryName">
          分类: {{ component.materialCategoryName }}
        </span>
        <span>参数: {{ component.parameters.length }}</span>
        <span>约束: {{ component.constraints.length }}</span>
      </div>
      
      <div v-if="component.description" class="text-xs text-gray-600 mt-1 truncate">
        {{ component.description }}
      </div>
      
      <!-- 标签 -->
      <div v-if="component.tags.length > 0" class="flex flex-wrap gap-1 mt-2">
        <Badge
          v-for="tag in component.tags.slice(0, 3)"
          :key="tag"
          variant="secondary"
          class="text-xs"
        >
          {{ tag }}
        </Badge>
        <Badge
          v-if="component.tags.length > 3"
          variant="secondary"
          class="text-xs"
        >
          +{{ component.tags.length - 3 }}
        </Badge>
      </div>
    </div>

    <!-- 时间信息 -->
    <div class="flex-shrink-0 text-right text-xs text-gray-500 mr-4">
      <div>创建: {{ formatDate(component.createdAt) }}</div>
      <div>更新: {{ formatDate(component.updatedAt) }}</div>
      <div class="text-gray-400">{{ component.createdBy }}</div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex-shrink-0">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreHorizontal class="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem @click="$emit('edit', component)">
            <Edit class="w-4 h-4 mr-2" />
            编辑
          </DropdownMenuItem>
          <DropdownMenuItem @click="$emit('duplicate', component)">
            <Copy class="w-4 h-4 mr-2" />
            复制
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="viewDetails">
            <Eye class="w-4 h-4 mr-2" />
            查看详情
          </DropdownMenuItem>
          <DropdownMenuItem @click="exportComponent">
            <Download class="w-4 h-4 mr-2" />
            导出
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            @click="$emit('delete', component)"
            class="text-red-600"
          >
            <Trash2 class="w-4 h-4 mr-2" />
            删除
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- 详情对话框 -->
    <ComponentDetailsDialog
      v-model:open="detailsOpen"
      :component="component"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { 
  MoreHorizontal, Edit, Copy, Eye, Download, Trash2,
  Package, Square, Wrench, Shield, HelpCircle
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import ComponentDetailsDialog from './ComponentDetailsDialog.vue';
import type { Component, ComponentType } from '@/types/product-structure';

// Props
interface Props {
  component: Component;
  selected: boolean;
}

// Emits
interface Emits {
  (e: 'select', componentId: string): void;
  (e: 'edit', component: Component): void;
  (e: 'duplicate', component: Component): void;
  (e: 'delete', component: Component): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const detailsOpen = ref(false);

// 方法
const getTypeIcon = (type: ComponentType) => {
  const iconMap = {
    frame: Square,
    glass: Package,
    hardware: Wrench,
    seal: Shield,
    other: HelpCircle
  };
  return iconMap[type] || HelpCircle;
};

const getTypeIconClass = (type: ComponentType) => {
  const classMap = {
    frame: 'bg-blue-100 text-blue-600',
    glass: 'bg-green-100 text-green-600',
    hardware: 'bg-orange-100 text-orange-600',
    seal: 'bg-purple-100 text-purple-600',
    other: 'bg-gray-100 text-gray-600'
  };
  return classMap[type] || 'bg-gray-100 text-gray-600';
};

const getTypeText = (type: ComponentType) => {
  const textMap = {
    frame: '框料',
    glass: '玻璃',
    hardware: '五金',
    seal: '密封',
    other: '其他'
  };
  return textMap[type] || '其他';
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary' as const,
    active: 'default' as const,
    deprecated: 'destructive' as const,
    archived: 'outline' as const
  };
  return variantMap[status] || 'secondary';
};

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return '今天';
  } else if (diffDays <= 7) {
    return `${diffDays}天前`;
  } else if (diffDays <= 30) {
    return `${Math.ceil(diffDays / 7)}周前`;
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
};

const viewDetails = () => {
  detailsOpen.value = true;
};

const exportComponent = () => {
  // 导出组件数据
  const dataStr = JSON.stringify(props.component, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${props.component.code}_${props.component.name}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
</script>
