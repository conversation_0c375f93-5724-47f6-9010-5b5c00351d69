<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[80vw] h-[80vh] max-w-none max-h-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <CheckCircle class="w-5 h-5" />
          结构验证结果
          <Badge v-if="validationResult" :variant="validationResult.isValid ? 'default' : 'destructive'">
            {{ validationResult.isValid ? '验证通过' : '验证失败' }}
          </Badge>
        </DialogTitle>
        <DialogDescription v-if="structure">
          产品结构"{{ structure.name }}"的验证结果和修复建议
        </DialogDescription>
      </DialogHeader>

      <div v-if="validationResult" class="flex-1 overflow-hidden">
        <Tabs v-model="activeTab" class="h-full flex flex-col">
          <TabsList class="grid w-full grid-cols-4">
            <TabsTrigger value="summary">验证摘要</TabsTrigger>
            <TabsTrigger value="errors">错误详情</TabsTrigger>
            <TabsTrigger value="warnings">警告信息</TabsTrigger>
            <TabsTrigger value="suggestions">优化建议</TabsTrigger>
          </TabsList>

          <!-- 验证摘要 -->
          <TabsContent value="summary" class="flex-1 overflow-auto p-4">
            <div class="space-y-6">
              <!-- 总体状态 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg">验证摘要</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-4 gap-4 text-center">
                    <div class="p-4 bg-red-50 rounded-lg">
                      <div class="text-3xl font-bold text-red-600">{{ validationResult.summary.totalErrors }}</div>
                      <div class="text-sm text-gray-600">错误</div>
                    </div>
                    <div class="p-4 bg-yellow-50 rounded-lg">
                      <div class="text-3xl font-bold text-yellow-600">{{ validationResult.summary.totalWarnings }}</div>
                      <div class="text-sm text-gray-600">警告</div>
                    </div>
                    <div class="p-4 bg-blue-50 rounded-lg">
                      <div class="text-3xl font-bold text-blue-600">{{ validationResult.summary.totalSuggestions }}</div>
                      <div class="text-sm text-gray-600">建议</div>
                    </div>
                    <div class="p-4 bg-green-50 rounded-lg">
                      <div class="text-3xl font-bold text-green-600">{{ validationResult.summary.criticalErrors }}</div>
                      <div class="text-sm text-gray-600">严重错误</div>
                    </div>
                  </div>
                  
                  <div class="mt-4 text-sm text-gray-500 text-center">
                    验证时间: {{ formatDateTime(validationResult.validationTime) }}
                  </div>
                </CardContent>
              </Card>

              <!-- 结构完整性 -->
              <Card v-if="'structureIntegrity' in validationResult">
                <CardHeader>
                  <CardTitle class="text-lg">结构完整性检查</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-2 gap-4">
                    <div class="space-y-3">
                      <div class="flex items-center justify-between">
                        <span class="text-sm">缺失组件检查</span>
                        <Badge :variant="validationResult.structureIntegrity.hasMissingComponents ? 'destructive' : 'default'">
                          {{ validationResult.structureIntegrity.hasMissingComponents ? '有缺失' : '完整' }}
                        </Badge>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm">循环引用检查</span>
                        <Badge :variant="validationResult.structureIntegrity.hasCircularReferences ? 'destructive' : 'default'">
                          {{ validationResult.structureIntegrity.hasCircularReferences ? '存在' : '正常' }}
                        </Badge>
                      </div>
                    </div>
                    <div class="space-y-3">
                      <div class="flex items-center justify-between">
                        <span class="text-sm">约束有效性检查</span>
                        <Badge :variant="validationResult.structureIntegrity.hasInvalidConstraints ? 'destructive' : 'default'">
                          {{ validationResult.structureIntegrity.hasInvalidConstraints ? '无效' : '有效' }}
                        </Badge>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm">公式有效性检查</span>
                        <Badge :variant="validationResult.structureIntegrity.hasInvalidFormulas ? 'destructive' : 'default'">
                          {{ validationResult.structureIntegrity.hasInvalidFormulas ? '无效' : '有效' }}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- 性能分析 -->
              <Card v-if="'performanceAnalysis' in validationResult">
                <CardHeader>
                  <CardTitle class="text-lg">性能分析</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-2 gap-4">
                    <div class="space-y-3">
                      <div class="flex items-center justify-between">
                        <span class="text-sm">复杂度评分</span>
                        <Badge variant="outline">{{ validationResult.performanceAnalysis.complexityScore }}</Badge>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm">最大层级深度</span>
                        <Badge variant="outline">{{ validationResult.performanceAnalysis.maxDepth }}</Badge>
                      </div>
                    </div>
                    <div class="space-y-3">
                      <div class="flex items-center justify-between">
                        <span class="text-sm">组件总数</span>
                        <Badge variant="outline">{{ validationResult.performanceAnalysis.totalComponents }}</Badge>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm">构件总数</span>
                        <Badge variant="outline">{{ validationResult.performanceAnalysis.totalAssemblies }}</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <!-- 错误详情 -->
          <TabsContent value="errors" class="flex-1 overflow-auto p-4">
            <div v-if="validationResult.errors && validationResult.errors.length > 0" class="space-y-3">
              <div v-for="error in validationResult.errors" :key="error.id" class="p-4 border border-red-200 rounded-lg bg-red-50">
                <div class="flex items-start gap-3">
                  <AlertTriangle class="w-5 h-5 text-red-500 mt-0.5" />
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                      <span class="font-medium text-red-800">{{ error.message }}</span>
                      <Badge variant="destructive" class="text-xs">{{ error.severity }}</Badge>
                    </div>
                    
                    <div class="text-sm text-red-700 mb-2">
                      位置: {{ error.location.objectType }} - {{ error.location.objectId }}
                      <span v-if="error.location.fieldName"> ({{ error.location.fieldName }})</span>
                    </div>

                    <div v-if="error.suggestions && error.suggestions.length > 0" class="space-y-1">
                      <Label class="text-xs text-red-600">修复建议:</Label>
                      <ul class="text-sm text-red-700 list-disc list-inside">
                        <li v-for="suggestion in error.suggestions" :key="suggestion">{{ suggestion }}</li>
                      </ul>
                    </div>
                  </div>
                  
                  <Button variant="outline" size="sm" @click="fixError(error)">
                    修复
                  </Button>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-gray-500">
              <CheckCircle class="w-12 h-12 text-green-500 mx-auto mb-2" />
              <p>没有发现错误</p>
            </div>
          </TabsContent>

          <!-- 警告信息 -->
          <TabsContent value="warnings" class="flex-1 overflow-auto p-4">
            <div v-if="validationResult.warnings && validationResult.warnings.length > 0" class="space-y-3">
              <div v-for="warning in validationResult.warnings" :key="warning.id" class="p-4 border border-yellow-200 rounded-lg bg-yellow-50">
                <div class="flex items-start gap-3">
                  <AlertTriangle class="w-5 h-5 text-yellow-500 mt-0.5" />
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                      <span class="font-medium text-yellow-800">{{ warning.message }}</span>
                      <Badge variant="outline" class="text-xs">警告</Badge>
                    </div>
                    
                    <div class="text-sm text-yellow-700 mb-2">
                      位置: {{ warning.location.objectType }} - {{ warning.location.objectId }}
                    </div>

                    <div v-if="warning.suggestions && warning.suggestions.length > 0" class="space-y-1">
                      <Label class="text-xs text-yellow-600">改进建议:</Label>
                      <ul class="text-sm text-yellow-700 list-disc list-inside">
                        <li v-for="suggestion in warning.suggestions" :key="suggestion">{{ suggestion }}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-gray-500">
              <CheckCircle class="w-12 h-12 text-green-500 mx-auto mb-2" />
              <p>没有发现警告</p>
            </div>
          </TabsContent>

          <!-- 优化建议 -->
          <TabsContent value="suggestions" class="flex-1 overflow-auto p-4">
            <div v-if="validationResult.suggestions && validationResult.suggestions.length > 0" class="space-y-3">
              <div v-for="suggestion in validationResult.suggestions" :key="suggestion.id" class="p-4 border border-blue-200 rounded-lg bg-blue-50">
                <div class="flex items-start gap-3">
                  <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                    <span class="text-white text-xs">!</span>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                      <span class="font-medium text-blue-800">{{ suggestion.suggestion }}</span>
                      <Badge variant="outline" class="text-xs">{{ suggestion.type }}</Badge>
                    </div>
                    
                    <div class="text-sm text-blue-700 mb-2">
                      预期收益: {{ suggestion.expectedBenefit }}
                    </div>

                    <div class="flex items-center gap-4 text-xs text-blue-600">
                      <span>实施难度: {{ getDifficultyText(suggestion.implementationDifficulty) }}</span>
                      <span>影响范围: {{ suggestion.impactScope.join(', ') }}</span>
                    </div>
                  </div>
                  
                  <Button variant="outline" size="sm" @click="applySuggestion(suggestion)">
                    应用
                  </Button>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-gray-500">
              <CheckCircle class="w-12 h-12 text-blue-500 mx-auto mb-2" />
              <p>暂无优化建议</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <DialogFooter class="border-t pt-4">
        <div class="flex items-center justify-between w-full">
          <div v-if="validationResult" class="text-sm text-gray-500">
            验证时间: {{ formatDateTime(validationResult.validationTime) }}
          </div>
          
          <div class="flex gap-2">
            <Button variant="outline" @click="revalidate">
              <CheckCircle class="w-4 h-4 mr-2" />
              重新验证
            </Button>
            <Button variant="outline" @click="$emit('update:open', false)">
              关闭
            </Button>
          </div>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { CheckCircle, AlertTriangle } from 'lucide-vue-next';

import type { 
  ProductStructure, 
  StructureValidationResult,
  ValidationError,
  ValidationWarning,
  OptimizationSuggestion
} from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  validationResult?: StructureValidationResult | null;
  structure?: ProductStructure | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:open': [open: boolean];
  'apply-fix': [fixAction: any];
}>();

// 响应式数据
const activeTab = ref('summary');

// 方法
const fixError = (error: ValidationError) => {
  console.log('修复错误:', error);
  emit('apply-fix', { type: 'fix_error', error });
};

const applySuggestion = (suggestion: OptimizationSuggestion) => {
  console.log('应用建议:', suggestion);
  emit('apply-fix', { type: 'apply_suggestion', suggestion });
};

const revalidate = () => {
  console.log('重新验证');
  // 这里可以触发重新验证
};

// 工具函数
const getDifficultyText = (difficulty: string): string => {
  const difficultyMap = {
    low: '低',
    medium: '中',
    high: '高'
  };
  return difficultyMap[difficulty] || difficulty;
};

const formatDateTime = (dateString: string): string => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString('zh-CN');
};
</script>

<style scoped>
/* 自定义样式 */
</style>
