<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[800px] h-[600px] max-w-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle>选择组件</DialogTitle>
        <DialogDescription>
          从组件库中选择要添加到构件的组件
        </DialogDescription>
      </DialogHeader>

      <div class="flex-1 overflow-hidden flex flex-col">
        <!-- 搜索和筛选 -->
        <div class="flex gap-4 p-4 border-b">
          <div class="flex-1 relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              v-model="searchQuery"
              placeholder="搜索组件名称或编码..."
              class="pl-10"
            />
          </div>
          <Select v-model="selectedType">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="组件类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="frame">框架组件</SelectItem>
              <SelectItem value="glass">玻璃组件</SelectItem>
              <SelectItem value="hardware">五金组件</SelectItem>
              <SelectItem value="seal">密封组件</SelectItem>
              <SelectItem value="other">其他组件</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- 组件列表 -->
        <div class="flex-1 overflow-y-auto">
          <div v-if="loading" class="flex items-center justify-center py-12">
            <div class="flex items-center gap-2 text-gray-500">
              <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span>加载中...</span>
            </div>
          </div>

          <div v-else-if="filteredComponents.length === 0" class="text-center py-12">
            <Package class="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无组件</h3>
            <p class="text-gray-500">没有找到符合条件的组件</p>
          </div>

          <div v-else class="divide-y">
            <div
              v-for="component in filteredComponents"
              :key="component.id"
              class="p-4 hover:bg-gray-50 cursor-pointer transition-colors"
              :class="{ 'bg-blue-50': selectedComponent?.id === component.id }"
              @click="selectComponent(component)"
              @dblclick="confirmSelection"
            >
              <div class="flex items-start gap-3">
                <!-- 组件图标 -->
                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 flex-shrink-0">
                  <component :is="getComponentIcon(component.componentType)" class="w-5 h-5 text-gray-600" />
                </div>

                <!-- 组件信息 -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center gap-2 mb-1">
                    <h3 class="text-base font-medium text-gray-900 truncate">
                      {{ component.name }}
                    </h3>
                    <Badge :variant="getStatusVariant(component.status)" class="text-xs">
                      {{ getStatusText(component.status) }}
                    </Badge>
                  </div>
                  
                  <div class="flex items-center gap-4 text-sm text-gray-500 mb-2">
                    <span>编码: {{ component.code }}</span>
                    <span>类型: {{ getTypeText(component.componentType) }}</span>
                    <span>参数: {{ (component.parameters || []).length }}个</span>
                  </div>

                  <p v-if="component.description" class="text-sm text-gray-600 line-clamp-2">
                    {{ component.description }}
                  </p>

                  <!-- 标签 -->
                  <div v-if="(component.tags || []).length > 0" class="flex items-center gap-1 flex-wrap mt-2">
                    <Badge
                      v-for="tag in (component.tags || []).slice(0, 3)"
                      :key="tag"
                      variant="secondary"
                      class="text-xs"
                    >
                      {{ tag }}
                    </Badge>
                    <span v-if="(component.tags || []).length > 3" class="text-xs text-gray-400">
                      +{{ (component.tags || []).length - 3 }}
                    </span>
                  </div>
                </div>

                <!-- 选择指示器 -->
                <div v-if="selectedComponent?.id === component.id" class="flex-shrink-0">
                  <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                    <Check class="w-3 h-3 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 组件详情预览 -->
        <div v-if="selectedComponent" class="border-t bg-gray-50 p-4">
          <h4 class="font-medium mb-2">组件详情</h4>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-500">物料分类:</span>
              <span class="ml-2">{{ selectedComponent.materialCategoryName }}</span>
            </div>
            <div>
              <span class="text-gray-500">是否可重用:</span>
              <span class="ml-2">{{ selectedComponent.reusable ? '是' : '否' }}</span>
            </div>
          </div>
          
          <div v-if="(selectedComponent.parameters || []).length > 0" class="mt-3">
            <span class="text-gray-500 text-sm">参数列表:</span>
            <div class="mt-1 flex flex-wrap gap-1">
              <Badge
                v-for="param in (selectedComponent.parameters || []).slice(0, 5)"
                :key="param.id"
                variant="outline"
                class="text-xs"
              >
                {{ param.displayName }}
              </Badge>
              <Badge v-if="(selectedComponent.parameters || []).length > 5" variant="outline" class="text-xs">
                +{{ (selectedComponent.parameters || []).length - 5 }}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button @click="$emit('update:open', false)" variant="outline">
          取消
        </Button>
        <Button
          @click="confirmSelection"
          :disabled="!selectedComponent"
          class="bg-blue-600 hover:bg-blue-700"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加组件
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import {
  Search,
  Package,
  Check,
  Plus,
  Square,
  Wrench,
  Shield,
  HelpCircle
} from 'lucide-vue-next';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { componentService } from '@/services/componentService';
import type { Component, ComponentType } from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'select', component: Component): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const components = ref<Component[]>([]);
const searchQuery = ref('');
const selectedType = ref('all');
const selectedComponent = ref<Component | null>(null);

// 计算属性
const filteredComponents = computed(() => {
  return components.value.filter(component => {
    // 搜索筛选
    if (searchQuery.value) {
      const search = searchQuery.value.toLowerCase();
      const matchesSearch =
        component.name.toLowerCase().includes(search) ||
        component.code.toLowerCase().includes(search) ||
        (component.description && component.description.toLowerCase().includes(search));

      if (!matchesSearch) return false;
    }

    // 类型筛选
    if (selectedType.value && selectedType.value !== 'all' && component.componentType !== selectedType.value) {
      return false;
    }

    // 只显示激活状态的组件
    return component.status === 'active';
  });
});

// 方法
const loadComponents = async () => {
  loading.value = true;
  try {
    const result = await componentService.getComponents();
    components.value = result.components;
  } catch (error) {
    console.error('加载组件失败:', error);
    components.value = [];
  } finally {
    loading.value = false;
  }
};

const selectComponent = (component: Component) => {
  selectedComponent.value = component;
};

const confirmSelection = () => {
  if (selectedComponent.value) {
    emit('select', selectedComponent.value);
    selectedComponent.value = null;
  }
};

const getComponentIcon = (type: ComponentType) => {
  const iconMap = {
    frame: Square,
    glass: Package,
    hardware: Wrench,
    seal: Shield,
    other: HelpCircle
  };
  return iconMap[type] || HelpCircle;
};

const getTypeText = (type: ComponentType) => {
  const textMap = {
    frame: '框架组件',
    glass: '玻璃组件',
    hardware: '五金组件',
    seal: '密封组件',
    other: '其他组件'
  };
  return textMap[type] || '未知类型';
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'secondary';
};

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    active: '激活',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};

// 监听对话框打开
watch(() => props.open, (open) => {
  if (open) {
    loadComponents();
    selectedComponent.value = null;
    searchQuery.value = '';
    selectedType.value = 'all';
  }
});

// 组件挂载时加载数据
onMounted(() => {
  if (props.open) {
    loadComponents();
  }
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
