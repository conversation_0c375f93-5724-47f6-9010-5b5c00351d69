<template>
  <Card 
    class="relative cursor-pointer transition-all duration-200 hover:shadow-md"
    :class="{ 
      'ring-2 ring-blue-500 ring-offset-2': selected,
      'hover:shadow-lg': !selected 
    }"
    @click="$emit('select', assembly.id)"
  >
    <!-- 选择指示器 -->
    <div v-if="selected" class="absolute top-2 left-2 z-10">
      <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
        <Check class="w-3 h-3 text-white" />
      </div>
    </div>

    <!-- 状态指示器 -->
    <div class="absolute top-2 right-2">
      <Badge :variant="getStatusVariant(assembly.status)" class="text-xs">
        {{ getStatusText(assembly.status) }}
      </Badge>
    </div>

    <CardContent class="p-4 pt-8">
      <!-- 类型图标和基本信息 -->
      <div class="flex items-start gap-3 mb-3">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-gray-100 flex-shrink-0">
          <component :is="getTypeIcon(assembly.assemblyType)" class="w-6 h-6 text-gray-600" />
        </div>
        <div class="flex-1 min-w-0">
          <h3 class="font-medium text-gray-900 truncate mb-1">
            {{ assembly.name }}
          </h3>
          <p class="text-sm text-gray-500 truncate">
            {{ assembly.code }}
          </p>
          <p class="text-xs text-gray-400 mt-1">
            {{ getTypeText(assembly.assemblyType) }}
          </p>
        </div>
      </div>

      <!-- 描述 -->
      <p v-if="assembly.description" class="text-sm text-gray-600 mb-3 line-clamp-2">
        {{ assembly.description }}
      </p>

      <!-- 统计信息 -->
      <div class="grid grid-cols-2 gap-2 mb-3 text-xs">
        <div class="flex items-center gap-1 text-gray-500">
          <Package class="w-3 h-3" />
          <span>{{ (assembly.componentInstances || []).length }}个组件</span>
        </div>
        <div class="flex items-center gap-1 text-gray-500">
          <Layers class="w-3 h-3" />
          <span>{{ (assembly.subAssemblies || []).length }}个子构件</span>
        </div>
        <div class="flex items-center gap-1 text-gray-500">
          <Clock class="w-3 h-3" />
          <span>{{ assembly.assemblyProcess?.totalEstimatedTime || 0 }}分钟</span>
        </div>
        <div class="flex items-center gap-1 text-gray-500">
          <CheckCircle class="w-3 h-3" />
          <span>{{ (assembly.qualityRequirements || []).length }}项质检</span>
        </div>
      </div>

      <!-- 标签 -->
      <div v-if="(assembly.tags || []).length > 0" class="flex items-center gap-1 flex-wrap mb-3">
        <Badge
          v-for="tag in (assembly.tags || []).slice(0, 2)"
          :key="tag"
          variant="secondary"
          class="text-xs"
        >
          {{ tag }}
        </Badge>
        <span v-if="(assembly.tags || []).length > 2" class="text-xs text-gray-400">
          +{{ (assembly.tags || []).length - 2 }}
        </span>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center gap-2 pt-2 border-t">
        <Button
          @click.stop="$emit('design', assembly)"
          variant="outline"
          size="sm"
          class="flex-1 text-blue-600 border-blue-200 hover:bg-blue-50"
        >
          <PenTool class="w-3 h-3 mr-1" />
          设计
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button @click.stop variant="ghost" size="sm" class="px-2">
              <MoreHorizontal class="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="$emit('edit', assembly)">
              <Edit class="w-4 h-4 mr-2" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem @click="showDetails = true">
              <Eye class="w-4 h-4 mr-2" />
              查看详情
            </DropdownMenuItem>
            <DropdownMenuItem @click="$emit('duplicate', assembly)">
              <Copy class="w-4 h-4 mr-2" />
              复制
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              @click="$emit('delete', assembly)"
              class="text-red-600 focus:text-red-600"
            >
              <Trash2 class="w-4 h-4 mr-2" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </CardContent>

    <!-- 详情对话框 -->
    <AssemblyDetailsDialog
      :open="showDetails"
      :assembly="assembly"
      @update:open="showDetails = $event"
    />
  </Card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  Check,
  Clock,
  CheckCircle,
  PenTool,
  MoreHorizontal,
  Edit,
  Eye,
  Copy,
  Trash2,
  Layers,
  Square,
  Package,
  Wrench,
  HelpCircle
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import AssemblyDetailsDialog from './AssemblyDetailsDialog.vue';
import type { Assembly, AssemblyType } from '@/types/product-structure';

// Props
interface Props {
  assembly: Assembly;
  selected: boolean;
}

// Emits
interface Emits {
  (e: 'select', assemblyId: string): void;
  (e: 'edit', assembly: Assembly): void;
  (e: 'duplicate', assembly: Assembly): void;
  (e: 'delete', assembly: Assembly): void;
  (e: 'design', assembly: Assembly): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const showDetails = ref(false);

// 方法
const getTypeIcon = (type: AssemblyType) => {
  const iconMap = {
    frame_assembly: Square,
    glass_assembly: Package,
    hardware_assembly: Wrench,
    complete_assembly: Layers
  };
  return iconMap[type] || HelpCircle;
};

const getTypeText = (type: AssemblyType) => {
  const textMap = {
    frame_assembly: '框架构件',
    glass_assembly: '玻璃构件',
    hardware_assembly: '五金构件',
    complete_assembly: '完整构件'
  };
  return textMap[type] || '未知类型';
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'secondary';
};

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    active: '激活',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
