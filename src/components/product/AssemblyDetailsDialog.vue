<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[80vw] h-[80vh] max-w-none max-h-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <component :is="getTypeIcon(assembly.assemblyType)" class="w-5 h-5" />
          {{ assembly.name }}
        </DialogTitle>
        <DialogDescription>
          构件编码: {{ assembly.code }} | 类型: {{ getTypeText(assembly.assemblyType) }}
        </DialogDescription>
      </DialogHeader>

      <div class="flex-1 overflow-hidden">
        <Tabs v-model="activeTab" class="h-full flex flex-col">
          <TabsList class="grid w-full grid-cols-4">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="components">组件实例</TabsTrigger>
            <TabsTrigger value="process">装配工艺</TabsTrigger>
            <TabsTrigger value="quality">质量要求</TabsTrigger>
          </TabsList>

          <div class="flex-1 overflow-hidden">
            <!-- 概览 -->
            <TabsContent value="overview" class="h-full overflow-y-auto p-6">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 基本信息 -->
                <Card>
                  <CardHeader>
                    <CardTitle class="text-lg">基本信息</CardTitle>
                  </CardHeader>
                  <CardContent class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <label class="text-sm font-medium text-gray-500">构件名称</label>
                        <p class="text-sm text-gray-900">{{ assembly.name }}</p>
                      </div>
                      <div>
                        <label class="text-sm font-medium text-gray-500">构件编码</label>
                        <p class="text-sm text-gray-900">{{ assembly.code }}</p>
                      </div>
                      <div>
                        <label class="text-sm font-medium text-gray-500">构件类型</label>
                        <p class="text-sm text-gray-900">{{ getTypeText(assembly.assemblyType) }}</p>
                      </div>
                      <div>
                        <label class="text-sm font-medium text-gray-500">状态</label>
                        <Badge :variant="getStatusVariant(assembly.status)">
                          {{ getStatusText(assembly.status) }}
                        </Badge>
                      </div>
                    </div>
                    
                    <div v-if="assembly.description">
                      <label class="text-sm font-medium text-gray-500">描述</label>
                      <p class="text-sm text-gray-900 mt-1">{{ assembly.description }}</p>
                    </div>

                    <div v-if="(assembly.tags || []).length > 0">
                      <label class="text-sm font-medium text-gray-500">标签</label>
                      <div class="flex flex-wrap gap-1 mt-1">
                        <Badge v-for="tag in (assembly.tags || [])" :key="tag" variant="secondary">
                          {{ tag }}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <!-- 统计信息 -->
                <Card>
                  <CardHeader>
                    <CardTitle class="text-lg">统计信息</CardTitle>
                  </CardHeader>
                  <CardContent class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                      <div class="flex items-center gap-2">
                        <Package class="w-4 h-4 text-blue-500" />
                        <div>
                          <p class="text-sm font-medium">组件实例</p>
                          <p class="text-lg font-bold">{{ (assembly.componentInstances || []).length }}</p>
                        </div>
                      </div>
                      <div class="flex items-center gap-2">
                        <Layers class="w-4 h-4 text-green-500" />
                        <div>
                          <p class="text-sm font-medium">子构件</p>
                          <p class="text-lg font-bold">{{ (assembly.subAssemblies || []).length }}</p>
                        </div>
                      </div>
                      <div class="flex items-center gap-2">
                        <Clock class="w-4 h-4 text-orange-500" />
                        <div>
                          <p class="text-sm font-medium">装配时间</p>
                          <p class="text-lg font-bold">{{ assembly.assemblyProcess?.totalEstimatedTime || 0 }}分钟</p>
                        </div>
                      </div>
                      <div class="flex items-center gap-2">
                        <CheckCircle class="w-4 h-4 text-purple-500" />
                        <div>
                          <p class="text-sm font-medium">质检项目</p>
                          <p class="text-lg font-bold">{{ (assembly.qualityRequirements || []).length }}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <!-- 版本信息 -->
                <Card>
                  <CardHeader>
                    <CardTitle class="text-lg">版本信息</CardTitle>
                  </CardHeader>
                  <CardContent class="space-y-3">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <label class="font-medium text-gray-500">版本号</label>
                        <p class="text-gray-900">v{{ assembly.version }}</p>
                      </div>
                      <div>
                        <label class="font-medium text-gray-500">创建者</label>
                        <p class="text-gray-900">{{ assembly.createdBy }}</p>
                      </div>
                      <div>
                        <label class="font-medium text-gray-500">创建时间</label>
                        <p class="text-gray-900">{{ formatDate(assembly.createdAt) }}</p>
                      </div>
                      <div>
                        <label class="font-medium text-gray-500">更新时间</label>
                        <p class="text-gray-900">{{ formatDate(assembly.updatedAt) }}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <!-- 构件参数 -->
                <Card v-if="(assembly.assemblyParameters || []).length > 0">
                  <CardHeader>
                    <CardTitle class="text-lg">构件参数</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div class="space-y-3">
                      <div
                        v-for="param in (assembly.assemblyParameters || [])"
                        :key="param.id"
                        class="flex items-center justify-between py-2 border-b last:border-b-0"
                      >
                        <div>
                          <p class="font-medium">{{ param.displayName }}</p>
                          <p class="text-sm text-gray-500">{{ param.description }}</p>
                        </div>
                        <div class="text-right">
                          <p class="font-medium">{{ param.defaultValue }}{{ param.unit }}</p>
                          <p class="text-sm text-gray-500">{{ param.type }}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <!-- 组件实例 -->
            <TabsContent value="components" class="h-full overflow-y-auto p-6">
              <div v-if="(assembly.componentInstances || []).length === 0" class="text-center py-12">
                <Package class="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无组件实例</h3>
                <p class="text-gray-500">此构件还没有添加任何组件实例</p>
              </div>

              <div v-else class="space-y-4">
                <div
                  v-for="instance in (assembly.componentInstances || [])"
                  :key="instance.id"
                  class="border rounded-lg p-4"
                >
                  <div class="flex items-start justify-between mb-3">
                    <div>
                      <h4 class="font-medium">{{ instance.instanceName }}</h4>
                      <p class="text-sm text-gray-500">{{ instance.componentName }} ({{ instance.componentCode }})</p>
                    </div>
                    <div class="text-right">
                      <p class="font-medium">数量: {{ instance.quantity }}</p>
                      <p class="text-sm text-gray-500">{{ instance.optional ? '可选' : '必需' }}</p>
                    </div>
                  </div>

                  <div v-if="instance.description" class="mb-3">
                    <p class="text-sm text-gray-600">{{ instance.description }}</p>
                  </div>

                  <div v-if="Object.keys(instance.parameterValues).length > 0">
                    <h5 class="text-sm font-medium mb-2">参数值</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                      <div
                        v-for="(value, key) in instance.parameterValues"
                        :key="key"
                        class="flex justify-between"
                      >
                        <span class="text-gray-500">{{ key }}:</span>
                        <span class="font-medium">{{ value }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <!-- 装配工艺 -->
            <TabsContent value="process" class="h-full overflow-y-auto p-6">
              <div v-if="!assembly.assemblyProcess || (assembly.assemblyProcess.steps || []).length === 0" class="text-center py-12">
                <Settings class="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无装配工艺</h3>
                <p class="text-gray-500">此构件还没有定义装配工艺流程</p>
              </div>

              <div v-else class="space-y-6">
                <!-- 工艺概览 -->
                <Card>
                  <CardHeader>
                    <CardTitle>{{ assembly.assemblyProcess.processName }}</CardTitle>
                    <CardDescription v-if="assembly.assemblyProcess.description">
                      {{ assembly.assemblyProcess.description }}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div class="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p class="text-2xl font-bold text-blue-600">{{ (assembly.assemblyProcess?.steps || []).length }}</p>
                        <p class="text-sm text-gray-500">工艺步骤</p>
                      </div>
                      <div>
                        <p class="text-2xl font-bold text-green-600">{{ assembly.assemblyProcess?.totalEstimatedTime || 0 }}</p>
                        <p class="text-sm text-gray-500">预计时间(分钟)</p>
                      </div>
                      <div>
                        <p class="text-2xl font-bold text-orange-600">{{ (assembly.assemblyProcess?.requiredSkills || []).length }}</p>
                        <p class="text-sm text-gray-500">技能要求</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <!-- 工艺步骤 -->
                <div class="space-y-4">
                  <h3 class="text-lg font-medium">工艺步骤</h3>
                  <div
                    v-for="step in assembly.assemblyProcess.steps"
                    :key="step.id"
                    class="border rounded-lg p-4"
                  >
                    <div class="flex items-start justify-between mb-2">
                      <div>
                        <h4 class="font-medium">步骤 {{ step.stepNumber }}: {{ step.stepName }}</h4>
                        <p class="text-sm text-gray-600 mt-1">{{ step.description }}</p>
                      </div>
                      <div class="text-right">
                        <p class="font-medium">{{ step.estimatedTime }}分钟</p>
                      </div>
                    </div>

                    <div v-if="step.resources && step.resources.length > 0" class="mt-3">
                      <h5 class="text-sm font-medium mb-1">所需资源</h5>
                      <div class="flex flex-wrap gap-1">
                        <Badge v-for="resource in step.resources" :key="resource" variant="outline">
                          {{ resource }}
                        </Badge>
                      </div>
                    </div>

                    <div v-if="step.qualityCheckPoints && step.qualityCheckPoints.length > 0" class="mt-3">
                      <h5 class="text-sm font-medium mb-1">质量检查点</h5>
                      <div class="space-y-1">
                        <div
                          v-for="qcp in step.qualityCheckPoints"
                          :key="qcp.id"
                          class="text-sm text-gray-600"
                        >
                          • {{ qcp.checkPoint }} ({{ qcp.tolerance }})
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <!-- 质量要求 -->
            <TabsContent value="quality" class="h-full overflow-y-auto p-6">
              <div v-if="(assembly.qualityRequirements || []).length === 0" class="text-center py-12">
                <CheckCircle class="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无质量要求</h3>
                <p class="text-gray-500">此构件还没有定义质量要求</p>
              </div>

              <div v-else class="space-y-4">
                <div
                  v-for="requirement in (assembly.qualityRequirements || [])"
                  :key="requirement.id"
                  class="border rounded-lg p-4"
                >
                  <div class="flex items-start justify-between mb-2">
                    <h4 class="font-medium">{{ requirement.requirementName }}</h4>
                    <Badge variant="outline">{{ requirement.standard }}</Badge>
                  </div>
                  
                  <p class="text-sm text-gray-600 mb-3">{{ requirement.description }}</p>
                  
                  <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <label class="font-medium text-gray-500">公差要求</label>
                      <p class="text-gray-900">{{ requirement.tolerance }}</p>
                    </div>
                    <div>
                      <label class="font-medium text-gray-500">检测方法</label>
                      <p class="text-gray-900">{{ requirement.testMethod }}</p>
                    </div>
                  </div>
                  
                  <div class="mt-3">
                    <label class="font-medium text-gray-500">验收标准</label>
                    <p class="text-sm text-gray-900">{{ requirement.acceptanceCriteria }}</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      <DialogFooter>
        <Button @click="$emit('update:open', false)" variant="outline">
          关闭
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  Package,
  Layers,
  Clock,
  CheckCircle,
  Settings,
  Square,
  Wrench,
  HelpCircle
} from 'lucide-vue-next';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import type { Assembly, AssemblyType } from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  assembly: Assembly;
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const activeTab = ref('overview');

// 方法
const getTypeIcon = (type: AssemblyType) => {
  const iconMap = {
    frame_assembly: Square,
    glass_assembly: Package,
    hardware_assembly: Wrench,
    complete_assembly: Layers
  };
  return iconMap[type] || HelpCircle;
};

const getTypeText = (type: AssemblyType) => {
  const textMap = {
    frame_assembly: '框架构件',
    glass_assembly: '玻璃构件',
    hardware_assembly: '五金构件',
    complete_assembly: '完整构件'
  };
  return textMap[type] || '未知类型';
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'secondary';
};

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    active: '激活',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};
</script>
