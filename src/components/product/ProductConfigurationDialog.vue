<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>
          {{ configuration ? '编辑产品配置' : '新增产品配置' }}
        </DialogTitle>
        <DialogDescription>
          配置产品的基本信息和材料组成
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- 基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label for="name">产品名称 *</Label>
            <Input
              id="name"
              v-model="formData.name"
              placeholder="如：中空5#白玻+12a+5#lowe"
              required
            />
          </div>
          
          <div>
            <Label for="structureTemplate">结构模板 *</Label>
            <Select v-model="formData.structureTemplateId" required>
              <SelectTrigger>
                <SelectValue placeholder="选择结构模板" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem 
                  v-for="template in structureTemplates" 
                  :key="template.id" 
                  :value="template.id"
                >
                  {{ template.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label for="description">产品描述</Label>
          <Textarea
            id="description"
            v-model="formData.description"
            placeholder="详细描述产品特性和用途"
            rows="3"
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label for="category">产品分类</Label>
            <Select v-model="formData.category">
              <SelectTrigger>
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="中空玻璃">中空玻璃</SelectItem>
                <SelectItem value="夹胶玻璃">夹胶玻璃</SelectItem>
                <SelectItem value="钢化玻璃">钢化玻璃</SelectItem>
                <SelectItem value="单片玻璃">单片玻璃</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label for="subCategory">子分类</Label>
            <Input
              id="subCategory"
              v-model="formData.subCategory"
              placeholder="如：节能玻璃"
            />
          </div>

          <div>
            <Label for="status">状态</Label>
            <Select v-model="formData.status">
              <SelectTrigger>
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">激活</SelectItem>
                <SelectItem value="draft">草稿</SelectItem>
                <SelectItem value="deprecated">已弃用</SelectItem>
                <SelectItem value="archived">已归档</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 标签 -->
        <div>
          <Label>产品标签</Label>
          <div class="flex flex-wrap gap-2 mt-2">
            <Badge
              v-for="(tag, index) in formData.tags"
              :key="index"
              variant="secondary"
              class="cursor-pointer"
              @click="removeTag(index)"
            >
              {{ tag }}
              <X class="w-3 h-3 ml-1" />
            </Badge>
            <Input
              v-model="newTag"
              placeholder="添加标签"
              class="w-32 h-6 text-xs"
              @keyup.enter="addTag"
            />
          </div>
        </div>

        <!-- 材料组成 -->
        <div>
          <div class="flex items-center justify-between mb-4">
            <Label class="text-base font-medium">材料组成</Label>
            <Button type="button" variant="outline" size="sm" @click="addMaterialComponent">
              <Plus class="w-4 h-4 mr-2" />
              添加材料
            </Button>
          </div>

          <div class="space-y-4">
            <Card v-for="(component, index) in formData.materialComponents" :key="index">
              <CardContent class="p-4">
                <div class="flex items-start justify-between mb-4">
                  <h4 class="font-medium">材料组件 {{ index + 1 }}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="removeMaterialComponent(index)"
                    class="text-red-600"
                  >
                    <Trash2 class="w-4 h-4" />
                  </Button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <Label>组件名称</Label>
                    <Input v-model="component.componentName" placeholder="如：面玻" />
                  </div>
                  
                  <div>
                    <Label>材料名称</Label>
                    <Input v-model="component.materialVariantName" placeholder="如：5#白玻" />
                  </div>
                  
                  <div>
                    <Label>计算公式</Label>
                    <Input v-model="component.quantityFormula" placeholder="如：length*width/1000000" />
                  </div>
                  
                  <div>
                    <Label>单位</Label>
                    <Select v-model="component.unit">
                      <SelectTrigger>
                        <SelectValue placeholder="选择单位" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="㎡">㎡</SelectItem>
                        <SelectItem value="米">米</SelectItem>
                        <SelectItem value="片">片</SelectItem>
                        <SelectItem value="个">个</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div>
                    <Label>单位成本 (元)</Label>
                    <Input 
                      v-model.number="component.unitCost" 
                      type="number" 
                      step="0.01"
                      placeholder="0.00" 
                    />
                  </div>
                  
                  <div>
                    <Label>损耗率 (%)</Label>
                    <Input 
                      v-model.number="component.wastageRate" 
                      type="number" 
                      step="0.01"
                      placeholder="5.00" 
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <div v-if="formData.materialComponents.length === 0" class="text-center py-8 text-gray-500">
              <Package class="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>暂无材料组件，点击"添加材料"开始配置</p>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <DialogFooter>
          <Button type="button" variant="outline" @click="$emit('cancel')">
            取消
          </Button>
          <Button type="submit" :disabled="!isFormValid">
            {{ configuration ? '更新' : '创建' }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template><script 
setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import Badge from '@/components/ui/badge/Badge.vue';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, X, Trash2, Package } from 'lucide-vue-next';

import type { 
  ProductConfiguration, 
  ProductStructureTemplate, 
  MaterialComponent,
  LifecycleStatus 
} from '@/types/product';

// Props
interface Props {
  open: boolean;
  configuration?: ProductConfiguration | null;
  structureTemplates: ProductStructureTemplate[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:open': [open: boolean];
  'save': [configuration: ProductConfiguration];
  'cancel': [];
}>();

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  structureTemplateId: '',
  structureTemplateName: '',
  category: '',
  subCategory: '',
  status: 'draft' as LifecycleStatus,
  tags: [] as string[],
  materialComponents: [] as MaterialComponent[]
});

const newTag = ref('');

// 监听配置变化，初始化表单
watch(() => props.configuration, (config) => {
  if (config) {
    Object.assign(formData, {
      name: config.name,
      description: config.description,
      structureTemplateId: config.structureTemplateId,
      structureTemplateName: config.structureTemplateName,
      category: config.category,
      subCategory: config.subCategory,
      status: config.status,
      tags: [...config.tags],
      materialComponents: [...config.materialComponents]
    });
  } else {
    // 重置表单
    Object.assign(formData, {
      name: '',
      description: '',
      structureTemplateId: '',
      structureTemplateName: '',
      category: '',
      subCategory: '',
      status: 'draft',
      tags: [],
      materialComponents: []
    });
  }
}, { immediate: true });

// 表单验证
const isFormValid = computed(() => {
  return formData.name.trim() !== '' && 
         formData.structureTemplateId !== '' &&
         formData.materialComponents.length > 0;
});

// 添加标签
const addTag = () => {
  const tag = newTag.value.trim();
  if (tag && !formData.tags.includes(tag)) {
    formData.tags.push(tag);
    newTag.value = '';
  }
};

// 移除标签
const removeTag = (index: number) => {
  formData.tags.splice(index, 1);
};

// 添加材料组件
const addMaterialComponent = () => {
  const newComponent: MaterialComponent = {
    id: `mc_${Date.now()}`,
    componentId: '',
    componentName: '',
    materialVariantId: '',
    materialVariantName: '',
    quantityFormula: '',
    wastageRate: 0.05,
    unitCost: 0,
    unit: '㎡'
  };
  formData.materialComponents.push(newComponent);
};

// 移除材料组件
const removeMaterialComponent = (index: number) => {
  formData.materialComponents.splice(index, 1);
};

// 提交表单
const handleSubmit = () => {
  if (!isFormValid.value) return;

  // 获取结构模板名称
  const template = props.structureTemplates.find(t => t.id === formData.structureTemplateId);
  formData.structureTemplateName = template?.name || '';

  const configurationData: ProductConfiguration = {
    id: props.configuration?.id || `config_${Date.now()}`,
    name: formData.name,
    description: formData.description,
    structureTemplateId: formData.structureTemplateId,
    structureTemplateName: formData.structureTemplateName,
    materialComponents: formData.materialComponents,
    status: formData.status,
    usageCount: props.configuration?.usageCount || 0,
    lastUsedAt: props.configuration?.lastUsedAt,
    averageCost: calculateAverageCost(formData.materialComponents),
    tags: formData.tags,
    category: formData.category,
    subCategory: formData.subCategory,
    version: props.configuration?.version || 1,
    createdAt: props.configuration?.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: props.configuration?.createdBy || 'current-user',
    updatedBy: 'current-user'
  };

  emit('save', configurationData);
};

// 计算平均成本
const calculateAverageCost = (components: MaterialComponent[]): number => {
  return components.reduce((total, component) => total + (component.unitCost || 0), 0);
};
</script>

<style scoped>
/* 自定义样式 */
</style>