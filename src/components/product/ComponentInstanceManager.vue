<template>
  <div class="component-instance-manager">
    <div v-if="modelValue.length === 0" class="text-center py-12">
      <Package class="w-12 h-12 text-gray-300 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无组件实例</h3>
      <p class="text-gray-500">点击"添加组件"按钮开始添加组件实例</p>
    </div>

    <div v-else class="space-y-4">
      <div
        v-for="instance in modelValue"
        :key="instance.id"
        class="border rounded-lg p-4"
      >
        <div class="flex items-start justify-between mb-3">
          <div class="flex-1">
            <h4 class="font-medium">{{ instance.instanceName }}</h4>
            <p class="text-sm text-gray-500">{{ instance.componentName }} ({{ instance.componentCode }})</p>
          </div>
          <Button
            @click="removeInstance(instance.id)"
            variant="ghost"
            size="sm"
            class="text-red-500 hover:text-red-600"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <Label class="text-sm">实例名称</Label>
            <Input
              v-model="instance.instanceName"
              class="text-sm"
              @input="updateInstance"
            />
          </div>
          <div>
            <Label class="text-sm">数量</Label>
            <Input
              v-model.number="instance.quantity"
              type="number"
              min="1"
              class="text-sm"
              @input="updateInstance"
            />
          </div>
        </div>

        <div class="flex items-center space-x-2 mt-3">
          <Checkbox
            v-model="instance.optional"
            @update:model-value="updateInstance"
          />
          <Label class="text-sm">可选组件</Label>
        </div>

        <div v-if="instance.description" class="mt-3">
          <Label class="text-sm">描述</Label>
          <Textarea
            v-model="instance.description"
            class="text-sm"
            rows="2"
            @input="updateInstance"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Package, Trash2 } from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';

import type { ComponentInstance, ValidationResult } from '@/types/product-structure';

// Props
interface Props {
  modelValue: ComponentInstance[];
  assemblyParameters?: any[];
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: ComponentInstance[]): void;
  (e: 'validate', errors: ValidationResult[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 方法
const removeInstance = (instanceId: string) => {
  const filtered = props.modelValue.filter(instance => instance.id !== instanceId);
  emit('update:modelValue', filtered);
};

const updateInstance = () => {
  emit('update:modelValue', [...props.modelValue]);
};
</script>
