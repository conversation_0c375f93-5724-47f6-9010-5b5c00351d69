<template>
  <div class="product-structure-visual-editor">
    <!-- AUTOCAD风格工具栏 -->
    <VisualEditorToolbar
      :active-tool="activeTool"
      :current-layout="layoutType"
      :has-changes="hasChanges"
      :can-undo="canUndo"
      :can-redo="canRedo"
      :node-count="nodeCount"
      :edge-count="edgeCount"
      :zoom="viewState.zoom"
      @new="handleNewStructure"
      @open="handleOpenStructure"
      @save="saveStructure"
      @undo="undo"
      @redo="redo"
      @tool-change="handleToolChange"
      @zoom-in="zoomIn"
      @zoom-out="zoomOut"
      @fit-view="fitToView"
      @reset-view="resetZoom"
      @layout-change="handleLayoutChange"
      @validate="validateStructure"
      @help="showHelp"
    />

    <!-- 主编辑区域 -->
    <div class="editor-main flex-1 flex">
      <!-- 左侧组件库面板 -->
      <div class="component-library-panel w-64 border-r bg-white">
        <div class="p-4 border-b">
          <h4 class="font-medium mb-3">组件库</h4>
          <Input
            v-model="componentSearchKeyword"
            placeholder="搜索组件..."
            class="mb-3"
          />
        </div>
        
        <div class="p-4 space-y-2 overflow-y-auto">
          <div
            v-for="component in filteredComponents"
            :key="component.id"
            class="component-item p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
            draggable="true"
            @dragstart="handleComponentDragStart($event, component)"
          >
            <div class="flex items-center gap-2">
              <component :is="getComponentIcon(component.componentType)" class="w-4 h-4 text-gray-600" />
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium truncate">{{ component.name }}</p>
                <p class="text-xs text-gray-500 truncate">{{ component.code }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间可视化画布 -->
      <div class="visual-canvas flex-1 relative">
        <div
          ref="canvasContainer"
          class="w-full h-full"
          @drop="handleCanvasDrop"
          @dragover.prevent
        >
          <v-chart
            ref="chartRef"
            :option="chartOption"
            :theme="theme"
            autoresize
            class="w-full h-full"
            @click="handleChartClick"
            @contextmenu="handleChartContextMenu"
          />
        </div>
        
        <!-- 浮动工具栏 -->
        <div class="floating-toolbar absolute top-4 right-4 flex flex-col gap-2">
          <Button variant="outline" size="sm" @click="zoomIn">
            <ZoomIn class="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" @click="zoomOut">
            <ZoomOut class="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" @click="resetZoom">
            <RotateCcw class="w-4 h-4" />
          </Button>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel w-80 border-l bg-white">
        <div class="p-4 border-b">
          <h4 class="font-medium">属性面板</h4>
        </div>
        
        <div class="p-4 overflow-y-auto">
          <div v-if="selectedNode">
            <NodePropertyEditor
              :node="selectedNode"
              @update="handleNodeUpdate"
              @delete="handleNodeDelete"
            />
          </div>
          <div v-else class="text-center py-8 text-gray-500">
            <MousePointer class="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p class="text-sm">选择节点查看属性</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 上下文菜单 -->
    <ContextMenu
      v-model:open="contextMenuOpen"
      :position="contextMenuPosition"
      :items="contextMenuItems"
      @item-click="handleContextMenuClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { TreeChart, GraphChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent
} from 'echarts/components';
import VChart from 'vue-echarts';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import {
  Package,
  Layers,
  Square,
  Wrench,
  Maximize2,
  Undo,
  Redo,
  CheckCircle,
  Save,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  MousePointer
} from 'lucide-vue-next';

import NodePropertyEditor from './NodePropertyEditor.vue';
import ContextMenu from './ContextMenu.vue';
import VisualEditorToolbar from './VisualEditorToolbar.vue';

import { useProductStructureStore } from '@/stores/productStructureStore';
import { componentService } from '@/services/productService';
import { useDragDropManager } from '@/composables/useDragDropManager';
import { useAutoCADShortcuts } from '@/composables/useKeyboardShortcuts';
import type {
  ProductStructure,
  Component,
  VisualNode,
  VisualEdge,
  VisualLayoutConfig,
  VisualViewState,
  VisualOperation
} from '@/types/product-structure';

// 注册ECharts组件
use([
  CanvasRenderer,
  TreeChart,
  GraphChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent
]);

// Props
interface Props {
  structure?: ProductStructure;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

// Emits
const emit = defineEmits<{
  'update:structure': [structure: ProductStructure];
  'save': [structure: ProductStructure];
  'validate': [structure: ProductStructure];
  'new': [];
  'open': [];
}>();

// 响应式数据
const chartRef = ref();
const canvasContainer = ref();
const productStructureStore = useProductStructureStore();

// 拖拽管理器
const {
  dragState,
  registerDropZone,
  startComponentDrag,
  startNodeDrag,
  handleDragEnter,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  calculateNodePosition,
  autoLayout
} = useDragDropManager();

// 编辑器状态
const layoutType = ref<'tree' | 'force' | 'circular'>('tree');
const selectedNode = ref<VisualNode | null>(null);
const contextMenuOpen = ref(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const componentSearchKeyword = ref('');
const activeTool = ref<string>('select');

// 可视化数据
const visualNodes = ref<VisualNode[]>([]);
const visualEdges = ref<VisualEdge[]>([]);
const operationHistory = ref<VisualOperation[]>([]);
const historyIndex = ref(-1);

// 视图状态
const viewState = reactive<VisualViewState>({
  zoom: 1,
  center: { x: 0, y: 0 },
  selectedNodes: [],
  highlightedNodes: [],
  editingNode: undefined
});

// 布局配置
const layoutConfig = reactive<VisualLayoutConfig>({
  type: 'tree',
  nodeSpacing: {
    horizontal: 200,
    vertical: 150
  },
  autoLayout: true,
  animation: {
    enabled: true,
    duration: 1000,
    easing: 'cubicOut'
  }
});

// 计算属性
const nodeCount = computed(() => visualNodes.value.length);
const edgeCount = computed(() => visualEdges.value.length);
const hasChanges = computed(() => operationHistory.value.length > 0);
const canUndo = computed(() => historyIndex.value >= 0);
const canRedo = computed(() => historyIndex.value < operationHistory.value.length - 1);

// 组件库数据
const availableComponents = ref<Component[]>([]);
const filteredComponents = computed(() => {
  if (!componentSearchKeyword.value) return availableComponents.value;
  const keyword = componentSearchKeyword.value.toLowerCase();
  return availableComponents.value.filter(comp => 
    comp.name.toLowerCase().includes(keyword) ||
    comp.code.toLowerCase().includes(keyword)
  );
});

// 上下文菜单项
const contextMenuItems = computed(() => {
  if (!selectedNode.value) return [];
  
  const items = [
    { id: 'edit', label: '编辑节点', icon: 'Edit' },
    { id: 'duplicate', label: '复制节点', icon: 'Copy' },
    { id: 'delete', label: '删除节点', icon: 'Trash2', danger: true }
  ];
  
  if (selectedNode.value.type === 'assembly') {
    items.unshift(
      { id: 'add_component', label: '添加组件', icon: 'Plus' },
      { id: 'add_subassembly', label: '添加子构件', icon: 'Layers' }
    );
  }
  
  return items;
});

// 主题
const theme = computed(() => 'light');

// 核心方法实现
const loadComponents = async () => {
  try {
    const components = await componentService.getComponents();
    availableComponents.value = components;
  } catch (error) {
    console.error('加载组件库失败:', error);
  }
};

const convertStructureToVisualData = () => {
  if (!props.structure) return;
  rebuildVisualDataFromStructure(props.structure);
};

const processAssemblyInstance = (
  assembly: any,
  parentId: string,
  nodes: VisualNode[],
  edges: VisualEdge[],
  level: number
) => {
  // 创建构件节点
  const assemblyNode: VisualNode = {
    id: assembly.id,
    type: 'assembly',
    name: assembly.assemblyName || assembly.instanceName,
    label: assembly.assemblyName || assembly.instanceName,
    description: assembly.description,
    icon: 'Layers',
    color: '#10b981',
    size: 60,
    position: { x: level * 200, y: nodes.length * 100 },
    status: 'active',
    data: assembly,
    parent: parentId,
    level,
    expanded: true
  };
  nodes.push(assemblyNode);

  // 创建父子连接
  edges.push({
    id: `${parentId}-${assembly.id}`,
    source: parentId,
    target: assembly.id,
    type: 'hierarchy',
    style: { color: '#6b7280', width: 2 }
  });

  // 处理组件实例
  if (assembly.componentInstances) {
    assembly.componentInstances.forEach((component: any) => {
      processComponentInstance(component, assembly.id, nodes, edges, level + 1);
    });
  }

  // 处理子构件
  if (assembly.subAssemblies) {
    assembly.subAssemblies.forEach((subAssembly: any) => {
      processAssemblyInstance(subAssembly, assembly.id, nodes, edges, level + 1);
    });
  }
};

const processComponentInstance = (
  component: any,
  parentId: string,
  nodes: VisualNode[],
  edges: VisualEdge[],
  level: number
) => {
  const componentNode: VisualNode = {
    id: component.id,
    type: 'component',
    name: component.componentName || component.instanceName,
    label: component.componentName || component.instanceName,
    description: component.description,
    icon: 'Square',
    color: '#f59e0b',
    size: 40,
    position: { x: level * 200, y: nodes.length * 80 },
    optional: component.optional,
    status: component.optional ? 'warning' : 'active',
    data: component,
    parent: parentId,
    level
  };
  nodes.push(componentNode);

  // 创建连接
  edges.push({
    id: `${parentId}-${component.id}`,
    source: parentId,
    target: component.id,
    type: 'hierarchy',
    style: {
      color: component.optional ? '#f59e0b' : '#6b7280',
      width: component.optional ? 1 : 2,
      dashArray: component.optional ? '5,5' : undefined
    }
  });
};

// 交互功能实现
const handleComponentDragStart = (event: DragEvent, component: Component) => {
  startComponentDrag(event, component);
};

const handleCanvasDrop = (event: DragEvent) => {
  const dropData = handleDrop(event, 'canvas');
  if (!dropData) return;

  if (dropData.type === 'component') {
    addComponentToStructure(dropData.data, dropData.position);
  } else if (dropData.type === 'node') {
    moveNodeToPosition(dropData.data, dropData.position);
  }
};

const moveNodeToPosition = (node: VisualNode, position: { x: number; y: number }) => {
  const nodeIndex = visualNodes.value.findIndex(n => n.id === node.id);
  if (nodeIndex !== -1) {
    const oldPosition = { ...visualNodes.value[nodeIndex].position };
    visualNodes.value[nodeIndex].position = position;

    recordOperation('move_node', {
      nodeId: node.id,
      newPosition: position,
      oldPosition
    });
  }
};

const addComponentToStructure = (component: Component, position: { x: number; y: number }) => {
  // 查找最近的构件节点作为父节点
  const parentNode = findNearestAssemblyNode(position);

  // 创建新的组件实例节点
  const newNode: VisualNode = {
    id: `comp_inst_${Date.now()}`,
    type: 'component',
    name: component.name,
    label: component.name,
    description: component.description,
    icon: 'Square',
    color: '#f59e0b',
    size: 40,
    position,
    status: 'active',
    data: {
      componentId: component.id,
      componentCode: component.code,
      componentName: component.name,
      instanceName: component.name,
      quantity: 1,
      quantityType: 'fixed',
      parameters: component.parameters || [],
      parameterConfig: {
        parameterOverrides: {},
        parameterBindings: {},
        parameterFormulas: {},
        validationStatus: {}
      },
      quantityConfig: {
        fixedQuantity: 1
      },
      optional: false,
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    parent: parentNode?.id,
    level: parentNode ? parentNode.level + 1 : 1
  };

  visualNodes.value.push(newNode);

  // 如果有父节点，创建连接
  if (parentNode) {
    const newEdge: VisualEdge = {
      id: `${parentNode.id}-${newNode.id}`,
      source: parentNode.id,
      target: newNode.id,
      type: 'hierarchy',
      style: { color: '#6b7280', width: 2 }
    };
    visualEdges.value.push(newEdge);
  }

  recordOperation('add_node', newNode);
};

const findNearestAssemblyNode = (position: { x: number; y: number }): VisualNode | null => {
  const assemblyNodes = visualNodes.value.filter(n => n.type === 'assembly');
  if (assemblyNodes.length === 0) return null;

  let nearest = assemblyNodes[0];
  let minDistance = calculateDistance(position, nearest.position);

  assemblyNodes.forEach(node => {
    const distance = calculateDistance(position, node.position);
    if (distance < minDistance) {
      minDistance = distance;
      nearest = node;
    }
  });

  return minDistance < 200 ? nearest : null;
};

const calculateDistance = (pos1: { x: number; y: number }, pos2: { x: number; y: number }): number => {
  return Math.sqrt(Math.pow(pos1.x - pos2.x, 2) + Math.pow(pos1.y - pos2.y, 2));
};

// 工具栏事件处理
const handleNewStructure = () => {
  emit('new');
};

const handleOpenStructure = () => {
  emit('open');
};

const handleToolChange = (tool: string) => {
  activeTool.value = tool;

  // 根据工具类型更新鼠标样式
  if (canvasContainer.value) {
    const canvas = canvasContainer.value;
    canvas.style.cursor = getCursorForTool(tool);
  }
};

const handleLayoutChange = (layout: string) => {
  layoutType.value = layout as 'tree' | 'force' | 'circular';
  layoutConfig.type = layout as any;

  // 重新应用布局
  autoLayout(visualNodes.value, layoutType.value);
};

const showHelp = () => {
  // 显示帮助对话框
  console.log('显示帮助信息');
};

const getCursorForTool = (tool: string): string => {
  const cursorMap = {
    select: 'default',
    move: 'move',
    'add-component': 'crosshair',
    'add-assembly': 'crosshair'
  };
  return cursorMap[tool] || 'default';
};

// 快捷键操作
const selectAll = () => {
  viewState.selectedNodes = visualNodes.value.map(n => n.id);
};

const deselectAll = () => {
  viewState.selectedNodes = [];
  selectedNode.value = null;
};

const copySelected = () => {
  if (selectedNode.value) {
    duplicateNode(selectedNode.value);
  }
};

const deleteSelected = () => {
  if (selectedNode.value) {
    handleNodeDelete(selectedNode.value.id);
  }
};

const pasteNode = () => {
  // 粘贴功能实现
  console.log('粘贴节点');
};

const handleChartClick = (params: any) => {
  if (params.data) {
    const nodeId = params.data.id || params.data.name;
    const node = visualNodes.value.find(n => n.id === nodeId);
    if (node) {
      selectedNode.value = node;
      viewState.selectedNodes = [nodeId];
    }
  } else {
    selectedNode.value = null;
    viewState.selectedNodes = [];
  }
};

const handleChartContextMenu = (params: any) => {
  if (params.event && params.data) {
    const nodeId = params.data.id || params.data.name;
    const node = visualNodes.value.find(n => n.id === nodeId);
    if (node) {
      selectedNode.value = node;
      contextMenuPosition.value = {
        x: params.event.offsetX,
        y: params.event.offsetY
      };
      contextMenuOpen.value = true;
    }
  }
};

const handleNodeUpdate = (updatedNode: VisualNode) => {
  const index = visualNodes.value.findIndex(n => n.id === updatedNode.id);
  if (index !== -1) {
    visualNodes.value[index] = { ...updatedNode };
    recordOperation('edit_node', updatedNode);
  }
};

const handleNodeDelete = (nodeId: string) => {
  const nodeIndex = visualNodes.value.findIndex(n => n.id === nodeId);
  if (nodeIndex !== -1) {
    const deletedNode = visualNodes.value[nodeIndex];
    visualNodes.value.splice(nodeIndex, 1);

    // 删除相关连接
    visualEdges.value = visualEdges.value.filter(e =>
      e.source !== nodeId && e.target !== nodeId
    );

    recordOperation('remove_node', deletedNode);
    selectedNode.value = null;
  }
};

const handleContextMenuClick = (itemId: string) => {
  if (!selectedNode.value) return;

  switch (itemId) {
    case 'edit':
      // 编辑节点（属性面板已处理）
      break;
    case 'duplicate':
      duplicateNode(selectedNode.value);
      break;
    case 'delete':
      handleNodeDelete(selectedNode.value.id);
      break;
    case 'add_component':
      // 添加组件到构件
      break;
    case 'add_subassembly':
      // 添加子构件
      break;
  }
};

const duplicateNode = (node: VisualNode) => {
  const newNode: VisualNode = {
    ...node,
    id: `${node.id}_copy_${Date.now()}`,
    name: `${node.name} (副本)`,
    label: `${node.label} (副本)`,
    position: {
      x: node.position.x + 50,
      y: node.position.y + 50
    }
  };

  visualNodes.value.push(newNode);
  recordOperation('add_node', newNode);
};

// 视图控制功能
const fitToView = () => {
  if (chartRef.value) {
    const chart = chartRef.value.getEchartsInstance();
    chart.dispatchAction({
      type: 'restore'
    });
  }
};

const zoomIn = () => {
  viewState.zoom = Math.min(viewState.zoom * 1.2, 3);
  updateChartZoom();
};

const zoomOut = () => {
  viewState.zoom = Math.max(viewState.zoom / 1.2, 0.3);
  updateChartZoom();
};

const resetZoom = () => {
  viewState.zoom = 1;
  viewState.center = { x: 0, y: 0 };
  updateChartZoom();
};

const updateChartZoom = () => {
  if (chartRef.value) {
    const chart = chartRef.value.getEchartsInstance();
    chart.dispatchAction({
      type: 'dataZoom',
      zoom: viewState.zoom,
      center: [viewState.center.x, viewState.center.y]
    });
  }
};

// 历史管理功能
const recordOperation = (type: string, data: any) => {
  const operation: VisualOperation = {
    id: `op_${Date.now()}`,
    type: type as any,
    timestamp: new Date().toISOString(),
    data: JSON.parse(JSON.stringify(data))
  };

  // 清除当前位置之后的历史
  operationHistory.value = operationHistory.value.slice(0, historyIndex.value + 1);
  operationHistory.value.push(operation);
  historyIndex.value = operationHistory.value.length - 1;
};

const undo = () => {
  if (canUndo.value) {
    const operation = operationHistory.value[historyIndex.value];
    applyReverseOperation(operation);
    historyIndex.value--;
  }
};

const redo = () => {
  if (canRedo.value) {
    historyIndex.value++;
    const operation = operationHistory.value[historyIndex.value];
    applyOperation(operation);
  }
};

const applyOperation = (operation: VisualOperation) => {
  switch (operation.type) {
    case 'add_node':
      visualNodes.value.push(operation.data);
      break;
    case 'remove_node':
      visualNodes.value = visualNodes.value.filter(n => n.id !== operation.data.id);
      break;
    case 'edit_node':
      const index = visualNodes.value.findIndex(n => n.id === operation.data.id);
      if (index !== -1) {
        visualNodes.value[index] = operation.data;
      }
      break;
  }
};

const applyReverseOperation = (operation: VisualOperation) => {
  switch (operation.type) {
    case 'add_node':
      visualNodes.value = visualNodes.value.filter(n => n.id !== operation.data.id);
      break;
    case 'remove_node':
      visualNodes.value.push(operation.data);
      break;
    case 'edit_node':
      if (operation.reverseData) {
        const index = visualNodes.value.findIndex(n => n.id === operation.data.id);
        if (index !== -1) {
          visualNodes.value[index] = operation.reverseData;
        }
      }
      break;
  }
};

// 保存和验证功能
const validateStructure = async () => {
  if (!props.structure) return;

  try {
    // 转换可视化数据回产品结构
    const updatedStructure = convertVisualDataToStructure();
    emit('validate', updatedStructure);
  } catch (error) {
    console.error('验证失败:', error);
  }
};

const saveStructure = async () => {
  if (!props.structure) return;

  try {
    const updatedStructure = convertVisualDataToStructure();
    emit('save', updatedStructure);

    // 清空操作历史
    operationHistory.value = [];
    historyIndex.value = -1;
  } catch (error) {
    console.error('保存失败:', error);
  }
};

const convertVisualDataToStructure = (): ProductStructure => {
  if (!props.structure) throw new Error('没有原始结构数据');

  // 基于可视化数据重构产品结构
  const updatedStructure: ProductStructure = {
    ...props.structure,
    updatedAt: new Date().toISOString(),
    version: props.structure.version + 1
  };

  // 重构根构件
  const rootNode = visualNodes.value.find(n => n.type === 'product');
  if (rootNode) {
    updatedStructure.rootAssembly = reconstructAssemblyFromVisualData(rootNode);
  }

  return updatedStructure;
};

const reconstructAssemblyFromVisualData = (rootNode: VisualNode): any => {
  // 找到根构件的子节点
  const childNodes = visualNodes.value.filter(n => n.parent === rootNode.id);
  const assemblyNodes = childNodes.filter(n => n.type === 'assembly');
  const componentNodes = childNodes.filter(n => n.type === 'component');

  // 重构根构件实例
  const rootAssembly = {
    id: rootNode.data?.rootAssembly?.id || `asm_inst_${Date.now()}`,
    assemblyId: rootNode.data?.rootAssembly?.assemblyId || '',
    assemblyCode: rootNode.data?.rootAssembly?.assemblyCode || '',
    assemblyName: rootNode.data?.rootAssembly?.assemblyName || rootNode.name,
    assemblyVersion: 1,
    instanceName: rootNode.name,
    quantity: 1,
    position: rootNode.position,
    rotation: { x: 0, y: 0, z: 0 },
    parameterValues: extractParameterValues(rootNode),
    optional: false,
    alternatives: [],
    properties: rootNode.data?.properties || {}
  };

  return rootAssembly;
};

const extractParameterValues = (node: VisualNode): Record<string, any> => {
  const parameterValues: Record<string, any> = {};

  if (node.data?.parameters) {
    node.data.parameters.forEach((param: any) => {
      parameterValues[param.name] = param.defaultValue;
    });
  }

  return parameterValues;
};

// 从产品结构重建可视化数据
const rebuildVisualDataFromStructure = (structure: ProductStructure) => {
  const nodes: VisualNode[] = [];
  const edges: VisualEdge[] = [];

  // 创建产品结构根节点
  const productNode: VisualNode = {
    id: structure.id,
    type: 'product',
    name: structure.name,
    label: structure.name,
    description: structure.description,
    icon: 'Package',
    color: '#3b82f6',
    size: 80,
    position: { x: 0, y: 0 },
    status: structure.status === 'active' ? 'active' : 'inactive',
    data: structure,
    level: 0,
    expanded: true
  };
  nodes.push(productNode);

  // 处理根构件
  if (structure.rootAssembly) {
    processAssemblyForVisualization(structure.rootAssembly, structure.id, nodes, edges, 1);
  }

  // 应用自动布局
  autoLayout(nodes, layoutType.value);

  visualNodes.value = nodes;
  visualEdges.value = edges;
};

const processAssemblyForVisualization = (
  assembly: any,
  parentId: string,
  nodes: VisualNode[],
  edges: VisualEdge[],
  level: number
) => {
  // 创建构件节点
  const assemblyNode: VisualNode = {
    id: assembly.id,
    type: 'assembly',
    name: assembly.assemblyName || assembly.instanceName,
    label: assembly.assemblyName || assembly.instanceName,
    description: assembly.description,
    icon: 'Layers',
    color: '#10b981',
    size: 60,
    position: assembly.position || { x: level * 200, y: nodes.length * 100 },
    status: 'active',
    data: assembly,
    parent: parentId,
    level,
    expanded: true
  };
  nodes.push(assemblyNode);

  // 创建连接
  edges.push({
    id: `${parentId}-${assembly.id}`,
    source: parentId,
    target: assembly.id,
    type: 'hierarchy',
    style: { color: '#6b7280', width: 2 }
  });
};

const getComponentIcon = (type: string) => {
  const iconMap = {
    frame: Square,
    glass: Package,
    hardware: Wrench,
    seal: Layers,
    other: Package
  };
  return iconMap[type] || Package;
};

// ECharts配置
const chartOption = computed(() => {
  if (layoutType.value === 'tree') {
    return getTreeChartOption();
  } else {
    return getGraphChartOption();
  }
});

const getTreeChartOption = () => {
  const treeData = convertNodesToTreeData();

  return {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params: any) => {
        const node = params.data;
        return `
          <div style="padding: 8px; max-width: 200px;">
            <div style="font-weight: 600; margin-bottom: 4px;">${node.name}</div>
            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">${node.description || ''}</div>
            <div style="font-size: 11px; color: #999;">类型: ${getNodeTypeLabel(node.type)}</div>
            ${node.optional ? '<div style="font-size: 11px; color: #f59e0b;">可选组件</div>' : ''}
          </div>
        `;
      }
    },
    series: [{
      type: 'tree',
      data: treeData,
      top: '5%',
      left: '10%',
      bottom: '5%',
      right: '10%',
      symbolSize: (value: any, params: any) => {
        return params.data.size || 50;
      },
      symbol: (value: any, params: any) => {
        // 根据节点类型返回不同形状
        switch (params.data.type) {
          case 'product': return 'diamond';
          case 'assembly': return 'rect';
          case 'component': return 'circle';
          default: return 'circle';
        }
      },
      itemStyle: {
        color: (params: any) => {
          const node = params.data;
          if (viewState.selectedNodes.includes(node.id)) {
            return '#ef4444'; // 选中状态
          }
          return node.color || '#3b82f6';
        },
        borderColor: '#fff',
        borderWidth: 2,
        shadowBlur: 5,
        shadowColor: 'rgba(0, 0, 0, 0.2)'
      },
      label: {
        show: true,
        position: 'bottom',
        distance: 15,
        fontSize: 11,
        fontWeight: 'normal',
        formatter: (params: any) => {
          const node = params.data;
          let label = node.label;
          if (node.optional) {
            label += ' (可选)';
          }
          if (node.data?.quantity && node.data.quantity > 1) {
            label += ` ×${node.data.quantity}`;
          }
          return label;
        }
      },
      emphasis: {
        focus: 'descendant',
        itemStyle: {
          borderWidth: 3,
          shadowBlur: 10
        },
        label: {
          fontSize: 12,
          fontWeight: 'bold'
        }
      },
      expandAndCollapse: true,
      animationDuration: layoutConfig.animation.duration,
      animationDurationUpdate: layoutConfig.animation.duration,
      layout: 'orthogonal',
      orient: 'TB',
      roam: true,
      scaleLimit: {
        min: 0.3,
        max: 3
      }
    }]
  };
};

const getGraphChartOption = () => {
  const graphData = convertNodesToGraphData();

  return {
    tooltip: {
      formatter: (params: any) => {
        if (params.dataType === 'node') {
          const node = params.data;
          return `
            <div class="p-2">
              <div class="font-medium">${node.name}</div>
              <div class="text-sm text-gray-600">${node.description || ''}</div>
              <div class="text-xs text-gray-500 mt-1">类型: ${getNodeTypeLabel(node.type)}</div>
            </div>
          `;
        }
        return params.data.label || '';
      }
    },
    series: [{
      type: 'graph',
      layout: 'force',
      data: graphData.nodes,
      links: graphData.links,
      categories: graphData.categories,
      roam: true,
      focusNodeAdjacency: true,
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1,
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{b}',
        fontSize: 12
      },
      labelLayout: {
        hideOverlap: true
      },
      scaleLimit: {
        min: 0.4,
        max: 2
      },
      lineStyle: {
        color: 'source',
        curveness: 0.3,
        width: 2
      },
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          width: 4
        }
      },
      force: {
        repulsion: 1000,
        gravity: 0.1,
        edgeLength: [100, 200],
        layoutAnimation: true
      }
    }]
  };
};

// 数据转换函数
const convertNodesToTreeData = () => {
  if (visualNodes.value.length === 0) return [];

  const nodeMap = new Map(visualNodes.value.map(node => [node.id, { ...node, children: [] }]));
  const rootNodes: any[] = [];

  // 构建树形结构
  visualNodes.value.forEach(node => {
    const treeNode = nodeMap.get(node.id);
    if (node.parent && nodeMap.has(node.parent)) {
      const parentNode = nodeMap.get(node.parent);
      parentNode.children.push(treeNode);
    } else {
      rootNodes.push(treeNode);
    }
  });

  return rootNodes;
};

const convertNodesToGraphData = () => {
  const nodes = visualNodes.value.map(node => ({
    id: node.id,
    name: node.label,
    type: node.type,
    symbolSize: node.size || 50,
    itemStyle: {
      color: node.color || '#3b82f6'
    },
    category: getCategoryIndex(node.type),
    label: {
      show: true,
      formatter: node.label
    },
    ...node
  }));

  const links = visualEdges.value.map(edge => ({
    source: edge.source,
    target: edge.target,
    label: {
      show: !!edge.label,
      formatter: edge.label
    },
    lineStyle: edge.style || {}
  }));

  const categories = [
    { name: '产品结构', itemStyle: { color: '#3b82f6' } },
    { name: '构件', itemStyle: { color: '#10b981' } },
    { name: '组件', itemStyle: { color: '#f59e0b' } },
    { name: '参数', itemStyle: { color: '#8b5cf6' } }
  ];

  return { nodes, links, categories };
};

const getCategoryIndex = (nodeType: string) => {
  const categoryMap = {
    product: 0,
    assembly: 1,
    component: 2,
    parameter: 3
  };
  return categoryMap[nodeType] || 0;
};

const getNodeTypeLabel = (type: string) => {
  const labelMap = {
    product: '产品结构',
    assembly: '构件',
    component: '组件',
    parameter: '参数'
  };
  return labelMap[type] || type;
};

// 快捷键初始化
useAutoCADShortcuts({
  save: saveStructure,
  undo,
  redo,
  delete: deleteSelected,
  copy: copySelected,
  paste: pasteNode,
  selectAll,
  deselect: deselectAll,
  zoomIn,
  zoomOut,
  fitView: fitToView,
  resetView: resetZoom,
  newFile: handleNewStructure,
  openFile: handleOpenStructure,
  validate: validateStructure,
  help: showHelp,
  selectTool: () => handleToolChange('select'),
  moveTool: () => handleToolChange('move'),
  componentTool: () => handleToolChange('add-component'),
  assemblyTool: () => handleToolChange('add-assembly')
});

// 初始化
onMounted(async () => {
  await loadComponents();
  if (props.structure) {
    convertStructureToVisualData();
  }

  // 注册拖放区域
  if (canvasContainer.value) {
    registerDropZone({
      id: 'canvas',
      type: 'canvas',
      accepts: ['component', 'node'],
      element: canvasContainer.value
    });
  }
});

// 监听结构变化
watch(() => props.structure, (newStructure) => {
  if (newStructure) {
    convertStructureToVisualData();
  }
}, { deep: true });
</script>

<style scoped>
.product-structure-visual-editor {
  @apply h-full flex flex-col bg-gray-100;
}

.editor-toolbar {
  @apply flex-shrink-0;
}

.editor-main {
  @apply flex-1 flex overflow-hidden;
}

.component-library-panel {
  @apply flex-shrink-0 overflow-hidden flex flex-col;
}

.visual-canvas {
  @apply flex-1 relative overflow-hidden;
}

.property-panel {
  @apply flex-shrink-0 overflow-hidden flex flex-col;
}

.floating-toolbar {
  @apply bg-white rounded-lg shadow-lg p-2;
}

.component-item {
  @apply transition-all duration-200;
}

.component-item:hover {
  @apply shadow-sm border-blue-300;
}

.component-item:active {
  @apply scale-95;
}
</style>
