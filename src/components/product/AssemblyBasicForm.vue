<template>
  <div class="assembly-basic-form space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 构件名称 -->
      <div class="space-y-2">
        <Label for="name" class="text-sm font-medium">
          构件名称 <span class="text-red-500">*</span>
        </Label>
        <Input
          id="name"
          v-model="localData.name"
          placeholder="请输入构件名称"
          :class="{ 'border-red-500': hasError('name') }"
          @blur="validateField('name')"
        />
        <p v-if="hasError('name')" class="text-sm text-red-600">
          {{ getError('name') }}
        </p>
      </div>

      <!-- 构件编码 -->
      <div class="space-y-2">
        <Label for="code" class="text-sm font-medium">
          构件编码 <span class="text-red-500">*</span>
        </Label>
        <Input
          id="code"
          v-model="localData.code"
          placeholder="请输入构件编码"
          :class="{ 'border-red-500': hasError('code') }"
          @blur="validateField('code')"
        />
        <p v-if="hasError('code')" class="text-sm text-red-600">
          {{ getError('code') }}
        </p>
      </div>
    </div>

    <!-- 构件类型 -->
    <div class="space-y-2">
      <Label for="assemblyType" class="text-sm font-medium">
        构件类型 <span class="text-red-500">*</span>
      </Label>
      <Select v-model="localData.assemblyType" @update:model-value="validateField('assemblyType')">
        <SelectTrigger :class="{ 'border-red-500': hasError('assemblyType') }">
          <SelectValue placeholder="请选择构件类型" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="frame_assembly">
            <div class="flex items-center gap-2">
              <Square class="w-4 h-4" />
              <span>框架构件</span>
            </div>
          </SelectItem>
          <SelectItem value="glass_assembly">
            <div class="flex items-center gap-2">
              <Package class="w-4 h-4" />
              <span>玻璃构件</span>
            </div>
          </SelectItem>
          <SelectItem value="hardware_assembly">
            <div class="flex items-center gap-2">
              <Wrench class="w-4 h-4" />
              <span>五金构件</span>
            </div>
          </SelectItem>
          <SelectItem value="complete_assembly">
            <div class="flex items-center gap-2">
              <Layers class="w-4 h-4" />
              <span>完整构件</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
      <p v-if="hasError('assemblyType')" class="text-sm text-red-600">
        {{ getError('assemblyType') }}
      </p>
    </div>

    <!-- 描述 -->
    <div class="space-y-2">
      <Label for="description" class="text-sm font-medium">描述</Label>
      <Textarea
        id="description"
        v-model="localData.description"
        placeholder="请输入构件描述"
        rows="3"
        class="resize-none"
      />
    </div>

    <!-- 状态 -->
    <div class="space-y-2">
      <Label for="status" class="text-sm font-medium">状态</Label>
      <Select v-model="localData.status">
        <SelectTrigger>
          <SelectValue placeholder="请选择状态" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="draft">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 rounded-full bg-gray-400"></div>
              <span>草稿</span>
            </div>
          </SelectItem>
          <SelectItem value="active">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 rounded-full bg-green-500"></div>
              <span>激活</span>
            </div>
          </SelectItem>
          <SelectItem value="deprecated">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
              <span>已弃用</span>
            </div>
          </SelectItem>
          <SelectItem value="archived">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 rounded-full bg-red-500"></div>
              <span>已归档</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>

    <!-- 标签 -->
    <div class="space-y-2">
      <Label class="text-sm font-medium">标签</Label>
      <div class="flex flex-wrap gap-2 mb-2">
        <Badge
          v-for="tag in localData.tags"
          :key="tag"
          variant="secondary"
          class="flex items-center gap-1"
        >
          {{ tag }}
          <button
            @click="removeTag(tag)"
            class="ml-1 hover:bg-gray-300 rounded-full p-0.5"
          >
            <X class="w-3 h-3" />
          </button>
        </Badge>
      </div>
      <div class="flex gap-2">
        <Input
          v-model="newTag"
          placeholder="添加标签"
          class="flex-1"
          @keydown.enter="addTag"
        />
        <Button @click="addTag" variant="outline" size="sm">
          <Plus class="w-4 h-4" />
        </Button>
      </div>
    </div>

    <!-- 扩展属性 -->
    <div class="space-y-2">
      <Label class="text-sm font-medium">扩展属性</Label>
      <div class="space-y-2">
        <div
          v-for="(value, key) in localData.properties"
          :key="key"
          class="flex gap-2 items-center"
        >
          <Input
            :value="key"
            placeholder="属性名"
            class="flex-1"
            @input="updatePropertyKey(key, $event.target.value)"
          />
          <Input
            :value="value"
            placeholder="属性值"
            class="flex-1"
            @input="updatePropertyValue(key, $event.target.value)"
          />
          <Button
            @click="removeProperty(key)"
            variant="ghost"
            size="sm"
            class="text-red-600 hover:text-red-700"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
        <Button @click="addProperty" variant="outline" size="sm" class="w-full">
          <Plus class="w-4 h-4 mr-2" />
          添加属性
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  Square,
  Package,
  Wrench,
  Layers,
  Plus,
  X,
  Trash2
} from 'lucide-vue-next';

import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import type { Assembly, ValidationResult } from '@/types/product-structure';

// Props
interface Props {
  modelValue: Partial<Assembly>;
  errors?: ValidationResult[];
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: Partial<Assembly>): void;
  (e: 'validate', errors: ValidationResult[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localData = ref<Partial<Assembly>>({ ...props.modelValue });
const newTag = ref('');
const validationErrors = ref<Record<string, string>>({});

// 计算属性
const hasError = computed(() => (field: string) => {
  return !!validationErrors.value[field];
});

const getError = computed(() => (field: string) => {
  return validationErrors.value[field];
});

// 方法
const validateField = (field: string) => {
  const errors: Record<string, string> = {};

  switch (field) {
    case 'name':
      if (!localData.value.name || localData.value.name.trim() === '') {
        errors.name = '构件名称不能为空';
      } else if (localData.value.name.length > 100) {
        errors.name = '构件名称不能超过100个字符';
      }
      break;

    case 'code':
      if (!localData.value.code || localData.value.code.trim() === '') {
        errors.code = '构件编码不能为空';
      } else if (!/^[A-Z0-9_]+$/.test(localData.value.code)) {
        errors.code = '构件编码只能包含大写字母、数字和下划线';
      } else if (localData.value.code.length > 50) {
        errors.code = '构件编码不能超过50个字符';
      }
      break;

    case 'assemblyType':
      if (!localData.value.assemblyType) {
        errors.assemblyType = '请选择构件类型';
      }
      break;
  }

  // 更新验证错误
  Object.keys(errors).forEach(key => {
    validationErrors.value[key] = errors[key];
  });

  // 清除已修复的错误
  if (!errors[field]) {
    delete validationErrors.value[field];
  }

  // 发送验证结果
  const validationResults: ValidationResult[] = Object.entries(validationErrors.value).map(([key, message]) => ({
    type: 'error',
    message,
    field: key
  }));

  emit('validate', validationResults);
};

const validateAll = () => {
  validateField('name');
  validateField('code');
  validateField('assemblyType');
};

const addTag = () => {
  if (newTag.value.trim() && !localData.value.tags?.includes(newTag.value.trim())) {
    localData.value.tags = localData.value.tags || [];
    localData.value.tags.push(newTag.value.trim());
    newTag.value = '';
  }
};

const removeTag = (tag: string) => {
  if (localData.value.tags) {
    const index = localData.value.tags.indexOf(tag);
    if (index > -1) {
      localData.value.tags.splice(index, 1);
    }
  }
};

const addProperty = () => {
  localData.value.properties = localData.value.properties || {};
  localData.value.properties[`property_${Date.now()}`] = '';
};

const removeProperty = (key: string) => {
  if (localData.value.properties) {
    delete localData.value.properties[key];
  }
};

const updatePropertyKey = (oldKey: string, newKey: string) => {
  if (localData.value.properties && oldKey !== newKey) {
    const value = localData.value.properties[oldKey];
    delete localData.value.properties[oldKey];
    localData.value.properties[newKey] = value;
  }
};

const updatePropertyValue = (key: string, value: string) => {
  if (localData.value.properties) {
    localData.value.properties[key] = value;
  }
};

// 监听数据变化
watch(localData, (newValue) => {
  emit('update:modelValue', newValue);
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  localData.value = { ...newValue };
}, { deep: true });

// 初始验证
validateAll();
</script>
