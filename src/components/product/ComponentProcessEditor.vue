<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">工艺要求</h3>
        <p class="text-sm text-gray-600">定义组件的加工和装配工艺要求</p>
      </div>
      <Button @click="addProcessRequirement" class="bg-blue-600 hover:bg-blue-700">
        <Plus class="w-4 h-4 mr-2" />
        添加工艺要求
      </Button>
    </div>

    <!-- 工艺要求列表 -->
    <div v-if="localProcessRequirements.length > 0" class="space-y-4">
      <div
        v-for="(requirement, index) in localProcessRequirements"
        :key="requirement.id || index"
        class="border rounded-lg p-4 space-y-4"
      >
        <!-- 工艺要求头部 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <Badge :variant="getProcessTypeVariant(requirement.processType)">
              {{ getProcessTypeText(requirement.processType) }}
            </Badge>
            <Badge v-if="requirement.skillLevel" :variant="getSkillLevelVariant(requirement.skillLevel)">
              {{ getSkillLevelText(requirement.skillLevel) }}
            </Badge>
          </div>
          <div class="flex items-center gap-2">
            <Button @click="duplicateProcessRequirement(index)" variant="ghost" size="sm">
              <Copy class="w-4 h-4" />
            </Button>
            <Button @click="removeProcessRequirement(index)" variant="ghost" size="sm" class="text-red-600">
              <Trash2 class="w-4 h-4" />
            </Button>
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label :for="`process-name-${index}`" class="required">工艺名称</Label>
            <Input
              :id="`process-name-${index}`"
              v-model="requirement.name"
              placeholder="工艺名称"
              :class="{ 'border-red-500': getProcessError(index, 'name') }"
              @blur="validateProcessRequirement(index)"
            />
            <p v-if="getProcessError(index, 'name')" class="text-sm text-red-600">
              {{ getProcessError(index, 'name') }}
            </p>
          </div>
          
          <div class="space-y-2">
            <Label :for="`process-type-${index}`" class="required">工艺类型</Label>
            <Select v-model="requirement.processType">
              <SelectTrigger :id="`process-type-${index}`">
                <SelectValue placeholder="选择工艺类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cutting">切割</SelectItem>
                <SelectItem value="drilling">钻孔</SelectItem>
                <SelectItem value="assembly">装配</SelectItem>
                <SelectItem value="welding">焊接</SelectItem>
                <SelectItem value="coating">涂装</SelectItem>
                <SelectItem value="testing">检测</SelectItem>
                <SelectItem value="other">其他</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 工艺描述 -->
        <div class="space-y-2">
          <Label :for="`process-description-${index}`">工艺描述</Label>
          <Textarea
            :id="`process-description-${index}`"
            v-model="requirement.description"
            placeholder="详细描述工艺要求和注意事项"
            rows="3"
          />
        </div>

        <!-- 工艺参数 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="space-y-2">
            <Label :for="`process-time-${index}`">预计时间 (分钟)</Label>
            <Input
              :id="`process-time-${index}`"
              v-model.number="requirement.estimatedTime"
              type="number"
              placeholder="预计时间"
              min="0"
            />
          </div>
          
          <div class="space-y-2">
            <Label :for="`process-skill-${index}`">技能等级</Label>
            <Select v-model="requirement.skillLevel">
              <SelectTrigger :id="`process-skill-${index}`">
                <SelectValue placeholder="选择技能等级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="beginner">初级</SelectItem>
                <SelectItem value="intermediate">中级</SelectItem>
                <SelectItem value="advanced">高级</SelectItem>
                <SelectItem value="expert">专家</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div class="space-y-2">
            <Label :for="`process-equipment-${index}`">设备要求</Label>
            <Input
              :id="`process-equipment-${index}`"
              v-model="requirement.equipmentRequired"
              placeholder="所需设备"
            />
          </div>
          
          <div class="space-y-2">
            <Label :for="`process-quality-${index}`">质量标准</Label>
            <Input
              :id="`process-quality-${index}`"
              v-model="requirement.qualityStandard"
              placeholder="质量标准"
            />
          </div>
        </div>

        <!-- 工艺步骤 -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <Label>工艺步骤</Label>
            <Button @click="addProcessStep(index)" variant="outline" size="sm">
              <Plus class="w-4 h-4 mr-1" />
              添加步骤
            </Button>
          </div>
          
          <div v-if="requirement.steps && requirement.steps.length > 0" class="space-y-2">
            <div
              v-for="(step, stepIndex) in requirement.steps"
              :key="stepIndex"
              class="flex items-center gap-3 p-3 border rounded-lg"
            >
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                {{ stepIndex + 1 }}
              </div>
              <Input
                v-model="step.description"
                placeholder="步骤描述"
                class="flex-1"
              />
              <Input
                v-model.number="step.estimatedTime"
                type="number"
                placeholder="时间(分)"
                class="w-24"
                min="0"
              />
              <Button @click="removeProcessStep(index, stepIndex)" variant="ghost" size="sm">
                <Trash2 class="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <p v-else class="text-sm text-gray-500">
            暂无工艺步骤，点击"添加步骤"按钮添加详细步骤
          </p>
        </div>

        <!-- 质量检查点 -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <Label>质量检查点</Label>
            <Button @click="addQualityCheck(index)" variant="outline" size="sm">
              <Plus class="w-4 h-4 mr-1" />
              添加检查点
            </Button>
          </div>
          
          <div v-if="requirement.qualityChecks && requirement.qualityChecks.length > 0" class="space-y-2">
            <div
              v-for="(check, checkIndex) in requirement.qualityChecks"
              :key="checkIndex"
              class="flex items-center gap-3 p-3 border rounded-lg"
            >
              <div class="flex items-center space-x-2 flex-shrink-0">
                <Checkbox
                  :id="`check-critical-${index}-${checkIndex}`"
                  v-model:checked="check.isCritical"
                />
                <Label :for="`check-critical-${index}-${checkIndex}`" class="text-sm">关键</Label>
              </div>
              <Input
                v-model="check.checkPoint"
                placeholder="检查项目"
                class="flex-1"
              />
              <Input
                v-model="check.standard"
                placeholder="检查标准"
                class="flex-1"
              />
              <Button @click="removeQualityCheck(index, checkIndex)" variant="ghost" size="sm">
                <Trash2 class="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <p v-else class="text-sm text-gray-500">
            暂无质量检查点，点击"添加检查点"按钮添加质量控制要求
          </p>
        </div>

        <!-- 安全注意事项 -->
        <div class="space-y-2">
          <Label :for="`process-safety-${index}`">安全注意事项</Label>
          <Textarea
            :id="`process-safety-${index}`"
            v-model="requirement.safetyNotes"
            placeholder="安全操作注意事项和防护要求"
            rows="2"
          />
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-12">
      <Settings class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <p class="text-gray-600 mb-4">暂无工艺要求</p>
      <Button @click="addProcessRequirement" class="bg-blue-600 hover:bg-blue-700">
        <Plus class="w-4 h-4 mr-2" />
        添加第一个工艺要求
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { Plus, Copy, Trash2, Settings } from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import type { ProcessRequirement, ProcessType, SkillLevel } from '@/types/product-structure';

// Props
interface Props {
  modelValue: ProcessRequirement[];
  errors: any[];
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: ProcessRequirement[]): void;
  (e: 'validate', errors: any[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localProcessRequirements = ref<ProcessRequirement[]>([...props.modelValue]);
const processErrors = ref<Record<number, Record<string, string>>>({});

// 方法
const addProcessRequirement = () => {
  const newRequirement: ProcessRequirement = {
    id: `process-${Date.now()}`,
    name: '',
    processType: 'other',
    description: '',
    steps: [],
    qualityChecks: []
  };
  
  localProcessRequirements.value.push(newRequirement);
};

const removeProcessRequirement = (index: number) => {
  localProcessRequirements.value.splice(index, 1);
  delete processErrors.value[index];
  
  // 重新索引错误
  const newErrors: Record<number, Record<string, string>> = {};
  Object.entries(processErrors.value).forEach(([key, value]) => {
    const numKey = parseInt(key);
    if (numKey > index) {
      newErrors[numKey - 1] = value;
    } else if (numKey < index) {
      newErrors[numKey] = value;
    }
  });
  processErrors.value = newErrors;
};

const duplicateProcessRequirement = (index: number) => {
  const original = localProcessRequirements.value[index];
  const duplicated: ProcessRequirement = {
    ...original,
    id: `process-${Date.now()}`,
    name: `${original.name}_copy`
  };
  
  localProcessRequirements.value.splice(index + 1, 0, duplicated);
};

const addProcessStep = (requirementIndex: number) => {
  const requirement = localProcessRequirements.value[requirementIndex];
  if (!requirement.steps) {
    requirement.steps = [];
  }
  
  requirement.steps.push({
    stepNumber: requirement.steps.length + 1,
    description: '',
    estimatedTime: 0
  });
};

const removeProcessStep = (requirementIndex: number, stepIndex: number) => {
  const requirement = localProcessRequirements.value[requirementIndex];
  if (requirement.steps) {
    requirement.steps.splice(stepIndex, 1);
    // 重新编号
    requirement.steps.forEach((step, index) => {
      step.stepNumber = index + 1;
    });
  }
};

const addQualityCheck = (requirementIndex: number) => {
  const requirement = localProcessRequirements.value[requirementIndex];
  if (!requirement.qualityChecks) {
    requirement.qualityChecks = [];
  }
  
  requirement.qualityChecks.push({
    checkPoint: '',
    standard: '',
    isCritical: false
  });
};

const removeQualityCheck = (requirementIndex: number, checkIndex: number) => {
  const requirement = localProcessRequirements.value[requirementIndex];
  if (requirement.qualityChecks) {
    requirement.qualityChecks.splice(checkIndex, 1);
  }
};

const validateProcessRequirement = (index: number) => {
  const requirement = localProcessRequirements.value[index];
  const errors: Record<string, string> = {};
  
  if (!requirement.name?.trim()) {
    errors.name = '工艺名称不能为空';
  }
  
  processErrors.value[index] = errors;
  
  // 发出验证事件
  const allErrors = Object.values(processErrors.value).flat();
  emit('validate', allErrors);
};

const getProcessError = (index: number, field: string) => {
  return processErrors.value[index]?.[field];
};

const getProcessTypeText = (type: ProcessType) => {
  const textMap = {
    cutting: '切割',
    drilling: '钻孔',
    assembly: '装配',
    welding: '焊接',
    coating: '涂装',
    testing: '检测',
    other: '其他'
  };
  return textMap[type] || type;
};

const getProcessTypeVariant = (type: ProcessType) => {
  const variantMap = {
    cutting: 'default' as const,
    drilling: 'secondary' as const,
    assembly: 'outline' as const,
    welding: 'destructive' as const,
    coating: 'default' as const,
    testing: 'secondary' as const,
    other: 'outline' as const
  };
  return variantMap[type] || 'outline';
};

const getSkillLevelText = (level: SkillLevel) => {
  const textMap = {
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家'
  };
  return textMap[level] || level;
};

const getSkillLevelVariant = (level: SkillLevel) => {
  const variantMap = {
    beginner: 'outline' as const,
    intermediate: 'secondary' as const,
    advanced: 'default' as const,
    expert: 'destructive' as const
  };
  return variantMap[level] || 'outline';
};

// 防止循环更新的标志
const isUpdating = ref(false);

// 监听器
watch(localProcessRequirements, (newValue) => {
  if (isUpdating.value) return;
  emit('update:modelValue', newValue);
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  if (isUpdating.value) return;

  isUpdating.value = true;
  localProcessRequirements.value = [...newValue];

  nextTick(() => {
    isUpdating.value = false;
  });
}, { deep: true });
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
