<template>
  <div class="h-full flex flex-col">
    <!-- 头部工具栏 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-xl font-semibold text-gray-900">组件预览</h3>
        <p class="text-sm text-gray-600 mt-1">查看组件的完整定义和验证结果</p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="exportPreview" variant="outline" size="sm">
          <Download class="w-4 h-4 mr-1" />
          导出
        </Button>
        <Button @click="validateComponent" variant="outline" size="sm">
          <CheckCircle class="w-4 h-4 mr-1" />
          验证
        </Button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 overflow-hidden flex gap-6">

      <!-- 左侧：组件概览和验证结果 -->
      <div class="w-96 flex-shrink-0 space-y-6">
        <!-- 组件概览卡片 -->
        <Card>
          <CardHeader>
            <div class="flex items-center gap-3">
              <div
                class="w-12 h-12 rounded-lg flex items-center justify-center"
                :class="getTypeIconClass(component.componentType)"
              >
                <component :is="getTypeIcon(component.componentType)" class="w-6 h-6" />
              </div>
              <div class="flex-1">
                <CardTitle class="text-lg">{{ component.name || '未命名组件' }}</CardTitle>
                <CardDescription>{{ component.code || '未设置编码' }}</CardDescription>
              </div>
              <Badge :variant="getStatusVariant(component.status)">
                {{ getStatusText(component.status) }}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 gap-4 text-center">
              <div class="bg-blue-50 rounded-lg p-3">
                <div class="text-2xl font-bold text-blue-600">{{ component.parameters?.length || 0 }}</div>
                <div class="text-sm text-gray-600">参数</div>
              </div>
              <div class="bg-green-50 rounded-lg p-3">
                <div class="text-2xl font-bold text-green-600">{{ component.constraints?.length || 0 }}</div>
                <div class="text-sm text-gray-600">约束</div>
              </div>
              <div class="bg-orange-50 rounded-lg p-3">
                <div class="text-2xl font-bold text-orange-600">{{ component.processRequirements?.length || 0 }}</div>
                <div class="text-sm text-gray-600">工艺</div>
              </div>
              <div class="bg-purple-50 rounded-lg p-3">
                <div class="text-2xl font-bold text-purple-600">{{ component.tags?.length || 0 }}</div>
                <div class="text-sm text-gray-600">标签</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 验证结果 -->
        <Card v-if="validationResult">
          <CardHeader>
            <CardTitle class="text-base">验证结果</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- 整体状态 -->
            <Alert :variant="validationResult.isValid ? 'default' : 'destructive'">
              <CheckCircle v-if="validationResult.isValid" class="h-4 w-4" />
              <AlertTriangle v-else class="h-4 w-4" />
              <AlertTitle>
                {{ validationResult.isValid ? '验证通过' : '验证失败' }}
              </AlertTitle>
              <AlertDescription>
                {{ validationResult.isValid
                  ? '组件定义完整且符合规范'
                  : `发现 ${validationResult.parameterValidation.errors.length + validationResult.constraintValidation.errors.length} 个错误`
                }}
              </AlertDescription>
            </Alert>

            <!-- 参数验证结果 -->
            <div v-if="validationResult.parameterValidation.errors.length > 0">
              <h5 class="text-sm font-medium text-red-600 mb-2">参数验证错误</h5>
              <div class="space-y-1 max-h-32 overflow-y-auto">
                <div
                  v-for="error in validationResult.parameterValidation.errors"
                  :key="error.id"
                  class="text-sm text-red-600 bg-red-50 p-2 rounded"
                >
                  {{ error.message }}
                </div>
              </div>
            </div>

            <!-- 约束验证结果 -->
            <div v-if="validationResult.constraintValidation.errors.length > 0">
              <h5 class="text-sm font-medium text-red-600 mb-2">约束验证错误</h5>
              <div class="space-y-1 max-h-32 overflow-y-auto">
                <div
                  v-for="error in validationResult.constraintValidation.errors"
                  :key="error.id"
                  class="text-sm text-red-600 bg-red-50 p-2 rounded"
                >
                  {{ error.message }}
                </div>
              </div>
            </div>

            <!-- 修复建议 -->
            <div v-if="validationResult.fixSuggestions.length > 0">
              <h5 class="text-sm font-medium text-blue-600 mb-2">修复建议</h5>
              <div class="space-y-1 max-h-32 overflow-y-auto">
                <div
                  v-for="suggestion in validationResult.fixSuggestions"
                  :key="suggestion.id"
                  class="text-sm text-blue-600 bg-blue-50 p-2 rounded"
                >
                  {{ suggestion.description }}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧：详细信息 -->
      <div class="flex-1 overflow-hidden">
        <Tabs v-model="activeTab" class="h-full flex flex-col">
          <div class="border-b border-gray-200 pb-4 mb-6">
            <TabsList class="grid w-full grid-cols-4 max-w-2xl">
              <TabsTrigger value="basic" class="text-sm">
                <Package class="w-4 h-4 mr-2" />
                基本信息
              </TabsTrigger>
              <TabsTrigger value="parameters" class="text-sm">
                <Settings class="w-4 h-4 mr-2" />
                参数 ({{ component.parameters?.length || 0 }})
              </TabsTrigger>
              <TabsTrigger value="constraints" class="text-sm">
                <Shield class="w-4 h-4 mr-2" />
                约束 ({{ component.constraints?.length || 0 }})
              </TabsTrigger>
              <TabsTrigger value="process" class="text-sm">
                <Wrench class="w-4 h-4 mr-2" />
                工艺 ({{ component.processRequirements?.length || 0 }})
              </TabsTrigger>
            </TabsList>
          </div>

          <div class="flex-1 overflow-y-auto">

      <!-- 基本信息 -->
      <TabsContent value="basic" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label class="text-sm font-medium text-gray-600">组件名称</Label>
            <p class="text-sm text-gray-900 mt-1">{{ component.name || '未设置' }}</p>
          </div>
          <div>
            <Label class="text-sm font-medium text-gray-600">组件编码</Label>
            <p class="text-sm text-gray-900 mt-1">{{ component.code || '未设置' }}</p>
          </div>
          <div>
            <Label class="text-sm font-medium text-gray-600">组件类型</Label>
            <p class="text-sm text-gray-900 mt-1">{{ getTypeText(component.componentType) }}</p>
          </div>
          <div>
            <Label class="text-sm font-medium text-gray-600">物料分类</Label>
            <p class="text-sm text-gray-900 mt-1">{{ component.materialCategoryName || '未设置' }}</p>
          </div>
          <div>
            <Label class="text-sm font-medium text-gray-600">数量公式</Label>
            <p class="text-sm text-gray-900 mt-1 font-mono">{{ component.quantityFormula || '1' }}</p>
          </div>
          <div>
            <Label class="text-sm font-medium text-gray-600">成本公式</Label>
            <p class="text-sm text-gray-900 mt-1 font-mono">{{ component.costFormula || '未设置' }}</p>
          </div>
        </div>
        
        <div v-if="component.description">
          <Label class="text-sm font-medium text-gray-600">组件描述</Label>
          <p class="text-sm text-gray-900 mt-1">{{ component.description }}</p>
        </div>
        
        <div v-if="component.tags && component.tags.length > 0">
          <Label class="text-sm font-medium text-gray-600">标签</Label>
          <div class="flex flex-wrap gap-1 mt-1">
            <Badge
              v-for="tag in component.tags"
              :key="tag"
              variant="secondary"
            >
              {{ tag }}
            </Badge>
          </div>
        </div>
      </TabsContent>

      <!-- 参数信息 -->
      <TabsContent value="parameters" class="space-y-4">
        <div v-if="component.parameters && component.parameters.length > 0" class="space-y-3">
          <div
            v-for="parameter in component.parameters"
            :key="parameter.id"
            class="border rounded-lg p-3"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center gap-2">
                <h4 class="font-medium">{{ parameter.displayName }}</h4>
                <Badge variant="outline" class="text-xs">
                  {{ getParameterTypeText(parameter.type) }}
                </Badge>
                <Badge v-if="parameter.required" variant="destructive" class="text-xs">
                  必填
                </Badge>
              </div>
              <span class="text-sm text-gray-500">{{ parameter.name }}</span>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
              <div v-if="parameter.unit">
                <span class="text-gray-600">单位:</span>
                <span class="ml-1">{{ parameter.unit }}</span>
              </div>
              <div v-if="parameter.defaultValue !== undefined">
                <span class="text-gray-600">默认值:</span>
                <span class="ml-1">{{ parameter.defaultValue }}</span>
              </div>
              <div v-if="parameter.minValue !== undefined">
                <span class="text-gray-600">最小值:</span>
                <span class="ml-1">{{ parameter.minValue }}</span>
              </div>
              <div v-if="parameter.maxValue !== undefined">
                <span class="text-gray-600">最大值:</span>
                <span class="ml-1">{{ parameter.maxValue }}</span>
              </div>
            </div>
            
            <div v-if="parameter.description" class="text-sm text-gray-600 mt-2">
              {{ parameter.description }}
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          暂无参数定义
        </div>
      </TabsContent>

      <!-- 约束信息 -->
      <TabsContent value="constraints" class="space-y-4">
        <div v-if="component.constraints && component.constraints.length > 0" class="space-y-3">
          <div
            v-for="constraint in component.constraints"
            :key="constraint.id"
            class="border rounded-lg p-3"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center gap-2">
                <h4 class="font-medium">{{ constraint.name }}</h4>
                <Badge :variant="getSeverityVariant(constraint.severity)" class="text-xs">
                  {{ getSeverityText(constraint.severity) }}
                </Badge>
                <Badge v-if="!constraint.enabled" variant="outline" class="text-xs">
                  已禁用
                </Badge>
              </div>
            </div>
            
            <div class="mb-2">
              <Label class="text-sm font-medium text-gray-600">约束表达式</Label>
              <div class="mt-1 p-2 bg-gray-50 rounded font-mono text-sm">
                {{ constraint.expression }}
              </div>
            </div>
            
            <div class="text-sm text-gray-600">
              {{ constraint.errorMessage }}
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          暂无约束条件
        </div>
      </TabsContent>

      <!-- 工艺信息 -->
      <TabsContent value="process" class="space-y-4">
        <div v-if="component.processRequirements && component.processRequirements.length > 0" class="space-y-3">
          <div
            v-for="requirement in component.processRequirements"
            :key="requirement.id"
            class="border rounded-lg p-3"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center gap-2">
                <h4 class="font-medium">{{ requirement.name }}</h4>
                <Badge variant="secondary" class="text-xs">
                  {{ getProcessTypeText(requirement.processType) }}
                </Badge>
                <Badge v-if="requirement.skillLevel" variant="outline" class="text-xs">
                  {{ getSkillLevelText(requirement.skillLevel) }}
                </Badge>
              </div>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm mb-2">
              <div v-if="requirement.estimatedTime">
                <span class="text-gray-600">预计时间:</span>
                <span class="ml-1">{{ requirement.estimatedTime }}分钟</span>
              </div>
              <div v-if="requirement.equipmentRequired">
                <span class="text-gray-600">设备要求:</span>
                <span class="ml-1">{{ requirement.equipmentRequired }}</span>
              </div>
              <div v-if="requirement.qualityStandard">
                <span class="text-gray-600">质量标准:</span>
                <span class="ml-1">{{ requirement.qualityStandard }}</span>
              </div>
            </div>
            
            <div v-if="requirement.description" class="text-sm text-gray-600">
              {{ requirement.description }}
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          暂无工艺要求
        </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  </div>
</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  Download, CheckCircle, AlertTriangle, Square, Package, Wrench, Shield, HelpCircle, Settings
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

import { parameterConstraintService } from '@/services/parameterConstraintService';
import type { 
  Component, 
  ComponentType, 
  ParameterType, 
  SeverityLevel,
  ProcessType,
  SkillLevel
} from '@/types/product-structure';

// Props
interface Props {
  component: Partial<Component>;
  validationResult?: any;
}

const props = defineProps<Props>();

// 响应式数据
const activeTab = ref('basic');
const validationResult = ref(props.validationResult);

// 方法
const validateComponent = async () => {
  if (!props.component.parameters || !props.component.constraints) {
    return;
  }

  try {
    // 使用参数默认值进行验证
    const testValues: Record<string, any> = {};
    props.component.parameters.forEach(param => {
      if (param.defaultValue !== undefined) {
        testValues[param.name] = param.defaultValue;
      }
    });

    const result = await parameterConstraintService.validateComponent(
      props.component as Component,
      testValues
    );

    validationResult.value = result;
  } catch (error) {
    console.error('组件验证失败:', error);
  }
};

const exportPreview = () => {
  const dataStr = JSON.stringify(props.component, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${props.component.code || 'component'}_preview.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

const getTypeIcon = (type?: ComponentType) => {
  const iconMap = {
    frame: Square,
    glass: Package,
    hardware: Wrench,
    seal: Shield,
    other: HelpCircle
  };
  return iconMap[type || 'other'] || HelpCircle;
};

const getTypeIconClass = (type?: ComponentType) => {
  const classMap = {
    frame: 'bg-blue-100 text-blue-600',
    glass: 'bg-green-100 text-green-600',
    hardware: 'bg-orange-100 text-orange-600',
    seal: 'bg-purple-100 text-purple-600',
    other: 'bg-gray-100 text-gray-600'
  };
  return classMap[type || 'other'] || 'bg-gray-100 text-gray-600';
};

const getTypeText = (type?: ComponentType) => {
  const textMap = {
    frame: '框料',
    glass: '玻璃',
    hardware: '五金',
    seal: '密封',
    other: '其他'
  };
  return textMap[type || 'other'] || '其他';
};

const getStatusVariant = (status?: string) => {
  const variantMap = {
    draft: 'secondary' as const,
    active: 'default' as const,
    deprecated: 'destructive' as const,
    archived: 'outline' as const
  };
  return variantMap[status || 'draft'] || 'secondary';
};

const getStatusText = (status?: string) => {
  const textMap = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status || 'draft'] || '草稿';
};

const getParameterTypeText = (type: ParameterType) => {
  const textMap = {
    number: '数值',
    string: '字符串',
    boolean: '布尔值',
    select: '选择',
    formula: '公式'
  };
  return textMap[type] || type;
};

const getSeverityText = (severity: SeverityLevel) => {
  const textMap = {
    error: '错误',
    warning: '警告',
    info: '信息'
  };
  return textMap[severity] || severity;
};

const getSeverityVariant = (severity: SeverityLevel) => {
  const variantMap = {
    error: 'destructive' as const,
    warning: 'secondary' as const,
    info: 'outline' as const
  };
  return variantMap[severity] || 'outline';
};

const getProcessTypeText = (type: ProcessType) => {
  const textMap = {
    cutting: '切割',
    drilling: '钻孔',
    assembly: '装配',
    welding: '焊接',
    coating: '涂装',
    testing: '检测',
    other: '其他'
  };
  return textMap[type] || type;
};

const getSkillLevelText = (level: SkillLevel) => {
  const textMap = {
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家'
  };
  return textMap[level] || level;
};
</script>
