<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <component :is="getOperationIcon(operationType)" class="w-5 h-5" />
          {{ getOperationTitle(operationType) }}
        </DialogTitle>
        <DialogDescription>
          {{ getOperationDescription(operationType, selectedCount) }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- 删除操作 -->
        <div v-if="operationType === 'delete'" class="space-y-3">
          <Alert variant="destructive">
            <AlertTriangle class="h-4 w-4" />
            <AlertTitle>警告</AlertTitle>
            <AlertDescription>
              此操作不可撤销，删除的组件将无法恢复。
            </AlertDescription>
          </Alert>
          
          <div class="flex items-center space-x-2">
            <Checkbox
              id="confirm-delete"
              v-model:checked="confirmDelete"
            />
            <Label for="confirm-delete" class="text-sm">
              我确认要删除这 {{ selectedCount }} 个组件
            </Label>
          </div>
        </div>

        <!-- 更新状态操作 -->
        <div v-else-if="operationType === 'updateStatus'" class="space-y-3">
          <div>
            <Label for="status-select">选择新状态</Label>
            <Select v-model="selectedStatus">
              <SelectTrigger id="status-select" class="mt-1">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">草稿</SelectItem>
                <SelectItem value="active">活跃</SelectItem>
                <SelectItem value="deprecated">已弃用</SelectItem>
                <SelectItem value="archived">已归档</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Alert>
            <Info class="h-4 w-4" />
            <AlertTitle>提示</AlertTitle>
            <AlertDescription>
              {{ getStatusUpdateDescription(selectedStatus) }}
            </AlertDescription>
          </Alert>
        </div>

        <!-- 添加标签操作 -->
        <div v-else-if="operationType === 'addTags'" class="space-y-3">
          <div>
            <Label for="tags-input">添加标签</Label>
            <div class="mt-1">
              <div v-if="tagsToAdd.length > 0" class="flex flex-wrap gap-1 mb-2">
                <Badge
                  v-for="tag in tagsToAdd"
                  :key="tag"
                  variant="secondary"
                  class="cursor-pointer"
                  @click="removeTagToAdd(tag)"
                >
                  {{ tag }}
                  <X class="w-3 h-3 ml-1" />
                </Badge>
              </div>
              <Input
                id="tags-input"
                v-model="tagInput"
                placeholder="输入标签名称，按回车或逗号添加"
                @keydown.enter.prevent="addTag"
                @keydown="handleTagInput"
              />
            </div>
          </div>
          
          <div class="text-sm text-gray-600">
            <p>常用标签：</p>
            <div class="flex flex-wrap gap-1 mt-1">
              <Badge
                v-for="tag in commonTags"
                :key="tag"
                variant="outline"
                class="cursor-pointer hover:bg-gray-100"
                @click="addCommonTag(tag)"
              >
                {{ tag }}
              </Badge>
            </div>
          </div>
        </div>

        <!-- 移除标签操作 -->
        <div v-else-if="operationType === 'removeTags'" class="space-y-3">
          <div>
            <Label for="remove-tags-input">移除标签</Label>
            <div class="mt-1">
              <div v-if="tagsToRemove.length > 0" class="flex flex-wrap gap-1 mb-2">
                <Badge
                  v-for="tag in tagsToRemove"
                  :key="tag"
                  variant="destructive"
                  class="cursor-pointer"
                  @click="removeTagToRemove(tag)"
                >
                  {{ tag }}
                  <X class="w-3 h-3 ml-1" />
                </Badge>
              </div>
              <Input
                id="remove-tags-input"
                v-model="removeTagInput"
                placeholder="输入要移除的标签名称"
                @keydown.enter.prevent="addTagToRemove"
                @keydown="handleRemoveTagInput"
              />
            </div>
          </div>
          
          <Alert>
            <Info class="h-4 w-4" />
            <AlertTitle>提示</AlertTitle>
            <AlertDescription>
              只会从选中的组件中移除这些标签，不会影响其他组件。
            </AlertDescription>
          </Alert>
        </div>

        <!-- 导出操作 -->
        <div v-else-if="operationType === 'export'" class="space-y-3">
          <div>
            <Label for="export-format">导出格式</Label>
            <Select v-model="exportFormat">
              <SelectTrigger id="export-format" class="mt-1">
                <SelectValue placeholder="选择导出格式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="json">JSON 格式</SelectItem>
                <SelectItem value="csv">CSV 格式</SelectItem>
                <SelectItem value="excel">Excel 格式</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox
              id="include-parameters"
              v-model:checked="includeParameters"
            />
            <Label for="include-parameters" class="text-sm">
              包含参数定义
            </Label>
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox
              id="include-constraints"
              v-model:checked="includeConstraints"
            />
            <Label for="include-constraints" class="text-sm">
              包含约束条件
            </Label>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button @click="$emit('update:open', false)" variant="outline">
          取消
        </Button>
        <Button 
          @click="confirmOperation"
          :disabled="!canConfirm"
          :variant="operationType === 'delete' ? 'destructive' : 'default'"
        >
          <component :is="getOperationIcon(operationType)" class="w-4 h-4 mr-2" />
          {{ getConfirmButtonText(operationType) }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Trash2, Settings, Tag, Bookmark, Download, AlertTriangle, Info, X
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import type { BatchOperationType } from '@/services/componentService';

// Props
interface Props {
  open: boolean;
  operationType: BatchOperationType;
  selectedCount: number;
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'confirm', params: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const confirmDelete = ref(false);
const selectedStatus = ref('');
const tagInput = ref('');
const tagsToAdd = ref<string[]>([]);
const removeTagInput = ref('');
const tagsToRemove = ref<string[]>([]);
const exportFormat = ref('json');
const includeParameters = ref(true);
const includeConstraints = ref(true);

// 常用标签
const commonTags = ref([
  '标准件', '定制件', '常用', '特殊', '高精度', '经济型', '环保', '防火'
]);

// 计算属性
const canConfirm = computed(() => {
  switch (props.operationType) {
    case 'delete':
      return confirmDelete.value;
    case 'updateStatus':
      return selectedStatus.value !== '';
    case 'addTags':
      return tagsToAdd.value.length > 0;
    case 'removeTags':
      return tagsToRemove.value.length > 0;
    case 'export':
      return exportFormat.value !== '';
    default:
      return true;
  }
});

// 方法
const getOperationIcon = (type: BatchOperationType) => {
  const iconMap = {
    delete: Trash2,
    updateStatus: Settings,
    addTags: Tag,
    removeTags: Bookmark,
    export: Download
  };
  return iconMap[type] || Settings;
};

const getOperationTitle = (type: BatchOperationType) => {
  const titleMap = {
    delete: '批量删除',
    updateStatus: '批量更新状态',
    addTags: '批量添加标签',
    removeTags: '批量移除标签',
    export: '批量导出'
  };
  return titleMap[type] || '批量操作';
};

const getOperationDescription = (type: BatchOperationType, count: number) => {
  const descriptionMap = {
    delete: `即将删除 ${count} 个组件`,
    updateStatus: `即将更新 ${count} 个组件的状态`,
    addTags: `即将为 ${count} 个组件添加标签`,
    removeTags: `即将从 ${count} 个组件中移除标签`,
    export: `即将导出 ${count} 个组件的数据`
  };
  return descriptionMap[type] || `即将对 ${count} 个组件执行操作`;
};

const getConfirmButtonText = (type: BatchOperationType) => {
  const textMap = {
    delete: '确认删除',
    updateStatus: '更新状态',
    addTags: '添加标签',
    removeTags: '移除标签',
    export: '开始导出'
  };
  return textMap[type] || '确认';
};

const getStatusUpdateDescription = (status: string) => {
  const descriptionMap = {
    draft: '将组件设置为草稿状态，组件将不会在生产中使用。',
    active: '将组件设置为活跃状态，组件可以正常使用。',
    deprecated: '将组件标记为已弃用，建议使用其他替代组件。',
    archived: '将组件归档，组件将被隐藏但保留历史记录。'
  };
  return descriptionMap[status] || '更新组件状态。';
};

const addTag = () => {
  const tag = tagInput.value.trim().replace(',', '');
  if (tag && !tagsToAdd.value.includes(tag)) {
    tagsToAdd.value.push(tag);
    tagInput.value = '';
  }
};

const handleTagInput = (event: KeyboardEvent) => {
  if (event.key === ',') {
    event.preventDefault();
    addTag();
  }
};

const removeTagToAdd = (tag: string) => {
  const index = tagsToAdd.value.indexOf(tag);
  if (index > -1) {
    tagsToAdd.value.splice(index, 1);
  }
};

const addCommonTag = (tag: string) => {
  if (!tagsToAdd.value.includes(tag)) {
    tagsToAdd.value.push(tag);
  }
};

const addTagToRemove = () => {
  const tag = removeTagInput.value.trim().replace(',', '');
  if (tag && !tagsToRemove.value.includes(tag)) {
    tagsToRemove.value.push(tag);
    removeTagInput.value = '';
  }
};

const handleRemoveTagInput = (event: KeyboardEvent) => {
  if (event.key === ',') {
    event.preventDefault();
    addTagToRemove();
  }
};

const removeTagToRemove = (tag: string) => {
  const index = tagsToRemove.value.indexOf(tag);
  if (index > -1) {
    tagsToRemove.value.splice(index, 1);
  }
};

const confirmOperation = () => {
  let params: any = {};

  switch (props.operationType) {
    case 'updateStatus':
      params = { status: selectedStatus.value };
      break;
    case 'addTags':
      params = { tags: tagsToAdd.value };
      break;
    case 'removeTags':
      params = { tags: tagsToRemove.value };
      break;
    case 'export':
      params = {
        format: exportFormat.value,
        includeParameters: includeParameters.value,
        includeConstraints: includeConstraints.value
      };
      break;
  }

  emit('confirm', params);
};
</script>
