<template>
  <div class="assembly-process-editor">
    <div class="space-y-6">
      <!-- 工艺基本信息 -->
      <Card>
        <CardHeader>
          <CardTitle class="text-lg">工艺基本信息</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm">工艺名称</Label>
              <Input
                v-model="localProcess.processName"
                placeholder="工艺名称"
                @input="updateProcess"
              />
            </div>
            <div>
              <Label class="text-sm">预计总时间(分钟)</Label>
              <Input
                v-model.number="localProcess.totalEstimatedTime"
                type="number"
                min="0"
                placeholder="0"
                @input="updateProcess"
              />
            </div>
          </div>
          <div>
            <Label class="text-sm">工艺描述</Label>
            <Textarea
              v-model="localProcess.description"
              placeholder="工艺描述"
              rows="3"
              @input="updateProcess"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 工艺步骤 -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle class="text-lg">工艺步骤</CardTitle>
            <Button @click="addStep" variant="outline" size="sm">
              <Plus class="w-4 h-4 mr-2" />
              添加步骤
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="localProcess.steps.length === 0" class="text-center py-8">
            <List class="w-8 h-8 text-gray-300 mx-auto mb-2" />
            <p class="text-sm text-gray-500">暂无工艺步骤</p>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="(step, index) in localProcess.steps"
              :key="step.id"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium">步骤 {{ step.stepNumber }}</h4>
                <Button
                  @click="removeStep(index)"
                  variant="ghost"
                  size="sm"
                  class="text-red-500 hover:text-red-600"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>

              <div class="grid grid-cols-2 gap-4 mb-3">
                <div>
                  <Label class="text-sm">步骤名称</Label>
                  <Input
                    v-model="step.stepName"
                    placeholder="步骤名称"
                    @input="updateProcess"
                  />
                </div>
                <div>
                  <Label class="text-sm">预计时间(分钟)</Label>
                  <Input
                    v-model.number="step.estimatedTime"
                    type="number"
                    min="0"
                    placeholder="0"
                    @input="updateProcess"
                  />
                </div>
              </div>

              <div class="mb-3">
                <Label class="text-sm">步骤描述</Label>
                <Textarea
                  v-model="step.description"
                  placeholder="步骤描述"
                  rows="2"
                  @input="updateProcess"
                />
              </div>

              <div class="mb-3">
                <Label class="text-sm">所需资源</Label>
                <div class="flex flex-wrap gap-1 mb-2">
                  <Badge
                    v-for="resource in step.resources"
                    :key="resource"
                    variant="secondary"
                    class="flex items-center gap-1"
                  >
                    {{ resource }}
                    <button
                      @click="removeResource(step, resource)"
                      class="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                    >
                      <X class="w-3 h-3" />
                    </button>
                  </Badge>
                </div>
                <div class="flex gap-2">
                  <Input
                    v-model="newResource"
                    placeholder="添加资源"
                    class="flex-1"
                    @keydown.enter="addResource(step)"
                  />
                  <Button @click="addResource(step)" variant="outline" size="sm">
                    <Plus class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 技能要求和安全要求 -->
      <div class="grid grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle class="text-base">技能要求</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="flex flex-wrap gap-1 mb-2">
              <Badge
                v-for="skill in localProcess.requiredSkills"
                :key="skill"
                variant="secondary"
                class="flex items-center gap-1"
              >
                {{ skill }}
                <button
                  @click="removeSkill(skill)"
                  class="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                >
                  <X class="w-3 h-3" />
                </button>
              </Badge>
            </div>
            <div class="flex gap-2">
              <Input
                v-model="newSkill"
                placeholder="添加技能要求"
                class="flex-1"
                @keydown.enter="addSkill"
              />
              <Button @click="addSkill" variant="outline" size="sm">
                <Plus class="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle class="text-base">安全要求</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="flex flex-wrap gap-1 mb-2">
              <Badge
                v-for="safety in localProcess.safetyRequirements"
                :key="safety"
                variant="secondary"
                class="flex items-center gap-1"
              >
                {{ safety }}
                <button
                  @click="removeSafety(safety)"
                  class="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                >
                  <X class="w-3 h-3" />
                </button>
              </Badge>
            </div>
            <div class="flex gap-2">
              <Input
                v-model="newSafety"
                placeholder="添加安全要求"
                class="flex-1"
                @keydown.enter="addSafety"
              />
              <Button @click="addSafety" variant="outline" size="sm">
                <Plus class="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Plus, List, Trash2, X } from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import type { AssemblyProcess, QualityRequirement, ValidationResult } from '@/types/product-structure';

// Props
interface Props {
  modelValue: AssemblyProcess;
  qualityRequirements?: QualityRequirement[];
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: AssemblyProcess): void;
  (e: 'validate', errors: ValidationResult[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localProcess = ref<AssemblyProcess>({ ...props.modelValue });
const newResource = ref('');
const newSkill = ref('');
const newSafety = ref('');

// 方法
const updateProcess = () => {
  emit('update:modelValue', localProcess.value);
};

const addStep = () => {
  const newStep = {
    id: `step_${Date.now()}`,
    stepNumber: localProcess.value.steps.length + 1,
    stepName: '',
    description: '',
    estimatedTime: 0,
    resources: [],
    qualityCheckPoints: [],
    previousSteps: [],
    nextSteps: []
  };
  
  localProcess.value.steps.push(newStep);
  updateProcess();
};

const removeStep = (index: number) => {
  localProcess.value.steps.splice(index, 1);
  // 重新编号
  localProcess.value.steps.forEach((step, i) => {
    step.stepNumber = i + 1;
  });
  updateProcess();
};

const addResource = (step: any) => {
  if (newResource.value.trim() && !step.resources.includes(newResource.value.trim())) {
    step.resources.push(newResource.value.trim());
    newResource.value = '';
    updateProcess();
  }
};

const removeResource = (step: any, resource: string) => {
  const index = step.resources.indexOf(resource);
  if (index > -1) {
    step.resources.splice(index, 1);
    updateProcess();
  }
};

const addSkill = () => {
  if (newSkill.value.trim() && !localProcess.value.requiredSkills.includes(newSkill.value.trim())) {
    localProcess.value.requiredSkills.push(newSkill.value.trim());
    newSkill.value = '';
    updateProcess();
  }
};

const removeSkill = (skill: string) => {
  const index = localProcess.value.requiredSkills.indexOf(skill);
  if (index > -1) {
    localProcess.value.requiredSkills.splice(index, 1);
    updateProcess();
  }
};

const addSafety = () => {
  if (newSafety.value.trim() && !localProcess.value.safetyRequirements.includes(newSafety.value.trim())) {
    localProcess.value.safetyRequirements.push(newSafety.value.trim());
    newSafety.value = '';
    updateProcess();
  }
};

const removeSafety = (safety: string) => {
  const index = localProcess.value.safetyRequirements.indexOf(safety);
  if (index > -1) {
    localProcess.value.safetyRequirements.splice(index, 1);
    updateProcess();
  }
};

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  localProcess.value = { ...newValue };
}, { deep: true });
</script>
