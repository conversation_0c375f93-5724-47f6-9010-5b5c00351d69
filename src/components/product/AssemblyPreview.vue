<template>
  <div class="assembly-preview">
    <div class="space-y-6">
      <!-- 构件概览 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <component :is="getTypeIcon(assembly.assemblyType)" class="w-5 h-5" />
            {{ assembly.name || '未命名构件' }}
          </CardTitle>
          <CardDescription>
            {{ assembly.description || '暂无描述' }}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">
                {{ (assembly.componentInstances || []).length }}
              </div>
              <div class="text-sm text-gray-500">组件实例</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">
                {{ (assembly.subAssemblies || []).length }}
              </div>
              <div class="text-sm text-gray-500">子构件</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-600">
                {{ assembly.assemblyProcess?.totalEstimatedTime || 0 }}
              </div>
              <div class="text-sm text-gray-500">装配时间(分钟)</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">
                {{ (assembly.qualityRequirements || []).length }}
              </div>
              <div class="text-sm text-gray-500">质检项目</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 验证结果 -->
      <Card v-if="(validationResults || []).length > 0">
        <CardHeader>
          <CardTitle class="text-lg flex items-center gap-2">
            <AlertCircle class="w-5 h-5 text-red-500" />
            验证结果
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <div
              v-for="result in validationResults"
              :key="result.field"
              class="flex items-start gap-2 p-2 rounded"
              :class="{
                'bg-red-50 text-red-800': result.type === 'error',
                'bg-yellow-50 text-yellow-800': result.type === 'warning',
                'bg-blue-50 text-blue-800': result.type === 'info'
              }"
            >
              <component
                :is="getValidationIcon(result.type)"
                class="w-4 h-4 flex-shrink-0 mt-0.5"
              />
              <div>
                <p class="text-sm font-medium">{{ result.message }}</p>
                <p v-if="result.field" class="text-xs opacity-75">字段: {{ result.field }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 组件实例列表 -->
      <Card v-if="assembly.componentInstances && assembly.componentInstances.length > 0">
        <CardHeader>
          <CardTitle class="text-lg">组件实例</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="instance in assembly.componentInstances"
              :key="instance.id"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                  <Package class="w-4 h-4 text-gray-600" />
                </div>
                <div>
                  <p class="font-medium">{{ instance.instanceName }}</p>
                  <p class="text-sm text-gray-500">{{ instance.componentName }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium">×{{ instance.quantity }}</p>
                <p class="text-xs text-gray-500">{{ instance.optional ? '可选' : '必需' }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 装配工艺预览 -->
      <Card v-if="assembly.assemblyProcess && (assembly.assemblyProcess.steps || []).length > 0">
        <CardHeader>
          <CardTitle class="text-lg">装配工艺</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="step in (assembly.assemblyProcess.steps || [])"
              :key="step.id"
              class="flex items-start gap-3 p-3 border rounded-lg"
            >
              <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span class="text-xs font-medium text-blue-600">{{ step.stepNumber }}</span>
              </div>
              <div class="flex-1">
                <p class="font-medium">{{ step.stepName }}</p>
                <p class="text-sm text-gray-600">{{ step.description }}</p>
                <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                  <span>预计时间: {{ step.estimatedTime }}分钟</span>
                  <span>资源: {{ (step.resources || []).length }}项</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 构件参数 -->
      <Card v-if="assembly.assemblyParameters && assembly.assemblyParameters.length > 0">
        <CardHeader>
          <CardTitle class="text-lg">构件参数</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="param in assembly.assemblyParameters"
              :key="param.id"
              class="p-3 border rounded-lg"
            >
              <div class="flex items-center justify-between mb-1">
                <p class="font-medium">{{ param.displayName }}</p>
                <Badge variant="outline" class="text-xs">{{ param.type }}</Badge>
              </div>
              <p class="text-sm text-gray-600 mb-2">{{ param.description }}</p>
              <div class="text-xs text-gray-500">
                默认值: {{ param.defaultValue }}{{ param.unit }}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 质量要求 -->
      <Card v-if="assembly.qualityRequirements && assembly.qualityRequirements.length > 0">
        <CardHeader>
          <CardTitle class="text-lg">质量要求</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="requirement in assembly.qualityRequirements"
              :key="requirement.id"
              class="p-3 border rounded-lg"
            >
              <div class="flex items-center justify-between mb-2">
                <p class="font-medium">{{ requirement.requirementName }}</p>
                <Badge variant="outline">{{ requirement.standard }}</Badge>
              </div>
              <p class="text-sm text-gray-600 mb-2">{{ requirement.description }}</p>
              <div class="grid grid-cols-2 gap-4 text-xs text-gray-500">
                <div>公差: {{ requirement.tolerance }}</div>
                <div>检测方法: {{ requirement.testMethod }}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Package,
  AlertCircle,
  AlertTriangle,
  Info,
  Square,
  Wrench,
  Layers,
  HelpCircle
} from 'lucide-vue-next';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import type { Assembly, AssemblyType, ValidationResult } from '@/types/product-structure';

// Props
interface Props {
  assembly: Partial<Assembly>;
  validationResults?: ValidationResult[];
}

const props = defineProps<Props>();

// 方法
const getTypeIcon = (type?: AssemblyType) => {
  if (!type) return HelpCircle;
  
  const iconMap = {
    frame_assembly: Square,
    glass_assembly: Package,
    hardware_assembly: Wrench,
    complete_assembly: Layers
  };
  return iconMap[type] || HelpCircle;
};

const getValidationIcon = (type: string) => {
  const iconMap = {
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info
  };
  return iconMap[type] || Info;
};
</script>
