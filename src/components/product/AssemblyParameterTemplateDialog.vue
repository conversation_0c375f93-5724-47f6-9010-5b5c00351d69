<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[800px] max-w-none">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <LayoutTemplate class="w-5 h-5" />
          选择参数模板
        </DialogTitle>
        <DialogDescription>
          基于构件类型选择合适的参数模板，快速建立参数框架
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- 模板选择 -->
        <div v-if="availableTemplates.length > 0">
          <Label class="text-sm font-medium mb-3 block">推荐模板</Label>
          <div class="grid grid-cols-1 gap-3">
            <div
              v-for="template in availableTemplates"
              :key="template.assemblyType"
              :class="[
                'border rounded-lg p-4 cursor-pointer transition-all',
                selectedTemplate?.assemblyType === template.assemblyType
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              ]"
              @click="selectTemplate(template)"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h4 class="font-medium text-sm">{{ template.name }}</h4>
                  <p class="text-xs text-gray-600 mt-1">{{ template.description }}</p>
                  
                  <!-- 参数分组预览 -->
                  <div class="flex flex-wrap gap-1 mt-2">
                    <Badge
                      v-for="group in template.parameterGroups"
                      :key="group.category"
                      variant="outline"
                      class="text-xs"
                    >
                      {{ group.displayName }}({{ group.parameters.length }})
                    </Badge>
                  </div>
                </div>
                
                <div class="flex items-center gap-2 ml-4">
                  <Badge variant="secondary" class="text-xs">
                    {{ getTotalParameterCount(template) }} 个参数
                  </Badge>
                  <CheckCircle
                    v-if="selectedTemplate?.assemblyType === template.assemblyType"
                    class="w-4 h-4 text-blue-600"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 参数预览 -->
        <div v-if="selectedTemplate" class="border-t pt-6">
          <Label class="text-sm font-medium mb-3 block">参数预览</Label>
          <div class="space-y-4 max-h-60 overflow-y-auto">
            <div
              v-for="group in selectedTemplate.parameterGroups"
              :key="group.category"
              class="border rounded-lg p-3"
            >
              <div class="flex items-center justify-between mb-2">
                <h5 class="font-medium text-sm">{{ group.displayName }}</h5>
                <Badge variant="outline" class="text-xs">
                  {{ group.parameters.length }} 个参数
                </Badge>
              </div>
              <p v-if="group.description" class="text-xs text-gray-600 mb-3">
                {{ group.description }}
              </p>
              
              <div class="grid grid-cols-2 gap-2">
                <div
                  v-for="param in group.parameters"
                  :key="param.id"
                  class="flex items-center justify-between p-2 bg-gray-50 rounded text-xs"
                >
                  <div class="flex items-center gap-2">
                    <span class="font-medium">{{ param.displayName }}</span>
                    <Badge v-if="param.required" variant="destructive" class="text-xs px-1">
                      必需
                    </Badge>
                  </div>
                  <span class="text-gray-500">{{ getParameterTypeText(param.type) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 自定义选项 -->
        <div class="border-t pt-6">
          <div class="flex items-center justify-between">
            <div>
              <Label class="text-sm font-medium">自定义参数</Label>
              <p class="text-xs text-gray-600 mt-1">
                选择模板后，您仍可以添加、修改或删除参数
              </p>
            </div>
            <div class="flex items-center gap-2">
              <Checkbox
                id="allow-custom"
                v-model="allowCustomization"
              />
              <Label for="allow-custom" class="text-sm">允许自定义修改</Label>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button @click="$emit('update:open', false)" variant="outline">
          取消
        </Button>
        <Button
          @click="applyTemplate"
          :disabled="!selectedTemplate"
          class="bg-blue-600 hover:bg-blue-700"
        >
          <LayoutTemplate class="w-4 h-4 mr-2" />
          应用模板
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { LayoutTemplate, CheckCircle } from 'lucide-vue-next';

import { assemblyTemplateService, type AssemblyParameterTemplate } from '@/services/assemblyTemplateService';
import type { AssemblyType, ComponentParameter } from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  assemblyType?: AssemblyType;
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'apply', parameters: ComponentParameter[], allowCustomization: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const selectedTemplate = ref<AssemblyParameterTemplate | null>(null);
const allowCustomization = ref(true);

// 计算属性
const availableTemplates = computed(() => {
  if (props.assemblyType) {
    const template = assemblyTemplateService.getTemplate(props.assemblyType);
    return template ? [template] : [];
  }
  return assemblyTemplateService.getAllTemplates();
});

// 方法
const selectTemplate = (template: AssemblyParameterTemplate) => {
  selectedTemplate.value = template;
};

const getTotalParameterCount = (template: AssemblyParameterTemplate): number => {
  return template.parameterGroups.reduce((total, group) => total + group.parameters.length, 0);
};

const getParameterTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'number': '数值',
    'string': '文本',
    'boolean': '布尔',
    'select': '选择',
    'formula': '公式'
  };
  return typeMap[type] || type;
};

const applyTemplate = () => {
  if (!selectedTemplate.value) return;

  // 收集所有参数
  const parameters: ComponentParameter[] = [];
  selectedTemplate.value.parameterGroups.forEach(group => {
    parameters.push(...group.parameters);
  });

  emit('apply', parameters, allowCustomization.value);
  emit('update:open', false);
};

// 监听props变化
watch(() => props.open, (open) => {
  if (open) {
    // 如果有指定的构件类型，自动选择对应模板
    if (props.assemblyType) {
      const template = assemblyTemplateService.getTemplate(props.assemblyType);
      if (template) {
        selectedTemplate.value = template;
      }
    } else {
      selectedTemplate.value = null;
    }
  }
});
</script>
