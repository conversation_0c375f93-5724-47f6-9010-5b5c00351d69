<template>
  <div class="assembly-parameters-editor">
    <div class="flex items-center justify-between mb-4">
      <h3 class="font-medium">构件参数</h3>
      <Button @click="addParameter" variant="outline" size="sm">
        <Plus class="w-4 h-4 mr-2" />
        添加参数
      </Button>
    </div>

    <div v-if="modelValue.length === 0" class="text-center py-8">
      <Settings class="w-8 h-8 text-gray-300 mx-auto mb-2" />
      <p class="text-sm text-gray-500">暂无构件参数</p>
    </div>

    <div v-else class="space-y-4">
      <div
        v-for="(param, index) in modelValue"
        :key="param.id"
        class="border rounded-lg p-4"
      >
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium">参数 {{ index + 1 }}</h4>
          <Button
            @click="removeParameter(index)"
            variant="ghost"
            size="sm"
            class="text-red-500 hover:text-red-600"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <Label class="text-sm">参数名称</Label>
            <Input
              v-model="param.name"
              placeholder="参数名称"
              class="text-sm"
              @input="updateParameters"
            />
          </div>
          <div>
            <Label class="text-sm">显示名称</Label>
            <Input
              v-model="param.displayName"
              placeholder="显示名称"
              class="text-sm"
              @input="updateParameters"
            />
          </div>
          <div>
            <Label class="text-sm">参数类型</Label>
            <Select v-model="param.type" @update:model-value="updateParameters">
              <SelectTrigger class="text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="number">数值</SelectItem>
                <SelectItem value="string">字符串</SelectItem>
                <SelectItem value="boolean">布尔值</SelectItem>
                <SelectItem value="select">选择</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label class="text-sm">单位</Label>
            <Input
              v-model="param.unit"
              placeholder="单位"
              class="text-sm"
              @input="updateParameters"
            />
          </div>
        </div>

        <div class="mt-3">
          <Label class="text-sm">描述</Label>
          <Textarea
            v-model="param.description"
            placeholder="参数描述"
            class="text-sm"
            rows="2"
            @input="updateParameters"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus, Settings, Trash2 } from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import type { ComponentParameter, ComponentConstraint, ValidationResult } from '@/types/product-structure';

// Props
interface Props {
  modelValue: ComponentParameter[];
  constraints?: ComponentConstraint[];
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: ComponentParameter[]): void;
  (e: 'validate', errors: ValidationResult[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 方法
const addParameter = () => {
  const newParam: ComponentParameter = {
    id: `param_${Date.now()}`,
    name: '',
    displayName: '',
    type: 'string',
    defaultValue: '',
    required: false,
    description: '',
    category: 'basic'
  };
  
  emit('update:modelValue', [...props.modelValue, newParam]);
};

const removeParameter = (index: number) => {
  const filtered = props.modelValue.filter((_, i) => i !== index);
  emit('update:modelValue', filtered);
};

const updateParameters = () => {
  emit('update:modelValue', [...props.modelValue]);
};
</script>
