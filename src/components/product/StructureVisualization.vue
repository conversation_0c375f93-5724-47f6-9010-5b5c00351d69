<template>
  <div class="structure-visualization w-full h-full relative">
    <div
      class="visualization-container w-full h-full"
      :style="{
        transform: `scale(${canvasState?.zoom || 1}) translate(${canvasState?.panX || 0}px, ${canvasState?.panY || 0}px)`,
        transformOrigin: 'center center'
      }"
    >
      <!-- 树形视图 -->
      <div v-if="viewMode === 'tree'" class="tree-visualization">
        <svg
          class="w-full h-full"
          :viewBox="`0 0 ${svgWidth} ${svgHeight}`"
          @click="handleCanvasClick"
        >
          <!-- 连接线 -->
          <g class="connections">
            <line
              v-for="connection in connections"
              :key="connection.id"
              :x1="connection.x1"
              :y1="connection.y1"
              :x2="connection.x2"
              :y2="connection.y2"
              stroke="#94a3b8"
              stroke-width="2"
              :stroke-dasharray="connection.dashed ? '5,5' : 'none'"
            />
          </g>
          
          <!-- 节点 -->
          <g class="nodes">
            <g
              v-for="node in visualNodes"
              :key="node.id"
              :transform="`translate(${node.x}, ${node.y})`"
              @click="handleNodeClick(node.id, $event)"
              @dblclick="handleNodeDoubleClick(node.id)"
              class="cursor-pointer"
            >
              <!-- 节点背景 -->
              <rect
                :width="node.width"
                :height="node.height"
                :rx="4"
                :fill="getNodeFill(node)"
                :stroke="getNodeStroke(node)"
                :stroke-width="selectedNodes?.has(node.id) ? 3 : 1"
                class="transition-all duration-200"
              />
              
              <!-- 节点图标 -->
              <foreignObject
                :x="8"
                :y="8"
                width="16"
                height="16"
              >
                <component
                  :is="getNodeIcon(node.type)"
                  :class="[
                    'w-4 h-4',
                    getNodeIconColor(node.type)
                  ]"
                />
              </foreignObject>
              
              <!-- 节点文本 -->
              <text
                :x="32"
                :y="16"
                class="text-sm font-medium fill-gray-800"
                dominant-baseline="middle"
              >
                {{ node.name }}
              </text>
              
              <!-- 节点编码 -->
              <text
                v-if="node.code"
                :x="32"
                :y="32"
                class="text-xs fill-gray-600"
                dominant-baseline="middle"
              >
                {{ node.code }}
              </text>
              
              <!-- 数量标识 -->
              <text
                v-if="node.quantity && node.quantity > 1"
                :x="node.width - 8"
                :y="16"
                class="text-xs fill-blue-600 font-medium"
                text-anchor="end"
                dominant-baseline="middle"
              >
                ×{{ node.quantity }}
              </text>
            </g>
          </g>
        </svg>
      </div>

      <!-- 关系图视图 -->
      <div v-else-if="viewMode === 'graph'" class="graph-visualization">
        <div class="w-full h-full flex items-center justify-center">
          <div class="text-center text-gray-500">
            <Network class="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <p>关系图视图开发中...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 网格背景 -->
    <div class="absolute inset-0 pointer-events-none opacity-20">
      <svg class="w-full h-full">
        <defs>
          <pattern
            id="grid"
            width="20"
            height="20"
            patternUnits="userSpaceOnUse"
          >
            <path
              d="M 20 0 L 0 0 0 20"
              fill="none"
              stroke="#e2e8f0"
              stroke-width="1"
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import {
  Package,
  Layers,
  Square,
  Settings,
  Network,
} from 'lucide-vue-next';
import type { ProductStructure } from '@/types/product-structure';

// Props
interface Props {
  structure: ProductStructure | null;
  viewMode: 'tree' | 'graph';
  canvasState?: any;
  selectedNodes?: Set<string>;
  nodes?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  canvasState: () => ({ zoom: 1, panX: 0, panY: 0 }),
  selectedNodes: () => new Set(),
  nodes: () => []
});

// Emits
const emit = defineEmits<{
  'node-click': [nodeId: string];
  'node-double-click': [nodeId: string];
}>();

// 响应式数据
const svgWidth = ref(1200);
const svgHeight = ref(800);

// 计算属性
const visualNodes = computed(() => {
  // 如果传入了nodes数组，直接使用
  if (props.nodes && props.nodes.length > 0) {
    return props.nodes;
  }

  // 否则从structure生成节点
  if (!props.structure) return [];

  const nodes = [];
  let yOffset = 50;

  // 根节点（产品）
  const rootNode = {
    id: props.structure.id,
    name: props.structure.name,
    code: props.structure.code,
    type: 'product',
    x: 50,
    y: yOffset,
    width: 200,
    height: 50,
    level: 0
  };
  nodes.push(rootNode);
  yOffset += 100;
  
  // 根构件
  if (props.structure.rootAssembly) {
    const assembly = props.structure.rootAssembly as any; // 临时类型断言
    const assemblyNode = {
      id: assembly.id,
      name: assembly.assemblyName || assembly.instanceName || '构件',
      code: assembly.assemblyCode || assembly.assemblyId || '',
      type: 'assembly',
      x: 100,
      y: yOffset,
      width: 180,
      height: 45,
      level: 1,
      parentId: props.structure.id
    };
    nodes.push(assemblyNode);
    yOffset += 80;

    // 组件实例
    if (assembly.componentInstances) {
      assembly.componentInstances.forEach((component: any, index: number) => {
        const componentNode = {
          id: component.id,
          name: component.componentName || component.instanceName || '组件',
          code: component.componentCode || component.componentId || '',
          type: 'component',
          x: 150,
          y: yOffset + (index * 70),
          width: 160,
          height: 40,
          level: 2,
          parentId: assembly.id,
          quantity: component.quantity || 1
        };
        nodes.push(componentNode);
      });
    }
  }
  
  return nodes;
});

const connections = computed(() => {
  const connections = [];
  
  visualNodes.value.forEach(node => {
    if (node.parentId) {
      const parent = visualNodes.value.find(n => n.id === node.parentId);
      if (parent) {
        connections.push({
          id: `${parent.id}-${node.id}`,
          x1: parent.x + parent.width / 2,
          y1: parent.y + parent.height,
          x2: node.x + node.width / 2,
          y2: node.y,
          dashed: node.type === 'component' && node.quantity === 0
        });
      }
    }
  });
  
  return connections;
});

// 方法
const handleCanvasClick = () => {
  // 点击空白区域取消选择
  emit('node-click', '');
};

const handleNodeClick = (nodeId: string, event: MouseEvent) => {
  event.stopPropagation();
  emit('node-click', nodeId);
};

const handleNodeDoubleClick = (nodeId: string) => {
  emit('node-double-click', nodeId);
};

const getNodeIcon = (type: string) => {
  const iconMap = {
    product: Package,
    assembly: Layers,
    component: Square,
    parameter: Settings
  };
  return iconMap[type] || Package;
};

const getNodeIconColor = (type: string) => {
  const colorMap = {
    product: 'text-blue-600',
    assembly: 'text-green-600',
    component: 'text-orange-600',
    parameter: 'text-purple-600'
  };
  return colorMap[type] || 'text-gray-600';
};

const getNodeFill = (node: any) => {
  if (props.selectedNodes?.has(node.id)) {
    return '#dbeafe'; // blue-100
  }

  const fillMap = {
    product: '#eff6ff', // blue-50
    assembly: '#f0fdf4', // green-50
    component: '#fff7ed', // orange-50
    parameter: '#faf5ff'  // purple-50
  };
  return fillMap[node.type] || '#f8fafc';
};

const getNodeStroke = (node: any) => {
  if (props.selectedNodes?.has(node.id)) {
    return '#3b82f6'; // blue-500
  }

  const strokeMap = {
    product: '#60a5fa', // blue-400
    assembly: '#4ade80', // green-400
    component: '#fb923c', // orange-400
    parameter: '#a855f7'  // purple-400
  };
  return strokeMap[node.type] || '#94a3b8';
};
</script>

<style scoped>
.structure-visualization {
  background: #f8fafc;
}

.visualization-container {
  transition: transform 0.2s ease-out;
}

.tree-visualization svg {
  overflow: visible;
}

.nodes g:hover rect {
  filter: brightness(0.95);
}
</style>
