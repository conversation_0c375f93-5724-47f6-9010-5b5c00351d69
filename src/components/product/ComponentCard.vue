<template>
  <Card 
    class="relative cursor-pointer transition-all duration-200 hover:shadow-md"
    :class="{ 
      'ring-2 ring-blue-500 ring-offset-2': selected,
      'hover:shadow-lg': !selected 
    }"
    @click="$emit('select', component.id)"
  >
    <!-- 选择指示器 -->
    <div v-if="selected" class="absolute top-2 left-2 z-10">
      <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
        <Check class="w-3 h-3 text-white" />
      </div>
    </div>

    <!-- 状态指示器 -->
    <div class="absolute top-2 right-2">
      <Badge :variant="getStatusVariant(component.status)" class="text-xs">
        {{ getStatusText(component.status) }}
      </Badge>
    </div>

    <CardContent class="p-4">
      <!-- 组件图标和基本信息 -->
      <div class="flex items-start gap-3 mb-3">
        <div 
          class="w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0"
          :class="getTypeIconClass(component.componentType)"
        >
          <component :is="getTypeIcon(component.componentType)" class="w-6 h-6" />
        </div>
        
        <div class="flex-1 min-w-0">
          <h3 class="font-medium text-gray-900 truncate mb-1">
            {{ component.name }}
          </h3>
          <p class="text-sm text-gray-500 truncate">
            {{ component.code }}
          </p>
          <p class="text-xs text-gray-400">
            {{ getTypeText(component.componentType) }}
          </p>
        </div>
      </div>

      <!-- 描述 -->
      <div v-if="component.description" class="mb-3">
        <p class="text-sm text-gray-600 line-clamp-2">
          {{ component.description }}
        </p>
      </div>

      <!-- 物料分类 -->
      <div v-if="component.materialCategoryName" class="mb-3">
        <div class="flex items-center gap-2">
          <Package class="w-4 h-4 text-gray-400" />
          <span class="text-sm text-gray-600 truncate">
            {{ component.materialCategoryName }}
          </span>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="grid grid-cols-2 gap-4 mb-3 text-center">
        <div class="bg-gray-50 rounded-lg p-2">
          <div class="text-lg font-semibold text-gray-900">
            {{ component.parameters.length }}
          </div>
          <div class="text-xs text-gray-500">参数</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-2">
          <div class="text-lg font-semibold text-gray-900">
            {{ component.constraints.length }}
          </div>
          <div class="text-xs text-gray-500">约束</div>
        </div>
      </div>

      <!-- 标签 -->
      <div v-if="component.tags.length > 0" class="mb-3">
        <div class="flex flex-wrap gap-1">
          <Badge
            v-for="tag in component.tags.slice(0, 2)"
            :key="tag"
            variant="secondary"
            class="text-xs"
          >
            {{ tag }}
          </Badge>
          <Badge
            v-if="component.tags.length > 2"
            variant="secondary"
            class="text-xs"
          >
            +{{ component.tags.length - 2 }}
          </Badge>
        </div>
      </div>

      <!-- 特性标识 -->
      <div class="flex items-center gap-2 mb-3">
        <Badge v-if="component.reusable" variant="outline" class="text-xs">
          <Recycle class="w-3 h-3 mr-1" />
          可重用
        </Badge>
        <Badge v-if="component.quantityFormula !== '1'" variant="outline" class="text-xs">
          <Calculator class="w-3 h-3 mr-1" />
          公式
        </Badge>
        <Badge v-if="component.processRequirements.length > 0" variant="outline" class="text-xs">
          <Settings class="w-3 h-3 mr-1" />
          工艺
        </Badge>
      </div>

      <!-- 时间信息 -->
      <div class="text-xs text-gray-400 mb-3">
        <div class="flex items-center gap-1">
          <Clock class="w-3 h-3" />
          <span>更新于 {{ formatDate(component.updatedAt) }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center gap-2">
        <Button
          @click.stop="$emit('edit', component)"
          variant="outline"
          size="sm"
          class="flex-1"
        >
          <Edit class="w-4 h-4 mr-1" />
          编辑
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button @click.stop variant="outline" size="sm">
              <MoreHorizontal class="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="$emit('duplicate', component)">
              <Copy class="w-4 h-4 mr-2" />
              复制
            </DropdownMenuItem>
            <DropdownMenuItem @click="viewDetails">
              <Eye class="w-4 h-4 mr-2" />
              查看详情
            </DropdownMenuItem>
            <DropdownMenuItem @click="exportComponent">
              <Download class="w-4 h-4 mr-2" />
              导出
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              @click="$emit('delete', component)"
              class="text-red-600"
            >
              <Trash2 class="w-4 h-4 mr-2" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </CardContent>

    <!-- 详情对话框 -->
    <ComponentDetailsDialog
      v-model:open="detailsOpen"
      :component="component"
    />
  </Card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { 
  Check, Package, Recycle, Calculator, Settings, Clock, Edit, MoreHorizontal,
  Copy, Eye, Download, Trash2, Square, Wrench, Shield, HelpCircle
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import ComponentDetailsDialog from './ComponentDetailsDialog.vue';
import type { Component, ComponentType } from '@/types/product-structure';

// Props
interface Props {
  component: Component;
  selected: boolean;
}

// Emits
interface Emits {
  (e: 'select', componentId: string): void;
  (e: 'edit', component: Component): void;
  (e: 'duplicate', component: Component): void;
  (e: 'delete', component: Component): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const detailsOpen = ref(false);

// 方法
const getTypeIcon = (type: ComponentType) => {
  const iconMap = {
    frame: Square,
    glass: Package,
    hardware: Wrench,
    seal: Shield,
    other: HelpCircle
  };
  return iconMap[type] || HelpCircle;
};

const getTypeIconClass = (type: ComponentType) => {
  const classMap = {
    frame: 'bg-blue-100 text-blue-600',
    glass: 'bg-green-100 text-green-600',
    hardware: 'bg-orange-100 text-orange-600',
    seal: 'bg-purple-100 text-purple-600',
    other: 'bg-gray-100 text-gray-600'
  };
  return classMap[type] || 'bg-gray-100 text-gray-600';
};

const getTypeText = (type: ComponentType) => {
  const textMap = {
    frame: '框料',
    glass: '玻璃',
    hardware: '五金',
    seal: '密封',
    other: '其他'
  };
  return textMap[type] || '其他';
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary' as const,
    active: 'default' as const,
    deprecated: 'destructive' as const,
    archived: 'outline' as const
  };
  return variantMap[status] || 'secondary';
};

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return '今天';
  } else if (diffDays <= 7) {
    return `${diffDays}天前`;
  } else if (diffDays <= 30) {
    return `${Math.ceil(diffDays / 7)}周前`;
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
};

const viewDetails = () => {
  detailsOpen.value = true;
};

const exportComponent = () => {
  // 导出组件数据
  const dataStr = JSON.stringify(props.component, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${props.component.code}_${props.component.name}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
