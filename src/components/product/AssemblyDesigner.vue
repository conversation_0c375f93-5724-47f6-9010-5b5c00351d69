<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[95vw] h-[95vh] max-w-none max-h-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <PenTool class="w-5 h-5" />
          构件设计器 - {{ assembly?.name || '新构件' }}
        </DialogTitle>
        <DialogDescription>
          可视化设计构件结构，配置组件实例和装配关系
        </DialogDescription>
      </DialogHeader>

      <div class="flex-1 overflow-hidden flex">
        <!-- 左侧工具栏 -->
        <div class="w-64 border-r bg-gray-50 flex flex-col">
          <!-- 组件库 -->
          <div class="p-4 border-b">
            <h3 class="font-medium mb-3">组件库</h3>
            <div class="relative mb-3">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                v-model="componentSearchQuery"
                placeholder="搜索组件..."
                class="pl-10 text-sm"
              />
            </div>
            <div class="space-y-2 max-h-48 overflow-y-auto">
              <div
                v-for="component in filteredComponents"
                :key="component.id"
                class="p-2 border rounded cursor-pointer hover:bg-white transition-colors"
                draggable="true"
                @dragstart="handleDragStart($event, component)"
              >
                <div class="flex items-center gap-2">
                  <component :is="getComponentIcon(component.componentType)" class="w-4 h-4 text-gray-600" />
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium truncate">{{ component.name }}</p>
                    <p class="text-xs text-gray-500 truncate">{{ component.code }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 构件实例列表 -->
          <div class="flex-1 p-4 overflow-y-auto">
            <div class="flex items-center justify-between mb-3">
              <h3 class="font-medium">组件实例</h3>
              <Badge variant="secondary" class="text-xs">
                {{ componentInstances.length }}
              </Badge>
            </div>
            <div class="space-y-2">
              <div
                v-for="instance in componentInstances"
                :key="instance.id"
                class="p-2 border rounded cursor-pointer transition-colors"
                :class="{
                  'bg-blue-50 border-blue-200': selectedInstance?.id === instance.id,
                  'hover:bg-gray-50': selectedInstance?.id !== instance.id
                }"
                @click="selectInstance(instance)"
              >
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium truncate">{{ instance.instanceName }}</p>
                    <p class="text-xs text-gray-500 truncate">{{ instance.componentName }}</p>
                  </div>
                  <div class="flex items-center gap-1">
                    <span class="text-xs text-gray-500">×{{ instance.quantity }}</span>
                    <Button
                      @click.stop="removeInstance(instance.id)"
                      variant="ghost"
                      size="sm"
                      class="w-6 h-6 p-0 text-red-500 hover:text-red-600"
                    >
                      <X class="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间设计画布 -->
        <div class="flex-1 flex flex-col">
          <!-- 画布工具栏 -->
          <div class="flex items-center justify-between p-3 border-b bg-white">
            <div class="flex items-center gap-2">
              <Button @click="zoomOut" variant="outline" size="sm">
                <ZoomOut class="w-4 h-4" />
              </Button>
              <span class="text-sm text-gray-600">{{ Math.round(zoomLevel * 100) }}%</span>
              <Button @click="zoomIn" variant="outline" size="sm">
                <ZoomIn class="w-4 h-4" />
              </Button>
              <Button @click="resetView" variant="outline" size="sm">
                <RotateCcw class="w-4 h-4" />
              </Button>
            </div>
            <div class="flex items-center gap-2">
              <Button @click="toggleGrid" variant="outline" size="sm">
                <Grid3X3 class="w-4 h-4" />
                网格
              </Button>
              <Button @click="autoLayout" variant="outline" size="sm">
                <Layout class="w-4 h-4" />
                自动布局
              </Button>
            </div>
          </div>

          <!-- 设计画布 -->
          <div
            ref="canvasContainer"
            class="flex-1 overflow-hidden bg-gray-100 relative"
            @drop="handleDrop"
            @dragover="handleDragOver"
          >
            <div
              class="absolute inset-0"
              :style="{
                transform: `scale(${zoomLevel}) translate(${panX}px, ${panY}px)`,
                transformOrigin: 'top left'
              }"
            >
              <!-- 网格背景 -->
              <div
                v-if="showGrid"
                class="absolute inset-0 opacity-20"
                :style="gridStyle"
              ></div>

              <!-- 组件实例节点 -->
              <div
                v-for="instance in componentInstances"
                :key="instance.id"
                class="absolute border-2 rounded-lg bg-white shadow-sm cursor-move"
                :class="{
                  'border-blue-500': selectedInstance?.id === instance.id,
                  'border-gray-300': selectedInstance?.id !== instance.id
                }"
                :style="{
                  left: `${instance.position?.x || 0}px`,
                  top: `${instance.position?.y || 0}px`,
                  width: '120px',
                  height: '80px'
                }"
                @click="selectInstance(instance)"
                @mousedown="startDrag($event, instance)"
              >
                <div class="p-2 h-full flex flex-col">
                  <div class="flex items-center gap-1 mb-1">
                    <component :is="getComponentIcon(getComponentType(instance.componentId))" class="w-3 h-3 text-gray-600" />
                    <span class="text-xs font-medium truncate">{{ instance.instanceName }}</span>
                  </div>
                  <p class="text-xs text-gray-500 truncate mb-1">{{ instance.componentName }}</p>
                  <div class="mt-auto flex items-center justify-between text-xs">
                    <span class="text-gray-500">×{{ instance.quantity }}</span>
                    <div
                      v-if="instance.optional"
                      class="w-2 h-2 rounded-full bg-yellow-400"
                      title="可选组件"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- 连接线 -->
              <svg class="absolute inset-0 pointer-events-none" style="z-index: 1;">
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
                  </marker>
                </defs>
                <!-- 这里可以添加组件之间的连接线 -->
              </svg>
            </div>
          </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="w-80 border-l bg-gray-50 flex flex-col">
          <div class="p-4 border-b bg-white">
            <h3 class="font-medium">属性面板</h3>
          </div>

          <div v-if="!selectedInstance" class="flex-1 flex items-center justify-center text-gray-500">
            <div class="text-center">
              <Settings class="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p class="text-sm">选择组件实例查看属性</p>
            </div>
          </div>

          <div v-else class="flex-1 overflow-y-auto p-4 space-y-4">
            <!-- 基本信息 -->
            <Card>
              <CardHeader class="pb-3">
                <CardTitle class="text-sm">基本信息</CardTitle>
              </CardHeader>
              <CardContent class="space-y-3">
                <div>
                  <Label class="text-xs">实例名称</Label>
                  <Input
                    v-model="selectedInstance.instanceName"
                    class="text-sm"
                    @input="updateInstance"
                  />
                </div>
                <div>
                  <Label class="text-xs">数量</Label>
                  <Input
                    v-model.number="selectedInstance.quantity"
                    type="number"
                    min="1"
                    class="text-sm"
                    @input="updateInstance"
                  />
                </div>
                <div class="flex items-center space-x-2">
                  <Checkbox
                    v-model="selectedInstance.optional"
                    @update:model-value="updateInstance"
                  />
                  <Label class="text-xs">可选组件</Label>
                </div>
              </CardContent>
            </Card>

            <!-- 位置信息 -->
            <Card>
              <CardHeader class="pb-3">
                <CardTitle class="text-sm">位置信息</CardTitle>
              </CardHeader>
              <CardContent class="space-y-3">
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <Label class="text-xs">X坐标</Label>
                    <Input
                      v-model.number="selectedInstance.position.x"
                      type="number"
                      class="text-sm"
                      @input="updateInstance"
                    />
                  </div>
                  <div>
                    <Label class="text-xs">Y坐标</Label>
                    <Input
                      v-model.number="selectedInstance.position.y"
                      type="number"
                      class="text-sm"
                      @input="updateInstance"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- 参数配置 -->
            <Card v-if="selectedComponentParameters.length > 0">
              <CardHeader class="pb-3">
                <CardTitle class="text-sm">参数配置</CardTitle>
              </CardHeader>
              <CardContent class="space-y-3">
                <div
                  v-for="param in selectedComponentParameters"
                  :key="param.id"
                  class="space-y-1"
                >
                  <Label class="text-xs">{{ param.displayName }}</Label>
                  <Input
                    v-if="param.type === 'number'"
                    v-model.number="selectedInstance.parameterValues[param.name]"
                    type="number"
                    :min="param.minValue"
                    :max="param.maxValue"
                    class="text-sm"
                    @input="updateInstance"
                  />
                  <Input
                    v-else-if="param.type === 'string'"
                    v-model="selectedInstance.parameterValues[param.name]"
                    class="text-sm"
                    @input="updateInstance"
                  />
                  <Select
                    v-else-if="param.type === 'select'"
                    v-model="selectedInstance.parameterValues[param.name]"
                    @update:model-value="updateInstance"
                  >
                    <SelectTrigger class="text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="option in param.options"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="param.description" class="text-xs text-gray-500">
                    {{ param.description }}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button @click="$emit('update:open', false)" variant="outline">
          取消
        </Button>
        <Button @click="saveDesign" class="bg-blue-600 hover:bg-blue-700">
          <Save class="w-4 h-4 mr-2" />
          保存设计
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import {
  PenTool,
  Search,
  X,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Grid3X3,
  Layout,
  Settings,
  Save,
  Square,
  Package,
  Wrench,
  HelpCircle
} from 'lucide-vue-next';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { componentService } from '@/services/componentService';
import type { 
  Assembly, 
  ComponentInstance, 
  Component, 
  ComponentType,
  ComponentParameter 
} from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  assembly?: Assembly | null;
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'save', assembly: Assembly): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const componentSearchQuery = ref('');
const availableComponents = ref<Component[]>([]);
const componentInstances = ref<ComponentInstance[]>([]);
const selectedInstance = ref<ComponentInstance | null>(null);

// 画布相关
const canvasContainer = ref<HTMLElement>();
const zoomLevel = ref(1);
const panX = ref(0);
const panY = ref(0);
const showGrid = ref(true);
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

// 计算属性
const filteredComponents = computed(() => {
  if (!componentSearchQuery.value) return availableComponents.value;
  
  const query = componentSearchQuery.value.toLowerCase();
  return availableComponents.value.filter(component =>
    component.name.toLowerCase().includes(query) ||
    component.code.toLowerCase().includes(query)
  );
});

const selectedComponentParameters = computed(() => {
  if (!selectedInstance.value) return [];
  
  const component = availableComponents.value.find(
    c => c.id === selectedInstance.value?.componentId
  );
  return component?.parameters || [];
});

const gridStyle = computed(() => {
  const gridSize = 20 * zoomLevel.value;
  return {
    backgroundImage: `
      linear-gradient(to right, #e5e7eb 1px, transparent 1px),
      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
    `,
    backgroundSize: `${gridSize}px ${gridSize}px`
  };
});

// 方法
const loadComponents = async () => {
  try {
    const result = await componentService.getComponents();
    availableComponents.value = result.components;
  } catch (error) {
    console.error('加载组件失败:', error);
  }
};

const getComponentIcon = (type: ComponentType) => {
  const iconMap = {
    frame: Square,
    glass: Package,
    hardware: Wrench,
    seal: Package,
    other: HelpCircle
  };
  return iconMap[type] || HelpCircle;
};

const getComponentType = (componentId: string): ComponentType => {
  const component = availableComponents.value.find(c => c.id === componentId);
  return component?.componentType || 'other';
};

const handleDragStart = (event: DragEvent, component: Component) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(component));
    event.dataTransfer.effectAllowed = 'copy';
  }
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy';
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  
  if (!event.dataTransfer) return;
  
  try {
    const component = JSON.parse(event.dataTransfer.getData('application/json')) as Component;
    const rect = canvasContainer.value?.getBoundingClientRect();
    
    if (rect) {
      const x = (event.clientX - rect.left) / zoomLevel.value - panX.value;
      const y = (event.clientY - rect.top) / zoomLevel.value - panY.value;
      
      addComponentInstance(component, { x, y });
    }
  } catch (error) {
    console.error('处理拖放失败:', error);
  }
};

const addComponentInstance = (component: Component, position: { x: number; y: number }) => {
  const newInstance: ComponentInstance = {
    id: `ci_${Date.now()}`,
    componentId: component.id,
    componentCode: component.code,
    componentName: component.name,
    instanceName: `${component.name}_${componentInstances.value.length + 1}`,
    parameterValues: {},
    quantity: 1,
    position: { x: position.x, y: position.y, z: 0 },
    optional: false,
    description: ''
  };
  
  // 设置默认参数值
  component.parameters.forEach(param => {
    if (param.defaultValue !== undefined) {
      newInstance.parameterValues[param.name] = param.defaultValue;
    }
  });
  
  componentInstances.value.push(newInstance);
  selectedInstance.value = newInstance;
};

const selectInstance = (instance: ComponentInstance) => {
  selectedInstance.value = instance;
};

const removeInstance = (instanceId: string) => {
  const index = componentInstances.value.findIndex(i => i.id === instanceId);
  if (index > -1) {
    componentInstances.value.splice(index, 1);
    if (selectedInstance.value?.id === instanceId) {
      selectedInstance.value = null;
    }
  }
};

const updateInstance = () => {
  // 触发响应式更新
  if (selectedInstance.value) {
    const index = componentInstances.value.findIndex(i => i.id === selectedInstance.value?.id);
    if (index > -1) {
      componentInstances.value[index] = { ...selectedInstance.value };
    }
  }
};

const startDrag = (event: MouseEvent, instance: ComponentInstance) => {
  if (event.button !== 0) return; // 只处理左键
  
  isDragging.value = true;
  selectedInstance.value = instance;
  
  const rect = canvasContainer.value?.getBoundingClientRect();
  if (rect) {
    dragOffset.value = {
      x: event.clientX - rect.left - (instance.position?.x || 0) * zoomLevel.value,
      y: event.clientY - rect.top - (instance.position?.y || 0) * zoomLevel.value
    };
  }
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value || !selectedInstance.value || !canvasContainer.value) return;
    
    const rect = canvasContainer.value.getBoundingClientRect();
    const x = (e.clientX - rect.left - dragOffset.value.x) / zoomLevel.value;
    const y = (e.clientY - rect.top - dragOffset.value.y) / zoomLevel.value;
    
    selectedInstance.value.position = { x, y, z: 0 };
    updateInstance();
  };
  
  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };
  
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
  
  event.preventDefault();
};

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3);
};

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.3);
};

const resetView = () => {
  zoomLevel.value = 1;
  panX.value = 0;
  panY.value = 0;
};

const toggleGrid = () => {
  showGrid.value = !showGrid.value;
};

const autoLayout = () => {
  // 简单的自动布局算法
  const padding = 20;
  const itemWidth = 120;
  const itemHeight = 80;
  const cols = Math.floor(Math.sqrt(componentInstances.value.length));
  
  componentInstances.value.forEach((instance, index) => {
    const row = Math.floor(index / cols);
    const col = index % cols;
    
    instance.position = {
      x: col * (itemWidth + padding) + padding,
      y: row * (itemHeight + padding) + padding,
      z: 0
    };
  });
};

const saveDesign = () => {
  if (!props.assembly) return;
  
  const updatedAssembly: Assembly = {
    ...props.assembly,
    componentInstances: componentInstances.value
  };
  
  emit('save', updatedAssembly);
};

// 监听props变化
watch(() => props.open, (open) => {
  if (open) {
    loadComponents();
    if (props.assembly) {
      componentInstances.value = [...(props.assembly.componentInstances || [])];
    } else {
      componentInstances.value = [];
    }
    selectedInstance.value = null;
  }
});
</script>
