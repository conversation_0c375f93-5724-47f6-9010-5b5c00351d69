<template>
  <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
    <div class="flex items-start gap-4">
      <div class="flex-shrink-0">
        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
          <Lightbulb class="w-5 h-5 text-blue-600" />
        </div>
      </div>
      
      <div class="flex-1">
        <h3 class="text-lg font-medium text-blue-900 mb-3">构件设计指南</h3>
        <p class="text-sm text-blue-700 mb-4">
          构件管理专注于产品结构设计，帮助您快速、准确地设计出产品结构，为后续的工艺设计提供清晰的基础。
        </p>
        
        <div class="space-y-4">
          <div class="bg-white rounded-lg p-4 border border-blue-200">
            <h4 class="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <Package class="w-4 h-4" />
              1. 结构设计
            </h4>
            <ul class="text-sm text-blue-700 space-y-1 ml-6">
              <li>• 添加组件实例，定义产品的基本组成</li>
              <li>• 设置组件间的层级关系和约束</li>
              <li>• 确保结构的完整性和合理性</li>
            </ul>
          </div>
          
          <div class="bg-white rounded-lg p-4 border border-blue-200">
            <h4 class="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <Settings class="w-4 h-4" />
              2. 参数配置
            </h4>
            <ul class="text-sm text-blue-700 space-y-1 ml-6">
              <li>• 定义构件级别的参数</li>
              <li>• 配置参数如何透传到组件实例</li>
              <li>• 确保参数的一致性和完整性</li>
            </ul>
          </div>
          
          <div class="bg-white rounded-lg p-4 border border-blue-200">
            <h4 class="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <Eye class="w-4 h-4" />
              3. 结构预览
            </h4>
            <ul class="text-sm text-blue-700 space-y-1 ml-6">
              <li>• 查看完整的产品结构树</li>
              <li>• 验证参数配置的正确性</li>
              <li>• 确认结构设计满足需求</li>
            </ul>
          </div>
        </div>
        
        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <div class="flex items-start gap-2">
            <AlertTriangle class="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div class="text-sm text-yellow-800">
              <strong>注意：</strong>构件管理不涉及具体的工艺流程设计。完成结构设计后，工艺设计师可以基于这个结构来设计具体的装配工艺和生产流程。
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Lightbulb, Package, Settings, Eye, AlertTriangle } from 'lucide-vue-next';
</script>
