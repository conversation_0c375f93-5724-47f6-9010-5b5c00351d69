<template>
  <div v-if="completenessResult && !completenessResult.isComplete" class="space-y-8">

    <!-- 缺失的必需参数 -->
    <div v-if="completenessResult.missingParameters.length > 0" class="bg-red-50 border border-red-200 rounded-xl p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center gap-3">
          <div class="flex items-center justify-center w-10 h-10 bg-red-100 rounded-full">
            <AlertTriangle class="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-red-900">缺失必需参数</h3>
            <p class="text-sm text-red-700">基于构件类型"{{ getAssemblyTypeText(assemblyType) }}"检测到 {{ completenessResult.missingParameters.length }} 个必需参数未定义</p>
          </div>
        </div>
        <Button
          @click="addAllMissingParameters"
          class="bg-red-600 hover:bg-red-700 text-white"
        >
          <Plus class="w-4 h-4 mr-2" />
          全部添加
        </Button>
      </div>

      <div class="grid gap-4">
        <div
          v-for="param in completenessResult.missingParameters"
          :key="param.id"
          class="bg-white rounded-lg border border-red-200 p-5 hover:shadow-sm transition-shadow"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0 pr-4">
              <!-- 参数标题 -->
              <div class="flex items-center gap-3 mb-3">
                <Badge variant="destructive" class="text-xs">必需</Badge>
                <h4 class="text-lg font-medium text-gray-900">{{ param.displayName }}</h4>
                <Badge variant="outline" class="text-xs">{{ getParameterTypeText(param.type) }}</Badge>
              </div>

              <!-- 技术名称 -->
              <div class="mb-3">
                <span class="text-sm text-gray-600">参数名称: </span>
                <code class="text-sm bg-gray-100 px-2 py-1 rounded font-mono">{{ param.name }}</code>
              </div>

              <!-- 描述 -->
              <div v-if="param.description" class="text-sm text-gray-700 leading-relaxed">
                {{ param.description }}
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex-shrink-0">
              <Button
                @click="addMissingParameter(param)"
                size="sm"
                variant="outline"
                class="border-red-300 text-red-700 hover:bg-red-50"
              >
                添加参数
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能参数建议 -->
    <div v-if="completenessResult.suggestions.length > 0" class="bg-blue-50 border border-blue-200 rounded-xl p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center gap-3">
          <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
            <Lightbulb class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-blue-900">智能参数建议</h3>
            <p class="text-sm text-blue-700">基于已选择的组件，建议添加 {{ completenessResult.suggestions.length }} 个参数以提高配置完整性</p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <Button
            v-if="completenessResult.suggestions.length > 3"
            @click="showAllSuggestions = !showAllSuggestions"
            size="sm"
            variant="ghost"
            class="text-blue-600 hover:bg-blue-100"
          >
            {{ showAllSuggestions ? '收起' : `展开全部 (${completenessResult.suggestions.length})` }}
            <ChevronDown :class="['w-4 h-4 ml-1 transition-transform', showAllSuggestions && 'rotate-180']" />
          </Button>
          <Button
            @click="addAllSuggestedParameters"
            variant="outline"
            class="border-blue-300 text-blue-700 hover:bg-blue-100"
          >
            <Plus class="w-4 h-4 mr-2" />
            全部添加
          </Button>
        </div>
      </div>

      <div class="grid gap-4">
        <div
          v-for="param in completenessResult.suggestions.slice(0, showAllSuggestions ? undefined : 3)"
          :key="param.id"
          class="bg-white rounded-lg border border-blue-200 p-5 hover:shadow-sm transition-shadow"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0 pr-4">
              <!-- 参数标题 -->
              <div class="flex items-center gap-3 mb-3">
                <Badge variant="secondary" class="text-xs bg-blue-100 text-blue-800">建议</Badge>
                <h4 class="text-lg font-medium text-gray-900">{{ param.displayName }}</h4>
                <Badge variant="outline" class="text-xs">{{ getParameterTypeText(param.type) }}</Badge>
              </div>

              <!-- 技术名称 -->
              <div class="mb-3">
                <span class="text-sm text-gray-600">参数名称: </span>
                <code class="text-sm bg-gray-100 px-2 py-1 rounded font-mono">{{ param.name }}</code>
              </div>

              <!-- 描述 -->
              <div v-if="param.description" class="text-sm text-gray-700 leading-relaxed">
                {{ param.description }}
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex-shrink-0">
              <Button
                @click="addSuggestedParameter(param)"
                size="sm"
                variant="outline"
                class="border-blue-300 text-blue-700 hover:bg-blue-50"
              >
                添加参数
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 参数冲突警告 -->
    <div v-if="completenessResult.conflicts.length > 0" class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
      <div class="flex items-center gap-3 mb-6">
        <div class="flex items-center justify-center w-10 h-10 bg-yellow-100 rounded-full">
          <AlertCircle class="w-5 h-5 text-yellow-600" />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-yellow-900">参数冲突检测</h3>
          <p class="text-sm text-yellow-700">检测到 {{ completenessResult.conflicts.length }} 个参数冲突，建议及时处理</p>
        </div>
      </div>

      <div class="grid gap-4">
        <div
          v-for="conflict in completenessResult.conflicts"
          :key="conflict.parameterName"
          class="bg-white rounded-lg border border-yellow-200 p-5"
        >
          <div class="flex items-center gap-3 mb-3">
            <Badge variant="destructive" class="text-xs">{{ getConflictTypeText(conflict.conflictType) }}</Badge>
            <h4 class="text-lg font-medium text-gray-900">{{ conflict.parameterName }}</h4>
          </div>

          <p class="text-sm text-gray-700 mb-3">{{ conflict.description }}</p>

          <div v-if="conflict.suggestions.length > 0" class="flex flex-wrap gap-2">
            <span class="text-sm text-gray-600">建议解决方案:</span>
            <Badge
              v-for="suggestion in conflict.suggestions"
              :key="suggestion"
              variant="outline"
              class="text-xs"
            >
              {{ suggestion }}
            </Badge>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作工具栏 -->
    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
      <div class="flex items-center gap-3">
        <Button
          @click="recheckCompleteness"
          size="sm"
          variant="outline"
          class="flex items-center gap-2"
        >
          <RefreshCw class="w-4 h-4" />
          重新检查
        </Button>
        <Button
          v-if="completenessResult.missingParameters.length > 0 || completenessResult.suggestions.length > 0"
          @click="openParameterTemplate"
          size="sm"
          variant="outline"
          class="flex items-center gap-2"
        >
          <LayoutTemplate class="w-4 h-4" />
          参数模板
        </Button>
      </div>

      <div class="text-sm text-gray-500">
        {{ getCompletionSummary() }}
      </div>
    </div>
  </div>

  <!-- 完整性通过提示 -->
  <div v-else-if="completenessResult?.isComplete" class="bg-green-50 border border-green-200 rounded-xl p-6">
    <div class="flex items-center gap-3">
      <div class="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
        <CheckCircle class="w-5 h-5 text-green-600" />
      </div>
      <div>
        <h3 class="text-lg font-semibold text-green-900">参数完整性检查通过</h3>
        <p class="text-sm text-green-700">构件参数定义完整，满足当前构件类型和组件配置的要求</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
// import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  AlertTriangle,
  Lightbulb,
  AlertCircle,
  CheckCircle,
  Plus,
  ChevronDown,
  RefreshCw,
  LayoutTemplate
} from 'lucide-vue-next';

import type {
  AssemblyType,
  ComponentParameter,
  Component
} from '@/types/product-structure';
import type { ParameterCompletenessResult } from '@/services/assemblyTemplateService';

// Props
interface Props {
  assemblyType: AssemblyType;
  currentParameters: ComponentParameter[];
  selectedComponents: Component[];
  completenessResult: ParameterCompletenessResult | null;
}

// Emits
interface Emits {
  (e: 'add-parameter', parameter: ComponentParameter): void;
  (e: 'add-parameters', parameters: ComponentParameter[]): void;
  (e: 'recheck'): void;
  (e: 'open-template'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const showAllSuggestions = ref(false);

// 方法
const getAssemblyTypeText = (type: AssemblyType): string => {
  const typeMap: Record<AssemblyType, string> = {
    'frame_assembly': '框架构件',
    'glass_assembly': '玻璃构件',
    'hardware_assembly': '五金构件',
    'complete_assembly': '完整构件'
  };
  return typeMap[type] || type;
};

const getParameterTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'number': '数值',
    'string': '文本',
    'boolean': '布尔',
    'select': '选择',
    'formula': '公式'
  };
  return typeMap[type] || type;
};

const getConflictTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'duplicate': '重复定义',
    'type_mismatch': '类型不匹配',
    'range_conflict': '范围冲突'
  };
  return typeMap[type] || type;
};

const addMissingParameter = (parameter: ComponentParameter) => {
  emit('add-parameter', parameter);
};

const addAllMissingParameters = () => {
  if (props.completenessResult?.missingParameters) {
    emit('add-parameters', props.completenessResult.missingParameters);
  }
};

const addAllSuggestedParameters = () => {
  if (props.completenessResult?.suggestions) {
    emit('add-parameters', props.completenessResult.suggestions);
  }
};

const getCompletionSummary = () => {
  if (!props.completenessResult) return '';

  const missing = props.completenessResult.missingParameters.length;
  const suggestions = props.completenessResult.suggestions.length;
  const conflicts = props.completenessResult.conflicts.length;

  const parts = [];
  if (missing > 0) parts.push(`${missing} 个必需参数`);
  if (suggestions > 0) parts.push(`${suggestions} 个建议参数`);
  if (conflicts > 0) parts.push(`${conflicts} 个冲突`);

  return parts.length > 0 ? `待处理: ${parts.join(', ')}` : '参数配置完整';
};

const addSuggestedParameter = (parameter: ComponentParameter) => {
  emit('add-parameter', parameter);
};

const recheckCompleteness = () => {
  emit('recheck');
};

const openParameterTemplate = () => {
  emit('open-template');
};
</script>
