<template>
  <div class="assembly-list-item flex items-center p-4 hover:bg-gray-50 transition-colors">
    <!-- 选择框 -->
    <div class="flex items-center mr-4">
      <Checkbox
        :checked="selected"
        @update:checked="$emit('select', assembly.id)"
      />
    </div>

    <!-- 构件信息 -->
    <div class="flex-1 min-w-0">
      <div class="flex items-center gap-3 mb-2">
        <!-- 类型图标 -->
        <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100">
          <component :is="getTypeIcon(assembly.assemblyType)" class="w-5 h-5 text-gray-600" />
        </div>

        <!-- 基本信息 -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center gap-2 mb-1">
            <h3 class="text-lg font-medium text-gray-900 truncate">
              {{ assembly.name }}
            </h3>
            <Badge :variant="getStatusVariant(assembly.status)" class="text-xs">
              {{ getStatusText(assembly.status) }}
            </Badge>
          </div>
          <div class="flex items-center gap-4 text-sm text-gray-500">
            <span>编码: {{ assembly.code }}</span>
            <span>类型: {{ getTypeText(assembly.assemblyType) }}</span>
            <span>组件: {{ (assembly.componentInstances || []).length }}个</span>
            <span v-if="(assembly.subAssemblies || []).length > 0">
              子构件: {{ (assembly.subAssemblies || []).length }}个
            </span>
          </div>
        </div>
      </div>

      <!-- 描述和标签 -->
      <div class="flex items-center justify-between">
        <div class="flex-1 min-w-0">
          <p v-if="assembly.description" class="text-sm text-gray-600 truncate mb-1">
            {{ assembly.description }}
          </p>
          <div v-if="(assembly.tags || []).length > 0" class="flex items-center gap-1 flex-wrap">
            <Badge
              v-for="tag in (assembly.tags || []).slice(0, 3)"
              :key="tag"
              variant="secondary"
              class="text-xs"
            >
              {{ tag }}
            </Badge>
            <span v-if="(assembly.tags || []).length > 3" class="text-xs text-gray-400">
              +{{ (assembly.tags || []).length - 3 }}
            </span>
          </div>
        </div>

        <!-- 装配工艺信息 -->
        <div class="flex items-center gap-4 text-sm text-gray-500 ml-4">
          <div class="flex items-center gap-1">
            <Clock class="w-4 h-4" />
            <span>{{ assembly.assemblyProcess?.totalEstimatedTime || 0 }}分钟</span>
          </div>
          <div class="flex items-center gap-1">
            <CheckCircle class="w-4 h-4" />
            <span>{{ (assembly.qualityRequirements || []).length }}项质检</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center gap-2 ml-4">
      <Button
        @click="$emit('design', assembly)"
        variant="outline"
        size="sm"
        class="text-blue-600 border-blue-200 hover:bg-blue-50"
      >
        <PenTool class="w-4 h-4 mr-1" />
        设计
      </Button>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreHorizontal class="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem @click="$emit('edit', assembly)">
            <Edit class="w-4 h-4 mr-2" />
            编辑
          </DropdownMenuItem>
          <DropdownMenuItem @click="showDetails = true">
            <Eye class="w-4 h-4 mr-2" />
            查看详情
          </DropdownMenuItem>
          <DropdownMenuItem @click="$emit('duplicate', assembly)">
            <Copy class="w-4 h-4 mr-2" />
            复制
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            @click="$emit('delete', assembly)"
            class="text-red-600 focus:text-red-600"
          >
            <Trash2 class="w-4 h-4 mr-2" />
            删除
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- 详情对话框 -->
    <AssemblyDetailsDialog
      :open="showDetails"
      :assembly="assembly"
      @update:open="showDetails = $event"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  Clock,
  CheckCircle,
  PenTool,
  MoreHorizontal,
  Edit,
  Eye,
  Copy,
  Trash2,
  Layers,
  Square,
  Package,
  Wrench,
  HelpCircle
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import AssemblyDetailsDialog from './AssemblyDetailsDialog.vue';
import type { Assembly, AssemblyType } from '@/types/product-structure';

// Props
interface Props {
  assembly: Assembly;
  selected: boolean;
}

// Emits
interface Emits {
  (e: 'select', assemblyId: string): void;
  (e: 'edit', assembly: Assembly): void;
  (e: 'duplicate', assembly: Assembly): void;
  (e: 'delete', assembly: Assembly): void;
  (e: 'design', assembly: Assembly): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const showDetails = ref(false);

// 方法
const getTypeIcon = (type: AssemblyType) => {
  const iconMap = {
    frame_assembly: Square,
    glass_assembly: Package,
    hardware_assembly: Wrench,
    complete_assembly: Layers
  };
  return iconMap[type] || HelpCircle;
};

const getTypeText = (type: AssemblyType) => {
  const textMap = {
    frame_assembly: '框架构件',
    glass_assembly: '玻璃构件',
    hardware_assembly: '五金构件',
    complete_assembly: '完整构件'
  };
  return textMap[type] || '未知类型';
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'secondary';
};

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    active: '激活',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};
</script>

<style scoped>
.assembly-list-item:hover .opacity-0 {
  opacity: 1;
}
</style>
