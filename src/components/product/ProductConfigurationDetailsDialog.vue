<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>产品配置详情</DialogTitle>
        <DialogDescription>
          查看产品配置的详细信息和材料组成
        </DialogDescription>
      </DialogHeader>

      <div v-if="configuration" class="space-y-6">
        <!-- 基本信息 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-lg">基本信息</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label class="text-sm font-medium text-gray-600">产品名称</Label>
                <p class="text-sm font-medium">{{ configuration.name }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-600">结构模板</Label>
                <p class="text-sm">{{ configuration.structureTemplateName }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-600">产品分类</Label>
                <p class="text-sm">{{ configuration.category }} / {{ configuration.subCategory }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-600">状态</Label>
                <Badge :variant="getStatusVariant(configuration.status)">
                  {{ getStatusText(configuration.status) }}
                </Badge>
              </div>
            </div>

            <div>
              <Label class="text-sm font-medium text-gray-600">产品描述</Label>
              <p class="text-sm text-gray-700 mt-1">{{ configuration.description || '暂无描述' }}</p>
            </div>

            <div>
              <Label class="text-sm font-medium text-gray-600">标签</Label>
              <div class="flex flex-wrap gap-2 mt-1">
                <Badge v-for="tag in configuration.tags" :key="tag" variant="secondary">
                  {{ tag }}
                </Badge>
                <span v-if="configuration.tags.length === 0" class="text-sm text-gray-500">暂无标签</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 使用统计 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-lg">使用统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ configuration.usageCount }}</div>
                <div class="text-sm text-blue-600">使用次数</div>
              </div>
              <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">¥{{ configuration.averageCost.toFixed(2) }}</div>
                <div class="text-sm text-green-600">平均成本</div>
              </div>
              <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-sm font-medium text-purple-600">
                  {{ configuration.lastUsedAt ? formatDate(configuration.lastUsedAt) : '未使用' }}
                </div>
                <div class="text-sm text-purple-600">最后使用</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 材料组成 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-lg">材料组成</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div 
                v-for="component in configuration.materialComponents" 
                :key="component.id"
                class="border rounded-lg p-4"
              >
                <div class="flex items-center justify-between mb-3">
                  <h4 class="font-medium">{{ component.componentName }}</h4>
                  <Badge variant="outline">{{ component.unit }}</Badge>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <Label class="text-xs text-gray-600">材料名称</Label>
                    <p class="font-medium">{{ component.materialVariantName }}</p>
                  </div>
                  <div>
                    <Label class="text-xs text-gray-600">计算公式</Label>
                    <p class="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                      {{ component.quantityFormula }}
                    </p>
                  </div>
                  <div>
                    <Label class="text-xs text-gray-600">单位成本</Label>
                    <p class="font-medium">¥{{ component.unitCost.toFixed(2) }}</p>
                  </div>
                  <div>
                    <Label class="text-xs text-gray-600">损耗率</Label>
                    <p class="font-medium">{{ (component.wastageRate * 100).toFixed(1) }}%</p>
                  </div>
                </div>
              </div>

              <div v-if="configuration.materialComponents.length === 0" class="text-center py-8 text-gray-500">
                <Package class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>暂无材料组件</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 版本信息 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-lg">版本信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <Label class="text-xs text-gray-600">版本号</Label>
                <p class="font-medium">v{{ configuration.version }}</p>
              </div>
              <div>
                <Label class="text-xs text-gray-600">创建时间</Label>
                <p>{{ formatDate(configuration.createdAt) }}</p>
              </div>
              <div>
                <Label class="text-xs text-gray-600">更新时间</Label>
                <p>{{ formatDate(configuration.updatedAt) }}</p>
              </div>
              <div>
                <Label class="text-xs text-gray-600">创建人</Label>
                <p>{{ configuration.createdBy }}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="$emit('update:open', false)">
          关闭
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import Badge from '@/components/ui/badge/Badge.vue';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Package } from 'lucide-vue-next';

import type { ProductConfiguration, LifecycleStatus } from '@/types/product';

// Props
interface Props {
  open: boolean;
  configuration?: ProductConfiguration | null;
}

defineProps<Props>();

// Emits
defineEmits<{
  'update:open': [open: boolean];
}>();

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取状态样式
const getStatusVariant = (status: LifecycleStatus): 'default' | 'secondary' | 'destructive' | 'outline' => {
  const variants: Record<LifecycleStatus, 'default' | 'secondary' | 'destructive' | 'outline'> = {
    active: 'default',
    draft: 'secondary',
    deprecated: 'destructive',
    archived: 'outline'
  };
  return variants[status] || 'outline';
};

// 获取状态文本
const getStatusText = (status: LifecycleStatus) => {
  const texts = {
    active: '激活',
    draft: '草稿',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return texts[status] || status;
};
</script>

<style scoped>
/* 自定义样式 */
</style>