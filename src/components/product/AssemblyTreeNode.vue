<template>
  <div class="assembly-tree-node">
    <!-- 构件节点 -->
    <TreeNode
      :node="assemblyNode"
      :level="level"
      :selected="selectedNode === assembly.id"
      :expanded="expandedNodes.has(assembly.id)"
      @select="$emit('node-select', assembly.id)"
      @toggle="$emit('node-toggle', assembly.id)"
      @add="handleAddNode"
      @delete="$emit('node-delete', assembly.id)"
    />

    <!-- 子节点 -->
    <div v-if="expandedNodes.has(assembly.id)" class="ml-4">
      <!-- 组件实例 -->
      <div v-if="assembly.componentInstances && assembly.componentInstances.length > 0">
        <ComponentTreeNode
          v-for="component in assembly.componentInstances"
          :key="component.id"
          :component="component"
          :level="level + 1"
          :selected-node="selectedNode"
          :expanded-nodes="expandedNodes"
          @node-select="$emit('node-select', $event)"
          @node-toggle="$emit('node-toggle', $event)"
          @node-add="$emit('node-add', $event)"
          @node-delete="$emit('node-delete', $event)"
        />
      </div>

      <!-- 子构件 -->
      <div v-if="assembly.subAssemblies && assembly.subAssemblies.length > 0">
        <AssemblyTreeNode
          v-for="subAssembly in assembly.subAssemblies"
          :key="subAssembly.id"
          :assembly="subAssembly"
          :level="level + 1"
          :parent-id="assembly.id"
          :selected-node="selectedNode"
          :expanded-nodes="expandedNodes"
          @node-select="$emit('node-select', $event)"
          @node-toggle="$emit('node-toggle', $event)"
          @node-add="$emit('node-add', $event)"
          @node-delete="$emit('node-delete', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import TreeNode from './TreeNode.vue';
import ComponentTreeNode from './ComponentTreeNode.vue';

// Props
interface Props {
  assembly: any; // AssemblyInstance type
  level: number;
  parentId: string;
  selectedNode: string | null;
  expandedNodes: Set<string>;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'node-select': [nodeId: string];
  'node-toggle': [nodeId: string];
  'node-add': [data: { parentId: string; type: string }];
  'node-delete': [nodeId: string];
}>();

// 计算属性
const assemblyNode = computed(() => ({
  id: props.assembly.id,
  name: props.assembly.assemblyName || props.assembly.instanceName,
  code: props.assembly.assemblyCode,
  type: 'assembly',
  description: props.assembly.description,
  hasChildren: hasChildren.value
}));

const hasChildren = computed(() => {
  return (
    (props.assembly.componentInstances && props.assembly.componentInstances.length > 0) ||
    (props.assembly.subAssemblies && props.assembly.subAssemblies.length > 0)
  );
});

// 方法
const handleAddNode = (nodeId: string) => {
  // 构件可以添加组件或子构件
  emit('node-add', { parentId: nodeId, type: 'component' });
};
</script>

<style scoped>
.assembly-tree-node {
  /* 样式 */
}
</style>
