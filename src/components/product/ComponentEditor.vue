<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[90vw] h-[90vh] max-w-none max-h-none overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle>
          {{ isEditing ? '编辑组件' : '新建组件' }}
        </DialogTitle>
        <DialogDescription>
          {{ isEditing ? '修改组件的基本信息、参数定义和约束条件' : '创建新的组件定义' }}
        </DialogDescription>
      </DialogHeader>

      <div class="flex-1 overflow-hidden">
        <Tabs v-model="activeTab" class="h-full flex flex-col">
          <div class="border-b border-gray-200 px-6 py-3">
            <TabsList class="grid w-full grid-cols-5 max-w-2xl">
              <TabsTrigger value="basic" class="text-sm">
                <Package class="w-4 h-4 mr-2" />
                基本信息
              </TabsTrigger>
              <TabsTrigger value="parameters" class="text-sm">
                <Settings class="w-4 h-4 mr-2" />
                参数定义
              </TabsTrigger>
              <TabsTrigger value="constraints" class="text-sm">
                <Shield class="w-4 h-4 mr-2" />
                约束条件
              </TabsTrigger>
              <TabsTrigger value="process" class="text-sm">
                <Wrench class="w-4 h-4 mr-2" />
                工艺要求
              </TabsTrigger>
              <TabsTrigger value="preview" class="text-sm">
                <Eye class="w-4 h-4 mr-2" />
                预览
              </TabsTrigger>
            </TabsList>
          </div>

          <div class="flex-1 overflow-hidden flex">
            <!-- 基本信息 -->
            <TabsContent value="basic" class="flex-1 overflow-hidden">
              <div class="h-full flex">
                <!-- 左侧表单 -->
                <div class="flex-1 overflow-y-auto p-6">
                  <ComponentBasicForm
                    v-model="formData"
                    :errors="validationErrors"
                    @validate="validateBasicInfo"
                  />
                </div>

                <!-- 右侧预览 -->
                <div class="w-80 border-l border-gray-200 overflow-y-auto p-6 bg-gray-50">
                  <h3 class="text-lg font-semibold mb-4">实时预览</h3>
                  <div class="space-y-4">
                    <div class="bg-white rounded-lg p-4 shadow-sm">
                      <h4 class="font-medium text-gray-900 mb-2">{{ formData.name || '未命名组件' }}</h4>
                      <p class="text-sm text-gray-600 mb-2">{{ formData.code || '未设置编码' }}</p>
                      <p class="text-sm text-gray-500">{{ formData.description || '暂无描述' }}</p>
                      <div class="flex items-center gap-2 mt-3">
                        <Badge v-if="formData.componentType" variant="secondary">
                          {{ getTypeLabel(formData.componentType) }}
                        </Badge>
                        <Badge v-if="formData.status" variant="outline">
                          {{ getStatusLabel(formData.status) }}
                        </Badge>
                      </div>
                    </div>

                    <div v-if="formData.tags && formData.tags.length > 0" class="bg-white rounded-lg p-4 shadow-sm">
                      <h5 class="font-medium text-gray-900 mb-2">标签</h5>
                      <div class="flex flex-wrap gap-1">
                        <Badge v-for="tag in formData.tags" :key="tag" variant="outline" class="text-xs">
                          {{ tag }}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <!-- 参数定义 -->
            <TabsContent value="parameters" class="flex-1 overflow-hidden">
              <div class="h-full overflow-y-auto p-6">
                <ComponentParametersEditor
                  v-model="formData.parameters"
                  :errors="validationErrors.parameters"
                  @validate="validateParameters"
                />
              </div>
            </TabsContent>

            <!-- 约束条件 -->
            <TabsContent value="constraints" class="flex-1 overflow-hidden">
              <div class="h-full overflow-y-auto p-6">
                <ComponentConstraintsEditor
                  v-model="formData.constraints"
                  :parameters="formData.parameters"
                  :errors="validationErrors.constraints"
                  @validate="validateConstraints"
                />
              </div>
            </TabsContent>

            <!-- 工艺要求 -->
            <TabsContent value="process" class="flex-1 overflow-hidden">
              <div class="h-full overflow-y-auto p-6">
                <ComponentProcessEditor
                  v-model="formData.processRequirements"
                  :errors="validationErrors.processRequirements"
                  @validate="validateProcessRequirements"
                />
              </div>
            </TabsContent>

            <!-- 预览 -->
            <TabsContent value="preview" class="flex-1 overflow-hidden">
              <div class="h-full overflow-y-auto p-6">
                <ComponentPreview
                  :component="formData"
                  :validation-result="validationResult"
                />
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      <DialogFooter class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <Badge v-if="hasValidationErrors" variant="destructive">
            {{ totalErrors }} 个错误
          </Badge>
          <Badge v-if="hasValidationWarnings" variant="secondary">
            {{ totalWarnings }} 个警告
          </Badge>
          <Button
            v-if="isEditing"
            @click="validateComponent"
            variant="outline"
            size="sm"
          >
            <CheckCircle class="w-4 h-4 mr-1" />
            验证
          </Button>
        </div>
        
        <div class="flex items-center gap-2">
          <Button @click="$emit('update:open', false)" variant="outline">
            取消
          </Button>
          <Button @click="saveAsDraft" variant="outline" :disabled="saving">
            保存草稿
          </Button>
          <Button 
            @click="saveComponent" 
            :disabled="hasValidationErrors || saving"
            class="bg-blue-600 hover:bg-blue-700"
          >
            <Save class="w-4 h-4 mr-2" />
            {{ saving ? '保存中...' : (isEditing ? '更新组件' : '创建组件') }}
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { Save, CheckCircle, Package, Settings, Shield, Wrench, Eye } from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import ComponentBasicForm from './ComponentBasicForm.vue';
import ComponentParametersEditor from './ComponentParametersEditor.vue';
import ComponentConstraintsEditor from './ComponentConstraintsEditor.vue';
import ComponentProcessEditor from './ComponentProcessEditor.vue';
import ComponentPreview from './ComponentPreview.vue';

import { parameterConstraintService } from '@/services/parameterConstraintService';
import type { Component } from '@/types/product-structure';

// Props
interface Props {
  open: boolean;
  component?: Component | null;
}

// Emits
interface Emits {
  (e: 'update:open', open: boolean): void;
  (e: 'save', component: Component): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const activeTab = ref('basic');
const saving = ref(false);

const formData = ref<Partial<Component>>({
  name: '',
  code: '',
  description: '',
  componentType: 'other',
  materialCategoryId: '',
  materialCategoryName: '',
  materialCategoryCode: '',
  parameters: [],
  constraints: [],
  processRequirements: [],
  quantityFormula: '1',
  costFormula: '',
  properties: {},
  tags: [],
  reusable: true,
  status: 'draft'
});

const validationErrors = ref<any>({
  basic: {},
  parameters: [],
  constraints: [],
  processRequirements: []
});

const validationResult = ref<any>(null);

// 计算属性
const isEditing = computed(() => !!props.component?.id);

const hasValidationErrors = computed(() => {
  return totalErrors.value > 0;
});

const hasValidationWarnings = computed(() => {
  return totalWarnings.value > 0;
});

const totalErrors = computed(() => {
  let count = 0;
  
  // 基本信息错误
  count += Object.keys(validationErrors.value.basic).length;
  
  // 参数错误
  if (Array.isArray(validationErrors.value.parameters)) {
    count += validationErrors.value.parameters.length;
  }
  
  // 约束错误
  if (Array.isArray(validationErrors.value.constraints)) {
    count += validationErrors.value.constraints.length;
  }
  
  // 工艺要求错误
  if (Array.isArray(validationErrors.value.processRequirements)) {
    count += validationErrors.value.processRequirements.length;
  }
  
  return count;
});

const totalWarnings = computed(() => {
  // 这里可以添加警告计数逻辑
  return 0;
});

// 方法
const initializeForm = () => {
  // 创建新的表单数据对象
  const newFormData = props.component ? { ...props.component } : {
    name: '',
    code: '',
    description: '',
    componentType: 'other' as const,
    materialCategoryId: '',
    materialCategoryName: '',
    materialCategoryCode: '',
    parameters: [],
    constraints: [],
    processRequirements: [],
    quantityFormula: '1',
    costFormula: '',
    properties: {},
    tags: [],
    reusable: true,
    status: 'draft' as const
  };

  // 一次性更新表单数据
  formData.value = newFormData;

  // 重置验证错误
  validationErrors.value = {
    basic: {},
    parameters: [],
    constraints: [],
    processRequirements: []
  };

  // 重置验证结果
  validationResult.value = null;
};

const validateBasicInfo = (errors: any) => {
  validationErrors.value.basic = errors;
};

const validateParameters = (errors: any[]) => {
  validationErrors.value.parameters = errors;
};

const validateConstraints = (errors: any[]) => {
  validationErrors.value.constraints = errors;
};

const validateProcessRequirements = (errors: any[]) => {
  validationErrors.value.processRequirements = errors;
};

const validateComponent = async () => {
  if (!formData.value.parameters || !formData.value.constraints) {
    return;
  }

  try {
    // 使用参数约束服务验证组件
    const testValues: Record<string, any> = {};
    formData.value.parameters.forEach(param => {
      if (param.defaultValue !== undefined) {
        testValues[param.name] = param.defaultValue;
      }
    });

    const result = await parameterConstraintService.validateComponent(
      formData.value as Component,
      testValues
    );

    validationResult.value = result;
    
    // 更新验证错误
    if (!result.isValid) {
      // 将验证结果转换为表单错误格式
      const parameterErrors = result.parameterValidation.errors.map(error => ({
        field: error.location?.fieldName,
        message: error.message
      }));
      
      const constraintErrors = result.constraintValidation.errors.map(error => ({
        field: error.location?.objectId,
        message: error.message
      }));
      
      validationErrors.value.parameters = parameterErrors;
      validationErrors.value.constraints = constraintErrors;
    }
  } catch (error) {
    console.error('组件验证失败:', error);
  }
};

const saveAsDraft = async () => {
  const draftData = {
    ...formData.value,
    status: 'draft' as const
  };
  
  await saveComponentData(draftData);
};

const saveComponent = async () => {
  await saveComponentData(formData.value);
};

const saveComponentData = async (data: Partial<Component>) => {
  saving.value = true;
  
  try {
    // 确保必要字段存在
    const componentData: Component = {
      id: data.id || '',
      code: data.code || '',
      name: data.name || '',
      description: data.description || '',
      version: data.version || 1,
      status: data.status || 'draft',
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: data.createdBy || 'current-user',
      updatedBy: 'current-user',
      componentType: data.componentType || 'other',
      materialCategoryId: data.materialCategoryId || '',
      materialCategoryName: data.materialCategoryName || '',
      materialCategoryCode: data.materialCategoryCode || '',
      parameters: data.parameters || [],
      constraints: data.constraints || [],
      processRequirements: data.processRequirements || [],
      quantityFormula: data.quantityFormula || '1',
      costFormula: data.costFormula,
      properties: data.properties || {},
      tags: data.tags || [],
      reusable: data.reusable !== undefined ? data.reusable : true
    };

    emit('save', componentData);
  } catch (error) {
    console.error('保存组件失败:', error);
  } finally {
    saving.value = false;
  }
};

// 辅助方法
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    frame: '框料',
    glass: '玻璃',
    hardware: '五金',
    seal: '密封',
    other: '其他'
  };
  return typeMap[type] || type;
};

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return statusMap[status] || status;
};

// 防止递归更新的标志
const isInitializing = ref(false);

// 监听器
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    isInitializing.value = true;
    initializeForm();
    activeTab.value = 'basic';
    // 延迟重置标志，确保初始化完成
    nextTick(() => {
      isInitializing.value = false;
    });
  }
});

watch(() => props.component, () => {
  if (props.open) {
    isInitializing.value = true;
    initializeForm();
    nextTick(() => {
      isInitializing.value = false;
    });
  }
});

// 防抖验证的定时器
let validationTimer: NodeJS.Timeout | null = null;

// 监听表单数据变化，自动验证
watch(formData, () => {
  // 如果正在初始化，跳过验证
  if (isInitializing.value || !props.open) {
    return;
  }

  // 清除之前的定时器
  if (validationTimer) {
    clearTimeout(validationTimer);
  }

  // 只有在有参数和约束时才进行验证
  if (formData.value.parameters && formData.value.constraints) {
    validationTimer = setTimeout(() => {
      validateComponent();
    }, 500);
  }
}, { deep: true });

// 生命周期
onMounted(() => {
  if (props.open) {
    initializeForm();
  }
});
</script>
