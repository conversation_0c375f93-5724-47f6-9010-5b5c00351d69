<template>
  <div class="structure-tree-view">
    <div v-if="structure" class="space-y-1">
      <!-- 根节点（产品结构） -->
      <TreeNode
        :node="rootNode"
        :level="0"
        :selected="selectedNode === structure.id"
        :expanded="expandedNodes.has(structure.id)"
        @select="$emit('node-select', structure.id)"
        @toggle="$emit('node-toggle', structure.id)"
        @add="handleAddNode"
        @delete="handleDeleteNode"
      />
      
      <!-- 根构件 -->
      <div v-if="structure.rootAssembly && expandedNodes.has(structure.id)">
        <AssemblyTreeNode
          :assembly="structure.rootAssembly"
          :level="1"
          :parent-id="structure.id"
          :selected-node="selectedNode"
          :expanded-nodes="expandedNodes"
          @node-select="$emit('node-select', $event)"
          @node-toggle="$emit('node-toggle', $event)"
          @node-add="$emit('node-add', $event.parentId, $event.type)"
          @node-delete="$emit('node-delete', $event)"
        />
      </div>
    </div>
    
    <div v-else class="text-center py-8 text-gray-500">
      <Package class="w-8 h-8 mx-auto mb-2 text-gray-400" />
      <p class="text-sm">暂无结构数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Package } from 'lucide-vue-next';
import TreeNode from './TreeNode.vue';
import AssemblyTreeNode from './AssemblyTreeNode.vue';
import type { ProductStructure } from '@/types/product-structure';

// Props
interface Props {
  structure: ProductStructure | null;
  selectedNode: string | null;
  expandedNodes: Set<string>;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'node-select': [nodeId: string];
  'node-toggle': [nodeId: string];
  'node-add': [parentId: string, type: string];
  'node-delete': [nodeId: string];
}>();

// 计算属性
const rootNode = computed(() => {
  if (!props.structure) return null;
  
  return {
    id: props.structure.id,
    name: props.structure.name,
    code: props.structure.code,
    type: 'product',
    description: props.structure.description,
    hasChildren: !!props.structure.rootAssembly
  };
});

// 方法
const handleAddNode = (parentId: string) => {
  // 根据父节点类型决定可添加的子节点类型
  if (parentId === props.structure?.id) {
    emit('node-add', parentId, 'assembly');
  }
};

const handleDeleteNode = (nodeId: string) => {
  if (nodeId !== props.structure?.id) { // 不能删除根节点
    emit('node-delete', nodeId);
  }
};
</script>

<style scoped>
.structure-tree-view {
  font-size: 14px;
}
</style>
