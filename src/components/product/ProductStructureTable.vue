<template>
  <div class="product-structure-table">
    <!-- 表格头部操作栏 -->
    <div class="flex items-center justify-between p-4 border-b">
      <div class="flex items-center gap-4">
        <h3 class="font-medium">产品结构列表</h3>
        <Badge variant="secondary">{{ structures.length }} 个结构</Badge>
      </div>
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="refreshData">
          <RefreshCw class="w-4 h-4 mr-2" />
          刷新
        </Button>
        <Button variant="outline" size="sm" @click="toggleView">
          <Grid3X3 class="w-4 h-4 mr-2" />
          {{ viewMode === 'table' ? '卡片视图' : '表格视图' }}
        </Button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center p-8">
      <div class="flex items-center gap-2">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span class="text-gray-600">加载中...</span>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-else-if="viewMode === 'table'" class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 border-b">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              基本信息
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              类型/类别
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              版本信息
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              验证状态
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="structure in paginatedStructures" :key="structure.id" class="hover:bg-gray-50">
            <!-- 基本信息 -->
            <td class="px-4 py-4">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Package class="w-5 h-5 text-blue-600" />
                  </div>
                </div>
                <div class="min-w-0 flex-1">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {{ structure.name }}
                  </p>
                  <p class="text-sm text-gray-500 truncate">
                    {{ structure.code }}
                  </p>
                  <p v-if="structure.description" class="text-xs text-gray-400 mt-1 line-clamp-2">
                    {{ structure.description }}
                  </p>
                </div>
              </div>
            </td>

            <!-- 类型/类别 -->
            <td class="px-4 py-4">
              <div class="space-y-1">
                <Badge :variant="getProductTypeVariant(structure.productType)">
                  {{ getProductTypeText(structure.productType) }}
                </Badge>
                <p class="text-sm text-gray-600">{{ structure.category }}</p>
                <p class="text-xs text-gray-400">{{ structure.subCategory }}</p>
              </div>
            </td>

            <!-- 状态 -->
            <td class="px-4 py-4">
              <div class="space-y-2">
                <Badge :variant="getStatusVariant(structure.status)">
                  {{ getStatusText(structure.status) }}
                </Badge>
                <div class="text-xs text-gray-500">
                  更新于 {{ formatDate(structure.updatedAt) }}
                </div>
              </div>
            </td>

            <!-- 版本信息 -->
            <td class="px-4 py-4">
              <div class="space-y-1">
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium">v{{ structure.version }}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    @click="$emit('view-versions', structure)"
                    class="h-6 px-2 text-xs"
                  >
                    <History class="w-3 h-3 mr-1" />
                    历史
                  </Button>
                </div>
                <p class="text-xs text-gray-500">
                  {{ structure.versionHistory?.length || 0 }} 个版本
                </p>
              </div>
            </td>

            <!-- 验证状态 -->
            <td class="px-4 py-4">
              <div class="space-y-1">
                <div v-if="structure.lastValidationResult" class="flex items-center gap-2">
                  <div
                    :class="[
                      'w-2 h-2 rounded-full',
                      getValidationStatusColor(structure.lastValidationResult)
                    ]"
                  ></div>
                  <span class="text-xs">
                    {{ getValidationStatusText(structure.lastValidationResult) }}
                  </span>
                </div>
                <div v-else class="flex items-center gap-2">
                  <div class="w-2 h-2 rounded-full bg-gray-300"></div>
                  <span class="text-xs text-gray-500">未验证</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('validate', structure)"
                  class="h-6 px-2 text-xs"
                >
                  <CheckCircle class="w-3 h-3 mr-1" />
                  验证
                </Button>
              </div>
            </td>

            <!-- 操作 -->
            <td class="px-4 py-4">
              <div class="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('view-details', structure)"
                  class="h-8 px-2"
                  title="查看详情"
                >
                  <Eye class="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('design', structure)"
                  class="h-8 px-2 text-purple-600 hover:text-purple-700"
                  title="可视化设计"
                >
                  <Palette class="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('edit', structure)"
                  class="h-8 px-2"
                  title="编辑"
                >
                  <Edit class="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('duplicate', structure)"
                  class="h-8 px-2"
                  title="复制"
                >
                  <Copy class="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="$emit('delete', structure)"
                  class="h-8 px-2 text-red-600 hover:text-red-700"
                  title="删除"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 卡片视图 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
      <Card v-for="structure in paginatedStructures" :key="structure.id" class="hover:shadow-md transition-shadow">
        <CardContent class="p-4">
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Package class="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">{{ structure.name }}</h3>
                <p class="text-sm text-gray-500">{{ structure.code }}</p>
              </div>
            </div>
            <Badge :variant="getStatusVariant(structure.status)">
              {{ getStatusText(structure.status) }}
            </Badge>
          </div>

          <div class="space-y-2 mb-4">
            <div class="flex items-center gap-2">
              <Badge :variant="getProductTypeVariant(structure.productType)" class="text-xs">
                {{ getProductTypeText(structure.productType) }}
              </Badge>
              <span class="text-sm text-gray-600">{{ structure.category }}</span>
            </div>
            
            <p v-if="structure.description" class="text-sm text-gray-600 line-clamp-2">
              {{ structure.description }}
            </p>

            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>版本 v{{ structure.version }}</span>
              <span>{{ formatDate(structure.updatedAt) }}</span>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                @click="$emit('view-details', structure)"
                class="h-8 px-2"
                title="查看详情"
              >
                <Eye class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                @click="$emit('design', structure)"
                class="h-8 px-2 text-purple-600 hover:text-purple-700"
                title="可视化设计"
              >
                <Palette class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                @click="$emit('edit', structure)"
                class="h-8 px-2"
                title="编辑"
              >
                <Edit class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                @click="$emit('duplicate', structure)"
                class="h-8 px-2"
                title="复制"
              >
                <Copy class="w-4 h-4" />
              </Button>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              @click="$emit('validate', structure)"
              class="h-8 px-3"
            >
              <CheckCircle class="w-3 h-3 mr-1" />
              验证
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 分页 -->
    <div class="flex items-center justify-between p-4 border-t">
      <div class="text-sm text-gray-500">
        显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} - 
        {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，
        共 {{ pagination.total }} 条记录
      </div>
      
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.page <= 1"
          @click="$emit('page-change', pagination.page - 1)"
        >
          上一页
        </Button>
        <span class="text-sm">{{ pagination.page }} / {{ totalPages }}</span>
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.page >= totalPages"
          @click="$emit('page-change', pagination.page + 1)"
        >
          下一页
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Package,
  Eye,
  Edit,
  Copy,
  Trash2,
  CheckCircle,
  History,
  RefreshCw,
  Grid3X3,
  Palette
} from 'lucide-vue-next';

import type { 
  ProductStructure, 
  StructureValidationResult,
  ProductType
} from '@/types/product-structure';

// Props
interface Props {
  structures: ProductStructure[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'edit': [structure: ProductStructure];
  'delete': [structure: ProductStructure];
  'view-details': [structure: ProductStructure];
  'view-versions': [structure: ProductStructure];
  'duplicate': [structure: ProductStructure];
  'validate': [structure: ProductStructure];
  'design': [structure: ProductStructure];
  'page-change': [page: number];
  'page-size-change': [pageSize: number];
}>();

// 响应式数据
const viewMode = ref<'table' | 'card'>('table');

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(props.pagination.total / props.pagination.pageSize);
});

const paginatedStructures = computed(() => {
  const start = (props.pagination.page - 1) * props.pagination.pageSize;
  const end = start + props.pagination.pageSize;
  return props.structures.slice(start, end);
});

// 工具函数
const getProductTypeText = (type: ProductType): string => {
  const typeMap = {
    partition: '隔断',
    window: '窗户',
    door: '门',
    curtain_wall: '幕墙',
    other: '其他'
  };
  return typeMap[type] || type;
};

const getProductTypeVariant = (type: ProductType) => {
  const variantMap = {
    partition: 'default',
    window: 'secondary',
    door: 'outline',
    curtain_wall: 'destructive',
    other: 'secondary'
  };
  return variantMap[type] || 'default';
};

const getStatusText = (status: string): string => {
  const statusMap = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return statusMap[status] || status;
};

const getStatusVariant = (status: string) => {
  const variantMap = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'default';
};

const getValidationStatusColor = (result: StructureValidationResult): string => {
  if (result.errors && result.errors.length > 0) return 'bg-red-500';
  if (result.warnings && result.warnings.length > 0) return 'bg-yellow-500';
  return 'bg-green-500';
};

const getValidationStatusText = (result: StructureValidationResult): string => {
  if (result.errors && result.errors.length > 0) return `${result.errors.length} 个错误`;
  if (result.warnings && result.warnings.length > 0) return `${result.warnings.length} 个警告`;
  return '验证通过';
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

// 方法
const toggleView = () => {
  viewMode.value = viewMode.value === 'table' ? 'card' : 'table';
};

const refreshData = () => {
  // 触发父组件刷新数据
  emit('page-change', props.pagination.page);
};
</script>

<style scoped>
.product-structure-table {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
