<template>
  <div class="space-y-6">
    <!-- 阶段标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">交付计划制定</h3>
        <p class="text-sm text-gray-600 mt-1">分析订单需求，制定可行的交付计划</p>
      </div>
      <Badge variant="secondary" size="lg">
        <Calendar class="h-4 w-4 mr-2" />
        计划阶段
      </Badge>
    </div>

    <!-- 进度指示器 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-blue-900">计划制定进度</span>
        <span class="text-sm text-blue-700">{{ planningProgress }}%</span>
      </div>
      <div class="w-full bg-blue-200 rounded-full h-2">
        <div 
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${planningProgress}%` }"
        ></div>
      </div>
    </div>

    <!-- 计划制定步骤 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 步骤1: 原料需求分析 -->
      <Card class="p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium text-gray-900">原料需求分析</h4>
          <div 
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
            :class="getStepStatusClass('material_analysis')"
          >
            <Check v-if="completedSteps.includes('material_analysis')" class="h-4 w-4" />
            <span v-else>1</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="text-sm text-gray-600">
            <p>分析订单所需原材料规格和数量</p>
          </div>
          
          <!-- 原料需求列表 -->
          <div class="space-y-2">
            <div 
              v-for="requirement in materialRequirements" 
              :key="requirement.materialId"
              class="p-2 bg-gray-50 rounded text-xs"
            >
              <div class="flex justify-between items-center">
                <span class="font-medium">{{ requirement.materialName }}</span>
                <Badge 
                  :variant="requirement.shortfall > 0 ? 'destructive' : 'default'" 
                  size="sm"
                >
                  {{ requirement.shortfall > 0 ? '缺料' : '充足' }}
                </Badge>
              </div>
              <div class="mt-1 text-gray-600">
                需求: {{ requirement.requiredQuantity }}片 | 
                库存: {{ requirement.availableQuantity }}片
                <span v-if="requirement.shortfall > 0" class="text-red-600">
                  | 缺口: {{ requirement.shortfall }}片
                </span>
              </div>
            </div>
          </div>
          
          <Button 
            size="sm" 
            variant="outline" 
            class="w-full"
            :disabled="completedSteps.includes('material_analysis')"
            @click="completeMaterialAnalysis"
          >
            <Package class="h-4 w-4 mr-2" />
            {{ completedSteps.includes('material_analysis') ? '分析完成' : '开始分析' }}
          </Button>
        </div>
      </Card>

      <!-- 步骤2: 产能约束评估 -->
      <Card class="p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium text-gray-900">产能约束评估</h4>
          <div 
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
            :class="getStepStatusClass('capacity_evaluation')"
          >
            <Check v-if="completedSteps.includes('capacity_evaluation')" class="h-4 w-4" />
            <span v-else>2</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="text-sm text-gray-600">
            <p>评估生产设备和人员产能约束</p>
          </div>
          
          <!-- 产能评估结果 -->
          <div class="space-y-2">
            <div 
              v-for="capacity in capacityAllocations" 
              :key="capacity.workstationId"
              class="p-2 bg-gray-50 rounded text-xs"
            >
              <div class="flex justify-between items-center">
                <span class="font-medium">{{ capacity.workstationName }}</span>
                <Badge 
                  :variant="capacity.bottleneck ? 'destructive' : 'default'" 
                  size="sm"
                >
                  {{ capacity.bottleneck ? '瓶颈' : '正常' }}
                </Badge>
              </div>
              <div class="mt-1 text-gray-600">
                利用率: {{ Math.round(capacity.utilizationRate * 100) }}% | 
                需求: {{ capacity.allocatedHours }}h | 
                可用: {{ capacity.availableHours }}h
              </div>
            </div>
          </div>
          
          <Button 
            size="sm" 
            variant="outline" 
            class="w-full"
            :disabled="completedSteps.includes('capacity_evaluation') || !completedSteps.includes('material_analysis')"
            @click="completeCapacityEvaluation"
          >
            <Settings class="h-4 w-4 mr-2" />
            {{ completedSteps.includes('capacity_evaluation') ? '评估完成' : '开始评估' }}
          </Button>
        </div>
      </Card>

      <!-- 步骤3: 交期承诺确认 -->
      <Card class="p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium text-gray-900">交期承诺确认</h4>
          <div 
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
            :class="getStepStatusClass('delivery_commitment')"
          >
            <Check v-if="completedSteps.includes('delivery_commitment')" class="h-4 w-4" />
            <span v-else>3</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="text-sm text-gray-600">
            <p>基于分析结果确认最终交期承诺</p>
          </div>
          
          <!-- 交期承诺信息 -->
          <div v-if="deliveryCommitment" class="p-3 bg-green-50 border border-green-200 rounded">
            <div class="text-sm">
              <div class="flex justify-between items-center mb-2">
                <span class="font-medium text-green-800">承诺交期</span>
                <Badge variant="outline" size="sm">
                  置信度 {{ deliveryCommitment.confidence }}%
                </Badge>
              </div>
              <div class="text-green-700">
                {{ formatDate(deliveryCommitment.commitmentDate) }}
              </div>
              <div class="text-xs text-green-600 mt-1">
                缓冲时间: {{ deliveryCommitment.bufferDays }}天
              </div>
            </div>
          </div>
          
          <Button 
            size="sm" 
            variant="outline" 
            class="w-full"
            :disabled="completedSteps.includes('delivery_commitment') || !canConfirmDelivery"
            @click="confirmDeliveryCommitment"
          >
            <Clock class="h-4 w-4 mr-2" />
            {{ completedSteps.includes('delivery_commitment') ? '承诺确认' : '确认交期' }}
          </Button>
        </div>
      </Card>
    </div>

    <!-- 风险提醒 -->
    <div v-if="planningRisks.length > 0" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <h4 class="font-medium text-yellow-800 mb-2 flex items-center">
        <AlertTriangle class="h-4 w-4 mr-2" />
        计划风险提醒
      </h4>
      <div class="space-y-2">
        <div 
          v-for="risk in planningRisks" 
          :key="risk.id"
          class="text-sm text-yellow-700"
        >
          <div class="font-medium">{{ risk.description }}</div>
          <div class="text-xs mt-1">影响: {{ risk.impact }} | 缓解措施: {{ risk.mitigation }}</div>
        </div>
      </div>
    </div>

    <!-- 完成计划制定 -->
    <div class="flex justify-end">
      <Button 
        :disabled="!isAllStepsCompleted"
        @click="completePlanning"
        class="px-6"
      >
        <ArrowRight class="h-4 w-4 mr-2" />
        完成计划制定，进入排程阶段
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  Check, 
  Package, 
  Settings, 
  Clock, 
  AlertTriangle, 
  ArrowRight 
} from 'lucide-vue-next';
import type { OrderDeliveryEntity } from '@/types/order-delivery';

interface Props {
  order: OrderDeliveryEntity;
}

interface Emits {
  (e: 'plan-completed'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const completedSteps = ref<string[]>([]);

// 模拟数据
const materialRequirements = ref([
  {
    materialId: 'glass-001',
    materialName: 'Low-E玻璃 8mm',
    requiredQuantity: 50,
    availableQuantity: 45,
    shortfall: 5,
    estimatedCost: 14000
  }
]);

const capacityAllocations = ref([
  {
    workstationId: 'cutting-001',
    workstationName: '切割工段',
    allocatedHours: 12,
    availableHours: 16,
    utilizationRate: 0.75,
    bottleneck: false
  },
  {
    workstationId: 'tempering-001',
    workstationName: '钢化工段',
    allocatedHours: 8,
    availableHours: 8,
    utilizationRate: 1.0,
    bottleneck: true
  }
]);

const deliveryCommitment = ref({
  commitmentDate: '2024-03-15',
  confidence: 85,
  bufferDays: 2,
  riskFactors: ['原料短缺', '设备瓶颈']
});

const planningRisks = ref([
  {
    id: 'risk-001',
    type: 'material',
    severity: 'medium',
    description: 'Low-E玻璃库存不足5片',
    impact: '可能延期1-2天',
    mitigation: '紧急采购或调配其他规格'
  }
]);

// 计算属性
const planningProgress = computed(() => {
  return Math.round((completedSteps.value.length / 3) * 100);
});

const canConfirmDelivery = computed(() => {
  return completedSteps.value.includes('material_analysis') && 
         completedSteps.value.includes('capacity_evaluation');
});

const isAllStepsCompleted = computed(() => {
  return completedSteps.value.length === 3;
});

// 方法
const getStepStatusClass = (step: string) => {
  if (completedSteps.value.includes(step)) {
    return 'bg-green-500 text-white';
  }
  return 'bg-gray-300 text-gray-600';
};

const completeMaterialAnalysis = () => {
  if (!completedSteps.value.includes('material_analysis')) {
    completedSteps.value.push('material_analysis');
  }
};

const completeCapacityEvaluation = () => {
  if (!completedSteps.value.includes('capacity_evaluation')) {
    completedSteps.value.push('capacity_evaluation');
  }
};

const confirmDeliveryCommitment = () => {
  if (!completedSteps.value.includes('delivery_commitment')) {
    completedSteps.value.push('delivery_commitment');
  }
};

const completePlanning = () => {
  emit('plan-completed');
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};
</script>
