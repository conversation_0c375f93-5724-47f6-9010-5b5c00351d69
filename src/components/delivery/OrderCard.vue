<template>
  <div 
    class="p-3 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
    :class="cardClasses"
    @click="$emit('click')"
  >
    <!-- 订单头部信息 -->
    <div class="flex items-start justify-between mb-2">
      <div class="flex-1">
        <div class="flex items-center gap-2 mb-1">
          <h4 class="font-semibold text-sm text-gray-900">{{ order.orderNumber }}</h4>
          <Badge :variant="getPriorityVariant(order.customerInfo.priority)" size="sm">
            {{ getPriorityText(order.customerInfo.priority) }}
          </Badge>
        </div>
        <p class="text-xs text-gray-600">{{ order.customerInfo.name }}</p>
      </div>
      
      <!-- 状态指示器 -->
      <div class="flex flex-col items-end">
        <div class="flex items-center gap-1 mb-1">
          <div 
            class="w-2 h-2 rounded-full"
            :class="getStatusDotClass(order.currentPhase)"
          ></div>
          <span class="text-xs text-gray-500">{{ getPhaseText(order.currentPhase) }}</span>
        </div>
        <span class="text-xs text-gray-400">{{ formatDate(order.updatedAt) }}</span>
      </div>
    </div>

    <!-- 订单详情 -->
    <div class="space-y-2">
      <!-- 订单项信息 -->
      <div class="flex items-center justify-between text-xs">
        <span class="text-gray-500">订单项</span>
        <span class="font-medium">{{ order.orderItems.length }}项</span>
      </div>
      
      <!-- 总金额 -->
      <div class="flex items-center justify-between text-xs">
        <span class="text-gray-500">订单金额</span>
        <span class="font-medium text-green-600">¥{{ formatCurrency(order.totalValue) }}</span>
      </div>
      
      <!-- 交付日期 -->
      <div class="flex items-center justify-between text-xs">
        <span class="text-gray-500">交付日期</span>
        <span 
          class="font-medium"
          :class="getDeliveryDateClass(order.orderItems[0]?.deliveryDate)"
        >
          {{ formatDate(order.orderItems[0]?.deliveryDate) }}
        </span>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="mt-3">
      <div class="flex items-center justify-between mb-1">
        <span class="text-xs text-gray-500">完成进度</span>
        <span class="text-xs font-medium">{{ getProgressPercentage(order) }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-1.5">
        <div 
          class="h-1.5 rounded-full transition-all duration-300"
          :class="getProgressBarClass(order.currentPhase)"
          :style="{ width: `${getProgressPercentage(order)}%` }"
        ></div>
      </div>
    </div>

    <!-- 当前任务 -->
    <div v-if="getCurrentTask(order)" class="mt-2 p-2 bg-blue-50 rounded text-xs">
      <div class="flex items-center gap-1 text-blue-700">
        <Clock class="h-3 w-3" />
        <span class="font-medium">当前任务</span>
      </div>
      <p class="text-blue-600 mt-1">{{ getCurrentTask(order) }}</p>
    </div>

    <!-- 风险提醒 -->
    <div v-if="hasRisk(order)" class="mt-2 p-2 bg-red-50 rounded text-xs">
      <div class="flex items-center gap-1 text-red-700">
        <AlertTriangle class="h-3 w-3" />
        <span class="font-medium">风险提醒</span>
      </div>
      <p class="text-red-600 mt-1">{{ getRiskMessage(order) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle } from 'lucide-vue-next';
import type { OrderDeliveryEntity, DeliveryPhase } from '@/types/order-delivery';

interface Props {
  order: OrderDeliveryEntity;
  isSelected: boolean;
}

interface Emits {
  (e: 'click'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性
const cardClasses = computed(() => {
  const baseClasses = 'border-gray-200';
  const selectedClasses = 'border-blue-500 bg-blue-50 shadow-md';
  const priorityClasses = getPriorityCardClass(props.order.customerInfo.priority);
  
  return [
    baseClasses,
    props.isSelected ? selectedClasses : '',
    priorityClasses
  ].filter(Boolean).join(' ');
});

// 辅助函数
const getPriorityVariant = (priority: string) => {
  const variants = {
    urgent: 'destructive' as const,
    high: 'default' as const,
    normal: 'secondary' as const,
    low: 'outline' as const
  };
  return variants[priority as keyof typeof variants] || 'secondary';
};

const getPriorityText = (priority: string) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  };
  return texts[priority as keyof typeof texts] || priority;
};

const getPriorityCardClass = (priority: string) => {
  if (priority === 'urgent') return 'border-l-4 border-l-red-500';
  if (priority === 'high') return 'border-l-4 border-l-orange-500';
  return '';
};

const getPhaseText = (phase: DeliveryPhase) => {
  const texts = {
    planning: '计划制定',
    scheduling: '生产排程',
    executing: '执行监控',
    delivered: '已交付'
  };
  return texts[phase] || phase;
};

const getStatusDotClass = (phase: DeliveryPhase) => {
  const classes = {
    planning: 'bg-orange-500',
    scheduling: 'bg-purple-500',
    executing: 'bg-green-500',
    delivered: 'bg-gray-500'
  };
  return classes[phase] || 'bg-gray-400';
};

const getProgressBarClass = (phase: DeliveryPhase) => {
  const classes = {
    planning: 'bg-orange-500',
    scheduling: 'bg-purple-500',
    executing: 'bg-green-500',
    delivered: 'bg-gray-500'
  };
  return classes[phase] || 'bg-gray-400';
};

const getProgressPercentage = (order: OrderDeliveryEntity) => {
  const phaseProgress = {
    planning: 25,
    scheduling: 50,
    executing: 75,
    delivered: 100
  };
  return phaseProgress[order.currentPhase] || 0;
};

const getDeliveryDateClass = (deliveryDate: string) => {
  if (!deliveryDate) return '';
  
  const today = new Date();
  const delivery = new Date(deliveryDate);
  const diffDays = Math.ceil((delivery.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) return 'text-red-600'; // 已逾期
  if (diffDays <= 3) return 'text-orange-600'; // 即将到期
  return 'text-gray-900'; // 正常
};

const getCurrentTask = (order: OrderDeliveryEntity) => {
  const currentEvent = order.timeline.events.find(event => 
    event.id === order.timeline.currentEvent
  );
  return currentEvent?.description || null;
};

const hasRisk = (order: OrderDeliveryEntity) => {
  // 检查是否有延期风险
  const deliveryDate = order.orderItems[0]?.deliveryDate;
  if (!deliveryDate) return false;
  
  const today = new Date();
  const delivery = new Date(deliveryDate);
  const diffDays = Math.ceil((delivery.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  return diffDays <= 3 && order.currentPhase !== 'delivered';
};

const getRiskMessage = (order: OrderDeliveryEntity) => {
  const deliveryDate = order.orderItems[0]?.deliveryDate;
  if (!deliveryDate) return '';
  
  const today = new Date();
  const delivery = new Date(deliveryDate);
  const diffDays = Math.ceil((delivery.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) return '交付日期已逾期';
  if (diffDays <= 3) return `距离交付仅剩${diffDays}天`;
  return '';
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  });
};

const formatCurrency = (amount: number) => {
  return amount.toLocaleString('zh-CN');
};
</script>
