<template>
  <div class="space-y-6">
    <!-- 阶段标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">生产排程优化</h3>
        <p class="text-sm text-gray-600 mt-1">优化切割方案，安排生产资源，制定执行时间表</p>
      </div>
      <Badge variant="secondary" size="lg">
        <Settings class="h-4 w-4 mr-2" />
        排程阶段
      </Badge>
    </div>

    <!-- 进度指示器 -->
    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-purple-900">排程优化进度</span>
        <span class="text-sm text-purple-700">{{ schedulingProgress }}%</span>
      </div>
      <div class="w-full bg-purple-200 rounded-full h-2">
        <div 
          class="bg-purple-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${schedulingProgress}%` }"
        ></div>
      </div>
    </div>

    <!-- 排程优化步骤 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 步骤1: 切割方案优化 -->
      <Card class="p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium text-gray-900">切割方案优化</h4>
          <div 
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
            :class="getStepStatusClass('cutting_optimization')"
          >
            <Check v-if="completedSteps.includes('cutting_optimization')" class="h-4 w-4" />
            <span v-else>1</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="text-sm text-gray-600">
            <p>智能算法优化原片切割方案</p>
          </div>
          
          <!-- 切割优化结果 -->
          <div v-if="cuttingPlan" class="space-y-2">
            <div class="p-2 bg-gray-50 rounded text-xs">
              <div class="flex justify-between items-center mb-1">
                <span class="font-medium">原片利用率</span>
                <Badge variant="default" size="sm">
                  {{ Math.round(cuttingPlan.utilizationRate * 100) }}%
                </Badge>
              </div>
              <div class="text-gray-600">
                原片数量: {{ cuttingPlan.rawMaterialSheets.length }}张 | 
                浪费率: {{ Math.round(cuttingPlan.wastePercentage * 100) }}%
              </div>
            </div>
            
            <div class="p-2 bg-gray-50 rounded text-xs">
              <div class="font-medium mb-1">预估切割时间</div>
              <div class="text-gray-600">{{ Math.round(cuttingPlan.estimatedTime / 60) }}小时</div>
            </div>
          </div>
          
          <Button 
            size="sm" 
            variant="outline" 
            class="w-full"
            :disabled="completedSteps.includes('cutting_optimization')"
            @click="completeCuttingOptimization"
          >
            <Scissors class="h-4 w-4 mr-2" />
            {{ completedSteps.includes('cutting_optimization') ? '优化完成' : '开始优化' }}
          </Button>
        </div>
      </Card>

      <!-- 步骤2: 资源分配调度 -->
      <Card class="p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium text-gray-900">资源分配调度</h4>
          <div 
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
            :class="getStepStatusClass('resource_allocation')"
          >
            <Check v-if="completedSteps.includes('resource_allocation')" class="h-4 w-4" />
            <span v-else>2</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="text-sm text-gray-600">
            <p>分配设备和人员资源</p>
          </div>
          
          <!-- 资源分配结果 -->
          <div class="space-y-2">
            <div 
              v-for="resource in resourceSchedule" 
              :key="resource.resourceId"
              class="p-2 bg-gray-50 rounded text-xs"
            >
              <div class="flex justify-between items-center">
                <span class="font-medium">{{ resource.resourceName }}</span>
                <Badge 
                  :variant="resource.utilization > 0.9 ? 'destructive' : 'default'" 
                  size="sm"
                >
                  {{ Math.round(resource.utilization * 100) }}%
                </Badge>
              </div>
              <div class="mt-1 text-gray-600">
                任务数: {{ resource.scheduledTasks.length }}个 | 
                类型: {{ getResourceTypeText(resource.resourceType) }}
              </div>
            </div>
          </div>
          
          <Button 
            size="sm" 
            variant="outline" 
            class="w-full"
            :disabled="completedSteps.includes('resource_allocation') || !completedSteps.includes('cutting_optimization')"
            @click="completeResourceAllocation"
          >
            <Users class="h-4 w-4 mr-2" />
            {{ completedSteps.includes('resource_allocation') ? '分配完成' : '开始分配' }}
          </Button>
        </div>
      </Card>

      <!-- 步骤3: 时间表确认 -->
      <Card class="p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium text-gray-900">时间表确认</h4>
          <div 
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
            :class="getStepStatusClass('timeline_confirmation')"
          >
            <Check v-if="completedSteps.includes('timeline_confirmation')" class="h-4 w-4" />
            <span v-else>3</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="text-sm text-gray-600">
            <p>确认生产执行时间表</p>
          </div>
          
          <!-- 时间表信息 -->
          <div v-if="scheduleTimeline" class="p-3 bg-blue-50 border border-blue-200 rounded">
            <div class="text-sm">
              <div class="flex justify-between items-center mb-2">
                <span class="font-medium text-blue-800">执行时间表</span>
                <Badge variant="outline" size="sm">
                  {{ Math.round(scheduleTimeline.totalDuration / 60) }}小时
                </Badge>
              </div>
              <div class="text-blue-700 space-y-1">
                <div>开始: {{ formatDateTime(scheduleTimeline.startDate) }}</div>
                <div>结束: {{ formatDateTime(scheduleTimeline.endDate) }}</div>
              </div>
              <div class="text-xs text-blue-600 mt-2">
                里程碑: {{ scheduleTimeline.milestones.length }}个 | 
                关键路径: {{ scheduleTimeline.criticalPath.length }}个任务
              </div>
            </div>
          </div>
          
          <Button 
            size="sm" 
            variant="outline" 
            class="w-full"
            :disabled="completedSteps.includes('timeline_confirmation') || !canConfirmTimeline"
            @click="confirmTimeline"
          >
            <Calendar class="h-4 w-4 mr-2" />
            {{ completedSteps.includes('timeline_confirmation') ? '时间表确认' : '确认时间表' }}
          </Button>
        </div>
      </Card>
    </div>

    <!-- 优化指标 -->
    <div v-if="optimizationMetrics" class="bg-green-50 border border-green-200 rounded-lg p-4">
      <h4 class="font-medium text-green-800 mb-3 flex items-center">
        <TrendingUp class="h-4 w-4 mr-2" />
        优化指标
      </h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="text-center">
          <div class="text-2xl font-bold text-green-700">{{ Math.round(optimizationMetrics.materialUtilization * 100) }}%</div>
          <div class="text-xs text-green-600">材料利用率</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-700">{{ Math.round(optimizationMetrics.equipmentUtilization * 100) }}%</div>
          <div class="text-xs text-green-600">设备利用率</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-700">{{ Math.round(optimizationMetrics.onTimeDeliveryProbability * 100) }}%</div>
          <div class="text-xs text-green-600">按时交付概率</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-700">¥{{ Math.round(optimizationMetrics.totalCost) }}</div>
          <div class="text-xs text-green-600">总成本</div>
        </div>
      </div>
    </div>

    <!-- 完成排程优化 -->
    <div class="flex justify-end">
      <Button 
        :disabled="!isAllStepsCompleted"
        @click="completeScheduling"
        class="px-6"
      >
        <ArrowRight class="h-4 w-4 mr-2" />
        完成排程优化，开始生产执行
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Check, 
  Scissors, 
  Users, 
  Calendar, 
  TrendingUp, 
  ArrowRight 
} from 'lucide-vue-next';
import type { OrderDeliveryEntity } from '@/types/order-delivery';

interface Props {
  order: OrderDeliveryEntity;
}

interface Emits {
  (e: 'scheduling-completed'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const completedSteps = ref<string[]>([]);

// 模拟数据
const cuttingPlan = ref({
  utilizationRate: 0.87,
  wastePercentage: 0.13,
  rawMaterialSheets: [
    { id: 'sheet-001', dimensions: { length: 3300, width: 2140, thickness: 8 } },
    { id: 'sheet-002', dimensions: { length: 3300, width: 2140, thickness: 8 } }
  ],
  estimatedTime: 720 // 分钟
});

const resourceSchedule = ref([
  {
    resourceId: 'cutting-001',
    resourceName: '切割机1号',
    resourceType: 'equipment',
    scheduledTasks: [
      { id: 'task-001', taskName: '切割任务1', duration: 120 }
    ],
    utilization: 0.75
  },
  {
    resourceId: 'operator-001',
    resourceName: '张师傅',
    resourceType: 'operator',
    scheduledTasks: [
      { id: 'task-002', taskName: '操作任务1', duration: 480 }
    ],
    utilization: 0.85
  }
]);

const scheduleTimeline = ref({
  startDate: '2024-02-22T08:00:00',
  endDate: '2024-02-24T17:00:00',
  totalDuration: 1440, // 分钟
  milestones: [
    { id: 'milestone-001', name: '切割完成', date: '2024-02-22T17:00:00' },
    { id: 'milestone-002', name: '磨边完成', date: '2024-02-23T12:00:00' }
  ],
  criticalPath: ['task-001', 'task-002', 'task-003']
});

const optimizationMetrics = ref({
  materialUtilization: 0.87,
  equipmentUtilization: 0.82,
  onTimeDeliveryProbability: 0.92,
  totalCost: 12500,
  totalDuration: 1440
});

// 计算属性
const schedulingProgress = computed(() => {
  return Math.round((completedSteps.value.length / 3) * 100);
});

const canConfirmTimeline = computed(() => {
  return completedSteps.value.includes('cutting_optimization') && 
         completedSteps.value.includes('resource_allocation');
});

const isAllStepsCompleted = computed(() => {
  return completedSteps.value.length === 3;
});

// 方法
const getStepStatusClass = (step: string) => {
  if (completedSteps.value.includes(step)) {
    return 'bg-green-500 text-white';
  }
  return 'bg-gray-300 text-gray-600';
};

const getResourceTypeText = (type: string) => {
  const types = {
    equipment: '设备',
    workstation: '工作站',
    operator: '操作员'
  };
  return types[type as keyof typeof types] || type;
};

const completeCuttingOptimization = () => {
  if (!completedSteps.value.includes('cutting_optimization')) {
    completedSteps.value.push('cutting_optimization');
  }
};

const completeResourceAllocation = () => {
  if (!completedSteps.value.includes('resource_allocation')) {
    completedSteps.value.push('resource_allocation');
  }
};

const confirmTimeline = () => {
  if (!completedSteps.value.includes('timeline_confirmation')) {
    completedSteps.value.push('timeline_confirmation');
  }
};

const completeScheduling = () => {
  emit('scheduling-completed');
};

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>
