<template>
  <div class="flex flex-col h-full">
    <!-- 面板标题 -->
    <div class="p-4 border-b">
      <h3 class="font-semibold text-gray-900">生产工单列表</h3>
      <p class="text-sm text-gray-500 mt-1">{{ orders.length }} 个工单</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="p-4 border-b space-y-3">
      <!-- 搜索框 -->
      <div class="relative">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          :value="searchQuery"
          @input="handleSearchInput"
          placeholder="搜索工单号、客户名称..."
          class="pl-10"
        />
      </div>

      <!-- 筛选器 -->
      <div class="grid grid-cols-1 gap-2">
        <Select :value="statusFilter" @update:value="handleStatusFilterChange">
          <SelectTrigger>
            <SelectValue placeholder="订单状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="order_confirmed">工单确认</SelectItem>
            <SelectItem value="planning">计划制定</SelectItem>
            <SelectItem value="scheduled">已排程</SelectItem>
            <SelectItem value="executing">执行中</SelectItem>
            <SelectItem value="delivered">已交付</SelectItem>
          </SelectContent>
        </Select>

        <Select :value="phaseFilter" @update:value="handlePhaseFilterChange">
          <SelectTrigger>
            <SelectValue placeholder="交付阶段" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部阶段</SelectItem>
            <SelectItem value="planning">交付计划</SelectItem>
            <SelectItem value="scheduling">生产排程</SelectItem>
            <SelectItem value="executing">执行监控</SelectItem>
            <SelectItem value="delivered">交付确认</SelectItem>
          </SelectContent>
        </Select>

        <Select :value="priorityFilter" @update:value="handlePriorityFilterChange">
          <SelectTrigger>
            <SelectValue placeholder="优先级" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部优先级</SelectItem>
            <SelectItem value="urgent">紧急</SelectItem>
            <SelectItem value="high">高</SelectItem>
            <SelectItem value="normal">普通</SelectItem>
            <SelectItem value="low">低</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 清除筛选 -->
      <Button 
        v-if="hasActiveFilters" 
        @click="clearFilters" 
        variant="ghost" 
        size="sm" 
        class="w-full"
      >
        <X class="h-4 w-4 mr-2" />
        清除筛选
      </Button>
    </div>

    <!-- 订单列表 -->
    <div class="flex-1 overflow-y-auto">
      <div v-if="loading" class="p-4 text-center text-gray-500">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        加载中...
      </div>

      <div v-else-if="orders.length === 0" class="p-4 text-center text-gray-500">
        <Package class="h-12 w-12 mx-auto mb-2 text-gray-300" />
        <p>暂无工单</p>
      </div>

      <div v-else class="space-y-2 p-2">
        <OrderCard
          v-for="order in orders"
          :key="order.id"
          :order="order"
          :is-selected="currentOrder?.id === order.id"
          @click="$emit('order-selected', order.id)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, X, Package } from 'lucide-vue-next';
import type { OrderDeliveryEntity } from '@/types/order-delivery';
import OrderCard from './OrderCard.vue';

interface Props {
  orders: OrderDeliveryEntity[];
  currentOrder: OrderDeliveryEntity | null;
  loading: boolean;
}

interface Emits {
  (e: 'order-selected', orderId: string): void;
  (e: 'search-changed', query: string): void;
  (e: 'filters-changed', filters: {
    status?: string;
    phase?: string;
    priority?: string;
  }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 本地状态
const searchQuery = ref('');
const statusFilter = ref('all');
const phaseFilter = ref('all');
const priorityFilter = ref('all');

// 计算属性
const hasActiveFilters = computed(() => {
  return searchQuery.value !== '' || 
         statusFilter.value !== 'all' || 
         phaseFilter.value !== 'all' || 
         priorityFilter.value !== 'all';
});

// 事件处理
const handleSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  searchQuery.value = target.value;
  emit('search-changed', target.value);
};

const handleStatusFilterChange = (value: string) => {
  statusFilter.value = value;
  emit('filters-changed', { status: value });
};

const handlePhaseFilterChange = (value: string) => {
  phaseFilter.value = value;
  emit('filters-changed', { phase: value });
};

const handlePriorityFilterChange = (value: string) => {
  priorityFilter.value = value;
  emit('filters-changed', { priority: value });
};

const clearFilters = () => {
  searchQuery.value = '';
  statusFilter.value = 'all';
  phaseFilter.value = 'all';
  priorityFilter.value = 'all';
  
  emit('search-changed', '');
  emit('filters-changed', {
    status: 'all',
    phase: 'all',
    priority: 'all'
  });
};
</script>
