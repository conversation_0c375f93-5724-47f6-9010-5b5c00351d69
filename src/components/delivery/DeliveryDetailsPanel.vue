<template>
  <div class="flex flex-col h-full">
    <!-- 面板标题 -->
    <div class="p-4 border-b">
      <h3 class="font-semibold text-gray-900">工单详情</h3>
      <p v-if="currentOrder" class="text-sm text-gray-500 mt-1">
        {{ currentOrder.orderNumber }}
      </p>
    </div>

    <div v-if="!currentOrder" class="flex-1 flex items-center justify-center">
      <div class="text-center text-gray-500">
        <FileText class="h-12 w-12 mx-auto mb-2 text-gray-300" />
        <p>选择工单查看详情</p>
      </div>
    </div>

    <div v-else class="flex-1 overflow-y-auto">
      <!-- 订单基本信息 -->
      <div class="p-4 border-b">
        <h4 class="font-medium text-gray-900 mb-3">基本信息</h4>
        <div class="space-y-2 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-500">客户名称</span>
            <span class="font-medium">{{ currentOrder.customerInfo.name }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">联系人</span>
            <span>{{ currentOrder.customerInfo.contact }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">联系电话</span>
            <span>{{ currentOrder.customerInfo.phone }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">工单金额</span>
            <span class="font-medium text-green-600">¥{{ formatCurrency(currentOrder.totalValue) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">预估利润</span>
            <span class="font-medium text-blue-600">¥{{ formatCurrency(currentOrder.estimatedProfit) }}</span>
          </div>
        </div>
      </div>

      <!-- 订单项详情 -->
      <div class="p-4 border-b">
        <h4 class="font-medium text-gray-900 mb-3">工单项详情</h4>
        <div class="space-y-3">
          <div 
            v-for="item in currentOrder.orderItems" 
            :key="item.id"
            class="p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex justify-between items-start mb-2">
              <span class="text-sm font-medium">规格 {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm</span>
              <Badge variant="outline" size="sm">{{ item.quantity }}片</Badge>
            </div>
            <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div>玻璃类型: {{ getGlassTypeText(item.specifications.glassType) }}</div>
              <div>边部处理: {{ getEdgeWorkText(item.specifications.edgeWork) }}</div>
              <div>单价: ¥{{ item.unitPrice }}</div>
              <div>小计: ¥{{ formatCurrency(item.totalAmount) }}</div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              交付日期: {{ formatDate(item.deliveryDate) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 时间线 -->
      <div class="p-4 border-b">
        <h4 class="font-medium text-gray-900 mb-3">执行时间线</h4>
        <div class="space-y-3">
          <div 
            v-for="event in currentOrder.timeline.events" 
            :key="event.id"
            class="flex gap-3 cursor-pointer hover:bg-gray-50 p-2 rounded"
            @click="$emit('timeline-event-clicked', event.id)"
          >
            <div class="flex flex-col items-center">
              <div 
                class="w-3 h-3 rounded-full border-2"
                :class="getTimelineEventClass(event.status)"
              ></div>
              <div 
                v-if="event.id !== currentOrder.timeline.events[currentOrder.timeline.events.length - 1].id"
                class="w-0.5 h-8 bg-gray-300 mt-1"
              ></div>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium">{{ event.name }}</span>
                <Badge 
                  :variant="getEventStatusVariant(event.status)" 
                  size="sm"
                >
                  {{ getEventStatusText(event.status) }}
                </Badge>
              </div>
              <p class="text-xs text-gray-600 mt-1">{{ event.description }}</p>
              <div class="flex items-center gap-4 mt-1 text-xs text-gray-500">
                <span>负责: {{ event.responsible }}</span>
                <span>
                  {{ event.actualTime ? formatDateTime(event.actualTime) : formatDateTime(event.scheduledTime) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 质量记录 -->
      <div v-if="currentOrder.qualityRecords.length > 0" class="p-4 border-b">
        <h4 class="font-medium text-gray-900 mb-3">质量记录</h4>
        <div class="space-y-2">
          <div 
            v-for="record in currentOrder.qualityRecords" 
            :key="record.id"
            class="p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex justify-between items-start mb-2">
              <span class="text-sm font-medium">{{ record.checkpointName }}</span>
              <Badge 
                :variant="record.result === 'pass' ? 'default' : 'destructive'" 
                size="sm"
              >
                {{ record.result === 'pass' ? '合格' : record.result === 'fail' ? '不合格' : '返工' }}
              </Badge>
            </div>
            <div class="text-xs text-gray-600">
              <div>检验员: {{ record.inspector }}</div>
              <div>检验时间: {{ formatDateTime(record.checkTime) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="p-4">
        <h4 class="font-medium text-gray-900 mb-3">快捷操作</h4>
        <div class="space-y-2">
          <Button variant="outline" size="sm" class="w-full justify-start">
            <Phone class="h-4 w-4 mr-2" />
            联系客户
          </Button>
          <Button variant="outline" size="sm" class="w-full justify-start">
            <FileText class="h-4 w-4 mr-2" />
            查看合同
          </Button>
          <Button variant="outline" size="sm" class="w-full justify-start">
            <Truck class="h-4 w-4 mr-2" />
            安排物流
          </Button>
          <Button variant="outline" size="sm" class="w-full justify-start">
            <MessageSquare class="h-4 w-4 mr-2" />
            添加备注
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Phone, 
  Truck, 
  MessageSquare
} from 'lucide-vue-next';
import type { OrderDeliveryEntity } from '@/types/order-delivery';

interface Props {
  currentOrder: OrderDeliveryEntity | null;
}

interface Emits {
  (e: 'timeline-event-clicked', eventId: string): void;
  (e: 'resource-details', resourceId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 辅助函数
const formatCurrency = (amount: number) => {
  return amount.toLocaleString('zh-CN');
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

const formatDateTime = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getGlassTypeText = (glassType: string) => {
  const types = {
    clear: '透明玻璃',
    tinted: '有色玻璃',
    low_e: 'Low-E玻璃',
    reflective: '反射玻璃'
  };
  return types[glassType as keyof typeof types] || glassType;
};

const getEdgeWorkText = (edgeWork?: string) => {
  if (!edgeWork) return '无';
  const types = {
    polished: '抛光',
    ground: '磨边',
    beveled: '斜边'
  };
  return types[edgeWork as keyof typeof types] || edgeWork;
};

const getTimelineEventClass = (status: string) => {
  const classes = {
    completed: 'bg-green-500 border-green-500',
    in_progress: 'bg-blue-500 border-blue-500',
    pending: 'bg-white border-gray-300',
    delayed: 'bg-red-500 border-red-500'
  };
  return classes[status as keyof typeof classes] || 'bg-gray-300 border-gray-300';
};

const getEventStatusVariant = (status: string) => {
  const variants = {
    completed: 'default' as const,
    in_progress: 'secondary' as const,
    pending: 'outline' as const,
    delayed: 'destructive' as const
  };
  return variants[status as keyof typeof variants] || 'outline';
};

const getEventStatusText = (status: string) => {
  const texts = {
    completed: '已完成',
    in_progress: '进行中',
    pending: '待开始',
    delayed: '延期'
  };
  return texts[status as keyof typeof texts] || status;
};
</script>
