<template>
  <div class="space-y-6">
    <!-- 阶段标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">执行监控</h3>
        <p class="text-sm text-gray-600 mt-1">实时监控生产进度，跟踪质量指标，处理异常情况</p>
      </div>
      <Badge variant="default" size="lg">
        <Play class="h-4 w-4 mr-2" />
        执行阶段
      </Badge>
    </div>

    <!-- 总体进度 -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-green-900">总体执行进度</span>
        <span class="text-sm text-green-700">{{ overallProgress }}%</span>
      </div>
      <div class="w-full bg-green-200 rounded-full h-3">
        <div 
          class="bg-green-600 h-3 rounded-full transition-all duration-300"
          :style="{ width: `${overallProgress}%` }"
        ></div>
      </div>
      <div class="flex justify-between text-xs text-green-600 mt-2">
        <span>开始时间: {{ formatDateTime(executionStartTime) }}</span>
        <span>预计完成: {{ formatDateTime(estimatedEndTime) }}</span>
      </div>
    </div>

    <!-- 当前执行任务 -->
    <Card class="p-4">
      <h4 class="font-medium text-gray-900 mb-3 flex items-center">
        <Clock class="h-4 w-4 mr-2" />
        当前执行任务
      </h4>
      
      <div v-if="currentTask" class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium text-blue-900">{{ currentTask.name }}</span>
          <Badge variant="secondary" size="sm">
            {{ currentTask.progress }}%
          </Badge>
        </div>
        <div class="grid grid-cols-2 gap-4 text-sm text-blue-700">
          <div>执行人员: {{ currentTask.assignee }}</div>
          <div>工作位置: {{ currentTask.location }}</div>
          <div>开始时间: {{ formatDateTime(currentTask.startTime) }}</div>
          <div>预计完成: {{ formatDateTime(currentTask.estimatedEndTime) }}</div>
        </div>
        <div class="mt-2">
          <div class="w-full bg-blue-200 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${currentTask.progress}%` }"
            ></div>
          </div>
        </div>
      </div>
      
      <div v-else class="text-center text-gray-500 py-4">
        <Play class="h-8 w-8 mx-auto mb-2 text-gray-300" />
        <p>暂无执行中的任务</p>
      </div>
    </Card>

    <!-- 阶段进度详情 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card class="p-4">
        <h4 class="font-medium text-gray-900 mb-3">各阶段进度</h4>
        <div class="space-y-3">
          <div 
            v-for="phase in phaseProgress" 
            :key="phase.phase"
            class="flex items-center justify-between"
          >
            <div class="flex items-center gap-2">
              <div 
                class="w-3 h-3 rounded-full"
                :class="getPhaseStatusColor(phase.status)"
              ></div>
              <span class="text-sm">{{ phase.phase }}</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-20 bg-gray-200 rounded-full h-2">
                <div 
                  class="h-2 rounded-full transition-all duration-300"
                  :class="getPhaseProgressColor(phase.status)"
                  :style="{ width: `${phase.progress}%` }"
                ></div>
              </div>
              <span class="text-xs text-gray-500 w-8">{{ phase.progress }}%</span>
            </div>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <h4 class="font-medium text-gray-900 mb-3">质量监控</h4>
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">合格率</span>
            <Badge variant="default" size="sm">{{ qualityMetrics.passRate }}%</Badge>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">返工率</span>
            <Badge 
              :variant="qualityMetrics.reworkRate > 5 ? 'destructive' : 'secondary'" 
              size="sm"
            >
              {{ qualityMetrics.reworkRate }}%
            </Badge>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">检验点</span>
            <span class="text-sm font-medium">{{ qualityMetrics.checkpoints.completed }}/{{ qualityMetrics.checkpoints.total }}</span>
          </div>
          
          <!-- 最近质量记录 -->
          <div class="mt-3 pt-3 border-t">
            <div class="text-xs text-gray-500 mb-2">最近检验记录</div>
            <div 
              v-for="record in recentQualityRecords" 
              :key="record.id"
              class="flex items-center justify-between py-1"
            >
              <span class="text-xs">{{ record.checkpointName }}</span>
              <Badge 
                :variant="record.result === 'pass' ? 'default' : 'destructive'" 
                size="sm"
              >
                {{ record.result === 'pass' ? '合格' : '不合格' }}
              </Badge>
            </div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 已完成任务 -->
    <Card class="p-4">
      <h4 class="font-medium text-gray-900 mb-3 flex items-center">
        <CheckCircle class="h-4 w-4 mr-2" />
        已完成任务
      </h4>
      
      <div class="space-y-2 max-h-40 overflow-y-auto">
        <div 
          v-for="task in completedTasks" 
          :key="task.id"
          class="flex items-center justify-between p-2 bg-gray-50 rounded"
        >
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <CheckCircle class="h-4 w-4 text-green-500" />
              <span class="text-sm font-medium">{{ task.name }}</span>
            </div>
            <div class="text-xs text-gray-500 mt-1">
              完成人: {{ task.completedBy }} | 
              用时: {{ Math.round(task.duration / 60) }}小时 | 
              质量评分: {{ task.qualityScore }}/100
            </div>
          </div>
          <div class="text-xs text-gray-400">
            {{ formatDateTime(task.completedAt) }}
          </div>
        </div>
      </div>
    </Card>

    <!-- 执行问题 -->
    <div v-if="executionIssues.length > 0" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <h4 class="font-medium text-red-800 mb-3 flex items-center">
        <AlertTriangle class="h-4 w-4 mr-2" />
        执行问题 ({{ executionIssues.length }})
      </h4>
      <div class="space-y-3">
        <div 
          v-for="issue in executionIssues" 
          :key="issue.id"
          class="p-3 bg-white border border-red-200 rounded"
        >
          <div class="flex items-start justify-between mb-2">
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-1">
                <Badge 
                  :variant="getSeverityVariant(issue.severity)" 
                  size="sm"
                >
                  {{ getSeverityText(issue.severity) }}
                </Badge>
                <span class="text-sm font-medium">{{ getIssueTypeText(issue.type) }}</span>
              </div>
              <p class="text-sm text-gray-700">{{ issue.description }}</p>
            </div>
            <Badge 
              :variant="getIssueStatusVariant(issue.status)" 
              size="sm"
            >
              {{ getIssueStatusText(issue.status) }}
            </Badge>
          </div>
          <div class="text-xs text-gray-500">
            报告时间: {{ formatDateTime(issue.reportedAt) }} | 
            报告人: {{ issue.reportedBy }}
          </div>
          <div v-if="issue.resolution" class="mt-2 p-2 bg-green-50 rounded text-xs">
            <span class="font-medium text-green-800">解决方案: </span>
            <span class="text-green-700">{{ issue.resolution }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 完成执行 -->
    <div class="flex justify-end">
      <Button 
        :disabled="overallProgress < 100"
        @click="completeExecution"
        class="px-6"
      >
        <ArrowRight class="h-4 w-4 mr-2" />
        完成生产执行，进入交付确认
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  ArrowRight 
} from 'lucide-vue-next';
import type { OrderDeliveryEntity } from '@/types/order-delivery';

interface Props {
  order: OrderDeliveryEntity;
}

interface Emits {
  (e: 'execution-completed'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 模拟数据
const overallProgress = ref(75);
const executionStartTime = ref('2024-02-22T08:00:00');
const estimatedEndTime = ref('2024-02-24T17:00:00');

const currentTask = ref({
  id: 'task-current',
  name: '钢化处理',
  assignee: '李师傅',
  startTime: '2024-02-23T14:00:00',
  estimatedEndTime: '2024-02-23T18:00:00',
  progress: 60,
  location: '钢化车间A区'
});

const phaseProgress = ref([
  { phase: '切割工段', progress: 100, status: 'completed' },
  { phase: '磨边工段', progress: 100, status: 'completed' },
  { phase: '钢化工段', progress: 60, status: 'in_progress' },
  { phase: '质检工段', progress: 0, status: 'not_started' }
]);

const qualityMetrics = ref({
  passRate: 96,
  reworkRate: 3,
  checkpoints: { completed: 8, total: 12 }
});

const recentQualityRecords = ref([
  { id: 'qr-001', checkpointName: '切割尺寸检验', result: 'pass' },
  { id: 'qr-002', checkpointName: '磨边质量检验', result: 'pass' },
  { id: 'qr-003', checkpointName: '表面质量检验', result: 'fail' }
]);

const completedTasks = ref([
  {
    id: 'task-001',
    name: '原片切割',
    completedBy: '张师傅',
    completedAt: '2024-02-22T17:00:00',
    duration: 480,
    qualityScore: 95
  },
  {
    id: 'task-002',
    name: '边部磨削',
    completedBy: '王师傅',
    completedAt: '2024-02-23T12:00:00',
    duration: 360,
    qualityScore: 92
  }
]);

const executionIssues = ref([
  {
    id: 'issue-001',
    type: 'quality_issue',
    severity: 'medium',
    description: '发现3片玻璃表面有轻微划痕',
    reportedAt: '2024-02-23T10:30:00',
    reportedBy: '质检员小刘',
    status: 'investigating',
    resolution: null
  }
]);

// 方法
const getPhaseStatusColor = (status: string) => {
  const colors = {
    completed: 'bg-green-500',
    in_progress: 'bg-blue-500',
    not_started: 'bg-gray-300',
    blocked: 'bg-red-500'
  };
  return colors[status as keyof typeof colors] || 'bg-gray-300';
};

const getPhaseProgressColor = (status: string) => {
  const colors = {
    completed: 'bg-green-500',
    in_progress: 'bg-blue-500',
    not_started: 'bg-gray-300',
    blocked: 'bg-red-500'
  };
  return colors[status as keyof typeof colors] || 'bg-gray-300';
};

const getSeverityVariant = (severity: string) => {
  const variants = {
    low: 'secondary' as const,
    medium: 'default' as const,
    high: 'destructive' as const,
    critical: 'destructive' as const
  };
  return variants[severity as keyof typeof variants] || 'secondary';
};

const getSeverityText = (severity: string) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  };
  return texts[severity as keyof typeof texts] || severity;
};

const getIssueTypeText = (type: string) => {
  const texts = {
    delay: '延期',
    quality: '质量',
    resource: '资源',
    material: '材料'
  };
  return texts[type as keyof typeof texts] || type;
};

const getIssueStatusVariant = (status: string) => {
  const variants = {
    open: 'destructive' as const,
    investigating: 'default' as const,
    resolved: 'secondary' as const
  };
  return variants[status as keyof typeof variants] || 'secondary';
};

const getIssueStatusText = (status: string) => {
  const texts = {
    open: '待处理',
    investigating: '处理中',
    resolved: '已解决'
  };
  return texts[status as keyof typeof texts] || status;
};

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const completeExecution = () => {
  emit('execution-completed');
};
</script>
