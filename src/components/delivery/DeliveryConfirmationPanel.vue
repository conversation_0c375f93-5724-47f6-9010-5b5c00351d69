<template>
  <div class="space-y-6">
    <!-- 阶段标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">交付确认</h3>
        <p class="text-sm text-gray-600 mt-1">最终质量检验，包装发货，客户确认收货</p>
      </div>
      <Badge variant="outline" size="lg">
        <CheckCircle class="h-4 w-4 mr-2" />
        交付阶段
      </Badge>
    </div>

    <!-- 交付状态概览 -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-3">
        <h4 class="font-medium text-green-900">交付状态概览</h4>
        <Badge variant="default" size="sm">
          {{ deliveryStatus }}
        </Badge>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div class="text-2xl font-bold text-green-700">{{ deliveryMetrics.totalQuantity }}</div>
          <div class="text-xs text-green-600">总数量(片)</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-700">{{ deliveryMetrics.qualifiedQuantity }}</div>
          <div class="text-xs text-green-600">合格数量(片)</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-700">{{ deliveryMetrics.qualityRate }}%</div>
          <div class="text-xs text-green-600">合格率</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-700">{{ deliveryMetrics.onTimeDelivery ? '是' : '否' }}</div>
          <div class="text-xs text-green-600">按时交付</div>
        </div>
      </div>
    </div>

    <!-- 交付检查清单 -->
    <Card class="p-4">
      <h4 class="font-medium text-gray-900 mb-4 flex items-center">
        <ClipboardCheck class="h-4 w-4 mr-2" />
        交付检查清单
      </h4>
      
      <div class="space-y-3">
        <div 
          v-for="item in deliveryChecklist" 
          :key="item.id"
          class="flex items-center justify-between p-3 border rounded-lg"
          :class="item.completed ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'"
        >
          <div class="flex items-center gap-3">
            <div 
              class="w-5 h-5 rounded-full flex items-center justify-center"
              :class="item.completed ? 'bg-green-500' : 'bg-gray-300'"
            >
              <Check v-if="item.completed" class="h-3 w-3 text-white" />
            </div>
            <div>
              <div class="font-medium text-sm">{{ item.name }}</div>
              <div class="text-xs text-gray-600">{{ item.description }}</div>
            </div>
          </div>
          
          <div class="flex items-center gap-2">
            <span v-if="item.completedAt" class="text-xs text-gray-500">
              {{ formatDateTime(item.completedAt) }}
            </span>
            <Button 
              v-if="!item.completed"
              size="sm" 
              variant="outline"
              @click="completeChecklistItem(item.id)"
            >
              完成
            </Button>
          </div>
        </div>
      </div>
    </Card>

    <!-- 最终质量报告 -->
    <Card class="p-4">
      <h4 class="font-medium text-gray-900 mb-4 flex items-center">
        <FileText class="h-4 w-4 mr-2" />
        最终质量报告
      </h4>
      
      <div class="space-y-4">
        <!-- 质量统计 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center p-3 bg-blue-50 rounded">
            <div class="text-lg font-bold text-blue-700">{{ qualityReport.inspectedQuantity }}</div>
            <div class="text-xs text-blue-600">检验数量</div>
          </div>
          <div class="text-center p-3 bg-green-50 rounded">
            <div class="text-lg font-bold text-green-700">{{ qualityReport.passedQuantity }}</div>
            <div class="text-xs text-green-600">合格数量</div>
          </div>
          <div class="text-center p-3 bg-yellow-50 rounded">
            <div class="text-lg font-bold text-yellow-700">{{ qualityReport.reworkQuantity }}</div>
            <div class="text-xs text-yellow-600">返工数量</div>
          </div>
          <div class="text-center p-3 bg-red-50 rounded">
            <div class="text-lg font-bold text-red-700">{{ qualityReport.rejectedQuantity }}</div>
            <div class="text-xs text-red-600">报废数量</div>
          </div>
        </div>
        
        <!-- 质量问题汇总 -->
        <div v-if="qualityReport.issues.length > 0" class="border-t pt-4">
          <h5 class="font-medium text-gray-900 mb-2">质量问题汇总</h5>
          <div class="space-y-2">
            <div 
              v-for="issue in qualityReport.issues" 
              :key="issue.id"
              class="p-2 bg-yellow-50 border border-yellow-200 rounded text-sm"
            >
              <div class="flex justify-between items-start">
                <span class="font-medium">{{ issue.type }}</span>
                <Badge variant="outline" size="sm">{{ issue.quantity }}片</Badge>
              </div>
              <div class="text-xs text-gray-600 mt-1">{{ issue.description }}</div>
              <div class="text-xs text-gray-500 mt-1">处理方式: {{ issue.resolution }}</div>
            </div>
          </div>
        </div>
      </div>
    </Card>

    <!-- 包装发货信息 -->
    <Card class="p-4">
      <h4 class="font-medium text-gray-900 mb-4 flex items-center">
        <Package class="h-4 w-4 mr-2" />
        包装发货信息
      </h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-3">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">包装方式</span>
            <span class="font-medium">{{ shippingInfo.packagingMethod }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">包装数量</span>
            <span class="font-medium">{{ shippingInfo.packageCount }}箱</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">总重量</span>
            <span class="font-medium">{{ shippingInfo.totalWeight }}kg</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">发货日期</span>
            <span class="font-medium">{{ formatDate(shippingInfo.shippingDate) }}</span>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">物流公司</span>
            <span class="font-medium">{{ shippingInfo.logisticsCompany }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">运单号</span>
            <span class="font-medium">{{ shippingInfo.trackingNumber }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">预计到达</span>
            <span class="font-medium">{{ formatDate(shippingInfo.estimatedArrival) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">运费</span>
            <span class="font-medium">¥{{ shippingInfo.shippingCost }}</span>
          </div>
        </div>
      </div>
    </Card>

    <!-- 客户确认 -->
    <Card class="p-4">
      <h4 class="font-medium text-gray-900 mb-4 flex items-center">
        <User class="h-4 w-4 mr-2" />
        客户确认
      </h4>
      
      <div v-if="customerConfirmation" class="space-y-3">
        <div class="p-3 bg-green-50 border border-green-200 rounded">
          <div class="flex items-center justify-between mb-2">
            <span class="font-medium text-green-800">客户已确认收货</span>
            <Badge variant="default" size="sm">已确认</Badge>
          </div>
          <div class="text-sm text-green-700 space-y-1">
            <div>确认人: {{ customerConfirmation.confirmedBy }}</div>
            <div>确认时间: {{ formatDateTime(customerConfirmation.confirmedAt) }}</div>
            <div>满意度评分: {{ customerConfirmation.satisfactionScore }}/10</div>
          </div>
          <div v-if="customerConfirmation.feedback" class="mt-2 p-2 bg-white rounded text-sm">
            <span class="font-medium">客户反馈: </span>
            <span>{{ customerConfirmation.feedback }}</span>
          </div>
        </div>
      </div>
      
      <div v-else class="text-center py-6 text-gray-500">
        <Clock class="h-8 w-8 mx-auto mb-2 text-gray-300" />
        <p>等待客户确认收货</p>
        <Button class="mt-3" variant="outline" size="sm">
          <Phone class="h-4 w-4 mr-2" />
          联系客户确认
        </Button>
      </div>
    </Card>

    <!-- 订单完成总结 -->
    <div v-if="isOrderCompleted" class="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
      <CheckCircle class="h-12 w-12 mx-auto text-green-500 mb-4" />
      <h3 class="text-lg font-semibold text-green-900 mb-2">订单交付完成！</h3>
      <p class="text-green-700 mb-4">
        订单 {{ order.orderNumber }} 已成功完成交付，感谢您的信任！
      </p>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div class="text-lg font-bold text-green-700">{{ completionSummary.actualDuration }}天</div>
          <div class="text-xs text-green-600">实际用时</div>
        </div>
        <div>
          <div class="text-lg font-bold text-green-700">{{ completionSummary.qualityScore }}/100</div>
          <div class="text-xs text-green-600">质量评分</div>
        </div>
        <div>
          <div class="text-lg font-bold text-green-700">¥{{ completionSummary.actualCost }}</div>
          <div class="text-xs text-green-600">实际成本</div>
        </div>
        <div>
          <div class="text-lg font-bold text-green-700">¥{{ completionSummary.actualProfit }}</div>
          <div class="text-xs text-green-600">实际利润</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  ClipboardCheck, 
  FileText, 
  Package, 
  User, 
  Clock, 
  Phone,
  Check
} from 'lucide-vue-next';
import type { OrderDeliveryEntity } from '@/types/order-delivery';

interface Props {
  order: OrderDeliveryEntity;
}

const props = defineProps<Props>();

// 模拟数据
const deliveryStatus = ref('已交付');

const deliveryMetrics = ref({
  totalQuantity: 50,
  qualifiedQuantity: 48,
  qualityRate: 96,
  onTimeDelivery: true
});

const deliveryChecklist = ref([
  {
    id: 'check-001',
    name: '最终质量检验',
    description: '对所有产品进行最终质量检验',
    completed: true,
    completedAt: '2024-02-24T14:00:00'
  },
  {
    id: 'check-002',
    name: '包装标识',
    description: '按要求进行包装并贴上标识',
    completed: true,
    completedAt: '2024-02-24T15:30:00'
  },
  {
    id: 'check-003',
    name: '发货单据',
    description: '准备发货单据和质量证书',
    completed: true,
    completedAt: '2024-02-24T16:00:00'
  },
  {
    id: 'check-004',
    name: '物流安排',
    description: '联系物流公司安排发货',
    completed: true,
    completedAt: '2024-02-24T16:30:00'
  }
]);

const qualityReport = ref({
  inspectedQuantity: 50,
  passedQuantity: 48,
  reworkQuantity: 2,
  rejectedQuantity: 0,
  issues: [
    {
      id: 'issue-001',
      type: '边部质量',
      quantity: 2,
      description: '边部磨削不够光滑',
      resolution: '重新磨边处理'
    }
  ]
});

const shippingInfo = ref({
  packagingMethod: '木箱包装',
  packageCount: 5,
  totalWeight: 1200,
  shippingDate: '2024-02-25',
  logisticsCompany: '顺丰物流',
  trackingNumber: 'SF1234567890',
  estimatedArrival: '2024-02-26',
  shippingCost: 800
});

const customerConfirmation = ref({
  confirmedBy: '张经理',
  confirmedAt: '2024-02-26T10:30:00',
  satisfactionScore: 9,
  feedback: '产品质量很好，包装完整，按时交付，非常满意！'
});

const completionSummary = ref({
  actualDuration: 6,
  qualityScore: 96,
  actualCost: 12800,
  actualProfit: 3200
});

// 计算属性
const isOrderCompleted = computed(() => {
  return deliveryChecklist.value.every(item => item.completed) && customerConfirmation.value;
});

// 方法
const completeChecklistItem = (itemId: string) => {
  const item = deliveryChecklist.value.find(i => i.id === itemId);
  if (item) {
    item.completed = true;
    item.completedAt = new Date().toISOString();
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>
