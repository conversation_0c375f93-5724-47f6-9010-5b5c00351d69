<template>
  <div class="flex flex-col h-full">
    <!-- 工作区标题 -->
    <div class="p-4 border-b">
      <div v-if="!currentOrder" class="text-center py-8">
        <Package class="h-16 w-16 mx-auto text-gray-300 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">选择工单开始工作</h3>
        <p class="text-gray-500">从左侧工单列表中选择一个生产工单来查看详情和执行交付操作</p>
      </div>
      
      <div v-else>
        <div class="flex items-center justify-between mb-2">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ currentOrder.orderNumber }}</h3>
            <p class="text-sm text-gray-600">{{ currentOrder.customerInfo.name }}</p>
          </div>
          
          <!-- 当前阶段指示器 -->
          <div class="flex items-center gap-2">
            <Badge :variant="getPhaseVariant(currentOrder.currentPhase)" size="lg">
              <component :is="getPhaseIcon(currentOrder.currentPhase)" class="h-4 w-4 mr-2" />
              {{ getPhaseText(currentOrder.currentPhase) }}
            </Badge>
          </div>
        </div>
        
        <!-- 阶段进度条 -->
        <div class="flex items-center gap-2 mt-3">
          <div 
            v-for="(phase, index) in phases" 
            :key="phase.key"
            class="flex items-center"
          >
            <div 
              class="flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium"
              :class="getPhaseStepClass(phase.key, currentOrder.currentPhase)"
            >
              {{ index + 1 }}
            </div>
            <div 
              v-if="index < phases.length - 1"
              class="w-12 h-0.5 mx-2"
              :class="isPhaseCompleted(phase.key, currentOrder.currentPhase) ? 'bg-green-500' : 'bg-gray-300'"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工作区内容 -->
    <div v-if="currentOrder" class="flex-1 overflow-y-auto">
      <!-- 智能建议和异常提醒 -->
      <div v-if="actionSuggestions.length > 0 || deliveryAnomalies.length > 0" class="p-4 border-b bg-gray-50">
        <h4 class="font-medium text-gray-900 mb-3">智能建议与提醒</h4>
        
        <!-- 异常提醒 -->
        <div v-if="deliveryAnomalies.length > 0" class="space-y-2 mb-3">
          <div 
            v-for="anomaly in deliveryAnomalies" 
            :key="anomaly.id"
            class="p-3 bg-red-50 border border-red-200 rounded-lg"
          >
            <div class="flex items-start gap-2">
              <AlertTriangle class="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm font-medium text-red-800">{{ anomaly.description }}</p>
                <div class="mt-2 flex flex-wrap gap-1">
                  <Button 
                    v-for="action in anomaly.suggestedActions.slice(0, 2)" 
                    :key="action"
                    size="sm" 
                    variant="outline"
                    class="text-xs"
                  >
                    {{ action }}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 智能建议 -->
        <div v-if="actionSuggestions.length > 0" class="space-y-2">
          <div 
            v-for="suggestion in actionSuggestions" 
            :key="suggestion.id"
            class="p-3 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <div class="flex items-start gap-2">
              <Lightbulb class="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm font-medium text-blue-800">{{ suggestion.title }}</p>
                <p class="text-xs text-blue-600 mt-1">{{ suggestion.description }}</p>
                <div class="mt-2 flex items-center gap-2">
                  <Button 
                    size="sm" 
                    variant="outline"
                    class="text-xs"
                    @click="$emit('action-executed', suggestion.id)"
                  >
                    执行建议
                  </Button>
                  <span class="text-xs text-blue-600">{{ suggestion.impact }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段特定的工作区内容 -->
      <div class="p-4">
        <!-- 交付计划制定阶段 -->
        <DeliveryPlanningPanel 
          v-if="currentOrder.currentPhase === 'planning'"
          :order="currentOrder"
          @plan-completed="handlePlanCompleted"
        />
        
        <!-- 生产排程优化阶段 -->
        <ProductionSchedulingPanel 
          v-else-if="currentOrder.currentPhase === 'scheduling'"
          :order="currentOrder"
          @scheduling-completed="handleSchedulingCompleted"
        />
        
        <!-- 执行监控阶段 -->
        <ExecutionMonitoringPanel 
          v-else-if="currentOrder.currentPhase === 'executing'"
          :order="currentOrder"
          @execution-completed="handleExecutionCompleted"
        />
        
        <!-- 交付确认阶段 -->
        <DeliveryConfirmationPanel 
          v-else-if="currentOrder.currentPhase === 'delivered'"
          :order="currentOrder"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Package, 
  AlertTriangle, 
  Lightbulb,
  Calendar,
  Settings,
  Play,
  CheckCircle
} from 'lucide-vue-next';
import type { 
  OrderDeliveryEntity, 
  ActionSuggestion, 
  DeliveryAnomaly,
  DeliveryPhase 
} from '@/types/order-delivery';

// 导入阶段面板组件
import DeliveryPlanningPanel from './DeliveryPlanningPanel.vue';
import ProductionSchedulingPanel from './ProductionSchedulingPanel.vue';
import ExecutionMonitoringPanel from './ExecutionMonitoringPanel.vue';
import DeliveryConfirmationPanel from './DeliveryConfirmationPanel.vue';

interface Props {
  currentOrder: OrderDeliveryEntity | null;
  actionSuggestions: ActionSuggestion[];
  deliveryAnomalies: DeliveryAnomaly[];
}

interface Emits {
  (e: 'phase-transition', orderId: string, targetPhase: DeliveryPhase): void;
  (e: 'action-executed', actionId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 阶段定义
const phases = [
  { key: 'planning', name: '交付计划', icon: Calendar },
  { key: 'scheduling', name: '生产排程', icon: Settings },
  { key: 'executing', name: '执行监控', icon: Play },
  { key: 'delivered', name: '交付确认', icon: CheckCircle }
];

// 辅助函数
const getPhaseText = (phase: DeliveryPhase) => {
  const texts = {
    planning: '交付计划制定',
    scheduling: '生产排程优化',
    executing: '执行监控',
    delivered: '交付确认'
  };
  return texts[phase] || phase;
};

const getPhaseVariant = (phase: DeliveryPhase) => {
  const variants = {
    planning: 'default' as const,
    scheduling: 'secondary' as const,
    executing: 'default' as const,
    delivered: 'outline' as const
  };
  return variants[phase] || 'default';
};

const getPhaseIcon = (phase: DeliveryPhase) => {
  const icons = {
    planning: Calendar,
    scheduling: Settings,
    executing: Play,
    delivered: CheckCircle
  };
  return icons[phase] || Calendar;
};

const getPhaseStepClass = (phaseKey: string, currentPhase: DeliveryPhase) => {
  const phaseOrder = ['planning', 'scheduling', 'executing', 'delivered'];
  const currentIndex = phaseOrder.indexOf(currentPhase);
  const stepIndex = phaseOrder.indexOf(phaseKey);
  
  if (stepIndex < currentIndex) {
    return 'bg-green-500 text-white'; // 已完成
  } else if (stepIndex === currentIndex) {
    return 'bg-blue-500 text-white'; // 当前阶段
  } else {
    return 'bg-gray-300 text-gray-600'; // 未开始
  }
};

const isPhaseCompleted = (phaseKey: string, currentPhase: DeliveryPhase) => {
  const phaseOrder = ['planning', 'scheduling', 'executing', 'delivered'];
  const currentIndex = phaseOrder.indexOf(currentPhase);
  const stepIndex = phaseOrder.indexOf(phaseKey);
  
  return stepIndex < currentIndex;
};

// 事件处理
const handlePlanCompleted = () => {
  if (props.currentOrder) {
    emit('phase-transition', props.currentOrder.id, 'scheduling');
  }
};

const handleSchedulingCompleted = () => {
  if (props.currentOrder) {
    emit('phase-transition', props.currentOrder.id, 'executing');
  }
};

const handleExecutionCompleted = () => {
  if (props.currentOrder) {
    emit('phase-transition', props.currentOrder.id, 'delivered');
  }
};
</script>
