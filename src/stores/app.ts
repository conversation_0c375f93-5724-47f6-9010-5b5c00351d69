/**
 * 应用全局状态管理Store
 * 管理应用级别的状态，如侧边栏、主题、通知、加载状态等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 主题类型
export type Theme = 'light' | 'dark' | 'system'

// 通知类型
export interface Notification {
  id: string
  type: 'info' | 'warning' | 'success' | 'error'
  title: string
  message: string
  read: boolean
  createdAt: string
  actions?: NotificationAction[]
}

// 通知操作
export interface NotificationAction {
  label: string
  action: () => void
  variant?: 'default' | 'destructive' | 'outline' | 'secondary'
}

// 应用配置
export interface AppConfig {
  sidebarCollapsed: boolean
  theme: Theme
  language: string
  timezone: string
}

export const useAppStore = defineStore('app', () => {
  // 状态定义
  const sidebarCollapsed = ref<boolean>(false)
  const theme = ref<Theme>('system')
  const language = ref<string>('zh-CN')
  const timezone = ref<string>('Asia/Shanghai')
  const isLoading = ref<boolean>(false)
  const loadingMessage = ref<string>('')
  const notifications = ref<Notification[]>([])
  const isOnline = ref<boolean>(navigator.onLine)

  // 计算属性
  const unreadNotifications = computed<number>(() => {
    return notifications.value.filter(n => !n.read).length
  })

  const hasUnreadNotifications = computed<boolean>(() => {
    return unreadNotifications.value > 0
  })

  const currentTheme = computed<'light' | 'dark'>(() => {
    if (theme.value === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return theme.value
  })

  const appConfig = computed<AppConfig>(() => ({
    sidebarCollapsed: sidebarCollapsed.value,
    theme: theme.value,
    language: language.value,
    timezone: timezone.value
  }))

  // Actions

  /**
   * 切换侧边栏状态
   */
  const toggleSidebar = (): void => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    persistAppConfig()
  }

  /**
   * 设置侧边栏状态
   */
  const setSidebarCollapsed = (collapsed: boolean): void => {
    sidebarCollapsed.value = collapsed
    persistAppConfig()
  }

  /**
   * 设置主题
   */
  const setTheme = (newTheme: Theme): void => {
    theme.value = newTheme
    applyTheme()
    persistAppConfig()
  }

  /**
   * 切换主题
   */
  const toggleTheme = (): void => {
    const themes: Theme[] = ['light', 'dark', 'system']
    const currentIndex = themes.indexOf(theme.value)
    const nextIndex = (currentIndex + 1) % themes.length
    setTheme(themes[nextIndex])
  }

  /**
   * 应用主题到DOM
   */
  const applyTheme = (): void => {
    const html = document.documentElement
    const isDark = currentTheme.value === 'dark'
    
    if (isDark) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  /**
   * 设置语言
   */
  const setLanguage = (lang: string): void => {
    language.value = lang
    persistAppConfig()
  }

  /**
   * 设置时区
   */
  const setTimezone = (tz: string): void => {
    timezone.value = tz
    persistAppConfig()
  }

  /**
   * 显示加载状态
   */
  const showLoading = (message: string = '加载中...'): void => {
    isLoading.value = true
    loadingMessage.value = message
  }

  /**
   * 隐藏加载状态
   */
  const hideLoading = (): void => {
    isLoading.value = false
    loadingMessage.value = ''
  }

  /**
   * 添加通知
   */
  const addNotification = (notification: Omit<Notification, 'id' | 'createdAt'>): string => {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const newNotification: Notification = {
      ...notification,
      id,
      createdAt: new Date().toISOString()
    }
    
    notifications.value.unshift(newNotification)
    
    // 限制通知数量，最多保留100条
    if (notifications.value.length > 100) {
      notifications.value = notifications.value.slice(0, 100)
    }
    
    return id
  }

  /**
   * 移除通知
   */
  const removeNotification = (id: string): void => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  /**
   * 标记通知为已读
   */
  const markNotificationAsRead = (id: string): void => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  /**
   * 标记所有通知为已读
   */
  const markAllNotificationsAsRead = (): void => {
    notifications.value.forEach(n => n.read = true)
  }

  /**
   * 清除所有通知
   */
  const clearAllNotifications = (): void => {
    notifications.value = []
  }

  /**
   * 清除已读通知
   */
  const clearReadNotifications = (): void => {
    notifications.value = notifications.value.filter(n => !n.read)
  }

  /**
   * 设置网络状态
   */
  const setOnlineStatus = (online: boolean): void => {
    isOnline.value = online
  }

  /**
   * 持久化应用配置
   */
  const persistAppConfig = (): void => {
    if (typeof window === 'undefined') return

    const config = {
      sidebarCollapsed: sidebarCollapsed.value,
      theme: theme.value,
      language: language.value,
      timezone: timezone.value,
      timestamp: Date.now()
    }

    localStorage.setItem('app_config', JSON.stringify(config))
  }

  /**
   * 从本地存储恢复应用配置
   */
  const restoreAppConfig = (): boolean => {
    if (typeof window === 'undefined') return false

    try {
      const stored = localStorage.getItem('app_config')
      if (!stored) return false

      const config = JSON.parse(stored)
      
      // 恢复配置
      sidebarCollapsed.value = config.sidebarCollapsed ?? false
      theme.value = config.theme ?? 'system'
      language.value = config.language ?? 'zh-CN'
      timezone.value = config.timezone ?? 'Asia/Shanghai'

      // 应用主题
      applyTheme()

      return true
    } catch (err) {
      console.error('恢复应用配置失败:', err)
      return false
    }
  }

  /**
   * 重置应用配置
   */
  const resetAppConfig = (): void => {
    sidebarCollapsed.value = false
    theme.value = 'system'
    language.value = 'zh-CN'
    timezone.value = 'Asia/Shanghai'
    
    applyTheme()
    persistAppConfig()
  }

  /**
   * 初始化应用状态
   */
  const initializeApp = (): void => {
    // 恢复配置
    restoreAppConfig()
    
    // 监听网络状态变化
    window.addEventListener('online', () => setOnlineStatus(true))
    window.addEventListener('offline', () => setOnlineStatus(false))
    
    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', () => {
      if (theme.value === 'system') {
        applyTheme()
      }
    })
  }

  return {
    // 状态
    sidebarCollapsed,
    theme,
    language,
    timezone,
    isLoading,
    loadingMessage,
    notifications,
    isOnline,
    
    // 计算属性
    unreadNotifications,
    hasUnreadNotifications,
    currentTheme,
    appConfig,
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    toggleTheme,
    setLanguage,
    setTimezone,
    showLoading,
    hideLoading,
    addNotification,
    removeNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    clearAllNotifications,
    clearReadNotifications,
    setOnlineStatus,
    restoreAppConfig,
    resetAppConfig,
    initializeApp
  }
})
