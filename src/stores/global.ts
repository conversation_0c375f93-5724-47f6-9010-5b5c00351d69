/**
 * 全局状态管理Store
 * 管理跨模块的共享状态，如当前选中的物料变体、订单状态等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { OrderStatus } from './business'

// 全局选择状态接口
export interface GlobalSelection {
  materialVariantId: string | null
  orderId: string | null
  customerId: string | null
  projectId: string | null
}

// 工作流状态接口
export interface WorkflowState {
  currentStep: string | null
  completedSteps: string[]
  availableSteps: string[]
  workflowData: Record<string, any>
}

// 全局过滤器状态接口
export interface GlobalFilters {
  dateRange: {
    start: string | null
    end: string | null
  }
  statusFilter: OrderStatus[]
  departmentFilter: string[]
  priorityFilter: string[]
}

// 快捷操作历史接口
export interface QuickAction {
  id: string
  type: 'material_select' | 'order_create' | 'stock_update' | 'customer_contact'
  title: string
  description: string
  data: Record<string, any>
  timestamp: string
}

export const useGlobalStore = defineStore('global', () => {
  // 选择状态
  const currentSelection = ref<GlobalSelection>({
    materialVariantId: null,
    orderId: null,
    customerId: null,
    projectId: null
  })

  // 工作流状态
  const workflowState = ref<WorkflowState>({
    currentStep: null,
    completedSteps: [],
    availableSteps: [],
    workflowData: {}
  })

  // 全局过滤器
  const globalFilters = ref<GlobalFilters>({
    dateRange: {
      start: null,
      end: null
    },
    statusFilter: [],
    departmentFilter: [],
    priorityFilter: []
  })

  // 快捷操作历史
  const quickActions = ref<QuickAction[]>([])

  // 页面状态
  const currentPage = ref<string>('dashboard')
  const previousPage = ref<string | null>(null)
  const pageHistory = ref<string[]>([])

  // 临时数据存储（用于跨页面传递数据）
  const tempData = ref<Record<string, any>>({})

  // 全局加载状态
  const globalLoading = ref<boolean>(false)
  const globalLoadingMessage = ref<string>('')

  // 计算属性
  const hasActiveSelection = computed<boolean>(() => {
    return !!(
      currentSelection.value.materialVariantId ||
      currentSelection.value.orderId ||
      currentSelection.value.customerId ||
      currentSelection.value.projectId
    )
  })

  const isInWorkflow = computed<boolean>(() => {
    return !!workflowState.value.currentStep
  })

  const workflowProgress = computed<number>(() => {
    if (!workflowState.value.availableSteps.length) return 0
    return (workflowState.value.completedSteps.length / workflowState.value.availableSteps.length) * 100
  })

  const recentQuickActions = computed<QuickAction[]>(() => {
    return quickActions.value
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10)
  })

  const hasActiveFilters = computed<boolean>(() => {
    return !!(
      globalFilters.value.dateRange.start ||
      globalFilters.value.dateRange.end ||
      globalFilters.value.statusFilter.length ||
      globalFilters.value.departmentFilter.length ||
      globalFilters.value.priorityFilter.length
    )
  })

  // Actions

  /**
   * 设置当前选中的物料变体
   */
  const setSelectedMaterialVariant = (variantId: string | null): void => {
    currentSelection.value.materialVariantId = variantId
    
    if (variantId) {
      addQuickAction({
        type: 'material_select',
        title: '选择物料变体',
        description: `选中物料变体: ${variantId}`,
        data: { variantId }
      })
    }
    
    persistGlobalState()
  }

  /**
   * 设置当前选中的订单
   */
  const setSelectedOrder = (orderId: string | null): void => {
    currentSelection.value.orderId = orderId
    
    if (orderId) {
      addQuickAction({
        type: 'order_create',
        title: '选择订单',
        description: `选中订单: ${orderId}`,
        data: { orderId }
      })
    }
    
    persistGlobalState()
  }

  /**
   * 设置当前选中的客户
   */
  const setSelectedCustomer = (customerId: string | null): void => {
    currentSelection.value.customerId = customerId
    
    if (customerId) {
      addQuickAction({
        type: 'customer_contact',
        title: '选择客户',
        description: `选中客户: ${customerId}`,
        data: { customerId }
      })
    }
    
    persistGlobalState()
  }

  /**
   * 设置当前项目
   */
  const setSelectedProject = (projectId: string | null): void => {
    currentSelection.value.projectId = projectId
    persistGlobalState()
  }

  /**
   * 清除所有选择
   */
  const clearAllSelections = (): void => {
    currentSelection.value = {
      materialVariantId: null,
      orderId: null,
      customerId: null,
      projectId: null
    }
    persistGlobalState()
  }

  /**
   * 开始工作流
   */
  const startWorkflow = (workflowType: string, initialData: Record<string, any> = {}): void => {
    const workflows = {
      'order_creation': ['customer_select', 'product_config', 'material_select', 'pricing', 'confirmation'],
      'material_procurement': ['supplier_select', 'quantity_plan', 'approval', 'purchase_order'],
      'production_planning': ['order_review', 'material_check', 'schedule_plan', 'resource_allocation']
    }

    const steps = workflows[workflowType as keyof typeof workflows] || []

    workflowState.value = {
      currentStep: steps[0] || null,
      completedSteps: [],
      availableSteps: steps,
      workflowData: { ...initialData, workflowType }
    }

    persistGlobalState()
  }

  /**
   * 完成工作流步骤
   */
  const completeWorkflowStep = (stepData: Record<string, any> = {}): void => {
    if (!workflowState.value.currentStep) return

    const currentStepIndex = workflowState.value.availableSteps.indexOf(workflowState.value.currentStep)
    
    // 标记当前步骤为完成
    workflowState.value.completedSteps.push(workflowState.value.currentStep)
    
    // 合并步骤数据
    workflowState.value.workflowData = {
      ...workflowState.value.workflowData,
      [workflowState.value.currentStep]: stepData
    }

    // 移动到下一步
    if (currentStepIndex < workflowState.value.availableSteps.length - 1) {
      workflowState.value.currentStep = workflowState.value.availableSteps[currentStepIndex + 1]
    } else {
      // 工作流完成
      workflowState.value.currentStep = null
    }

    persistGlobalState()
  }

  /**
   * 取消工作流
   */
  const cancelWorkflow = (): void => {
    workflowState.value = {
      currentStep: null,
      completedSteps: [],
      availableSteps: [],
      workflowData: {}
    }
    persistGlobalState()
  }

  /**
   * 设置全局过滤器
   */
  const setGlobalFilters = (filters: Partial<GlobalFilters>): void => {
    globalFilters.value = {
      ...globalFilters.value,
      ...filters
    }
    persistGlobalState()
  }

  /**
   * 清除全局过滤器
   */
  const clearGlobalFilters = (): void => {
    globalFilters.value = {
      dateRange: { start: null, end: null },
      statusFilter: [],
      departmentFilter: [],
      priorityFilter: []
    }
    persistGlobalState()
  }

  /**
   * 添加快捷操作记录
   */
  const addQuickAction = (action: Omit<QuickAction, 'id' | 'timestamp'>): void => {
    const quickAction: QuickAction = {
      ...action,
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    }

    quickActions.value.unshift(quickAction)

    // 限制历史记录数量
    if (quickActions.value.length > 50) {
      quickActions.value = quickActions.value.slice(0, 50)
    }

    persistGlobalState()
  }

  /**
   * 清除快捷操作历史
   */
  const clearQuickActions = (): void => {
    quickActions.value = []
    persistGlobalState()
  }

  /**
   * 设置当前页面
   */
  const setCurrentPage = (page: string): void => {
    if (currentPage.value !== page) {
      previousPage.value = currentPage.value
      pageHistory.value.push(currentPage.value)
      currentPage.value = page

      // 限制页面历史记录
      if (pageHistory.value.length > 20) {
        pageHistory.value = pageHistory.value.slice(-20)
      }
    }
  }

  /**
   * 返回上一页
   */
  const goToPreviousPage = (): string | null => {
    if (previousPage.value) {
      const targetPage = previousPage.value
      setCurrentPage(targetPage)
      return targetPage
    }
    return null
  }

  /**
   * 设置临时数据
   */
  const setTempData = (key: string, data: any): void => {
    tempData.value[key] = data
  }

  /**
   * 获取临时数据
   */
  const getTempData = (key: string): any => {
    return tempData.value[key]
  }

  /**
   * 清除临时数据
   */
  const clearTempData = (key?: string): void => {
    if (key) {
      delete tempData.value[key]
    } else {
      tempData.value = {}
    }
  }

  /**
   * 设置全局加载状态
   */
  const setGlobalLoading = (loading: boolean, message: string = ''): void => {
    globalLoading.value = loading
    globalLoadingMessage.value = message
  }

  /**
   * 持久化全局状态
   */
  const persistGlobalState = (): void => {
    if (typeof window === 'undefined') return

    const globalState = {
      currentSelection: currentSelection.value,
      workflowState: workflowState.value,
      globalFilters: globalFilters.value,
      quickActions: quickActions.value,
      currentPage: currentPage.value,
      previousPage: previousPage.value,
      pageHistory: pageHistory.value,
      timestamp: Date.now()
    }

    try {
      localStorage.setItem('global_state', JSON.stringify(globalState))
    } catch (error) {
      console.error('持久化全局状态失败:', error)
    }
  }

  /**
   * 从本地存储恢复全局状态
   */
  const restoreGlobalState = (): boolean => {
    if (typeof window === 'undefined') return false

    try {
      const stored = localStorage.getItem('global_state')
      if (!stored) return false

      const globalState = JSON.parse(stored)

      // 检查数据是否过期（1小时）
      const isExpired = Date.now() - globalState.timestamp > 60 * 60 * 1000
      if (isExpired) {
        clearPersistedGlobalState()
        return false
      }

      // 恢复状态
      currentSelection.value = globalState.currentSelection || {
        materialVariantId: null,
        orderId: null,
        customerId: null,
        projectId: null
      }
      workflowState.value = globalState.workflowState || {
        currentStep: null,
        completedSteps: [],
        availableSteps: [],
        workflowData: {}
      }
      globalFilters.value = globalState.globalFilters || {
        dateRange: { start: null, end: null },
        statusFilter: [],
        departmentFilter: [],
        priorityFilter: []
      }
      quickActions.value = globalState.quickActions || []
      currentPage.value = globalState.currentPage || 'dashboard'
      previousPage.value = globalState.previousPage || null
      pageHistory.value = globalState.pageHistory || []

      return true
    } catch (error) {
      console.error('恢复全局状态失败:', error)
      clearPersistedGlobalState()
      return false
    }
  }

  /**
   * 清除持久化的全局状态
   */
  const clearPersistedGlobalState = (): void => {
    if (typeof window === 'undefined') return
    localStorage.removeItem('global_state')
  }

  /**
   * 重置全局状态
   */
  const resetGlobalState = (): void => {
    currentSelection.value = {
      materialVariantId: null,
      orderId: null,
      customerId: null,
      projectId: null
    }
    workflowState.value = {
      currentStep: null,
      completedSteps: [],
      availableSteps: [],
      workflowData: {}
    }
    globalFilters.value = {
      dateRange: { start: null, end: null },
      statusFilter: [],
      departmentFilter: [],
      priorityFilter: []
    }
    quickActions.value = []
    currentPage.value = 'dashboard'
    previousPage.value = null
    pageHistory.value = []
    tempData.value = {}
    globalLoading.value = false
    globalLoadingMessage.value = ''
    
    clearPersistedGlobalState()
  }

  return {
    // 状态
    currentSelection,
    workflowState,
    globalFilters,
    quickActions,
    currentPage,
    previousPage,
    pageHistory,
    tempData,
    globalLoading,
    globalLoadingMessage,

    // 计算属性
    hasActiveSelection,
    isInWorkflow,
    workflowProgress,
    recentQuickActions,
    hasActiveFilters,

    // 方法
    setSelectedMaterialVariant,
    setSelectedOrder,
    setSelectedCustomer,
    setSelectedProject,
    clearAllSelections,
    startWorkflow,
    completeWorkflowStep,
    cancelWorkflow,
    setGlobalFilters,
    clearGlobalFilters,
    addQuickAction,
    clearQuickActions,
    setCurrentPage,
    goToPreviousPage,
    setTempData,
    getTempData,
    clearTempData,
    setGlobalLoading,
    restoreGlobalState,
    resetGlobalState
  }
})