import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Assembly, AssemblyFilters } from '@/types/product';
import { assemblyService } from '@/services/productService';

// 构件管理Store
export const useAssemblyStore = defineStore('assembly', () => {
  const assemblies = ref<Assembly[]>([]);
  const currentAssembly = ref<Assembly | null>(null);
  const filters = ref<AssemblyFilters>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  const filteredAssemblies = computed(() => {
    if (!filters.value) return assemblies.value;
    
    return assemblies.value.filter(assembly => {
      if (filters.value.assemblyType && assembly.assemblyType !== filters.value.assemblyType) {
        return false;
      }
      if (filters.value.status && assembly.status !== filters.value.status) {
        return false;
      }
      if (filters.value.search) {
        const searchLower = filters.value.search.toLowerCase();
        return (
          assembly.name.toLowerCase().includes(searchLower) ||
          assembly.code.toLowerCase().includes(searchLower) ||
          assembly.description?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  });

  const loadAssemblies = async (newFilters?: AssemblyFilters) => {
    loading.value = true;
    error.value = null;
    
    try {
      if (newFilters) {
        filters.value = newFilters;
      }
      assemblies.value = await assemblyService.getAssemblies(filters.value);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载构件失败';
      console.error('Error loading assemblies:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadAssemblyById = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      currentAssembly.value = await assemblyService.getAssemblyById(id);
      if (!currentAssembly.value) {
        error.value = `未找到ID为 ${id} 的构件`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载构件详情失败';
      console.error('Error loading assembly:', err);
    } finally {
      loading.value = false;
    }
  };

  const createAssembly = async (assemblyData: Partial<Assembly>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const newAssembly = await assemblyService.createAssembly(assemblyData);
      assemblies.value.push(newAssembly);
      return newAssembly;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建构件失败';
      console.error('Error creating assembly:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateAssembly = async (id: string, updates: Partial<Assembly>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const updatedAssembly = await assemblyService.updateAssembly(id, updates);
      const index = assemblies.value.findIndex(a => a.id === id);
      if (index !== -1) {
        assemblies.value[index] = updatedAssembly;
      }
      if (currentAssembly.value?.id === id) {
        currentAssembly.value = updatedAssembly;
      }
      return updatedAssembly;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新构件失败';
      console.error('Error updating assembly:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteAssembly = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      await assemblyService.deleteAssembly(id);
      assemblies.value = assemblies.value.filter(a => a.id !== id);
      if (currentAssembly.value?.id === id) {
        currentAssembly.value = null;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除构件失败';
      console.error('Error deleting assembly:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const setFilters = (newFilters: AssemblyFilters) => {
    filters.value = newFilters;
  };

  const clearError = () => {
    error.value = null;
  };

  const reset = () => {
    assemblies.value = [];
    currentAssembly.value = null;
    filters.value = {};
    loading.value = false;
    error.value = null;
  };

  return {
    assemblies,
    currentAssembly,
    filters,
    loading,
    error,
    filteredAssemblies,
    loadAssemblies,
    loadAssemblyById,
    createAssembly,
    updateAssembly,
    deleteAssembly,
    setFilters,
    clearError,
    reset
  };
});
