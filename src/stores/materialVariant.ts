/**
 * 物料变体状态管理Store
 * 管理物料变体、库存、补货等核心业务数据
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  MaterialTemplate,
  MaterialVariant,
  MaterialVariantStock,
  MaterialVariantStockMove,
  WasteMaterialVariantStock,
  MaterialVariantStockAlert,
  MaterialVariantReorderRule,
  Supplier,
  StockLocation,
  MaterialVariantFilter,
  MaterialVariantSearchResult,
  MaterialVariantOperationResult,
  AttributeValue
} from '@/types/material-variant'
import { apiGet } from '@/utils/api'

export const useMaterialVariantStore = defineStore('materialVariant', () => {
  // 状态定义
  const materialTemplates = ref<MaterialTemplate[]>([])
  const selectedTemplateId = ref<string | null>(null)
  
  const materialVariants = ref<MaterialVariant[]>([])
  const selectedVariantId = ref<string | null>(null)
  
  const variantStocks = ref<MaterialVariantStock[]>([])
  const stockMoves = ref<MaterialVariantStockMove[]>([])
  const wasteStocks = ref<WasteMaterialVariantStock[]>([])
  const stockAlerts = ref<MaterialVariantStockAlert[]>([])
  
  const reorderRules = ref<MaterialVariantReorderRule[]>([])
  const suppliers = ref<Supplier[]>([])
  const stockLocations = ref<StockLocation[]>([])
  
  // 加载状态
  const isLoadingTemplates = ref<boolean>(false)
  const isLoadingVariants = ref<boolean>(false)
  const isLoadingStocks = ref<boolean>(false)
  
  // 错误状态
  const templatesError = ref<string | null>(null)
  const variantsError = ref<string | null>(null)
  const stocksError = ref<string | null>(null)
  
  // 搜索和过滤
  const searchQuery = ref<string>('')
  const currentFilter = ref<MaterialVariantFilter>({})
  const searchResults = ref<MaterialVariantSearchResult | null>(null)

  // 计算属性
  const selectedTemplate = computed<MaterialTemplate | null>(() => {
    if (!selectedTemplateId.value) return null
    return materialTemplates.value.find(t => t.id === selectedTemplateId.value) || null
  })

  const selectedVariant = computed<MaterialVariant | null>(() => {
    if (!selectedVariantId.value) return null
    return materialVariants.value.find(v => v.id === selectedVariantId.value) || null
  })

  const activeTemplates = computed<MaterialTemplate[]>(() => {
    return materialTemplates.value.filter(t => t.isActive)
  })

  const activeVariants = computed<MaterialVariant[]>(() => {
    return materialVariants.value.filter(v => v.isActive)
  })

  const variantsForSelectedTemplate = computed<MaterialVariant[]>(() => {
    if (!selectedTemplateId.value) return []
    return materialVariants.value.filter(v => v.templateId === selectedTemplateId.value)
  })

  const lowStockVariants = computed<MaterialVariant[]>(() => {
    return materialVariants.value.filter(variant => {
      const stock = variantStocks.value.find(s => s.materialVariantId === variant.id)
      if (!stock) return false
      return stock.availableQuantity <= stock.reorderPoint
    })
  })

  const outOfStockVariants = computed<MaterialVariant[]>(() => {
    return materialVariants.value.filter(variant => {
      const stock = variantStocks.value.find(s => s.materialVariantId === variant.id)
      if (!stock) return true
      return stock.availableQuantity <= 0
    })
  })

  const criticalStockAlerts = computed<MaterialVariantStockAlert[]>(() => {
    return stockAlerts.value.filter(alert => 
      alert.severity === 'critical' && !alert.isResolved
    )
  })

  const totalStockValue = computed<number>(() => {
    return variantStocks.value.reduce((total, stock) => total + stock.totalValue, 0)
  })

  const stockStatistics = computed(() => {
    const total = materialVariants.value.length
    const inStock = materialVariants.value.filter(variant => {
      const stock = variantStocks.value.find(s => s.materialVariantId === variant.id)
      return stock && stock.availableQuantity > 0
    }).length
    const lowStock = lowStockVariants.value.length
    const outOfStock = outOfStockVariants.value.length

    return {
      total,
      inStock,
      lowStock,
      outOfStock,
      inStockRate: total > 0 ? (inStock / total * 100).toFixed(1) : '0'
    }
  })

  // Actions

  /**
   * 设置选中的物料模板
   */
  const setSelectedTemplate = (templateId: string | null): void => {
    selectedTemplateId.value = templateId
    // 清空选中的变体
    selectedVariantId.value = null
  }

  /**
   * 设置选中的物料变体
   */
  const setSelectedVariant = (variantId: string | null): void => {
    selectedVariantId.value = variantId
  }

  /**
   * 加载物料模板
   */
  const loadMaterialTemplates = async (): Promise<boolean> => {
    try {
      isLoadingTemplates.value = true
      templatesError.value = null

      // 使用统一的 API 加载物料分类
      const response = await apiGet<{ materialCategories: any[] }>('metadata/materialCategories.json')
      
      if (!response.success) {
        throw new Error(response.message || '无法加载物料分类数据')
      }

      const data = response.data
      
      if (data.materialCategories) {
        // 将分类数据转换为模板数据，只选择有 attributeSchema 的分类
        const templates: MaterialTemplate[] = data.materialCategories
          .filter((category: any) => category.attributeSchema && !category.hasChildren)
          .map((category: any) => {
            // 根据分类ID确定物料类型
            let materialType: 'raw_glass' | 'profile' | 'hardware' | 'sealant' | 'chemical' = 'raw_glass'
            if (category.categoryId.includes('PROFILE')) {
              materialType = 'profile'
            } else if (category.categoryId.includes('HARDWARE')) {
              materialType = 'hardware'
            } else if (category.categoryId.includes('SEALANT')) {
              materialType = 'sealant'
            }

            return {
              id: category.categoryId,
              name: category.categoryName,
              code: category.categoryId,
              category: category.categoryName,
              materialType,
              baseAttributes: category.attributeSchema.baseAttributes.map((attr: any) => ({
                id: `${category.categoryId}_${attr.name}`,
                name: attr.name,
                type: attr.type,
                unit: attr.unit,
                options: attr.options,
                isRequired: attr.required || false,
                description: attr.description
              })),
              variantAttributes: category.attributeSchema.variantAttributes.map((attr: any) => ({
                id: `${category.categoryId}_${attr.name}`,
                name: attr.name,
                type: attr.type,
                unit: attr.unit || '',
                minValue: attr.minValue,
                maxValue: attr.maxValue,
                isRequired: attr.required || false,
                description: attr.description
              })),
              isActive: true,
              createdAt: category.createdAt || new Date().toISOString(),
              updatedAt: category.updatedAt
            }
          })

        materialTemplates.value = templates
        return true
      } else {
        throw new Error('物料分类数据格式错误')
      }
    } catch (error) {
      templatesError.value = error instanceof Error ? error.message : '加载物料模板失败'
      return false
    } finally {
      isLoadingTemplates.value = false
    }
  }

  /**
   * 加载物料变体
   */
  const loadMaterialVariants = async (): Promise<boolean> => {
    try {
      isLoadingVariants.value = true
      variantsError.value = null

      // 直接从 Mock 数据加载物料数据
      const response = await fetch('/mock/metadata/materials.json')
      if (!response.ok) {
        throw new Error('无法加载物料数据')
      }

      const data = await response.json()
      
      if (data.materials) {
        // 将物料数据转换为变体数据
        const variants: MaterialVariant[] = []
        
        data.materials.forEach((material: any) => {
          if (material.variants && Array.isArray(material.variants)) {
            material.variants.forEach((variant: unknown) => {
              // 创建供应商对象
              const supplier: Supplier = {
                id: `supplier_${variant.supplier.replace(/\s+/g, '_')}`,
                name: variant.supplier,
                code: variant.supplier.substring(0, 3).toUpperCase(),
                contactPerson: '联系人',
                phone: '************',
                email: `contact@${variant.supplier}.com`,
                address: '供应商地址',
                isActive: true
              }

              // 转换基础属性值
              const baseAttributeValues: AttributeValue[] = Object.entries(material.attributes || {}).map(([key, value]) => ({
                attributeId: `${material.categoryId}_${key}`,
                attributeName: key,
                value: value as string | number,
                unit: key === '厚度' ? 'mm' : undefined
              }))

              // 转换变体属性值
              const variantAttributeValues: AttributeValue[] = Object.entries(variant.variantAttributes || {}).map(([key, value]) => ({
                attributeId: `${material.categoryId}_${key}`,
                attributeName: key,
                value: value as string | number,
                unit: ['宽度', '高度', '长度'].includes(key) ? 'mm' : undefined
              }))

              variants.push({
                id: variant.variantId,
                templateId: material.categoryId,
                sku: variant.sku,
                displayName: variant.displayName,
                baseAttributeValues,
                variantAttributeValues,
                cost: variant.cost || 0,
                weight: variant.weightKg || (variant.weightKgPerMeter ? variant.weightKgPerMeter * (variant.variantAttributes?.长度 || 1000) / 1000 : 0),
                area: variant.areaSqm,
                supplier,
                leadTime: variant.leadTimeDays || 7,
                isActive: variant.isActive !== false,
                stockQuantity: variant.stock || 0,
                reservedQuantity: 0,
                availableQuantity: variant.stock || 0,
                createdAt: new Date().toISOString(),
                updatedAt: undefined
              })
            })
          }
        })

        materialVariants.value = variants
        return true
      } else {
        throw new Error('物料数据格式错误')
      }
    } catch (error) {
      variantsError.value = error instanceof Error ? error.message : '加载物料变体失败'
      return false
    } finally {
      isLoadingVariants.value = false
    }
  }

  /**
   * 加载库存数据
   */
  const loadVariantStocks = async (): Promise<boolean> => {
    try {
      isLoadingStocks.value = true
      stocksError.value = null

      // 基于已加载的物料变体生成库存数据
      const stocks: MaterialVariantStock[] = materialVariants.value.map(variant => ({
        id: `stock_${variant.id}`,
        materialVariantId: variant.id,
        materialVariant: variant,
        locationId: 'location_main',
        location: {
          id: 'location_main',
          name: '主仓库',
          code: 'MAIN',
          type: 'warehouse',
          isActive: true
        },
        quantity: variant.stockQuantity,
        reservedQuantity: variant.reservedQuantity,
        availableQuantity: variant.availableQuantity,
        unitCost: variant.cost,
        totalValue: variant.stockQuantity * variant.cost,
        lastMovementDate: new Date().toISOString(),
        reorderPoint: Math.max(10, Math.floor(variant.stockQuantity * 0.2)),
        maxStock: variant.stockQuantity * 2,
        lotNumbers: [`LOT_${Date.now()}`],
        expiryDate: undefined
      }))

      variantStocks.value = stocks

      // 生成库存预警
      generateStockAlerts()

      return true
    } catch (error) {
      stocksError.value = error instanceof Error ? error.message : '加载库存数据失败'
      return false
    } finally {
      isLoadingStocks.value = false
    }
  }

  /**
   * 生成库存预警
   */
  const generateStockAlerts = (): void => {
    const alerts: MaterialVariantStockAlert[] = []

    variantStocks.value.forEach(stock => {
      if (stock.availableQuantity <= 0) {
        alerts.push({
          id: `alert_${stock.id}_no_stock`,
          materialVariantId: stock.materialVariantId,
          materialVariant: stock.materialVariant,
          alertType: 'no_stock',
          currentQuantity: stock.availableQuantity,
          thresholdQuantity: 0,
          severity: 'critical',
          message: `${stock.materialVariant.displayName} 已无库存`,
          createdDate: new Date().toISOString(),
          isResolved: false
        })
      } else if (stock.availableQuantity <= stock.reorderPoint) {
        alerts.push({
          id: `alert_${stock.id}_low_stock`,
          materialVariantId: stock.materialVariantId,
          materialVariant: stock.materialVariant,
          alertType: 'low_stock',
          currentQuantity: stock.availableQuantity,
          thresholdQuantity: stock.reorderPoint,
          severity: stock.availableQuantity <= stock.reorderPoint * 0.5 ? 'high' : 'medium',
          message: `${stock.materialVariant.displayName} 库存不足，当前库存 ${stock.availableQuantity}，安全库存 ${stock.reorderPoint}`,
          createdDate: new Date().toISOString(),
          isResolved: false
        })
      }
    })

    stockAlerts.value = alerts
  }

  /**
   * 搜索物料变体
   */
  const searchMaterialVariants = async (
    query: string,
    filter: MaterialVariantFilter = {}
  ): Promise<MaterialVariantSearchResult> => {
    searchQuery.value = query
    currentFilter.value = filter

    let filteredVariants = [...materialVariants.value]

    // 文本搜索
    if (query.trim()) {
      const searchTerm = query.toLowerCase()
      filteredVariants = filteredVariants.filter(variant =>
        variant.displayName.toLowerCase().includes(searchTerm) ||
        variant.sku.toLowerCase().includes(searchTerm)
      )
    }

    // 应用过滤条件
    if (filter.templateId) {
      filteredVariants = filteredVariants.filter(v => v.templateId === filter.templateId)
    }

    if (filter.isActive !== undefined) {
      filteredVariants = filteredVariants.filter(v => v.isActive === filter.isActive)
    }

    if (filter.stockStatus) {
      filteredVariants = filteredVariants.filter(variant => {
        const stock = variantStocks.value.find(s => s.materialVariantId === variant.id)
        if (!stock) return filter.stockStatus === 'out_of_stock'
        
        switch (filter.stockStatus) {
          case 'in_stock':
            return stock.availableQuantity > stock.reorderPoint
          case 'low_stock':
            return stock.availableQuantity > 0 && stock.availableQuantity <= stock.reorderPoint
          case 'out_of_stock':
            return stock.availableQuantity <= 0
          default:
            return true
        }
      })
    }

    if (filter.priceRange) {
      filteredVariants = filteredVariants.filter(v =>
        v.cost >= filter.priceRange!.min && v.cost <= filter.priceRange!.max
      )
    }

    const result: MaterialVariantSearchResult = {
      variants: filteredVariants,
      totalCount: filteredVariants.length,
      filters: filter,
      sortBy: 'displayName',
      sortOrder: 'asc'
    }

    searchResults.value = result
    return result
  }

  /**
   * 更新物料变体库存
   */
  const updateVariantStock = (variantId: string, quantity: number): boolean => {
    const stock = variantStocks.value.find(s => s.materialVariantId === variantId)
    if (stock) {
      stock.quantity = quantity
      stock.availableQuantity = quantity - stock.reservedQuantity
      stock.totalValue = quantity * stock.unitCost
      stock.lastMovementDate = new Date().toISOString()

      // 更新对应的物料变体
      const variant = materialVariants.value.find(v => v.id === variantId)
      if (variant) {
        variant.stockQuantity = quantity
        variant.availableQuantity = stock.availableQuantity
      }

      // 重新生成库存预警
      generateStockAlerts()

      return true
    }
    return false
  }

  /**
   * 添加物料变体
   */
  const addMaterialVariant = async (variant: Omit<MaterialVariant, 'id' | 'createdAt'>): Promise<MaterialVariantOperationResult> => {
    try {
      const id = `variant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const newVariant: MaterialVariant = {
        ...variant,
        id,
        createdAt: new Date().toISOString()
      }

      materialVariants.value.push(newVariant)

      // 创建对应的库存记录
      const stock: MaterialVariantStock = {
        id: `stock_${id}`,
        materialVariantId: id,
        materialVariant: newVariant,
        locationId: 'location_main',
        location: {
          id: 'location_main',
          name: '主仓库',
          code: 'MAIN',
          type: 'warehouse',
          isActive: true
        },
        quantity: newVariant.stockQuantity,
        reservedQuantity: newVariant.reservedQuantity,
        availableQuantity: newVariant.availableQuantity,
        unitCost: newVariant.cost,
        totalValue: newVariant.stockQuantity * newVariant.cost,
        lastMovementDate: new Date().toISOString(),
        reorderPoint: Math.max(10, Math.floor(newVariant.stockQuantity * 0.2)),
        maxStock: newVariant.stockQuantity * 2,
        lotNumbers: [`LOT_${Date.now()}`]
      }

      variantStocks.value.push(stock)

      // 重新生成库存预警
      generateStockAlerts()

      return {
        success: true,
        message: '物料变体创建成功',
        data: newVariant
      }
    } catch (error) {
      return {
        success: false,
        message: '物料变体创建失败',
        errors: [error instanceof Error ? error.message : '未知错误']
      }
    }
  }

  /**
   * 清除错误状态
   */
  const clearErrors = (): void => {
    templatesError.value = null
    variantsError.value = null
    stocksError.value = null
  }

  /**
   * 重置所有数据
   */
  const resetAllData = (): void => {
    materialTemplates.value = []
    selectedTemplateId.value = null
    materialVariants.value = []
    selectedVariantId.value = null
    variantStocks.value = []
    stockMoves.value = []
    wasteStocks.value = []
    stockAlerts.value = []
    reorderRules.value = []
    suppliers.value = []
    stockLocations.value = []
    searchQuery.value = ''
    currentFilter.value = {}
    searchResults.value = null
    clearErrors()
  }

  /**
   * 加载供应商数据
   */
  const loadSuppliers = async (): Promise<boolean> => {
    try {
      // 从已加载的物料变体中提取供应商信息
      const uniqueSuppliers = new Map<string, Supplier>()
      
      materialVariants.value.forEach(variant => {
        if (variant.supplier && !uniqueSuppliers.has(variant.supplier.id)) {
          uniqueSuppliers.set(variant.supplier.id, variant.supplier)
        }
      })

      suppliers.value = Array.from(uniqueSuppliers.values())
      return true
    } catch (error) {
      console.error('加载供应商数据失败:', error)
      return false
    }
  }

  /**
   * 加载库存位置数据
   */
  const loadStockLocations = async (): Promise<boolean> => {
    try {
      // 创建默认的库存位置
      const locations: StockLocation[] = [
        {
          id: 'location_main',
          name: '主仓库',
          code: 'MAIN',
          type: 'warehouse',
          isActive: true
        },
        {
          id: 'location_workshop',
          name: '生产车间',
          code: 'WORKSHOP',
          type: 'workshop',
          isActive: true
        },
        {
          id: 'location_staging',
          name: '暂存区',
          code: 'STAGING',
          type: 'staging',
          isActive: true
        }
      ]

      stockLocations.value = locations
      return true
    } catch (error) {
      console.error('加载库存位置数据失败:', error)
      return false
    }
  }

  /**
   * 初始化物料变体数据
   */
  const initializeMaterialVariantData = async (): Promise<void> => {
    await Promise.all([
      loadMaterialTemplates(),
      loadMaterialVariants()
    ])

    // 在变体加载完成后加载其他数据
    if (materialVariants.value.length > 0) {
      await Promise.all([
        loadVariantStocks(),
        loadSuppliers(),
        loadStockLocations()
      ])
    }
  }

  return {
    // 状态
    materialTemplates,
    selectedTemplateId,
    materialVariants,
    selectedVariantId,
    variantStocks,
    stockMoves,
    wasteStocks,
    stockAlerts,
    reorderRules,
    suppliers,
    stockLocations,
    
    // 加载状态
    isLoadingTemplates,
    isLoadingVariants,
    isLoadingStocks,
    
    // 错误状态
    templatesError,
    variantsError,
    stocksError,
    
    // 搜索和过滤
    searchQuery,
    currentFilter,
    searchResults,
    
    // 计算属性
    selectedTemplate,
    selectedVariant,
    activeTemplates,
    activeVariants,
    variantsForSelectedTemplate,
    lowStockVariants,
    outOfStockVariants,
    criticalStockAlerts,
    totalStockValue,
    stockStatistics,
    
    // 方法
    setSelectedTemplate,
    setSelectedVariant,
    loadMaterialTemplates,
    loadMaterialVariants,
    loadVariantStocks,
    loadSuppliers,
    loadStockLocations,
    searchMaterialVariants,
    updateVariantStock,
    addMaterialVariant,
    clearErrors,
    resetAllData,
    initializeMaterialVariantData
  }
})
