import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  Product,
  ProductFilters
} from '@/types/product';
import { productService } from '@/services/productService';

// 产品管理Store
export const useProductStore = defineStore('product', () => {
  const products = ref<Product[]>([]);
  const currentProduct = ref<Product | null>(null);
  const filters = ref<ProductFilters>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  const filteredProducts = computed(() => {
    if (!filters.value) return products.value;

    return products.value.filter(product => {
      if (filters.value.productStructureId && product.productStructureId !== filters.value.productStructureId) {
        return false;
      }
      if (filters.value.category && product.category !== filters.value.category) {
        return false;
      }
      if (filters.value.lifecycle && product.lifecycle !== filters.value.lifecycle) {
        return false;
      }
      if (filters.value.status && product.status !== filters.value.status) {
        return false;
      }
      if (filters.value.search) {
        const searchLower = filters.value.search.toLowerCase();
        return (
          product.name.toLowerCase().includes(searchLower) ||
          product.code.toLowerCase().includes(searchLower) ||
          product.description?.toLowerCase().includes(searchLower)
        );
      }
      if (filters.value.tags && filters.value.tags.length > 0) {
        return filters.value.tags.some(tag => product.tags.includes(tag));
      }
      return true;
    });
  });

  const loadProducts = async (newFilters?: ProductFilters) => {
    loading.value = true;
    error.value = null;

    try {
      if (newFilters) {
        filters.value = newFilters;
      }
      products.value = await productService.getProducts(filters.value);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载产品失败';
      console.error('Error loading products:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadProductById = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      currentProduct.value = await productService.getProductById(id);
      if (!currentProduct.value) {
        error.value = `未找到ID为 ${id} 的产品`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载产品详情失败';
      console.error('Error loading product:', err);
    } finally {
      loading.value = false;
    }
  };

  const createProduct = async (productData: Partial<Product>) => {
    loading.value = true;
    error.value = null;

    try {
      const newProduct = await productService.createProduct(productData);
      products.value.push(newProduct);
      return newProduct;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建产品失败';
      console.error('Error creating product:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    products,
    currentProduct,
    filters,
    loading,
    error,
    filteredProducts,
    loadProducts,
    loadProductById,
    createProduct
  };
});

