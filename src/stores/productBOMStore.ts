import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { 
  QuoteBOM, 
  ProductionBOM, 
  QuoteBOMFilters, 
  ProductionBOMFilters 
} from '@/types/product';
import { 
  quoteBOMService, 
  productionBOMService 
} from '@/services/productService';

// BOM管理Store
export const useBOMStore = defineStore('bom', () => {
  const quoteBOMs = ref<QuoteBOM[]>([]);
  const productionBOMs = ref<ProductionBOM[]>([]);
  const currentQuoteBOM = ref<QuoteBOM | null>(null);
  const currentProductionBOM = ref<ProductionBOM | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const loadQuoteBOMs = async (filters?: QuoteBOMFilters) => {
    loading.value = true;
    error.value = null;

    try {
      quoteBOMs.value = await quoteBOMService.getQuoteBOMs(filters);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载报价BOM失败';
      console.error('Error loading quote BOMs:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadProductionBOMs = async (filters?: ProductionBOMFilters) => {
    loading.value = true;
    error.value = null;

    try {
      productionBOMs.value = await productionBOMService.getProductionBOMs(filters);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载生产BOM失败';
      console.error('Error loading production BOMs:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadQuoteBOMById = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      currentQuoteBOM.value = await quoteBOMService.getQuoteBOMById(id);
      if (!currentQuoteBOM.value) {
        error.value = `未找到ID为 ${id} 的报价BOM`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载报价BOM详情失败';
      console.error('Error loading quote BOM:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadProductionBOMById = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      currentProductionBOM.value = await productionBOMService.getProductionBOMById(id);
      if (!currentProductionBOM.value) {
        error.value = `未找到ID为 ${id} 的生产BOM`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载生产BOM详情失败';
      console.error('Error loading production BOM:', err);
    } finally {
      loading.value = false;
    }
  };

  const convertToProductionBOM = async (quoteBOMId: string) => {
    loading.value = true;
    error.value = null;

    try {
      const productionBOM = await productionBOMService.convertFromQuoteBOM(quoteBOMId);
      productionBOMs.value.push(productionBOM);
      return productionBOM;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '转换生产BOM失败';
      console.error('Error converting to production BOM:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const clearError = () => {
    error.value = null;
  };

  const reset = () => {
    quoteBOMs.value = [];
    productionBOMs.value = [];
    currentQuoteBOM.value = null;
    currentProductionBOM.value = null;
    loading.value = false;
    error.value = null;
  };

  return {
    quoteBOMs,
    productionBOMs,
    currentQuoteBOM,
    currentProductionBOM,
    loading,
    error,
    loadQuoteBOMs,
    loadProductionBOMs,
    loadQuoteBOMById,
    loadProductionBOMById,
    convertToProductionBOM,
    clearError,
    reset
  };
});
