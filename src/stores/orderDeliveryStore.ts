import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  OrderDeliveryEntity,
  DeliveryPhase,
  OrderDeliveryStatus,
  ActionSuggestion,
  DeliveryAnomaly,
  AutomationResult
} from '@/types/order-delivery';

export const useOrderDeliveryStore = defineStore('orderDelivery', () => {
  // 基础状态
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  // 订单数据
  const orders = ref<OrderDeliveryEntity[]>([]);
  const currentOrder = ref<OrderDeliveryEntity | null>(null);
  const selectedOrderIds = ref<string[]>([]);
  
  // 筛选和搜索
  const searchQuery = ref('');
  const statusFilter = ref<string>('all');
  const phaseFilter = ref<string>('all');
  const priorityFilter = ref<string>('all');
  
  // 智能建议和异常
  const actionSuggestions = ref<ActionSuggestion[]>([]);
  const deliveryAnomalies = ref<DeliveryAnomaly[]>([]);
  
  // 计算属性
  const filteredOrders = computed(() => {
    return orders.value.filter(order => {
      const matchesSearch = !searchQuery.value || 
        order.orderNumber.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        order.customerInfo.name.toLowerCase().includes(searchQuery.value.toLowerCase());
      
      const matchesStatus = statusFilter.value === 'all' || 
        order.deliveryStatus.primaryStatus === statusFilter.value;
      
      const matchesPhase = phaseFilter.value === 'all' || 
        order.currentPhase === phaseFilter.value;
      
      const matchesPriority = priorityFilter.value === 'all' || 
        order.customerInfo.priority === priorityFilter.value;
      
      return matchesSearch && matchesStatus && matchesPhase && matchesPriority;
    });
  });
  
  const orderStats = computed(() => {
    const stats = {
      total: orders.value.length,
      planning: 0,
      scheduling: 0,
      executing: 0,
      delivered: 0,
      urgent: 0,
      delayed: 0
    };
    
    orders.value.forEach(order => {
      switch (order.currentPhase) {
        case 'planning':
          stats.planning++;
          break;
        case 'scheduling':
          stats.scheduling++;
          break;
        case 'executing':
          stats.executing++;
          break;
        case 'delivered':
          stats.delivered++;
          break;
      }
      
      if (order.customerInfo.priority === 'urgent') {
        stats.urgent++;
      }
      
      // 检查是否延期
      if (order.timeline.estimatedCompletion && 
          new Date(order.timeline.estimatedCompletion) < new Date()) {
        stats.delayed++;
      }
    });
    
    return stats;
  });
  
  // Actions
  const initializeStore = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      // 加载订单数据
      await loadOrders();
      
      // 生成智能建议
      await generateActionSuggestions();
      
      // 检测异常
      await detectAnomalies();
      
      console.log(`订单交付管理中心已初始化，共加载 ${orders.value.length} 个订单`);
    } catch (err) {
      error.value = '初始化失败: ' + (err as Error).message;
      console.error('初始化订单交付管理中心失败:', err);
    } finally {
      loading.value = false;
    }
  };
  
  const loadOrders = async () => {
    // 从生产工单系统加载数据，转换为订单交付实体
    // 这里应该调用生产工单的API，然后转换数据格式
    const mockOrders: OrderDeliveryEntity[] = [
      {
        id: 'delivery-001',
        orderNumber: 'WO-2024-001', // 生产工单号
        customerInfo: {
          id: 'customer-001',
          name: '华润置地',
          contact: '张经理',
          phone: '138-0000-0001',
          address: '深圳市南山区',
          priority: 'high'
        },
        orderItems: [
          {
            id: 'item-001',
            specifications: {
              length: 2000,
              width: 1500,
              thickness: 8,
              glassType: 'low_e',
              color: '透明',
              edgeWork: 'polished'
            },
            quantity: 50,
            unitPrice: 280,
            totalAmount: 14000,
            deliveryDate: '2024-03-15',
            notes: '幕墙用钢化玻璃'
          }
        ],
        deliveryStatus: {
          primaryStatus: 'planning',
          planningSubStatus: 'material_analysis'
        },
        currentPhase: 'planning',
        qualityRecords: [],
        timeline: {
          events: [
            {
              id: 'event-001',
              name: '生产工单创建',
              type: 'milestone',
              scheduledTime: '2024-02-20T09:00:00',
              actualTime: '2024-02-20T09:15:00',
              status: 'completed',
              description: '从客户订单ORD-2024-001生成生产工单',
              responsible: '计划部'
            },
            {
              id: 'event-002',
              name: '工单发布',
              type: 'milestone',
              scheduledTime: '2024-02-20T14:00:00',
              actualTime: '2024-02-20T14:30:00',
              status: 'completed',
              description: '生产工单已发布，进入交付管理',
              responsible: '生产部'
            },
            {
              id: 'event-003',
              name: '交付计划制定',
              type: 'task',
              scheduledTime: '2024-02-21T10:00:00',
              status: 'in_progress',
              description: '制定详细的交付计划',
              responsible: '计划部'
            }
          ],
          currentEvent: 'event-003',
          estimatedCompletion: '2024-03-15T17:00:00'
        },
        createdAt: '2024-02-20T09:00:00',
        updatedAt: '2024-02-21T14:30:00',
        createdBy: '系统管理员',
        totalValue: 14000,
        estimatedProfit: 2800
      },
      {
        id: 'delivery-002',
        orderNumber: 'WO-2024-002', // 生产工单号
        customerInfo: {
          id: 'customer-002',
          name: '万科集团',
          contact: '李总监',
          phone: '138-0000-0002',
          address: '广州市天河区',
          priority: 'urgent'
        },
        orderItems: [
          {
            id: 'item-002',
            specifications: {
              length: 1800,
              width: 1200,
              thickness: 6,
              glassType: 'clear',
              color: '透明',
              edgeWork: 'ground'
            },
            quantity: 80,
            unitPrice: 180,
            totalAmount: 14400,
            deliveryDate: '2024-03-10',
            notes: '门窗用钢化玻璃'
          }
        ],
        deliveryStatus: {
          primaryStatus: 'scheduled',
          schedulingSubStatus: 'cutting_optimization'
        },
        currentPhase: 'scheduling',
        qualityRecords: [],
        timeline: {
          events: [
            {
              id: 'event-003',
              name: '订单确认',
              type: 'milestone',
              scheduledTime: '2024-02-18T09:00:00',
              actualTime: '2024-02-18T09:00:00',
              status: 'completed',
              description: '客户订单确认完成',
              responsible: '销售部'
            },
            {
              id: 'event-004',
              name: '交付计划制定',
              type: 'task',
              scheduledTime: '2024-02-19T10:00:00',
              actualTime: '2024-02-19T11:30:00',
              status: 'completed',
              description: '交付计划制定完成',
              responsible: '计划部'
            },
            {
              id: 'event-005',
              name: '生产排程优化',
              type: 'task',
              scheduledTime: '2024-02-20T09:00:00',
              status: 'in_progress',
              description: '进行生产排程和切割优化',
              responsible: '生产部'
            }
          ],
          currentEvent: 'event-005',
          estimatedCompletion: '2024-03-10T17:00:00'
        },
        createdAt: '2024-02-18T09:00:00',
        updatedAt: '2024-02-20T16:45:00',
        createdBy: '系统管理员',
        totalValue: 14400,
        estimatedProfit: 2880
      }
    ];
    
    orders.value = mockOrders;
  };
  
  const selectOrder = (orderId: string) => {
    const order = orders.value.find(o => o.id === orderId);
    if (order) {
      currentOrder.value = order;
      console.log('选中订单:', order.orderNumber);
    }
  };
  
  const transitionOrderPhase = async (orderId: string, targetPhase: DeliveryPhase) => {
    const order = orders.value.find(o => o.id === orderId);
    if (!order) return;
    
    loading.value = true;
    try {
      // 模拟状态转换逻辑
      order.currentPhase = targetPhase;
      
      // 更新状态
      switch (targetPhase) {
        case 'planning':
          order.deliveryStatus.primaryStatus = 'planning';
          break;
        case 'scheduling':
          order.deliveryStatus.primaryStatus = 'scheduled';
          break;
        case 'executing':
          order.deliveryStatus.primaryStatus = 'executing';
          break;
        case 'delivered':
          order.deliveryStatus.primaryStatus = 'delivered';
          break;
      }
      
      order.updatedAt = new Date().toISOString();
      
      // 如果是当前选中的订单，更新当前订单
      if (currentOrder.value?.id === orderId) {
        currentOrder.value = { ...order };
      }
      
      console.log(`订单 ${order.orderNumber} 状态已更新为: ${targetPhase}`);
    } catch (err) {
      error.value = '状态转换失败: ' + (err as Error).message;
    } finally {
      loading.value = false;
    }
  };
  
  const generateActionSuggestions = async () => {
    // 模拟生成智能建议
    actionSuggestions.value = [
      {
        id: 'suggestion-001',
        type: 'optimization',
        title: '批量优化建议',
        description: '发现2个订单使用相同规格玻璃，建议合并生产以提高效率',
        impact: '预计节省15%的切割时间',
        effort: 'low',
        priority: 8,
        action: () => console.log('执行批量优化')
      },
      {
        id: 'suggestion-002',
        type: 'warning',
        title: '交期风险提醒',
        description: '订单ORD-2024-002存在交期风险，建议调整生产优先级',
        impact: '避免延期交付',
        effort: 'medium',
        priority: 9,
        action: () => console.log('调整生产优先级')
      }
    ];
  };
  
  const detectAnomalies = async () => {
    // 模拟异常检测
    deliveryAnomalies.value = [
      {
        id: 'anomaly-001',
        type: 'delay_risk',
        severity: 'medium',
        description: '订单ORD-2024-002的生产进度落后于计划',
        affectedOrders: ['order-002'],
        suggestedActions: ['增加生产资源', '调整生产计划', '与客户沟通延期'],
        detectedAt: new Date().toISOString()
      }
    ];
  };
  
  const executeAutomatedAction = async (actionId: string): Promise<AutomationResult> => {
    // 模拟自动化操作
    return {
      success: true,
      actionsExecuted: [actionId],
      results: { message: '操作执行成功' }
    };
  };
  
  const updateSearchQuery = (query: string) => {
    searchQuery.value = query;
  };
  
  const updateFilters = (filters: {
    status?: string;
    phase?: string;
    priority?: string;
  }) => {
    if (filters.status !== undefined) statusFilter.value = filters.status;
    if (filters.phase !== undefined) phaseFilter.value = filters.phase;
    if (filters.priority !== undefined) priorityFilter.value = filters.priority;
  };
  
  const resetFilters = () => {
    searchQuery.value = '';
    statusFilter.value = 'all';
    phaseFilter.value = 'all';
    priorityFilter.value = 'all';
  };
  
  return {
    // State
    loading,
    error,
    orders,
    currentOrder,
    selectedOrderIds,
    searchQuery,
    statusFilter,
    phaseFilter,
    priorityFilter,
    actionSuggestions,
    deliveryAnomalies,
    
    // Computed
    filteredOrders,
    orderStats,
    
    // Actions
    initializeStore,
    loadOrders,
    selectOrder,
    transitionOrderPhase,
    generateActionSuggestions,
    detectAnomalies,
    executeAutomatedAction,
    updateSearchQuery,
    updateFilters,
    resetFilters
  };
});
