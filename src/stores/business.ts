/**
 * 业务数据状态管理Store
 * 管理核心业务数据，如物料变体、订单、库存等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { MaterialVariant } from '@/types/material-variant'
import {
  CrmDataService,
  InventoryDataService,
} from '../utils/dataService'

// 订单状态类型
export type OrderStatus = 'draft' | 'confirmed' | 'in_production' | 'completed' | 'cancelled'

// 简化的订单接口
export interface Order {
  id: string
  customerId: string
  customerName: string
  status: OrderStatus
  totalAmount: number
  createdAt: string
  updatedAt?: string
  items: OrderItem[]
}

// 订单项接口
export interface OrderItem {
  id: string
  materialVariantId: string
  quantity: number
  unitPrice: number
  totalPrice: number
  specifications?: Record<string, any>
}

// 库存项接口
export interface StockItem {
  id: string
  materialId: string
  materialName: string
  quantity: number
  unit: string
  location: string
  minStock: number
  maxStock: number
  lastUpdated: string
}

// 客户接口
export interface Customer {
  id: string
  name: string
  type: string
  industry: string
  contactPerson: string
  phone: string
  email: string
  address: string
  isActive: boolean
}

export const useBusinessStore = defineStore('business', () => {
  // 状态定义
  const selectedMaterialVariant = ref<MaterialVariant | null>(null)
  const currentOrder = ref<Order | null>(null)
  const recentOrders = ref<Order[]>([])
  const stockItems = ref<StockItem[]>([])
  const customers = ref<Customer[]>([])
  const materialVariants = ref<any[]>([])
  
  // 缓存状态
  const isLoadingOrders = ref<boolean>(false)
  const isLoadingStock = ref<boolean>(false)
  const isLoadingCustomers = ref<boolean>(false)
  const isLoadingMaterials = ref<boolean>(false)
  
  // 错误状态
  const ordersError = ref<string | null>(null)
  const stockError = ref<string | null>(null)
  const customersError = ref<string | null>(null)
  const materialsError = ref<string | null>(null)

  // 计算属性
  const lowStockItems = computed<StockItem[]>(() => {
    return stockItems.value.filter(item => item.quantity <= item.minStock)
  })

  const lowStockCount = computed<number>(() => {
    return lowStockItems.value.length
  })

  const activeCustomers = computed<Customer[]>(() => {
    return customers.value.filter(customer => customer.isActive)
  })

  const pendingOrders = computed<Order[]>(() => {
    return recentOrders.value.filter(order => 
      order.status === 'draft' || order.status === 'confirmed'
    )
  })

  const inProductionOrders = computed<Order[]>(() => {
    return recentOrders.value.filter(order => order.status === 'in_production')
  })

  const completedOrders = computed<Order[]>(() => {
    return recentOrders.value.filter(order => order.status === 'completed')
  })

  const ordersByStatus = computed(() => {
    const statusCounts = {
      draft: 0,
      confirmed: 0,
      in_production: 0,
      completed: 0,
      cancelled: 0
    }

    recentOrders.value.forEach(order => {
      statusCounts[order.status]++
    })

    return statusCounts
  })

  // Actions

  /**
   * 设置选中的物料变体
   */
  const setSelectedMaterialVariant = (variant: MaterialVariant | null): void => {
    selectedMaterialVariant.value = variant
  }

  /**
   * 设置当前订单
   */
  const setCurrentOrder = (order: Order | null): void => {
    currentOrder.value = order
  }

  /**
   * 加载最近订单（使用Mock数据服务）
   */
  const loadRecentOrders = async (limit: number = 50): Promise<boolean> => {
    try {
      isLoadingOrders.value = true
      ordersError.value = null

      // 使用CRM数据服务加载订单
      const response = await CrmDataService.getOrders({ pageSize: limit })

      if (response.success && response.data) {
        // 转换Mock数据格式到业务Store格式
        const orders: Order[] = response.data.map((mockOrder: any) => ({
          id: mockOrder.id,
          customerId: mockOrder.customerId,
          customerName: mockOrder.customerName,
          status: mockOrder.status as OrderStatus,
          totalAmount: mockOrder.totalAmount,
          createdAt: mockOrder.createdAt,
          updatedAt: mockOrder.updatedAt,
          items: mockOrder.items || []
        }))

        recentOrders.value = orders
        return true
      } else {
        throw new Error(response.message || '加载订单数据失败')
      }
    } catch (error) {
      ordersError.value = error instanceof Error ? error.message : '加载订单失败'
      return false
    } finally {
      isLoadingOrders.value = false
    }
  }

  /**
   * 加载库存数据（使用Mock数据服务）
   */
  const loadStockItems = async (): Promise<boolean> => {
    try {
      isLoadingStock.value = true
      stockError.value = null

      // 使用库存数据服务加载库存
      const response = await InventoryDataService.getStock()

      if (response.success && response.data) {
        // 转换Mock数据格式到业务Store格式
        const stocks: StockItem[] = response.data.map((mockStock: any) => ({
          id: mockStock.id,
          materialId: mockStock.materialId,
          materialName: mockStock.materialName,
          quantity: mockStock.quantity,
          unit: mockStock.unit,
          location: mockStock.location,
          minStock: mockStock.minStock || 10,
          maxStock: mockStock.maxStock || 100,
          lastUpdated: mockStock.lastUpdated || new Date().toISOString()
        }))

        stockItems.value = stocks
        return true
      } else {
        throw new Error(response.message || '加载库存数据失败')
      }
    } catch (error) {
      stockError.value = error instanceof Error ? error.message : '加载库存失败'
      return false
    } finally {
      isLoadingStock.value = false
    }
  }

  /**
   * 加载客户数据（使用Mock数据服务）
   */
  const loadCustomers = async (): Promise<boolean> => {
    try {
      isLoadingCustomers.value = true
      customersError.value = null

      // 使用CRM数据服务加载客户
      const response = await CrmDataService.getCustomers()

      if (response.success && response.data) {
        // 转换Mock数据格式到业务Store格式
        const customerList: Customer[] = response.data.map((mockCustomer: any) => ({
          id: mockCustomer.id,
          name: mockCustomer.name,
          type: mockCustomer.type,
          industry: mockCustomer.industry,
          contactPerson: mockCustomer.contactPerson,
          phone: mockCustomer.phone,
          email: mockCustomer.email,
          address: mockCustomer.address,
          isActive: mockCustomer.isActive !== false
        }))

        customers.value = customerList
        return true
      } else {
        throw new Error(response.message || '加载客户数据失败')
      }
    } catch (error) {
      customersError.value = error instanceof Error ? error.message : '加载客户失败'
      return false
    } finally {
      isLoadingCustomers.value = false
    }
  }

  /**
   * 加载物料数据（使用Mock数据服务）
   */
  const loadMaterials = async (): Promise<boolean> => {
    try {
      isLoadingMaterials.value = true
      materialsError.value = null

      // 直接从 Mock 数据加载物料数据
      const response = await fetch('/mock/metadata/materials.json')
      if (!response.ok) {
        throw new Error('无法加载物料数据')
      }

      const data = await response.json()
      
      if (data.materials) {
        // 简化的物料数据结构，用于业务 Store
        const materials: any[] = data.materials.map((material: any) => ({
          id: material.materialId,
          templateId: material.categoryId,
          name: material.displayName,
          category: material.categoryId,
          baseAttributes: material.attributes || {},
          variantCount: material.variants ? material.variants.length : 0,
          totalStock: material.variants ? material.variants.reduce((sum: number, v: unknown) => sum + (v.stock || 0), 0) : 0,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: undefined
        }))

        materialVariants.value = materials
        return true
      } else {
        throw new Error('物料数据格式错误')
      }
    } catch (error) {
      materialsError.value = error instanceof Error ? error.message : '加载物料失败'
      return false
    } finally {
      isLoadingMaterials.value = false
    }
  }

  /**
   * 更新订单状态
   */
  const updateOrderStatus = (orderId: string, status: OrderStatus): boolean => {
    const order = recentOrders.value.find(o => o.id === orderId)
    if (order) {
      order.status = status
      order.updatedAt = new Date().toISOString()
      return true
    }
    return false
  }

  /**
   * 添加新订单
   */
  const addOrder = (order: Omit<Order, 'id' | 'createdAt'>): string => {
    const id = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const newOrder: Order = {
      ...order,
      id,
      createdAt: new Date().toISOString()
    }
    
    recentOrders.value.unshift(newOrder)
    return id
  }

  /**
   * 更新库存数量
   */
  const updateStockQuantity = (stockId: string, quantity: number): boolean => {
    const stock = stockItems.value.find(s => s.id === stockId)
    if (stock) {
      stock.quantity = quantity
      stock.lastUpdated = new Date().toISOString()
      return true
    }
    return false
  }

  /**
   * 清除错误状态
   */
  const clearErrors = (): void => {
    ordersError.value = null
    stockError.value = null
    customersError.value = null
    materialsError.value = null
  }

  /**
   * 持久化业务数据
   */
  const persistBusinessData = (): void => {
    if (typeof window === 'undefined') return

    const businessData = {
      selectedMaterialVariant: selectedMaterialVariant.value,
      currentOrder: currentOrder.value,
      recentOrders: recentOrders.value,
      stockItems: stockItems.value,
      customers: customers.value,
      materialVariants: materialVariants.value,
      timestamp: Date.now()
    }

    try {
      localStorage.setItem('business_data', JSON.stringify(businessData))
    } catch (error) {
      console.error('持久化业务数据失败:', error)
    }
  }

  /**
   * 从本地存储恢复业务数据
   */
  const restoreBusinessData = (): boolean => {
    if (typeof window === 'undefined') return false

    try {
      const stored = localStorage.getItem('business_data')
      if (!stored) return false

      const businessData = JSON.parse(stored)

      // 检查数据是否过期（2小时）
      const isExpired = Date.now() - businessData.timestamp > 2 * 60 * 60 * 1000
      if (isExpired) {
        clearPersistedBusinessData()
        return false
      }

      // 恢复数据
      selectedMaterialVariant.value = businessData.selectedMaterialVariant
      currentOrder.value = businessData.currentOrder
      recentOrders.value = businessData.recentOrders || []
      stockItems.value = businessData.stockItems || []
      customers.value = businessData.customers || []
      materialVariants.value = businessData.materialVariants || []

      return true
    } catch (error) {
      console.error('恢复业务数据失败:', error)
      clearPersistedBusinessData()
      return false
    }
  }

  /**
   * 清除持久化的业务数据
   */
  const clearPersistedBusinessData = (): void => {
    if (typeof window === 'undefined') return
    localStorage.removeItem('business_data')
  }

  /**
   * 重置所有数据
   */
  const resetAllData = (): void => {
    selectedMaterialVariant.value = null
    currentOrder.value = null
    recentOrders.value = []
    stockItems.value = []
    customers.value = []
    materialVariants.value = []
    clearErrors()
    clearPersistedBusinessData()
  }

  /**
   * 初始化业务数据
   */
  const initializeBusinessData = async (): Promise<void> => {
    // 尝试从本地存储恢复数据
    const restored = restoreBusinessData()

    if (!restored) {
      // 如果没有缓存数据，加载基础数据
      await Promise.all([
        loadRecentOrders(20),
        loadStockItems(),
        loadCustomers(),
        loadMaterials()
      ])

      // 持久化加载的数据
      persistBusinessData()
    }
  }

  return {
    // 状态
    selectedMaterialVariant,
    currentOrder,
    recentOrders,
    stockItems,
    customers,
    materialVariants,
    
    // 加载状态
    isLoadingOrders,
    isLoadingStock,
    isLoadingCustomers,
    isLoadingMaterials,
    
    // 错误状态
    ordersError,
    stockError,
    customersError,
    materialsError,
    
    // 计算属性
    lowStockItems,
    lowStockCount,
    activeCustomers,
    pendingOrders,
    inProductionOrders,
    completedOrders,
    ordersByStatus,
    
    // 方法
    setSelectedMaterialVariant,
    setCurrentOrder,
    loadRecentOrders,
    loadStockItems,
    loadCustomers,
    loadMaterials,
    updateOrderStatus,
    addOrder,
    updateStockQuantity,
    clearErrors,
    resetAllData,
    persistBusinessData,
    restoreBusinessData,
    initializeBusinessData
  }
})
