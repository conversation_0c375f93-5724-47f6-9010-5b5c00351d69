import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { productStructureService } from '@/services/productService';
import type { 
  ProductStructure, 
  ProductStructureFilters 
} from '@/types/product-structure';

// 产品结构管理Store
export const useProductStructureStore = defineStore('productStructure', () => {
  const structures = ref<ProductStructure[]>([]);
  const currentStructure = ref<ProductStructure | null>(null);
  const filters = ref<ProductStructureFilters>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  const filteredStructures = computed(() => {
    if (!filters.value) return structures.value;

    return structures.value.filter(structure => {
      // 产品类型筛选
      if (filters.value.productType && filters.value.productType.length > 0) {
        if (!filters.value.productType.includes(structure.productType)) {
          return false;
        }
      }
      
      // 类别筛选
      if (filters.value.category && filters.value.category.length > 0) {
        if (!filters.value.category.includes(structure.category)) {
          return false;
        }
      }
      
      // 状态筛选
      if (filters.value.status && filters.value.status.length > 0) {
        if (!filters.value.status.includes(structure.status)) {
          return false;
        }
      }
      
      // 关键词搜索
      if (filters.value.keyword) {
        const searchLower = filters.value.keyword.toLowerCase();
        const matchesKeyword = (
          structure.name.toLowerCase().includes(searchLower) ||
          structure.code.toLowerCase().includes(searchLower) ||
          structure.description?.toLowerCase().includes(searchLower)
        );
        if (!matchesKeyword) {
          return false;
        }
      }
      
      // 标签筛选
      if (filters.value.tags && filters.value.tags.length > 0) {
        const hasMatchingTag = filters.value.tags.some(tag => structure.tags.includes(tag));
        if (!hasMatchingTag) {
          return false;
        }
      }
      
      // 应用场景筛选
      if (filters.value.applications && filters.value.applications.length > 0) {
        const hasMatchingApplication = filters.value.applications.some(app => structure.applications.includes(app));
        if (!hasMatchingApplication) {
          return false;
        }
      }
      
      // 创建人筛选
      if (filters.value.createdBy && filters.value.createdBy.length > 0) {
        if (!filters.value.createdBy.includes(structure.createdBy)) {
          return false;
        }
      }
      
      // 创建时间范围筛选
      if (filters.value.createdDateRange) {
        const createdDate = new Date(structure.createdAt);
        if (filters.value.createdDateRange.start) {
          const startDate = new Date(filters.value.createdDateRange.start);
          if (createdDate < startDate) {
            return false;
          }
        }
        if (filters.value.createdDateRange.end) {
          const endDate = new Date(filters.value.createdDateRange.end);
          if (createdDate > endDate) {
            return false;
          }
        }
      }
      
      return true;
    });
  });

  const loadStructures = async (filterParams?: ProductStructureFilters) => {
    loading.value = true;
    error.value = null;

    try {
      if (filterParams) {
        filters.value = filterParams;
      }
      
      const data = await productStructureService.getStructures(filters.value);
      structures.value = data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载产品结构失败';
      console.error('Error loading product structures:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadStructureById = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      const structure = await productStructureService.getStructureById(id);
      currentStructure.value = structure;
      return structure;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载产品结构失败';
      console.error('Error loading product structure:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const createStructure = async (structureData: Partial<ProductStructure>) => {
    loading.value = true;
    error.value = null;

    try {
      const newStructure = await productStructureService.createStructure(structureData);
      structures.value.push(newStructure);
      return newStructure;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建产品结构失败';
      console.error('Error creating product structure:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateStructure = async (id: string, updates: Partial<ProductStructure>) => {
    loading.value = true;
    error.value = null;

    try {
      const updatedStructure = await productStructureService.updateStructure(id, updates);
      const index = structures.value.findIndex(s => s.id === id);
      if (index !== -1) {
        structures.value[index] = updatedStructure;
      }
      if (currentStructure.value?.id === id) {
        currentStructure.value = updatedStructure;
      }
      return updatedStructure;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新产品结构失败';
      console.error('Error updating product structure:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteStructure = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      await productStructureService.deleteStructure(id);
      structures.value = structures.value.filter(s => s.id !== id);
      if (currentStructure.value?.id === id) {
        currentStructure.value = null;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除产品结构失败';
      console.error('Error deleting product structure:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const setFilters = (newFilters: ProductStructureFilters) => {
    filters.value = newFilters;
  };

  const clearError = () => {
    error.value = null;
  };

  const reset = () => {
    structures.value = [];
    currentStructure.value = null;
    filters.value = {};
    loading.value = false;
    error.value = null;
  };

  return {
    structures,
    currentStructure,
    filters,
    loading,
    error,
    filteredStructures,
    loadStructures,
    loadStructureById,
    createStructure,
    updateStructure,
    deleteStructure,
    setFilters,
    clearError,
    reset
  };
});
