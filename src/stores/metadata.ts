import { defineStore } from "pinia";
import type {
  MetadataState,
  MaterialCategory,
  MaterialCategoryTreeNode,
  Material,
  CategoryOperationResult,
} from "@/types/material";
import { apiGet, getCategoryTree } from '@/utils/api'; // 导入封装的API

export const useMetadataStore = defineStore("metadata", {
  state: (): MetadataState => ({
    materialCategories: [],
    materials: [],
    categoryTree: [], // 原始树结构数据
    selectedCategoryId: null,
    selectedMaterialId: null,
    expandedCategoryIds: new Set<string>(),
    loading: false,
    error: null,
    operationDialogOpen: false,
    batchDialogOpen: false,
    currentOperation: null,
    editingCategory: null,
  }),

  getters: {
    // 返回带有 expanded 状态的分类树
    expandedCategoryTree: (state): MaterialCategoryTreeNode[] => {
      // 如果 categoryTree 未初始化，返回空数组
      if (!state.categoryTree || !Array.isArray(state.categoryTree)) {
        return [];
      }

      // 递归地为节点设置 expanded 状态
      const setExpanded = (nodes: MaterialCategoryTreeNode[]): MaterialCategoryTreeNode[] => {
        return nodes.map(node => ({
          ...node,
          expanded: state.expandedCategoryIds.has(node.categoryId),
          children: node.children ? setExpanded(node.children) : [],
        }));
      };
      return setExpanded(state.categoryTree);
    },

    // 获取可选择的叶子节点（具有attributeSchema的分类）
    selectableCategories: (state) => {
      return state.materialCategories.filter(
        (category) => !category.hasChildren && category.attributeSchema
      );
    },

    materialsForSelectedCategory: (state) => {
      if (!state.selectedCategoryId) return [];
      return state.materials.filter(
        (material) => material.categoryId === state.selectedCategoryId
      );
    },

    selectedMaterial: (state) => {
      if (!state.selectedMaterialId) return null;
      return (
        state.materials.find(
          (material) => material.materialId === state.selectedMaterialId
        ) || null
      );
    },

    selectedCategory: (state) => {
      if (!state.selectedCategoryId) return null;
      return (
        state.materialCategories.find(
          (category) => category.categoryId === state.selectedCategoryId
        ) || null
      );
    },

    // 获取分类路径面包屑
    getCategoryPath: (state) => (categoryId: string) => {
      const path: MaterialCategory[] = [];
      let currentId: string | null = categoryId;

      while (currentId) {
        const category = state.materialCategories.find(
          (c) => c.categoryId === currentId
        );
        if (category) {
          path.unshift(category);
          currentId = category.parentId;
        } else {
          break;
        }
      }

      return path;
    },
  },

  actions: {
    async fetchMetadata() {
      this.loading = true;
      this.error = null;

      try {
        // 并行加载分类树和物料数据
        const [categoryTreeRes, materialsRes] = await Promise.all([
          getCategoryTree(), // 使用新的API获取树
          apiGet<{ materials: Material[] }>('metadata/materials.json'),
        ]);

        if (!materialsRes.success) {
          throw new Error(materialsRes.message || "Failed to fetch materials");
        }
        this.materials = materialsRes.data.materials || [];

        if (!categoryTreeRes.success) {
          throw new Error(categoryTreeRes.message || "Failed to fetch category tree");
        }
        this.categoryTree = categoryTreeRes.data || [];
        
        // 从树结构中扁平化出 categories 列表，用于其他地方
        const flattenTree = (nodes: MaterialCategoryTreeNode[]): MaterialCategory[] => {
            const list: MaterialCategory[] = [];
            const traverse = (node: MaterialCategoryTreeNode) => {
                const { children, ...rest } = node;
                list.push(rest as MaterialCategory);
                if (children) {
                    children.forEach(traverse);
                }
            };
            nodes.forEach(traverse);
            return list;
        };
        this.materialCategories = flattenTree(this.categoryTree);

        // 初始化时展开根节点
        const rootCategories = this.materialCategories.filter(
          (c) => c.level === 0
        );
        rootCategories.forEach((category) => {
          this.expandedCategoryIds.add(category.categoryId);
        });

      } catch (error) {
        this.error =
          error instanceof Error ? error.message : "Unknown error occurred";
        console.error("Error fetching metadata:", error);
      } finally {
        this.loading = false;
      }
    },

    toggleCategoryExpansion(categoryId: string) {
      if (this.expandedCategoryIds.has(categoryId)) {
        this.expandedCategoryIds.delete(categoryId);
      } else {
        this.expandedCategoryIds.add(categoryId);
      }
    },

    selectCategory(categoryId: string | null) {
      // 只能选择叶子节点（具有attributeSchema的分类）
      if (categoryId) {
        const category = this.materialCategories.find(
          (c) => c.categoryId === categoryId
        );
        if (category && category.hasChildren) {
          // 如果是父节点，则切换展开/折叠状态
          this.toggleCategoryExpansion(categoryId);
          return;
        }
      }

      this.selectedCategoryId = categoryId;
      // 清空选中的物料
      this.selectedMaterialId = null;
    },

    selectMaterial(materialId: string | null) {
      this.selectedMaterialId = materialId;
    },

    clearSelection() {
      this.selectedCategoryId = null;
      this.selectedMaterialId = null;
    },

    expandCategoryPath(categoryId: string) {
      // 展开到指定分类的完整路径
      const path = this.getCategoryPath(categoryId);
      path.forEach((category) => {
        if (category.hasChildren) {
          this.expandedCategoryIds.add(category.categoryId);
        }
      });
    },

    expandAllCategories() {
      // 展开所有具有子节点的分类
      this.materialCategories.forEach((category) => {
        if (category.hasChildren) {
          this.expandedCategoryIds.add(category.categoryId);
        }
      });
    },

    collapseAllCategories() {
      // 折叠所有分类（保留根节点）
      this.materialCategories.forEach((category) => {
        if (category.hasChildren && category.level > 0) {
          this.expandedCategoryIds.delete(category.categoryId);
        }
      });
    },

    // 新增分类操作方法
    async addCategory(
      category: MaterialCategory
    ): Promise<CategoryOperationResult> {
      try {
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 生成新的分类数据
        const newCategory: MaterialCategory = {
          ...category,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: "current-user",
          materialCount: 0,
          isSystemDefault: false,
          sortOrder: this.materialCategories.length + 1,
        };

        this.materialCategories.push(newCategory);
        // TODO: Should refetch or update tree structure
        await this.fetchMetadata(); // Easiest way for mock is to refetch

        return {
          success: true,
          message: "分类创建成功",
          data: newCategory,
        };
      } catch (error) {
        return {
          success: false,
          message: "分类创建失败",
          errors: [error instanceof Error ? error.message : "未知错误"],
        };
      }
    },

    async updateCategory(
      category: MaterialCategory
    ): Promise<CategoryOperationResult> {
      try {
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 500));

        const index = this.materialCategories.findIndex(
          (c) => c.categoryId === category.categoryId
        );
        if (index === -1) {
          throw new Error("分类不存在");
        }

        // 更新分类数据
        this.materialCategories[index] = { ...this.materialCategories[index], ...category, updatedAt: new Date().toISOString() };
        
        await this.fetchMetadata(); // Easiest way for mock is to refetch

        return {
          success: true,
          message: "分类更新成功",
          data: this.materialCategories[index],
        };
      } catch (error) {
        return {
          success: false,
          message: "分类更新失败",
          errors: [error instanceof Error ? error.message : "未知错误"],
        };
      }
    },

    async deleteCategory(categoryId: string): Promise<CategoryOperationResult> {
      try {
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 500));

        const categoryIndex = this.materialCategories.findIndex(
          (c) => c.categoryId === categoryId
        );
        if (categoryIndex === -1) {
          throw new Error("分类不存在");
        }

        const category = this.materialCategories[categoryIndex];

        // 检查是否有子分类
        const hasChildren = this.materialCategories.some(c => c.parentId === categoryId);
        if (hasChildren) {
          throw new Error("不能删除有子分类的分类");
        }

        // 检查是否有关联的物料
        const hasRelatedMaterials = this.materials.some(
          (m) => m.categoryId === categoryId
        );
        if (hasRelatedMaterials) {
          throw new Error("不能删除有关联物料的分类");
        }

        // 删除分类
        this.materialCategories.splice(categoryIndex, 1);
        
        await this.fetchMetadata(); // Easiest way for mock is to refetch

        // 如果删除的分类是当前选中的分类，清空选择
        if (this.selectedCategoryId === categoryId) {
          this.selectedCategoryId = null;
          this.selectedMaterialId = null;
        }

        return {
          success: true,
          message: "分类删除成功",
        };
      } catch (error) {
        return {
          success: false,
          message: "分类删除失败",
          errors: [error instanceof Error ? error.message : "未知错误"],
        };
      }
    },

    async resetAllCategories(): Promise<CategoryOperationResult> {
      try {
        // 模拟重新加载初始数据
        await this.fetchMetadata();

        // 清空选择状态
        this.selectedCategoryId = null;
        this.selectedMaterialId = null;
        this.expandedCategoryIds.clear();

        return {
          success: true,
          message: "分类重置成功",
        };
      } catch (error) {
        return {
          success: false,
          message: "分类重置失败",
          errors: [error instanceof Error ? error.message : "未知错误"],
        };
      }
    },

    // 分类验证方法
    validateCategory(category: MaterialCategory): {
      isValid: boolean;
      errors: string[];
    } {
      const errors: string[] = [];

      // 基本字段验证
      if (!category.categoryId?.trim()) {
        errors.push("分类编码不能为空");
      } else if (!/^[A-Z_][A-Z0-9_]*$/.test(category.categoryId)) {
        errors.push(
          "分类编码必须以字母或下划线开头，只能包含大写字母、数字和下划线"
        );
      }

      if (!category.categoryName?.trim()) {
        errors.push("分类名称不能为空");
      }

      // 检查分类编码是否重复
      const existing = this.materialCategories.find(
        (c) => c.categoryId === category.categoryId && c.categoryId !== this.editingCategory?.categoryId
      );
      if (existing) {
        errors.push("分类编码已存在");
      }

      // 属性模板验证
      if (category.attributeSchema) {
        const { baseAttributes, variantAttributes } = category.attributeSchema;

        // 检查属性名称重复
        const allAttributes = [...baseAttributes, ...variantAttributes];
        const attributeNames = allAttributes.map((attr) => attr.name);
        const duplicates = attributeNames.filter(
          (name, index) => name && attributeNames.indexOf(name) !== index
        );

        if (duplicates.length > 0) {
          errors.push(`属性名称重复: ${duplicates.join(", ")}`);
        }

        // 检查属性完整性
        allAttributes.forEach((attr, index) => {
          if (!attr.name?.trim()) {
            errors.push(`第${index + 1}个属性名称不能为空`);
          }
          if (!attr.type) {
            errors.push(`属性"${attr.name}"的类型不能为空`);
          }
          if (
            attr.type === "select" &&
            (!attr.options || attr.options.length === 0)
          ) {
            errors.push(`选择型属性"${attr.name}"必须定义可选值`);
          }
          if (attr.type === "number") {
            if (
              attr.minValue !== undefined &&
              attr.maxValue !== undefined &&
              attr.minValue >= attr.maxValue
            ) {
              errors.push(`属性"${attr.name}"的最小值必须小于最大值`);
            }
          }
        });
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
});
