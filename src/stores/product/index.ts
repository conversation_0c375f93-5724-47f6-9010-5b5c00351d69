// 产品相关Store的统一导出文件
export { useProductStore } from '../productStore';
export { useComponentStore } from '../productComponentStore';
export { useAssemblyStore } from '../productAssemblyStore';
export { useProductStructureStore } from '../productStructureStore';
export { useBOMStore } from '../productBOMStore';

// 类型导出
export type {
  Product,
  Component,
  Assembly,
  ProductStructure,
  QuoteBOM,
  ProductionBOM,
  ProductFilters,
  ComponentFilters,
  AssemblyFilters,
  ProductStructureFilters,
  QuoteBOMFilters,
  ProductionBOMFilters
} from '@/types/product';

export type {
  ProductStructure as ProductStructureType,
  ProductStructureFilters as ProductStructureFiltersType
} from '@/types/product-structure';
