# 产品管理Store架构

本目录包含了产品管理相关的所有Pinia Store，按功能模块进行了拆分，提高了代码的可维护性和模块化程度。

## Store文件结构

```
src/stores/
├── productStore.ts              # 产品管理Store
├── productComponentStore.ts     # 组件管理Store  
├── productAssemblyStore.ts      # 构件管理Store
├── productStructureStore.ts     # 产品结构管理Store
├── productBOMStore.ts          # BOM管理Store
└── product/
    ├── index.ts                # 统一导出文件
    └── README.md               # 本文档
```

## Store功能说明

### 1. useProductStore (productStore.ts)
- **功能**：管理产品实例数据
- **主要状态**：products, currentProduct, filters, loading, error
- **主要方法**：loadProducts, loadProductById, createProduct
- **适用场景**：产品列表展示、产品详情查看、产品创建

### 2. useComponentStore (productComponentStore.ts)
- **功能**：管理组件库数据
- **主要状态**：components, currentComponent, filters, loading, error
- **主要方法**：loadComponents, createComponent, updateComponent, deleteComponent
- **计算属性**：filteredComponents, componentsByType
- **适用场景**：组件库管理、组件选择、组件编辑

### 3. useAssemblyStore (productAssemblyStore.ts)
- **功能**：管理构件数据
- **主要状态**：assemblies, currentAssembly, filters, loading, error
- **主要方法**：loadAssemblies, createAssembly, updateAssembly, deleteAssembly
- **计算属性**：filteredAssemblies
- **适用场景**：构件管理、构件设计、构件组装

### 4. useProductStructureStore (productStructureStore.ts)
- **功能**：管理产品结构定义
- **主要状态**：structures, currentStructure, filters, loading, error
- **主要方法**：loadStructures, createStructure, updateStructure, deleteStructure
- **计算属性**：filteredStructures
- **适用场景**：产品结构设计、结构模板管理、结构验证

### 5. useBOMStore (productBOMStore.ts)
- **功能**：管理BOM数据
- **主要状态**：quoteBOMs, productionBOMs, currentQuoteBOM, currentProductionBOM
- **主要方法**：loadQuoteBOMs, loadProductionBOMs, convertToProductionBOM
- **适用场景**：BOM生成、成本计算、生产计划

## 使用方式

### 方式1：独立导入
```typescript
import { useProductStore } from '@/stores/productStore';
import { useComponentStore } from '@/stores/productComponentStore';
import { useAssemblyStore } from '@/stores/productAssemblyStore';
import { useProductStructureStore } from '@/stores/productStructureStore';
import { useBOMStore } from '@/stores/productBOMStore';
```

### 方式2：统一导入
```typescript
import {
  useProductStore,
  useComponentStore,
  useAssemblyStore,
  useProductStructureStore,
  useBOMStore
} from '@/stores/product';
```

## 使用示例

### 组件管理
```typescript
const componentStore = useComponentStore();

// 加载组件
await componentStore.loadComponents({ componentType: 'frame' });

// 访问数据
const components = componentStore.filteredComponents;
const loading = componentStore.loading;

// 创建组件
const newComponent = await componentStore.createComponent({
  code: 'COMP_001',
  name: '新组件'
});
```

### 产品结构管理
```typescript
const structureStore = useProductStructureStore();

// 加载产品结构
await structureStore.loadStructures();

// 筛选结构
structureStore.setFilters({
  productType: ['window'],
  status: ['active']
});

// 创建结构
const newStructure = await structureStore.createStructure({
  code: 'PS_001',
  name: '新产品结构'
});
```

## 架构优势

### 1. 模块化
- 每个Store专注于单一职责
- 减少了代码耦合
- 便于独立测试和维护

### 2. 类型安全
- 每个Store都有完整的TypeScript类型定义
- 编译时类型检查
- 更好的IDE支持

### 3. 可扩展性
- 新功能可以独立添加到对应Store
- 不影响其他模块的稳定性
- 便于团队协作开发

### 4. 性能优化
- 按需加载Store
- 减少不必要的状态监听
- 更精确的响应式更新

## 迁移指南

如果您之前使用的是统一的productStore.ts文件，请按以下步骤迁移：

1. **更新导入语句**：
   ```typescript
   // 旧方式
   import { useComponentStore } from '@/stores/productStore';
   
   // 新方式
   import { useComponentStore } from '@/stores/productComponentStore';
   ```

2. **使用统一导入**（推荐）：
   ```typescript
   import {
     useComponentStore,
     useAssemblyStore,
     useProductStructureStore
   } from '@/stores/product';
   ```

3. **功能保持不变**：
   - 所有Store的API接口保持不变
   - 状态结构保持不变
   - 计算属性和方法保持不变

## 测试

运行以下命令测试所有Store：
```bash
npm test src/tests/stores/productStores.test.ts
```

测试覆盖：
- ✅ Store初始化测试
- ✅ 计算属性测试
- ✅ Store独立性测试
- ✅ 方法存在性测试
