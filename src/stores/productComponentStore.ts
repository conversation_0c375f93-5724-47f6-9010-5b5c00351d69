import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Component, ComponentFilters } from '@/types/product';
import { componentService } from '@/services/productService';

// 组件管理Store
export const useComponentStore = defineStore('component', () => {
  // 状态
  const components = ref<Component[]>([]);
  const currentComponent = ref<Component | null>(null);
  const filters = ref<ComponentFilters>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性
  const filteredComponents = computed(() => {
    if (!filters.value) return components.value;
    
    return components.value.filter(component => {
      if (filters.value.componentType && component.componentType !== filters.value.componentType) {
        return false;
      }
      if (filters.value.materialCategoryId && component.materialCategoryId !== filters.value.materialCategoryId) {
        return false;
      }
      if (filters.value.status && component.status !== filters.value.status) {
        return false;
      }
      if (filters.value.search) {
        const searchLower = filters.value.search.toLowerCase();
        return (
          component.name.toLowerCase().includes(searchLower) ||
          component.code.toLowerCase().includes(searchLower) ||
          component.description?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  });

  const componentsByType = computed(() => {
    const grouped: Record<string, Component[]> = {};
    components.value.forEach(component => {
      if (!grouped[component.componentType]) {
        grouped[component.componentType] = [];
      }
      grouped[component.componentType].push(component);
    });
    return grouped;
  });

  // 操作方法
  const loadComponents = async (newFilters?: ComponentFilters) => {
    loading.value = true;
    error.value = null;
    
    try {
      if (newFilters) {
        filters.value = newFilters;
      }
      components.value = await componentService.getComponents(filters.value);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载组件失败';
      console.error('Error loading components:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadComponentById = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      currentComponent.value = await componentService.getComponentById(id);
      if (!currentComponent.value) {
        error.value = `未找到ID为 ${id} 的组件`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载组件详情失败';
      console.error('Error loading component:', err);
    } finally {
      loading.value = false;
    }
  };

  const createComponent = async (componentData: Partial<Component>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const newComponent = await componentService.createComponent(componentData);
      components.value.push(newComponent);
      return newComponent;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建组件失败';
      console.error('Error creating component:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateComponent = async (id: string, updates: Partial<Component>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const updatedComponent = await componentService.updateComponent(id, updates);
      const index = components.value.findIndex(c => c.id === id);
      if (index !== -1) {
        components.value[index] = updatedComponent;
      }
      if (currentComponent.value?.id === id) {
        currentComponent.value = updatedComponent;
      }
      return updatedComponent;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新组件失败';
      console.error('Error updating component:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteComponent = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      await componentService.deleteComponent(id);
      components.value = components.value.filter(c => c.id !== id);
      if (currentComponent.value?.id === id) {
        currentComponent.value = null;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除组件失败';
      console.error('Error deleting component:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const setFilters = (newFilters: ComponentFilters) => {
    filters.value = newFilters;
  };

  const clearError = () => {
    error.value = null;
  };

  const reset = () => {
    components.value = [];
    currentComponent.value = null;
    filters.value = {};
    loading.value = false;
    error.value = null;
  };

  return {
    // 状态
    components,
    currentComponent,
    filters,
    loading,
    error,
    
    // 计算属性
    filteredComponents,
    componentsByType,
    
    // 方法
    loadComponents,
    loadComponentById,
    createComponent,
    updateComponent,
    deleteComponent,
    setFilters,
    clearError,
    reset
  };
});
