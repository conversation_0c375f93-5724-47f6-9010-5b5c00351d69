/**
 * Pinia 状态管理配置入口
 * 统一管理所有 Store 的初始化和配置
 */

import { createPinia } from 'pinia'
import type { App } from 'vue'

// 导入所有 Store
import { useAppStore } from './app'
import { useUserStore } from './user'
import { useBusinessStore } from './business'
import { useMetadataStore } from './metadata'
import { useMaterialVariantStore } from './materialVariant'
import { useGlobalStore } from './global'

// 创建 Pinia 实例
export const pinia = createPinia()

/**
 * 安装 Pinia 到 Vue 应用
 */
export function setupStore(app: App<Element>): void {
  app.use(pinia)
}

/**
 * 初始化所有 Store
 * 在应用启动时调用，恢复持久化状态并初始化数据
 */
export async function initializeStores(): Promise<void> {
  try {
    // 获取 Store 实例
    const appStore = useAppStore()
    const userStore = useUserStore()
    const businessStore = useBusinessStore()
    const materialVariantStore = useMaterialVariantStore()
    const globalStore = useGlobalStore()

    // 初始化应用状态
    appStore.initializeApp()

    // 恢复全局状态
    globalStore.restoreGlobalState()

    // 恢复用户认证状态
    const authRestored = userStore.restoreAuthState()
    
    if (authRestored) {
      // 如果用户已登录，初始化业务数据
      await Promise.all([
        businessStore.initializeBusinessData(),
        materialVariantStore.initializeMaterialVariantData(),
      ])
    }

    console.log('✅ Pinia 状态管理初始化完成')
  } catch (error) {
    console.error('❌ Pinia 状态管理初始化失败:', error)
  }
}

/**
 * 重置所有 Store 状态
 * 用于用户登出或应用重置
 */
export function resetAllStores(): void {
  const appStore = useAppStore()
  const userStore = useUserStore()
  const businessStore = useBusinessStore()
  const materialVariantStore = useMaterialVariantStore()
  const globalStore = useGlobalStore()

  // 重置各个 Store
  appStore.resetAppConfig()
  userStore.logout()
  businessStore.resetAllData()
  materialVariantStore.resetAllData()
  globalStore.resetGlobalState()

  console.log('🔄 所有 Store 状态已重置')
}

/**
 * 持久化所有 Store 状态
 * 在关键操作后调用，确保状态同步到本地存储
 */
export function persistAllStores(): void {
  const appStore = useAppStore()
  const businessStore = useBusinessStore()

  // 持久化状态（用户状态在登录/登出时自动持久化）
  // 物料变体状态暂不持久化，每次重新加载以确保数据新鲜度
  // appStore.persistAppConfig() // 应用配置会自动持久化
  businessStore.persistBusinessData()
}

// 导出所有 Store 的类型，便于在组件中使用
export type AppStore = ReturnType<typeof useAppStore>
export type UserStore = ReturnType<typeof useUserStore>
export type BusinessStore = ReturnType<typeof useBusinessStore>
export type MaterialVariantStore = ReturnType<typeof useMaterialVariantStore>
export type GlobalStore = ReturnType<typeof useGlobalStore>

// 导出 Store 实例获取函数
export {
  useAppStore,
  useUserStore,
  useBusinessStore,
  useMetadataStore,
  useMaterialVariantStore,
  useGlobalStore
}

// 导出 Store 相关类型
export type {
  Theme,
  Notification,
  NotificationAction,
  AppConfig
} from './app'

export type {
  User,
  Role,
  AuthState,
  LoginRequest,
  LoginResponse,
  PermissionCheckOptions
} from '../types/user'

export type {
  OrderStatus,
  Order,
  OrderItem,
  StockItem,
  Customer
} from './business'

export type {
  MaterialTemplate,
  MaterialVariant,
  MaterialVariantStock,
  MaterialVariantStockAlert,
  MaterialVariantFilter,
  MaterialVariantSearchResult
} from '../types/material-variant'

/**
 * Store 组合函数
 * 提供常用的跨 Store 操作
 */
export function useStores() {
  const appStore = useAppStore()
  const userStore = useUserStore()
  const businessStore = useBusinessStore()
  const materialVariantStore = useMaterialVariantStore()
  const globalStore = useGlobalStore()

  return {
    app: appStore,
    user: userStore,
    business: businessStore,
    materialVariant: materialVariantStore,
    global: globalStore,

    // 常用组合操作
    async login(credentials: { username: string; password: string }) {
      appStore.showLoading('正在登录...')
      
      try {
        const success = await userStore.login(credentials)
        
        if (success) {
          // 登录成功后初始化业务数据
          await Promise.all([
            businessStore.initializeBusinessData(),
            materialVariantStore.initializeMaterialVariantData(),
          ])
          appStore.addNotification({
            type: 'success',
            title: '登录成功',
            message: `欢迎回来，${userStore.currentUser?.name}！`,
            read: false
          })
        }
        
        return success
      } finally {
        appStore.hideLoading()
      }
    },

    async logout() {
      appStore.showLoading('正在登出...')
      
      try {
        await userStore.logout()
        businessStore.resetAllData()
        materialVariantStore.resetAllData()
        globalStore.resetGlobalState()
        
        appStore.addNotification({
          type: 'info',
          title: '已登出',
          message: '您已安全登出系统',
          read: false
        })
      } finally {
        appStore.hideLoading()
      }
    },

    // 权限检查组合
    hasPermission(permission: string | string[], requireAll = false) {
      return userStore.hasPermission(permission, { requireAll })
    },

    hasRole(role: string | string[]) {
      return userStore.hasRole(role)
    },

    // 通知管理组合
    showSuccess(message: string, title = '操作成功') {
      appStore.addNotification({
        type: 'success',
        title,
        message,
        read: false
      })
    },

    showError(message: string, title = '操作失败') {
      appStore.addNotification({
        type: 'error',
        title,
        message,
        read: false
      })
    },

    showWarning(message: string, title = '注意') {
      appStore.addNotification({
        type: 'warning',
        title,
        message,
        read: false
      })
    },

    showInfo(message: string, title = '提示') {
      appStore.addNotification({
        type: 'info',
        title,
        message,
        read: false
      })
    },

    // 全局状态管理组合方法
    selectMaterialVariant(variantId: string | null) {
      globalStore.setSelectedMaterialVariant(variantId)
      if (variantId) {
        materialVariantStore.setSelectedVariant(variantId)
      }
    },

    selectOrder(orderId: string | null) {
      globalStore.setSelectedOrder(orderId)
      if (orderId) {
        businessStore.setCurrentOrder(
          businessStore.recentOrders.find(o => o.id === orderId) || null
        )
      }
    },

    selectCustomer(customerId: string | null) {
      globalStore.setSelectedCustomer(customerId)
    },

    // 工作流管理组合方法
    startOrderCreationWorkflow(initialData: Record<string, any> = {}) {
      globalStore.startWorkflow('order_creation', initialData)
      this.showInfo('已开始订单创建流程', '工作流')
    },

    startMaterialProcurementWorkflow(initialData: Record<string, any> = {}) {
      globalStore.startWorkflow('material_procurement', initialData)
      this.showInfo('已开始物料采购流程', '工作流')
    },

    startProductionPlanningWorkflow(initialData: Record<string, any> = {}) {
      globalStore.startWorkflow('production_planning', initialData)
      this.showInfo('已开始生产计划流程', '工作流')
    },

    completeCurrentWorkflowStep(stepData: Record<string, any> = {}) {
      const currentStep = globalStore.workflowState.currentStep
      if (currentStep) {
        globalStore.completeWorkflowStep(stepData)
        this.showSuccess(`已完成步骤: ${currentStep}`, '工作流')
      }
    },

    cancelCurrentWorkflow() {
      globalStore.cancelWorkflow()
      this.showInfo('已取消当前工作流', '工作流')
    },

    // 快捷操作方法
    quickSelectMaterialVariant(variantId: string, variantName: string) {
      this.selectMaterialVariant(variantId)
      globalStore.addQuickAction({
        type: 'material_select',
        title: '快速选择物料变体',
        description: `选择了 ${variantName}`,
        data: { variantId, variantName }
      })
    },

    quickCreateOrder(customerId: string, customerName: string) {
      this.selectCustomer(customerId)
      this.startOrderCreationWorkflow({ customerId, customerName })
    },

    quickUpdateStock(variantId: string, quantity: number) {
      const success = materialVariantStore.updateVariantStock(variantId, quantity)
      if (success) {
        globalStore.addQuickAction({
          type: 'stock_update',
          title: '库存更新',
          description: `更新库存数量为 ${quantity}`,
          data: { variantId, quantity }
        })
        this.showSuccess('库存更新成功')
      } else {
        this.showError('库存更新失败')
      }
    }
  }
}

/**
 * 开发环境调试工具
 */
if (import.meta.env.DEV) {
  // 暴露 Store 到全局对象供调试使用
  ;(window as any).stores = {
    app: () => useAppStore(),
    user: () => useUserStore(),
    business: () => useBusinessStore(),
    materialVariant: () => useMaterialVariantStore(),
    global: () => useGlobalStore(),
    reset: resetAllStores,
    persist: persistAllStores
  }
}
