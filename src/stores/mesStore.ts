import { defineStore } from 'pinia';
import { ref } from 'vue';

/**
 * MES Store - 简化版本用于排产工作台
 */
export const useMesStore = defineStore('mes', () => {
  // 状态
  const loading = ref(false);
  const error = ref<string | null>(null);
  const productionOrders = ref<any[]>([]);

  // Actions
  const loadProductionOrders = async () => {
    loading.value = true;
    error.value = null;

    try {
      // 从JSON文件加载生产工单数据
      const response = await fetch('/mock/mes/production-orders.json');
      const data = await response.json();

      productionOrders.value = data.productionOrders || [];
      
      console.log('生产工单数据加载完成:', productionOrders.value.length);
    } catch (err) {
      error.value = '加载生产工单失败: ' + (err as Error).message;
      console.error('加载生产工单失败:', err);
    } finally {
      loading.value = false;
    }
  };

  return {
    // State
    loading,
    error,
    productionOrders,
    
    // Actions
    loadProductionOrders
  };
});
