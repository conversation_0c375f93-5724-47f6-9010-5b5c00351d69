// 设备能力约束类型定义

/**
 * 设备能力约束
 */
export interface EquipmentCapabilityConstraint {
  id: string;
  constraintType: 'size' | 'thickness' | 'material' | 'weight' | 'shape' | 'edge_type' | 'precision';
  dimension?: 'length' | 'width' | 'height' | 'area' | 'perimeter';
  minValue?: number;
  maxValue?: number;
  allowedValues?: string[];
  unit: string;
  description: string;
  priority: number; // 约束优先级，1-10
  isHardConstraint: boolean; // 是否为硬约束
}

/**
 * 设备处理能力
 */
export interface EquipmentProcessingCapability {
  equipmentId: string;
  processType: string; // 'edging', 'cutting', 'tempering', etc.
  constraints: EquipmentCapabilityConstraint[];
  
  // 性能参数
  standardSpeed: number; // 标准加工速度
  setupTime: number; // 换线时间（分钟）
  efficiency: number; // 效率系数
  qualityLevel: 'A' | 'B' | 'C'; // 质量等级
  
  // 成本参数
  operatingCost: number; // 运行成本（元/小时）
  setupCost: number; // 换线成本（元/次）
  
  // 适用性评分函数参数
  sizeOptimalRange: {
    lengthMin: number;
    lengthMax: number;
    widthMin: number;
    widthMax: number;
  };
  
  // 批量处理特性
  batchSizeOptimal: number; // 最优批量
  batchSizeMin: number; // 最小批量
  batchSizeMax: number; // 最大批量
}

/**
 * 工作中心综合能力
 */
export interface WorkCenterCapability {
  workCenterId: string;
  processType: string;
  equipmentCapabilities: EquipmentProcessingCapability[];
  
  // 综合能力范围
  overallConstraints: EquipmentCapabilityConstraint[];
  
  // 负载分配策略
  loadBalancingStrategy: 'round_robin' | 'least_loaded' | 'best_fit' | 'priority_based';
  
  // 智能分配参数
  intelligentAllocation: {
    enableSizeOptimization: boolean;
    enableBatchOptimization: boolean;
    enableQualityMatching: boolean;
    enableCostOptimization: boolean;
  };
}

/**
 * 订单项处理要求
 */
export interface OrderItemRequirement {
  orderItemId: string;
  
  // 尺寸要求
  dimensions: {
    length: number;
    width: number;
    thickness: number;
    area: number;
    perimeter: number;
  };
  
  // 材料要求
  materialType: string;
  glassType: string;
  
  // 工艺要求
  edgeType: string; // 'straight', 'beveled', 'rounded', 'polished'
  precisionLevel: 'standard' | 'high' | 'precision';
  surfaceQuality: 'A' | 'B' | 'C';
  
  // 数量和时间要求
  quantity: number;
  requiredDeliveryDate: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  
  // 特殊要求
  specialRequirements?: string[];
}

/**
 * 设备匹配结果
 */
export interface EquipmentMatchResult {
  equipmentId: string;
  equipmentName: string;
  
  // 匹配度评分
  overallScore: number; // 0-100，综合评分
  capabilityScore: number; // 能力匹配度
  efficiencyScore: number; // 效率评分
  costScore: number; // 成本评分
  availabilityScore: number; // 可用性评分
  
  // 约束检查结果
  constraintChecks: {
    constraintId: string;
    constraintType: string;
    passed: boolean;
    actualValue?: number;
    requiredValue?: number;
    message: string;
  }[];
  
  // 处理预测
  estimatedProcessingTime: number; // 预计加工时间（分钟）
  estimatedSetupTime: number; // 预计换线时间（分钟）
  estimatedCost: number; // 预计成本
  qualityRisk: 'low' | 'medium' | 'high'; // 质量风险
  
  // 可用时间窗口
  availableTimeSlots: {
    startTime: Date;
    endTime: Date;
    duration: number; // 分钟
  }[];
  
  // 推荐原因
  recommendationReasons: string[];
  
  // 限制因素
  limitations: string[];
}

/**
 * 智能排产决策数据
 */
export interface SchedulingDecisionData {
  orderItemId: string;
  workCenterId: string;
  
  // 候选设备评估
  candidateEquipments: EquipmentMatchResult[];
  
  // 推荐方案
  recommendedEquipment: string;
  alternativeEquipments: string[];
  
  // 排产建议
  schedulingSuggestion: {
    preferredStartTime: Date;
    estimatedCompletionTime: Date;
    batchingOpportunities: string[]; // 批量处理机会
    sequencingAdvice: string; // 排序建议
  };
  
  // 优化建议
  optimizationAdvice: {
    loadBalancing: string[];
    efficiencyImprovement: string[];
    costReduction: string[];
    qualityAssurance: string[];
  };
  
  // 风险评估
  risks: {
    deliveryRisk: 'low' | 'medium' | 'high';
    qualityRisk: 'low' | 'medium' | 'high';
    costRisk: 'low' | 'medium' | 'high';
    riskFactors: string[];
    mitigationSuggestions: string[];
  };
}

/**
 * 设备能力配置
 */
export interface EquipmentCapabilityConfig {
  equipmentId: string;
  
  // 基础约束配置
  sizeConstraints: {
    maxLength: number;
    maxWidth: number;
    maxThickness: number;
    minLength?: number;
    minWidth?: number;
    minThickness?: number;
  };
  
  // 材料约束
  materialConstraints: {
    supportedGlassTypes: string[];
    supportedThicknesses: number[];
    supportedCoatings: string[];
  };
  
  // 工艺约束
  processConstraints: {
    supportedEdgeTypes: string[];
    maxPrecisionLevel: string;
    supportedShapes: string[];
  };
  
  // 性能特征
  performanceCharacteristics: {
    optimalSizeRange: {
      lengthMin: number;
      lengthMax: number;
      widthMin: number;
      widthMax: number;
    };
    speedBySize: {
      sizeCategory: 'small' | 'medium' | 'large' | 'extra_large';
      speed: number; // mm/min
      efficiency: number; // %
    }[];
    setupTimeByChange: {
      changeType: 'thickness' | 'edge_type' | 'material';
      setupTime: number; // minutes
    }[];
  };
  
  // 质量特征
  qualityCharacteristics: {
    precisionLevel: number; // mm
    surfaceQuality: string;
    edgeQuality: string;
    consistencyRating: number; // 1-10
  };
  
  // 维护和可用性
  maintenanceSchedule: {
    dailyMaintenanceTime: number; // minutes
    weeklyMaintenanceTime: number; // minutes
    plannedDowntime: {
      startTime: string;
      endTime: string;
      frequency: 'daily' | 'weekly' | 'monthly';
    }[];
  };
}
