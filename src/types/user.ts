/**
 * 用户相关的TypeScript类型定义
 */

// 用户基础信息接口
export interface User {
  id: string
  username: string
  email: string
  name: string
  avatar?: string
  roles: string[]
  department: string
  isActive: boolean
  createdAt: string
  updatedAt?: string
}

// 角色权限接口
export interface Role {
  id: string
  name: string
  code: string
  permissions: string[]
  description?: string
}

// 用户认证状态接口
export interface AuthState {
  isAuthenticated: boolean
  currentUser: User | null
  userRoles: Role[]
  permissions: string[]
  token?: string
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应接口
export interface LoginResponse {
  user: User
  token: string
  roles: Role[]
  permissions: string[]
}

// 权限检查选项
export interface PermissionCheckOptions {
  requireAll?: boolean // 是否需要所有权限都满足，默认false（满足任一即可）
}
