// src/types/masterdata.ts

// --- 模块一: 制造资源管理 ---

export interface Equipment {
  id: string; // 唯一ID, e.g., "CUT-01"
  name: string; // "1号自动切割机"
  model: string; // "GL-CUT-3000"
  status: 'running' | 'idle' | 'fault' | 'maintenance';
  location?: string; // 设备位置
  assetNumber?: string; // 资产编号
  installDate?: string; // 安装日期
  lastMaintenanceDate?: string; // 最后维护日期
  nextMaintenanceDate?: string; // 下次维护日期
  // 产能参数 (示例，可扩展)
  parameters: {
    max_width?: number;
    max_height?: number;
    speed?: number; // m/min
    area?: number; // m²
    cycle_time?: number; // seconds
    power?: number; // 功率 kW
    efficiency?: number; // 效率 %
  };
  description?: string; // 设备描述
  createdAt?: string;
  updatedAt?: string;
}

export interface WorkCenter {
  id: string; // e.g., "WC-CUT"
  name: string; // "自动切割中心"
  equipmentIds: string[]; // 关联的设备ID列表
  calendarId: string; // 关联的产能日历ID
  description?: string; // 工作中心描述
  location?: string; // 位置
  capacity?: number; // 产能
  efficiency?: number; // 效率 %
  costCenter?: string; // 成本中心
  supervisor?: string; // 负责人
  createdAt?: string;
  updatedAt?: string;
}

// 产能日历接口
export interface Calendar {
  id: string;
  name: string;
  workingDays: number[]; // 工作日 [1,2,3,4,5] 表示周一到周五
  shifts: Shift[];
  holidays: Holiday[];
  createdAt?: string;
  updatedAt?: string;
}

export interface Shift {
  id: string;
  name: string; // "白班", "夜班"
  startTime: string; // "08:00"
  endTime: string; // "20:00"
  breakTime?: number; // 休息时间（分钟）
}

export interface Holiday {
  date: string; // "2025-01-01"
  name: string; // "元旦"
  type: 'national' | 'company';
}

// --- 模块二: 产品与物料工程 ---

export interface Material {
  id: string; // SKU, e.g., "GL-RAW-5-CLR"
  name: string; // "5mm白玻原片"
  type: 'raw' | 'semi-finished' | 'finished' | 'auxiliary';
  category?: string; // 物料分类
  baseUnit: 'piece' | 'm' | 'm²' | 'kg' | 'ton' | 'liter';
  // 使用Record<string, any>以支持自定义属性集
  attributes: Record<string, any>; // { thickness: 5, color: "clear", width: 3300, height: 2440 }
  supplier?: string; // 主供应商
  leadTime?: number; // 采购周期（天）
  minOrderQty?: number; // 最小订购量
  standardCost?: number; // 标准成本
  safetyStock?: number; // 安全库存
  description?: string; // 描述
  isActive?: boolean; // 是否启用
  createdAt?: string;
  updatedAt?: string;
}

export interface ConfigurationAttribute {
  id: string; // e.g., "width"
  label: string; // "总宽度"
  type: 'number' | 'select' | 'boolean';
  defaultValue: any;
  options?: string[]; // for select type
  rules?: string[]; // 简化的规则字符串, e.g., "min:500,max:3000"
}

export interface ProductFamily {
  id: string; // e.g., "PF-IGU" (Insulated Glass Unit)
  name: string; // "中空玻璃产品族"
  category?: string; // 产品分类
  attributes: ConfigurationAttribute[];
  // 依赖规则 (原型阶段简化为字符串描述)
  dependencyRules: string[]; // e.g., "IF(attribute.is_tempered == true) THEN (attribute.edge_type MUST BE 'polished')"
  description?: string; // 产品族描述
  isActive?: boolean; // 是否启用
  createdAt?: string;
  updatedAt?: string;
}

// 参数化BOM接口
export interface ParametricBOM {
  id: string;
  productFamilyId: string;
  name: string; // BOM名称
  version: number; // 版本号
  isActive: boolean; // 是否激活
  bomItems: ParametricBOMItem[];
  createdAt?: string;
  updatedAt?: string;
}

export interface ParametricBOMItem {
  id: string;
  materialId: string; // 关联物料ID
  sequence: number; // 序号
  quantityFormula: string; // 用量公式，如 "width * height / 1000000"
  isOptional: boolean; // 是否可选
  optionalCondition?: string; // 可选条件，如 "attributes.has_coating == true"
  scrapRate?: number; // 损耗率 %
  description?: string;
}

// 项目/订单实例接口
export interface ProjectInstance {
  id: string;
  projectNumber: string; // 项目编号
  productFamilyId: string;
  customerName: string;
  configurationValues: Record<string, any>; // 配置参数值
  generatedBOM?: GeneratedBOM; // 生成的BOM
  status: 'draft' | 'confirmed' | 'in_production' | 'completed';
  createdAt?: string;
  updatedAt?: string;
}

export interface GeneratedBOM {
  id: string;
  projectInstanceId: string;
  bomItems: GeneratedBOMItem[];
  totalCost?: number;
  generatedAt: string;
}

export interface GeneratedBOMItem {
  materialId: string;
  materialName: string;
  quantity: number;
  unit: string;
  unitCost?: number;
  totalCost?: number;
}

// --- 模块三: 分段式工艺工程 ---

export interface ProcessStep {
  id: string; // e.g., "PS-CUT"
  name: string; // "切割"
  type: 'internal' | 'external';
  category?: string; // 工序分类
  // 关联，对于internal是workCenterId，external是supplierId
  assigneeIds: string[];
  // 工时计算公式 (原型阶段简化为字符串)
  processingTimeFormula: string; // e.g., "(params.width + params.height) * 2 / 15"
  setupTimeFormula?: string; // 准备时间公式
  qualityControlPoints?: QualityControlPoint[]; // 质量控制点
  requiredSkills?: string[]; // 所需技能
  safetyRequirements?: string[]; // 安全要求
  description?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// 质量控制点接口
export interface QualityControlPoint {
  id: string;
  name: string;
  type: 'inspection' | 'test' | 'measurement';
  isRequired: boolean; // 是否必检
  inspectionStandard?: string; // 检验标准
  toleranceRange?: string; // 公差范围
}

export interface WipBuffer {
  id: string; // e.g., "BUF-EDGE-01"
  name: string; // "磨边前缓冲"
  capacity: number; // 可容纳的单位数
  unit: 'piece' | 'rack' | 'm²' | 'kg';
  location?: string; // 缓冲区位置
  bufferType: 'fifo' | 'lifo' | 'priority'; // 缓冲类型
  maxWaitTime?: number; // 最大等待时间（小时）
  description?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface ProcessSegmentNode {
  nodeId: string; // 实例ID, e.g., "node-12345"
  type: 'ProcessStep' | 'WipBuffer';
  entityId: string; // 关联的ProcessStep.id或WipBuffer.id
  position: { x: number; y: number }; // 用于UI可视化
}

export interface ProcessSegmentEdge {
  sourceNodeId: string;
  targetNodeId: string;
}

export interface ProcessSegment {
  id: string; // e.g., "SEG-COLD"
  name: string; // "冷加工段"
  description?: string;
  nodes: ProcessSegmentNode[];
  edges: ProcessSegmentEdge[];
  processStepIds?: string[]; // 兼容旧版本
  wipBufferIds?: string[]; // 兼容旧版本
  inputBufferId?: string;
  outputBufferId?: string;
}

export interface WipWarehouse {
  id: string; // e.g., "WH-WIP-TEMPER"
  name: string; // "钢化前WIP仓"
  locationId?: string; // 关联WMS的库位ID
}

export interface RoutingNode {
  nodeId: string; // 实例ID, e.g., "r-node-abc"
  type: 'ProcessSegment' | 'WipWarehouse';
  entityId: string; // 关联的ProcessSegment.id或WipWarehouse.id
  position: { x: number; y: number };
}

export interface RoutingEdge {
  sourceNodeId: string;
  targetNodeId: string;
}

export interface Routing {
  id: string; // e.g., "RT-IGU-TEMPERED"
  name: string; // "钢化中空玻璃工艺路线"
  productFamilyId: string;
  nodes: RoutingNode[];
  edges: RoutingEdge[];
  version: number;
  status?: 'draft' | 'active' | 'archived'; // 路线状态
  isActive: boolean;
  description?: string;
  estimatedLeadTime?: number; // 预计生产周期（小时）
  processSegmentIds?: string[]; // 兼容旧版本
  createdAt?: string;
  updatedAt?: string;
}

// 通用枚举和常量
export enum MaterialType {
  RAW = 'raw',
  SEMI_FINISHED = 'semi-finished',
  FINISHED = 'finished',
  AUXILIARY = 'auxiliary'
}

export enum EquipmentStatus {
  RUNNING = 'running',
  IDLE = 'idle',
  FAULT = 'fault',
  MAINTENANCE = 'maintenance'
}

export enum ProcessStepType {
  INTERNAL = 'internal',
  EXTERNAL = 'external'
}

// 数据验证和操作相关接口
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
}

// 批量操作接口
export interface BatchOperation<T> {
  operation: 'create' | 'update' | 'delete';
  items: T[];
  options?: {
    validateOnly?: boolean;
    skipValidation?: boolean;
    continueOnError?: boolean;
  };
}

export interface BatchOperationResult<T> {
  success: boolean;
  processedCount: number;
  errorCount: number;
  results: BatchItemResult<T>[];
}

export interface BatchItemResult<T> {
  item: T;
  success: boolean;
  error?: string;
}
