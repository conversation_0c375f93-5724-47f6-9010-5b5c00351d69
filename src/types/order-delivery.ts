/**
 * 订单交付管理相关类型定义
 */

// 交付阶段
export type DeliveryPhase = 'planning' | 'scheduling' | 'executing' | 'delivered';

// 订单交付状态
export interface OrderDeliveryStatus {
  primaryStatus: 'order_confirmed' | 'planning' | 'scheduled' | 'executing' | 'delivered';
  planningSubStatus?: 'material_analysis' | 'capacity_evaluation' | 'delivery_commitment';
  schedulingSubStatus?: 'cutting_optimization' | 'resource_allocation' | 'timeline_confirmation';
  executionSubStatus?: 'production_started' | 'quality_inspection' | 'packaging_ready';
}

// 客户信息
export interface CustomerInfo {
  id: string;
  name: string;
  contact: string;
  phone: string;
  address: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

// 订单项规格
export interface OrderItemSpecification {
  length: number;    // mm
  width: number;     // mm
  thickness: number; // mm
  glassType: 'clear' | 'tinted' | 'low_e' | 'reflective';
  color?: string;
  edgeWork?: 'polished' | 'ground' | 'beveled';
  holes?: HoleSpecification[];
  coating?: string;
}

export interface HoleSpecification {
  diameter: number;
  x: number;
  y: number;
}

// 订单项
export interface OrderItem {
  id: string;
  specifications: OrderItemSpecification;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  deliveryDate: string;
  notes?: string;
}

// 交付计划
export interface DeliveryPlan {
  id: string;
  plannedStartDate: string;
  plannedEndDate: string;
  materialRequirements: MaterialRequirement[];
  capacityAllocation: CapacityAllocation[];
  deliveryCommitment: DeliveryCommitment;
  risks: DeliveryRisk[];
}

export interface MaterialRequirement {
  materialId: string;
  materialName: string;
  requiredQuantity: number;
  availableQuantity: number;
  shortfall: number;
  supplier?: string;
  estimatedCost: number;
}

export interface CapacityAllocation {
  workstationId: string;
  workstationName: string;
  allocatedHours: number;
  availableHours: number;
  utilizationRate: number;
  bottleneck: boolean;
}

export interface DeliveryCommitment {
  commitmentDate: string;
  confidence: number; // 0-100
  bufferDays: number;
  riskFactors: string[];
}

export interface DeliveryRisk {
  id: string;
  type: 'material' | 'capacity' | 'quality' | 'delivery';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  impact: string;
  mitigation: string;
  status: 'identified' | 'mitigating' | 'resolved';
}

// 生产排程
export interface ProductionSchedule {
  id: string;
  scheduleDate: string;
  cuttingPlan: CuttingPlan;
  resourceSchedule: ResourceSchedule[];
  timeline: ScheduleTimeline;
  optimizationMetrics: OptimizationMetrics;
}

export interface CuttingPlan {
  id: string;
  rawMaterialSheets: RawMaterialSheet[];
  cuttingInstructions: CuttingInstruction[];
  utilizationRate: number;
  wastePercentage: number;
  estimatedTime: number;
}

export interface RawMaterialSheet {
  id: string;
  dimensions: { length: number; width: number; thickness: number };
  glassType: string;
  cost: number;
  utilization: number;
}

export interface CuttingInstruction {
  id: string;
  sheetId: string;
  cuts: Cut[];
  sequence: number;
  estimatedTime: number;
}

export interface Cut {
  id: string;
  orderItemId: string;
  position: { x: number; y: number };
  dimensions: { length: number; width: number };
  rotation: number;
}

export interface ResourceSchedule {
  resourceId: string;
  resourceName: string;
  resourceType: 'equipment' | 'workstation' | 'operator';
  scheduledTasks: ScheduledTask[];
  utilization: number;
}

export interface ScheduledTask {
  id: string;
  orderItemId: string;
  taskName: string;
  startTime: string;
  endTime: string;
  duration: number;
  dependencies: string[];
  status: 'planned' | 'started' | 'completed' | 'delayed';
}

export interface ScheduleTimeline {
  startDate: string;
  endDate: string;
  milestones: Milestone[];
  criticalPath: string[];
}

export interface Milestone {
  id: string;
  name: string;
  date: string;
  type: 'start' | 'checkpoint' | 'delivery';
  status: 'pending' | 'achieved' | 'missed';
}

export interface OptimizationMetrics {
  materialUtilization: number;
  equipmentUtilization: number;
  onTimeDeliveryProbability: number;
  totalCost: number;
  totalDuration: number;
}

// 执行进度
export interface ExecutionProgress {
  id: string;
  overallProgress: number; // 0-100
  phaseProgress: PhaseProgress[];
  currentTask: CurrentTask;
  completedTasks: CompletedTask[];
  issues: ExecutionIssue[];
}

export interface PhaseProgress {
  phase: string;
  progress: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'blocked';
  startTime?: string;
  endTime?: string;
}

export interface CurrentTask {
  id: string;
  name: string;
  assignee: string;
  startTime: string;
  estimatedEndTime: string;
  progress: number;
  location: string;
}

export interface CompletedTask {
  id: string;
  name: string;
  completedBy: string;
  completedAt: string;
  duration: number;
  qualityScore: number;
}

export interface ExecutionIssue {
  id: string;
  type: 'delay' | 'quality' | 'resource' | 'material';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  reportedAt: string;
  reportedBy: string;
  status: 'open' | 'investigating' | 'resolved';
  resolution?: string;
}

// 质量记录
export interface QualityRecord {
  id: string;
  orderItemId: string;
  checkpointName: string;
  checkTime: string;
  inspector: string;
  result: 'pass' | 'fail' | 'rework';
  measurements: QualityMeasurement[];
  notes?: string;
  images?: string[];
}

export interface QualityMeasurement {
  parameter: string;
  measuredValue: number;
  targetValue: number;
  tolerance: number;
  unit: string;
  withinTolerance: boolean;
}

// 交付时间线
export interface DeliveryTimeline {
  events: TimelineEvent[];
  currentEvent: string;
  estimatedCompletion: string;
}

export interface TimelineEvent {
  id: string;
  name: string;
  type: 'milestone' | 'task' | 'checkpoint';
  scheduledTime: string;
  actualTime?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'delayed';
  description: string;
  responsible: string;
}

// 主要的订单交付实体
export interface OrderDeliveryEntity {
  // 基础信息
  id: string;
  orderNumber: string;
  customerInfo: CustomerInfo;
  orderItems: OrderItem[];
  
  // 业务状态
  deliveryStatus: OrderDeliveryStatus;
  currentPhase: DeliveryPhase;
  
  // 计划信息
  deliveryPlan?: DeliveryPlan;
  productionSchedule?: ProductionSchedule;
  
  // 执行信息
  executionProgress?: ExecutionProgress;
  qualityRecords: QualityRecord[];
  
  // 时间线
  timeline: DeliveryTimeline;
  
  // 元数据
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  totalValue: number;
  estimatedProfit: number;
}

// 操作建议
export interface ActionSuggestion {
  id: string;
  type: 'optimization' | 'warning' | 'recommendation';
  title: string;
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  priority: number;
  action: () => void;
}

// 交付异常
export interface DeliveryAnomaly {
  id: string;
  type: 'delay_risk' | 'resource_conflict' | 'quality_issue' | 'cost_overrun';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedOrders: string[];
  suggestedActions: string[];
  detectedAt: string;
}

// 自动化结果
export interface AutomationResult {
  success: boolean;
  actionsExecuted: string[];
  results: Record<string, any>;
  errors?: string[];
}
