/**
 * 甘特图时间调试工具
 * 用于诊断和修复甘特图时间显示问题
 */

import type { ScheduledBatch, GanttChartData, GanttTask } from '@/types/scheduling';

export class GanttTimeDebugger {
  /**
   * 分析排产批次的时间数据
   */
  static analyzeScheduledBatches(batches: ScheduledBatch[]): {
    isValid: boolean;
    issues: string[];
    timeStats: {
      earliest: string;
      latest: string;
      totalSpan: number; // 小时
      averageDuration: number; // 分钟
    };
  } {
    const issues: string[] = [];
    
    if (!batches || batches.length === 0) {
      return {
        isValid: false,
        issues: ['批次数据为空'],
        timeStats: {
          earliest: '',
          latest: '',
          totalSpan: 0,
          averageDuration: 0
        }
      };
    }

    const startTimes: Date[] = [];
    const endTimes: Date[] = [];
    const durations: number[] = [];

    // 分析每个批次的时间数据
    batches.forEach((batch, index) => {
      if (!batch.scheduledStartTime) {
        issues.push(`批次 ${batch.id} (索引${index}) 缺少开始时间`);
        return;
      }
      
      if (!batch.scheduledEndTime) {
        issues.push(`批次 ${batch.id} (索引${index}) 缺少结束时间`);
        return;
      }

      const startTime = new Date(batch.scheduledStartTime);
      const endTime = new Date(batch.scheduledEndTime);

      if (isNaN(startTime.getTime())) {
        issues.push(`批次 ${batch.id} 开始时间格式无效: ${batch.scheduledStartTime}`);
        return;
      }

      if (isNaN(endTime.getTime())) {
        issues.push(`批次 ${batch.id} 结束时间格式无效: ${batch.scheduledEndTime}`);
        return;
      }

      if (startTime >= endTime) {
        issues.push(`批次 ${batch.id} 时间范围无效: 开始时间不能晚于或等于结束时间`);
        return;
      }

      const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60); // 分钟
      if (duration < 5 || duration > 24 * 60) {
        issues.push(`批次 ${batch.id} 持续时间异常: ${duration.toFixed(1)}分钟`);
      }

      startTimes.push(startTime);
      endTimes.push(endTime);
      durations.push(duration);
    });

    // 计算时间统计
    const timeStats = {
      earliest: startTimes.length > 0 ? 
        new Date(Math.min(...startTimes.map(d => d.getTime()))).toISOString() : '',
      latest: endTimes.length > 0 ? 
        new Date(Math.max(...endTimes.map(d => d.getTime()))).toISOString() : '',
      totalSpan: startTimes.length > 0 && endTimes.length > 0 ? 
        (Math.max(...endTimes.map(d => d.getTime())) - Math.min(...startTimes.map(d => d.getTime()))) / (1000 * 60 * 60) : 0,
      averageDuration: durations.length > 0 ? 
        durations.reduce((sum, d) => sum + d, 0) / durations.length : 0
    };

    return {
      isValid: issues.length === 0,
      issues,
      timeStats
    };
  }

  /**
   * 分析甘特图数据的时间一致性
   */
  static analyzeGanttData(ganttData: GanttChartData): {
    isValid: boolean;
    issues: string[];
    timeRange: {
      declared: { start: string; end: string };
      actual: { start: string; end: string };
      isConsistent: boolean;
    };
  } {
    const issues: string[] = [];

    if (!ganttData.timeRange) {
      return {
        isValid: false,
        issues: ['甘特图缺少时间范围信息'],
        timeRange: {
          declared: { start: '', end: '' },
          actual: { start: '', end: '' },
          isConsistent: false
        }
      };
    }

    const declaredStart = new Date(ganttData.timeRange.start);
    const declaredEnd = new Date(ganttData.timeRange.end);

    if (isNaN(declaredStart.getTime()) || isNaN(declaredEnd.getTime())) {
      issues.push('甘特图时间范围格式无效');
    }

    if (declaredStart >= declaredEnd) {
      issues.push('甘特图时间范围无效: 开始时间不能晚于或等于结束时间');
    }

    // 分析任务时间
    const taskStartTimes: Date[] = [];
    const taskEndTimes: Date[] = [];

    ganttData.tasks.forEach(task => {
      const taskStart = new Date(task.start);
      const taskEnd = new Date(task.end);

      if (isNaN(taskStart.getTime()) || isNaN(taskEnd.getTime())) {
        issues.push(`任务 ${task.id} 时间格式无效`);
        return;
      }

      if (taskStart >= taskEnd) {
        issues.push(`任务 ${task.id} 时间范围无效`);
        return;
      }

      // 检查任务是否在声明的时间范围内
      if (taskStart < declaredStart || taskEnd > declaredEnd) {
        issues.push(`任务 ${task.id} 超出了声明的时间范围`);
      }

      taskStartTimes.push(taskStart);
      taskEndTimes.push(taskEnd);
    });

    // 计算实际时间范围
    const actualStart = taskStartTimes.length > 0 ? 
      new Date(Math.min(...taskStartTimes.map(d => d.getTime()))) : declaredStart;
    const actualEnd = taskEndTimes.length > 0 ? 
      new Date(Math.max(...taskEndTimes.map(d => d.getTime()))) : declaredEnd;

    const isConsistent = Math.abs(actualStart.getTime() - declaredStart.getTime()) < 60000 && // 1分钟误差
                        Math.abs(actualEnd.getTime() - declaredEnd.getTime()) < 60000;

    return {
      isValid: issues.length === 0,
      issues,
      timeRange: {
        declared: {
          start: ganttData.timeRange.start,
          end: ganttData.timeRange.end
        },
        actual: {
          start: actualStart.toISOString(),
          end: actualEnd.toISOString()
        },
        isConsistent
      }
    };
  }

  /**
   * 生成时间调试报告
   */
  static generateDebugReport(
    batchAnalysis: ReturnType<typeof GanttTimeDebugger.analyzeScheduledBatches>,
    ganttAnalysis: ReturnType<typeof GanttTimeDebugger.analyzeGanttData>
  ): string {
    const lines: string[] = [];
    
    lines.push('=== 甘特图时间调试报告 ===');
    lines.push(`生成时间: ${new Date().toLocaleString('zh-CN')}`);
    lines.push('');

    // 批次时间分析
    lines.push('📊 排产批次时间分析:');
    lines.push(`  状态: ${batchAnalysis.isValid ? '✅ 正常' : '❌ 异常'}`);
    if (batchAnalysis.timeStats.earliest) {
      lines.push(`  最早开始: ${new Date(batchAnalysis.timeStats.earliest).toLocaleString('zh-CN')}`);
      lines.push(`  最晚结束: ${new Date(batchAnalysis.timeStats.latest).toLocaleString('zh-CN')}`);
      lines.push(`  总时间跨度: ${batchAnalysis.timeStats.totalSpan.toFixed(1)} 小时`);
      lines.push(`  平均持续时间: ${batchAnalysis.timeStats.averageDuration.toFixed(1)} 分钟`);
    }
    
    if (batchAnalysis.issues.length > 0) {
      lines.push('  问题列表:');
      batchAnalysis.issues.forEach(issue => lines.push(`    - ${issue}`));
    }
    lines.push('');

    // 甘特图时间分析
    lines.push('📈 甘特图时间分析:');
    lines.push(`  状态: ${ganttAnalysis.isValid ? '✅ 正常' : '❌ 异常'}`);
    lines.push(`  时间范围一致性: ${ganttAnalysis.timeRange.isConsistent ? '✅ 一致' : '❌ 不一致'}`);
    
    if (ganttAnalysis.timeRange.declared.start) {
      lines.push('  声明时间范围:');
      lines.push(`    开始: ${new Date(ganttAnalysis.timeRange.declared.start).toLocaleString('zh-CN')}`);
      lines.push(`    结束: ${new Date(ganttAnalysis.timeRange.declared.end).toLocaleString('zh-CN')}`);
      
      lines.push('  实际时间范围:');
      lines.push(`    开始: ${new Date(ganttAnalysis.timeRange.actual.start).toLocaleString('zh-CN')}`);
      lines.push(`    结束: ${new Date(ganttAnalysis.timeRange.actual.end).toLocaleString('zh-CN')}`);
    }
    
    if (ganttAnalysis.issues.length > 0) {
      lines.push('  问题列表:');
      ganttAnalysis.issues.forEach(issue => lines.push(`    - ${issue}`));
    }
    lines.push('');

    // 修复建议
    const allIssues = [...batchAnalysis.issues, ...ganttAnalysis.issues];
    if (allIssues.length > 0) {
      lines.push('🔧 修复建议:');
      if (allIssues.some(issue => issue.includes('时间格式'))) {
        lines.push('  - 检查时间数据格式，确保使用ISO 8601格式');
      }
      if (allIssues.some(issue => issue.includes('时间范围无效'))) {
        lines.push('  - 确保开始时间早于结束时间');
      }
      if (allIssues.some(issue => issue.includes('超出'))) {
        lines.push('  - 调整甘特图时间范围，确保包含所有任务');
      }
      if (allIssues.some(issue => issue.includes('持续时间异常'))) {
        lines.push('  - 检查任务持续时间计算逻辑');
      }
    } else {
      lines.push('✨ 所有时间数据检查通过！');
    }

    return lines.join('\n');
  }

  /**
   * 快速调试方法
   */
  static async quickDebug(batches: ScheduledBatch[], ganttData: GanttChartData): Promise<void> {
    console.log('🕐 开始甘特图时间调试...');
    
    const batchAnalysis = this.analyzeScheduledBatches(batches);
    const ganttAnalysis = this.analyzeGanttData(ganttData);
    
    const report = this.generateDebugReport(batchAnalysis, ganttAnalysis);
    console.log(report);
    
    if (!batchAnalysis.isValid || !ganttAnalysis.isValid) {
      console.error('❌ 发现时间数据问题，请查看上述报告');
    } else {
      console.log('✅ 时间数据检查通过！');
    }
  }
}

// 导出便捷方法
export const debugGanttTime = (batches: ScheduledBatch[], ganttData: GanttChartData) =>
  GanttTimeDebugger.quickDebug(batches, ganttData);
