// src/utils/dataValidation.ts
import { masterDataService } from '@/services/masterDataService';

/**
 * 数据一致性验证工具
 */
export class DataValidationTool {
  /**
   * 运行完整的数据一致性检查
   */
  static async runFullValidation() {
    console.log('🔍 开始数据一致性验证...');
    
    try {
      const result = await masterDataService.validateDataConsistency();
      
      console.log('\n📊 验证结果:');
      console.log(`✅ 数据一致性: ${result.isValid ? '通过' : '失败'}`);
      
      if (result.errors.length > 0) {
        console.log('\n❌ 发现错误:');
        result.errors.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error}`);
        });
      }
      
      if (result.warnings.length > 0) {
        console.log('\n⚠️  警告信息:');
        result.warnings.forEach((warning, index) => {
          console.log(`  ${index + 1}. ${warning}`);
        });
      }
      
      if (result.isValid && result.warnings.length === 0) {
        console.log('\n🎉 所有数据检查通过！');
      }
      
      return result;
    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error);
      return {
        isValid: false,
        errors: [`验证过程异常: ${error}`],
        warnings: []
      };
    }
  }

  /**
   * 测试设备-工作中心关联
   */
  static async testEquipmentWorkCenterAssociation() {
    console.log('\n🔗 测试设备-工作中心关联...');
    
    try {
      const equipments = await masterDataService.getEquipments();
      const workCenters = await masterDataService.getWorkCenters();
      
      console.log(`📋 设备总数: ${equipments.length}`);
      console.log(`📋 工作中心总数: ${workCenters.length}`);
      
      // 测试每个工作中心的设备关联
      for (const workCenter of workCenters) {
        console.log(`\n🏭 工作中心: ${workCenter.name} (${workCenter.id})`);
        
        const associatedEquipments = await masterDataService.getWorkCenterEquipments(workCenter.id);
        console.log(`  📦 关联设备数量: ${associatedEquipments.length}`);
        
        associatedEquipments.forEach(eq => {
          console.log(`    - ${eq.name} (${eq.id}) - 状态: ${eq.status}`);
        });
        
        // 计算产能
        const capacity = await masterDataService.calculateWorkCenterCapacity(workCenter.id);
        console.log(`  ⚡ 总产能: ${Math.round(capacity.totalCapacity)}`);
        console.log(`  ✅ 可用产能: ${Math.round(capacity.availableCapacity)}`);
        console.log(`  📊 利用率: ${Math.round(capacity.utilizationRate)}%`);
      }
      
      return true;
    } catch (error) {
      console.error('❌ 关联测试失败:', error);
      return false;
    }
  }

  /**
   * 测试工作中心-标准工序关联
   */
  static async testWorkCenterProcessStepAssociation() {
    console.log('\n🔗 测试工作中心-标准工序关联...');
    
    try {
      const workCenters = await masterDataService.getWorkCenters();
      const processSteps = await masterDataService.getProcessSteps();
      
      console.log(`📋 工作中心总数: ${workCenters.length}`);
      console.log(`📋 标准工序总数: ${processSteps.length}`);
      
      // 测试每个工作中心分配的工序
      for (const workCenter of workCenters) {
        console.log(`\n🏭 工作中心: ${workCenter.name} (${workCenter.id})`);
        
        const assignedProcessSteps = await masterDataService.getWorkCenterProcessSteps(workCenter.id);
        console.log(`  🔧 分配工序数量: ${assignedProcessSteps.length}`);
        
        assignedProcessSteps.forEach(ps => {
          console.log(`    - ${ps.name} (${ps.id}) - 类型: ${ps.type}`);
        });
      }
      
      // 测试每个工序分配的工作中心
      console.log('\n📋 工序分配情况:');
      for (const processStep of processSteps) {
        if (processStep.type === 'internal') {
          const assignedWorkCenters = await masterDataService.getProcessStepWorkCenters(processStep.id);
          console.log(`  🔧 ${processStep.name}: ${assignedWorkCenters.map(wc => wc.name).join(', ')}`);
        }
      }
      
      return true;
    } catch (error) {
      console.error('❌ 工序关联测试失败:', error);
      return false;
    }
  }

  /**
   * 测试设备状态级联影响
   */
  static async testEquipmentStatusCascade() {
    console.log('\n🔄 测试设备状态级联影响...');
    
    try {
      const equipments = await masterDataService.getEquipments();
      
      if (equipments.length === 0) {
        console.log('⚠️  没有设备数据可供测试');
        return false;
      }
      
      // 选择第一台设备进行测试
      const testEquipment = equipments[0];
      console.log(`🧪 测试设备: ${testEquipment.name} (${testEquipment.id})`);
      console.log(`📊 当前状态: ${testEquipment.status}`);
      
      // 模拟状态变更
      const originalStatus = testEquipment.status;
      const newStatus = originalStatus === 'running' ? 'maintenance' : 'running';
      
      console.log(`🔄 模拟状态变更: ${originalStatus} -> ${newStatus}`);
      
      const cascadeResult = await masterDataService.updateEquipmentStatusWithCascade(
        testEquipment.id, 
        newStatus
      );
      
      console.log(`✅ 设备状态已更新`);
      console.log(`🏭 受影响的工作中心数量: ${cascadeResult.affectedWorkCenters.length}`);
      cascadeResult.affectedWorkCenters.forEach(wc => {
        console.log(`    - ${wc.name} (${wc.id})`);
      });
      
      console.log(`🔧 受影响的标准工序数量: ${cascadeResult.affectedProcessSteps.length}`);
      cascadeResult.affectedProcessSteps.forEach(ps => {
        console.log(`    - ${ps.name} (${ps.id})`);
      });
      
      // 恢复原状态
      await masterDataService.updateEquipmentStatusWithCascade(testEquipment.id, originalStatus);
      console.log(`🔄 已恢复原始状态: ${originalStatus}`);
      
      return true;
    } catch (error) {
      console.error('❌ 级联影响测试失败:', error);
      return false;
    }
  }

  /**
   * 运行所有测试
   */
  static async runAllTests() {
    console.log('🚀 开始运行所有数据一致性测试...\n');
    
    const results = {
      validation: false,
      equipmentWorkCenter: false,
      workCenterProcessStep: false,
      statusCascade: false
    };
    
    // 1. 数据一致性验证
    const validationResult = await this.runFullValidation();
    results.validation = validationResult.isValid;
    
    // 2. 设备-工作中心关联测试
    results.equipmentWorkCenter = await this.testEquipmentWorkCenterAssociation();
    
    // 3. 工作中心-工序关联测试
    results.workCenterProcessStep = await this.testWorkCenterProcessStepAssociation();
    
    // 4. 设备状态级联影响测试
    results.statusCascade = await this.testEquipmentStatusCascade();
    
    // 总结
    console.log('\n📊 测试结果总结:');
    console.log(`✅ 数据一致性验证: ${results.validation ? '通过' : '失败'}`);
    console.log(`✅ 设备-工作中心关联: ${results.equipmentWorkCenter ? '通过' : '失败'}`);
    console.log(`✅ 工作中心-工序关联: ${results.workCenterProcessStep ? '通过' : '失败'}`);
    console.log(`✅ 设备状态级联影响: ${results.statusCascade ? '通过' : '失败'}`);
    
    const allPassed = Object.values(results).every(result => result);
    console.log(`\n🎯 总体结果: ${allPassed ? '全部通过 🎉' : '存在问题 ⚠️'}`);
    
    return results;
  }
}

// 在开发环境下暴露到全局，方便调试
if (import.meta.env.DEV) {
  (window as any).DataValidationTool = DataValidationTool;
}
