// 基于生产工单的智能排产测试工具

import { EquipmentCapabilityService } from '@/services/equipmentCapabilityService';
import { batchOptimizationService } from '@/services/batchOptimizationService';
import type { SelectedOrderItem } from '@/types/production-order-creation';

/**
 * 生产工单智能排产测试工具
 */
export class ProductionOrderSchedulingTestHelper {
  
  /**
   * 运行完整的生产工单排产测试
   */
  static async runCompleteSchedulingTest() {
    console.log('🏭 开始生产工单智能排产测试...\n');

    const results = {
      workOrderGeneration: false,
      batchOptimization: false,
      equipmentAllocation: false,
      constraintValidation: false,
      performanceAnalysis: false
    };

    try {
      // 1. 测试工单生成
      console.log('📋 测试生产工单生成...');
      results.workOrderGeneration = await this.testWorkOrderGeneration();

      // 2. 测试批次优化
      console.log('🔄 测试批次优化...');
      results.batchOptimization = await this.testBatchOptimization();

      // 3. 测试设备分配
      console.log('⚙️  测试设备分配...');
      results.equipmentAllocation = await this.testEquipmentAllocation();

      // 4. 测试约束验证
      console.log('🔍 测试约束验证...');
      results.constraintValidation = await this.testConstraintValidation();

      // 5. 测试性能分析
      console.log('📊 测试性能分析...');
      results.performanceAnalysis = await this.testPerformanceAnalysis();

      // 总结测试结果
      console.log('\n📊 测试结果总结:');
      Object.entries(results).forEach(([feature, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${this.getFeatureLabel(feature)}: ${passed ? '通过' : '失败'}`);
      });

      const allPassed = Object.values(results).every(result => result);
      console.log(`\n🎯 总体结果: ${allPassed ? '全部通过 🎉' : '存在问题 ⚠️'}`);

      return results;
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
      return results;
    }
  }

  /**
   * 测试工单生成
   */
  static async testWorkOrderGeneration(): Promise<boolean> {
    try {
      const testWorkOrder = this.generateTestWorkOrder();
      
      console.log(`  📋 生成测试工单: ${testWorkOrder.workOrderNumber}`);
      console.log(`  👤 客户: ${testWorkOrder.customerName}`);
      console.log(`  📦 工单项数量: ${testWorkOrder.items.length}`);
      
      // 验证工单结构
      const hasValidStructure = testWorkOrder.items.every(item => 
        item.specifications && 
        item.quantity > 0 && 
        item.processFlow && 
        item.processFlow.length > 0
      );
      
      if (hasValidStructure) {
        console.log('  ✅ 工单结构验证通过');
        return true;
      } else {
        console.log('  ❌ 工单结构验证失败');
        return false;
      }
    } catch (error) {
      console.error('  ❌ 工单生成测试失败:', error);
      return false;
    }
  }

  /**
   * 测试批次优化
   */
  static async testBatchOptimization(): Promise<boolean> {
    try {
      const testWorkOrder = this.generateTestWorkOrder();
      const selectedItems: SelectedOrderItem[] = testWorkOrder.items.map(item => ({
        id: item.id,
        customerOrderItemId: item.customerOrderItemId,
        specifications: item.specifications,
        quantity: item.quantity,
        selectedQuantity: item.quantity,
        processFlow: item.processFlow,
        currentStatus: item.currentStatus,
        currentWorkstation: item.currentWorkstation
      }));

      const batchResult = await batchOptimizationService.optimizeBatches(selectedItems);
      
      console.log(`  🔄 优化批次数: ${batchResult.optimizedBatches.length}`);
      console.log(`  📈 效率提升: ${batchResult.efficiencyImprovement}%`);
      console.log(`  ⏱️  节省时间: ${batchResult.timeSaved} 分钟`);
      
      // 验证批次优化结果
      const hasValidBatches = batchResult.optimizedBatches.length > 0 &&
                             batchResult.efficiencyImprovement >= 0 &&
                             batchResult.timeSaved >= 0;
      
      if (hasValidBatches) {
        console.log('  ✅ 批次优化验证通过');
        return true;
      } else {
        console.log('  ❌ 批次优化验证失败');
        return false;
      }
    } catch (error) {
      console.error('  ❌ 批次优化测试失败:', error);
      return false;
    }
  }

  /**
   * 测试设备分配
   */
  static async testEquipmentAllocation(): Promise<boolean> {
    try {
      const testWorkOrder = this.generateTestWorkOrder();
      
      // 分析工单项的设备分配
      const analysisResult = await EquipmentCapabilityService.analyzeProductionOrderItems(
        testWorkOrder.items,
        'WC-EDGE-001'
      );
      
      console.log(`  ⚙️  分析批次数: ${analysisResult.batchAnalysis.length}`);
      console.log(`  📊 平均匹配评分: ${analysisResult.overallOptimization.averageMatchScore}`);
      console.log(`  ⏱️  总预计时间: ${analysisResult.overallOptimization.totalEstimatedTime} 分钟`);
      
      // 验证设备分配结果
      let allocationValid = true;
      
      for (const batch of analysisResult.batchAnalysis) {
        const specKey = batch.specificationKey;
        const matches = analysisResult.equipmentRecommendations.get(specKey);
        
        if (!matches || matches.length === 0) {
          console.log(`  ❌ 批次 ${specKey} 没有找到匹配的设备`);
          allocationValid = false;
          continue;
        }
        
        const bestMatch = matches[0];
        console.log(`  📋 ${specKey}: 推荐设备 ${bestMatch.equipmentName} (评分: ${bestMatch.overallScore})`);
        
        // 验证设备选择逻辑
        const item = batch.items[0];
        const area = (item.specifications.length * item.specifications.width) / 1000000;
        
        if (area <= 2.0 && item.specifications.width <= 1830) {
          // 小规格应该推荐1号设备
          if (!bestMatch.equipmentId.includes('001')) {
            console.log(`  ⚠️  小规格 ${specKey} 应该推荐1号设备，但推荐了 ${bestMatch.equipmentId}`);
          }
        } else if (item.specifications.width > 1830) {
          // 大规格应该推荐2号设备
          if (!bestMatch.equipmentId.includes('002')) {
            console.log(`  ⚠️  大规格 ${specKey} 应该推荐2号设备，但推荐了 ${bestMatch.equipmentId}`);
          }
        }
      }
      
      if (allocationValid) {
        console.log('  ✅ 设备分配验证通过');
        return true;
      } else {
        console.log('  ❌ 设备分配验证失败');
        return false;
      }
    } catch (error) {
      console.error('  ❌ 设备分配测试失败:', error);
      return false;
    }
  }

  /**
   * 测试约束验证
   */
  static async testConstraintValidation(): Promise<boolean> {
    try {
      // 创建包含边界情况的测试用例
      const testCases = [
        {
          name: '正常小规格',
          specs: { length: 1800, width: 1200, thickness: 6 },
          expectedValid: true,
          expectedEquipment: 'EQ-EDGE-001'
        },
        {
          name: '正常大规格',
          specs: { length: 2200, width: 3200, thickness: 8 },
          expectedValid: true,
          expectedEquipment: 'EQ-EDGE-002'
        },
        {
          name: '边界规格',
          specs: { length: 2440, width: 1830, thickness: 6 },
          expectedValid: true,
          expectedEquipment: 'EQ-EDGE-001'
        },
        {
          name: '超大规格',
          specs: { length: 2500, width: 4000, thickness: 10 },
          expectedValid: false,
          expectedEquipment: null
        }
      ];

      let allTestsPassed = true;

      for (const testCase of testCases) {
        console.log(`  🔍 测试用例: ${testCase.name}`);
        
        const requirement = {
          orderItemId: `TEST-${testCase.name}`,
          dimensions: {
            length: testCase.specs.length,
            width: testCase.specs.width,
            thickness: testCase.specs.thickness,
            area: (testCase.specs.length * testCase.specs.width) / 1000000,
            perimeter: 2 * (testCase.specs.length + testCase.specs.width)
          },
          materialType: 'float_glass',
          glassType: 'clear',
          edgeType: 'straight',
          precisionLevel: 'standard' as any,
          surfaceQuality: 'A' as any,
          quantity: 50,
          requiredDeliveryDate: new Date(),
          priority: 'medium' as any
        };

        const matches = await EquipmentCapabilityService.matchEquipmentForOrderItem(
          requirement,
          'WC-EDGE-001'
        );

        if (testCase.expectedValid) {
          if (matches.length > 0 && matches[0].overallScore > 0) {
            console.log(`    ✅ ${testCase.name}: 找到匹配设备 ${matches[0].equipmentName} (评分: ${matches[0].overallScore})`);
          } else {
            console.log(`    ❌ ${testCase.name}: 应该找到匹配设备但没有找到`);
            allTestsPassed = false;
          }
        } else {
          const hasValidMatch = matches.some(match => match.capabilityScore === 100);
          if (!hasValidMatch) {
            console.log(`    ✅ ${testCase.name}: 正确识别为约束违反`);
          } else {
            console.log(`    ❌ ${testCase.name}: 应该识别为约束违反但没有`);
            allTestsPassed = false;
          }
        }
      }

      if (allTestsPassed) {
        console.log('  ✅ 约束验证测试通过');
        return true;
      } else {
        console.log('  ❌ 约束验证测试失败');
        return false;
      }
    } catch (error) {
      console.error('  ❌ 约束验证测试失败:', error);
      return false;
    }
  }

  /**
   * 测试性能分析
   */
  static async testPerformanceAnalysis(): Promise<boolean> {
    try {
      const startTime = performance.now();
      
      // 生成大量测试数据
      const largeWorkOrder = this.generateLargeTestWorkOrder(20); // 20个工单项
      
      const analysisResult = await EquipmentCapabilityService.analyzeProductionOrderItems(
        largeWorkOrder.items,
        'WC-EDGE-001'
      );
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      console.log(`  📊 处理 ${largeWorkOrder.items.length} 个工单项`);
      console.log(`  ⏱️  处理时间: ${processingTime.toFixed(2)} ms`);
      console.log(`  📈 平均每项处理时间: ${(processingTime / largeWorkOrder.items.length).toFixed(2)} ms`);
      
      // 性能基准：每个工单项处理时间应该小于100ms
      const avgTimePerItem = processingTime / largeWorkOrder.items.length;
      
      if (avgTimePerItem < 100 && analysisResult.batchAnalysis.length > 0) {
        console.log('  ✅ 性能分析测试通过');
        return true;
      } else {
        console.log('  ❌ 性能分析测试失败');
        return false;
      }
    } catch (error) {
      console.error('  ❌ 性能分析测试失败:', error);
      return false;
    }
  }

  /**
   * 生成测试工单
   */
  static generateTestWorkOrder() {
    return {
      id: `WO-TEST-${Date.now()}`,
      workOrderNumber: `WO-2025-TEST-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
      customerName: '测试客户',
      items: [
        {
          id: 'WOI-1',
          customerOrderItemId: 'COI-1',
          specifications: { length: 1800, width: 1200, thickness: 6, glassType: 'float_glass', color: 'clear' },
          quantity: 50,
          processFlow: [
            { stepName: '切割', workstation: 'cutting', estimatedDuration: 30, constraints: {}, status: 'pending', workstationGroup: 'cutting' },
            { stepName: '磨边', workstation: 'edging', estimatedDuration: 45, constraints: {}, status: 'pending', workstationGroup: 'edging' }
          ],
          currentStatus: 'pending',
          currentWorkstation: 'cutting'
        },
        {
          id: 'WOI-2',
          customerOrderItemId: 'COI-2',
          specifications: { length: 2200, width: 3200, thickness: 8, glassType: 'tempered_glass', color: 'clear' },
          quantity: 30,
          processFlow: [
            { stepName: '切割', workstation: 'cutting', estimatedDuration: 40, constraints: {}, status: 'pending', workstationGroup: 'cutting' },
            { stepName: '磨边', workstation: 'edging', estimatedDuration: 60, constraints: {}, status: 'pending', workstationGroup: 'edging' }
          ],
          currentStatus: 'pending',
          currentWorkstation: 'cutting'
        }
      ]
    };
  }

  /**
   * 生成大型测试工单
   */
  static generateLargeTestWorkOrder(itemCount: number) {
    const specs = [
      { length: 1800, width: 1200, thickness: 6, glassType: 'float_glass' },
      { length: 2400, width: 1800, thickness: 8, glassType: 'tempered_glass' },
      { length: 2200, width: 3200, thickness: 10, glassType: 'laminated_glass' },
      { length: 1500, width: 1000, thickness: 5, glassType: 'float_glass' },
      { length: 2400, width: 3600, thickness: 12, glassType: 'tempered_glass' }
    ];

    const items = [];
    for (let i = 0; i < itemCount; i++) {
      const spec = specs[i % specs.length];
      items.push({
        id: `WOI-${i + 1}`,
        customerOrderItemId: `COI-${i + 1}`,
        specifications: { ...spec, color: 'clear' },
        quantity: Math.floor(Math.random() * 80) + 20,
        processFlow: [
          { stepName: '切割', workstation: 'cutting', estimatedDuration: 30, constraints: {}, status: 'pending', workstationGroup: 'cutting' },
          { stepName: '磨边', workstation: 'edging', estimatedDuration: 45, constraints: {}, status: 'pending', workstationGroup: 'edging' }
        ],
        currentStatus: 'pending',
        currentWorkstation: 'cutting'
      });
    }

    return {
      id: `WO-LARGE-TEST-${Date.now()}`,
      workOrderNumber: `WO-2025-LARGE-${itemCount}`,
      customerName: '大型测试客户',
      items
    };
  }

  /**
   * 获取功能标签
   */
  static getFeatureLabel(feature: string): string {
    const labels: Record<string, string> = {
      workOrderGeneration: '生产工单生成',
      batchOptimization: '批次优化',
      equipmentAllocation: '设备分配',
      constraintValidation: '约束验证',
      performanceAnalysis: '性能分析'
    };
    return labels[feature] || feature;
  }

  /**
   * 演示生产工单排产场景
   */
  static async demonstrateSchedulingScenarios() {
    console.log('🎭 演示生产工单智能排产场景...\n');

    console.log('📋 场景1: 混合规格工单排产');
    console.log('  - 包含小规格(1800×1200×6mm)和大规格(2200×3200×8mm)');
    console.log('  - 小规格自动分配给1号设备');
    console.log('  - 大规格自动分配给2号设备');
    console.log('  - 批次优化提高生产效率');

    console.log('\n🔄 场景2: 批次优化处理');
    console.log('  - 相同规格工单项自动分组');
    console.log('  - 减少设备换线次数');
    console.log('  - 提高设备利用率');
    console.log('  - 缩短总生产时间');

    console.log('\n⚙️  场景3: 设备能力约束');
    console.log('  - 超大规格(2500×4000mm)自动识别约束违反');
    console.log('  - 边界规格(2440×1830mm)正确分配给1号设备');
    console.log('  - 设备匹配评分反映真实适配度');
    console.log('  - 约束检查确保生产可行性');

    console.log('\n📊 场景4: 智能决策支持');
    console.log('  - 多维度评分(能力、效率、成本、可用性)');
    console.log('  - 处理时间和成本预测');
    console.log('  - 质量风险评估');
    console.log('  - 优化建议生成');
  }
}

// 在开发环境下暴露到全局，方便调试
if (import.meta.env.DEV) {
  (window as any).ProductionOrderSchedulingTestHelper = ProductionOrderSchedulingTestHelper;
}
