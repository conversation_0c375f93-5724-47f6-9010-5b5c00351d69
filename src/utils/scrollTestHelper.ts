// 设备详情弹窗滚动条测试工具
export class ScrollTestHelper {
  /**
   * 测试弹窗滚动条功能
   */
  static testDialogScrolling() {
    console.log('🧪 测试设备详情弹窗滚动条功能...\n');

    console.log('📋 滚动条测试要点:');
    console.log('1. 弹窗整体布局使用 flex 布局');
    console.log('2. 标签页内容区域设置 overflow-y-auto');
    console.log('3. 各个子面板的长列表区域独立滚动');
    console.log('4. 操作按钮区域固定在底部');

    console.log('\n🔍 测试步骤:');
    console.log('1. 打开设备详情弹窗');
    console.log('2. 切换到不同的标签页');
    console.log('3. 在有大量内容的标签页中滚动');
    console.log('4. 验证滚动条是否正确显示');

    console.log('\n✅ 预期行为:');
    console.log('- 弹窗高度固定为 90vh');
    console.log('- 标签页内容区域可独立滚动');
    console.log('- 长列表区域显示滚动条');
    console.log('- 操作按钮始终可见');

    console.log('\n🎯 测试场景:');
    console.log('- 基础信息: 技术规格列表滚动');
    console.log('- 产能参数: 自定义参数列表滚动');
    console.log('- 维护管理: 维护记录列表滚动');
    console.log('- 工作中心: 产能分析列表滚动');
    console.log('- 历史记录: 历史记录时间线滚动');

    return {
      dialogLayout: 'flex flex-col',
      contentArea: 'flex-1 overflow-y-auto min-h-0',
      listAreas: 'max-h-[specific] overflow-y-auto',
      fixedAreas: 'flex-shrink-0'
    };
  }

  /**
   * 生成测试数据以验证滚动功能
   */
  static generateTestData() {
    console.log('📊 生成测试数据以验证滚动功能...\n');

    // 生成大量技术规格数据
    const specifications = [];
    for (let i = 1; i <= 20; i++) {
      specifications.push({
        name: `技术规格 ${i}`,
        value: `值 ${i}`,
        unit: i % 2 === 0 ? 'mm' : 'kg'
      });
    }

    // 生成大量自定义参数
    const customParameters = [];
    for (let i = 1; i <= 15; i++) {
      customParameters.push({
        name: `自定义参数 ${i}`,
        value: `参数值 ${i}`,
        unit: i % 3 === 0 ? '°C' : 'kW'
      });
    }

    // 生成大量维护记录
    const maintenanceRecords = [];
    for (let i = 1; i <= 25; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i * 7);
      
      maintenanceRecords.push({
        date: date.toISOString().split('T')[0],
        type: ['routine', 'emergency', 'preventive', 'corrective'][i % 4],
        technician: `技术员 ${i % 5 + 1}`,
        description: `维护记录 ${i} - 详细描述维护内容和发现的问题，包括更换的零件和调整的参数。`,
        cost: Math.floor(Math.random() * 2000) + 100,
        duration: Math.floor(Math.random() * 8) + 1
      });
    }

    // 生成大量历史记录
    const historyRecords = [];
    for (let i = 1; i <= 30; i++) {
      const date = new Date();
      date.setHours(date.getHours() - i * 2);
      
      historyRecords.push({
        id: `history-${i}`,
        type: ['status', 'maintenance', 'fault', 'production'][i % 4],
        title: `历史记录 ${i}`,
        description: `详细描述历史记录 ${i} 的内容，包括相关的操作和影响。`,
        timestamp: date.toISOString(),
        details: {
          duration: `${Math.floor(Math.random() * 8) + 1}小时`,
          operator: `操作员 ${i % 3 + 1}`,
          impact: i % 2 === 0 ? '高' : '低'
        }
      });
    }

    console.log(`✅ 生成测试数据:`);
    console.log(`  - 技术规格: ${specifications.length} 条`);
    console.log(`  - 自定义参数: ${customParameters.length} 条`);
    console.log(`  - 维护记录: ${maintenanceRecords.length} 条`);
    console.log(`  - 历史记录: ${historyRecords.length} 条`);

    return {
      specifications,
      customParameters,
      maintenanceRecords,
      historyRecords
    };
  }

  /**
   * 检查滚动条样式配置
   */
  static checkScrollbarStyles() {
    console.log('🎨 检查滚动条样式配置...\n');

    const scrollbarConfig = {
      // 主内容区域滚动
      mainContent: {
        class: 'flex-1 overflow-y-auto py-4 min-h-0',
        description: '主要内容区域，支持垂直滚动'
      },

      // 列表区域滚动
      listAreas: {
        specifications: {
          class: 'max-h-48 overflow-y-auto',
          description: '技术规格列表，最大高度48，超出显示滚动条'
        },
        customParameters: {
          class: 'max-h-48 overflow-y-auto',
          description: '自定义参数列表，最大高度48'
        },
        maintenanceRecords: {
          class: 'max-h-64 overflow-y-auto',
          description: '维护记录列表，最大高度64'
        },
        historyRecords: {
          class: 'max-h-80 overflow-y-auto pr-2',
          description: '历史记录列表，最大高度80，右侧留出滚动条空间'
        },
        capacityAnalysis: {
          class: 'max-h-64 overflow-y-auto',
          description: '产能分析列表，最大高度64'
        }
      },

      // 固定区域
      fixedAreas: {
        header: {
          class: 'flex-shrink-0',
          description: '弹窗头部，不参与滚动'
        },
        tabNavigation: {
          class: 'flex-shrink-0',
          description: '标签页导航，固定显示'
        },
        actionButtons: {
          class: 'flex-shrink-0',
          description: '操作按钮区域，固定在底部'
        }
      }
    };

    console.log('📋 滚动配置详情:');
    console.log('主内容区域:', scrollbarConfig.mainContent);
    console.log('\n列表区域配置:');
    Object.entries(scrollbarConfig.listAreas).forEach(([key, config]) => {
      console.log(`  ${key}:`, config);
    });
    console.log('\n固定区域配置:');
    Object.entries(scrollbarConfig.fixedAreas).forEach(([key, config]) => {
      console.log(`  ${key}:`, config);
    });

    return scrollbarConfig;
  }

  /**
   * 提供滚动条测试指南
   */
  static getTestingGuide() {
    console.log('📖 滚动条功能测试指南...\n');

    const guide = {
      preparation: [
        '1. 确保开发服务器正在运行',
        '2. 打开浏览器开发者工具',
        '3. 导航到设备管理页面'
      ],

      testSteps: [
        '1. 点击任意设备的"查看详情"按钮',
        '2. 验证弹窗是否正确显示',
        '3. 切换到"基础信息"标签页',
        '4. 添加多个技术规格，观察滚动条',
        '5. 切换到"产能参数"标签页',
        '6. 添加多个自定义参数，观察滚动条',
        '7. 切换到"维护管理"标签页',
        '8. 查看维护记录列表的滚动',
        '9. 切换到"历史记录"标签页',
        '10. 验证历史记录时间线的滚动'
      ],

      checkPoints: [
        '✅ 弹窗高度固定为屏幕高度的90%',
        '✅ 标签页内容区域可以独立滚动',
        '✅ 长列表显示垂直滚动条',
        '✅ 滚动时操作按钮保持可见',
        '✅ 滚动条样式与整体设计一致',
        '✅ 在不同屏幕尺寸下正常工作'
      ],

      troubleshooting: [
        '❌ 如果滚动条不显示: 检查 overflow-y-auto 类',
        '❌ 如果内容被截断: 检查 min-h-0 和 flex-1 类',
        '❌ 如果按钮被遮挡: 检查 flex-shrink-0 类',
        '❌ 如果滚动不流畅: 检查容器的高度设置'
      ]
    };

    console.log('🚀 准备工作:');
    guide.preparation.forEach(step => console.log(`  ${step}`));

    console.log('\n🧪 测试步骤:');
    guide.testSteps.forEach(step => console.log(`  ${step}`));

    console.log('\n✅ 检查要点:');
    guide.checkPoints.forEach(point => console.log(`  ${point}`));

    console.log('\n🔧 故障排除:');
    guide.troubleshooting.forEach(issue => console.log(`  ${issue}`));

    return guide;
  }
}

// 在开发环境下暴露到全局，方便调试
if (import.meta.env.DEV) {
  (window as any).ScrollTestHelper = ScrollTestHelper;
}
