// MES 数据服务（原型） - 统一从 public/mock 读取并封装错误处理
export interface ApiResult<T> { data: T; ok: boolean; error?: string }

async function getJSON<T = any>(path: string): Promise<ApiResult<T>> {
  try {
    const res = await fetch(path)
    if (!res.ok) return { ok: false, data: undefined as unknown as T, error: `HTTP ${res.status}` }
    const data = await res.json()
    return { ok: true, data }
  } catch (e: any) {
    return { ok: false, data: undefined as unknown as T, error: e?.message || 'network error' }
  }
}

// 排版优化相关
export async function getAlgorithmStrategy() {
  return getJSON('/mock/mes/validation/algorithm-strategy.json')
}
export async function getCuttingValueComparison() {
  return getJSON('/mock/mes/validation/cutting-optimization/value-comparison.json')
}

// 工段调度相关
export async function getEfficiencyMetrics() {
  return getJSON('/mock/mes/validation/workstation-scheduling/efficiency-metrics.json')
}
export async function getExceptionSamples() {
  return getJSON('/mock/mes/validation/exceptions.json')
}

// 交期承诺相关
export async function getAccuracyAnalysis() {
  return getJSON('/mock/mes/validation/delivery-promise/accuracy-analysis.json')
}

// 汇总入口（便于替换为真实API）
export const mesApi = {
  getJSON,
  getAlgorithmStrategy,
  getCuttingValueComparison,
  getEfficiencyMetrics,
  getExceptionSamples,
  getAccuracyAnalysis,
}

