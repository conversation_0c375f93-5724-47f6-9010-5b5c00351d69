export interface TelemetryEvent {
  name: string
  ts: number
  durationMs?: number
  props?: Record<string, any>
}

const _events: TelemetryEvent[] = []

export function recordEvent(name: string, props?: Record<string, any>, durationMs?: number) {
  _events.push({ name, ts: Date.now(), durationMs, props })
}

export function getEvents(): TelemetryEvent[] {
  return _events.slice(-200)
}

export function getSummary(): { name: string; count: number; avgDuration?: number }[] {
  const map = new Map<string, { count: number; total: number; samples: number }>()
  for (const e of _events) {
    const curr = map.get(e.name) || { count: 0, total: 0, samples: 0 }
    curr.count += 1
    if (typeof e.durationMs === 'number') {
      curr.total += e.durationMs
      curr.samples += 1
    }
    map.set(e.name, curr)
  }
  return Array.from(map.entries()).map(([name, v]) => ({ name, count: v.count, avgDuration: v.samples ? +(v.total / v.samples).toFixed(1) : undefined }))
}

