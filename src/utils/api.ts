/**
 * API 工具类 - 统一的数据加载接口和错误处理机制
 * 支持 Mock 数据和真实 API 的无缝切换
 */

// API 响应接口定义
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message: string;
  code?: string;
  timestamp?: string;
  details?: any;
}

// API 错误接口定义
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// API 配置接口
export interface ApiConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
  useMock?: boolean;
}

// 默认配置
const DEFAULT_CONFIG: ApiConfig = {
  baseURL: import.meta.env.DEV ? '' : '/api', // 在开发环境，mock文件从根路径获取
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  useMock: import.meta.env.DEV,
};

// 全局配置
let globalConfig: ApiConfig = { ...DEFAULT_CONFIG };

/**
 * 设置全局 API 配置
 */
export function setApiConfig(config: Partial<ApiConfig>): void {
  globalConfig = { ...globalConfig, ...config };
}

/**
 * 获取当前 API 配置
 */
export function getApiConfig(): ApiConfig {
  return { ...globalConfig };
}

/**
 * 创建 API 错误对象
 */
function createApiError(message: string, code: string = 'API_ERROR', details?: any): ApiError {
  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString(),
  };
}

/**
 * 统一的 fetch 封装函数
 * 支持自动错误处理、超时控制、请求重试等功能
 */
export async function apiFetch<T = any>(
  endpoint: string,
  options: RequestInit & { 
    config?: Partial<ApiConfig>;
    retries?: number;
    retryDelay?: number;
  } = {}
): Promise<ApiResponse<T>> {
  const { config = {}, retries = 0, retryDelay = 1000, ...fetchOptions } = options;
  const finalConfig = { ...globalConfig, ...config };
  
  // 如果是mock模式，endpoint应该直接指向public下的文件路径
  const mockEndpoint = finalConfig.useMock ? `/mock${endpoint.startsWith('/') ? '' : '/'}${endpoint}` : endpoint;
  
  // 构建完整 URL (FIXED: robust path joining)
  const baseURL = (finalConfig.baseURL || '').replace(/\/$/, '');
  const finalUrlPart = finalConfig.useMock ? mockEndpoint : (endpoint.startsWith('/') ? endpoint : `/${endpoint}`);
  const url = finalUrlPart.startsWith('http') ? finalUrlPart : `${baseURL}${finalUrlPart}`;

  // 设置默认请求头
  const headers = {
    ...finalConfig.headers,
    ...fetchOptions.headers,
  };

  // 创建 AbortController 用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
  }, finalConfig.timeout || 10000);

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // 检查 HTTP 状态
    if (!response.ok) {
      const errorText = await response.text();
      let errorData: any;
      
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { message: errorText };
      }

      throw createApiError(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        `HTTP_${response.status}`,
        errorData
      );
    }

    // 解析响应数据
    const contentType = response.headers.get('content-type');
    let data: any;

    if (contentType?.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    // 如果是 Mock 数据，并且已经符合 ApiResponse 格式，直接返回
    if (finalConfig.useMock && typeof data === 'object' && data !== null && data.success !== undefined) {
      return data as ApiResponse<T>;
    }

    // 包装成标准响应格式
    return {
      data: data as T,
      success: true,
      message: 'Request successful',
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    clearTimeout(timeoutId);

    // 如果是 AbortError（超时），创建超时错误
    if (error instanceof Error && error.name === 'AbortError') {
      const timeoutError = createApiError(
        `Request timeout after ${finalConfig.timeout}ms`,
        'TIMEOUT_ERROR'
      );
      
      // 重试逻辑
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return apiFetch<T>(endpoint, { 
          ...options, 
          retries: retries - 1,
          retryDelay: retryDelay * 1.5 // 指数退避
        });
      }
      
      throw timeoutError;
    }

    // 如果是网络错误，尝试重试
    if (error instanceof TypeError && error.message.includes('fetch')) {
      const networkError = createApiError(
        'Network error - please check your connection',
        'NETWORK_ERROR',
        error
      );
      
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return apiFetch<T>(endpoint, { 
          ...options, 
          retries: retries - 1,
          retryDelay: retryDelay * 1.5
        });
      }
      
      throw networkError;
    }

    // 重新抛出 API 错误
    if (typeof error === 'object' && error !== null && 'code' in error) {
      throw error;
    }

    // 包装未知错误
    throw createApiError(
      error instanceof Error ? error.message : 'Unknown error occurred',
      'UNKNOWN_ERROR',
      error
    );
  }
}

/**
 * GET 请求封装
 */
export async function apiGet<T = any>(
  endpoint: string,
  params?: Record<string, any>,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T>> {
  let url = endpoint;
  
  // 添加查询参数
  if (params && Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    url += `?${searchParams.toString()}`;
  }

  return apiFetch<T>(url, {
    ...options,
    method: 'GET',
  });
}

/**
 * POST 请求封装
 */
export async function apiPost<T = any>(
  endpoint: string,
  data?: any,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * PUT 请求封装
 */
export async function apiPut<T = any>(
  endpoint: string,
  data?: any,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * DELETE 请求封装
 */
export async function apiDelete<T = any>(
  endpoint: string,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, {
    ...options,
    method: 'DELETE',
  });
}

/**
 * 批量请求工具 (FIXED: preserves order and handles partial failures)
 */
export async function apiBatch<T = any>(
  requests: Array<{
    endpoint: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: any;
    params?: Record<string, any>;
  }>,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<(T | null)[]>> {
  try {
    const promises = requests.map(request => {
      switch (request.method || 'GET') {
        case 'GET':
          return apiGet<T>(request.endpoint, request.params, options);
        case 'POST':
          return apiPost<T>(request.endpoint, request.data, options);
        case 'PUT':
          return apiPut<T>(request.endpoint, request.data, options);
        case 'DELETE':
          return apiDelete<T>(request.endpoint, options);
        default:
          return Promise.reject(createApiError(`Unsupported method: ${request.method}`, 'INVALID_METHOD'));
      }
    });

    const results = await Promise.allSettled(promises);
    
    const errors: ApiError[] = [];
    const data = results.map((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        return result.value.data as T;
      } else {
        const reason = result.status === 'rejected'
          ? result.reason
          : createApiError(
              (result.value as ApiResponse<any>).message,
              (result.value as ApiResponse<any>).code || 'BATCH_ITEM_FAILURE',
              result.value
            );
        
        errors.push(createApiError(
          `Batch request ${index} ('${requests[index].method || 'GET'}' to '${requests[index].endpoint}') failed: ${reason.message}`,
          'BATCH_ITEM_FAILURE',
          reason
        ));
        return null;
      }
    });

    return {
      data,
      success: errors.length === 0,
      message: errors.length === 0 
        ? 'All batch requests completed successfully'
        : `${errors.length} of ${requests.length} requests failed`,
      code: errors.length === 0 ? 'BATCH_SUCCESS' : 'BATCH_PARTIAL_FAILURE',
      details: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    throw createApiError(
      'Batch request failed unexpectedly',
      'BATCH_UNEXPECTED_ERROR',
      error
    );
  }
}


import type { MaterialCategory, MaterialCategoryTreeNode } from '@/types/material';

/**
 * 从扁平列表构建分类树
 */
export function buildCategoryTree(categories: MaterialCategory[]): MaterialCategoryTreeNode[] {
  const tree: MaterialCategoryTreeNode[] = [];
  const map: Record<string, MaterialCategoryTreeNode> = {};

  // 1. 初始化所有节点
  categories.forEach(category => {
    map[category.categoryId] = {
      ...category,
      children: [],
      expanded: false, // 默认不展开
    };
  });

  // 2. 构建层级关系
  Object.values(map).forEach(node => {
    if (node.parentId && map[node.parentId]) {
      if (!map[node.parentId].children) {
        map[node.parentId].children = [];
      }
      map[node.parentId].children!.push(node);
    } else {
      tree.push(node);
    }
  });

  // 可选：对子节点进行排序
  Object.values(map).forEach(node => {
    if (node.children && node.children.length > 0) {
      node.children.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    }
  });
  
  // 对根节点排序
  tree.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  return tree;
}

/**
 * 获取物料分类树
 */
export async function getCategoryTree(options?: Parameters<typeof apiFetch>[1]): Promise<ApiResponse<MaterialCategoryTreeNode[]>> {
  try {
    // 1. 获取扁平的分类列表
    const response = await apiGet<{ materialCategories: MaterialCategory[] }>('metadata/materialCategories.json', undefined, options);

    if (!response.success) {
      // 如果获取失败，直接返回失败的响应
      return {
        ...response,
        data: [],
      };
    }

    // 2. 从响应数据中提取列表
    const flatCategories = response.data.materialCategories || [];

    // 3. 构建树形结构
    const categoryTree = buildCategoryTree(flatCategories);

    // 4. 返回包含树形结构的新响应
    return {
      ...response,
      data: categoryTree,
    };

  } catch (error) {
    // 捕获并处理构建过程中的任何错误
    const apiError = handleApiError(error);
    return {
      data: [],
      success: false,
      message: apiError.message,
      code: apiError.code,
      details: apiError.details,
      timestamp: apiError.timestamp,
    };
  }
}

/**
 * 错误处理工具函数
 */
export function handleApiError(error: unknown): ApiError {
  if (typeof error === 'object' && error !== null && 'code' in error) {
    return error as ApiError;
  }
  
  return createApiError(
    error instanceof Error ? error.message : 'Unknown error occurred',
    'UNKNOWN_ERROR',
    error
  );
}

/**
 * 检查响应是否成功
 */
export function isApiSuccess<T>(response: ApiResponse<T>): boolean {
  return response.success === true;
}

/**
 * 从响应中提取数据，如果失败则抛出错误
 */
export function extractApiData<T>(response: ApiResponse<T>): T {
  if (!isApiSuccess(response)) {
    throw createApiError(
      response.message || 'API request failed',
      'API_FAILURE',
      response
    );
  }
  return response.data;
}
