/**
 * 工艺段甘特图集成测试工具
 * 用于验证排产甘特图的数据一致性和功能完整性
 */

import type { ScheduledBatch, GanttChartData } from '@/types/scheduling';
import { processSegmentGanttService } from '@/services/processSegmentGanttService';

export class GanttIntegrationTest {
  /**
   * 验证工艺段甘特图数据一致性
   */
  static async validateProcessSegmentGantt(scheduledBatches: ScheduledBatch[]): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    ganttData?: GanttChartData;
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 1. 验证输入数据
      if (!scheduledBatches || scheduledBatches.length === 0) {
        errors.push('排产批次数据为空');
        return { isValid: false, errors, warnings };
      }

      // 2. 生成甘特图数据
      const ganttData = await processSegmentGanttService.convertToProcessSegmentView(scheduledBatches);

      // 3. 验证甘特图数据结构
      if (!ganttData.resources || ganttData.resources.length === 0) {
        errors.push('甘特图资源数据为空');
      }

      if (!ganttData.tasks || ganttData.tasks.length === 0) {
        warnings.push('甘特图任务数据为空');
      }

      // 4. 验证数据一致性
      this.validateDataConsistency(scheduledBatches, ganttData, errors, warnings);

      // 5. 验证时间范围
      this.validateTimeRange(scheduledBatches, ganttData, errors, warnings);

      // 6. 验证工艺段映射
      this.validateProcessSegmentMapping(scheduledBatches, ganttData, errors, warnings);

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        ganttData
      };

    } catch (error) {
      errors.push(`甘特图生成失败: ${(error as Error).message}`);
      return { isValid: false, errors, warnings };
    }
  }

  /**
   * 验证数据一致性
   */
  private static validateDataConsistency(
    scheduledBatches: ScheduledBatch[],
    ganttData: GanttChartData,
    errors: string[],
    warnings: string[]
  ) {
    // 验证任务数量
    const expectedTaskCount = scheduledBatches.length;
    const actualTaskCount = ganttData.tasks.length;
    
    if (actualTaskCount === 0) {
      errors.push('甘特图任务数量为0');
    } else if (actualTaskCount < expectedTaskCount) {
      warnings.push(`甘特图任务数量(${actualTaskCount})少于预期(${expectedTaskCount})`);
    }

    // 验证批次ID映射
    const batchIds = new Set(scheduledBatches.map(b => b.id));
    const taskBatchIds = new Set(ganttData.tasks.map(t => t.batchId).filter(Boolean));
    
    for (const batchId of batchIds) {
      if (!taskBatchIds.has(batchId)) {
        warnings.push(`批次 ${batchId} 未在甘特图中找到对应任务`);
      }
    }

    // 验证资源ID有效性
    const resourceIds = new Set(ganttData.resources.map(r => r.id));
    for (const task of ganttData.tasks) {
      if (!resourceIds.has(task.resourceId)) {
        errors.push(`任务 ${task.id} 引用了不存在的资源 ${task.resourceId}`);
      }
    }
  }

  /**
   * 验证时间范围
   */
  private static validateTimeRange(
    scheduledBatches: ScheduledBatch[],
    ganttData: GanttChartData,
    errors: string[],
    warnings: string[]
  ) {
    if (!ganttData.timeRange) {
      errors.push('甘特图缺少时间范围信息');
      return;
    }

    const ganttStart = new Date(ganttData.timeRange.start);
    const ganttEnd = new Date(ganttData.timeRange.end);

    if (ganttStart >= ganttEnd) {
      errors.push('甘特图时间范围无效：开始时间不能晚于结束时间');
    }

    // 验证任务时间是否在时间范围内
    for (const task of ganttData.tasks) {
      const taskStart = new Date(task.start);
      const taskEnd = new Date(task.end);

      if (taskStart < ganttStart || taskEnd > ganttEnd) {
        warnings.push(`任务 ${task.name} 的时间超出了甘特图时间范围`);
      }

      if (taskStart >= taskEnd) {
        errors.push(`任务 ${task.name} 的时间范围无效`);
      }
    }
  }

  /**
   * 验证工艺段映射
   */
  private static validateProcessSegmentMapping(
    scheduledBatches: ScheduledBatch[],
    ganttData: GanttChartData,
    errors: string[],
    warnings: string[]
  ) {
    // 验证工艺段资源类型
    const processSegmentResources = ganttData.resources.filter(r => r.type === 'process_segment');
    
    if (processSegmentResources.length === 0) {
      errors.push('未找到工艺段类型的资源');
      return;
    }

    // 验证工艺段名称
    const expectedSegments = ['冷工段', '热工段', '夹胶工段', '中空工段', '包装工段'];
    const actualSegments = processSegmentResources.map(r => r.name);
    
    for (const expected of expectedSegments) {
      if (!actualSegments.includes(expected)) {
        warnings.push(`缺少预期的工艺段: ${expected}`);
      }
    }

    // 验证工艺段状态
    for (const resource of processSegmentResources) {
      if (!resource.status || !['normal', 'bottleneck', 'idle'].includes(resource.status)) {
        warnings.push(`工艺段 ${resource.name} 的状态无效或缺失`);
      }

      if (resource.utilization === undefined || resource.utilization < 0 || resource.utilization > 100) {
        warnings.push(`工艺段 ${resource.name} 的利用率数据无效`);
      }
    }
  }

  /**
   * 生成测试报告
   */
  static generateTestReport(testResult: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    ganttData?: GanttChartData;
  }): string {
    const lines: string[] = [];
    
    lines.push('=== 工艺段甘特图集成测试报告 ===');
    lines.push(`测试时间: ${new Date().toLocaleString('zh-CN')}`);
    lines.push(`测试结果: ${testResult.isValid ? '✅ 通过' : '❌ 失败'}`);
    lines.push('');

    if (testResult.errors.length > 0) {
      lines.push('🚨 错误信息:');
      testResult.errors.forEach((error, index) => {
        lines.push(`  ${index + 1}. ${error}`);
      });
      lines.push('');
    }

    if (testResult.warnings.length > 0) {
      lines.push('⚠️ 警告信息:');
      testResult.warnings.forEach((warning, index) => {
        lines.push(`  ${index + 1}. ${warning}`);
      });
      lines.push('');
    }

    if (testResult.ganttData) {
      lines.push('📊 甘特图数据统计:');
      lines.push(`  资源数量: ${testResult.ganttData.resources.length}`);
      lines.push(`  任务数量: ${testResult.ganttData.tasks.length}`);
      lines.push(`  工艺段数量: ${testResult.ganttData.resources.filter(r => r.type === 'process_segment').length}`);
      
      if (testResult.ganttData.timeRange) {
        lines.push(`  时间范围: ${testResult.ganttData.timeRange.start} ~ ${testResult.ganttData.timeRange.end}`);
      }
      lines.push('');
    }

    if (testResult.isValid) {
      lines.push('✨ 所有验证项目均通过，工艺段甘特图集成成功！');
    } else {
      lines.push('💡 建议修复上述错误后重新测试。');
    }

    return lines.join('\n');
  }

  /**
   * 快速测试方法（用于开发调试）
   */
  static async quickTest(scheduledBatches: ScheduledBatch[]): Promise<void> {
    console.log('🧪 开始工艺段甘特图集成测试...');
    
    const testResult = await this.validateProcessSegmentGantt(scheduledBatches);
    const report = this.generateTestReport(testResult);
    
    console.log(report);
    
    if (!testResult.isValid) {
      console.error('❌ 测试失败，请检查错误信息');
    } else {
      console.log('✅ 测试通过！');
    }
  }
}

// 导出便捷方法
export const testProcessSegmentGantt = GanttIntegrationTest.quickTest;
export const validateGanttIntegration = GanttIntegrationTest.validateProcessSegmentGantt;
