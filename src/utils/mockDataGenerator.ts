import type { SelectedOrderItem } from '@/types/production-order-creation'

/**
 * 生成模拟的已选订单项数据
 */
export function generateMockSelectedItems(): SelectedOrderItem[] {
  return [
    {
      id: 'item-1',
      customerOrderId: 'order-1',
      orderNumber: 'CO-2024001',
      customerName: '华润置地',
      specifications: {
        length: 1800,
        width: 1200,
        thickness: 6,
        glassType: 'clear',
        color: '透明'
      },
      totalQuantity: 450,
      selectedQuantity: 450,
      processFlow: [
        {
          stepName: '切割',
          workstation: 'cutting_station_1',
          estimatedDuration: 15,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '磨边',
          workstation: 'edging_station_1',
          estimatedDuration: 20,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '钢化',
          workstation: 'tempering_furnace_1',
          estimatedDuration: 45,
          constraints: ['temperature_control'],
          status: 'pending'
        },
        {
          stepName: '质检',
          workstation: 'quality_station',
          estimatedDuration: 10,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '包装',
          workstation: 'packaging_station',
          estimatedDuration: 5,
          constraints: [],
          status: 'pending'
        }
      ],
      deliveryDate: '2024-02-15',
      originalItem: {} as any
    },
    {
      id: 'item-2',
      customerOrderId: 'order-2',
      orderNumber: 'CO-2024002',
      customerName: '万科集团',
      specifications: {
        length: 2400,
        width: 1600,
        thickness: 8,
        glassType: 'low_e',
        color: '透明'
      },
      totalQuantity: 280,
      selectedQuantity: 280,
      processFlow: [
        {
          stepName: '切割',
          workstation: 'cutting_station_1',
          estimatedDuration: 18,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '磨边',
          workstation: 'edging_station_1',
          estimatedDuration: 25,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '镀膜',
          workstation: 'coating_line',
          estimatedDuration: 40,
          constraints: ['clean_environment'],
          status: 'pending'
        },
        {
          stepName: '钢化',
          workstation: 'tempering_furnace_1',
          estimatedDuration: 50,
          constraints: ['temperature_control'],
          status: 'pending'
        },
        {
          stepName: '质检',
          workstation: 'quality_station',
          estimatedDuration: 15,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '包装',
          workstation: 'packaging_station',
          estimatedDuration: 8,
          constraints: [],
          status: 'pending'
        }
      ],
      deliveryDate: '2024-02-20',
      originalItem: {} as any
    },
    {
      id: 'item-3',
      customerOrderId: 'order-3',
      orderNumber: 'CO-2024003',
      customerName: '碧桂园',
      specifications: {
        length: 1500,
        width: 1000,
        thickness: 6,
        glassType: 'clear',
        color: '透明'
      },
      totalQuantity: 320,
      selectedQuantity: 320,
      processFlow: [
        {
          stepName: '切割',
          workstation: 'cutting_station_2',
          estimatedDuration: 12,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '磨边',
          workstation: 'edging_station_2',
          estimatedDuration: 18,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '质检',
          workstation: 'quality_station',
          estimatedDuration: 8,
          constraints: [],
          status: 'pending'
        },
        {
          stepName: '包装',
          workstation: 'packaging_station',
          estimatedDuration: 5,
          constraints: [],
          status: 'pending'
        }
      ],
      deliveryDate: '2024-02-18',
      originalItem: {} as any
    }
  ]
}

/**
 * 生成模拟的批次优化结果
 */
export function generateMockBatchOptimization(selectedItems: SelectedOrderItem[]) {
  const totalQuantity = selectedItems.reduce((sum, item) => sum + item.selectedQuantity, 0)
  
  return {
    efficiency: 15,
    timeSaved: 2.5,
    batches: [
      {
        id: 'batch_1',
        name: '批次 1 - 钢化玻璃',
        items: selectedItems.filter(item => 
          item.processFlow.some(step => step.stepName === '钢化')
        ),
        specifications: '钢化玻璃批次',
        totalQuantity: selectedItems
          .filter(item => item.processFlow.some(step => step.stepName === '钢化'))
          .reduce((sum, item) => sum + item.selectedQuantity, 0),
        estimatedDuration: 180,
        estimatedTime: 180,
        workstations: ['cutting_station_1', 'edging_station_1', 'tempering_furnace_1', 'quality_station', 'packaging_station'],
        workstationGroup: '钢化工艺组',
        priority: 'high',
        utilization: 85,
        processFlow: [
          { stepName: '切割', workstation: 'cutting_station_1', estimatedDuration: 15, constraints: [], status: 'pending' },
          { stepName: '磨边', workstation: 'edging_station_1', estimatedDuration: 20, constraints: [], status: 'pending' },
          { stepName: '钢化', workstation: 'tempering_furnace_1', estimatedDuration: 45, constraints: ['temperature_control'], status: 'pending' },
          { stepName: '质检', workstation: 'quality_station', estimatedDuration: 10, constraints: [], status: 'pending' },
          { stepName: '包装', workstation: 'packaging_station', estimatedDuration: 5, constraints: [], status: 'pending' }
        ]
      },
      {
        id: 'batch_2',
        name: '批次 2 - 普通玻璃',
        items: selectedItems.filter(item => 
          !item.processFlow.some(step => step.stepName === '钢化')
        ),
        specifications: '普通玻璃批次',
        totalQuantity: selectedItems
          .filter(item => !item.processFlow.some(step => step.stepName === '钢化'))
          .reduce((sum, item) => sum + item.selectedQuantity, 0),
        estimatedDuration: 90,
        estimatedTime: 90,
        workstations: ['cutting_station_2', 'edging_station_2', 'quality_station', 'packaging_station'],
        workstationGroup: '冷加工组',
        priority: 'normal',
        utilization: 78,
        processFlow: [
          { stepName: '切割', workstation: 'cutting_station_2', estimatedDuration: 12, constraints: [], status: 'pending' },
          { stepName: '磨边', workstation: 'edging_station_2', estimatedDuration: 18, constraints: [], status: 'pending' },
          { stepName: '质检', workstation: 'quality_station', estimatedDuration: 8, constraints: [], status: 'pending' },
          { stepName: '包装', workstation: 'packaging_station', estimatedDuration: 5, constraints: [], status: 'pending' }
        ]
      }
    ],
    recommendations: [
      '建议将钢化玻璃产品安排在同一批次，提高炉温利用效率',
      '普通玻璃产品可以使用较小的工作站，节省大型设备资源',
      '建议在生产负荷较低时段安排复杂工艺产品'
    ],
    totalItems: selectedItems.length,
    totalQuantity
  }
}