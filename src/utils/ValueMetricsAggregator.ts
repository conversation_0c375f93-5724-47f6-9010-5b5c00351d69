export interface ValueMetric { name: string; target: number; actual: number; unit: string; period: string; notes?: string }

export async function aggregateMetrics() : Promise<ValueMetric[]> {
  const [value, eff, acc] = await Promise.all([
    fetch('/mock/mes/validation/cutting-optimization/value-comparison.json').then(r => r.json()),
    fetch('/mock/mes/validation/workstation-scheduling/efficiency-metrics.json').then(r => r.json()),
    fetch('/mock/mes/validation/delivery-promise/accuracy-analysis.json').then(r => r.json())
  ])

  const period = '2024Q1'
  const metrics: ValueMetric[] = [
    { name: '原片利用率提升', target: 5, actual: value.valueMetrics.utilizationImprovement, unit: '%', period },
    { name: '年化成本节省', target: 0, actual: value.valueMetrics.annualSaving, unit: '元', period },
    { name: '平均设备利用率提升', target: 8, actual: (eff.optimizedFlow.averageUtilization - eff.traditionalFlow.averageUtilization), unit: '%', period },
    { name: '交期准确率提升', target: 20, actual: (acc.smartMethod.accuracyRate - acc.traditionalMethod.accuracyRate), unit: '%', period },
    { name: '投诉减少', target: 40, actual: acc.businessImpact.complaintReduction, unit: '%', period }
  ]

  return metrics
}

