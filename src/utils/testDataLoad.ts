/**
 * 测试数据加载工具
 * 用于验证产品结构数据是否能正确加载
 */

import { productStructureService } from '@/services/productService';

export async function testProductStructureDataLoad() {
  try {
    console.log('🔍 开始测试产品结构数据加载...');
    
    // 测试获取所有产品结构
    const structures = await productStructureService.getStructures();
    console.log(`✅ 成功加载 ${structures.length} 个产品结构:`);
    
    structures.forEach((structure, index) => {
      console.log(`  ${index + 1}. ${structure.name} (${structure.code})`);
      console.log(`     - 类型: ${structure.productType}`);
      console.log(`     - 类别: ${structure.category}`);
      console.log(`     - 状态: ${structure.status}`);
      console.log(`     - 产品参数: ${structure.productParameters?.length || 0} 个`);
      console.log(`     - 产品约束: ${structure.productConstraints?.length || 0} 个`);
      console.log(`     - 标签: ${structure.tags?.join(', ') || '无'}`);
      
      if (structure.rootAssembly) {
        console.log(`     - 根构件: ${structure.rootAssembly.assemblyName}`);
        console.log(`     - 组件实例: ${structure.rootAssembly.componentInstances?.length || 0} 个`);
      }
      console.log('');
    });
    
    // 测试按ID获取特定结构
    if (structures.length > 0) {
      const firstStructure = structures[0];
      console.log(`🔍 测试按ID获取结构: ${firstStructure.id}`);
      
      const structureById = await productStructureService.getStructureById(firstStructure.id);
      if (structureById) {
        console.log(`✅ 成功获取结构: ${structureById.name}`);
      } else {
        console.log('❌ 未找到指定结构');
      }
    }
    
    // 测试筛选功能
    console.log('🔍 测试筛选功能...');
    
    const windowStructures = await productStructureService.getStructures({
      productType: ['window']
    });
    console.log(`✅ 筛选窗户类型: ${windowStructures.length} 个结果`);
    
    const activeStructures = await productStructureService.getStructures({
      status: ['active']
    });
    console.log(`✅ 筛选活跃状态: ${activeStructures.length} 个结果`);
    
    const keywordSearch = await productStructureService.getStructures({
      keyword: '防火'
    });
    console.log(`✅ 关键词搜索"防火": ${keywordSearch.length} 个结果`);
    
    console.log('🎉 所有测试通过！数据加载正常。');
    return true;
    
  } catch (error) {
    console.error('❌ 数据加载测试失败:', error);
    return false;
  }
}

// 在浏览器控制台中可以调用的全局函数
if (typeof window !== 'undefined') {
  (window as any).testProductStructureDataLoad = testProductStructureDataLoad;
}
