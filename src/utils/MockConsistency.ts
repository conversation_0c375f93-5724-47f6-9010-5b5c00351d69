// 最小一致性模拟：基于版本号的简易并发冲突
let version = 1

export interface VersionedUpdate<T> { entityId: string; expectedVersion: number; payload: T }
export interface UpdateResult { ok: boolean; newVersion?: number; conflict?: boolean }

export async function simulateUpdate<T>(_req: VersionedUpdate<T>): Promise<UpdateResult> {
  // 50% 概率制造冲突
  const conflict = Math.random() < 0.5
  if (conflict) {
    return { ok: false, conflict: true }
  }
  version += 1
  return { ok: true, newVersion: version }
}

