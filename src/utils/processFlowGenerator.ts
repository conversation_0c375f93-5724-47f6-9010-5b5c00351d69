/**
 * 工艺流程生成器
 * 根据玻璃规格和类型自动生成标准工艺流程
 */

export interface ProcessStep {
  stepName: string
  workstation: string
  estimatedDuration: number
  constraints: string[]
  status: 'pending' | 'in_progress' | 'completed'
}

export interface GlassSpecifications {
  length: number
  width: number
  thickness: number
  glassType: 'clear' | 'tinted' | 'low_e' | 'reflective' | 'tempered' | 'laminated' | 'insulating'
  color?: string
}

/**
 * 根据玻璃规格生成标准工艺流程
 */
export function generateStandardProcessFlow(specifications: GlassSpecifications): ProcessStep[] {
  const flow: ProcessStep[] = []
  
  // 1. 切割工序（必须）
  flow.push({
    stepName: '切割',
    workstation: getOptimalCuttingStation(specifications),
    estimatedDuration: calculateCuttingTime(specifications),
    constraints: [],
    status: 'pending'
  })
  
  // 2. 磨边工序（必须）
  flow.push({
    stepName: '磨边',
    workstation: getOptimalEdgingStation(specifications),
    estimatedDuration: calculateEdgingTime(specifications),
    constraints: [],
    status: 'pending'
  })
  
  // 3. 特殊工艺（根据玻璃类型）
  if (specifications.glassType === 'low_e' || specifications.glassType === 'reflective') {
    flow.push({
      stepName: '镀膜',
      workstation: 'coating_line',
      estimatedDuration: calculateCoatingTime(specifications),
      constraints: ['clean_environment'],
      status: 'pending'
    })
  }
  
  // 4. 钢化工序（根据厚度和类型）
  if (needsTempering(specifications)) {
    flow.push({
      stepName: '钢化',
      workstation: getOptimalTemperingFurnace(specifications),
      estimatedDuration: calculateTemperingTime(specifications),
      constraints: ['temperature_control'],
      status: 'pending'
    })
  }
  
  // 5. 夹胶工序
  if (specifications.glassType === 'laminated') {
    flow.push({
      stepName: '夹胶',
      workstation: 'laminating_line',
      estimatedDuration: calculateLaminatingTime(specifications),
      constraints: ['humidity_control', 'clean_environment'],
      status: 'pending'
    })
  }
  
  // 6. 中空合片工序
  if (specifications.glassType === 'insulating') {
    flow.push({
      stepName: '中空合片',
      workstation: 'insulating_line',
      estimatedDuration: calculateInsulatingTime(specifications),
      constraints: ['clean_environment'],
      status: 'pending'
    })
  }
  
  // 7. 质检工序（必须）
  flow.push({
    stepName: '质检',
    workstation: 'quality_station',
    estimatedDuration: calculateQualityCheckTime(specifications),
    constraints: [],
    status: 'pending'
  })
  
  // 8. 包装工序（必须）
  flow.push({
    stepName: '包装',
    workstation: 'packaging_station',
    estimatedDuration: calculatePackagingTime(specifications),
    constraints: [],
    status: 'pending'
  })
  
  return flow
}

/**
 * 获取最优切割工位
 */
function getOptimalCuttingStation(specs: GlassSpecifications): string {
  const area = specs.length * specs.width
  
  // 大尺寸玻璃使用大型切割台
  if (area > 3000000) { // 3平米以上
    return 'cutting_station_1'
  }
  
  return 'cutting_station_2'
}

/**
 * 获取最优磨边工位
 */
function getOptimalEdgingStation(specs: GlassSpecifications): string {
  // 厚玻璃使用重型磨边机
  if (specs.thickness >= 8) {
    return 'edging_station_1'
  }
  
  return 'edging_station_2'
}

/**
 * 获取最优钢化炉
 */
function getOptimalTemperingFurnace(specs: GlassSpecifications): string {
  // Low-E玻璃需要特殊钢化炉
  if (specs.glassType === 'low_e') {
    return 'tempering_furnace_1'
  }
  
  return 'tempering_furnace_2'
}

/**
 * 判断是否需要钢化
 */
function needsTempering(specs: GlassSpecifications): boolean {
  // 厚度>=5mm或特殊玻璃类型需要钢化
  return specs.thickness >= 5 || 
         specs.glassType === 'tempered' || 
         specs.glassType === 'low_e'
}

/**
 * 计算切割时间
 */
function calculateCuttingTime(specs: GlassSpecifications): number {
  const area = specs.length * specs.width / 1000000 // 转换为平米
  const baseTime = 10
  const areaFactor = area * 2 // 每平米增加2分钟
  const thicknessFactor = specs.thickness > 6 ? 5 : 0 // 厚玻璃增加5分钟
  
  return Math.round(baseTime + areaFactor + thicknessFactor)
}

/**
 * 计算磨边时间
 */
function calculateEdgingTime(specs: GlassSpecifications): number {
  const perimeter = (specs.length + specs.width) * 2 / 1000 // 周长，转换为米
  const baseTime = 15
  const perimeterFactor = perimeter * 1.5 // 每米周长增加1.5分钟
  const thicknessFactor = specs.thickness > 6 ? 8 : 0 // 厚玻璃增加8分钟
  
  return Math.round(baseTime + perimeterFactor + thicknessFactor)
}

/**
 * 计算镀膜时间
 */
function calculateCoatingTime(specs: GlassSpecifications): number {
  const area = specs.length * specs.width / 1000000
  const baseTime = 25
  const areaFactor = area * 3 // 每平米增加3分钟
  
  return Math.round(baseTime + areaFactor)
}

/**
 * 计算钢化时间
 */
function calculateTemperingTime(specs: GlassSpecifications): number {
  const area = specs.length * specs.width / 1000000
  const baseTime = 35
  const areaFactor = area * 2 // 每平米增加2分钟
  const thicknessFactor = specs.thickness > 8 ? 10 : 0 // 超厚玻璃增加10分钟
  
  return Math.round(baseTime + areaFactor + thicknessFactor)
}

/**
 * 计算夹胶时间
 */
function calculateLaminatingTime(specs: GlassSpecifications): number {
  const area = specs.length * specs.width / 1000000
  const baseTime = 60
  const areaFactor = area * 5 // 每平米增加5分钟
  
  return Math.round(baseTime + areaFactor)
}

/**
 * 计算中空合片时间
 */
function calculateInsulatingTime(specs: GlassSpecifications): number {
  const area = specs.length * specs.width / 1000000
  const baseTime = 40
  const areaFactor = area * 3 // 每平米增加3分钟
  
  return Math.round(baseTime + areaFactor)
}

/**
 * 计算质检时间
 */
function calculateQualityCheckTime(specs: GlassSpecifications): number {
  const area = specs.length * specs.width / 1000000
  const baseTime = 5
  const areaFactor = area * 1 // 每平米增加1分钟
  const complexityFactor = specs.glassType !== 'clear' ? 3 : 0 // 特殊玻璃增加3分钟
  
  return Math.round(baseTime + areaFactor + complexityFactor)
}

/**
 * 计算包装时间
 */
function calculatePackagingTime(specs: GlassSpecifications): number {
  const area = specs.length * specs.width / 1000000
  const baseTime = 3
  const areaFactor = area * 0.5 // 每平米增加0.5分钟
  
  return Math.round(baseTime + areaFactor)
}

/**
 * 为现有订单项补充工艺流程
 */
export function addProcessFlowToOrderItem(orderItem: any): any {
  if (orderItem.processFlow && orderItem.processFlow.length > 0) {
    return orderItem // 已有工艺流程，不需要修改
  }
  
  return {
    ...orderItem,
    processFlow: generateStandardProcessFlow(orderItem.specifications)
  }
}

/**
 * 批量为订单项添加工艺流程
 */
export function addProcessFlowToOrders(orders: any[]): any[] {
  return orders.map(order => ({
    ...order,
    items: order.items.map(addProcessFlowToOrderItem)
  }))
}