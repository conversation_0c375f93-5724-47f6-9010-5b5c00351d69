/**
 * 公式计算器 - 支持产品参数化配置中的公式计算
 * 用于处理产品结构中的参数依赖关系和数值计算
 */

export interface CalculationContext {
  [key: string]: number | string | boolean
}

export interface CalculationResult {
  value: number | string
  isValid: boolean
  error?: string
  dependencies: string[]
}

export class FormulaCalculator {
  /**
   * 计算参数化公式的值
   * @param formula 公式字符串，如 "width * height / 1000000"
   * @param context 参数上下文，包含所有可用的参数值
   * @returns 计算结果
   */
  static calculateValue(
    formula: string,
    context: CalculationContext
  ): CalculationResult {
    try {
      // 提取公式中的依赖参数
      const dependencies = this.extractDependencies(formula)
      
      // 验证所有依赖参数是否存在
      const missingDeps = dependencies.filter(dep => !(dep in context))
      if (missingDeps.length > 0) {
        return {
          value: 0,
          isValid: false,
          error: `缺少参数: ${missingDeps.join(', ')}`,
          dependencies
        }
      }

      // 替换公式中的参数变量
      const expression = this.replaceVariables(formula, context)
      
      // 计算表达式
      const value = this.evaluateExpression(expression)
      
      return {
        value,
        isValid: true,
        dependencies
      }
    } catch (error) {
      return {
        value: 0,
        isValid: false,
        error: error instanceof Error ? error.message : '公式计算错误',
        dependencies: []
      }
    }
  }

  /**
   * 批量计算多个公式
   * @param formulas 公式映射对象
   * @param context 参数上下文
   * @returns 计算结果映射
   */
  static calculateMultiple(
    formulas: Record<string, string>,
    context: CalculationContext
  ): Record<string, CalculationResult> {
    const results: Record<string, CalculationResult> = {}
    
    // 按依赖关系排序，确保依赖的参数先计算
    const sortedKeys = this.sortByDependencies(formulas, context)
    
    const extendedContext = { ...context }
    
    for (const key of sortedKeys) {
      const result = this.calculateValue(formulas[key], extendedContext)
      results[key] = result
      
      // 将计算结果添加到上下文中，供后续公式使用
      if (result.isValid && typeof result.value === 'number') {
        extendedContext[key] = result.value
      }
    }
    
    return results
  }

  /**
   * 提取公式中的变量依赖
   */
  private static extractDependencies(formula: string): string[] {
    // 匹配变量名（字母开头，可包含字母、数字、下划线）
    const variablePattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g
    const matches = formula.match(variablePattern) || []
    
    // 过滤掉数学函数名
    const mathFunctions = ['ceil', 'floor', 'round', 'abs', 'max', 'min', 'sqrt']
    const variables = matches.filter(match => !mathFunctions.includes(match))
    
    // 去重并返回
    return Array.from(new Set(variables))
  }

  /**
   * 替换公式中的变量
   */
  private static replaceVariables(
    formula: string,
    context: CalculationContext
  ): string {
    let expression = formula
    
    // 按变量名长度倒序排列，避免短变量名替换长变量名的问题
    const sortedVars = Object.keys(context).sort((a, b) => b.length - a.length)
    
    for (const variable of sortedVars) {
      const value = context[variable]
      // 使用正则表达式确保只替换完整的变量名
      const regex = new RegExp(`\\b${variable}\\b`, 'g')
      expression = expression.replace(regex, String(value))
    }
    
    return expression
  }

  /**
   * 安全地计算数学表达式
   */
  private static evaluateExpression(expression: string): number {
    // 验证表达式安全性
    if (!this.isExpressionSafe(expression)) {
      throw new Error('不安全的表达式')
    }
    
    try {
      // 支持的数学函数
      const mathFunctions = {
        ceil: Math.ceil,
        floor: Math.floor,
        round: Math.round,
        abs: Math.abs,
        max: Math.max,
        min: Math.min,
        sqrt: Math.sqrt
      }
      
      // 创建安全的计算环境
      const safeEval = new Function(
        'ceil', 'floor', 'round', 'abs', 'max', 'min', 'sqrt',
        `"use strict"; return (${expression})`
      )
      
      const result = safeEval(
        mathFunctions.ceil,
        mathFunctions.floor,
        mathFunctions.round,
        mathFunctions.abs,
        mathFunctions.max,
        mathFunctions.min,
        mathFunctions.sqrt
      )
      
      if (typeof result !== 'number' || !isFinite(result)) {
        throw new Error('计算结果无效')
      }
      
      return result
    } catch (error) {
      throw new Error(`表达式计算失败: ${error}`)
    }
  }

  /**
   * 验证表达式安全性
   */
  private static isExpressionSafe(expression: string): boolean {
    // 只允许数字、基本运算符、括号和支持的函数
    const safePattern = /^[0-9+\-*/.() \t\n,a-zA-Z_]+$/
    if (!safePattern.test(expression)) {
      return false
    }
    
    // 禁止的关键字
    const forbiddenKeywords = [
      'eval', 'function', 'Function', 'constructor', 'prototype',
      'window', 'document', 'global', 'process', 'require', 'import'
    ]
    
    for (const keyword of forbiddenKeywords) {
      if (expression.includes(keyword)) {
        return false
      }
    }
    
    return true
  }

  /**
   * 按依赖关系对公式进行拓扑排序
   */
  private static sortByDependencies(
    formulas: Record<string, string>,
    context: CalculationContext
  ): string[] {
    const keys = Object.keys(formulas)
    const dependencies: Record<string, string[]> = {}
    
    // 构建依赖图
    for (const key of keys) {
      const deps = this.extractDependencies(formulas[key])
      // 只考虑在公式集合中的依赖
      dependencies[key] = deps.filter(dep => keys.includes(dep))
    }
    
    // 拓扑排序
    const sorted: string[] = []
    const visited = new Set<string>()
    const visiting = new Set<string>()
    
    const visit = (key: string) => {
      if (visiting.has(key)) {
        throw new Error(`检测到循环依赖: ${key}`)
      }
      if (visited.has(key)) {
        return
      }
      
      visiting.add(key)
      
      for (const dep of dependencies[key] || []) {
        visit(dep)
      }
      
      visiting.delete(key)
      visited.add(key)
      sorted.push(key)
    }
    
    for (const key of keys) {
      if (!visited.has(key)) {
        visit(key)
      }
    }
    
    return sorted
  }

  /**
   * 格式化计算结果用于显示
   */
  static formatResult(
    result: CalculationResult,
    precision: number = 2,
    unit?: string
  ): string {
    if (!result.isValid) {
      return `错误: ${result.error}`
    }
    
    if (typeof result.value === 'number') {
      const formatted = result.value.toFixed(precision)
      return unit ? `${formatted} ${unit}` : formatted
    }
    
    return String(result.value)
  }
}