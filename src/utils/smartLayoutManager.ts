/**
 * 智能布局管理器
 * 负责订单项选择面板的空间分配和响应式布局
 */

export interface LayoutState {
  // 显示状态
  showDetailsPanel: boolean
  detailsPanelHeight: number
  quickPreviewVisible: boolean
  
  // 空间分配
  orderListHeight: number
  availableHeight: number
  
  // 响应式状态
  screenSize: 'sm' | 'md' | 'lg'
  isCompactMode: boolean
  
  // 动画状态
  isTransitioning: boolean
  transitionDuration: number
}

export interface LayoutConfiguration {
  // 空间限制
  maxDetailsPanelHeight: number
  minOrderListHeight: number
  quickPreviewHeight: number
  searchFilterHeight: number
  
  // 响应式断点
  breakpoints: {
    sm: number
    md: number
    lg: number
  }
  
  // 动画配置
  transitionDuration: number
  easeFunction: string
}

export interface LayoutAllocation {
  orderListHeight: number
  detailsPanelHeight: number
  quickPreviewHeight: number
  searchFilterHeight: number
}

export interface SelectionSummary {
  totalItems: number
  totalQuantity: number
  uniqueCustomers: number
  hasConflicts: boolean
  estimatedValue: number
  earliestDelivery: string
  latestDelivery: string
}

export class SmartLayoutManager {
  private config: LayoutConfiguration
  private state: LayoutState
  
  constructor(config?: Partial<LayoutConfiguration>) {
    this.config = {
      maxDetailsPanelHeight: 320,
      minOrderListHeight: 200,
      quickPreviewHeight: 40,
      searchFilterHeight: 60,
      breakpoints: {
        sm: 768,
        md: 1024,
        lg: 1280
      },
      transitionDuration: 300,
      easeFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
      ...config
    }
    
    this.state = this.initializeState()
  }
  
  private initializeState(): LayoutState {
    return {
      showDetailsPanel: false,
      detailsPanelHeight: 0,
      quickPreviewVisible: false,
      orderListHeight: 0,
      availableHeight: 0,
      screenSize: 'lg',
      isCompactMode: false,
      isTransitioning: false,
      transitionDuration: this.config.transitionDuration
    }
  }
  
  /**
   * 计算最优空间分配
   */
  calculateOptimalLayout(
    containerHeight: number,
    selectedItemsCount: number,
    hasConflicts: boolean,
    showDetails: boolean = false
  ): LayoutAllocation {
    const fixedHeight = this.calculateFixedHeight(selectedItemsCount > 0)
    const availableHeight = containerHeight - fixedHeight
    
    // 根据选中项数量和冲突情况调整详情面板高度
    const detailsPanelHeight = showDetails ? this.calculateDetailsPanelHeight(
      selectedItemsCount,
      hasConflicts,
      availableHeight
    ) : 0
    
    const orderListHeight = Math.max(
      availableHeight - detailsPanelHeight,
      this.config.minOrderListHeight
    )
    
    return {
      orderListHeight,
      detailsPanelHeight,
      quickPreviewHeight: selectedItemsCount > 0 ? this.config.quickPreviewHeight : 0,
      searchFilterHeight: this.config.searchFilterHeight
    }
  }
  
  /**
   * 响应式布局调整
   */
  adjustForScreenSize(screenWidth: number, screenHeight: number): void {
    const screenSize = this.determineScreenSize(screenWidth)
    const isCompactMode = screenHeight < 600
    
    this.state.screenSize = screenSize
    this.state.isCompactMode = isCompactMode
    
    // 根据屏幕尺寸调整配置
    if (isCompactMode) {
      this.config.maxDetailsPanelHeight = Math.min(
        this.config.maxDetailsPanelHeight,
        screenHeight * 0.4
      )
    }
    
    // 小屏幕适配
    if (screenSize === 'sm') {
      this.config.maxDetailsPanelHeight = Math.min(
        this.config.maxDetailsPanelHeight,
        240
      )
      this.config.quickPreviewHeight = 35
    }
  }
  
  /**
   * 平滑过渡动画
   */
  async animateLayoutChange(
    fromLayout: LayoutAllocation,
    toLayout: LayoutAllocation
  ): Promise<void> {
    return new Promise((resolve) => {
      this.state.isTransitioning = true
      
      // 使用CSS动画
      setTimeout(() => {
        this.state.isTransitioning = false
        resolve()
      }, this.config.transitionDuration)
    })
  }
  
  /**
   * 获取当前状态
   */
  getState(): LayoutState {
    return { ...this.state }
  }
  
  /**
   * 更新状态
   */
  updateState(updates: Partial<LayoutState>): void {
    this.state = { ...this.state, ...updates }
  }
  
  /**
   * 计算选择摘要
   */
  calculateSelectionSummary(selectedItems: any[]): SelectionSummary {
    const totalItems = selectedItems.length
    const totalQuantity = selectedItems.reduce((sum, item) => sum + item.selectedQuantity, 0)
    const uniqueCustomers = new Set(selectedItems.map(item => item.customerName)).size
    const hasConflicts = selectedItems.some(item => item.hasConflict)
    
    const deliveryDates = selectedItems
      .map(item => new Date(item.deliveryDate))
      .filter(date => !isNaN(date.getTime()))
      .sort((a, b) => a.getTime() - b.getTime())
    
    return {
      totalItems,
      totalQuantity,
      uniqueCustomers,
      hasConflicts,
      estimatedValue: 0, // 需要根据实际业务逻辑计算
      earliestDelivery: deliveryDates.length > 0 ? deliveryDates[0].toISOString() : '',
      latestDelivery: deliveryDates.length > 0 ? deliveryDates[deliveryDates.length - 1].toISOString() : ''
    }
  }
  
  /**
   * 获取响应式CSS类
   */
  getResponsiveClasses(): string[] {
    const classes = [`screen-${this.state.screenSize}`]
    
    if (this.state.isCompactMode) {
      classes.push('compact-mode')
    }
    
    if (this.state.isTransitioning) {
      classes.push('transitioning')
    }
    
    return classes
  }
  
  private calculateFixedHeight(hasSelectedItems: boolean): number {
    return this.config.searchFilterHeight + 
           (hasSelectedItems ? this.config.quickPreviewHeight : 0)
  }
  
  private calculateDetailsPanelHeight(
    itemsCount: number,
    hasConflicts: boolean,
    availableHeight: number
  ): number {
    if (itemsCount === 0) {
      return 0
    }
    
    // 基础高度计算
    let baseHeight = 120 // 最小高度（头部 + 边距）
    baseHeight += Math.min(itemsCount * 60, 180) // 每项60px，最多显示3项
    
    // 如果有冲突，增加兼容性检查区域高度
    if (hasConflicts) {
      baseHeight += 100
    }
    
    // 限制最大高度
    const maxHeight = Math.min(
      this.config.maxDetailsPanelHeight,
      availableHeight * 0.4 // 不超过可用高度的40%
    )
    
    return Math.min(baseHeight, maxHeight)
  }
  
  private determineScreenSize(width: number): 'sm' | 'md' | 'lg' {
    if (width < this.config.breakpoints.sm) return 'sm'
    if (width < this.config.breakpoints.md) return 'md'
    return 'lg'
  }
}

// 默认配置
export const defaultLayoutConfig: LayoutConfiguration = {
  maxDetailsPanelHeight: 320,
  minOrderListHeight: 200,
  quickPreviewHeight: 40,
  searchFilterHeight: 60,
  breakpoints: {
    sm: 768,
    md: 1024,
    lg: 1280
  },
  transitionDuration: 300,
  easeFunction: 'cubic-bezier(0.4, 0, 0.2, 1)'
}

// 创建单例实例
export const layoutManager = new SmartLayoutManager(defaultLayoutConfig)