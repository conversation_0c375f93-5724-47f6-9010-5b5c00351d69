/**
 * 工具类简单测试
 * 用于验证核心业务逻辑工具类的基本功能
 */

import { FormulaCalculator } from '../formulaCalculator'
import { MaterialSelector } from '../materialSelector'
import { BOMGenerator } from '../bomGenerator'

// 测试公式计算器
export function testFormulaCalculator() {
  console.log('=== 测试公式计算器 ===')
  
  // 测试基本计算
  const context = {
    width: 1200,
    height: 1500,
    thickness: 6
  }
  
  const result1 = FormulaCalculator.calculateValue('width * height / 1000000', context)
  console.log('面积计算:', result1)
  
  const result2 = FormulaCalculator.calculateValue('ceil(width / 100) * 2', context)
  console.log('数量计算:', result2)
  
  // 测试批量计算
  const formulas = {
    area: 'width * height / 1000000',
    perimeter: '(width + height) * 2',
    volume: 'width * height * thickness / 1000000000'
  }
  
  const batchResults = FormulaCalculator.calculateMultiple(formulas, context)
  console.log('批量计算结果:', batchResults)
}

// 测试物料选择器
export function testMaterialSelector() {
  console.log('=== 测试物料选择器 ===')
  
  // 模拟物料数据
  const materials = [
    {
      variantId: 'glass_001',
      sku: 'GL-6MM-CLR',
      displayName: '6mm透明玻璃',
      variantAttributes: { thickness: 6, color: 'clear' },
      stock: 100,
      unit: 'm²',
      cost: 45,
      supplier: '华南玻璃',
      leadTimeDays: 7,
      isActive: true
    },
    {
      variantId: 'glass_002', 
      sku: 'GL-8MM-CLR',
      displayName: '8mm透明玻璃',
      variantAttributes: { thickness: 8, color: 'clear' },
      stock: 50,
      unit: 'm²',
      cost: 65,
      supplier: '华南玻璃',
      leadTimeDays: 7,
      isActive: true
    }
  ]
  
  // 模拟选择规则
  const rules = [
    {
      id: 'rule_001',
      name: '厚度选择规则',
      condition: 'thickness >= 6',
      materialCriteria: {
        materialType: 'glass',
        requiredProperties: { thickness: { min: 6 } },
        preferredProperties: { color: 'clear' }
      },
      priority: 1,
      isActive: true
    }
  ]
  
  const context = { thickness: 6, color: 'clear' }
  
  const selectionResult = MaterialSelector.selectMaterial(rules, materials, context)
  console.log('物料选择结果:', selectionResult)
}

// 测试BOM生成器
export function testBOMGenerator() {
  console.log('=== 测试BOM生成器 ===')
  
  // 模拟产品结构
  const productStructure = {
    id: 'struct_001',
    name: '简单窗户结构',
    components: [
      {
        id: 'comp_001',
        name: '窗框',
        type: 'frame' as const,
        description: '铝合金窗框',
        parameters: [],
        subComponents: [
          {
            id: 'sub_001',
            name: '立柱',
            materialType: 'profile',
            parameters: [],
            quantityFormula: '2',
            materialSelectionRules: [
              {
                id: 'rule_001',
                name: '立柱选择',
                condition: 'true',
                materialCriteria: {
                  materialType: 'profile',
                  requiredProperties: {},
                  preferredProperties: {}
                },
                priority: 1,
                isActive: true
              }
            ]
          }
        ],
        quantityFormula: '1',
        isRequired: true,
        isConfigurable: false
      }
    ]
  }
  
  const parameters = { width: 1200, height: 1500 }
  const materials = [
    {
      variantId: 'profile_001',
      sku: 'PR-50X30-AL',
      displayName: '50x30铝型材',
      variantAttributes: {},
      stock: 1000,
      unit: 'm',
      cost: 25,
      supplier: '铝材厂',
      leadTimeDays: 5,
      isActive: true
    }
  ]
  
  BOMGenerator.generateBOM(productStructure, parameters, materials)
    .then(result => {
      console.log('BOM生成结果:', result)
    })
    .catch(error => {
      console.error('BOM生成失败:', error)
    })
}

// 运行所有测试
export function runAllTests() {
  testFormulaCalculator()
  testMaterialSelector()
  testBOMGenerator()
}