/**
 * 路由配置测试工具
 * 用于验证路由能够正确导航到对应页面组件 (Requirement 6.4)
 */

import router, { getVisibleRoutes, getRouteByName } from '@/router'

export interface RouteTestResult {
  routeName: string
  path: string
  exists: boolean
  hasComponent: boolean
  hasTitle: boolean
  error?: string
}

/**
 * 测试核心路由是否正确配置
 */
export function testCoreRoutes(): RouteTestResult[] {
  const coreRoutes = ['dashboard', 'metadata', 'crm']
  const results: RouteTestResult[] = []

  coreRoutes.forEach(routeName => {
    const route = getRouteByName(routeName)
    const result: RouteTestResult = {
      routeName,
      path: route?.path || '',
      exists: !!route,
      hasComponent: !!route?.component,
      hasTitle: !!route?.meta?.title
    }

    if (!route) {
      result.error = `Route '${routeName}' not found`
    } else if (!route.component) {
      result.error = `Route '${routeName}' has no component`
    } else if (!route.meta?.title) {
      result.error = `Route '${routeName}' has no title in meta`
    }

    results.push(result)
  })

  return results
}

/**
 * 测试所有可见路由
 */
export function testAllVisibleRoutes(): RouteTestResult[] {
  const visibleRoutes = getVisibleRoutes()
  const results: RouteTestResult[] = []

  visibleRoutes.forEach(route => {
    if (route.name && route.path !== '/') {
      const routeName = String(route.name)
      const result: RouteTestResult = {
        routeName,
        path: route.path,
        exists: true,
        hasComponent: !!route.component,
        hasTitle: !!route.meta?.title
      }

      if (!route.component) {
        result.error = `Route '${routeName}' has no component`
      } else if (!route.meta?.title) {
        result.error = `Route '${routeName}' has no title in meta`
      }

      results.push(result)
    }
  })

  return results
}

/**
 * 测试路由导航功能
 */
export async function testRouteNavigation(routeName: string): Promise<boolean> {
  try {
    await router.push({ name: routeName })
    return router.currentRoute.value.name === routeName
  } catch (error) {
    console.error(`Navigation to route '${routeName}' failed:`, error)
    return false
  }
}

/**
 * 打印路由测试结果
 */
export function printRouteTestResults(results: RouteTestResult[]): void {
  console.group('🧪 路由配置测试结果')
  
  const passed = results.filter(r => !r.error)
  const failed = results.filter(r => r.error)
  
  console.log(`✅ 通过: ${passed.length}`)
  console.log(`❌ 失败: ${failed.length}`)
  
  if (failed.length > 0) {
    console.group('❌ 失败的路由:')
    failed.forEach(result => {
      console.error(`- ${result.routeName}: ${result.error}`)
    })
    console.groupEnd()
  }
  
  if (passed.length > 0) {
    console.group('✅ 通过的路由:')
    passed.forEach(result => {
      console.log(`- ${result.routeName} (${result.path})`)
    })
    console.groupEnd()
  }
  
  console.groupEnd()
}

/**
 * 运行完整的路由测试
 */
export function runRouteTests(): void {
  console.log('🚀 开始路由配置测试...')
  
  // 测试核心路由
  const coreResults = testCoreRoutes()
  console.group('📋 核心路由测试 (Requirements 6.2)')
  printRouteTestResults(coreResults)
  console.groupEnd()
  
  // 测试所有可见路由
  const allResults = testAllVisibleRoutes()
  console.group('📋 所有路由测试 (Requirements 6.1, 6.3, 6.4)')
  printRouteTestResults(allResults)
  console.groupEnd()
  
  // 总结
  const totalPassed = coreResults.filter(r => !r.error).length + allResults.filter(r => !r.error).length
  const totalFailed = coreResults.filter(r => r.error).length + allResults.filter(r => r.error).length
  
  console.log(`🎯 测试总结: ${totalPassed} 通过, ${totalFailed} 失败`)
  
  if (totalFailed === 0) {
    console.log('🎉 所有路由测试通过！路由系统配置正确。')
  } else {
    console.warn('⚠️ 部分路由测试失败，请检查路由配置。')
  }
}