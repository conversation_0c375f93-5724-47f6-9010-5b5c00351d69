/**
 * 产品结构数据验证工具
 * 用于验证MTO模式下的产品结构数据完整性
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalNodes: number;
    productNodes: number;
    assemblyNodes: number;
    componentNodes: number;
    parametersCount: number;
    constraintsCount: number;
  };
}

/**
 * 验证产品结构数据的完整性
 */
export function validateProductStructure(structure: any): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    summary: {
      totalNodes: 0,
      productNodes: 0,
      assemblyNodes: 0,
      componentNodes: 0,
      parametersCount: 0,
      constraintsCount: 0
    }
  };

  // 验证产品级别
  if (!structure.id || !structure.code || !structure.name) {
    result.errors.push('产品缺少基本信息 (id, code, name)');
    result.isValid = false;
  }

  result.summary.productNodes = 1;
  result.summary.totalNodes = 1;

  if (structure.parameters) {
    result.summary.parametersCount += structure.parameters.length;
  }

  if (structure.constraints) {
    result.summary.constraintsCount += structure.constraints.length;
  }

  // 验证根构件
  if (structure.rootAssembly) {
    const assemblyResult = validateAssembly(structure.rootAssembly, result);
    result.summary.totalNodes += assemblyResult.nodeCount;
    result.summary.assemblyNodes += assemblyResult.assemblyCount;
    result.summary.componentNodes += assemblyResult.componentCount;
    result.summary.parametersCount += assemblyResult.parametersCount;
    result.summary.constraintsCount += assemblyResult.constraintsCount;
  } else {
    result.errors.push('产品缺少根构件 (rootAssembly)');
    result.isValid = false;
  }

  return result;
}

/**
 * 验证构件数据
 */
function validateAssembly(assembly: any, result: ValidationResult): {
  nodeCount: number;
  assemblyCount: number;
  componentCount: number;
  parametersCount: number;
  constraintsCount: number;
} {
  let nodeCount = 1;
  let assemblyCount = 1;
  let componentCount = 0;
  let parametersCount = 0;
  let constraintsCount = 0;

  // 验证构件基本信息
  if (!assembly.id || !assembly.assemblyCode || !assembly.assemblyName) {
    result.errors.push(`构件 ${assembly.instanceName || 'unknown'} 缺少基本信息`);
    result.isValid = false;
  }

  // 统计参数和约束
  if (assembly.parameters) {
    parametersCount += assembly.parameters.length;
    
    // 验证参数完整性
    assembly.parameters.forEach((param: any, index: number) => {
      if (!param.name || !param.displayName || !param.type) {
        result.errors.push(`构件 ${assembly.instanceName} 的参数 ${index + 1} 缺少必要信息`);
        result.isValid = false;
      }
    });
  }

  if (assembly.constraints) {
    constraintsCount += assembly.constraints.length;
  }

  // 验证组件实例
  if (assembly.componentInstances) {
    assembly.componentInstances.forEach((component: any) => {
      nodeCount++;
      componentCount++;
      
      if (!component.id || !component.componentCode || !component.componentName) {
        result.errors.push(`组件 ${component.instanceName || 'unknown'} 缺少基本信息`);
        result.isValid = false;
      }

      // 验证物料映射
      if (component.properties && !component.properties.materialCategoryId) {
        result.warnings.push(`组件 ${component.instanceName} 缺少物料分类映射`);
      }

      if (component.parameters) {
        parametersCount += component.parameters.length;
      }

      if (component.constraints) {
        constraintsCount += component.constraints.length;
      }
    });
  }

  // 验证子构件
  if (assembly.subAssemblies) {
    assembly.subAssemblies.forEach((subAssembly: any) => {
      const subResult = validateAssembly(subAssembly, result);
      nodeCount += subResult.nodeCount;
      assemblyCount += subResult.assemblyCount;
      componentCount += subResult.componentCount;
      parametersCount += subResult.parametersCount;
      constraintsCount += subResult.constraintsCount;
    });
  }

  return {
    nodeCount,
    assemblyCount,
    componentCount,
    parametersCount,
    constraintsCount
  };
}

/**
 * 生成验证报告
 */
export function generateValidationReport(structure: any): string {
  const result = validateProductStructure(structure);
  
  let report = `=== 产品结构验证报告 ===\n`;
  report += `产品: ${structure.name} (${structure.code})\n`;
  report += `状态: ${result.isValid ? '✅ 通过' : '❌ 失败'}\n\n`;
  
  report += `=== 结构统计 ===\n`;
  report += `总节点数: ${result.summary.totalNodes}\n`;
  report += `产品节点: ${result.summary.productNodes}\n`;
  report += `构件节点: ${result.summary.assemblyNodes}\n`;
  report += `组件节点: ${result.summary.componentNodes}\n`;
  report += `参数总数: ${result.summary.parametersCount}\n`;
  report += `约束总数: ${result.summary.constraintsCount}\n\n`;
  
  if (result.errors.length > 0) {
    report += `=== 错误 (${result.errors.length}) ===\n`;
    result.errors.forEach((error, index) => {
      report += `${index + 1}. ${error}\n`;
    });
    report += '\n';
  }
  
  if (result.warnings.length > 0) {
    report += `=== 警告 (${result.warnings.length}) ===\n`;
    result.warnings.forEach((warning, index) => {
      report += `${index + 1}. ${warning}\n`;
    });
    report += '\n';
  }
  
  return report;
}
