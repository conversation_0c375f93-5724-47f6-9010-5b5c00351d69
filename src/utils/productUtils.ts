import type {
  Component,
  Assembly,
  ProductStructure,
  Product,
  ComponentParameter,
  ComponentConstraint,
  ValidationResult,
  ProductConfiguration
} from '@/types/product';

/**
 * 产品管理相关工具函数
 */

// 参数验证工具
export class ParameterValidator {
  /**
   * 验证参数值是否符合参数定义
   */
  static validateParameter(parameter: ComponentParameter, value: any): ValidationResult | null {
    // 必填验证
    if (parameter.required && (value === null || value === undefined || value === '')) {
      return {
        ruleId: `required_${parameter.id}`,
        ruleName: '必填验证',
        type: 'error',
        message: `${parameter.displayName} 是必填项`,
        affectedParameters: [parameter.name]
      };
    }

    // 类型验证
    if (value !== null && value !== undefined && value !== '') {
      switch (parameter.type) {
        case 'number':
          if (isNaN(Number(value))) {
            return {
              ruleId: `type_${parameter.id}`,
              ruleName: '类型验证',
              type: 'error',
              message: `${parameter.displayName} 必须是数字`,
              affectedParameters: [parameter.name]
            };
          }
          
          const numValue = Number(value);
          
          // 最小值验证
          if (parameter.minValue !== undefined && numValue < parameter.minValue) {
            return {
              ruleId: `min_${parameter.id}`,
              ruleName: '最小值验证',
              type: 'error',
              message: `${parameter.displayName} 不能小于 ${parameter.minValue}${parameter.unit || ''}`,
              affectedParameters: [parameter.name]
            };
          }
          
          // 最大值验证
          if (parameter.maxValue !== undefined && numValue > parameter.maxValue) {
            return {
              ruleId: `max_${parameter.id}`,
              ruleName: '最大值验证',
              type: 'error',
              message: `${parameter.displayName} 不能大于 ${parameter.maxValue}${parameter.unit || ''}`,
              affectedParameters: [parameter.name]
            };
          }
          break;

        case 'select':
          if (parameter.options && parameter.options.length > 0) {
            const validValues = parameter.options.map(opt => opt.value);
            if (!validValues.includes(value)) {
              return {
                ruleId: `select_${parameter.id}`,
                ruleName: '选项验证',
                type: 'error',
                message: `${parameter.displayName} 的值不在有效选项中`,
                affectedParameters: [parameter.name]
              };
            }
          }
          break;

        case 'boolean':
          if (typeof value !== 'boolean') {
            return {
              ruleId: `boolean_${parameter.id}`,
              ruleName: '布尔值验证',
              type: 'error',
              message: `${parameter.displayName} 必须是布尔值`,
              affectedParameters: [parameter.name]
            };
          }
          break;
      }
    }

    return null;
  }

  /**
   * 验证所有参数
   */
  static validateParameters(parameters: ComponentParameter[], values: Record<string, any>): ValidationResult[] {
    const results: ValidationResult[] = [];

    for (const parameter of parameters) {
      const value = values[parameter.name];
      const result = this.validateParameter(parameter, value);
      if (result) {
        results.push(result);
      }
    }

    return results;
  }
}

// 约束求解器
export class ConstraintSolver {
  /**
   * 评估约束表达式
   */
  static evaluateConstraint(constraint: ComponentConstraint, values: Record<string, any>): boolean {
    try {
      // 创建安全的表达式评估环境
      const safeValues = { ...values };
      
      // 替换表达式中的变量
      let expression = constraint.expression;
      for (const [key, value] of Object.entries(safeValues)) {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        expression = expression.replace(regex, String(value));
      }

      // 简单的安全检查
      if (!/^[0-9+\-*/.()>\s<>=&|!]+$/.test(expression.replace(/\s/g, ''))) {
        console.warn(`Unsafe expression detected: ${constraint.expression}`);
        return true; // 默认通过
      }

      // 评估表达式
      return eval(expression);
    } catch (error) {
      console.error(`Error evaluating constraint ${constraint.id}:`, error);
      return true; // 默认通过
    }
  }

  /**
   * 验证所有约束
   */
  static validateConstraints(constraints: ComponentConstraint[], values: Record<string, any>): ValidationResult[] {
    const results: ValidationResult[] = [];

    for (const constraint of constraints) {
      const isValid = this.evaluateConstraint(constraint, values);
      if (!isValid) {
        results.push({
          ruleId: constraint.id,
          ruleName: constraint.name,
          type: constraint.severity,
          message: constraint.errorMessage,
          affectedParameters: this.extractParameterNames(constraint.expression)
        });
      }
    }

    return results;
  }

  /**
   * 从表达式中提取参数名
   */
  private static extractParameterNames(expression: string): string[] {
    const parameterPattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g;
    const matches = expression.match(parameterPattern) || [];
    
    // 过滤掉JavaScript关键字和操作符
    const keywords = ['true', 'false', 'null', 'undefined', 'and', 'or', 'not'];
    return [...new Set(matches.filter(match => !keywords.includes(match)))];
  }

  /**
   * 尝试自动修复约束违规
   */
  static autoFixConstraints(constraints: ComponentConstraint[], values: Record<string, any>): Record<string, any> {
    const fixedValues = { ...values };

    for (const constraint of constraints) {
      if (!constraint.autoFix?.enabled) continue;

      const isValid = this.evaluateConstraint(constraint, fixedValues);
      if (!isValid && constraint.autoFix.fixExpression) {
        try {
          // 执行修复表达式
          const fixResult = this.evaluateFixExpression(constraint.autoFix.fixExpression, fixedValues);
          if (fixResult) {
            Object.assign(fixedValues, fixResult);
          }
        } catch (error) {
          console.error(`Error applying auto-fix for constraint ${constraint.id}:`, error);
        }
      }
    }

    return fixedValues;
  }

  /**
   * 评估修复表达式
   */
  private static evaluateFixExpression(fixExpression: string, values: Record<string, any>): Record<string, any> | null {
    try {
      // 简化的修复表达式解析
      // 支持格式: "parameter = value" 或 "parameter = expression"
      const assignmentMatch = fixExpression.match(/(\w+)\s*=\s*(.+)/);
      if (assignmentMatch) {
        const [, paramName, valueExpression] = assignmentMatch;
        
        // 替换表达式中的变量
        let expression = valueExpression;
        for (const [key, value] of Object.entries(values)) {
          const regex = new RegExp(`\\b${key}\\b`, 'g');
          expression = expression.replace(regex, String(value));
        }

        // 评估值表达式
        const newValue = eval(expression);
        return { [paramName]: newValue };
      }
    } catch (error) {
      console.error('Error evaluating fix expression:', error);
    }
    
    return null;
  }
}

// 公式计算器
export class FormulaCalculator {
  /**
   * 计算公式结果
   */
  static calculate(formula: string, parameters: Record<string, any>): number {
    try {
      // 替换公式中的参数
      let expression = formula;
      for (const [key, value] of Object.entries(parameters)) {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        expression = expression.replace(regex, String(value));
      }

      // 支持常用数学函数
      const mathFunctions = {
        ceil: Math.ceil,
        floor: Math.floor,
        round: Math.round,
        max: Math.max,
        min: Math.min,
        abs: Math.abs,
        sqrt: Math.sqrt,
        pow: Math.pow
      };

      // 替换数学函数
      for (const [funcName, func] of Object.entries(mathFunctions)) {
        const regex = new RegExp(`\\b${funcName}\\(`, 'g');
        expression = expression.replace(regex, `mathFunctions.${funcName}(`);
      }

      // 创建安全的执行环境
      const safeEval = new Function('mathFunctions', `return ${expression}`);
      const result = safeEval(mathFunctions);

      return typeof result === 'number' && !isNaN(result) ? result : 0;
    } catch (error) {
      console.error('Error calculating formula:', error);
      return 0;
    }
  }

  /**
   * 验证公式语法
   */
  static validateFormula(formula: string): boolean {
    try {
      // 简单的语法检查
      const testParams = { test: 1 };
      this.calculate(formula.replace(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g, 'test'), testParams);
      return true;
    } catch {
      return false;
    }
  }
}

// 产品配置工具
export class ProductConfigurationUtils {
  /**
   * 创建产品配置
   */
  static createConfiguration(
    product: Product,
    parameterValues: Record<string, any>,
    sourceType: 'manual' | 'order' | 'template' = 'manual',
    sourceId?: string
  ): ProductConfiguration {
    return {
      id: `config_${Date.now()}`,
      productId: product.id,
      productCode: product.code,
      productName: product.name,
      configurationName: `${product.name} 配置 - ${new Date().toLocaleString()}`,
      parameterValues,
      calculatedValues: {},
      validationResults: [],
      createdAt: new Date().toISOString(),
      createdBy: 'current_user',
      sourceType,
      sourceId
    };
  }

  /**
   * 验证产品配置
   */
  static async validateConfiguration(
    productStructure: ProductStructure,
    configuration: ProductConfiguration
  ): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    // 验证产品参数
    const parameterResults = ParameterValidator.validateParameters(
      productStructure.productParameters,
      configuration.parameterValues
    );
    results.push(...parameterResults);

    // 验证产品约束
    const constraintResults = ConstraintSolver.validateConstraints(
      productStructure.productConstraints,
      configuration.parameterValues
    );
    results.push(...constraintResults);

    return results;
  }

  /**
   * 应用配置选项
   */
  static applyConfigurationOption(
    baseConfiguration: ProductConfiguration,
    configurationOption: any
  ): ProductConfiguration {
    const newConfiguration = { ...baseConfiguration };
    
    // 应用参数覆盖
    for (const choice of configurationOption.configurationChoices) {
      if (choice.parameterOverrides) {
        Object.assign(newConfiguration.parameterValues, choice.parameterOverrides);
      }
    }

    return newConfiguration;
  }
}

// 数据格式化工具
export class DataFormatter {
  /**
   * 格式化数值
   */
  static formatNumber(value: number, decimals: number = 2, unit?: string): string {
    const formatted = value.toFixed(decimals);
    return unit ? `${formatted}${unit}` : formatted;
  }

  /**
   * 格式化货币
   */
  static formatCurrency(value: number, currency: string = '¥'): string {
    return `${currency}${value.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
  }

  /**
   * 格式化日期
   */
  static formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString('zh-CN');
  }

  /**
   * 格式化状态
   */
  static formatStatus(status: string): { text: string; color: string } {
    const statusMap: Record<string, { text: string; color: string }> = {
      draft: { text: '草稿', color: 'gray' },
      active: { text: '激活', color: 'green' },
      deprecated: { text: '已弃用', color: 'orange' },
      archived: { text: '已归档', color: 'red' },
      confirmed: { text: '已确认', color: 'blue' },
      approved: { text: '已批准', color: 'green' },
      released: { text: '已发布', color: 'purple' }
    };

    return statusMap[status] || { text: status, color: 'gray' };
  }
}

// 导出所有工具类
export {
  ParameterValidator,
  ConstraintSolver,
  FormulaCalculator,
  ProductConfigurationUtils,
  DataFormatter
};
