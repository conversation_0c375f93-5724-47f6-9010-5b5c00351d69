import { ref } from 'vue'
import { mesApi } from '@/utils/mesApi'

export function useWorkstationScheduling() {
  const loading = ref(false)
  const efficiency = ref<any>(null)
  const exceptions = ref<any[]>([])

  async function load() {
    loading.value = true
    try {
      const [eff, ex] = await Promise.all([
        mesApi.getEfficiencyMetrics(),
        mesApi.getExceptionSamples()
      ])
      efficiency.value = eff.ok ? eff.data : null
      exceptions.value = ex.ok && Array.isArray(ex.data) ? ex.data : []
    } finally {
      loading.value = false
    }
  }

  return { loading, efficiency, exceptions, load }
}

