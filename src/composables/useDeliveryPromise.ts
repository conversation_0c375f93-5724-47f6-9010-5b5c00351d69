import { ref, computed } from 'vue'
import { mesApi } from '@/utils/mesApi'

export function useDeliveryPromise() {
  const loading = ref(false)
  const analysis = ref<any>(null)

  async function load() {
    loading.value = true
    try {
      const res = await mesApi.getAccuracyAnalysis()
      analysis.value = res.ok ? res.data : null
    } finally {
      loading.value = false
    }
  }

  const accuracyImprovement = computed(() => {
    const a = analysis.value
    if (!a) return 0
    return Number(((a.smartMethod.accuracyRate - a.traditionalMethod.accuracyRate).toFixed(1)))
  })

  return { loading, analysis, load, accuracyImprovement }
}

