import { ref } from 'vue'
import { mesApi } from '@/utils/mesApi'

export function useCuttingOptimization() {
  const loading = ref(false)
  const strategy = ref<any>(null)
  const valueComparison = ref<any>(null)

  async function load() {
    loading.value = true
    try {
      const [s, v] = await Promise.all([
        mesApi.getAlgorithmStrategy(),
        mesApi.getCuttingValueComparison()
      ])
      strategy.value = s.ok ? s.data : null
      valueComparison.value = v.ok ? v.data : null
    } finally {
      loading.value = false
    }
  }

  function computeUtilizationImprovement(): number {
    const v = valueComparison.value
    if (!v) return 0
    return Number(v?.valueMetrics?.utilizationImprovement ?? 0)
  }

  return { loading, strategy, valueComparison, load, computeUtilizationImprovement }
}

