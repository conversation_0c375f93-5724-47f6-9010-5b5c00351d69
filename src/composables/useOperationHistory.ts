import { ref, computed } from 'vue';

export interface Operation {
  id: string;
  type: 'add' | 'delete' | 'modify' | 'move' | 'batch';
  timestamp: number;
  description: string;
  data: {
    before: any;
    after: any;
    nodeIds?: string[];
  };
}

export function useOperationHistory() {
  const operations = ref<Operation[]>([]);
  const currentIndex = ref(-1);
  const maxHistorySize = ref(100);

  // 计算属性
  const canUndo = computed(() => currentIndex.value >= 0);
  const canRedo = computed(() => currentIndex.value < operations.value.length - 1);
  
  const currentOperation = computed(() => 
    currentIndex.value >= 0 ? operations.value[currentIndex.value] : null
  );

  // 添加操作到历史记录
  const addOperation = (operation: Omit<Operation, 'id' | 'timestamp'>) => {
    // 如果当前不在历史记录的末尾，删除后面的记录
    if (currentIndex.value < operations.value.length - 1) {
      operations.value = operations.value.slice(0, currentIndex.value + 1);
    }

    // 创建新操作
    const newOperation: Operation = {
      ...operation,
      id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };

    // 添加到历史记录
    operations.value.push(newOperation);
    currentIndex.value = operations.value.length - 1;

    // 限制历史记录大小
    if (operations.value.length > maxHistorySize.value) {
      const removeCount = operations.value.length - maxHistorySize.value;
      operations.value = operations.value.slice(removeCount);
      currentIndex.value -= removeCount;
    }

    return newOperation;
  };

  // 撤销操作
  const undo = (): Operation | null => {
    if (!canUndo.value) return null;

    const operation = operations.value[currentIndex.value];
    currentIndex.value--;
    
    return operation;
  };

  // 重做操作
  const redo = (): Operation | null => {
    if (!canRedo.value) return null;

    currentIndex.value++;
    const operation = operations.value[currentIndex.value];
    
    return operation;
  };

  // 清空历史记录
  const clearHistory = () => {
    operations.value = [];
    currentIndex.value = -1;
  };

  // 获取操作历史摘要
  const getHistorySummary = (limit = 10) => {
    return operations.value
      .slice(-limit)
      .map(op => ({
        id: op.id,
        type: op.type,
        description: op.description,
        timestamp: op.timestamp,
        isCurrent: operations.value.indexOf(op) === currentIndex.value
      }));
  };

  // 跳转到特定操作
  const jumpToOperation = (operationId: string): boolean => {
    const index = operations.value.findIndex(op => op.id === operationId);
    if (index === -1) return false;

    currentIndex.value = index;
    return true;
  };

  // 批量操作支持
  const startBatch = () => {
    return {
      operations: [] as Omit<Operation, 'id' | 'timestamp'>[],
      add: function(operation: Omit<Operation, 'id' | 'timestamp'>) {
        this.operations.push(operation);
      },
      commit: function(description: string) {
        if (this.operations.length === 0) return null;
        
        if (this.operations.length === 1) {
          return addOperation(this.operations[0]);
        }
        
        return addOperation({
          type: 'batch',
          description,
          data: {
            before: null,
            after: null,
            operations: this.operations
          }
        });
      }
    };
  };

  // 创建常用操作的便捷方法
  const recordNodeAdd = (nodeData: any, description?: string) => {
    return addOperation({
      type: 'add',
      description: description || `添加节点: ${nodeData.name}`,
      data: {
        before: null,
        after: nodeData,
        nodeIds: [nodeData.id]
      }
    });
  };

  const recordNodeDelete = (nodeData: any, description?: string) => {
    return addOperation({
      type: 'delete',
      description: description || `删除节点: ${nodeData.name}`,
      data: {
        before: nodeData,
        after: null,
        nodeIds: [nodeData.id]
      }
    });
  };

  const recordNodeModify = (beforeData: any, afterData: any, description?: string) => {
    return addOperation({
      type: 'modify',
      description: description || `修改节点: ${afterData.name}`,
      data: {
        before: beforeData,
        after: afterData,
        nodeIds: [afterData.id]
      }
    });
  };

  const recordNodeMove = (nodeIds: string[], beforePositions: any[], afterPositions: any[], description?: string) => {
    return addOperation({
      type: 'move',
      description: description || `移动 ${nodeIds.length} 个节点`,
      data: {
        before: beforePositions,
        after: afterPositions,
        nodeIds
      }
    });
  };

  // 获取操作统计信息
  const getStatistics = () => {
    const stats = {
      total: operations.value.length,
      add: 0,
      delete: 0,
      modify: 0,
      move: 0,
      batch: 0
    };

    operations.value.forEach(op => {
      stats[op.type]++;
    });

    return stats;
  };

  // 导出历史记录
  const exportHistory = () => {
    return {
      operations: operations.value,
      currentIndex: currentIndex.value,
      timestamp: Date.now(),
      version: '1.0'
    };
  };

  // 导入历史记录
  const importHistory = (historyData: any) => {
    if (!historyData || !Array.isArray(historyData.operations)) {
      return false;
    }

    try {
      operations.value = historyData.operations;
      currentIndex.value = historyData.currentIndex ?? -1;
      
      // 确保索引在有效范围内
      if (currentIndex.value >= operations.value.length) {
        currentIndex.value = operations.value.length - 1;
      }
      
      return true;
    } catch (error) {
      console.error('导入历史记录失败:', error);
      return false;
    }
  };

  return {
    operations,
    currentIndex,
    maxHistorySize,
    
    // 计算属性
    canUndo,
    canRedo,
    currentOperation,
    
    // 基本操作
    addOperation,
    undo,
    redo,
    clearHistory,
    
    // 历史记录查询
    getHistorySummary,
    jumpToOperation,
    getStatistics,
    
    // 批量操作
    startBatch,
    
    // 便捷方法
    recordNodeAdd,
    recordNodeDelete,
    recordNodeModify,
    recordNodeMove,
    
    // 导入导出
    exportHistory,
    importHistory
  };
}
