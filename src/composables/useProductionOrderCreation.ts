/**
 * 生产工单创建逻辑组合式函数
 */

import { ref, computed, watch } from 'vue'
import type {
  WorkOrderCreationState,
  SelectedOrderItem,
  BatchOptimizationResult,
  ProcessConflict,
  ValidationResult,
  BatchConfiguration,
  CreateWorkOrderRequest,
  CreateWorkOrderResponse
} from '@/types/production-order-creation'
import type { CustomerOrder, CustomerOrderItem } from '@/types/mes-validation'

export function useProductionOrderCreation() {
  // ============ 状态管理 ============
  
  const state = ref<WorkOrderCreationState>({
    // 基础数据
    availableOrders: [],
    selectedOrderItems: [],
    
    // 批次优化
    batchOptimization: null,
    processConflicts: [],
    
    // 配置参数
    workOrderPriority: 'normal',
    plannedStartDate: new Date().toISOString().split('T')[0],
    customBatchConfig: {},
    
    // 智能推荐
    scheduleRecommendation: '',
    estimatedEndDate: '',
    validationResults: [],
    
    // 界面状态
    isLoading: false,
    isCreating: false,
    showConflictDetails: false,
    showBatchDetails: false,
    activeTab: 'selection',
    errors: {},
    
    // 搜索和筛选
    searchQuery: '',
    statusFilter: 'all',
    processTypeFilter: 'all',
    customerFilter: 'all'
  })

  // ============ 计算属性 ============
  
  const canCreateOrder = computed(() => {
    return state.value.selectedOrderItems.length > 0 && 
           !state.value.isCreating &&
           validationSummary.value.canProceed
  })

  const validationSummary = computed(() => {
    const results = state.value.validationResults
    const errors = results.filter(r => r.level === 'error').length
    const warnings = results.filter(r => r.level === 'warning').length
    const infos = results.filter(r => r.level === 'info').length
    
    return {
      totalIssues: results.length,
      errors,
      warnings,
      infos,
      canProceed: errors === 0,
      criticalIssues: results.filter(r => r.level === 'error')
    }
  })

  const totalSelectedQuantity = computed(() => {
    return state.value.selectedOrderItems.reduce((sum, item) => sum + item.selectedQuantity, 0)
  })

  const uniqueCustomers = computed(() => {
    const customers = new Set(state.value.selectedOrderItems.map(item => item.customerName))
    return customers.size
  })

  const estimatedBatches = computed(() => {
    return state.value.batchOptimization?.batches.length || 0
  })

  // ============ 数据加载 ============
  
  const loadAvailableOrders = async (): Promise<void> => {
    state.value.isLoading = true
    try {
      // 调用服务加载可用订单
      // const orders = await mesService.getAvailableCustomerOrders()
      // state.value.availableOrders = orders
      
      // 模拟数据加载
      await new Promise(resolve => setTimeout(resolve, 1000))
      state.value.availableOrders = mockAvailableOrders()
    } catch (error) {
      console.error('加载订单失败:', error)
      state.value.errors.loadOrders = '加载订单失败'
    } finally {
      state.value.isLoading = false
    }
  }

  // ============ 订单项选择 ============
  
  const selectOrderItem = async (item: CustomerOrderItem, quantity: number): Promise<void> => {
    // 检查是否已选择
    const existingIndex = state.value.selectedOrderItems.findIndex(selected => selected.id === item.id)
    if (existingIndex > -1) {
      // 更新数量
      state.value.selectedOrderItems[existingIndex].selectedQuantity = quantity
    } else {
      // 添加新选择
      const selectedItem: SelectedOrderItem = {
        id: item.id,
        customerOrderId: item.customerOrderId || '',
        orderNumber: getOrderNumberById(item.customerOrderId || ''),
        customerName: getCustomerNameById(item.customerOrderId || ''),
        specifications: item.specifications,
        totalQuantity: item.quantity,
        selectedQuantity: quantity,
        processFlow: item.processFlow || [],
        deliveryDate: getDeliveryDateById(item.customerOrderId || ''),
        originalItem: item
      }
      state.value.selectedOrderItems.push(selectedItem)
    }
    
    // 触发批次优化
    await optimizeBatches()
  }

  const removeOrderItem = (itemId: string): void => {
    const index = state.value.selectedOrderItems.findIndex(item => item.id === itemId)
    if (index > -1) {
      state.value.selectedOrderItems.splice(index, 1)
      // 重新优化批次
      optimizeBatches()
    }
  }

  const updateOrderItemQuantity = (itemId: string, quantity: number): void => {
    const item = state.value.selectedOrderItems.find(item => item.id === itemId)
    if (item) {
      item.selectedQuantity = Math.max(1, Math.min(quantity, item.totalQuantity))
      // 重新优化批次
      optimizeBatches()
    }
  }

  const clearAllSelections = (): void => {
    state.value.selectedOrderItems = []
    state.value.batchOptimization = null
    state.value.processConflicts = []
    state.value.validationResults = []
  }

  // ============ 批次优化 ============
  
  const optimizeBatches = async (): Promise<void> => {
    if (state.value.selectedOrderItems.length === 0) {
      state.value.batchOptimization = null
      state.value.processConflicts = []
      return
    }
    
    state.value.isLoading = true
    try {
      // 调用批次优化服务
      // const optimization = await smartRecommendationService.optimizeBatches(state.value.selectedOrderItems)
      // const conflicts = await smartRecommendationService.detectProcessConflicts(state.value.selectedOrderItems)
      
      // 模拟批次优化
      await new Promise(resolve => setTimeout(resolve, 1500))
      state.value.batchOptimization = mockBatchOptimization()
      state.value.processConflicts = mockProcessConflicts()
      
      // 触发验证
      await validateConfiguration()
    } catch (error) {
      console.error('批次优化失败:', error)
      state.value.errors.optimization = '批次优化失败'
    } finally {
      state.value.isLoading = false
    }
  }

  const modifyBatch = (batchId: string, config: BatchConfiguration): void => {
    state.value.customBatchConfig[batchId] = config
    // 重新验证
    validateConfiguration()
  }

  // ============ 冲突处理 ============
  
  const resolveConflict = (conflictId: string, resolution: string): void => {
    const index = state.value.processConflicts.findIndex(c => c.id === conflictId)
    if (index > -1) {
      if (resolution === 'auto') {
        // 自动解决冲突
        autoResolveConflict(state.value.processConflicts[index])
      }
      // 移除冲突
      state.value.processConflicts.splice(index, 1)
      // 重新验证
      validateConfiguration()
    }
  }

  const autoResolveConflict = (conflict: ProcessConflict): void => {
    // 实现自动解决冲突的逻辑
    console.log('自动解决冲突:', conflict.id)
  }

  // ============ 验证 ============
  
  const validateConfiguration = async (): Promise<void> => {
    try {
      // 调用验证服务
      // const results = await smartRecommendationService.validateWorkOrderConfig(state.value)
      
      // 模拟验证
      state.value.validationResults = mockValidationResults()
    } catch (error) {
      console.error('验证失败:', error)
    }
  }

  // ============ 工单创建 ============
  
  const createWorkOrder = async (): Promise<CreateWorkOrderResponse> => {
    if (!canCreateOrder.value) {
      throw new Error('无法创建工单：存在验证错误')
    }
    
    state.value.isCreating = true
    try {
      const request: CreateWorkOrderRequest = {
        batches: state.value.batchOptimization?.batches || [],
        priority: state.value.workOrderPriority,
        plannedStartDate: state.value.plannedStartDate,
        customConfigurations: state.value.customBatchConfig,
        createdBy: 'current-user' // 实际应用中从用户上下文获取
      }
      
      // 调用创建服务
      // const response = await workOrderCreationService.createWorkOrder(request)
      
      // 模拟创建
      await new Promise(resolve => setTimeout(resolve, 2000))
      const response: CreateWorkOrderResponse = {
        success: true,
        workOrderIds: ['WO-2024-' + Date.now()],
        warnings: []
      }
      
      return response
    } catch (error) {
      console.error('创建工单失败:', error)
      throw error
    } finally {
      state.value.isCreating = false
    }
  }

  // ============ 草稿管理 ============
  
  const saveDraft = async (name?: string): Promise<void> => {
    try {
      const draft = {
        id: 'draft-' + Date.now(),
        name: name || `草稿-${new Date().toLocaleString()}`,
        selectedOrderItems: state.value.selectedOrderItems,
        batchOptimization: state.value.batchOptimization,
        configurations: state.value.customBatchConfig,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      // 保存到本地存储或服务器
      localStorage.setItem('workOrderDraft', JSON.stringify(draft))
      console.log('草稿已保存:', draft.name)
    } catch (error) {
      console.error('保存草稿失败:', error)
    }
  }

  const loadDraft = async (draftId?: string): Promise<void> => {
    try {
      // 从本地存储或服务器加载
      const draftData = localStorage.getItem('workOrderDraft')
      if (draftData) {
        const draft = JSON.parse(draftData)
        state.value.selectedOrderItems = draft.selectedOrderItems || []
        state.value.batchOptimization = draft.batchOptimization
        state.value.customBatchConfig = draft.configurations || {}
        
        // 重新优化和验证
        await optimizeBatches()
      }
    } catch (error) {
      console.error('加载草稿失败:', error)
    }
  }

  // ============ 搜索和筛选 ============
  
  const updateSearchQuery = (query: string): void => {
    state.value.searchQuery = query
  }

  const updateFilter = (filterType: string, value: string): void => {
    switch (filterType) {
      case 'status':
        state.value.statusFilter = value
        break
      case 'processType':
        state.value.processTypeFilter = value
        break
      case 'customer':
        state.value.customerFilter = value
        break
    }
  }

  // ============ 工具方法 ============
  
  const getOrderNumberById = (orderId: string): string => {
    const order = state.value.availableOrders.find(o => o.id === orderId)
    return order?.orderNumber || ''
  }

  const getCustomerNameById = (orderId: string): string => {
    const order = state.value.availableOrders.find(o => o.id === orderId)
    return order?.customerName || ''
  }

  const getDeliveryDateById = (orderId: string): string => {
    const order = state.value.availableOrders.find(o => o.id === orderId)
    return order?.deliveryDate || ''
  }

  const resetState = (): void => {
    state.value = {
      availableOrders: [],
      selectedOrderItems: [],
      batchOptimization: null,
      processConflicts: [],
      workOrderPriority: 'normal',
      plannedStartDate: new Date().toISOString().split('T')[0],
      customBatchConfig: {},
      scheduleRecommendation: '',
      estimatedEndDate: '',
      validationResults: [],
      isLoading: false,
      isCreating: false,
      showConflictDetails: false,
      showBatchDetails: false,
      activeTab: 'selection',
      errors: {},
      searchQuery: '',
      statusFilter: 'all',
      processTypeFilter: 'all',
      customerFilter: 'all'
    }
  }

  // ============ 模拟数据 ============
  
  const mockAvailableOrders = (): CustomerOrder[] => {
    return [] // 实际应用中从服务获取
  }

  const mockBatchOptimization = (): BatchOptimizationResult => {
    return {
      efficiency: 15,
      timeSaved: 2.5,
      batches: [],
      recommendations: ['建议合并相同规格的产品', '优化工序顺序可节省换线时间'],
      totalItems: state.value.selectedOrderItems.length,
      totalQuantity: totalSelectedQuantity.value
    }
  }

  const mockProcessConflicts = (): ProcessConflict[] => {
    return []
  }

  const mockValidationResults = (): ValidationResult[] => {
    return []
  }

  // ============ 监听器 ============
  
  // 监听选中项变化，自动触发优化
  watch(
    () => state.value.selectedOrderItems.length,
    (newLength, oldLength) => {
      if (newLength !== oldLength && newLength > 0) {
        optimizeBatches()
      }
    }
  )

  // ============ 返回接口 ============
  
  return {
    // 状态
    state,
    
    // 计算属性
    canCreateOrder,
    validationSummary,
    totalSelectedQuantity,
    uniqueCustomers,
    estimatedBatches,
    
    // 方法
    loadAvailableOrders,
    selectOrderItem,
    removeOrderItem,
    updateOrderItemQuantity,
    clearAllSelections,
    optimizeBatches,
    modifyBatch,
    resolveConflict,
    validateConfiguration,
    createWorkOrder,
    saveDraft,
    loadDraft,
    updateSearchQuery,
    updateFilter,
    resetState
  }
}