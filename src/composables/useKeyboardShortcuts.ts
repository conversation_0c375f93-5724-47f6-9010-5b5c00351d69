import { onMounted, onUnmounted } from 'vue';

export interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  shift?: boolean;
  alt?: boolean;
  action: () => void;
  description: string;
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[]) {
  const handleKeydown = (event: KeyboardEvent) => {
    // 忽略在输入框中的按键
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return;
    }

    const matchingShortcut = shortcuts.find(shortcut => {
      const keyMatch = shortcut.key.toLowerCase() === event.key.toLowerCase();
      const ctrlMatch = !!shortcut.ctrl === event.ctrlKey;
      const shiftMatch = !!shortcut.shift === event.shiftKey;
      const altMatch = !!shortcut.alt === event.altKey;
      
      return keyMatch && ctrlMatch && shiftMatch && altMatch;
    });

    if (matchingShortcut) {
      event.preventDefault();
      event.stopPropagation();
      matchingShortcut.action();
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', handleKeydown);
  });

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
  });

  return {
    shortcuts
  };
}

// AUTOCAD风格的快捷键配置
export function useAutoCADShortcuts(actions: {
  save: () => void;
  undo: () => void;
  redo: () => void;
  delete: () => void;
  copy: () => void;
  paste: () => void;
  selectAll: () => void;
  deselect: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
  fitView: () => void;
  resetView: () => void;
  newFile: () => void;
  openFile: () => void;
  validate: () => void;
  help: () => void;
  selectTool: () => void;
  moveTool: () => void;
  componentTool: () => void;
  assemblyTool: () => void;
}) {
  const shortcuts: KeyboardShortcut[] = [
    // 文件操作
    { key: 'n', ctrl: true, action: actions.newFile, description: '新建文件' },
    { key: 'o', ctrl: true, action: actions.openFile, description: '打开文件' },
    { key: 's', ctrl: true, action: actions.save, description: '保存文件' },

    // 编辑操作
    { key: 'z', ctrl: true, action: actions.undo, description: '撤销' },
    { key: 'y', ctrl: true, action: actions.redo, description: '重做' },
    { key: 'z', ctrl: true, shift: true, action: actions.redo, description: '重做（备选）' },
    { key: 'Delete', action: actions.delete, description: '删除选中项' },
    { key: 'c', ctrl: true, action: actions.copy, description: '复制' },
    { key: 'd', ctrl: true, action: actions.copy, description: '复制（AUTOCAD风格）' },
    { key: 'v', ctrl: true, action: actions.paste, description: '粘贴' },
    { key: 'a', ctrl: true, action: actions.selectAll, description: '全选' },
    { key: 'Escape', action: actions.deselect, description: '取消选择' },

    // 视图操作
    { key: '=', action: actions.zoomIn, description: '放大' },
    { key: '+', action: actions.zoomIn, description: '放大' },
    { key: '-', action: actions.zoomOut, description: '缩小' },
    { key: 'f', action: actions.fitView, description: '适应视图' },
    { key: 'r', action: actions.resetView, description: '重置视图' },

    // 工具切换（AUTOCAD风格单字母快捷键）
    { key: 's', action: actions.selectTool, description: '选择工具' },
    { key: 'm', action: actions.moveTool, description: '移动工具' },
    { key: 'c', action: actions.componentTool, description: '组件工具' },
    { key: 'a', action: actions.assemblyTool, description: '构件工具' },

    // 其他功能
    { key: 't', ctrl: true, action: actions.validate, description: '验证结构' },
    { key: 'F1', action: actions.help, description: '帮助' }
  ];

  return useKeyboardShortcuts(shortcuts);
}

// 快捷键显示格式化
export function formatShortcut(shortcut: KeyboardShortcut): string {
  const parts: string[] = [];
  
  if (shortcut.ctrl) parts.push('Ctrl');
  if (shortcut.shift) parts.push('Shift');
  if (shortcut.alt) parts.push('Alt');
  
  // 特殊键名映射
  const keyMap: Record<string, string> = {
    'Delete': 'Del',
    'Escape': 'Esc',
    'ArrowUp': '↑',
    'ArrowDown': '↓',
    'ArrowLeft': '←',
    'ArrowRight': '→',
    ' ': 'Space'
  };
  
  const keyName = keyMap[shortcut.key] || shortcut.key.toUpperCase();
  parts.push(keyName);
  
  return parts.join('+');
}

// 快捷键帮助信息
export function getShortcutHelp(shortcuts: KeyboardShortcut[]): Array<{
  category: string;
  shortcuts: Array<{ key: string; description: string }>;
}> {
  const categories = [
    {
      category: '文件操作',
      shortcuts: shortcuts
        .filter(s => ['新建文件', '打开文件', '保存文件'].includes(s.description))
        .map(s => ({ key: formatShortcut(s), description: s.description }))
    },
    {
      category: '编辑操作',
      shortcuts: shortcuts
        .filter(s => ['撤销', '重做', '删除选中项', '复制', '粘贴', '全选', '取消选择'].includes(s.description))
        .map(s => ({ key: formatShortcut(s), description: s.description }))
    },
    {
      category: '视图操作',
      shortcuts: shortcuts
        .filter(s => ['放大', '缩小', '适应视图', '重置视图'].includes(s.description))
        .map(s => ({ key: formatShortcut(s), description: s.description }))
    },
    {
      category: '工具切换',
      shortcuts: shortcuts
        .filter(s => ['选择工具', '移动工具', '组件工具', '构件工具'].includes(s.description))
        .map(s => ({ key: formatShortcut(s), description: s.description }))
    },
    {
      category: '其他功能',
      shortcuts: shortcuts
        .filter(s => ['验证结构', '帮助'].includes(s.description))
        .map(s => ({ key: formatShortcut(s), description: s.description }))
    }
  ];

  return categories.filter(cat => cat.shortcuts.length > 0);
}
