import { toast } from 'vue-sonner'

/**
 * 通知管理 Composable
 * 基于 vue-sonner 的封装，提供统一的通知接口
 */
export function useNotifications() {
  /**
   * 显示成功通知
   * @param message 主要消息
   * @param description 描述信息（可选）
   * @param options 额外选项
   */
  const success = (
    message: string, 
    description?: string, 
    options?: {
      duration?: number
      action?: {
        label: string
        onClick: () => void
      }
    }
  ) => {
    toast.success(message, {
      description,
      duration: options?.duration || 4000,
      action: options?.action,
    })
  }

  /**
   * 显示错误通知
   * @param message 主要消息
   * @param description 描述信息（可选）
   * @param options 额外选项
   */
  const error = (
    message: string, 
    description?: string, 
    options?: {
      duration?: number
      action?: {
        label: string
        onClick: () => void
      }
    }
  ) => {
    toast.error(message, {
      description,
      duration: options?.duration || 5000,
      action: options?.action,
    })
  }

  /**
   * 显示警告通知
   * @param message 主要消息
   * @param description 描述信息（可选）
   * @param options 额外选项
   */
  const warning = (
    message: string, 
    description?: string, 
    options?: {
      duration?: number
      action?: {
        label: string
        onClick: () => void
      }
    }
  ) => {
    toast.warning(message, {
      description,
      duration: options?.duration || 4000,
      action: options?.action,
    })
  }

  /**
   * 显示信息通知
   * @param message 主要消息
   * @param description 描述信息（可选）
   * @param options 额外选项
   */
  const info = (
    message: string, 
    description?: string, 
    options?: {
      duration?: number
      action?: {
        label: string
        onClick: () => void
      }
    }
  ) => {
    toast.info(message, {
      description,
      duration: options?.duration || 4000,
      action: options?.action,
    })
  }

  /**
   * 显示普通通知
   * @param message 主要消息
   * @param description 描述信息（可选）
   * @param options 额外选项
   */
  const notify = (
    message: string, 
    description?: string, 
    options?: {
      duration?: number
      action?: {
        label: string
        onClick: () => void
      }
    }
  ) => {
    toast(message, {
      description,
      duration: options?.duration || 4000,
      action: options?.action,
    })
  }

  /**
   * 显示 Promise 通知
   * @param promise Promise 对象
   * @param messages 不同状态的消息
   */
  const promise = <T>(
    promise: Promise<T>,
    messages: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ) => {
    toast.promise(promise, messages)
  }

  /**
   * 显示加载通知
   * @param message 加载消息
   * @param options 额外选项
   */
  const loading = (
    message: string,
    options?: {
      duration?: number
    }
  ) => {
    return toast.loading(message, {
      duration: options?.duration || Infinity,
    })
  }

  /**
   * 关闭指定的通知
   * @param toastId 通知ID
   */
  const dismiss = (toastId?: string | number) => {
    toast.dismiss(toastId)
  }

  /**
   * 关闭所有通知
   */
  const dismissAll = () => {
    toast.dismiss()
  }

  return {
    success,
    error,
    warning,
    info,
    notify,
    promise,
    loading,
    dismiss,
    dismissAll,
  }
}

/**
 * 业务相关的通知预设
 */
export function useBusinessNotifications() {
  const notifications = useNotifications()

  /**
   * 保存成功通知
   */
  const saveSuccess = (itemName = '数据') => {
    notifications.success('保存成功', `${itemName}已成功保存`)
  }

  /**
   * 保存失败通知
   */
  const saveError = (error?: string) => {
    notifications.error('保存失败', error || '请检查网络连接后重试')
  }

  /**
   * 删除成功通知
   */
  const deleteSuccess = (itemName = '项目') => {
    notifications.success('删除成功', `${itemName}已被删除`)
  }

  /**
   * 删除确认通知
   */
  const deleteConfirm = (onConfirm: () => void, itemName = '项目') => {
    notifications.warning('确认删除', `确定要删除这个${itemName}吗？`, {
      action: {
        label: '确认',
        onClick: onConfirm,
      },
    })
  }

  /**
   * 网络错误通知
   */
  const networkError = () => {
    notifications.error('网络错误', '请检查网络连接后重试')
  }

  /**
   * 权限错误通知
   */
  const permissionError = () => {
    notifications.error('权限不足', '您没有执行此操作的权限')
  }

  /**
   * 表单验证错误通知
   */
  const validationError = (message = '请检查表单输入') => {
    notifications.warning('表单验证失败', message)
  }

  /**
   * 操作成功通知
   */
  const operationSuccess = (operation = '操作') => {
    notifications.success(`${operation}成功`, '操作已完成')
  }

  /**
   * 操作失败通知
   */
  const operationError = (operation = '操作', error?: string) => {
    notifications.error(`${operation}失败`, error || '请稍后重试')
  }

  /**
   * 数据加载通知
   */
  const loadingData = (dataName = '数据') => {
    return notifications.loading(`正在加载${dataName}...`)
  }

  /**
   * 数据加载完成通知
   */
  const loadingComplete = (toastId: string | number, dataName = '数据') => {
    notifications.dismiss(toastId)
    notifications.success('加载完成', `${dataName}加载成功`)
  }

  return {
    saveSuccess,
    saveError,
    deleteSuccess,
    deleteConfirm,
    networkError,
    permissionError,
    validationError,
    operationSuccess,
    operationError,
    loadingData,
    loadingComplete,
    ...notifications,
  }
}
