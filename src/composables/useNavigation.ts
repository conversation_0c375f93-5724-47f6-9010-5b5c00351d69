import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getVisibleRoutes, getRouteByName } from '@/router'

/**
 * 导航相关的组合式函数
 * 确保路由能够正确导航到对应页面组件 (Requirement 6.4)
 */
export function useNavigation() {
  const router = useRouter()
  const route = useRoute()

  // 当前路由信息
  const currentRoute = computed(() => route)
  
  // 当前路由名称
  const currentRouteName = computed(() => route.name as string)
  
  // 当前路由标题
  const currentRouteTitle = computed(() => route.meta?.title as string || '')

  // 获取所有可见的导航路由
  const navigationRoutes = computed(() => {
    return getVisibleRoutes().filter(route => 
      route.path !== '/' && // 排除重定向路由
      !route.meta?.parent && // 只显示顶级路由
      !route.meta?.hidden // 排除隐藏路由
    )
  })

  // 获取面包屑导航
  const breadcrumbs = computed(() => {
    const crumbs: Array<{ name: string; title: string; path: string }> = []
    
    // 添加当前路由
    if (route.name && route.meta?.title) {
      crumbs.push({
        name: route.name as string,
        title: route.meta.title as string,
        path: route.path
      })
    }

    // 如果有父路由，添加父路由
    if (route.meta?.parent) {
      const parentRoute = getRouteByName(route.meta.parent as string)
      if (parentRoute && parentRoute.meta?.title) {
        crumbs.unshift({
          name: parentRoute.name as string,
          title: parentRoute.meta.title as string,
          path: parentRoute.path
        })
      }
    }

    return crumbs
  })

  // 导航到指定路由
  const navigateTo = (name: string, params?: Record<string, any>) => {
    try {
      router.push({ name, params })
    } catch (error) {
      console.error('Navigation error:', error)
    }
  }

  // 导航到指定路径
  const navigateToPath = (path: string) => {
    try {
      router.push(path)
    } catch (error) {
      console.error('Navigation error:', error)
    }
  }

  // 返回上一页
  const goBack = () => {
    router.back()
  }

  // 前进到下一页
  const goForward = () => {
    router.forward()
  }

  // 替换当前路由
  const replaceTo = (name: string, params?: Record<string, any>) => {
    try {
      router.replace({ name, params })
    } catch (error) {
      console.error('Navigation error:', error)
    }
  }

  // 检查路由是否存在
  const routeExists = (name: string): boolean => {
    return !!getRouteByName(name)
  }

  // 检查当前路由是否匹配
  const isCurrentRoute = (name: string): boolean => {
    return currentRouteName.value === name
  }

  // 检查是否为子路由
  const isChildRoute = (parentName: string): boolean => {
    return route.meta?.parent === parentName
  }

  // 获取路由的完整URL
  const getRouteUrl = (name: string, params?: Record<string, any>): string => {
    try {
      const resolved = router.resolve({ name, params })
      return resolved.href
    } catch (error) {
      console.error('Route resolve error:', error)
      return '#'
    }
  }

  return {
    // 状态
    currentRoute,
    currentRouteName,
    currentRouteTitle,
    navigationRoutes,
    breadcrumbs,
    
    // 方法
    navigateTo,
    navigateToPath,
    goBack,
    goForward,
    replaceTo,
    routeExists,
    isCurrentRoute,
    isChildRoute,
    getRouteUrl
  }
}