import { ref, type Ref } from 'vue'

/**
 * Dialog 状态管理 Composable
 * 用于管理对话框的打开/关闭状态
 */
export function useDialog(initialOpen = false) {
  const isOpen = ref(initialOpen)

  const open = () => {
    isOpen.value = true
  }

  const close = () => {
    isOpen.value = false
  }

  const toggle = () => {
    isOpen.value = !isOpen.value
  }

  return {
    isOpen,
    open,
    close,
    toggle,
  }
}

/**
 * 多个 Dialog 状态管理
 */
export function useDialogs<T extends string>(dialogNames: T[]) {
  const dialogs = {} as Record<T, Ref<boolean>>
  const actions = {} as Record<T, { open: () => void; close: () => void; toggle: () => void }>

  dialogNames.forEach((name) => {
    dialogs[name] = ref(false)
    actions[name] = {
      open: () => { dialogs[name].value = true },
      close: () => { dialogs[name].value = false },
      toggle: () => { dialogs[name].value = !dialogs[name].value },
    }
  })

  const closeAll = () => {
    dialogNames.forEach((name) => {
      dialogs[name].value = false
    })
  }

  const isAnyOpen = () => {
    return dialogNames.some((name) => dialogs[name].value)
  }

  return {
    dialogs,
    actions,
    closeAll,
    isAnyOpen,
  }
}

/**
 * 确认对话框 Composable
 */
export function useConfirmDialog() {
  const isOpen = ref(false)
  const title = ref('')
  const description = ref('')
  const confirmText = ref('确认')
  const cancelText = ref('取消')
  const variant = ref<'default' | 'destructive'>('default')
  
  let resolvePromise: ((value: boolean) => void) | null = null

  const confirm = (options: {
    title: string
    description?: string
    confirmText?: string
    cancelText?: string
    variant?: 'default' | 'destructive'
  }): Promise<boolean> => {
    return new Promise((resolve) => {
      title.value = options.title
      description.value = options.description || ''
      confirmText.value = options.confirmText || '确认'
      cancelText.value = options.cancelText || '取消'
      variant.value = options.variant || 'default'
      
      resolvePromise = resolve
      isOpen.value = true
    })
  }

  const handleConfirm = () => {
    isOpen.value = false
    if (resolvePromise) {
      resolvePromise(true)
      resolvePromise = null
    }
  }

  const handleCancel = () => {
    isOpen.value = false
    if (resolvePromise) {
      resolvePromise(false)
      resolvePromise = null
    }
  }

  return {
    isOpen,
    title,
    description,
    confirmText,
    cancelText,
    variant,
    confirm,
    handleConfirm,
    handleCancel,
  }
}

/**
 * 表单对话框 Composable
 */
export function useFormDialog<T extends Record<string, any>>(
  initialData: T,
  onSubmit?: (data: T) => Promise<void> | void
) {
  const isOpen = ref(false)
  const isLoading = ref(false)
  const formData = ref<T>({ ...initialData })
  const errors = ref<Partial<Record<keyof T, string>>>({})

  const open = (data?: Partial<T>) => {
    if (data) {
      formData.value = { ...initialData, ...data }
    } else {
      formData.value = { ...initialData }
    }
    errors.value = {}
    isOpen.value = true
  }

  const close = () => {
    isOpen.value = false
    isLoading.value = false
    errors.value = {}
  }

  const setError = (field: keyof T, message: string) => {
    errors.value[field] = message
  }

  const clearError = (field: keyof T) => {
    delete errors.value[field]
  }

  const clearAllErrors = () => {
    errors.value = {}
  }

  const handleSubmit = async () => {
    if (!onSubmit) return

    try {
      isLoading.value = true
      clearAllErrors()
      
      await onSubmit(formData.value)
      close()
    } catch (error) {
      console.error('Form submission error:', error)
      // 可以在这里处理表单提交错误
    } finally {
      isLoading.value = false
    }
  }

  const reset = () => {
    formData.value = { ...initialData }
    clearAllErrors()
  }

  return {
    isOpen,
    isLoading,
    formData,
    errors,
    open,
    close,
    setError,
    clearError,
    clearAllErrors,
    handleSubmit,
    reset,
  }
}

/**
 * 业务相关的对话框预设
 */
export function useBusinessDialogs() {
  const confirmDialog = useConfirmDialog()

  /**
   * 删除确认对话框
   */
  const confirmDelete = (itemName = '项目') => {
    return confirmDialog.confirm({
      title: '确认删除',
      description: `确定要删除这个${itemName}吗？此操作无法撤销。`,
      confirmText: '删除',
      cancelText: '取消',
      variant: 'destructive',
    })
  }

  /**
   * 保存确认对话框
   */
  const confirmSave = (hasChanges = true) => {
    if (!hasChanges) return Promise.resolve(true)
    
    return confirmDialog.confirm({
      title: '保存更改',
      description: '您有未保存的更改，确定要保存吗？',
      confirmText: '保存',
      cancelText: '取消',
    })
  }

  /**
   * 离开确认对话框
   */
  const confirmLeave = () => {
    return confirmDialog.confirm({
      title: '确认离开',
      description: '您有未保存的更改，确定要离开吗？',
      confirmText: '离开',
      cancelText: '取消',
      variant: 'destructive',
    })
  }

  /**
   * 重置确认对话框
   */
  const confirmReset = () => {
    return confirmDialog.confirm({
      title: '重置表单',
      description: '确定要重置表单吗？所有更改将丢失。',
      confirmText: '重置',
      cancelText: '取消',
      variant: 'destructive',
    })
  }

  return {
    confirmDialog,
    confirmDelete,
    confirmSave,
    confirmLeave,
    confirmReset,
  }
}
