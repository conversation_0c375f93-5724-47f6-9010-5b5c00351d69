import { ref, reactive } from 'vue';
import type { VisualNode, VisualEdge, Component } from '@/types/product-structure';

export interface DragDropState {
  isDragging: boolean;
  dragType: 'component' | 'node' | 'edge' | null;
  dragData: any;
  dropZone: string | null;
  ghostPosition: { x: number; y: number };
}

export interface DropZone {
  id: string;
  type: 'canvas' | 'node' | 'panel';
  accepts: string[];
  element?: HTMLElement;
  bounds?: DOMRect;
}

export function useDragDropManager() {
  const dragState = reactive<DragDropState>({
    isDragging: false,
    dragType: null,
    dragData: null,
    dropZone: null,
    ghostPosition: { x: 0, y: 0 }
  });

  const dropZones = ref<DropZone[]>([]);
  const dragPreview = ref<HTMLElement | null>(null);

  // 注册拖放区域
  const registerDropZone = (zone: DropZone) => {
    dropZones.value.push(zone);
  };

  const unregisterDropZone = (zoneId: string) => {
    const index = dropZones.value.findIndex(z => z.id === zoneId);
    if (index !== -1) {
      dropZones.value.splice(index, 1);
    }
  };

  // 开始拖拽组件
  const startComponentDrag = (event: DragEvent, component: Component) => {
    dragState.isDragging = true;
    dragState.dragType = 'component';
    dragState.dragData = component;
    
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'copy';
      event.dataTransfer.setData('application/json', JSON.stringify({
        type: 'component',
        data: component
      }));
      
      // 创建拖拽预览
      createDragPreview(component.name, 'component');
    }
  };

  // 开始拖拽节点
  const startNodeDrag = (event: DragEvent, node: VisualNode) => {
    dragState.isDragging = true;
    dragState.dragType = 'node';
    dragState.dragData = node;
    
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('application/json', JSON.stringify({
        type: 'node',
        data: node
      }));
      
      createDragPreview(node.name, 'node');
    }
  };

  // 创建拖拽预览
  const createDragPreview = (name: string, type: string) => {
    const preview = document.createElement('div');
    preview.className = 'drag-preview fixed pointer-events-none z-50 bg-white border rounded-lg shadow-lg p-2 text-sm';
    preview.innerHTML = `
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 rounded-full bg-blue-500"></div>
        <span>${name}</span>
        <span class="text-xs text-gray-500">(${type})</span>
      </div>
    `;
    
    document.body.appendChild(preview);
    dragPreview.value = preview;
  };

  // 更新拖拽预览位置
  const updateDragPreview = (event: DragEvent) => {
    if (dragPreview.value) {
      dragPreview.value.style.left = `${event.clientX + 10}px`;
      dragPreview.value.style.top = `${event.clientY - 10}px`;
    }
    
    dragState.ghostPosition = {
      x: event.clientX,
      y: event.clientY
    };
  };

  // 检查拖放目标
  const checkDropTarget = (event: DragEvent): DropZone | null => {
    const point = { x: event.clientX, y: event.clientY };
    
    for (const zone of dropZones.value) {
      if (zone.bounds && isPointInBounds(point, zone.bounds)) {
        if (zone.accepts.includes(dragState.dragType || '')) {
          return zone;
        }
      }
    }
    
    return null;
  };

  // 处理拖拽进入
  const handleDragEnter = (event: DragEvent, zoneId: string) => {
    event.preventDefault();
    const zone = dropZones.value.find(z => z.id === zoneId);
    if (zone && zone.accepts.includes(dragState.dragType || '')) {
      dragState.dropZone = zoneId;
      event.dataTransfer!.dropEffect = 'copy';
    }
  };

  // 处理拖拽悬停
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    updateDragPreview(event);
    
    const target = checkDropTarget(event);
    if (target) {
      dragState.dropZone = target.id;
      event.dataTransfer!.dropEffect = target.type === 'canvas' ? 'copy' : 'move';
    } else {
      dragState.dropZone = null;
      event.dataTransfer!.dropEffect = 'none';
    }
  };

  // 处理拖拽离开
  const handleDragLeave = (event: DragEvent, zoneId: string) => {
    if (dragState.dropZone === zoneId) {
      dragState.dropZone = null;
    }
  };

  // 处理放置
  const handleDrop = (event: DragEvent, zoneId: string) => {
    event.preventDefault();
    
    if (!dragState.isDragging || dragState.dropZone !== zoneId) {
      return null;
    }
    
    const dropData = {
      type: dragState.dragType,
      data: dragState.dragData,
      position: {
        x: event.offsetX,
        y: event.offsetY
      },
      zone: zoneId
    };
    
    endDrag();
    return dropData;
  };

  // 结束拖拽
  const endDrag = () => {
    dragState.isDragging = false;
    dragState.dragType = null;
    dragState.dragData = null;
    dragState.dropZone = null;
    
    if (dragPreview.value) {
      document.body.removeChild(dragPreview.value);
      dragPreview.value = null;
    }
  };

  // 工具函数
  const isPointInBounds = (point: { x: number; y: number }, bounds: DOMRect): boolean => {
    return point.x >= bounds.left && 
           point.x <= bounds.right && 
           point.y >= bounds.top && 
           point.y <= bounds.bottom;
  };

  // 节点位置计算
  const calculateNodePosition = (
    parentNode: VisualNode | null, 
    siblingNodes: VisualNode[], 
    layoutType: 'tree' | 'force' | 'circular'
  ): { x: number; y: number } => {
    if (!parentNode) {
      return { x: 0, y: 0 };
    }
    
    switch (layoutType) {
      case 'tree':
        return calculateTreePosition(parentNode, siblingNodes);
      case 'force':
        return calculateForcePosition(parentNode, siblingNodes);
      case 'circular':
        return calculateCircularPosition(parentNode, siblingNodes);
      default:
        return { x: parentNode.position.x + 200, y: parentNode.position.y + 100 };
    }
  };

  const calculateTreePosition = (parent: VisualNode, siblings: VisualNode[]) => {
    const baseX = parent.position.x + 200;
    const baseY = parent.position.y;
    const spacing = 80;
    
    return {
      x: baseX,
      y: baseY + (siblings.length * spacing)
    };
  };

  const calculateForcePosition = (parent: VisualNode, siblings: VisualNode[]) => {
    const angle = (siblings.length * 60) * (Math.PI / 180);
    const radius = 150;
    
    return {
      x: parent.position.x + Math.cos(angle) * radius,
      y: parent.position.y + Math.sin(angle) * radius
    };
  };

  const calculateCircularPosition = (parent: VisualNode, siblings: VisualNode[]) => {
    const angle = (siblings.length * (360 / Math.max(8, siblings.length + 1))) * (Math.PI / 180);
    const radius = 120;
    
    return {
      x: parent.position.x + Math.cos(angle) * radius,
      y: parent.position.y + Math.sin(angle) * radius
    };
  };

  // 自动布局功能
  const autoLayout = (nodes: VisualNode[], layoutType: 'tree' | 'force' | 'circular') => {
    if (nodes.length === 0) return;
    
    switch (layoutType) {
      case 'tree':
        applyTreeLayout(nodes);
        break;
      case 'force':
        applyForceLayout(nodes);
        break;
      case 'circular':
        applyCircularLayout(nodes);
        break;
    }
  };

  const applyTreeLayout = (nodes: VisualNode[]) => {
    const rootNode = nodes.find(n => n.level === 0);
    if (!rootNode) return;
    
    rootNode.position = { x: 0, y: 0 };
    
    const levelGroups = new Map<number, VisualNode[]>();
    nodes.forEach(node => {
      if (!levelGroups.has(node.level)) {
        levelGroups.set(node.level, []);
      }
      levelGroups.get(node.level)!.push(node);
    });
    
    levelGroups.forEach((levelNodes, level) => {
      if (level === 0) return;
      
      levelNodes.forEach((node, index) => {
        node.position = {
          x: level * 250,
          y: (index - levelNodes.length / 2) * 100
        };
      });
    });
  };

  const applyForceLayout = (nodes: VisualNode[]) => {
    // 简化的力导向布局
    nodes.forEach((node, index) => {
      const angle = (index / nodes.length) * 2 * Math.PI;
      const radius = 200 + node.level * 100;
      
      node.position = {
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius
      };
    });
  };

  const applyCircularLayout = (nodes: VisualNode[]) => {
    const centerX = 0;
    const centerY = 0;
    
    nodes.forEach((node, index) => {
      if (node.level === 0) {
        node.position = { x: centerX, y: centerY };
        return;
      }
      
      const angle = (index / nodes.length) * 2 * Math.PI;
      const radius = node.level * 150;
      
      node.position = {
        x: centerX + Math.cos(angle) * radius,
        y: centerY + Math.sin(angle) * radius
      };
    });
  };

  return {
    dragState,
    dropZones,
    registerDropZone,
    unregisterDropZone,
    startComponentDrag,
    startNodeDrag,
    handleDragEnter,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    endDrag,
    calculateNodePosition,
    autoLayout
  };
}
