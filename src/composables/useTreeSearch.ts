import { ref, computed, watch } from 'vue';

export interface TreeNode {
  id: string;
  name: string;
  code?: string;
  type: string;
  description?: string;
  children?: TreeNode[];
  parent?: TreeNode;
  level: number;
  path: string[];
}

export interface SearchOptions {
  caseSensitive: boolean;
  searchInDescription: boolean;
  searchInCode: boolean;
  nodeTypes: string[];
}

export function useTreeSearch(nodes: TreeNode[]) {
  const searchQuery = ref('');
  const searchOptions = ref<SearchOptions>({
    caseSensitive: false,
    searchInDescription: true,
    searchInCode: true,
    nodeTypes: []
  });
  
  const expandedNodes = ref<Set<string>>(new Set());
  const highlightedNodes = ref<Set<string>>(new Set());
  const searchResults = ref<TreeNode[]>([]);
  const currentResultIndex = ref(-1);

  // 搜索匹配函数
  const matchesSearch = (node: TreeNode, query: string): boolean => {
    if (!query.trim()) return true;

    const searchText = searchOptions.value.caseSensitive ? query : query.toLowerCase();
    
    // 检查节点名称
    const name = searchOptions.value.caseSensitive ? node.name : node.name.toLowerCase();
    if (name.includes(searchText)) return true;

    // 检查编码
    if (searchOptions.value.searchInCode && node.code) {
      const code = searchOptions.value.caseSensitive ? node.code : node.code.toLowerCase();
      if (code.includes(searchText)) return true;
    }

    // 检查描述
    if (searchOptions.value.searchInDescription && node.description) {
      const description = searchOptions.value.caseSensitive ? node.description : node.description.toLowerCase();
      if (description.includes(searchText)) return true;
    }

    return false;
  };

  // 类型过滤函数
  const matchesTypeFilter = (node: TreeNode): boolean => {
    if (searchOptions.value.nodeTypes.length === 0) return true;
    return searchOptions.value.nodeTypes.includes(node.type);
  };

  // 递归搜索节点
  const searchInTree = (nodes: TreeNode[], query: string): TreeNode[] => {
    const results: TreeNode[] = [];

    const searchRecursive = (nodeList: TreeNode[]) => {
      nodeList.forEach(node => {
        const matchesQuery = matchesSearch(node, query);
        const matchesType = matchesTypeFilter(node);

        if (matchesQuery && matchesType) {
          results.push(node);
        }

        if (node.children) {
          searchRecursive(node.children);
        }
      });
    };

    searchRecursive(nodes);
    return results;
  };

  // 计算过滤后的节点
  const filteredNodes = computed(() => {
    if (!searchQuery.value.trim() && searchOptions.value.nodeTypes.length === 0) {
      return nodes;
    }

    return searchInTree(nodes, searchQuery.value);
  });

  // 计算需要展开的节点（显示搜索结果的路径）
  const nodesNeedExpansion = computed(() => {
    const needExpansion = new Set<string>();

    searchResults.value.forEach(node => {
      // 展开到根节点的路径
      node.path.forEach(nodeId => {
        needExpansion.add(nodeId);
      });
    });

    return needExpansion;
  });

  // 执行搜索
  const performSearch = (query: string) => {
    searchQuery.value = query;
    
    if (!query.trim()) {
      searchResults.value = [];
      highlightedNodes.value.clear();
      currentResultIndex.value = -1;
      return;
    }

    const results = searchInTree(nodes, query);
    searchResults.value = results;
    
    // 高亮搜索结果
    highlightedNodes.value.clear();
    results.forEach(node => {
      highlightedNodes.value.add(node.id);
    });

    // 自动展开搜索结果的父节点
    nodesNeedExpansion.value.forEach(nodeId => {
      expandedNodes.value.add(nodeId);
    });

    currentResultIndex.value = results.length > 0 ? 0 : -1;
  };

  // 导航到下一个搜索结果
  const nextResult = () => {
    if (searchResults.value.length === 0) return null;
    
    currentResultIndex.value = (currentResultIndex.value + 1) % searchResults.value.length;
    return searchResults.value[currentResultIndex.value];
  };

  // 导航到上一个搜索结果
  const previousResult = () => {
    if (searchResults.value.length === 0) return null;
    
    currentResultIndex.value = currentResultIndex.value <= 0 
      ? searchResults.value.length - 1 
      : currentResultIndex.value - 1;
    return searchResults.value[currentResultIndex.value];
  };

  // 获取当前搜索结果
  const getCurrentResult = () => {
    if (currentResultIndex.value >= 0 && currentResultIndex.value < searchResults.value.length) {
      return searchResults.value[currentResultIndex.value];
    }
    return null;
  };

  // 清除搜索
  const clearSearch = () => {
    searchQuery.value = '';
    searchResults.value = [];
    highlightedNodes.value.clear();
    currentResultIndex.value = -1;
  };

  // 展开/折叠节点
  const toggleNode = (nodeId: string) => {
    if (expandedNodes.value.has(nodeId)) {
      expandedNodes.value.delete(nodeId);
    } else {
      expandedNodes.value.add(nodeId);
    }
  };

  const expandNode = (nodeId: string) => {
    expandedNodes.value.add(nodeId);
  };

  const collapseNode = (nodeId: string) => {
    expandedNodes.value.delete(nodeId);
  };

  // 展开所有节点
  const expandAll = () => {
    const expandRecursive = (nodeList: TreeNode[]) => {
      nodeList.forEach(node => {
        expandedNodes.value.add(node.id);
        if (node.children) {
          expandRecursive(node.children);
        }
      });
    };

    expandRecursive(nodes);
  };

  // 折叠所有节点
  const collapseAll = () => {
    expandedNodes.value.clear();
  };

  // 展开到指定节点
  const expandToNode = (nodeId: string) => {
    const findNodePath = (nodeList: TreeNode[], targetId: string, path: string[] = []): string[] | null => {
      for (const node of nodeList) {
        const currentPath = [...path, node.id];
        
        if (node.id === targetId) {
          return currentPath;
        }
        
        if (node.children) {
          const result = findNodePath(node.children, targetId, currentPath);
          if (result) return result;
        }
      }
      return null;
    };

    const path = findNodePath(nodes, nodeId);
    if (path) {
      // 展开路径上的所有节点（除了目标节点本身）
      path.slice(0, -1).forEach(id => {
        expandedNodes.value.add(id);
      });
    }
  };

  // 获取搜索统计信息
  const getSearchStats = () => {
    const stats = {
      total: searchResults.value.length,
      current: currentResultIndex.value + 1,
      byType: {} as Record<string, number>
    };

    searchResults.value.forEach(node => {
      stats.byType[node.type] = (stats.byType[node.type] || 0) + 1;
    });

    return stats;
  };

  // 高级搜索功能
  const searchByRegex = (pattern: string) => {
    try {
      const regex = new RegExp(pattern, searchOptions.value.caseSensitive ? 'g' : 'gi');
      
      const results = nodes.filter(node => {
        return regex.test(node.name) || 
               (node.code && regex.test(node.code)) ||
               (node.description && regex.test(node.description));
      });

      searchResults.value = results;
      highlightedNodes.value.clear();
      results.forEach(node => highlightedNodes.value.add(node.id));
      
      return results;
    } catch (error) {
      console.error('正则表达式搜索失败:', error);
      return [];
    }
  };

  // 按路径搜索
  const searchByPath = (pathPattern: string) => {
    const results = nodes.filter(node => {
      const pathString = node.path.join(' > ');
      const searchText = searchOptions.value.caseSensitive ? pathPattern : pathPattern.toLowerCase();
      const targetText = searchOptions.value.caseSensitive ? pathString : pathString.toLowerCase();
      
      return targetText.includes(searchText);
    });

    searchResults.value = results;
    return results;
  };

  // 监听搜索查询变化
  watch(searchQuery, (newQuery) => {
    if (newQuery.trim()) {
      performSearch(newQuery);
    } else {
      clearSearch();
    }
  }, { debounce: 300 });

  return {
    searchQuery,
    searchOptions,
    expandedNodes,
    highlightedNodes,
    searchResults,
    currentResultIndex,
    filteredNodes,
    
    // 搜索功能
    performSearch,
    clearSearch,
    nextResult,
    previousResult,
    getCurrentResult,
    getSearchStats,
    
    // 高级搜索
    searchByRegex,
    searchByPath,
    
    // 节点展开/折叠
    toggleNode,
    expandNode,
    collapseNode,
    expandAll,
    collapseAll,
    expandToNode
  };
}
