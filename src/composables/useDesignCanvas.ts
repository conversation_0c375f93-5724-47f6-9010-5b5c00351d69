import { ref, reactive, computed } from 'vue';

export interface CanvasNode {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  selected: boolean;
  dragging: boolean;
  data: any;
}

export interface CanvasState {
  zoom: number;
  panX: number;
  panY: number;
  isDragging: boolean;
  isSelecting: boolean;
  selectionBox: {
    startX: number;
    startY: number;
    endX: number;
    endY: number;
    visible: boolean;
  };
  dragStart: {
    x: number;
    y: number;
    panX: number;
    panY: number;
  };
}

export function useDesignCanvas() {
  const canvasRef = ref<HTMLElement>();
  const nodes = ref<CanvasNode[]>([]);
  const selectedNodeIds = ref<Set<string>>(new Set());
  
  const canvasState = reactive<CanvasState>({
    zoom: 1,
    panX: 0,
    panY: 0,
    isDragging: false,
    isSelecting: false,
    selectionBox: {
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      visible: false
    },
    dragStart: {
      x: 0,
      y: 0,
      panX: 0,
      panY: 0
    }
  });

  // 计算属性
  const selectedNodes = computed(() => 
    nodes.value.filter(node => selectedNodeIds.value.has(node.id))
  );

  const canvasTransform = computed(() => 
    `scale(${canvasState.zoom}) translate(${canvasState.panX}px, ${canvasState.panY}px)`
  );

  // 坐标转换
  const screenToCanvas = (screenX: number, screenY: number) => {
    if (!canvasRef.value) return { x: screenX, y: screenY };
    
    const rect = canvasRef.value.getBoundingClientRect();
    const canvasX = (screenX - rect.left - canvasState.panX) / canvasState.zoom;
    const canvasY = (screenY - rect.top - canvasState.panY) / canvasState.zoom;
    
    return { x: canvasX, y: canvasY };
  };

  const canvasToScreen = (canvasX: number, canvasY: number) => {
    if (!canvasRef.value) return { x: canvasX, y: canvasY };
    
    const rect = canvasRef.value.getBoundingClientRect();
    const screenX = canvasX * canvasState.zoom + canvasState.panX + rect.left;
    const screenY = canvasY * canvasState.zoom + canvasState.panY + rect.top;
    
    return { x: screenX, y: screenY };
  };

  // 缩放功能
  const zoomIn = (centerX?: number, centerY?: number) => {
    const oldZoom = canvasState.zoom;
    canvasState.zoom = Math.min(canvasState.zoom * 1.2, 5);
    
    if (centerX !== undefined && centerY !== undefined) {
      const zoomRatio = canvasState.zoom / oldZoom;
      canvasState.panX = centerX - (centerX - canvasState.panX) * zoomRatio;
      canvasState.panY = centerY - (centerY - canvasState.panY) * zoomRatio;
    }
  };

  const zoomOut = (centerX?: number, centerY?: number) => {
    const oldZoom = canvasState.zoom;
    canvasState.zoom = Math.max(canvasState.zoom / 1.2, 0.1);
    
    if (centerX !== undefined && centerY !== undefined) {
      const zoomRatio = canvasState.zoom / oldZoom;
      canvasState.panX = centerX - (centerX - canvasState.panX) * zoomRatio;
      canvasState.panY = centerY - (centerY - canvasState.panY) * zoomRatio;
    }
  };

  const setZoom = (zoom: number, centerX?: number, centerY?: number) => {
    const oldZoom = canvasState.zoom;
    canvasState.zoom = Math.max(0.1, Math.min(5, zoom));
    
    if (centerX !== undefined && centerY !== undefined) {
      const zoomRatio = canvasState.zoom / oldZoom;
      canvasState.panX = centerX - (centerX - canvasState.panX) * zoomRatio;
      canvasState.panY = centerY - (centerY - canvasState.panY) * zoomRatio;
    }
  };

  // 适应视图
  const fitToView = () => {
    if (nodes.value.length === 0) return;
    
    const bounds = calculateNodesBounds();
    if (!bounds || !canvasRef.value) return;
    
    const rect = canvasRef.value.getBoundingClientRect();
    const padding = 50;
    
    const scaleX = (rect.width - padding * 2) / bounds.width;
    const scaleY = (rect.height - padding * 2) / bounds.height;
    const scale = Math.min(scaleX, scaleY, 2); // 最大缩放2倍
    
    canvasState.zoom = scale;
    canvasState.panX = (rect.width - bounds.width * scale) / 2 - bounds.left * scale;
    canvasState.panY = (rect.height - bounds.height * scale) / 2 - bounds.top * scale;
  };

  const calculateNodesBounds = () => {
    if (nodes.value.length === 0) return null;
    
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    nodes.value.forEach(node => {
      minX = Math.min(minX, node.x);
      minY = Math.min(minY, node.y);
      maxX = Math.max(maxX, node.x + node.width);
      maxY = Math.max(maxY, node.y + node.height);
    });
    
    return {
      left: minX,
      top: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  };

  // 节点选择
  const selectNode = (nodeId: string, multiSelect = false) => {
    if (multiSelect) {
      if (selectedNodeIds.value.has(nodeId)) {
        selectedNodeIds.value.delete(nodeId);
      } else {
        selectedNodeIds.value.add(nodeId);
      }
    } else {
      selectedNodeIds.value.clear();
      selectedNodeIds.value.add(nodeId);
    }
  };

  const selectNodes = (nodeIds: string[]) => {
    selectedNodeIds.value.clear();
    nodeIds.forEach(id => selectedNodeIds.value.add(id));
  };

  const clearSelection = () => {
    selectedNodeIds.value.clear();
  };

  // 框选功能
  const startSelection = (x: number, y: number) => {
    const canvasPos = screenToCanvas(x, y);
    canvasState.isSelecting = true;
    canvasState.selectionBox.startX = canvasPos.x;
    canvasState.selectionBox.startY = canvasPos.y;
    canvasState.selectionBox.endX = canvasPos.x;
    canvasState.selectionBox.endY = canvasPos.y;
    canvasState.selectionBox.visible = true;
  };

  const updateSelection = (x: number, y: number) => {
    if (!canvasState.isSelecting) return;
    
    const canvasPos = screenToCanvas(x, y);
    canvasState.selectionBox.endX = canvasPos.x;
    canvasState.selectionBox.endY = canvasPos.y;
    
    // 计算框选范围内的节点
    const selectedIds = getNodesInSelectionBox();
    selectNodes(selectedIds);
  };

  const endSelection = () => {
    canvasState.isSelecting = false;
    canvasState.selectionBox.visible = false;
  };

  const getNodesInSelectionBox = (): string[] => {
    const box = canvasState.selectionBox;
    const left = Math.min(box.startX, box.endX);
    const right = Math.max(box.startX, box.endX);
    const top = Math.min(box.startY, box.endY);
    const bottom = Math.max(box.startY, box.endY);
    
    return nodes.value
      .filter(node => 
        node.x < right && 
        node.x + node.width > left && 
        node.y < bottom && 
        node.y + node.height > top
      )
      .map(node => node.id);
  };

  // 节点拖拽
  const startNodeDrag = (nodeId: string, x: number, y: number) => {
    const node = nodes.value.find(n => n.id === nodeId);
    if (!node) return;
    
    if (!selectedNodeIds.value.has(nodeId)) {
      selectNode(nodeId);
    }
    
    const canvasPos = screenToCanvas(x, y);
    selectedNodes.value.forEach(n => {
      n.dragging = true;
      n.data.dragOffset = {
        x: canvasPos.x - n.x,
        y: canvasPos.y - n.y
      };
    });
  };

  const updateNodeDrag = (x: number, y: number) => {
    const canvasPos = screenToCanvas(x, y);
    
    selectedNodes.value.forEach(node => {
      if (node.dragging && node.data.dragOffset) {
        node.x = canvasPos.x - node.data.dragOffset.x;
        node.y = canvasPos.y - node.data.dragOffset.y;
      }
    });
  };

  const endNodeDrag = () => {
    selectedNodes.value.forEach(node => {
      node.dragging = false;
      delete node.data.dragOffset;
    });
  };

  // 画布拖拽
  const startCanvasDrag = (x: number, y: number) => {
    canvasState.isDragging = true;
    canvasState.dragStart.x = x;
    canvasState.dragStart.y = y;
    canvasState.dragStart.panX = canvasState.panX;
    canvasState.dragStart.panY = canvasState.panY;
  };

  const updateCanvasDrag = (x: number, y: number) => {
    if (!canvasState.isDragging) return;
    
    const deltaX = x - canvasState.dragStart.x;
    const deltaY = y - canvasState.dragStart.y;
    
    canvasState.panX = canvasState.dragStart.panX + deltaX;
    canvasState.panY = canvasState.dragStart.panY + deltaY;
  };

  const endCanvasDrag = () => {
    canvasState.isDragging = false;
  };

  // 重置视图
  const resetView = () => {
    canvasState.zoom = 1;
    canvasState.panX = 0;
    canvasState.panY = 0;
  };

  return {
    canvasRef,
    nodes,
    selectedNodeIds,
    selectedNodes,
    canvasState,
    canvasTransform,
    
    // 坐标转换
    screenToCanvas,
    canvasToScreen,
    
    // 缩放功能
    zoomIn,
    zoomOut,
    setZoom,
    fitToView,
    resetView,
    
    // 选择功能
    selectNode,
    selectNodes,
    clearSelection,
    startSelection,
    updateSelection,
    endSelection,
    
    // 拖拽功能
    startNodeDrag,
    updateNodeDrag,
    endNodeDrag,
    startCanvasDrag,
    updateCanvasDrag,
    endCanvasDrag
  };
}
