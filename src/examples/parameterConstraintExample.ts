/**
 * 参数化设计和约束管理使用示例
 * 
 * 展示如何使用参数验证引擎和约束求解系统
 */

import { parameterConstraintService } from '@/services/parameterConstraintService';
import type { Component, ComponentParameter, ComponentConstraint } from '@/types/product-structure';

/**
 * 创建示例组件：铝合金窗框
 */
function createSampleWindowFrame(): Component {
  const parameters: ComponentParameter[] = [
    {
      id: 'param-width',
      name: 'width',
      displayName: '窗框宽度',
      type: 'number',
      unit: 'mm',
      defaultValue: 1200,
      minValue: 600,
      maxValue: 2400,
      required: true,
      description: '窗框的宽度尺寸',
      category: 'dimension',
      visible: true,
      editable: true,
      validationRules: ['value >= 600', 'value <= 2400', 'value % 50 == 0'] // 必须是50的倍数
    },
    {
      id: 'param-height',
      name: 'height',
      displayName: '窗框高度',
      type: 'number',
      unit: 'mm',
      defaultValue: 1500,
      minValue: 800,
      maxValue: 2200,
      required: true,
      description: '窗框的高度尺寸',
      category: 'dimension',
      visible: true,
      editable: true,
      validationRules: ['value >= 800', 'value <= 2200', 'value % 50 == 0']
    },
    {
      id: 'param-profile-series',
      name: 'profileSeries',
      displayName: '型材系列',
      type: 'select',
      options: [
        { value: '60', label: '60系列' },
        { value: '70', label: '70系列' },
        { value: '80', label: '80系列' }
      ],
      defaultValue: '70',
      required: true,
      description: '铝合金型材系列',
      category: 'material',
      visible: true,
      editable: true
    },
    {
      id: 'param-glass-thickness',
      name: 'glassThickness',
      displayName: '玻璃厚度',
      type: 'select',
      options: [
        { value: '5', label: '5mm' },
        { value: '6', label: '6mm' },
        { value: '8', label: '8mm' },
        { value: '10', label: '10mm' }
      ],
      defaultValue: '6',
      required: true,
      description: '玻璃厚度规格',
      category: 'material',
      visible: true,
      editable: true
    },
    {
      id: 'param-opening-type',
      name: 'openingType',
      displayName: '开启方式',
      type: 'select',
      options: [
        { value: 'fixed', label: '固定窗' },
        { value: 'casement', label: '平开窗' },
        { value: 'sliding', label: '推拉窗' },
        { value: 'tilt-turn', label: '内开内倒' }
      ],
      defaultValue: 'casement',
      required: true,
      description: '窗户开启方式',
      category: 'process',
      visible: true,
      editable: true
    }
  ];

  const constraints: ComponentConstraint[] = [
    {
      id: 'constraint-aspect-ratio',
      name: '宽高比约束',
      type: 'dimension',
      expression: 'width / height >= 0.4 && width / height <= 4.0',
      errorMessage: '窗框宽高比必须在0.4到4.0之间，以确保结构稳定性',
      severity: 'error',
      enabled: true,
      description: '确保窗框比例合理，避免结构问题',
      autoFix: {
        enabled: true,
        fixExpression: 'height = width / 2.0',
        fixMessage: '建议调整高度为宽度的一半'
      }
    },
    {
      id: 'constraint-min-area',
      name: '最小面积约束',
      type: 'dimension',
      expression: 'width * height >= 600000',
      errorMessage: '窗框面积不能小于0.6平方米',
      severity: 'warning',
      enabled: true,
      description: '确保窗户面积满足采光要求'
    },
    {
      id: 'constraint-max-area',
      name: '最大面积约束',
      type: 'dimension',
      expression: 'width * height <= 4000000',
      errorMessage: '窗框面积不能超过4平方米，超大窗户需要特殊设计',
      severity: 'error',
      enabled: true,
      description: '限制窗户最大面积以确保安全性'
    },
    {
      id: 'constraint-profile-glass-compatibility',
      name: '型材玻璃兼容性',
      type: 'compatibility',
      expression: '(profileSeries == "60" && glassThickness <= 6) || (profileSeries == "70" && glassThickness <= 8) || (profileSeries == "80" && glassThickness <= 10)',
      errorMessage: '所选型材系列不支持该玻璃厚度',
      severity: 'error',
      enabled: true,
      description: '确保型材系列与玻璃厚度匹配'
    },
    {
      id: 'constraint-opening-size-limit',
      name: '开启扇尺寸限制',
      type: 'process',
      expression: 'openingType == "fixed" || (openingType != "fixed" && width <= 1800 && height <= 2000)',
      errorMessage: '开启扇尺寸过大，建议使用固定窗或分格设计',
      severity: 'warning',
      enabled: true,
      description: '限制开启扇尺寸以确保操作便利性'
    }
  ];

  return {
    id: 'window-frame-001',
    code: 'WF001',
    name: '铝合金窗框',
    description: '标准铝合金窗框组件',
    version: 1,
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    updatedBy: 'system',
    componentType: 'frame',
    materialCategoryId: 'aluminum-frame',
    materialCategoryName: '铝合金框料',
    materialCategoryCode: 'ALF001',
    parameters,
    constraints,
    quantityFormula: '1',
    costFormula: '(width + height) * 2 * profileSeries * 0.1 + width * height * glassThickness * 0.001',
    processRequirements: [],
    properties: {},
    tags: ['window', 'frame', 'aluminum'],
    reusable: true
  };
}

/**
 * 示例1: 基本参数验证
 */
export async function example1_BasicParameterValidation() {
  console.log('=== 示例1: 基本参数验证 ===');
  
  const component = createSampleWindowFrame();
  
  // 测试有效参数
  const validParams = {
    width: 1200,
    height: 1500,
    profileSeries: '70',
    glassThickness: '6',
    openingType: 'casement'
  };
  
  console.log('测试有效参数:', validParams);
  const result1 = await parameterConstraintService.validateComponent(component, validParams);
  console.log('验证结果:', {
    isValid: result1.isValid,
    parameterErrors: result1.parameterValidation.errors.length,
    constraintErrors: result1.constraintValidation.errors.length
  });
  
  // 测试无效参数
  const invalidParams = {
    width: 3000, // 超过最大值
    height: 500, // 小于最小值
    profileSeries: '60',
    glassThickness: '10', // 与60系列不兼容
    openingType: 'casement'
  };
  
  console.log('\n测试无效参数:', invalidParams);
  const result2 = await parameterConstraintService.validateComponent(component, invalidParams);
  console.log('验证结果:', {
    isValid: result2.isValid,
    parameterErrors: result2.parameterValidation.errors.length,
    constraintErrors: result2.constraintValidation.errors.length
  });
  
  if (result2.parameterValidation.errors.length > 0) {
    console.log('参数错误:');
    result2.parameterValidation.errors.forEach(error => {
      console.log(`  - ${error.message}`);
    });
  }
  
  if (result2.constraintValidation.errors.length > 0) {
    console.log('约束错误:');
    result2.constraintValidation.errors.forEach(error => {
      console.log(`  - ${error.message}`);
    });
  }
}

/**
 * 示例2: 约束冲突检测和修复建议
 */
export async function example2_ConstraintConflictDetection() {
  console.log('\n=== 示例2: 约束冲突检测和修复建议 ===');
  
  const component = createSampleWindowFrame();
  
  // 创建冲突的参数值
  const conflictParams = {
    width: 600,  // 宽高比 = 0.3，小于最小值 0.4
    height: 2000,
    profileSeries: '70',
    glassThickness: '6',
    openingType: 'casement'
  };
  
  console.log('测试冲突参数:', conflictParams);
  const result = await parameterConstraintService.validateComponent(component, conflictParams);
  
  console.log('验证结果:', {
    isValid: result.isValid,
    conflicts: result.constraintSolverResult.conflicts.length,
    suggestions: result.fixSuggestions.length
  });
  
  if (result.constraintSolverResult.conflicts.length > 0) {
    console.log('检测到的冲突:');
    result.constraintSolverResult.conflicts.forEach(conflict => {
      console.log(`  - ${conflict.description}`);
      console.log(`    涉及参数: ${conflict.affectedParameters.join(', ')}`);
    });
  }
  
  if (result.fixSuggestions.length > 0) {
    console.log('修复建议:');
    result.fixSuggestions.forEach(suggestion => {
      console.log(`  - ${suggestion.description} (置信度: ${suggestion.confidence})`);
      if (suggestion.parameterChanges) {
        console.log(`    建议参数值:`, suggestion.parameterChanges);
      }
    });
  }
}

/**
 * 示例3: 参数优化
 */
export async function example3_ParameterOptimization() {
  console.log('\n=== 示例3: 参数优化 ===');
  
  const component = createSampleWindowFrame();
  
  const currentParams = {
    width: 1000,
    height: 1200,
    profileSeries: '60',
    glassThickness: '5',
    openingType: 'casement'
  };
  
  // 优化目标：最大化窗户面积
  const objectives = [
    { parameter: 'width', target: 'maximize' as const },
    { parameter: 'height', target: 'maximize' as const }
  ];
  
  console.log('当前参数:', currentParams);
  console.log('优化目标: 最大化窗户面积');
  
  const optimizationResult = await parameterConstraintService.optimizeParameterConfiguration(
    component.parameters,
    component.constraints,
    currentParams,
    objectives
  );
  
  console.log('优化结果:');
  console.log('  优化后参数:', optimizationResult.optimizedValues);
  console.log('  改进分数:', optimizationResult.improvementScore.toFixed(2));
  console.log('  优化步骤:');
  optimizationResult.optimizationSteps.forEach(step => {
    console.log(`    - ${step}`);
  });
  
  // 计算面积改进
  const originalArea = currentParams.width * currentParams.height;
  const optimizedArea = optimizationResult.optimizedValues.width * optimizationResult.optimizedValues.height;
  const areaImprovement = ((optimizedArea - originalArea) / originalArea * 100).toFixed(1);
  
  console.log(`  面积改进: ${areaImprovement}% (${(originalArea/1000000).toFixed(2)}m² → ${(optimizedArea/1000000).toFixed(2)}m²)`);
}

/**
 * 示例4: 参数建议生成
 */
export async function example4_ParameterSuggestions() {
  console.log('\n=== 示例4: 参数建议生成 ===');
  
  const component = createSampleWindowFrame();
  
  // 只提供部分参数
  const partialParams = {
    openingType: 'sliding',
    profileSeries: '80'
  };
  
  console.log('部分参数:', partialParams);
  
  const suggestions = await parameterConstraintService.generateParameterSuggestions(
    component.parameters,
    component.constraints,
    partialParams
  );
  
  console.log('参数建议:');
  Object.entries(suggestions.parameterSuggestions).forEach(([param, value]) => {
    console.log(`  ${param}: ${value}`);
  });
  
  console.log('建议理由:');
  suggestions.reasoning.forEach(reason => {
    console.log(`  - ${reason}`);
  });
  
  if (suggestions.constraintSuggestions.length > 0) {
    console.log('约束建议:');
    suggestions.constraintSuggestions.forEach(suggestion => {
      console.log(`  - ${suggestion.description}`);
    });
  }
}

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  try {
    await example1_BasicParameterValidation();
    await example2_ConstraintConflictDetection();
    await example3_ParameterOptimization();
    await example4_ParameterSuggestions();
    
    console.log('\n=== 所有示例运行完成 ===');
  } catch (error) {
    console.error('运行示例时发生错误:', error);
  }
}

// 如果直接运行此文件，则执行所有示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples();
}
