/**
 * 组件管理功能使用示例
 * 
 * 展示如何使用组件管理服务进行组件的创建、查询、更新和删除操作
 */

import { componentService } from '@/services/componentService';
import type { Component, ComponentParameter, ComponentConstraint } from '@/types/product-structure';

/**
 * 示例1: 创建一个完整的铝合金窗框组件
 */
export async function example1_CreateCompleteComponent() {
  console.log('=== 示例1: 创建完整的铝合金窗框组件 ===');

  // 定义参数
  const parameters: ComponentParameter[] = [
    {
      id: 'param-width',
      name: 'width',
      displayName: '窗框宽度',
      type: 'number',
      unit: 'mm',
      defaultValue: 1200,
      minValue: 600,
      maxValue: 2400,
      required: true,
      description: '窗框的宽度尺寸',
      category: 'dimension',
      visible: true,
      editable: true,
      validationRules: ['value >= 600', 'value <= 2400', 'value % 50 == 0']
    },
    {
      id: 'param-height',
      name: 'height',
      displayName: '窗框高度',
      type: 'number',
      unit: 'mm',
      defaultValue: 1500,
      minValue: 800,
      maxValue: 2200,
      required: true,
      description: '窗框的高度尺寸',
      category: 'dimension',
      visible: true,
      editable: true,
      validationRules: ['value >= 800', 'value <= 2200', 'value % 50 == 0']
    },
    {
      id: 'param-profile-series',
      name: 'profileSeries',
      displayName: '型材系列',
      type: 'select',
      options: [
        { value: '60', label: '60系列' },
        { value: '70', label: '70系列' },
        { value: '80', label: '80系列' }
      ],
      defaultValue: '70',
      required: true,
      description: '铝合金型材系列',
      category: 'material',
      visible: true,
      editable: true
    },
    {
      id: 'param-color',
      name: 'color',
      displayName: '颜色',
      type: 'select',
      options: [
        { value: 'white', label: '白色' },
        { value: 'black', label: '黑色' },
        { value: 'gray', label: '灰色' },
        { value: 'brown', label: '棕色' }
      ],
      defaultValue: 'white',
      required: true,
      description: '窗框颜色',
      category: 'appearance',
      visible: true,
      editable: true
    }
  ];

  // 定义约束条件
  const constraints: ComponentConstraint[] = [
    {
      id: 'constraint-aspect-ratio',
      name: '宽高比约束',
      type: 'dimension',
      expression: 'width / height >= 0.4 && width / height <= 4.0',
      errorMessage: '窗框宽高比必须在0.4到4.0之间，以确保结构稳定性',
      severity: 'error',
      enabled: true,
      description: '确保窗框比例合理，避免结构问题',
      autoFix: {
        enabled: true,
        fixExpression: 'height = width / 2.0',
        fixMessage: '建议调整高度为宽度的一半'
      }
    },
    {
      id: 'constraint-min-area',
      name: '最小面积约束',
      type: 'dimension',
      expression: 'width * height >= 600000',
      errorMessage: '窗框面积不能小于0.6平方米',
      severity: 'warning',
      enabled: true,
      description: '确保窗户面积满足采光要求'
    }
  ];

  // 创建组件
  const component = await componentService.createComponent({
    name: '铝合金窗框',
    code: 'ALU_FRAME_001',
    description: '标准铝合金窗框，支持多种尺寸和系列配置',
    componentType: 'frame',
    materialCategoryId: 'aluminum-frame',
    materialCategoryName: '铝合金框料',
    materialCategoryCode: 'ALF001',
    parameters,
    constraints,
    quantityFormula: '1',
    costFormula: '(width + height) * 2 * profileSeries * 0.1',
    processRequirements: [
      {
        id: 'process-cutting',
        name: '型材切割',
        processType: 'cutting',
        description: '按照尺寸要求切割铝合金型材',
        estimatedTime: 15,
        skillLevel: 'intermediate',
        equipmentRequired: '铝材切割机',
        qualityStandard: '切割精度±0.5mm',
        steps: [
          { stepNumber: 1, description: '测量并标记切割位置', estimatedTime: 3 },
          { stepNumber: 2, description: '设置切割机参数', estimatedTime: 2 },
          { stepNumber: 3, description: '执行切割操作', estimatedTime: 8 },
          { stepNumber: 4, description: '检查切割质量', estimatedTime: 2 }
        ],
        qualityChecks: [
          { checkPoint: '切割尺寸', standard: '±0.5mm', isCritical: true },
          { checkPoint: '切割面光洁度', standard: '无毛刺', isCritical: false }
        ]
      }
    ],
    properties: {
      thermalBreak: true,
      windResistance: 'Grade 6',
      waterTightness: 'Grade 4'
    },
    tags: ['铝合金', '窗框', '标准件', '可定制'],
    reusable: true,
    status: 'active'
  });

  console.log('创建的组件:', {
    id: component.id,
    name: component.name,
    code: component.code,
    parametersCount: component.parameters.length,
    constraintsCount: component.constraints.length,
    processRequirementsCount: component.processRequirements.length
  });

  return component;
}

/**
 * 示例2: 组件查询和筛选
 */
export async function example2_QueryAndFilterComponents() {
  console.log('\n=== 示例2: 组件查询和筛选 ===');

  // 创建一些测试组件
  await Promise.all([
    componentService.createComponent({
      name: '钢化玻璃',
      code: 'GLASS_001',
      componentType: 'glass',
      materialCategoryId: 'tempered-glass',
      status: 'active',
      tags: ['玻璃', '安全', '透明'],
      reusable: true
    }),
    componentService.createComponent({
      name: '门锁五金',
      code: 'HARDWARE_001',
      componentType: 'hardware',
      materialCategoryId: 'door-hardware',
      status: 'draft',
      tags: ['五金', '门锁', '安全'],
      reusable: false
    }),
    componentService.createComponent({
      name: '密封胶条',
      code: 'SEAL_001',
      componentType: 'seal',
      materialCategoryId: 'rubber-seal',
      status: 'active',
      tags: ['密封', '橡胶', '防水'],
      reusable: true
    })
  ]);

  // 1. 获取所有组件
  console.log('\n1. 获取所有组件:');
  const allComponents = await componentService.getComponents();
  console.log(`总共 ${allComponents.total} 个组件`);

  // 2. 按类型筛选
  console.log('\n2. 筛选框料组件:');
  const frameComponents = await componentService.getComponents({
    componentType: ['frame']
  });
  console.log(`框料组件: ${frameComponents.components.length} 个`);
  frameComponents.components.forEach(c => {
    console.log(`  - ${c.name} (${c.code})`);
  });

  // 3. 按状态筛选
  console.log('\n3. 筛选活跃状态组件:');
  const activeComponents = await componentService.getComponents({
    status: ['active']
  });
  console.log(`活跃组件: ${activeComponents.components.length} 个`);

  // 4. 关键词搜索
  console.log('\n4. 搜索包含"玻璃"的组件:');
  const glassComponents = await componentService.getComponents({
    search: '玻璃'
  });
  console.log(`搜索结果: ${glassComponents.components.length} 个`);
  glassComponents.components.forEach(c => {
    console.log(`  - ${c.name} (${c.code})`);
  });

  // 5. 按标签筛选
  console.log('\n5. 筛选带有"安全"标签的组件:');
  const safetyComponents = await componentService.getComponents({
    tags: ['安全']
  });
  console.log(`安全相关组件: ${safetyComponents.components.length} 个`);

  // 6. 排序和分页
  console.log('\n6. 按名称排序并分页:');
  const sortedComponents = await componentService.getComponents(
    {},
    { field: 'name', direction: 'asc' },
    { page: 1, pageSize: 3 }
  );
  console.log(`第1页 (共${Math.ceil(sortedComponents.total / 3)}页):`);
  sortedComponents.components.forEach(c => {
    console.log(`  - ${c.name} (${c.code})`);
  });
}

/**
 * 示例3: 批量操作
 */
export async function example3_BatchOperations() {
  console.log('\n=== 示例3: 批量操作 ===');

  // 创建一些测试组件
  const testComponents = await Promise.all([
    componentService.createComponent({
      name: '批量测试组件1',
      code: 'BATCH_001',
      componentType: 'frame',
      status: 'draft',
      tags: ['测试']
    }),
    componentService.createComponent({
      name: '批量测试组件2',
      code: 'BATCH_002',
      componentType: 'glass',
      status: 'draft',
      tags: ['测试']
    }),
    componentService.createComponent({
      name: '批量测试组件3',
      code: 'BATCH_003',
      componentType: 'hardware',
      status: 'draft',
      tags: ['测试']
    })
  ]);

  const componentIds = testComponents.map(c => c.id);
  console.log(`创建了 ${componentIds.length} 个测试组件`);

  // 1. 批量更新状态
  console.log('\n1. 批量更新状态为活跃:');
  const statusResult = await componentService.batchOperation({
    type: 'updateStatus',
    componentIds,
    params: { status: 'active' }
  });
  console.log(`成功更新: ${statusResult.success} 个，失败: ${statusResult.failed} 个`);

  // 2. 批量添加标签
  console.log('\n2. 批量添加标签:');
  const addTagsResult = await componentService.batchOperation({
    type: 'addTags',
    componentIds,
    params: { tags: ['批量操作', '已处理'] }
  });
  console.log(`成功添加标签: ${addTagsResult.success} 个，失败: ${addTagsResult.failed} 个`);

  // 验证标签已添加
  const updatedComponent = await componentService.getComponentById(componentIds[0]);
  console.log(`组件标签: ${updatedComponent?.tags.join(', ')}`);

  // 3. 批量删除
  console.log('\n3. 批量删除组件:');
  const deleteResult = await componentService.batchOperation({
    type: 'delete',
    componentIds
  });
  console.log(`成功删除: ${deleteResult.success} 个，失败: ${deleteResult.failed} 个`);

  // 验证组件已删除
  const deletedComponent = await componentService.getComponentById(componentIds[0]);
  console.log(`组件是否已删除: ${deletedComponent === null ? '是' : '否'}`);
}

/**
 * 示例4: 组件统计和复制
 */
export async function example4_StatisticsAndDuplication() {
  console.log('\n=== 示例4: 组件统计和复制 ===');

  // 1. 获取统计信息
  console.log('\n1. 组件统计信息:');
  const stats = await componentService.getStatistics();
  console.log(`总组件数: ${stats.total}`);
  console.log('按类型统计:');
  Object.entries(stats.byType).forEach(([type, count]) => {
    if (count > 0) {
      console.log(`  ${type}: ${count} 个`);
    }
  });
  console.log('按状态统计:');
  Object.entries(stats.byStatus).forEach(([status, count]) => {
    if (count > 0) {
      console.log(`  ${status}: ${count} 个`);
    }
  });
  console.log(`最近创建: ${stats.recentlyCreated} 个`);
  console.log(`最近更新: ${stats.recentlyUpdated} 个`);

  // 2. 组件复制
  console.log('\n2. 组件复制功能:');
  
  // 先创建一个原始组件
  const originalComponent = await componentService.createComponent({
    name: '原始窗框组件',
    code: 'ORIG_FRAME_001',
    description: '这是一个原始的窗框组件',
    componentType: 'frame',
    materialCategoryId: 'aluminum-frame',
    parameters: [
      {
        id: 'param-width',
        name: 'width',
        displayName: '宽度',
        type: 'number',
        defaultValue: 1000,
        required: true,
        visible: true,
        editable: true,
        category: 'dimension'
      }
    ],
    constraints: [
      {
        id: 'constraint-min-width',
        name: '最小宽度约束',
        type: 'dimension',
        expression: 'width >= 500',
        errorMessage: '宽度不能小于500mm',
        severity: 'error',
        enabled: true
      }
    ],
    tags: ['原始', '窗框', '铝合金'],
    reusable: true,
    status: 'active'
  });

  console.log(`原始组件: ${originalComponent.name} (${originalComponent.code})`);

  // 复制组件
  const duplicatedComponent = await componentService.duplicateComponent(
    originalComponent.id,
    '复制的窗框组件'
  );

  console.log(`复制组件: ${duplicatedComponent.name} (${duplicatedComponent.code})`);
  console.log(`复制组件状态: ${duplicatedComponent.status}`);
  console.log(`参数数量: ${duplicatedComponent.parameters.length}`);
  console.log(`约束数量: ${duplicatedComponent.constraints.length}`);
  console.log(`标签: ${duplicatedComponent.tags.join(', ')}`);
}

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  try {
    await example1_CreateCompleteComponent();
    await example2_QueryAndFilterComponents();
    await example3_BatchOperations();
    await example4_StatisticsAndDuplication();
    
    console.log('\n=== 所有示例运行完成 ===');
  } catch (error) {
    console.error('运行示例时发生错误:', error);
  }
}

// 如果直接运行此文件，则执行所有示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples();
}
