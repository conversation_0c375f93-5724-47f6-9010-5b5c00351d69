import { describe, it, expect, beforeEach } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useProductStore } from '@/stores/productStore';
import { useComponentStore } from '@/stores/productComponentStore';
import { useAssemblyStore } from '@/stores/productAssemblyStore';
import { useProductStructureStore } from '@/stores/productStructureStore';
import { useBOMStore } from '@/stores/productBOMStore';

describe('Product Stores', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  describe('useProductStore', () => {
    it('should initialize with empty state', () => {
      const store = useProductStore();
      
      expect(store.products).toEqual([]);
      expect(store.currentProduct).toBeNull();
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('should have correct computed properties', () => {
      const store = useProductStore();
      
      expect(store.filteredProducts).toEqual([]);
    });
  });

  describe('useComponentStore', () => {
    it('should initialize with empty state', () => {
      const store = useComponentStore();
      
      expect(store.components).toEqual([]);
      expect(store.currentComponent).toBeNull();
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('should have correct computed properties', () => {
      const store = useComponentStore();
      
      expect(store.filteredComponents).toEqual([]);
      expect(store.componentsByType).toEqual({});
    });
  });

  describe('useAssemblyStore', () => {
    it('should initialize with empty state', () => {
      const store = useAssemblyStore();
      
      expect(store.assemblies).toEqual([]);
      expect(store.currentAssembly).toBeNull();
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('should have correct computed properties', () => {
      const store = useAssemblyStore();
      
      expect(store.filteredAssemblies).toEqual([]);
    });
  });

  describe('useProductStructureStore', () => {
    it('should initialize with empty state', () => {
      const store = useProductStructureStore();
      
      expect(store.structures).toEqual([]);
      expect(store.currentStructure).toBeNull();
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('should have correct computed properties', () => {
      const store = useProductStructureStore();
      
      expect(store.filteredStructures).toEqual([]);
    });
  });

  describe('useBOMStore', () => {
    it('should initialize with empty state', () => {
      const store = useBOMStore();
      
      expect(store.quoteBOMs).toEqual([]);
      expect(store.productionBOMs).toEqual([]);
      expect(store.currentQuoteBOM).toBeNull();
      expect(store.currentProductionBOM).toBeNull();
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });
  });

  describe('Store independence', () => {
    it('should have independent state between stores', () => {
      const productStore = useProductStore();
      const componentStore = useComponentStore();
      const assemblyStore = useAssemblyStore();
      const structureStore = useProductStructureStore();
      const bomStore = useBOMStore();

      // 设置一个Store的loading状态
      productStore.loading = true;

      // 其他Store应该保持独立
      expect(componentStore.loading).toBe(false);
      expect(assemblyStore.loading).toBe(false);
      expect(structureStore.loading).toBe(false);
      expect(bomStore.loading).toBe(false);
    });
  });

  describe('Store methods', () => {
    it('should have all required methods', () => {
      const productStore = useProductStore();
      const componentStore = useComponentStore();
      const assemblyStore = useAssemblyStore();
      const structureStore = useProductStructureStore();
      const bomStore = useBOMStore();

      // 检查产品Store方法
      expect(typeof productStore.loadProducts).toBe('function');
      expect(typeof productStore.loadProductById).toBe('function');
      expect(typeof productStore.createProduct).toBe('function');

      // 检查组件Store方法
      expect(typeof componentStore.loadComponents).toBe('function');
      expect(typeof componentStore.loadComponentById).toBe('function');
      expect(typeof componentStore.createComponent).toBe('function');
      expect(typeof componentStore.updateComponent).toBe('function');
      expect(typeof componentStore.deleteComponent).toBe('function');

      // 检查构件Store方法
      expect(typeof assemblyStore.loadAssemblies).toBe('function');
      expect(typeof assemblyStore.loadAssemblyById).toBe('function');
      expect(typeof assemblyStore.createAssembly).toBe('function');
      expect(typeof assemblyStore.updateAssembly).toBe('function');
      expect(typeof assemblyStore.deleteAssembly).toBe('function');

      // 检查产品结构Store方法
      expect(typeof structureStore.loadStructures).toBe('function');
      expect(typeof structureStore.loadStructureById).toBe('function');
      expect(typeof structureStore.createStructure).toBe('function');
      expect(typeof structureStore.updateStructure).toBe('function');
      expect(typeof structureStore.deleteStructure).toBe('function');

      // 检查BOM Store方法
      expect(typeof bomStore.loadQuoteBOMs).toBe('function');
      expect(typeof bomStore.loadProductionBOMs).toBe('function');
      expect(typeof bomStore.loadQuoteBOMById).toBe('function');
      expect(typeof bomStore.loadProductionBOMById).toBe('function');
      expect(typeof bomStore.convertToProductionBOM).toBe('function');
    });
  });
});
