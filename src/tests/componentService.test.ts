/**
 * 组件管理服务测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { ComponentService } from '@/services/componentService';
import type { Component } from '@/types/product-structure';

describe('ComponentService', () => {
  let service: ComponentService;

  beforeEach(() => {
    service = new ComponentService();
  });

  describe('组件CRUD操作', () => {
    it('应该能够创建组件', async () => {
      const componentData = {
        name: '测试组件',
        code: 'TEST001',
        description: '这是一个测试组件',
        componentType: 'frame' as const,
        materialCategoryId: 'aluminum-frame',
        materialCategoryName: '铝合金框料',
        materialCategoryCode: 'ALF001',
        parameters: [],
        constraints: [],
        processRequirements: [],
        quantityFormula: '1',
        tags: ['测试'],
        reusable: true
      };

      const result = await service.createComponent(componentData);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.name).toBe('测试组件');
      expect(result.code).toBe('TEST001');
      expect(result.componentType).toBe('frame');
      expect(result.status).toBe('draft');
    });

    it('应该能够根据ID获取组件', async () => {
      const componentData = {
        name: '测试组件2',
        code: 'TEST002',
        componentType: 'glass' as const
      };

      const created = await service.createComponent(componentData);
      const retrieved = await service.getComponentById(created.id);

      expect(retrieved).toBeDefined();
      expect(retrieved!.id).toBe(created.id);
      expect(retrieved!.name).toBe('测试组件2');
    });

    it('应该能够更新组件', async () => {
      const componentData = {
        name: '原始组件',
        code: 'ORIG001',
        componentType: 'hardware' as const
      };

      const created = await service.createComponent(componentData);
      const updated = await service.updateComponent(created.id, {
        name: '更新后的组件',
        description: '更新后的描述'
      });

      expect(updated.name).toBe('更新后的组件');
      expect(updated.description).toBe('更新后的描述');
      expect(updated.code).toBe('ORIG001'); // 编码不应该改变
    });

    it('应该能够删除组件', async () => {
      const componentData = {
        name: '待删除组件',
        code: 'DEL001',
        componentType: 'seal' as const
      };

      const created = await service.createComponent(componentData);
      await service.deleteComponent(created.id);

      const retrieved = await service.getComponentById(created.id);
      expect(retrieved).toBeNull();
    });
  });

  describe('组件查询和筛选', () => {
    beforeEach(async () => {
      // 创建测试数据
      await service.createComponent({
        name: '铝合金窗框',
        code: 'ALU001',
        componentType: 'frame',
        materialCategoryId: 'aluminum-frame',
        status: 'active',
        tags: ['窗户', '铝合金'],
        reusable: true
      });

      await service.createComponent({
        name: '钢化玻璃',
        code: 'GLS001',
        componentType: 'glass',
        materialCategoryId: 'tempered-glass',
        status: 'active',
        tags: ['玻璃', '安全'],
        reusable: true
      });

      await service.createComponent({
        name: '门锁五金',
        code: 'HW001',
        componentType: 'hardware',
        materialCategoryId: 'door-hardware',
        status: 'draft',
        tags: ['五金', '门锁'],
        reusable: false
      });
    });

    it('应该能够获取所有组件', async () => {
      const result = await service.getComponents();

      expect(result.components.length).toBeGreaterThanOrEqual(3);
      expect(result.total).toBeGreaterThanOrEqual(3);
    });

    it('应该能够按组件类型筛选', async () => {
      const result = await service.getComponents({
        componentType: ['frame']
      });

      expect(result.components.length).toBeGreaterThanOrEqual(1);
      result.components.forEach(component => {
        expect(component.componentType).toBe('frame');
      });
    });

    it('应该能够按状态筛选', async () => {
      const result = await service.getComponents({
        status: ['active']
      });

      expect(result.components.length).toBeGreaterThanOrEqual(2);
      result.components.forEach(component => {
        expect(component.status).toBe('active');
      });
    });

    it('应该能够按关键词搜索', async () => {
      const result = await service.getComponents({
        search: '铝合金'
      });

      expect(result.components.length).toBeGreaterThanOrEqual(1);
      const found = result.components.some(c => 
        c.name.includes('铝合金') || c.tags.includes('铝合金')
      );
      expect(found).toBe(true);
    });

    it('应该能够按标签筛选', async () => {
      const result = await service.getComponents({
        tags: ['玻璃']
      });

      expect(result.components.length).toBeGreaterThanOrEqual(1);
      result.components.forEach(component => {
        expect(component.tags).toContain('玻璃');
      });
    });

    it('应该能够按可重用性筛选', async () => {
      const result = await service.getComponents({
        reusable: true
      });

      expect(result.components.length).toBeGreaterThanOrEqual(2);
      result.components.forEach(component => {
        expect(component.reusable).toBe(true);
      });
    });

    it('应该能够排序', async () => {
      const result = await service.getComponents(
        {},
        { field: 'name', direction: 'asc' }
      );

      expect(result.components.length).toBeGreaterThan(1);
      
      // 检查是否按名称升序排列
      for (let i = 1; i < result.components.length; i++) {
        const current = result.components[i].name.toLowerCase();
        const previous = result.components[i - 1].name.toLowerCase();
        expect(current >= previous).toBe(true);
      }
    });

    it('应该支持分页', async () => {
      const page1 = await service.getComponents(
        {},
        undefined,
        { page: 1, pageSize: 2 }
      );

      expect(page1.components.length).toBeLessThanOrEqual(2);
      expect(page1.page).toBe(1);
      expect(page1.pageSize).toBe(2);

      if (page1.total > 2) {
        const page2 = await service.getComponents(
          {},
          undefined,
          { page: 2, pageSize: 2 }
        );

        expect(page2.components.length).toBeGreaterThan(0);
        expect(page2.page).toBe(2);
        
        // 确保两页的组件不重复
        const page1Ids = page1.components.map(c => c.id);
        const page2Ids = page2.components.map(c => c.id);
        const intersection = page1Ids.filter(id => page2Ids.includes(id));
        expect(intersection.length).toBe(0);
      }
    });
  });

  describe('批量操作', () => {
    let componentIds: string[];

    beforeEach(async () => {
      // 创建测试组件
      const components = await Promise.all([
        service.createComponent({
          name: '批量测试1',
          code: 'BATCH001',
          componentType: 'frame',
          status: 'draft',
          tags: ['批量测试']
        }),
        service.createComponent({
          name: '批量测试2',
          code: 'BATCH002',
          componentType: 'glass',
          status: 'draft',
          tags: ['批量测试']
        }),
        service.createComponent({
          name: '批量测试3',
          code: 'BATCH003',
          componentType: 'hardware',
          status: 'draft',
          tags: ['批量测试']
        })
      ]);

      componentIds = components.map(c => c.id);
    });

    it('应该能够批量更新状态', async () => {
      const result = await service.batchOperation({
        type: 'updateStatus',
        componentIds,
        params: { status: 'active' }
      });

      expect(result.success).toBe(3);
      expect(result.failed).toBe(0);

      // 验证状态已更新
      for (const id of componentIds) {
        const component = await service.getComponentById(id);
        expect(component!.status).toBe('active');
      }
    });

    it('应该能够批量添加标签', async () => {
      const result = await service.batchOperation({
        type: 'addTags',
        componentIds,
        params: { tags: ['新标签', '批量添加'] }
      });

      expect(result.success).toBe(3);
      expect(result.failed).toBe(0);

      // 验证标签已添加
      for (const id of componentIds) {
        const component = await service.getComponentById(id);
        expect(component!.tags).toContain('新标签');
        expect(component!.tags).toContain('批量添加');
      }
    });

    it('应该能够批量删除', async () => {
      const result = await service.batchOperation({
        type: 'delete',
        componentIds
      });

      expect(result.success).toBe(3);
      expect(result.failed).toBe(0);

      // 验证组件已删除
      for (const id of componentIds) {
        const component = await service.getComponentById(id);
        expect(component).toBeNull();
      }
    });
  });

  describe('统计功能', () => {
    beforeEach(async () => {
      // 创建不同类型和状态的组件
      await Promise.all([
        service.createComponent({
          name: '统计测试1',
          componentType: 'frame',
          status: 'active'
        }),
        service.createComponent({
          name: '统计测试2',
          componentType: 'frame',
          status: 'draft'
        }),
        service.createComponent({
          name: '统计测试3',
          componentType: 'glass',
          status: 'active'
        }),
        service.createComponent({
          name: '统计测试4',
          componentType: 'hardware',
          status: 'deprecated'
        })
      ]);
    });

    it('应该能够获取统计信息', async () => {
      const stats = await service.getStatistics();

      expect(stats.total).toBeGreaterThanOrEqual(4);
      expect(stats.byType.frame).toBeGreaterThanOrEqual(2);
      expect(stats.byType.glass).toBeGreaterThanOrEqual(1);
      expect(stats.byType.hardware).toBeGreaterThanOrEqual(1);
      expect(stats.byStatus.active).toBeGreaterThanOrEqual(2);
      expect(stats.byStatus.draft).toBeGreaterThanOrEqual(1);
      expect(stats.byStatus.deprecated).toBeGreaterThanOrEqual(1);
    });
  });

  describe('组件复制', () => {
    it('应该能够复制组件', async () => {
      const original = await service.createComponent({
        name: '原始组件',
        code: 'ORIG001',
        description: '原始描述',
        componentType: 'frame',
        tags: ['原始', '测试'],
        parameters: [
          {
            id: 'param1',
            name: 'width',
            displayName: '宽度',
            type: 'number',
            defaultValue: 1000,
            required: true,
            visible: true,
            editable: true,
            category: 'dimension'
          }
        ]
      });

      const duplicated = await service.duplicateComponent(original.id, '复制的组件');

      expect(duplicated.id).not.toBe(original.id);
      expect(duplicated.name).toBe('复制的组件');
      expect(duplicated.code).toBe('ORIG001_COPY');
      expect(duplicated.status).toBe('draft');
      expect(duplicated.description).toBe(original.description);
      expect(duplicated.componentType).toBe(original.componentType);
      expect(duplicated.tags).toEqual(original.tags);
      expect(duplicated.parameters).toHaveLength(1);
      expect(duplicated.parameters[0].name).toBe('width');
    });
  });
});
