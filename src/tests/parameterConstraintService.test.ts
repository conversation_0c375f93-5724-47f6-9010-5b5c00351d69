/**
 * 参数化设计和约束管理服务测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { ParameterConstraintService } from '@/services/parameterConstraintService';
import type { Component, ComponentParameter, ComponentConstraint } from '@/types/product-structure';

describe('ParameterConstraintService', () => {
  let service: ParameterConstraintService;
  let mockComponent: Component;

  beforeEach(() => {
    service = new ParameterConstraintService();
    
    // 创建模拟组件
    mockComponent = {
      id: 'test-component-1',
      code: 'TC001',
      name: '测试组件',
      description: '用于测试的组件',
      version: 1,
      status: 'active',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      createdBy: 'test-user',
      updatedBy: 'test-user',
      componentType: 'frame',
      materialCategoryId: 'cat-001',
      materialCategoryName: '铝合金',
      materialCategoryCode: 'AL001',
      parameters: [
        {
          id: 'param-width',
          name: 'width',
          displayName: '宽度',
          type: 'number',
          unit: 'mm',
          defaultValue: 1000,
          minValue: 500,
          maxValue: 3000,
          required: true,
          description: '组件宽度',
          category: 'dimension',
          visible: true,
          editable: true,
          validationRules: ['value > 0', 'value <= 3000']
        },
        {
          id: 'param-height',
          name: 'height',
          displayName: '高度',
          type: 'number',
          unit: 'mm',
          // 移除默认值以测试必填验证
          minValue: 800,
          maxValue: 2500,
          required: true,
          description: '组件高度',
          category: 'dimension',
          visible: true,
          editable: true,
          validationRules: ['value > 0', 'value <= 2500']
        },
        {
          id: 'param-material',
          name: 'material',
          displayName: '材料类型',
          type: 'select',
          options: [
            { value: 'aluminum', label: '铝合金' },
            { value: 'steel', label: '钢材' },
            { value: 'plastic', label: '塑料' }
          ],
          defaultValue: 'aluminum',
          required: true,
          description: '组件材料',
          category: 'material',
          visible: true,
          editable: true
        }
      ],
      constraints: [
        {
          id: 'constraint-aspect-ratio',
          name: '宽高比约束',
          type: 'dimension',
          expression: 'width / height >= 0.5 && width / height <= 3.0',
          errorMessage: '宽高比必须在0.5到3.0之间',
          severity: 'error',
          enabled: true,
          description: '确保组件宽高比合理'
        },
        {
          id: 'constraint-min-area',
          name: '最小面积约束',
          type: 'dimension',
          expression: 'width * height >= 500000',
          errorMessage: '组件面积不能小于0.5平方米',
          severity: 'warning',
          enabled: true,
          description: '确保组件面积足够'
        }
      ],
      quantityFormula: '1',
      costFormula: 'width * height * 0.001',
      processRequirements: [],
      properties: {},
      tags: ['test'],
      reusable: true
    };
  });

  describe('参数验证', () => {
    it('应该验证有效的参数值', async () => {
      const parameterValues = {
        width: 1200,
        height: 1800,
        material: 'aluminum'
      };

      const result = await service.validateComponent(mockComponent, parameterValues);

      expect(result.parameterValidation.isValid).toBe(true);
      expect(result.parameterValidation.errors).toHaveLength(0);
    });

    it('应该检测缺失的必填参数', async () => {
      const parameterValues = {
        width: 1200
        // 缺少 height 和 material
      };

      const result = await service.validateComponent(mockComponent, parameterValues);

      expect(result.parameterValidation.isValid).toBe(false);
      expect(result.parameterValidation.errors.length).toBeGreaterThan(0);
      
      const missingHeightError = result.parameterValidation.errors.find(
        e => e.message.includes('高度')
      );
      expect(missingHeightError).toBeDefined();
    });

    it('应该检测超出范围的数值参数', async () => {
      const parameterValues = {
        width: 4000, // 超过最大值 3000
        height: 1800,
        material: 'aluminum'
      };

      const result = await service.validateComponent(mockComponent, parameterValues);

      expect(result.parameterValidation.isValid).toBe(false);
      
      const rangeError = result.parameterValidation.errors.find(
        e => e.type === 'range_violation'
      );
      expect(rangeError).toBeDefined();
    });

    it('应该检测无效的选择参数', async () => {
      const parameterValues = {
        width: 1200,
        height: 1800,
        material: 'invalid_material'
      };

      const result = await service.validateComponent(mockComponent, parameterValues);

      expect(result.parameterValidation.isValid).toBe(false);
      
      const optionError = result.parameterValidation.errors.find(
        e => e.type === 'invalid_option'
      );
      expect(optionError).toBeDefined();
    });
  });

  describe('约束验证', () => {
    it('应该验证满足约束的参数值', async () => {
      const parameterValues = {
        width: 1200,
        height: 1800,
        material: 'aluminum'
      };

      const result = await service.validateComponent(mockComponent, parameterValues);

      expect(result.constraintValidation.isValid).toBe(true);
      expect(result.constraintValidation.errors).toHaveLength(0);
    });

    it('应该检测违反约束的参数值', async () => {
      const parameterValues = {
        width: 500,  // 宽高比 = 0.33，小于最小值 0.5
        height: 1500,
        material: 'aluminum'
      };

      const result = await service.validateComponent(mockComponent, parameterValues);

      expect(result.constraintValidation.isValid).toBe(false);
      
      const constraintError = result.constraintValidation.errors.find(
        e => e.type === 'constraint_violation'
      );
      expect(constraintError).toBeDefined();
    });

    it('应该处理警告级别的约束', async () => {
      const parameterValues = {
        width: 600,   // 面积 = 480000，小于最小面积 500000
        height: 800,
        material: 'aluminum'
      };

      const result = await service.validateComponent(mockComponent, parameterValues);

      // 参数验证应该通过
      expect(result.parameterValidation.isValid).toBe(true);
      
      // 应该有警告
      expect(result.constraintValidation.warnings.length).toBeGreaterThan(0);
      
      const warningConstraint = result.constraintValidation.warnings.find(
        w => w.message.includes('面积')
      );
      expect(warningConstraint).toBeDefined();
    });
  });

  describe('约束求解', () => {
    it('应该提供修复建议', async () => {
      const parameterValues = {
        width: 500,  // 违反宽高比约束
        height: 1500,
        material: 'aluminum'
      };

      const result = await service.validateComponent(mockComponent, parameterValues);

      expect(result.fixSuggestions.length).toBeGreaterThan(0);
      
      const parameterSuggestion = result.fixSuggestions.find(
        s => s.type === 'parameter_change'
      );
      expect(parameterSuggestion).toBeDefined();
    });

    it('应该检测约束冲突', async () => {
      // 添加一个冲突的约束
      const conflictingComponent = {
        ...mockComponent,
        constraints: [
          ...mockComponent.constraints,
          {
            id: 'constraint-conflict',
            name: '冲突约束',
            type: 'dimension' as const,
            expression: 'width < 800', // 与现有约束冲突
            errorMessage: '宽度必须小于800',
            severity: 'error' as const,
            enabled: true,
            description: '测试冲突约束'
          }
        ]
      };

      const parameterValues = {
        width: 1200,
        height: 1800,
        material: 'aluminum'
      };

      const result = await service.validateComponent(conflictingComponent, parameterValues);

      expect(result.constraintSolverResult.conflicts.length).toBeGreaterThan(0);
    });
  });

  describe('参数建议生成', () => {
    it('应该为缺失参数生成建议', async () => {
      const suggestions = await service.generateParameterSuggestions(
        mockComponent.parameters,
        mockComponent.constraints,
        { material: 'aluminum' } // 只提供部分参数
      );

      expect(suggestions.parameterSuggestions.width).toBeDefined();
      expect(suggestions.parameterSuggestions.height).toBeDefined();
      expect(suggestions.reasoning.length).toBeGreaterThan(0);
    });

    it('应该基于约束优化参数', async () => {
      const currentValues = {
        width: 500,  // 不满足约束
        height: 1500,
        material: 'aluminum'
      };

      const suggestions = await service.generateParameterSuggestions(
        mockComponent.parameters,
        mockComponent.constraints,
        currentValues
      );

      // 应该建议调整宽度以满足宽高比约束
      expect(suggestions.parameterSuggestions.width).toBeGreaterThan(500);
    });
  });

  describe('参数优化', () => {
    it('应该优化参数以达到目标', async () => {
      const currentValues = {
        width: 1000,
        height: 1500,
        material: 'aluminum'
      };

      const objectives = [
        { parameter: 'width', target: 'maximize' as const }
      ];

      const result = await service.optimizeParameterConfiguration(
        mockComponent.parameters,
        mockComponent.constraints,
        currentValues,
        objectives
      );

      expect(result.optimizedValues.width).toBeGreaterThan(currentValues.width);
      expect(result.improvementScore).toBeGreaterThan(0);
      expect(result.optimizationSteps.length).toBeGreaterThan(0);
    });
  });

  describe('依赖关系检测', () => {
    it('应该检测参数依赖关系', async () => {
      const dependencies = await service.detectParameterDependencies(
        mockComponent.parameters,
        mockComponent.constraints
      );

      // 宽度和高度应该有依赖关系（因为约束中同时使用了它们）
      expect(dependencies.width).toBeDefined();
      expect(dependencies.height).toBeDefined();
    });
  });
});
