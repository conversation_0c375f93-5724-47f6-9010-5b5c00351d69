import { describe, it, expect, beforeEach } from 'vitest';
import {
  componentService,
  assemblyService,
  productStructureService,
  productService,
  quoteBOMService,
  productionBOMService
} from '@/services/productService';
import type { Component, Assembly, ProductStructure, Product } from '@/types/product';

// Mock fetch for testing
global.fetch = vi.fn();

describe('ProductService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ComponentService', () => {
    it('should load components successfully', async () => {
      const mockComponents = {
        components: [
          {
            id: 'comp_001',
            code: 'FRAME_OUTER_VERTICAL',
            name: '外框立柱',
            componentType: 'frame',
            status: 'active'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const components = await componentService.getComponents();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/components.json');
      expect(components).toHaveLength(1);
      expect(components[0].name).toBe('外框立柱');
    });

    it('should filter components by type', async () => {
      const mockComponents = {
        components: [
          {
            id: 'comp_001',
            code: 'FRAME_OUTER_VERTICAL',
            name: '外框立柱',
            componentType: 'frame',
            status: 'active'
          },
          {
            id: 'comp_002',
            code: 'GLASS_PANEL_FIRE',
            name: '防火玻璃面板',
            componentType: 'glass',
            status: 'active'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const components = await componentService.getComponents({
        componentType: 'frame'
      });
      
      expect(components).toHaveLength(1);
      expect(components[0].componentType).toBe('frame');
    });

    it('should get component by id', async () => {
      const mockComponents = {
        components: [
          {
            id: 'comp_001',
            code: 'FRAME_OUTER_VERTICAL',
            name: '外框立柱',
            componentType: 'frame',
            status: 'active'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const component = await componentService.getComponentById('comp_001');
      
      expect(component).toBeTruthy();
      expect(component?.id).toBe('comp_001');
      expect(component?.name).toBe('外框立柱');
    });

    it('should return null for non-existent component', async () => {
      const mockComponents = {
        components: []
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const component = await componentService.getComponentById('non_existent');
      
      expect(component).toBeNull();
    });

    it('should create new component', async () => {
      const newComponentData = {
        code: 'TEST_COMPONENT',
        name: '测试组件',
        componentType: 'other' as const,
        materialCategoryId: 'cat_test',
        materialCategoryName: '测试材料',
        materialCategoryCode: 'TEST_MAT'
      };

      const createdComponent = await componentService.createComponent(newComponentData);
      
      expect(createdComponent.id).toMatch(/^comp_\d+$/);
      expect(createdComponent.code).toBe('TEST_COMPONENT');
      expect(createdComponent.name).toBe('测试组件');
      expect(createdComponent.status).toBe('draft');
      expect(createdComponent.version).toBe(1);
    });

    it('should validate parameters correctly', async () => {
      const mockComponents = {
        components: [
          {
            id: 'comp_001',
            constraints: [
              {
                id: 'const_001',
                name: '尺寸约束',
                expression: 'width >= 60 && width <= 120',
                errorMessage: '宽度必须在60-120mm之间',
                severity: 'error'
              }
            ]
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const results = await componentService.validateParameters('comp_001', {
        width: 50 // 违反约束
      });
      
      expect(results).toHaveLength(1);
      expect(results[0].type).toBe('error');
      expect(results[0].message).toBe('宽度必须在60-120mm之间');
    });

    it('should calculate quantity using formula', async () => {
      const mockComponents = {
        components: [
          {
            id: 'comp_001',
            quantityFormula: 'ceiling(window_height / 6000) * 2'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const quantity = await componentService.calculateQuantity('comp_001', {
        window_height: 1800
      });
      
      expect(quantity).toBe(2); // ceiling(1800/6000) * 2 = 1 * 2 = 2
    });
  });

  describe('AssemblyService', () => {
    it('should load assemblies successfully', async () => {
      const mockAssemblies = {
        assemblies: [
          {
            id: 'asm_001',
            code: 'FRAME_MAIN_ASSEMBLY',
            name: '主框架构件',
            assemblyType: 'frame_assembly',
            status: 'active'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAssemblies
      });

      const assemblies = await assemblyService.getAssemblies();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/assemblies.json');
      expect(assemblies).toHaveLength(1);
      expect(assemblies[0].name).toBe('主框架构件');
    });

    it('should create new assembly', async () => {
      const newAssemblyData = {
        code: 'TEST_ASSEMBLY',
        name: '测试构件',
        assemblyType: 'complete_assembly' as const
      };

      const createdAssembly = await assemblyService.createAssembly(newAssemblyData);
      
      expect(createdAssembly.id).toMatch(/^asm_\d+$/);
      expect(createdAssembly.code).toBe('TEST_ASSEMBLY');
      expect(createdAssembly.name).toBe('测试构件');
      expect(createdAssembly.status).toBe('draft');
    });
  });

  describe('ProductStructureService', () => {
    it('should load product structures successfully', async () => {
      const mockStructures = {
        productStructures: [
          {
            id: 'struct_001',
            code: 'FIRE_WINDOW_STD',
            name: '标准防火窗结构',
            productType: 'window',
            category: '防火窗',
            status: 'active'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockStructures
      });

      const structures = await productStructureService.getStructures();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/product-structures.json');
      expect(structures).toHaveLength(1);
      expect(structures[0].name).toBe('标准防火窗结构');
    });

    it('should filter structures by category', async () => {
      const mockStructures = {
        productStructures: [
          {
            id: 'struct_001',
            code: 'FIRE_WINDOW_STD',
            name: '标准防火窗结构',
            productType: 'window',
            category: '防火窗',
            status: 'active'
          },
          {
            id: 'struct_002',
            code: 'FIRE_PARTITION_STD',
            name: '标准防火隔断结构',
            productType: 'partition',
            category: '防火隔断',
            status: 'active'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockStructures
      });

      const structures = await productStructureService.getStructures({
        category: '防火窗'
      });
      
      expect(structures).toHaveLength(1);
      expect(structures[0].category).toBe('防火窗');
    });
  });

  describe('ProductService', () => {
    it('should load products successfully', async () => {
      const mockProducts = {
        products: [
          {
            id: 'prod_001',
            code: 'FIRE_WIN_STD_001',
            name: '标准防火窗-A级60分钟',
            category: '防火窗',
            lifecycle: 'mass_production',
            status: 'active'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockProducts
      });

      const products = await productService.getProducts();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/products.json');
      expect(products).toHaveLength(1);
      expect(products[0].name).toBe('标准防火窗-A级60分钟');
    });

    it('should create new product', async () => {
      const newProductData = {
        code: 'TEST_PRODUCT',
        name: '测试产品',
        productStructureId: 'struct_001',
        productStructureCode: 'TEST_STRUCTURE',
        productStructureName: '测试结构',
        category: '测试类别'
      };

      const createdProduct = await productService.createProduct(newProductData);
      
      expect(createdProduct.id).toMatch(/^prod_\d+$/);
      expect(createdProduct.code).toBe('TEST_PRODUCT');
      expect(createdProduct.name).toBe('测试产品');
      expect(createdProduct.lifecycle).toBe('design');
      expect(createdProduct.status).toBe('draft');
    });
  });

  describe('QuoteBOMService', () => {
    it('should load quote BOMs successfully', async () => {
      const mockQuoteBOMs = {
        quoteBOMs: [
          {
            id: 'qbom_001',
            code: 'QB-20240101-001',
            name: '标准防火窗-报价BOM',
            productId: 'prod_001',
            status: 'confirmed'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockQuoteBOMs
      });

      const quoteBOMs = await quoteBOMService.getQuoteBOMs();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/quote-boms.json');
      expect(quoteBOMs).toHaveLength(1);
      expect(quoteBOMs[0].name).toBe('标准防火窗-报价BOM');
    });
  });

  describe('ProductionBOMService', () => {
    it('should load production BOMs successfully', async () => {
      const mockProductionBOMs = {
        productionBOMs: [
          {
            id: 'pbom_001',
            code: 'PB-20240105-001',
            name: '标准防火窗-生产BOM',
            productId: 'prod_001',
            status: 'approved'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockProductionBOMs
      });

      const productionBOMs = await productionBOMService.getProductionBOMs();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/production-boms.json');
      expect(productionBOMs).toHaveLength(1);
      expect(productionBOMs[0].name).toBe('标准防火窗-生产BOM');
    });
  });
});
