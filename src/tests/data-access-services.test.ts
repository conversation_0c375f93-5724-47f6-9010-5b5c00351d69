/**
 * 数据访问服务测试
 * 
 * 测试任务2.2实现的数据访问服务功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  componentService,
  assemblyService,
  productStructureService,
  validationService
} from '@/services/productService';

// Mock fetch
global.fetch = vi.fn();

describe('数据访问服务测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ComponentService', () => {
    it('应该能够获取组件列表', async () => {
      const mockComponents = {
        components: [
          {
            id: 'comp_001',
            code: 'FRAME_001',
            name: '铝合金框架',
            componentType: 'frame',
            materialCategoryId: 'mat_001',
            materialCategoryName: '铝合金',
            materialCategoryCode: 'AL',
            parameters: [],
            quantityFormula: '1',
            constraints: [],
            processRequirements: [],
            properties: {},
            tags: ['框架', '铝合金'],
            reusable: true,
            status: 'active',
            version: 1,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            createdBy: 'test_user',
            updatedBy: 'test_user'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const components = await componentService.getComponents();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/components.json');
      expect(components).toHaveLength(1);
      expect(components[0].name).toBe('铝合金框架');
    });

    it('应该能够使用过滤器筛选组件', async () => {
      const mockComponents = {
        components: [
          {
            id: 'comp_001',
            code: 'FRAME_001',
            name: '铝合金框架',
            description: '铝合金框架组件',
            componentType: 'frame',
            materialCategoryId: 'mat_001',
            materialCategoryName: '铝合金',
            materialCategoryCode: 'AL',
            quantityFormula: '1',
            status: 'active',
            tags: ['框架'],
            reusable: true,
            parameters: [],
            constraints: [],
            processRequirements: [],
            properties: {},
            version: 1,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            createdBy: 'test_user',
            updatedBy: 'test_user'
          },
          {
            id: 'comp_002',
            code: 'GLASS_001',
            name: '钢化玻璃',
            description: '钢化玻璃组件',
            componentType: 'glass',
            materialCategoryId: 'mat_002',
            materialCategoryName: '玻璃',
            materialCategoryCode: 'GL',
            quantityFormula: '1',
            status: 'active',
            tags: ['玻璃'],
            reusable: true,
            parameters: [],
            constraints: [],
            processRequirements: [],
            properties: {},
            version: 1,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            createdBy: 'test_user',
            updatedBy: 'test_user'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const components = await componentService.getComponents({
        componentType: ['frame']
      });
      
      expect(components).toHaveLength(1);
      expect(components[0].componentType).toBe('frame');
    });

    it('应该能够获取分页的组件列表', async () => {
      const mockComponents = {
        components: Array.from({ length: 25 }, (_, i) => ({
          id: `comp_${i + 1}`,
          code: `COMP_${i + 1}`,
          name: `组件 ${i + 1}`,
          description: `组件 ${i + 1} 描述`,
          componentType: 'other',
          materialCategoryId: 'mat_001',
          materialCategoryName: '通用材料',
          materialCategoryCode: 'GEN',
          quantityFormula: '1',
          status: 'active',
          tags: [],
          reusable: true,
          parameters: [],
          constraints: [],
          processRequirements: [],
          properties: {},
          version: 1,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          createdBy: 'test_user',
          updatedBy: 'test_user'
        }))
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockComponents
      });

      const result = await componentService.getComponentsPaginated(
        {},
        { page: 2, pageSize: 10 }
      );

      expect(result.items).toHaveLength(10);
      expect(result.page).toBe(2);
      expect(result.pageSize).toBe(10);
      expect(result.total).toBe(25);
      expect(result.totalPages).toBe(3);
      expect(result.hasNext).toBe(true);
      expect(result.hasPrevious).toBe(true);
    });

    it('应该能够创建新组件', async () => {
      const newComponent = await componentService.createComponent({
        code: 'NEW_COMP',
        name: '新组件',
        componentType: 'frame',
        materialCategoryId: 'mat_001',
        materialCategoryName: '铝合金',
        materialCategoryCode: 'AL'
      });

      expect(newComponent.code).toBe('NEW_COMP');
      expect(newComponent.name).toBe('新组件');
      expect(newComponent.componentType).toBe('frame');
      expect(newComponent.status).toBe('draft');
      expect(newComponent.version).toBe(1);
      expect(newComponent.reusable).toBe(true);
    });

    it('应该能够验证组件参数', async () => {
      // Mock getComponentById
      const mockComponent = {
        id: 'comp_001',
        constraints: [
          {
            id: 'constraint_001',
            name: '宽度约束',
            type: 'dimension',
            expression: 'width > 100',
            errorMessage: '宽度必须大于100mm',
            severity: 'error',
            autoFix: {
              enabled: false,
              fixExpression: '',
              fixMessage: ''
            }
          }
        ]
      };

      vi.spyOn(componentService, 'getComponentById').mockResolvedValue(mockComponent as any);

      const result = await componentService.validateParameters('comp_001', { width: 50 });
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('宽度必须大于100mm');
    });

    it('应该能够计算组件数量', async () => {
      const mockComponent = {
        id: 'comp_001',
        quantityFormula: 'width * height / 1000000'
      };

      vi.spyOn(componentService, 'getComponentById').mockResolvedValue(mockComponent as any);

      const quantity = await componentService.calculateQuantity('comp_001', {
        width: 1000,
        height: 2000
      });
      
      expect(quantity).toBe(2);
    });

    it('应该能够计算组件成本', async () => {
      const mockComponent = {
        id: 'comp_001',
        costFormula: 'width * height * 0.001'
      };

      vi.spyOn(componentService, 'getComponentById').mockResolvedValue(mockComponent as any);

      const cost = await componentService.calculateCost('comp_001', {
        width: 1000,
        height: 2000
      });
      
      expect(cost).toBe(2000);
    });
  });

  describe('AssemblyService', () => {
    it('应该能够获取构件列表', async () => {
      const mockAssemblies = {
        assemblies: [
          {
            id: 'asm_001',
            code: 'WINDOW_FRAME',
            name: '窗框构件',
            assemblyType: 'frame_assembly',
            componentInstances: [],
            subAssemblies: [],
            assemblyParameters: [],
            assemblyConstraints: [],
            assemblyProcess: {
              id: 'proc_001',
              processName: '窗框装配',
              steps: [],
              totalEstimatedTime: 60
            },
            qualityRequirements: [],
            status: 'active',
            version: 1,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            createdBy: 'test_user',
            updatedBy: 'test_user',
            properties: {}
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAssemblies
      });

      const assemblies = await assemblyService.getAssemblies();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/assemblies.json');
      expect(assemblies).toHaveLength(1);
      expect(assemblies[0].name).toBe('窗框构件');
    });

    it('应该能够创建新构件', async () => {
      const newAssembly = await assemblyService.createAssembly({
        code: 'NEW_ASSEMBLY',
        name: '新构件',
        assemblyType: 'complete_assembly'
      });

      expect(newAssembly.code).toBe('NEW_ASSEMBLY');
      expect(newAssembly.name).toBe('新构件');
      expect(newAssembly.assemblyType).toBe('complete_assembly');
      expect(newAssembly.status).toBe('draft');
      expect(newAssembly.version).toBe(1);
    });
  });

  describe('ProductStructureService', () => {
    it('应该能够获取产品结构列表', async () => {
      const mockStructures = {
        productStructures: [
          {
            id: 'struct_001',
            code: 'FIRE_WINDOW_STD',
            name: '标准防火窗结构',
            productType: 'window',
            category: '防火窗',
            subCategory: '标准型',
            rootAssembly: {
              id: 'root_asm_001',
              assemblyId: 'asm_001',
              assemblyCode: 'WINDOW_FRAME',
              assemblyName: '窗框构件',
              assemblyVersion: 1,
              instanceName: '主窗框',
              quantity: 1,
              position: { x: 0, y: 0, z: 0 },
              rotation: { x: 0, y: 0, z: 0 },
              parameterValues: {},
              optional: false,
              alternatives: [],
              properties: {}
            },
            productParameters: [],
            productConstraints: [],
            configurationOptions: [],
            versionHistory: [],
            applications: ['商业建筑', '住宅建筑'],
            tags: ['防火', '标准'],
            properties: {},
            status: 'active',
            version: 1,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            createdBy: 'test_user',
            updatedBy: 'test_user'
          }
        ]
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockStructures
      });

      const structures = await productStructureService.getStructures();
      
      expect(fetch).toHaveBeenCalledWith('/mock/product/product-structures.json');
      expect(structures).toHaveLength(1);
      expect(structures[0].name).toBe('标准防火窗结构');
    });

    it('应该能够创建新产品结构', async () => {
      const newStructure = await productStructureService.createStructure({
        code: 'NEW_STRUCTURE',
        name: '新产品结构',
        productType: 'window',
        category: '测试类别'
      });

      expect(newStructure.code).toBe('NEW_STRUCTURE');
      expect(newStructure.name).toBe('新产品结构');
      expect(newStructure.productType).toBe('window');
      expect(newStructure.status).toBe('draft');
      expect(newStructure.version).toBe(1);
    });
  });

  describe('ValidationService', () => {
    it('应该能够验证产品结构', async () => {
      const mockStructure = {
        id: 'struct_001',
        productConstraints: [
          {
            id: 'constraint_001',
            name: '尺寸约束',
            type: 'dimension',
            expression: 'width > 500',
            errorMessage: '宽度必须大于500mm',
            severity: 'error',
            autoFix: {
              enabled: false,
              fixExpression: '',
              fixMessage: ''
            }
          }
        ]
      };

      const result = await validationService.validateStructure(mockStructure as any);
      
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('warnings');
      expect(result).toHaveProperty('structureIntegrity');
      expect(result).toHaveProperty('structureStatistics');
    });
  });
});
