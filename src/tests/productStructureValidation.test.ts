/**
 * 产品结构数据验证测试
 * 验证MTO模式下的产品结构数据完整性
 */

import { describe, it, expect } from 'vitest';
import { validateProductStructure, generateValidationReport } from '@/utils/validateProductStructure';

// 测试用的标准防火窗结构数据
const standardFireWindowStructure = {
  id: 'fw_std_001',
  code: 'FW-STD-001',
  name: '标准防火窗结构',
  description: '符合国标GB16809-2008的A级防火窗，60分钟耐火极限',
  version: 1,
  status: 'active',
  rootAssembly: {
    id: 'asm_fw_main',
    assemblyId: 'asm_fw_main',
    assemblyCode: 'FW-MAIN-ASM',
    assemblyName: '防火窗主框架构件',
    assemblyVersion: 1,
    instanceName: '主框架总成',
    quantity: 1,
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    parameterValues: {
      frame_width: 80,
      frame_depth: 120,
      corner_joint_type: 'welded'
    },
    parameters: [
      {
        id: 'param_frame_width',
        name: 'frame_width',
        displayName: '框架宽度',
        type: 'dimension',
        inputType: 'number',
        unit: 'mm',
        defaultValue: 80,
        required: true
      }
    ],
    componentInstances: [
      {
        id: 'comp_fw_glass_main',
        componentId: 'comp_fw_glass',
        componentCode: 'FW-GLASS-001',
        componentName: '防火玻璃面板',
        instanceName: '主玻璃面板',
        quantity: 1,
        properties: {
          materialCategoryId: 'cat_fire_glass',
          quantityFormula: '(glass_width * glass_height) / 1000000',
          costFormula: 'quantity * material_unit_cost * (glass_thickness / 6)'
        },
        parameters: [
          {
            id: 'param_glass_width',
            name: 'glass_width',
            displayName: '玻璃宽度',
            type: 'dimension',
            inputType: 'number',
            unit: 'mm',
            required: true
          }
        ]
      }
    ]
  },
  parameters: [
    {
      id: 'prod_param_width',
      name: 'window_width',
      displayName: '窗户宽度',
      type: 'dimension',
      inputType: 'number',
      unit: 'mm',
      required: true
    }
  ],
  constraints: [
    {
      id: 'const_prod_001',
      name: '窗户面积约束',
      expression: 'window_width * window_height <= 6000000',
      errorMessage: '窗户面积不能超过6平方米',
      severity: 'error'
    }
  ]
};

describe('产品结构数据验证', () => {
  it('应该验证完整的产品结构', () => {
    const result = validateProductStructure(standardFireWindowStructure);
    
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.summary.totalNodes).toBeGreaterThan(1);
    expect(result.summary.productNodes).toBe(1);
    expect(result.summary.assemblyNodes).toBeGreaterThan(0);
    expect(result.summary.componentNodes).toBeGreaterThan(0);
    expect(result.summary.parametersCount).toBeGreaterThan(0);
  });

  it('应该检测缺少基本信息的产品', () => {
    const invalidStructure = {
      // 缺少 id, code, name
      description: '测试产品'
    };
    
    const result = validateProductStructure(invalidStructure);
    
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
    expect(result.errors[0]).toContain('缺少基本信息');
  });

  it('应该检测缺少根构件的产品', () => {
    const structureWithoutRoot = {
      id: 'test_001',
      code: 'TEST-001',
      name: '测试产品',
      // 缺少 rootAssembly
    };
    
    const result = validateProductStructure(structureWithoutRoot);
    
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('产品缺少根构件 (rootAssembly)');
  });

  it('应该生成详细的验证报告', () => {
    const report = generateValidationReport(standardFireWindowStructure);
    
    expect(report).toContain('产品结构验证报告');
    expect(report).toContain('标准防火窗结构');
    expect(report).toContain('FW-STD-001');
    expect(report).toContain('结构统计');
    expect(report).toContain('总节点数');
  });

  it('应该正确统计节点数量', () => {
    const result = validateProductStructure(standardFireWindowStructure);
    
    // 1个产品 + 1个构件 + 1个组件 = 3个节点
    expect(result.summary.totalNodes).toBe(3);
    expect(result.summary.productNodes).toBe(1);
    expect(result.summary.assemblyNodes).toBe(1);
    expect(result.summary.componentNodes).toBe(1);
  });

  it('应该验证参数完整性', () => {
    const structureWithInvalidParam = {
      ...standardFireWindowStructure,
      rootAssembly: {
        ...standardFireWindowStructure.rootAssembly,
        parameters: [
          {
            // 缺少 name, displayName, type
            id: 'invalid_param'
          }
        ]
      }
    };
    
    const result = validateProductStructure(structureWithInvalidParam);
    
    expect(result.isValid).toBe(false);
    expect(result.errors.some(error => error.includes('缺少必要信息'))).toBe(true);
  });
});
