import { describe, it, expect, beforeEach } from 'vitest';
import { productStructureService } from '@/services/productService';
import type { ProductStructure } from '@/types/product';

// Mock fetch for testing
global.fetch = vi.fn();

describe('ProductStructureService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should load product structures successfully', async () => {
    const mockProductStructures = {
      productStructures: [
        {
          id: 'struct_001',
          code: 'FIRE_WINDOW_STD',
          name: '标准防火窗结构',
          productType: 'window',
          category: '防火窗',
          status: 'active'
        },
        {
          id: 'struct_002',
          code: 'FIRE_PARTITION_STD',
          name: '标准防火隔断结构',
          productType: 'partition',
          category: '防火隔断',
          status: 'active'
        },
        {
          id: 'struct_003',
          code: 'CURTAIN_WALL_STD',
          name: '标准幕墙结构',
          productType: 'curtain_wall',
          category: '玻璃幕墙',
          status: 'active'
        }
      ]
    };

    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockProductStructures
    });

    const structures = await productStructureService.getStructures();
    
    expect(fetch).toHaveBeenCalledWith('/mock/product/product-structures.json');
    expect(structures).toHaveLength(3);
    expect(structures[0].name).toBe('标准防火窗结构');
    expect(structures[1].name).toBe('标准防火隔断结构');
    expect(structures[2].name).toBe('标准幕墙结构');
  });

  it('should filter structures by product type', async () => {
    const mockProductStructures = {
      productStructures: [
        {
          id: 'struct_001',
          code: 'FIRE_WINDOW_STD',
          name: '标准防火窗结构',
          productType: 'window',
          category: '防火窗',
          status: 'active'
        },
        {
          id: 'struct_002',
          code: 'FIRE_PARTITION_STD',
          name: '标准防火隔断结构',
          productType: 'partition',
          category: '防火隔断',
          status: 'active'
        }
      ]
    };

    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockProductStructures
    });

    const structures = await productStructureService.getStructures({
      productType: 'window'
    });
    
    expect(structures).toHaveLength(1);
    expect(structures[0].productType).toBe('window');
    expect(structures[0].name).toBe('标准防火窗结构');
  });

  it('should filter structures by category', async () => {
    const mockProductStructures = {
      productStructures: [
        {
          id: 'struct_001',
          code: 'FIRE_WINDOW_STD',
          name: '标准防火窗结构',
          productType: 'window',
          category: '防火窗',
          status: 'active'
        },
        {
          id: 'struct_002',
          code: 'FIRE_PARTITION_STD',
          name: '标准防火隔断结构',
          productType: 'partition',
          category: '防火隔断',
          status: 'active'
        }
      ]
    };

    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockProductStructures
    });

    const structures = await productStructureService.getStructures({
      category: '防火窗'
    });
    
    expect(structures).toHaveLength(1);
    expect(structures[0].category).toBe('防火窗');
  });

  it('should filter structures by tags', async () => {
    const mockProductStructures = {
      productStructures: [
        {
          id: 'struct_001',
          code: 'FIRE_WINDOW_STD',
          name: '标准防火窗结构',
          productType: 'window',
          category: '防火窗',
          tags: ['防火', '标准', '固定式'],
          status: 'active'
        },
        {
          id: 'struct_002',
          code: 'FIRE_PARTITION_STD',
          name: '标准防火隔断结构',
          productType: 'partition',
          category: '防火隔断',
          tags: ['防火', '隔断', '不锈钢'],
          status: 'active'
        }
      ]
    };

    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockProductStructures
    });

    const structures = await productStructureService.getStructures({
      tags: ['标准']
    });
    
    expect(structures).toHaveLength(1);
    expect(structures[0].tags).toContain('标准');
  });

  it('should search structures by name and description', async () => {
    const mockProductStructures = {
      productStructures: [
        {
          id: 'struct_001',
          code: 'FIRE_WINDOW_STD',
          name: '标准防火窗结构',
          description: '标准防火窗产品结构，支持单扇或双扇配置',
          productType: 'window',
          category: '防火窗',
          status: 'active'
        },
        {
          id: 'struct_002',
          code: 'FIRE_PARTITION_STD',
          name: '标准防火隔断结构',
          description: '标准防火隔断产品结构，适用于室内空间分隔',
          productType: 'partition',
          category: '防火隔断',
          status: 'active'
        }
      ]
    };

    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockProductStructures
    });

    const structures = await productStructureService.getStructures({
      search: '隔断'
    });
    
    expect(structures).toHaveLength(1);
    expect(structures[0].name).toContain('隔断');
  });

  it('should get structure by id', async () => {
    const mockProductStructures = {
      productStructures: [
        {
          id: 'struct_001',
          code: 'FIRE_WINDOW_STD',
          name: '标准防火窗结构',
          productType: 'window',
          category: '防火窗',
          status: 'active'
        }
      ]
    };

    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockProductStructures
    });

    const structure = await productStructureService.getStructureById('struct_001');
    
    expect(structure).toBeTruthy();
    expect(structure?.id).toBe('struct_001');
    expect(structure?.name).toBe('标准防火窗结构');
  });

  it('should return null for non-existent structure', async () => {
    const mockProductStructures = {
      productStructures: []
    };

    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockProductStructures
    });

    const structure = await productStructureService.getStructureById('non_existent');
    
    expect(structure).toBeNull();
  });

  it('should handle fetch errors gracefully', async () => {
    (fetch as any).mockRejectedValueOnce(new Error('Network error'));

    await expect(productStructureService.getStructures()).rejects.toThrow('Network error');
  });

  it('should validate product structure data structure', async () => {
    const mockProductStructures = {
      productStructures: [
        {
          id: 'struct_001',
          code: 'FIRE_WINDOW_STD',
          name: '标准防火窗结构',
          productType: 'window',
          category: '防火窗',
          productParameters: [
            {
              id: 'param_001',
              name: 'window_width',
              displayName: '窗宽',
              type: 'number',
              required: true,
              category: 'dimension'
            }
          ],
          productConstraints: [
            {
              id: 'const_001',
              name: '尺寸约束',
              type: 'dimension',
              expression: 'window_width >= 600',
              errorMessage: '窗宽不能小于600mm',
              severity: 'error'
            }
          ],
          status: 'active'
        }
      ]
    };

    (fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockProductStructures
    });

    const structures = await productStructureService.getStructures();
    const structure = structures[0];
    
    expect(structure.productParameters).toBeDefined();
    expect(structure.productParameters).toHaveLength(1);
    expect(structure.productParameters[0].name).toBe('window_width');
    
    expect(structure.productConstraints).toBeDefined();
    expect(structure.productConstraints).toHaveLength(1);
    expect(structure.productConstraints[0].name).toBe('尺寸约束');
  });
});
