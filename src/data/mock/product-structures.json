{"productStructures": [{"id": "ps_001", "code": "FW_BASIC_001", "name": "基础防火窗结构", "description": "标准防火窗产品结构，适用于商业和住宅建筑", "productType": "window", "category": "防火窗", "subCategory": "钢质防火窗", "status": "active", "version": 3, "rootAssembly": {"assemblyId": "asm_fw_001", "assemblyCode": "FW_FRAME_001", "assemblyName": "防火窗框架总成"}, "productParameters": [{"id": "param_001", "name": "width", "displayName": "窗宽", "type": "number", "unit": "mm", "defaultValue": 1200, "minValue": 600, "maxValue": 2400, "required": true, "description": "窗户的宽度尺寸", "category": "dimension"}, {"id": "param_002", "name": "height", "displayName": "窗高", "type": "number", "unit": "mm", "defaultValue": 1500, "minValue": 800, "maxValue": 2800, "required": true, "description": "窗户的高度尺寸", "category": "dimension"}], "configurationOptions": [{"id": "config_001", "name": "opening_type", "displayName": "开启方式", "type": "select", "required": true, "description": "窗户的开启方式选择", "choices": [{"id": "choice_001", "value": "fixed", "label": "固定窗", "description": "不可开启的固定窗"}, {"id": "choice_002", "value": "casement", "label": "平开窗", "description": "向内或向外开启的平开窗"}]}], "productConstraints": [{"id": "constraint_001", "name": "size_constraint", "type": "formula", "expression": "width * height <= 6000000", "description": "窗户面积不能超过6平方米", "errorMessage": "窗户面积超出限制"}], "applications": ["commercial", "residential"], "tags": ["防火", "安全", "标准"], "properties": {"fireRating": "A级", "soundInsulation": "35dB", "thermalInsulation": "K值≤2.8"}, "createdAt": "2024-01-15T08:30:00Z", "updatedAt": "2024-03-10T14:20:00Z", "createdBy": "engineer_001", "updatedBy": "engineer_001", "versionHistory": [{"id": "vh_001", "versionNumber": 1, "changeDate": "2024-01-15T08:30:00Z", "changedBy": "engineer_001", "changeType": "create", "changeDescription": "初始创建防火窗结构", "isCurrent": false, "changeRecords": []}, {"id": "vh_002", "versionNumber": 2, "changeDate": "2024-02-20T10:15:00Z", "changedBy": "engineer_001", "changeType": "update", "changeDescription": "添加开启方式配置选项", "isCurrent": false, "changeRecords": []}, {"id": "vh_003", "versionNumber": 3, "changeDate": "2024-03-10T14:20:00Z", "changedBy": "engineer_001", "changeType": "update", "changeDescription": "优化尺寸约束条件", "isCurrent": true, "changeRecords": []}], "lastValidationResult": {"id": "validation_001", "isValid": true, "validationTime": "2024-03-10T14:25:00Z", "summary": {"totalErrors": 0, "totalWarnings": 1, "totalSuggestions": 2, "criticalErrors": 0}, "errors": [], "warnings": [{"id": "warning_001", "message": "建议添加更多配置选项以提高产品灵活性", "severity": "medium", "location": {"objectType": "configuration", "objectId": "config_001"}, "suggestions": ["添加颜色选择", "添加玻璃类型选择"]}], "suggestions": [{"id": "suggestion_001", "type": "performance", "suggestion": "考虑添加缓存机制以提高配置计算性能", "expectedBenefit": "提高配置计算速度30%", "implementationDifficulty": "medium", "impactScope": ["configuration", "calculation"]}]}, "lastValidationTime": "2024-03-10T14:25:00Z"}, {"id": "ps_002", "code": "PD_GLASS_001", "name": "玻璃隔断结构", "description": "办公室玻璃隔断产品结构", "productType": "partition", "category": "玻璃隔断", "subCategory": "办公隔断", "status": "active", "version": 2, "rootAssembly": {"assemblyId": "asm_pd_001", "assemblyCode": "PD_FRAME_001", "assemblyName": "隔断框架总成"}, "productParameters": [{"id": "param_003", "name": "panel_width", "displayName": "面板宽度", "type": "number", "unit": "mm", "defaultValue": 1000, "minValue": 600, "maxValue": 1500, "required": true, "description": "隔断面板的宽度", "category": "dimension"}], "configurationOptions": [{"id": "config_002", "name": "glass_type", "displayName": "玻璃类型", "type": "select", "required": true, "description": "隔断使用的玻璃类型", "choices": [{"id": "choice_003", "value": "clear", "label": "透明玻璃", "description": "标准透明玻璃"}, {"id": "choice_004", "value": "frosted", "label": "磨砂玻璃", "description": "半透明磨砂玻璃"}]}], "productConstraints": [], "applications": ["commercial"], "tags": ["隔断", "办公", "玻璃"], "properties": {"soundInsulation": "40dB", "transparency": "85%"}, "createdAt": "2024-02-01T09:00:00Z", "updatedAt": "2024-03-05T16:30:00Z", "createdBy": "engineer_002", "updatedBy": "engineer_002", "versionHistory": [{"id": "vh_004", "versionNumber": 1, "changeDate": "2024-02-01T09:00:00Z", "changedBy": "engineer_002", "changeType": "create", "changeDescription": "创建玻璃隔断结构", "isCurrent": false, "changeRecords": []}, {"id": "vh_005", "versionNumber": 2, "changeDate": "2024-03-05T16:30:00Z", "changedBy": "engineer_002", "changeType": "update", "changeDescription": "添加玻璃类型选择", "isCurrent": true, "changeRecords": []}], "lastValidationResult": {"id": "validation_002", "isValid": true, "validationTime": "2024-03-05T16:35:00Z", "summary": {"totalErrors": 0, "totalWarnings": 0, "totalSuggestions": 1, "criticalErrors": 0}, "errors": [], "warnings": [], "suggestions": [{"id": "suggestion_002", "type": "usability", "suggestion": "考虑添加高度参数以支持不同高度需求", "expectedBenefit": "提高产品适用性", "implementationDifficulty": "low", "impactScope": ["parameter"]}]}, "lastValidationTime": "2024-03-05T16:35:00Z"}, {"id": "ps_003", "code": "DR_STEEL_001", "name": "钢质防火门结构", "description": "工业级钢质防火门产品结构", "productType": "door", "category": "防火门", "subCategory": "钢质防火门", "status": "draft", "version": 1, "rootAssembly": {"assemblyId": "asm_dr_001", "assemblyCode": "DR_FRAME_001", "assemblyName": "防火门框架总成"}, "productParameters": [{"id": "param_004", "name": "door_width", "displayName": "门宽", "type": "number", "unit": "mm", "defaultValue": 900, "minValue": 700, "maxValue": 1200, "required": true, "description": "门的宽度尺寸", "category": "dimension"}], "configurationOptions": [], "productConstraints": [], "applications": ["industrial", "public"], "tags": ["防火", "钢质", "工业"], "properties": {"fireRating": "甲级", "thickness": "50mm"}, "createdAt": "2024-03-01T11:00:00Z", "updatedAt": "2024-03-01T11:00:00Z", "createdBy": "engineer_003", "updatedBy": "engineer_003", "versionHistory": [{"id": "vh_006", "versionNumber": 1, "changeDate": "2024-03-01T11:00:00Z", "changedBy": "engineer_003", "changeType": "create", "changeDescription": "创建钢质防火门结构", "isCurrent": true, "changeRecords": []}], "lastValidationResult": null, "lastValidationTime": null}]}