<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">用户状态管理测试</h1>
    </div>
    
    <!-- 用户状态显示 -->
    <Card>
      <CardHeader>
        <CardTitle>当前用户状态</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium">认证状态:</label>
            <p class="text-lg">{{ userStore.isAuthenticated ? '已登录' : '未登录' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">用户名:</label>
            <p class="text-lg">{{ userStore.currentUser?.name || '无' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">邮箱:</label>
            <p class="text-lg">{{ userStore.currentUser?.email || '无' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">部门:</label>
            <p class="text-lg">{{ userStore.currentUser?.department || '无' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">角色:</label>
            <p class="text-lg">{{ userStore.currentUser?.roles.join(', ') || '无' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">是否管理员:</label>
            <p class="text-lg">{{ userStore.isAdmin ? '是' : '否' }}</p>
          </div>
        </div>
        
        <div>
          <label class="text-sm font-medium">权限列表:</label>
          <div class="flex flex-wrap gap-2 mt-2">
            <Badge v-for="permission in userStore.permissions" :key="permission" variant="secondary">
              {{ permission }}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 权限测试 -->
    <Card>
      <CardHeader>
        <CardTitle>权限测试</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium">订单创建权限:</label>
            <p class="text-lg">{{ userStore.hasPermission('order.create') ? '有权限' : '无权限' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">生产管理权限:</label>
            <p class="text-lg">{{ userStore.hasPermission('production.manage') ? '有权限' : '无权限' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">管理员角色:</label>
            <p class="text-lg">{{ userStore.hasRole('admin') ? '是' : '否' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">销售工程师角色:</label>
            <p class="text-lg">{{ userStore.hasRole('sales_engineer') ? '是' : '否' }}</p>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 操作按钮 -->
    <Card>
      <CardHeader>
        <CardTitle>操作测试</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="flex gap-4">
          <Button @click="testLogin" :disabled="userStore.isLoading">
            {{ userStore.isLoading ? '登录中...' : '测试登录 (admin)' }}
          </Button>
          <Button @click="testLogout" variant="outline" :disabled="userStore.isLoading">
            {{ userStore.isLoading ? '登出中...' : '测试登出' }}
          </Button>
          <Button @click="refreshUser" variant="secondary" :disabled="userStore.isLoading">
            刷新用户信息
          </Button>
        </div>
        
        <div v-if="userStore.error" class="p-4 bg-red-50 border border-red-200 rounded-md">
          <p class="text-red-800">错误: {{ userStore.error }}</p>
          <Button @click="userStore.clearError" size="sm" variant="outline" class="mt-2">
            清除错误
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

const userStore = useUserStore()

const testLogin = async () => {
  await userStore.login({
    username: 'admin',
    password: 'admin'
  })
}

const testLogout = async () => {
  await userStore.logout()
}

const refreshUser = async () => {
  await userStore.refreshUserInfo()
}
</script>
