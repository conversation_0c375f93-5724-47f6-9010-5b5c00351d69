<template>
  <div class="container mx-auto p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-6">数据集成测试</h1>
      
      <!-- 验证状态 -->
      <div class="mb-6">
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">数据集成验证</h2>
          
          <div class="flex items-center gap-4 mb-4">
            <button 
              @click="runValidation"
              :disabled="isValidating"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {{ isValidating ? '验证中...' : '运行验证' }}
            </button>
            
            <div v-if="validationResult" class="flex items-center gap-2">
              <span :class="validationResult.isValid ? 'text-green-600' : 'text-red-600'">
                {{ validationResult.isValid ? '✅ 验证通过' : '❌ 验证失败' }}
              </span>
            </div>
          </div>

          <div v-if="validationResult && !validationResult.isValid" class="space-y-4">
            <div v-if="validationResult.issues.length > 0">
              <h3 class="font-medium text-red-600 mb-2">发现的问题:</h3>
              <ul class="list-disc list-inside space-y-1 text-sm text-red-600">
                <li v-for="issue in validationResult.issues" :key="issue">{{ issue }}</li>
              </ul>
            </div>
            
            <div v-if="validationResult.recommendations.length > 0">
              <h3 class="font-medium text-blue-600 mb-2">建议:</h3>
              <ul class="list-disc list-inside space-y-1 text-sm text-blue-600">
                <li v-for="rec in validationResult.recommendations" :key="rec">{{ rec }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">产品模板</h3>
          <p class="text-2xl font-bold text-blue-600">{{ productStore.productTemplates.length }}</p>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">物料变体</h3>
          <p class="text-2xl font-bold text-green-600">{{ materialVariantStore.materialVariants.length }}</p>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">有成本物料</h3>
          <p class="text-2xl font-bold text-orange-600">{{ materialsWithCost }}</p>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-sm font-medium text-gray-500">有库存物料</h3>
          <p class="text-2xl font-bold text-purple-600">{{ materialsWithStock }}</p>
        </div>
      </div>

      <!-- 功能测试 -->
      <div class="space-y-6">
        <!-- 产品配置测试 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">产品配置测试</h2>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium mb-2">选择产品模板:</label>
              <select 
                v-model="selectedTemplateId" 
                @change="onTemplateChange"
                class="w-full p-2 border rounded"
              >
                <option value="">请选择...</option>
                <option 
                  v-for="template in productStore.productTemplates" 
                  :key="template.id" 
                  :value="template.id"
                >
                  {{ template.name }}
                </option>
              </select>
            </div>

            <div v-if="selectedTemplateId" class="space-y-4">
              <button 
                @click="createTestConfiguration"
                :disabled="isCreatingConfig"
                class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
              >
                {{ isCreatingConfig ? '创建中...' : '创建测试配置' }}
              </button>

              <div v-if="testConfiguration" class="p-4 bg-gray-50 rounded">
                <h3 class="font-medium mb-2">配置信息:</h3>
                <p><strong>名称:</strong> {{ testConfiguration.name }}</p>
                <p><strong>编码:</strong> {{ testConfiguration.code }}</p>
                <p><strong>状态:</strong> {{ testConfiguration.status }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- BOM生成测试 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">BOM生成测试</h2>
          
          <div class="space-y-4">
            <button 
              @click="generateTestBOM"
              :disabled="!testConfiguration || isGeneratingBOM"
              class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
            >
              {{ isGeneratingBOM ? '生成中...' : '生成BOM' }}
            </button>

            <div v-if="generatedBOM" class="space-y-4">
              <div class="p-4 bg-gray-50 rounded">
                <h3 class="font-medium mb-2">BOM汇总:</h3>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <p><strong>项目数量:</strong> {{ generatedBOM.summary.totalItems }}</p>
                  <p><strong>物料成本:</strong> ¥{{ generatedBOM.summary.totalMaterialCost.toFixed(2) }}</p>
                  <p><strong>总重量:</strong> {{ generatedBOM.summary.totalWeight.toFixed(2)}} kg</p>
                  <p><strong>关键物料:</strong> {{ generatedBOM.summary.criticalMaterials.length }} 个</p>
                </div>
              </div>

              <div v-if="generatedBOM.items.length > 0">
                <h3 class="font-medium mb-2">BOM项目 (前5项):</h3>
                <div class="overflow-x-auto">
                  <table class="min-w-full text-sm">
                    <thead class="bg-gray-100">
                      <tr>
                        <th class="px-3 py-2 text-left">物料名称</th>
                        <th class="px-3 py-2 text-left">数量</th>
                        <th class="px-3 py-2 text-left">单位</th>
                        <th class="px-3 py-2 text-left">单价</th>
                        <th class="px-3 py-2 text-left">总价</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="item in generatedBOM.items.slice(0, 5)" :key="item.id" class="border-t">
                        <td class="px-3 py-2">{{ item.materialVariant?.displayName || '未知物料' }}</td>
                        <td class="px-3 py-2">{{ item.quantity }}</td>
                        <td class="px-3 py-2">{{ item.unit }}</td>
                        <td class="px-3 py-2">¥{{ item.unitCost.toFixed(2) }}</td>
                        <td class="px-3 py-2">¥{{ item.totalCost.toFixed(2) }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 成本计算测试 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">成本计算测试</h2>
          
          <div class="space-y-4">
            <button 
              @click="calculateTestCost"
              :disabled="!generatedBOM || isCalculatingCost"
              class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
            >
              {{ isCalculatingCost ? '计算中...' : '计算成本' }}
            </button>

            <div v-if="costBreakdown" class="p-4 bg-gray-50 rounded">
              <h3 class="font-medium mb-2">成本明细:</h3>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <p><strong>物料成本:</strong> ¥{{ costBreakdown.materialCost.toFixed(2) }}</p>
                <p><strong>人工成本:</strong> ¥{{ costBreakdown.laborCost.toFixed(2) }}</p>
                <p><strong>制造费用:</strong> ¥{{ costBreakdown.overheadCost.toFixed(2) }}</p>
                <p><strong>总成本:</strong> ¥{{ costBreakdown.totalCost.toFixed(2) }}</p>
                <p><strong>建议售价:</strong> ¥{{ costBreakdown.suggestedPrice.toFixed(2) }}</p>
                <p><strong>利润率:</strong> {{ costBreakdown.profitMargin.toFixed(1) }}%</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// import { useProductStore } from '@/stores/product'
import { useMaterialVariantStore } from '@/stores/materialVariant'
import type { ProductConfiguration, ProductBOM, CostBreakdown } from '@/types/product'

// const productStore = useProductStore()
const materialVariantStore = useMaterialVariantStore()

// 响应式数据
const isValidating = ref(false)
const validationResult = ref<{
  isValid: boolean
  issues: string[]
  recommendations: string[]
} | null>(null)

const selectedTemplateId = ref('')
const isCreatingConfig = ref(false)
const testConfiguration = ref<ProductConfiguration | null>(null)

const isGeneratingBOM = ref(false)
const generatedBOM = ref<ProductBOM | null>(null)

const isCalculatingCost = ref(false)
const costBreakdown = ref<CostBreakdown | null>(null)

// 计算属性
const materialsWithCost = computed(() => {
  return materialVariantStore.materialVariants.filter(m => m.cost > 0).length
})

const materialsWithStock = computed(() => {
  return materialVariantStore.materialVariants.filter(m => m.availableQuantity > 0).length
})

// 方法
const runValidation = async () => {
  isValidating.value = true
  try {
    validationResult.value = await productStore.validateDataIntegration()
  } catch (error) {
    console.error('验证失败:', error)
  } finally {
    isValidating.value = false
  }
}

const onTemplateChange = () => {
  if (selectedTemplateId.value) {
    productStore.selectTemplate(selectedTemplateId.value)
  }
  testConfiguration.value = null
  generatedBOM.value = null
  costBreakdown.value = null
}

const createTestConfiguration = async () => {
  if (!selectedTemplateId.value) return
  
  isCreatingConfig.value = true
  try {
    const template = productStore.productTemplates.find(t => t.id === selectedTemplateId.value)
    if (template) {
      testConfiguration.value = productStore.createConfiguration(
        selectedTemplateId.value, 
        `测试配置 - ${template.name}`
      )
      
      // 设置一些基础参数
      productStore.updateConfigurationParameters([
        { parameterId: 'width', parameterName: '宽度', value: 1000, unit: 'mm' },
        { parameterId: 'height', parameterName: '高度', value: 1200, unit: 'mm' }
      ])
    }
  } catch (error) {
    console.error('创建配置失败:', error)
  } finally {
    isCreatingConfig.value = false
  }
}

const generateTestBOM = async () => {
  if (!testConfiguration.value) return
  
  isGeneratingBOM.value = true
  try {
    generatedBOM.value = await productStore.generateBOM()
  } catch (error) {
    console.error('生成BOM失败:', error)
  } finally {
    isGeneratingBOM.value = false
  }
}

const calculateTestCost = async () => {
  if (!generatedBOM.value) return
  
  isCalculatingCost.value = true
  try {
    costBreakdown.value = await productStore.calculateCost()
  } catch (error) {
    console.error('计算成本失败:', error)
  } finally {
    isCalculatingCost.value = false
  }
}

// 初始化
onMounted(async () => {
  try {
    await productStore.initializeProductData()
    await materialVariantStore.initializeMaterialVariantData()
    
    // 自动运行验证
    await runValidation()
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>