<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">路由系统测试</h1>
      <Button @click="runTests" :disabled="testing">
        <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': testing }" />
        {{ testing ? '测试中...' : '运行测试' }}
      </Button>
    </div>

    <!-- 核心路由测试结果 -->
    <Card>
      <CardHeader>
        <CardTitle>核心路由测试 (Requirements 6.2)</CardTitle>
        <CardDescription>
          测试"仪表盘"、"元数据管理"、"客户关系"页面路由
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-2">
          <div v-for="result in coreTestResults" :key="result.routeName"
               class="flex items-center justify-between p-3 rounded-lg border">
            <div class="flex items-center space-x-3">
              <div class="flex items-center space-x-2">
                <CheckCircle v-if="!result.error" class="h-5 w-5 text-green-500" />
                <XCircle v-else class="h-5 w-5 text-red-500" />
                <span class="font-medium">{{ result.routeName }}</span>
              </div>
              <Badge variant="outline">{{ result.path }}</Badge>
            </div>
            <div class="flex items-center space-x-2">
              <Button variant="outline" size="sm" @click="navigateToRoute(result.routeName)"
                      :disabled="!!result.error">
                <ExternalLink class="h-4 w-4 mr-1" />
                测试导航
              </Button>
              <span v-if="result.error" class="text-sm text-red-500">{{ result.error }}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 所有路由测试结果 -->
    <Card>
      <CardHeader>
        <CardTitle>所有路由测试 (Requirements 6.1, 6.3, 6.4)</CardTitle>
        <CardDescription>
          测试所有可见路由的配置和导航功能
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-2 max-h-96 overflow-y-auto">
          <div v-for="result in allTestResults" :key="result.routeName"
               class="flex items-center justify-between p-3 rounded-lg border">
            <div class="flex items-center space-x-3">
              <div class="flex items-center space-x-2">
                <CheckCircle v-if="!result.error" class="h-5 w-5 text-green-500" />
                <XCircle v-else class="h-5 w-5 text-red-500" />
                <span class="font-medium">{{ result.routeName }}</span>
              </div>
              <Badge variant="outline">{{ result.path }}</Badge>
            </div>
            <div class="flex items-center space-x-2">
              <Button variant="outline" size="sm" @click="navigateToRoute(result.routeName)"
                      :disabled="!!result.error">
                <ExternalLink class="h-4 w-4 mr-1" />
                测试导航
              </Button>
              <span v-if="result.error" class="text-sm text-red-500 max-w-xs truncate">{{ result.error }}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 测试统计 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm font-medium">总路由数</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ totalRoutes }}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm font-medium">通过测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-green-600">{{ passedTests }}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm font-medium">失败测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-red-600">{{ failedTests }}</div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, RefreshCw, ExternalLink } from 'lucide-vue-next'
import { testCoreRoutes, testAllVisibleRoutes, type RouteTestResult } from '@/utils/routeTest'

const router = useRouter()
const testing = ref(false)
const coreTestResults = ref<RouteTestResult[]>([])
const allTestResults = ref<RouteTestResult[]>([])

// 计算统计数据
const totalRoutes = computed(() => allTestResults.value.length)
const passedTests = computed(() => 
  coreTestResults.value.filter(r => !r.error).length + 
  allTestResults.value.filter(r => !r.error).length
)
const failedTests = computed(() => 
  coreTestResults.value.filter(r => r.error).length + 
  allTestResults.value.filter(r => r.error).length
)

// 运行测试
const runTests = async () => {
  testing.value = true
  
  try {
    // 模拟测试延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 运行核心路由测试
    coreTestResults.value = testCoreRoutes()
    
    // 运行所有路由测试
    allTestResults.value = testAllVisibleRoutes()
    
    console.log('✅ 路由测试完成')
    console.log('核心路由测试结果:', coreTestResults.value)
    console.log('所有路由测试结果:', allTestResults.value)
  } catch (error) {
    console.error('❌ 路由测试失败:', error)
  } finally {
    testing.value = false
  }
}

// 导航到指定路由
const navigateToRoute = async (routeName: string) => {
  try {
    await router.push({ name: routeName })
    console.log(`✅ 成功导航到路由: ${routeName}`)
  } catch (error) {
    console.error(`❌ 导航到路由 ${routeName} 失败:`, error)
  }
}

// 组件挂载时自动运行测试
onMounted(() => {
  runTests()
})
</script>