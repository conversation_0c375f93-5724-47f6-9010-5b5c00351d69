<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">客户关系管理</h1>
      <Button>
        <Plus class="mr-2 h-4 w-4" />
        新增客户
      </Button>
    </div>
    
    <Card>
      <CardHeader>
        <CardTitle>客户列表</CardTitle>
        <CardDescription>
          管理您的客户信息和订单历史
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="h-[400px] flex items-center justify-center text-muted-foreground">
          客户数据表格区域
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-vue-next'
</script>