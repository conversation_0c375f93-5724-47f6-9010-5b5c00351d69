<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { Material } from '@/types/masterdata';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Search, Edit, Trash2 } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isCreateDialogOpen = ref(false);
const isEditDialogOpen = ref(false);
const editingMaterial = ref<Material | null>(null);

// Form data
const formData = ref<Partial<Material>>({
  name: '',
  type: 'raw',
  category: '',
  baseUnit: 'piece',
  attributes: {},
  supplier: '',
  leadTime: 0,
  minOrderQty: 0,
  standardCost: 0,
  safetyStock: 0,
  description: '',
  isActive: true
});

// Options
const materialTypes = [
  { value: 'raw', label: '原材料' },
  { value: 'semi-finished', label: '半成品' },
  { value: 'finished', label: '成品' },
  { value: 'auxiliary', label: '辅料' }
];

const baseUnits = [
  { value: 'piece', label: '件' },
  { value: 'm', label: '米' },
  { value: 'm²', label: '平方米' },
  { value: 'kg', label: '千克' },
  { value: 'ton', label: '吨' },
  { value: 'liter', label: '升' }
];

// Computed
const filteredMaterials = computed(() => {
  if (!searchQuery.value) return masterDataStore.materials;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.materials.filter(m =>
    m.name.toLowerCase().includes(query) ||
    m.id.toLowerCase().includes(query) ||
    m.type.toLowerCase().includes(query) ||
    m.category?.toLowerCase().includes(query)
  );
});

const getTypeLabel = (type: string) => {
  const option = materialTypes.find(opt => opt.value === type);
  return option?.label || type;
};

const getUnitLabel = (unit: string) => {
  const option = baseUnits.find(opt => opt.value === unit);
  return option?.label || unit;
};

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    type: 'raw',
    category: '',
    baseUnit: 'piece',
    attributes: {},
    supplier: '',
    leadTime: 0,
    minOrderQty: 0,
    standardCost: 0,
    safetyStock: 0,
    description: '',
    isActive: true
  };
};

const openCreateDialog = () => {
  resetForm();
  isCreateDialogOpen.value = true;
};

const openEditDialog = (material: Material) => {
  editingMaterial.value = material;
  formData.value = { ...material };
  isEditDialogOpen.value = true;
};

const handleCreate = async () => {
  try {
    if (!formData.value.name || !formData.value.type) {
      toast.error('请填写必填字段');
      return;
    }

    await masterDataStore.createMaterial(formData.value as Omit<Material, 'id' | 'createdAt' | 'updatedAt'>);
    toast.success('物料创建成功');
    isCreateDialogOpen.value = false;
    resetForm();
  } catch (error) {
    toast.error('物料创建失败');
  }
};

const handleUpdate = async () => {
  try {
    if (!editingMaterial.value || !formData.value.name || !formData.value.type) {
      toast.error('请填写必填字段');
      return;
    }

    await masterDataStore.updateMaterial(editingMaterial.value.id, formData.value);
    toast.success('物料更新成功');
    isEditDialogOpen.value = false;
    editingMaterial.value = null;
    resetForm();
  } catch (error) {
    toast.error('物料更新失败');
  }
};

const handleDelete = async (material: Material) => {
  if (!confirm(`确定要删除物料 "${material.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteMaterial(material.id);
    toast.success('物料删除成功');
  } catch (error) {
    toast.error('物料删除失败');
  }
};

// Lifecycle
onMounted(() => {
  masterDataStore.fetchMaterials();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">物料管理</h1>
        <p class="text-muted-foreground">管理原材料、半成品、成品和辅料信息</p>
      </div>

      <Button @click="openCreateDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增物料
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">搜索和筛选</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="搜索物料名称、编号、类型或分类..."
                class="pl-10"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Material Table -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">物料列表</CardTitle>
        <CardDescription>
          共 {{ filteredMaterials.length }} 种物料
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>物料编号</TableHead>
                <TableHead>物料名称</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>分类</TableHead>
                <TableHead>基本单位</TableHead>
                <TableHead>供应商</TableHead>
                <TableHead>标准成本</TableHead>
                <TableHead>状态</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="9" class="text-center py-8">
                  <div class="flex items-center justify-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
              <TableRow v-else-if="filteredMaterials.length === 0">
                <TableCell colspan="9" class="text-center py-8 text-muted-foreground">
                  暂无物料数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="material in filteredMaterials" :key="material.id">
                <TableCell class="font-medium">{{ material.id }}</TableCell>
                <TableCell>{{ material.name }}</TableCell>
                <TableCell>
                  <Badge variant="outline">{{ getTypeLabel(material.type) }}</Badge>
                </TableCell>
                <TableCell>{{ material.category || '-' }}</TableCell>
                <TableCell>{{ getUnitLabel(material.baseUnit) }}</TableCell>
                <TableCell>{{ material.supplier || '-' }}</TableCell>
                <TableCell>
                  <span v-if="material.standardCost">
                    ¥{{ material.standardCost.toFixed(2) }}
                  </span>
                  <span v-else class="text-muted-foreground">-</span>
                </TableCell>
                <TableCell>
                  <Badge :variant="material.isActive !== false ? 'default' : 'secondary'">
                    {{ material.isActive !== false ? '启用' : '禁用' }}
                  </Badge>
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openEditDialog(material)"
                      class="h-8 w-8 p-0"
                    >
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="handleDelete(material)"
                      class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- Create Material Dialog -->
    <Dialog v-model:open="isCreateDialogOpen">
      <DialogContent class="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>新增物料</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">物料名称 *</Label>
              <Input
                id="name"
                v-model="formData.name"
                placeholder="请输入物料名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="type">物料类型 *</Label>
              <Select v-model="formData.type">
                <SelectTrigger>
                  <SelectValue placeholder="选择物料类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in materialTypes" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="category">物料分类</Label>
              <Input
                id="category"
                v-model="formData.category"
                placeholder="请输入物料分类"
              />
            </div>
            <div class="space-y-2">
              <Label for="baseUnit">基本单位 *</Label>
              <Select v-model="formData.baseUnit">
                <SelectTrigger>
                  <SelectValue placeholder="选择基本单位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in baseUnits" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="supplier">供应商</Label>
              <Input
                id="supplier"
                v-model="formData.supplier"
                placeholder="请输入供应商"
              />
            </div>
            <div class="space-y-2">
              <Label for="leadTime">采购周期(天)</Label>
              <Input
                id="leadTime"
                v-model.number="formData.leadTime"
                type="number"
                placeholder="0"
              />
            </div>
          </div>

          <div class="grid grid-cols-3 gap-4">
            <div class="space-y-2">
              <Label for="standardCost">标准成本</Label>
              <Input
                id="standardCost"
                v-model.number="formData.standardCost"
                type="number"
                step="0.01"
                placeholder="0.00"
              />
            </div>
            <div class="space-y-2">
              <Label for="minOrderQty">最小订购量</Label>
              <Input
                id="minOrderQty"
                v-model.number="formData.minOrderQty"
                type="number"
                placeholder="0"
              />
            </div>
            <div class="space-y-2">
              <Label for="safetyStock">安全库存</Label>
              <Input
                id="safetyStock"
                v-model.number="formData.safetyStock"
                type="number"
                placeholder="0"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="description">物料描述</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              placeholder="请输入物料描述"
              rows="3"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isCreateDialogOpen = false">
            取消
          </Button>
          <Button @click="handleCreate" :disabled="masterDataStore.loading">
            创建
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Edit Material Dialog -->
    <Dialog v-model:open="isEditDialogOpen">
      <DialogContent class="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>编辑物料</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-name">物料名称 *</Label>
              <Input
                id="edit-name"
                v-model="formData.name"
                placeholder="请输入物料名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-type">物料类型 *</Label>
              <Select v-model="formData.type">
                <SelectTrigger>
                  <SelectValue placeholder="选择物料类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in materialTypes" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-category">物料分类</Label>
              <Input
                id="edit-category"
                v-model="formData.category"
                placeholder="请输入物料分类"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-baseUnit">基本单位 *</Label>
              <Select v-model="formData.baseUnit">
                <SelectTrigger>
                  <SelectValue placeholder="选择基本单位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in baseUnits" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-supplier">供应商</Label>
              <Input
                id="edit-supplier"
                v-model="formData.supplier"
                placeholder="请输入供应商"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-leadTime">采购周期(天)</Label>
              <Input
                id="edit-leadTime"
                v-model.number="formData.leadTime"
                type="number"
                placeholder="0"
              />
            </div>
          </div>

          <div class="grid grid-cols-3 gap-4">
            <div class="space-y-2">
              <Label for="edit-standardCost">标准成本</Label>
              <Input
                id="edit-standardCost"
                v-model.number="formData.standardCost"
                type="number"
                step="0.01"
                placeholder="0.00"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-minOrderQty">最小订购量</Label>
              <Input
                id="edit-minOrderQty"
                v-model.number="formData.minOrderQty"
                type="number"
                placeholder="0"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-safetyStock">安全库存</Label>
              <Input
                id="edit-safetyStock"
                v-model.number="formData.safetyStock"
                type="number"
                placeholder="0"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="edit-description">物料描述</Label>
            <Textarea
              id="edit-description"
              v-model="formData.description"
              placeholder="请输入物料描述"
              rows="3"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isEditDialogOpen = false">
            取消
          </Button>
          <Button @click="handleUpdate" :disabled="masterDataStore.loading">
            更新
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>
