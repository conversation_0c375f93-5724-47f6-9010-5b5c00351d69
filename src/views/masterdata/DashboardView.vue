<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useMasterDataStore } from '@/stores/masterDataStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Settings, Package, Wrench, Factory, Route, GitBranch,
  Database, TrendingUp, AlertTriangle, CheckCircle,
  Plus, ArrowRight, Activity
} from 'lucide-vue-next';
import VueFlowTest from '@/components/test/VueFlowTest.vue';

const router = useRouter();
const masterDataStore = useMasterDataStore();

// 模块配置
const modules = [
  {
    id: 'equipments',
    title: '设备管理',
    description: '管理生产设备档案和状态',
    icon: Settings,
    route: '/masterdata/equipments',
    color: 'bg-blue-500',
    stats: computed(() => ({
      total: masterDataStore.equipments?.length ?? 0,
      active: masterDataStore.equipments?.filter(e => e.status === 'running').length ?? 0,
      status: 'running'
    }))
  },
  {
    id: 'workcenters',
    title: '工作中心',
    description: '配置工作中心和产能日历',
    icon: Factory,
    route: '/masterdata/workcenters',
    color: 'bg-green-500',
    stats: computed(() => ({
      total: masterDataStore.workCenters?.length ?? 0,
      active: masterDataStore.workCenters?.length ?? 0,
      status: 'configured'
    }))
  },
  {
    id: 'materials',
    title: '物料管理',
    description: '管理原材料、半成品和辅料',
    icon: Package,
    route: '/masterdata/materials',
    color: 'bg-purple-500',
    stats: computed(() => ({
      total: masterDataStore.materials?.length ?? 0,
      active: masterDataStore.materials?.filter(m => m.isActive).length ?? 0,
      status: 'managed'
    }))
  },
  {
    id: 'product-families',
    title: '产品族',
    description: '配置产品族和参数化BOM',
    icon: GitBranch,
    route: '/masterdata/product-families',
    color: 'bg-orange-500',
    stats: computed(() => ({
      total: masterDataStore.productFamilies?.length ?? 0,
      active: masterDataStore.productFamilies?.filter(pf => pf.isActive).length ?? 0,
      status: 'configured'
    }))
  },
  {
    id: 'process-steps',
    title: '标准工序',
    description: '定义标准工序和工艺参数',
    icon: Wrench,
    route: '/masterdata/process-steps',
    color: 'bg-red-500',
    stats: computed(() => ({
      total: masterDataStore.processSteps?.length ?? 0,
      active: masterDataStore.processSteps?.filter(ps => ps.isActive).length ?? 0,
      status: 'defined'
    }))
  },
  {
    id: 'process-segments',
    title: '工艺段',
    description: '设计工艺段和缓冲区配置',
    icon: Route,
    route: '/masterdata/process-segments',
    color: 'bg-indigo-500',
    stats: computed(() => ({
      total: masterDataStore.processSegments?.length ?? 0,
      active: masterDataStore.processSegments?.length ?? 0,
      status: 'designed'
    }))
  },
  {
    id: 'routings',
    title: '工艺路线',
    description: '配置完整的工艺路线',
    icon: GitBranch,
    route: '/masterdata/routings',
    color: 'bg-teal-500',
    stats: computed(() => ({
      total: masterDataStore.routings?.length ?? 0,
      active: masterDataStore.routings?.filter(r => r.isActive).length ?? 0,
      status: 'configured'
    }))
  }
];

// 系统概览统计
const systemStats = computed(() => {
  const totalModules = modules.length;
  const activeModules = modules.filter(m => m.stats.value.total > 0).length;
  const completionRate = totalModules > 0 ? Math.round((activeModules / totalModules) * 100) : 0;
  
  return {
    totalModules,
    activeModules,
    completionRate,
    totalRecords: modules.reduce((sum, m) => sum + m.stats.value.total, 0)
  };
});

// 快速操作
const quickActions = [
  {
    title: '新增设备',
    description: '添加新的生产设备',
    icon: Settings,
    action: () => router.push('/masterdata/equipments'),
    color: 'bg-blue-50 text-blue-600 hover:bg-blue-100'
  },
  {
    title: '新增物料',
    description: '添加新的物料信息',
    icon: Package,
    action: () => router.push('/masterdata/materials'),
    color: 'bg-purple-50 text-purple-600 hover:bg-purple-100'
  },
  {
    title: '配置工序',
    description: '定义标准工序',
    icon: Wrench,
    action: () => router.push('/masterdata/process-steps'),
    color: 'bg-red-50 text-red-600 hover:bg-red-100'
  },
  {
    title: '设计路线',
    description: '配置工艺路线',
    icon: Route,
    action: () => router.push('/masterdata/routings'),
    color: 'bg-teal-50 text-teal-600 hover:bg-teal-100'
  }
];

// 方法
const navigateToModule = (route: string) => {
  router.push(route);
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'running':
    case 'configured':
    case 'managed':
    case 'defined':
    case 'designed':
      return CheckCircle;
    default:
      return AlertTriangle;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'running':
    case 'configured':
    case 'managed':
    case 'defined':
    case 'designed':
      return 'text-green-600';
    default:
      return 'text-yellow-600';
  }
};

// 生命周期
onMounted(async () => {
  // 加载所有模块的数据
  await Promise.all([
    masterDataStore.fetchEquipments(),
    masterDataStore.fetchWorkCenters(),
    masterDataStore.fetchMaterials(),
    masterDataStore.fetchProductFamilies(),
    masterDataStore.fetchProcessSteps(),
    masterDataStore.fetchProcessSegments(),
    masterDataStore.fetchRoutings(),
  ]);
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">主数据管理工作台</h1>
        <p class="text-muted-foreground">统一管理制造基础数据，支持智能制造系统运行</p>
      </div>
      
      <div class="flex items-center gap-2">
        <Badge variant="outline" class="gap-1">
          <Database class="h-3 w-3" />
          {{ systemStats.totalRecords }} 条记录
        </Badge>
        <Badge variant="outline" class="gap-1">
          <Activity class="h-3 w-3" />
          {{ systemStats.activeModules }}/{{ systemStats.totalModules }} 模块活跃
        </Badge>
      </div>
    </div>

    <!-- 系统概览 -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">总模块数</CardTitle>
          <Database class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ systemStats.totalModules }}</div>
          <p class="text-xs text-muted-foreground">主数据管理模块</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">活跃模块</CardTitle>
          <CheckCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ systemStats.activeModules }}</div>
          <p class="text-xs text-muted-foreground">已配置数据的模块</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">完成度</CardTitle>
          <TrendingUp class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ systemStats.completionRate }}%</div>
          <Progress :value="systemStats.completionRate" class="mt-2" />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">数据记录</CardTitle>
          <Package class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ systemStats.totalRecords }}</div>
          <p class="text-xs text-muted-foreground">总数据记录数</p>
        </CardContent>
      </Card>
    </div>

    <!-- 模块卡片 -->
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <Card 
        v-for="module in modules" 
        :key="module.id"
        class="cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]"
        @click="navigateToModule(module.route)"
      >
        <CardHeader class="pb-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div :class="[module.color, 'p-2 rounded-lg text-white']">
                <component :is="module.icon" class="h-5 w-5" />
              </div>
              <div>
                <CardTitle class="text-lg">{{ module.title }}</CardTitle>
                <CardDescription class="text-sm">{{ module.description }}</CardDescription>
              </div>
            </div>
            <ArrowRight class="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold">{{ module.stats.value.total }}</div>
                <div class="text-xs text-muted-foreground">总数</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ module.stats.value.active }}</div>
                <div class="text-xs text-muted-foreground">活跃</div>
              </div>
            </div>
            <div class="flex items-center gap-1">
              <component 
                :is="getStatusIcon(module.stats.value.status)" 
                :class="['h-4 w-4', getStatusColor(module.stats.value.status)]" 
              />
              <span :class="['text-sm', getStatusColor(module.stats.value.status)]">
                {{ module.stats.value.status }}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 快速操作 -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">快速操作</CardTitle>
        <CardDescription>常用的主数据管理操作</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
          <Button
            v-for="action in quickActions"
            :key="action.title"
            variant="ghost"
            :class="[action.color, 'h-auto p-4 flex-col gap-2']"
            @click="action.action"
          >
            <component :is="action.icon" class="h-6 w-6" />
            <div class="text-center">
              <div class="font-medium">{{ action.title }}</div>
              <div class="text-xs opacity-70">{{ action.description }}</div>
            </div>
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Vue Flow 测试 -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">Vue Flow 测试</CardTitle>
        <CardDescription>验证 Vue Flow 组件是否正常工作</CardDescription>
      </CardHeader>
      <CardContent>
        <VueFlowTest />
      </CardContent>
    </Card>
  </div>
</template>
