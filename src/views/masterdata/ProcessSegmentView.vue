<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { ProcessSegment } from '@/types/masterdata';
import ProcessSegmentEditor from '@/components/masterdata/ProcessSegmentEditor.vue';
import ProcessSegmentGanttChart from '@/components/mes/scheduling/ProcessSegmentGanttChart.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Edit, Trash2, Eye, BarChart3 } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isEditorOpen = ref(false);
const isViewerOpen = ref(false);
const activeProcessSegment = ref<ProcessSegment | null>(null);
const activeTab = ref('management');
const isGanttDemoOpen = ref(false);

// 模拟排产数据用于演示
const mockScheduledBatches = ref([
  {
    id: 'batch-001',
    batchName: '钢化玻璃批次A',
    scheduledStartTime: new Date().toISOString(),
    scheduledEndTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
    items: [
      {
        id: 'item-001',
        specifications: { productType: '钢化玻璃' },
        processFlow: [
          {
            stepName: '切割',
            workstation: 'cold_processing',
            workstationGroup: '冷工段',
            estimatedDuration: 120,
            status: 'pending' as const
          },
          {
            stepName: '钢化',
            workstation: 'hot_processing',
            workstationGroup: '热工段',
            estimatedDuration: 180,
            status: 'pending' as const
          }
        ]
      }
    ],
    assignedResources: [],
    estimatedCost: 0
  }
]);

// Computed
const filteredProcessSegments = computed(() => {
  if (!searchQuery.value) return masterDataStore.processSegments;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.processSegments.filter(ps =>
    ps.name.toLowerCase().includes(query) ||
    ps.id.toLowerCase().includes(query)
  );
});

// Methods
const openCreateDialog = () => {
  activeProcessSegment.value = null;
  isEditorOpen.value = true;
};

const openEditDialog = (processSegment: ProcessSegment) => {
  activeProcessSegment.value = { ...processSegment }; // Use a copy to avoid reactivity issues
  isEditorOpen.value = true;
};

const openViewer = (processSegment: ProcessSegment) => {
  activeProcessSegment.value = processSegment;
  isViewerOpen.value = true;
};

const handleEditorSave = async (segment: ProcessSegment) => {
  try {
    if (masterDataStore.processSegments.some(ps => ps.id === segment.id)) {
      // Update existing
      await masterDataStore.updateProcessSegment(segment.id, segment);
      toast.success(`工艺段 "${segment.name}" 更新成功`);
    } else {
      // Create new
      await masterDataStore.createProcessSegment(segment);
      toast.success(`工艺段 "${segment.name}" 创建成功`);
    }
    isEditorOpen.value = false;
    activeProcessSegment.value = null;
  } catch (error) {
    toast.error('保存工艺段失败');
    console.error('保存工艺段失败:', error);
  }
};

const handleDelete = async (processSegment: ProcessSegment) => {
  if (!confirm(`确定要删除工艺段 "${processSegment.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteProcessSegment(processSegment.id);
    toast.success('工艺段删除成功');
  } catch (error) {
    toast.error('工艺段删除失败');
    console.error('删除工艺段失败:', error);
  }
};

const openGanttDemo = () => {
  isGanttDemoOpen.value = true;
};

const handleTaskClick = (task: any) => {
  console.log('任务点击:', task);
  toast.info(`点击了任务: ${task.name}`);
};

const handleViewChange = (viewMode: string) => {
  console.log('视图切换:', viewMode);
  toast.info(`切换到${viewMode}视图`);
};

// Lifecycle
onMounted(async () => {
  await masterDataStore.fetchProcessSegments();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">工艺段管理</h1>
        <p class="text-muted-foreground">通过可视化编辑器，将标准工序组合成可复用的工艺段，支持排产甘特图演示。</p>
      </div>

      <div class="flex items-center gap-2">
        <Button @click="openGanttDemo" variant="outline" class="gap-2">
          <BarChart3 class="h-4 w-4" />
          排产甘特图演示
        </Button>
        <Button @click="openCreateDialog" class="gap-2">
          <Plus class="h-4 w-4" />
          新增工艺段
        </Button>
      </div>
    </div>

    <!-- 标签页 -->
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="management">工艺段管理</TabsTrigger>
        <TabsTrigger value="scheduling">排产配置</TabsTrigger>
      </TabsList>

      <!-- 工艺段管理标签页 -->
      <TabsContent value="management" class="space-y-6">
        <!-- Search -->
        <Card>
          <CardContent class="pt-6">
            <div class="relative">
              <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="搜索工艺段名称或编号..."
                class="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        <!-- Process Segments Table -->
        <Card>
          <CardHeader>
            <CardTitle>工艺段列表</CardTitle>
            <CardDescription>
              共 {{ filteredProcessSegments.length }} 个工艺段
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div class="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>编号</TableHead>
                    <TableHead>名称</TableHead>
                    <TableHead>节点数</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead class="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-if="masterDataStore.loading">
                    <TableCell colspan="5" class="text-center py-8">加载中...</TableCell>
                  </TableRow>
                  <TableRow v-else-if="filteredProcessSegments.length === 0">
                    <TableCell colspan="5" class="text-center py-8 text-muted-foreground">
                      暂无工艺段数据
                    </TableCell>
                  </TableRow>
                  <TableRow v-else v-for="segment in filteredProcessSegments" :key="segment.id">
                    <TableCell class="font-mono text-sm">{{ segment.id }}</TableCell>
                    <TableCell class="font-medium">{{ segment.name }}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {{ segment.nodes.length }} 个节点
                      </Badge>
                    </TableCell>
                    <TableCell class="max-w-[300px] truncate text-muted-foreground">
                      {{ segment.description || '-' }}
                    </TableCell>
                    <TableCell class="text-right">
                      <div class="flex items-center justify-end gap-2">
                        <Button variant="ghost" size="icon" @click="openViewer(segment)">
                          <Eye class="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" @click="openEditDialog(segment)">
                          <Edit class="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" @click="handleDelete(segment)" class="text-destructive hover:text-destructive">
                          <Trash2 class="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 排产配置标签页 -->
      <TabsContent value="scheduling" class="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>工艺段排产配置</CardTitle>
            <CardDescription>
              配置工艺段在排产甘特图中的显示和行为
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-4">
                <h4 class="font-medium">排产视图配置</h4>
                <div class="space-y-2">
                  <div class="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div class="font-medium text-sm">按工艺段组织</div>
                      <div class="text-xs text-muted-foreground">推荐：符合玻璃深加工行业特点</div>
                    </div>
                    <Badge variant="default">推荐</Badge>
                  </div>
                  <div class="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div class="font-medium text-sm">支持下钻查看</div>
                      <div class="text-xs text-muted-foreground">点击工艺段可查看内部设备详情</div>
                    </div>
                    <Badge variant="secondary">已启用</Badge>
                  </div>
                  <div class="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div class="font-medium text-sm">瓶颈识别</div>
                      <div class="text-xs text-muted-foreground">自动标识利用率>90%的工艺段</div>
                    </div>
                    <Badge variant="secondary">已启用</Badge>
                  </div>
                </div>
              </div>

              <div class="space-y-4">
                <h4 class="font-medium">工艺段映射</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between p-2 bg-muted rounded">
                    <span>冷工段</span>
                    <span class="text-muted-foreground">切割、磨边、钻孔、清洗</span>
                  </div>
                  <div class="flex justify-between p-2 bg-muted rounded">
                    <span>热工段</span>
                    <span class="text-muted-foreground">钢化、弯钢化</span>
                  </div>
                  <div class="flex justify-between p-2 bg-muted rounded">
                    <span>夹胶工段</span>
                    <span class="text-muted-foreground">PVB夹胶、EVA夹胶</span>
                  </div>
                  <div class="flex justify-between p-2 bg-muted rounded">
                    <span>中空工段</span>
                    <span class="text-muted-foreground">中空组装、充气</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>

    <!-- Editor Dialog (for Create and Edit) -->
    <Dialog v-model:open="isEditorOpen">
      <DialogContent class="max-w-7xl w-full h-[90vh]">
        <DialogHeader>
          <DialogTitle>{{ activeProcessSegment ? '编辑' : '新增' }}工艺段</DialogTitle>
          <DialogDescription>
            拖拽节点、连接流程，定义工艺段。
          </DialogDescription>
        </DialogHeader>
        <div class="h-[calc(90vh-100px)]">
          <ProcessSegmentEditor
            :process-segment="activeProcessSegment"
            @save="handleEditorSave"
            @cancel="isEditorOpen = false"
          />
        </div>
      </DialogContent>
    </Dialog>

    <!-- Viewer Dialog -->
    <Dialog v-model:open="isViewerOpen">
      <DialogContent class="max-w-7xl w-full h-[90vh]">
        <DialogHeader>
          <DialogTitle>查看工艺段: {{ activeProcessSegment?.name }}</DialogTitle>
        </DialogHeader>
        <div class="h-[calc(90vh-100px)]">
          <ProcessSegmentEditor
            :process-segment="activeProcessSegment"
            :readonly="true"
            @cancel="isViewerOpen = false"
          />
        </div>
      </DialogContent>
    </Dialog>

    <!-- 甘特图演示对话框 -->
    <Dialog v-model:open="isGanttDemoOpen">
      <DialogContent class="max-w-7xl w-full h-[90vh]">
        <DialogHeader>
          <DialogTitle>工艺段排产甘特图演示</DialogTitle>
          <DialogDescription>
            展示按工艺段组织的排产甘特图，支持下钻查看和视图切换
          </DialogDescription>
        </DialogHeader>
        <div class="h-[calc(90vh-120px)]">
          <ProcessSegmentGanttChart
            :scheduled-batches="mockScheduledBatches"
            :enable-drill-down="true"
            @task-click="handleTaskClick"
            @view-change="handleViewChange"
          />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>