<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { ProductFamily, ConfigurationAttribute } from '@/types/masterdata';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Plus, Search, Edit, Trash2 } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isCreateDialogOpen = ref(false);
const isEditDialogOpen = ref(false);
const editingProductFamily = ref<ProductFamily | null>(null);

// Form data
const formData = ref<Partial<ProductFamily>>({
  name: '',
  category: '',
  attributes: [],
  dependencyRules: [],
  description: '',
  isActive: true
});

// Attribute form for adding new attributes
const newAttribute = ref<Partial<ConfigurationAttribute>>({
  id: '',
  label: '',
  type: 'number',
  defaultValue: ''
});

// Options
const attributeTypes = [
  { value: 'number', label: '数值型' },
  { value: 'string', label: '文本型' },
  { value: 'boolean', label: '布尔型' },
  { value: 'select', label: '选择型' }
];

const productCategories = [
  { value: 'insulated_glass', label: '中空玻璃' },
  { value: 'laminated_glass', label: '夹胶玻璃' },
  { value: 'tempered_glass', label: '钢化玻璃' },
  { value: 'curtain_wall', label: '幕墙系统' },
  { value: 'window_door', label: '门窗系统' },
  { value: 'special_glass', label: '特种玻璃' }
];

// Computed
const filteredProductFamilies = computed(() => {
  if (!searchQuery.value) return masterDataStore.productFamilies;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.productFamilies.filter(pf =>
    pf.name.toLowerCase().includes(query) ||
    pf.id.toLowerCase().includes(query) ||
    pf.category?.toLowerCase().includes(query)
  );
});

const getCategoryLabel = (category: string) => {
  const option = productCategories.find(opt => opt.value === category);
  return option?.label || category;
};

const getAttributeTypeLabel = (type: string) => {
  const option = attributeTypes.find(opt => opt.value === type);
  return option?.label || type;
};

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    category: '',
    attributes: [],
    dependencyRules: [],
    description: '',
    isActive: true
  };
};

const resetNewAttribute = () => {
  newAttribute.value = {
    id: '',
    label: '',
    type: 'number',
    defaultValue: ''
  };
};

const addAttribute = () => {
  if (!newAttribute.value.id || !newAttribute.value.label) {
    toast.error('请输入属性ID和标签');
    return;
  }

  const attribute: ConfigurationAttribute = {
    id: newAttribute.value.id,
    label: newAttribute.value.label,
    type: newAttribute.value.type || 'number',
    defaultValue: newAttribute.value.defaultValue,
    options: newAttribute.value.type === 'select' ? [] : undefined
  };

  if (!formData.value.attributes) {
    formData.value.attributes = [];
  }

  formData.value.attributes.push(attribute);
  resetNewAttribute();
  toast.success('属性添加成功');
};

const removeAttribute = (index: number) => {
  if (formData.value.attributes) {
    formData.value.attributes.splice(index, 1);
    toast.success('属性删除成功');
  }
};

const openCreateDialog = () => {
  resetForm();
  isCreateDialogOpen.value = true;
};

const openEditDialog = (productFamily: ProductFamily) => {
  editingProductFamily.value = productFamily;
  formData.value = {
    ...productFamily,
    attributes: [...(productFamily.attributes || [])]
  };
  isEditDialogOpen.value = true;
};

const handleCreate = async () => {
  try {
    if (!formData.value.name) {
      toast.error('请填写产品族名称');
      return;
    }

    await masterDataStore.createProductFamily(formData.value as Omit<ProductFamily, 'id' | 'createdAt' | 'updatedAt'>);
    toast.success('产品族创建成功');
    isCreateDialogOpen.value = false;
    resetForm();
  } catch (error) {
    toast.error('产品族创建失败');
  }
};

const handleUpdate = async () => {
  try {
    if (!editingProductFamily.value || !formData.value.name) {
      toast.error('请填写产品族名称');
      return;
    }

    await masterDataStore.updateProductFamily(editingProductFamily.value.id, formData.value);
    toast.success('产品族更新成功');
    isEditDialogOpen.value = false;
    editingProductFamily.value = null;
    resetForm();
  } catch (error) {
    toast.error('产品族更新失败');
  }
};

const handleDelete = async (productFamily: ProductFamily) => {
  if (!confirm(`确定要删除产品族 "${productFamily.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteProductFamily(productFamily.id);
    toast.success('产品族删除成功');
  } catch (error) {
    toast.error('产品族删除失败');
  }
};

// Lifecycle
onMounted(() => {
  masterDataStore.fetchProductFamilies();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">产品族管理</h1>
        <p class="text-muted-foreground">管理产品族定义和配置属性，支持参数化定制</p>
      </div>

      <Button @click="openCreateDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增产品族
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">搜索和筛选</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="搜索产品族名称、编号或分类..."
                class="pl-10"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Product Families Table -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">产品族列表</CardTitle>
        <CardDescription>
          共 {{ filteredProductFamilies.length }} 个产品族
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>产品族编号</TableHead>
                <TableHead>产品族名称</TableHead>
                <TableHead>分类</TableHead>
                <TableHead>配置属性</TableHead>
                <TableHead>依赖规则</TableHead>
                <TableHead>状态</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="7" class="text-center py-8">
                  <div class="flex items-center justify-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
              <TableRow v-else-if="filteredProductFamilies.length === 0">
                <TableCell colspan="7" class="text-center py-8 text-muted-foreground">
                  暂无产品族数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="productFamily in filteredProductFamilies" :key="productFamily.id">
                <TableCell class="font-medium">{{ productFamily.id }}</TableCell>
                <TableCell>{{ productFamily.name }}</TableCell>
                <TableCell>
                  <Badge variant="outline">{{ getCategoryLabel(productFamily.category || '') }}</Badge>
                </TableCell>
                <TableCell>
                  <div class="flex gap-1 flex-wrap">
                    <Badge
                      v-for="attr in productFamily.attributes?.slice(0, 3)"
                      :key="attr.id"
                      variant="secondary"
                      class="text-xs"
                    >
                      {{ attr.label }}
                    </Badge>
                    <Badge
                      v-if="(productFamily.attributes?.length || 0) > 3"
                      variant="secondary"
                      class="text-xs"
                    >
                      +{{ (productFamily.attributes?.length || 0) - 3 }}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell>
                  <span class="text-sm text-muted-foreground">
                    {{ productFamily.dependencyRules?.length || 0 }} 条规则
                  </span>
                </TableCell>
                <TableCell>
                  <Badge :variant="productFamily.isActive !== false ? 'default' : 'secondary'">
                    {{ productFamily.isActive !== false ? '启用' : '禁用' }}
                  </Badge>
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openEditDialog(productFamily)"
                      class="h-8 w-8 p-0"
                    >
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="handleDelete(productFamily)"
                      class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- Create Product Family Dialog -->
    <Dialog v-model:open="isCreateDialogOpen">
      <DialogContent class="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>新增产品族</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4 max-h-[70vh] overflow-y-auto">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">产品族名称 *</Label>
              <Input
                id="name"
                v-model="formData.name"
                placeholder="请输入产品族名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="category">产品分类</Label>
              <Select v-model="formData.category">
                <SelectTrigger>
                  <SelectValue placeholder="选择产品分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in productCategories" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="space-y-2">
            <Label for="description">产品族描述</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              placeholder="请输入产品族描述"
              rows="3"
            />
          </div>

          <Separator />

          <!-- Configuration Attributes Section -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <Label class="text-base font-medium">配置属性</Label>
              <Button type="button" variant="outline" size="sm" @click="addAttribute" class="gap-2">
                <Plus class="h-4 w-4" />
                添加属性
              </Button>
            </div>

            <!-- Add New Attribute Form -->
            <Card class="p-4">
              <div class="grid grid-cols-4 gap-4">
                <div class="space-y-2">
                  <Label for="attr-id">属性ID</Label>
                  <Input
                    id="attr-id"
                    v-model="newAttribute.id"
                    placeholder="如: width"
                  />
                </div>
                <div class="space-y-2">
                  <Label for="attr-label">属性标签</Label>
                  <Input
                    id="attr-label"
                    v-model="newAttribute.label"
                    placeholder="如: 总宽度"
                  />
                </div>
                <div class="space-y-2">
                  <Label for="attr-type">属性类型</Label>
                  <Select v-model="newAttribute.type">
                    <SelectTrigger>
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="option in attributeTypes" :key="option.value" :value="option.value">
                        {{ option.label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div class="space-y-2">
                  <Label for="attr-default">默认值</Label>
                  <Input
                    id="attr-default"
                    v-model="newAttribute.defaultValue"
                    placeholder="默认值"
                  />
                </div>
              </div>
            </Card>

            <!-- Existing Attributes List -->
            <div v-if="formData.attributes && formData.attributes.length > 0" class="space-y-2">
              <div
                v-for="(attr, index) in formData.attributes"
                :key="index"
                class="flex items-center justify-between p-3 border rounded-lg"
              >
                <div class="flex items-center gap-4">
                  <Badge variant="outline">{{ getAttributeTypeLabel(attr.type) }}</Badge>
                  <div>
                    <div class="font-medium">{{ attr.label }}</div>
                    <div class="text-sm text-muted-foreground">ID: {{ attr.id }}</div>
                  </div>
                  <div v-if="attr.defaultValue" class="text-sm text-muted-foreground">
                    默认: {{ attr.defaultValue }}
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="removeAttribute(index)"
                  class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                >
                  <Trash2 class="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isCreateDialogOpen = false">
            取消
          </Button>
          <Button @click="handleCreate" :disabled="masterDataStore.loading">
            创建
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Edit Product Family Dialog -->
    <Dialog v-model:open="isEditDialogOpen">
      <DialogContent class="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>编辑产品族</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4 max-h-[70vh] overflow-y-auto">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-name">产品族名称 *</Label>
              <Input
                id="edit-name"
                v-model="formData.name"
                placeholder="请输入产品族名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-category">产品分类</Label>
              <Select v-model="formData.category">
                <SelectTrigger>
                  <SelectValue placeholder="选择产品分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in productCategories" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="space-y-2">
            <Label for="edit-description">产品族描述</Label>
            <Textarea
              id="edit-description"
              v-model="formData.description"
              placeholder="请输入产品族描述"
              rows="3"
            />
          </div>

          <Separator />

          <!-- Configuration Attributes Section -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <Label class="text-base font-medium">配置属性</Label>
              <Button type="button" variant="outline" size="sm" @click="addAttribute" class="gap-2">
                <Plus class="h-4 w-4" />
                添加属性
              </Button>
            </div>

            <!-- Add New Attribute Form -->
            <Card class="p-4">
              <div class="grid grid-cols-4 gap-4">
                <div class="space-y-2">
                  <Label for="edit-attr-id">属性ID</Label>
                  <Input
                    id="edit-attr-id"
                    v-model="newAttribute.id"
                    placeholder="如: width"
                  />
                </div>
                <div class="space-y-2">
                  <Label for="edit-attr-label">属性标签</Label>
                  <Input
                    id="edit-attr-label"
                    v-model="newAttribute.label"
                    placeholder="如: 总宽度"
                  />
                </div>
                <div class="space-y-2">
                  <Label for="edit-attr-type">属性类型</Label>
                  <Select v-model="newAttribute.type">
                    <SelectTrigger>
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="option in attributeTypes" :key="option.value" :value="option.value">
                        {{ option.label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div class="space-y-2">
                  <Label for="edit-attr-default">默认值</Label>
                  <Input
                    id="edit-attr-default"
                    v-model="newAttribute.defaultValue"
                    placeholder="默认值"
                  />
                </div>
              </div>
            </Card>

            <!-- Existing Attributes List -->
            <div v-if="formData.attributes && formData.attributes.length > 0" class="space-y-2">
              <div
                v-for="(attr, index) in formData.attributes"
                :key="index"
                class="flex items-center justify-between p-3 border rounded-lg"
              >
                <div class="flex items-center gap-4">
                  <Badge variant="outline">{{ getAttributeTypeLabel(attr.type) }}</Badge>
                  <div>
                    <div class="font-medium">{{ attr.label }}</div>
                    <div class="text-sm text-muted-foreground">ID: {{ attr.id }}</div>
                  </div>
                  <div v-if="attr.defaultValue" class="text-sm text-muted-foreground">
                    默认: {{ attr.defaultValue }}
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="removeAttribute(index)"
                  class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                >
                  <Trash2 class="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isEditDialogOpen = false">
            取消
          </Button>
          <Button @click="handleUpdate" :disabled="masterDataStore.loading">
            更新
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>
