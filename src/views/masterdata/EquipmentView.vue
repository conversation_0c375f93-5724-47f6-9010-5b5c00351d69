<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { Equipment } from '@/types/masterdata';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Search, Edit, Trash2, Settings, Eye } from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import EquipmentDetailDialog from '@/components/masterdata/EquipmentDetailDialog.vue';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isCreateDialogOpen = ref(false);
const isEditDialogOpen = ref(false);
const editingEquipment = ref<Equipment | null>(null);

// 新的增强弹窗状态
const isDetailDialogOpen = ref(false);
const selectedEquipment = ref<Equipment | null>(null);
const dialogMode = ref<'view' | 'edit' | 'create'>('view');

// Form data
const formData = ref<Partial<Equipment>>({
  name: '',
  model: '',
  status: 'idle',
  location: '',
  assetNumber: '',
  parameters: {},
  description: ''
});

// Computed
const filteredEquipments = computed(() => {
  if (!searchQuery.value) return masterDataStore.equipments;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.equipments.filter(eq =>
    eq.name.toLowerCase().includes(query) ||
    eq.model.toLowerCase().includes(query) ||
    eq.id.toLowerCase().includes(query) ||
    eq.location?.toLowerCase().includes(query)
  );
});

const statusOptions = [
  { value: 'running', label: '运行中', variant: 'default' },
  { value: 'idle', label: '空闲', variant: 'secondary' },
  { value: 'fault', label: '故障', variant: 'destructive' },
  { value: 'maintenance', label: '维护中', variant: 'outline' }
] as const;

const getStatusBadge = (status: string) => {
  const option = statusOptions.find(opt => opt.value === status);
  return option || { value: status, label: status, variant: 'secondary' };
};

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    model: '',
    status: 'idle',
    location: '',
    assetNumber: '',
    parameters: {},
    description: ''
  };
};

const openCreateDialog = () => {
  resetForm();
  isCreateDialogOpen.value = true;
};

const openEditDialog = (equipment: Equipment) => {
  editingEquipment.value = equipment;
  formData.value = { ...equipment };
  isEditDialogOpen.value = true;
};

const handleCreate = async () => {
  try {
    if (!formData.value.name || !formData.value.model) {
      toast.error('请填写必填字段');
      return;
    }

    await masterDataStore.createEquipment(formData.value as Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>);
    toast.success('设备创建成功');
    isCreateDialogOpen.value = false;
    resetForm();
  } catch (error) {
    toast.error('设备创建失败');
  }
};

const handleUpdate = async () => {
  try {
    if (!editingEquipment.value || !formData.value.name || !formData.value.model) {
      toast.error('请填写必填字段');
      return;
    }

    await masterDataStore.updateEquipment(editingEquipment.value.id, formData.value);
    toast.success('设备更新成功');
    isEditDialogOpen.value = false;
    editingEquipment.value = null;
    resetForm();
  } catch (error) {
    toast.error('设备更新失败');
  }
};

const handleDelete = async (equipment: Equipment) => {
  if (!confirm(`确定要删除设备 "${equipment.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteEquipment(equipment.id);
    toast.success('设备删除成功');
  } catch (error) {
    toast.error('设备删除失败');
  }
};

// 新的增强弹窗方法
const openDetailDialog = (equipment: Equipment, mode: 'view' | 'edit' = 'view') => {
  selectedEquipment.value = equipment;
  dialogMode.value = mode;
  isDetailDialogOpen.value = true;
};

const openCreateDetailDialog = () => {
  selectedEquipment.value = null;
  dialogMode.value = 'create';
  isDetailDialogOpen.value = true;
};

const handleDetailDialogSave = async (equipmentData: Equipment) => {
  try {
    if (dialogMode.value === 'create') {
      await masterDataStore.createEquipment(equipmentData);
      toast.success('设备创建成功');
    } else {
      await masterDataStore.updateEquipment(equipmentData.id, equipmentData);
      toast.success('设备更新成功');
    }
    isDetailDialogOpen.value = false;
  } catch (error) {
    toast.error(dialogMode.value === 'create' ? '设备创建失败' : '设备更新失败');
  }
};

const handleEquipmentDuplicate = async (equipmentData: Equipment) => {
  try {
    await masterDataStore.createEquipment(equipmentData);
    toast.success('设备复制成功');
    isDetailDialogOpen.value = false;
  } catch (error) {
    toast.error('设备复制失败');
  }
};

// Lifecycle
onMounted(() => {
  masterDataStore.fetchEquipments();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">设备管理</h1>
        <p class="text-muted-foreground">管理生产设备档案和状态信息</p>
      </div>

      <Button @click="openCreateDetailDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增设备
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">搜索和筛选</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="搜索设备名称、型号、编号或位置..."
                class="pl-10"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Equipment Table -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">设备列表</CardTitle>
        <CardDescription>
          共 {{ filteredEquipments.length }} 台设备
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>设备编号</TableHead>
                <TableHead>设备名称</TableHead>
                <TableHead>型号</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>位置</TableHead>
                <TableHead>资产编号</TableHead>
                <TableHead>效率</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="8" class="text-center py-8">
                  <div class="flex items-center justify-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
              <TableRow v-else-if="filteredEquipments.length === 0">
                <TableCell colspan="8" class="text-center py-8 text-muted-foreground">
                  暂无设备数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="equipment in filteredEquipments" :key="equipment.id">
                <TableCell class="font-medium">{{ equipment.id }}</TableCell>
                <TableCell>{{ equipment.name }}</TableCell>
                <TableCell>{{ equipment.model }}</TableCell>
                <TableCell>
                  <Badge :variant="getStatusBadge(equipment.status).variant">
                    {{ getStatusBadge(equipment.status).label }}
                  </Badge>
                </TableCell>
                <TableCell>{{ equipment.location || '-' }}</TableCell>
                <TableCell>{{ equipment.assetNumber || '-' }}</TableCell>
                <TableCell>
                  <span v-if="equipment.parameters?.efficiency">
                    {{ equipment.parameters.efficiency }}%
                  </span>
                  <span v-else class="text-muted-foreground">-</span>
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openDetailDialog(equipment, 'view')"
                      class="h-8 w-8 p-0"
                      title="查看详情"
                    >
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openDetailDialog(equipment, 'edit')"
                      class="h-8 w-8 p-0"
                      title="编辑设备"
                    >
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="handleDelete(equipment)"
                      class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      title="删除设备"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- Create Equipment Dialog -->
    <Dialog v-model:open="isCreateDialogOpen">
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>新增设备</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">设备名称 *</Label>
              <Input
                id="name"
                v-model="formData.name"
                placeholder="请输入设备名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="model">设备型号 *</Label>
              <Input
                id="model"
                v-model="formData.model"
                placeholder="请输入设备型号"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="status">设备状态</Label>
              <Select v-model="formData.status">
                <SelectTrigger>
                  <SelectValue placeholder="选择设备状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in statusOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="space-y-2">
              <Label for="location">设备位置</Label>
              <Input
                id="location"
                v-model="formData.location"
                placeholder="请输入设备位置"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="assetNumber">资产编号</Label>
            <Input
              id="assetNumber"
              v-model="formData.assetNumber"
              placeholder="请输入资产编号"
            />
          </div>

          <div class="space-y-2">
            <Label for="description">设备描述</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              placeholder="请输入设备描述"
              rows="3"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isCreateDialogOpen = false">
            取消
          </Button>
          <Button @click="handleCreate" :disabled="masterDataStore.loading">
            创建
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Edit Equipment Dialog -->
    <Dialog v-model:open="isEditDialogOpen">
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>编辑设备</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-name">设备名称 *</Label>
              <Input
                id="edit-name"
                v-model="formData.name"
                placeholder="请输入设备名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-model">设备型号 *</Label>
              <Input
                id="edit-model"
                v-model="formData.model"
                placeholder="请输入设备型号"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-status">设备状态</Label>
              <Select v-model="formData.status">
                <SelectTrigger>
                  <SelectValue placeholder="选择设备状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in statusOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="space-y-2">
              <Label for="edit-location">设备位置</Label>
              <Input
                id="edit-location"
                v-model="formData.location"
                placeholder="请输入设备位置"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="edit-assetNumber">资产编号</Label>
            <Input
              id="edit-assetNumber"
              v-model="formData.assetNumber"
              placeholder="请输入资产编号"
            />
          </div>

          <div class="space-y-2">
            <Label for="edit-description">设备描述</Label>
            <Textarea
              id="edit-description"
              v-model="formData.description"
              placeholder="请输入设备描述"
              rows="3"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isEditDialogOpen = false">
            取消
          </Button>
          <Button @click="handleUpdate" :disabled="masterDataStore.loading">
            更新
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Enhanced Equipment Detail Dialog -->
    <EquipmentDetailDialog
      v-model:open="isDetailDialogOpen"
      :equipment="selectedEquipment"
      :mode="dialogMode"
      @save="handleDetailDialogSave"
      @duplicate="handleEquipmentDuplicate"
    />
  </div>
</template>
