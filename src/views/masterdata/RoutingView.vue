<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { Routing } from '@/types/masterdata';
import RoutingEditor from '@/components/masterdata/RoutingEditor.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Search, Edit, Trash2, Eye } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isEditorOpen = ref(false);
const isViewerOpen = ref(false);
const activeRouting = ref<Routing | null>(null);

// Computed
const filteredRoutings = computed(() => {
  if (!searchQuery.value) return masterDataStore.routings;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.routings.filter(r =>
    r.name.toLowerCase().includes(query) ||
    r.id.toLowerCase().includes(query)
  );
});

const getProductFamilyName = (id: string) => {
  return masterDataStore.productFamilies.find(pf => pf.id === id)?.name || '未知';
};

// Methods
const openCreateDialog = () => {
  activeRouting.value = null;
  isEditorOpen.value = true;
};

const openEditDialog = (routing: Routing) => {
  activeRouting.value = { ...routing };
  isEditorOpen.value = true;
};

const openViewer = (routing: Routing) => {
  activeRouting.value = routing;
  isViewerOpen.value = true;
};

const handleEditorSave = async (routing: Routing) => {
  try {
    if (masterDataStore.routings.some(r => r.id === routing.id)) {
      await masterDataStore.updateRouting(routing.id, routing);
      toast.success(`工艺路线 "${routing.name}" 更新成功`);
    } else {
      await masterDataStore.createRouting(routing);
      toast.success(`工艺路线 "${routing.name}" 创建成功`);
    }
    isEditorOpen.value = false;
    activeRouting.value = null;
  } catch (error) {
    toast.error('保存工艺路线失败');
    console.error(error);
  }
};

const handleDelete = async (routing: Routing) => {
  if (!confirm(`确定要删除工艺路线 "${routing.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteRouting(routing.id);
    toast.success('工艺路线删除成功');
  } catch (error) {
    toast.error('工艺路线删除失败');
  }
};

// Lifecycle
onMounted(async () => {
  await masterDataStore.fetchRoutings();
  await masterDataStore.fetchProductFamilies(); // Needed for display
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">工艺路线管理</h1>
        <p class="text-muted-foreground">组合工艺段，定义产品族的完整生产流程。</p>
      </div>
      <Button @click="openCreateDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增工艺路线
      </Button>
    </div>

    <!-- Search -->
    <Card>
      <CardContent class="pt-6">
        <div class="relative">
          <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input v-model="searchQuery" placeholder="搜索工艺路线名称或编号..." class="pl-10" />
        </div>
      </CardContent>
    </Card>

    <!-- Routings Table -->
    <Card>
      <CardHeader>
        <CardTitle>工艺路线列表</CardTitle>
        <CardDescription>共 {{ filteredRoutings.length }} 条工艺路线</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>编号</TableHead>
                <TableHead>名称</TableHead>
                <TableHead>关联产品族</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>节点数</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="6" class="text-center py-8">加载中...</TableCell>
              </TableRow>
              <TableRow v-else-if="filteredRoutings.length === 0">
                <TableCell colspan="6" class="text-center py-8 text-muted-foreground">
                  暂无工艺路线数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="routing in filteredRoutings" :key="routing.id">
                <TableCell class="font-mono text-sm">{{ routing.id }}</TableCell>
                <TableCell class="font-medium">{{ routing.name }}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{{ getProductFamilyName(routing.productFamilyId) }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="routing.isActive ? 'default' : 'outline'">
                    {{ routing.isActive ? '激活' : '草稿' }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{{ routing.nodes.length }} 个节点</Badge>
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button variant="ghost" size="icon" @click="openViewer(routing)">
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" @click="openEditDialog(routing)">
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" @click="handleDelete(routing)" class="text-destructive hover:text-destructive">
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- Editor Dialog -->
    <Dialog v-model:open="isEditorOpen">
      <DialogContent class="max-w-7xl w-full h-[90vh]">
        <DialogHeader>
          <DialogTitle>{{ activeRouting ? '编辑' : '新增' }}工艺路线</DialogTitle>
        </DialogHeader>
        <div class="h-[calc(90vh-100px)]">
          <RoutingEditor :routing="activeRouting" @save="handleEditorSave" @cancel="isEditorOpen = false" />
        </div>
      </DialogContent>
    </Dialog>

    <!-- Viewer Dialog -->
    <Dialog v-model:open="isViewerOpen">
      <DialogContent class="max-w-7xl w-full h-[90vh]">
        <DialogHeader>
          <DialogTitle>查看工艺路线: {{ activeRouting?.name }}</DialogTitle>
        </DialogHeader>
        <div class="h-[calc(90vh-100px)]">
          <RoutingEditor :routing="activeRouting" :readonly="true" @cancel="isViewerOpen = false" />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>