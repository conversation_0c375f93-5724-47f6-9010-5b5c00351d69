<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { ProcessStep } from '@/types/masterdata';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Search, Edit, Trash2 } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isCreateDialogOpen = ref(false);
const isEditDialogOpen = ref(false);
const editingProcessStep = ref<ProcessStep | null>(null);

// Form data
const formData = ref<Partial<ProcessStep>>({
  name: '',
  type: 'internal',
  category: '',
  assigneeIds: [],
  processingTimeFormula: '',
  setupTimeFormula: '',
  requiredSkills: [],
  safetyRequirements: [],
  description: '',
  isActive: true
});

// Options
const processStepTypes = [
  { value: 'internal', label: '内部工序' },
  { value: 'external', label: '外协工序' }
];

const processCategories = [
  { value: 'cutting', label: '切割加工' },
  { value: 'edging', label: '磨边加工' },
  { value: 'washing', label: '清洗工序' },
  { value: 'tempering', label: '钢化工序' },
  { value: 'laminating', label: '夹胶工序' },
  { value: 'assembly', label: '合片工序' },
  { value: 'packaging', label: '包装工序' },
  { value: 'inspection', label: '检验工序' }
];

// Computed
const filteredProcessSteps = computed(() => {
  if (!searchQuery.value) return masterDataStore.processSteps;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.processSteps.filter(ps =>
    ps.name.toLowerCase().includes(query) ||
    ps.id.toLowerCase().includes(query) ||
    ps.type.toLowerCase().includes(query) ||
    ps.category?.toLowerCase().includes(query)
  );
});

const getTypeLabel = (type: string) => {
  const option = processStepTypes.find(opt => opt.value === type);
  return option?.label || type;
};

const getCategoryLabel = (category: string) => {
  const option = processCategories.find(opt => opt.value === category);
  return option?.label || category;
};

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    type: 'internal',
    category: '',
    assigneeIds: [],
    processingTimeFormula: '',
    setupTimeFormula: '',
    requiredSkills: [],
    safetyRequirements: [],
    description: '',
    isActive: true
  };
};

const openCreateDialog = () => {
  resetForm();
  isCreateDialogOpen.value = true;
};

const openEditDialog = (processStep: ProcessStep) => {
  editingProcessStep.value = processStep;
  formData.value = { ...processStep };
  isEditDialogOpen.value = true;
};

const handleCreate = async () => {
  try {
    if (!formData.value.name || !formData.value.type) {
      toast.error('请填写必填字段');
      return;
    }

    await masterDataStore.createProcessStep(formData.value as Omit<ProcessStep, 'id' | 'createdAt' | 'updatedAt'>);
    toast.success('工序创建成功');
    isCreateDialogOpen.value = false;
    resetForm();
  } catch (error) {
    toast.error('工序创建失败');
  }
};

const handleUpdate = async () => {
  try {
    if (!editingProcessStep.value || !formData.value.name || !formData.value.type) {
      toast.error('请填写必填字段');
      return;
    }

    await masterDataStore.updateProcessStep(editingProcessStep.value.id, formData.value);
    toast.success('工序更新成功');
    isEditDialogOpen.value = false;
    editingProcessStep.value = null;
    resetForm();
  } catch (error) {
    toast.error('工序更新失败');
  }
};

const handleDelete = async (processStep: ProcessStep) => {
  if (!confirm(`确定要删除工序 "${processStep.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteProcessStep(processStep.id);
    toast.success('工序删除成功');
  } catch (error) {
    toast.error('工序删除失败');
  }
};

// Lifecycle
onMounted(() => {
  masterDataStore.fetchProcessSteps();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">标准工序</h1>
        <p class="text-muted-foreground">管理生产工序定义和工艺参数</p>
      </div>

      <Button @click="openCreateDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增工序
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">搜索和筛选</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="搜索工序名称、编号、类型或分类..."
                class="pl-10"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Process Steps Table -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">工序列表</CardTitle>
        <CardDescription>
          共 {{ filteredProcessSteps.length }} 个工序
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>工序编号</TableHead>
                <TableHead>工序名称</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>分类</TableHead>
                <TableHead>工时公式</TableHead>
                <TableHead>状态</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="7" class="text-center py-8">
                  <div class="flex items-center justify-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
              <TableRow v-else-if="filteredProcessSteps.length === 0">
                <TableCell colspan="7" class="text-center py-8 text-muted-foreground">
                  暂无工序数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="processStep in filteredProcessSteps" :key="processStep.id">
                <TableCell class="font-medium">{{ processStep.id }}</TableCell>
                <TableCell>{{ processStep.name }}</TableCell>
                <TableCell>
                  <Badge :variant="processStep.type === 'internal' ? 'default' : 'secondary'">
                    {{ getTypeLabel(processStep.type) }}
                  </Badge>
                </TableCell>
                <TableCell>{{ getCategoryLabel(processStep.category || '') }}</TableCell>
                <TableCell class="font-mono text-sm">{{ processStep.processingTimeFormula || '-' }}</TableCell>
                <TableCell>
                  <Badge :variant="processStep.isActive !== false ? 'default' : 'secondary'">
                    {{ processStep.isActive !== false ? '启用' : '禁用' }}
                  </Badge>
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openEditDialog(processStep)"
                      class="h-8 w-8 p-0"
                    >
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="handleDelete(processStep)"
                      class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- Create Process Step Dialog -->
    <Dialog v-model:open="isCreateDialogOpen">
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>新增工序</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">工序名称 *</Label>
              <Input
                id="name"
                v-model="formData.name"
                placeholder="请输入工序名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="type">工序类型 *</Label>
              <Select v-model="formData.type">
                <SelectTrigger>
                  <SelectValue placeholder="选择工序类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in processStepTypes" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="space-y-2">
            <Label for="category">工序分类</Label>
            <Select v-model="formData.category">
              <SelectTrigger>
                <SelectValue placeholder="选择工序分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="option in processCategories" :key="option.value" :value="option.value">
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="processingTimeFormula">工时公式</Label>
              <Input
                id="processingTimeFormula"
                v-model="formData.processingTimeFormula"
                placeholder="例: (width + height) * 2 / 15"
              />
            </div>
            <div class="space-y-2">
              <Label for="setupTimeFormula">准备时间公式</Label>
              <Input
                id="setupTimeFormula"
                v-model="formData.setupTimeFormula"
                placeholder="例: 30"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="description">工序描述</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              placeholder="请输入工序描述"
              rows="3"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isCreateDialogOpen = false">
            取消
          </Button>
          <Button @click="handleCreate" :disabled="masterDataStore.loading">
            创建
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Edit Process Step Dialog -->
    <Dialog v-model:open="isEditDialogOpen">
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>编辑工序</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-name">工序名称 *</Label>
              <Input
                id="edit-name"
                v-model="formData.name"
                placeholder="请输入工序名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-type">工序类型 *</Label>
              <Select v-model="formData.type">
                <SelectTrigger>
                  <SelectValue placeholder="选择工序类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in processStepTypes" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="space-y-2">
            <Label for="edit-category">工序分类</Label>
            <Select v-model="formData.category">
              <SelectTrigger>
                <SelectValue placeholder="选择工序分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="option in processCategories" :key="option.value" :value="option.value">
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-processingTimeFormula">工时公式</Label>
              <Input
                id="edit-processingTimeFormula"
                v-model="formData.processingTimeFormula"
                placeholder="例: (width + height) * 2 / 15"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-setupTimeFormula">准备时间公式</Label>
              <Input
                id="edit-setupTimeFormula"
                v-model="formData.setupTimeFormula"
                placeholder="例: 30"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="edit-description">工序描述</Label>
            <Textarea
              id="edit-description"
              v-model="formData.description"
              placeholder="请输入工序描述"
              rows="3"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isEditDialogOpen = false">
            取消
          </Button>
          <Button @click="handleUpdate" :disabled="masterDataStore.loading">
            更新
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>
