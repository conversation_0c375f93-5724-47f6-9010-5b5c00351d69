<template>
  <div class="design-button-demo p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-2">产品结构设计按钮演示</h1>
      <p class="text-gray-600">
        演示在产品结构表格中添加的"设计"按钮功能，点击后打开可视化设计弹窗。
      </p>
    </div>

    <!-- 模拟产品结构表格 -->
    <div class="bg-white rounded-lg border">
      <div class="p-4 border-b">
        <h3 class="font-medium">产品结构列表</h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">名称</th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">编码</th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">版本</th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">状态</th>
              <th class="px-4 py-3 text-left text-sm font-medium text-gray-900">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="structure in demoStructures"
              :key="structure.id"
              class="hover:bg-gray-50"
            >
              <td class="px-4 py-3">
                <div>
                  <div class="font-medium text-sm">{{ structure.name }}</div>
                  <div class="text-xs text-gray-500">{{ structure.description }}</div>
                </div>
              </td>
              <td class="px-4 py-3 text-sm">{{ structure.code }}</td>
              <td class="px-4 py-3 text-sm">v{{ structure.version }}</td>
              <td class="px-4 py-3">
                <span
                  :class="[
                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                    structure.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  ]"
                >
                  {{ structure.status === 'active' ? '活跃' : '草稿' }}
                </span>
              </td>
              <td class="px-4 py-3">
                <div class="flex items-center gap-1">
                  <!-- 查看详情按钮 -->
                  <button
                    class="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                    title="查看详情"
                  >
                    <Eye class="w-4 h-4" />
                  </button>
                  
                  <!-- 设计按钮 - 新增的功能 -->
                  <button
                    @click="openDesignDialog(structure)"
                    class="p-1 text-purple-600 hover:text-purple-900 hover:bg-purple-50 rounded"
                    title="可视化设计"
                  >
                    <Palette class="w-4 h-4" />
                  </button>
                  
                  <!-- 编辑按钮 -->
                  <button
                    class="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                    title="编辑"
                  >
                    <Edit class="w-4 h-4" />
                  </button>
                  
                  <!-- 复制按钮 -->
                  <button
                    class="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                    title="复制"
                  >
                    <Copy class="w-4 h-4" />
                  </button>
                  
                  <!-- 删除按钮 -->
                  <button
                    class="p-1 text-red-600 hover:text-red-900 hover:bg-red-50 rounded"
                    title="删除"
                  >
                    <Trash2 class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 功能说明 -->
    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="bg-blue-50 p-4 rounded-lg">
        <h4 class="font-medium text-blue-900 mb-2">设计按钮功能</h4>
        <ul class="text-sm text-blue-800 space-y-1">
          <li>• 位置：在操作列中，查看详情按钮之后</li>
          <li>• 图标：使用紫色的调色板图标</li>
          <li>• 功能：点击打开可视化设计弹窗</li>
          <li>• 提示：鼠标悬停显示"可视化设计"</li>
        </ul>
      </div>
      
      <div class="bg-green-50 p-4 rounded-lg">
        <h4 class="font-medium text-green-900 mb-2">弹窗特性</h4>
        <ul class="text-sm text-green-800 space-y-1">
          <li>• 大尺寸：1400px宽，90%视口高度</li>
          <li>• 全屏模式：支持切换全屏显示</li>
          <li>• 三栏布局：工具面板 + 画布 + 属性面板</li>
          <li>• 实时编辑：支持节点的增删改操作</li>
        </ul>
      </div>
    </div>

    <!-- 设计弹窗 -->
    <ProductStructureDesignDialog
      v-model:open="showDesignDialog"
      :structure="selectedStructure"
      @save="handleSave"
      @validate="handleValidate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  Eye,
  Palette,
  Edit,
  Copy,
  Trash2
} from 'lucide-vue-next';

import ProductStructureDesignDialog from '@/components/product/ProductStructureDesignDialog.vue';
import type { ProductStructure } from '@/types/product-structure';

// 演示数据
const demoStructures = ref<ProductStructure[]>([
  {
    id: 'struct1',
    code: 'WIN001',
    name: '标准铝合金窗户',
    description: '1200×1500mm标准推拉窗',
    version: 1,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'user1',
    updatedBy: 'user1',
    rootAssembly: {
      id: 'asm1',
      assemblyId: 'asm1',
      assemblyCode: 'WIN_FRAME',
      assemblyName: '窗框构件',
      assemblyVersion: 1,
      instanceName: '主窗框',
      quantity: 1,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      parameterValues: {},
      optional: false,
      alternatives: [],
      properties: {},
      componentInstances: [
        {
          id: 'comp1',
          componentId: 'comp1',
          componentCode: 'FRAME_H',
          componentName: '水平框料',
          instanceName: '上框',
          quantity: 1,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: { length: 1200 },
          optional: false,
          alternatives: [],
          properties: {}
        },
        {
          id: 'comp2',
          componentId: 'comp2',
          componentCode: 'FRAME_V',
          componentName: '竖直框料',
          instanceName: '左框',
          quantity: 1,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: { length: 1500 },
          optional: false,
          alternatives: [],
          properties: {}
        }
      ]
    },
    parameters: [],
    constraints: [],
    validationRules: [],
    metadata: {},
    tags: ['窗户', '铝合金']
  },
  {
    id: 'struct2',
    code: 'DOOR001',
    name: '标准铝合金门',
    description: '900×2100mm标准平开门',
    version: 2,
    status: 'active',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
    createdBy: 'user1',
    updatedBy: 'user1',
    rootAssembly: {
      id: 'asm2',
      assemblyId: 'asm2',
      assemblyCode: 'DOOR_FRAME',
      assemblyName: '门框构件',
      assemblyVersion: 1,
      instanceName: '主门框',
      quantity: 1,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      parameterValues: {},
      optional: false,
      alternatives: [],
      properties: {}
    },
    parameters: [],
    constraints: [],
    validationRules: [],
    metadata: {},
    tags: ['门', '铝合金']
  }
]);

// 弹窗状态
const showDesignDialog = ref(false);
const selectedStructure = ref<ProductStructure | null>(null);

// 方法
const openDesignDialog = (structure: ProductStructure) => {
  selectedStructure.value = structure;
  showDesignDialog.value = true;
};

const handleSave = (structure: ProductStructure) => {
  console.log('保存结构:', structure);
  showDesignDialog.value = false;
  // 这里可以调用API保存数据
};

const handleValidate = (structure: ProductStructure) => {
  console.log('验证结构:', structure);
  // 这里可以调用验证API
};
</script>

<style scoped>
.design-button-demo {
  min-height: 100vh;
  background: #f8fafc;
}

/* 表格样式 */
table {
  border-collapse: collapse;
}

th, td {
  border: none;
}

/* 按钮悬停效果 */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: scale(1.05);
}

/* 状态徽章样式 */
.bg-green-100 {
  background-color: #dcfce7;
}

.text-green-800 {
  color: #166534;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.text-gray-800 {
  color: #1f2937;
}
</style>
