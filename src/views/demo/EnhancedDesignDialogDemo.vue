<template>
  <div class="enhanced-design-dialog-demo p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-2">MTO模式产品结构设计器演示</h1>
      <p class="text-gray-600 mb-4">
        专为玻璃深加工企业设计的参数化产品结构维护系统，展示完整的防火窗和隔断产品结构配置功能。
      </p>
      
      <!-- 功能特性说明 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <div class="bg-blue-50 p-4 rounded-lg">
          <h3 class="font-semibold text-blue-900 mb-2">🏗️ 参数化结构</h3>
          <ul class="text-sm text-blue-800 space-y-1">
            <li>• 产品-构件-组件三级结构</li>
            <li>• 完整的参数化配置</li>
            <li>• 尺寸、材质、功能参数</li>
            <li>• 动态约束验证</li>
          </ul>
        </div>

        <div class="bg-green-50 p-4 rounded-lg">
          <h3 class="font-semibold text-green-900 mb-2">🎯 MTO模式支持</h3>
          <ul class="text-sm text-green-800 space-y-1">
            <li>• 客户定制参数配置</li>
            <li>• 构件/组件库复用</li>
            <li>• 物料映射和成本计算</li>
            <li>• 业务规则约束验证</li>
          </ul>
        </div>

        <div class="bg-purple-50 p-4 rounded-lg">
          <h3 class="font-semibold text-purple-900 mb-2">⚡ 快捷键支持</h3>
          <ul class="text-sm text-purple-800 space-y-1">
            <li>• Ctrl+Z/Y 撤销重做</li>
            <li>• Ctrl+S 保存</li>
            <li>• Delete 删除节点</li>
            <li>• Ctrl+A 全选</li>
          </ul>
        </div>

        <div class="bg-orange-50 p-4 rounded-lg">
          <h3 class="font-semibold text-orange-900 mb-2">📝 属性编辑</h3>
          <ul class="text-sm text-orange-800 space-y-1">
            <li>• 右侧属性面板</li>
            <li>• 实时数据验证</li>
            <li>• 批量编辑支持</li>
            <li>• 参数配置管理</li>
          </ul>
        </div>

        <div class="bg-red-50 p-4 rounded-lg">
          <h3 class="font-semibold text-red-900 mb-2">🔄 操作历史</h3>
          <ul class="text-sm text-red-800 space-y-1">
            <li>• 完整撤销重做栈</li>
            <li>• 操作历史记录</li>
            <li>• 批量操作支持</li>
            <li>• 数据安全保障</li>
          </ul>
        </div>

        <div class="bg-indigo-50 p-4 rounded-lg">
          <h3 class="font-semibold text-indigo-900 mb-2">💾 数据管理</h3>
          <ul class="text-sm text-indigo-800 space-y-1">
            <li>• 自动保存功能</li>
            <li>• 数据验证检查</li>
            <li>• 结构完整性保证</li>
            <li>• 版本控制支持</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 演示按钮 -->
    <div class="flex items-center gap-4 mb-6">
      <Button
        @click="openDesignDialog(demoStructures[0])"
        class="bg-blue-600 hover:bg-blue-700 text-white"
      >
        <Shield class="w-4 h-4 mr-2" />
        标准防火窗结构
      </Button>

      <Button
        @click="openDesignDialog(demoStructures[1])"
        variant="outline"
      >
        <Layers class="w-4 h-4 mr-2" />
        高端防火隔断结构
      </Button>

      <Button
        @click="validateStructures"
        variant="outline"
        class="border-green-300 text-green-700 hover:bg-green-50"
      >
        <Shield class="w-4 h-4 mr-2" />
        验证数据结构
      </Button>
      
      <Button
        @click="showShortcutsHelp = true"
        variant="outline"
      >
        <Keyboard class="w-4 h-4 mr-2" />
        快捷键帮助
      </Button>
    </div>

    <!-- 快捷键说明 -->
    <div class="bg-gray-50 rounded-lg p-4 mb-6">
      <h3 class="font-medium mb-3">常用快捷键</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div class="space-y-1">
          <div class="font-medium text-gray-700">编辑操作</div>
          <div><kbd class="kbd">Ctrl+Z</kbd> 撤销</div>
          <div><kbd class="kbd">Ctrl+Y</kbd> 重做</div>
          <div><kbd class="kbd">Delete</kbd> 删除</div>
        </div>
        <div class="space-y-1">
          <div class="font-medium text-gray-700">选择操作</div>
          <div><kbd class="kbd">Ctrl+A</kbd> 全选</div>
          <div><kbd class="kbd">Ctrl+点击</kbd> 多选</div>
          <div><kbd class="kbd">Esc</kbd> 取消选择</div>
        </div>
        <div class="space-y-1">
          <div class="font-medium text-gray-700">视图操作</div>
          <div><kbd class="kbd">+/-</kbd> 缩放</div>
          <div><kbd class="kbd">F</kbd> 适应视图</div>
          <div><kbd class="kbd">R</kbd> 重置视图</div>
        </div>
        <div class="space-y-1">
          <div class="font-medium text-gray-700">其他功能</div>
          <div><kbd class="kbd">Ctrl+S</kbd> 保存</div>
          <div><kbd class="kbd">Ctrl+F</kbd> 搜索</div>
          <div><kbd class="kbd">F11</kbd> 全屏</div>
        </div>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="bg-white rounded-lg border p-6">
      <h3 class="font-medium mb-4">使用说明</h3>
      <div class="space-y-4 text-sm text-gray-600">
        <div>
          <strong>1. 树状表格操作：</strong>
          <ul class="ml-4 mt-1 space-y-1">
            <li>• 点击表格行选择节点</li>
            <li>• Ctrl+点击进行多选</li>
            <li>• 双击快速编辑节点</li>
            <li>• 使用搜索框快速定位节点</li>
          </ul>
        </div>

        <div>
          <strong>2. 节点操作：</strong>
          <ul class="ml-4 mt-1 space-y-1">
            <li>• 点击展开/折叠按钮控制层级显示</li>
            <li>• 使用操作列的按钮进行编辑、添加、删除</li>
            <li>• 支持批量删除选中的多个节点</li>
            <li>• 右键点击显示上下文菜单</li>
          </ul>
        </div>

        <div>
          <strong>3. 属性编辑：</strong>
          <ul class="ml-4 mt-1 space-y-1">
            <li>• 选中节点后在右侧面板编辑属性</li>
            <li>• 支持实时验证和错误提示</li>
            <li>• 多选时显示批量编辑选项</li>
            <li>• 自动保存编辑结果</li>
          </ul>
        </div>

        <div>
          <strong>4. 数据管理：</strong>
          <ul class="ml-4 mt-1 space-y-1">
            <li>• 完整的撤销重做功能</li>
            <li>• 自动保存和手动保存</li>
            <li>• 结构验证和完整性检查</li>
            <li>• 操作历史记录和回溯</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 设计弹窗 -->
    <ProductStructureDesignDialog
      v-model:open="showDesignDialog"
      :structure="selectedStructure"
      @save="handleSave"
      @validate="handleValidate"
    />

    <!-- 快捷键帮助弹窗 -->
    <Dialog v-model:open="showShortcutsHelp">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>键盘快捷键</DialogTitle>
          <DialogDescription>
            AUTOCAD风格的快捷键设计，提高操作效率
          </DialogDescription>
        </DialogHeader>
        
        <div class="space-y-4">
          <div v-for="category in shortcutCategories" :key="category.name" class="space-y-2">
            <h4 class="font-medium text-gray-900">{{ category.name }}</h4>
            <div class="grid grid-cols-2 gap-2">
              <div
                v-for="shortcut in category.shortcuts"
                :key="shortcut.key"
                class="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
              >
                <span>{{ shortcut.description }}</span>
                <kbd class="kbd">{{ shortcut.key }}</kbd>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import {
  Palette,
  Layers,
  Keyboard,
  Shield
} from 'lucide-vue-next';

import ProductStructureDesignDialog from '@/components/product/ProductStructureDesignDialog.vue';
import type { ProductStructure } from '@/types/product-structure';
import { validateProductStructure, generateValidationReport } from '@/utils/validateProductStructure';

// 演示数据
const demoStructures = ref<ProductStructure[]>([
  {
    id: 'fw_std_001',
    code: 'FW-STD-001',
    name: '标准防火窗结构',
    description: '符合国标GB16809-2008的A级防火窗，60分钟耐火极限',
    version: 1,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    createdBy: 'engineer_001',
    updatedBy: 'engineer_001',
    rootAssembly: {
      id: 'asm_fw_main',
      assemblyId: 'asm_fw_main',
      assemblyCode: 'FW-MAIN-ASM',
      assemblyName: '防火窗主框架构件',
      assemblyVersion: 1,
      instanceName: '主框架总成',
      quantity: 1,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      parameterValues: {
        frame_width: 80,
        frame_depth: 120,
        corner_joint_type: 'welded'
      },
      optional: false,
      alternatives: [],
      properties: {
        material_grade: 'Q355B',
        surface_treatment: 'galvanized',
        fire_rating: 'A'
      },
      parameters: [
        {
          id: 'param_frame_width',
          name: 'frame_width',
          displayName: '框架宽度',
          type: 'dimension',
          inputType: 'number',
          unit: 'mm',
          defaultValue: 80,
          minValue: 60,
          maxValue: 120,
          required: true,
          helpText: '框架型材的宽度，影响承重能力'
        },
        {
          id: 'param_frame_depth',
          name: 'frame_depth',
          displayName: '框架深度',
          type: 'dimension',
          inputType: 'number',
          unit: 'mm',
          defaultValue: 120,
          minValue: 80,
          maxValue: 160,
          required: true,
          helpText: '框架型材的深度，影响安装和密封'
        },
        {
          id: 'param_corner_joint',
          name: 'corner_joint_type',
          displayName: '转角连接方式',
          type: 'feature',
          inputType: 'select',
          defaultValue: 'welded',
          required: true,
          options: [
            { value: 'welded', label: '焊接', additionalCost: 0 },
            { value: 'mechanical', label: '机械连接', additionalCost: 50 }
          ],
          helpText: '框架转角的连接工艺'
        }
      ],
      constraints: [
        {
          id: 'const_frame_001',
          name: '框架尺寸约束',
          expression: 'frame_width >= 60 && frame_depth >= 80',
          errorMessage: '框架尺寸不符合防火窗标准要求',
          severity: 'error'
        }
      ],
      componentInstances: [
        // 外框立柱组件
        {
          id: 'comp_fw_vertical_left',
          componentId: 'comp_fw_vertical',
          componentCode: 'FW-VERT-001',
          componentName: '外框立柱',
          instanceName: '左立柱',
          quantity: 1,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            height: 1800,
            profile_section: '80x120x4',
            material_grade: 'Q355B'
          },
          optional: false,
          alternatives: [],
          properties: {
            materialCategoryId: 'cat_steel_profile',
            quantityFormula: 'height / 1000',
            costFormula: 'quantity * material_unit_cost * 1.2'
          },
          parameters: [
            {
              id: 'param_vert_height',
              name: 'height',
              displayName: '立柱高度',
              type: 'dimension',
              inputType: 'number',
              unit: 'mm',
              defaultValue: 1800,
              minValue: 800,
              maxValue: 3000,
              required: true,
              helpText: '立柱的总高度，需考虑安装余量'
            },
            {
              id: 'param_vert_section',
              name: 'profile_section',
              displayName: '型材规格',
              type: 'material',
              inputType: 'select',
              defaultValue: '80x120x4',
              required: true,
              options: [
                { value: '80x120x4', label: '80×120×4mm', additionalCost: 0 },
                { value: '100x120x4', label: '100×120×4mm', additionalCost: 30 },
                { value: '80x140x4', label: '80×140×4mm', additionalCost: 25 }
              ],
              helpText: '立柱型材的截面规格'
            }
          ],
          constraints: [
            {
              id: 'const_vert_001',
              name: '立柱高度约束',
              expression: 'height >= 800 && height <= 3000',
              errorMessage: '立柱高度超出标准范围',
              severity: 'error'
            }
          ]
        },
        {
          id: 'comp_fw_vertical_right',
          componentId: 'comp_fw_vertical',
          componentCode: 'FW-VERT-001',
          componentName: '外框立柱',
          instanceName: '右立柱',
          quantity: 1,
          position: { x: 1500, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            height: 1800,
            profile_section: '80x120x4',
            material_grade: 'Q355B'
          },
          optional: false,
          alternatives: [],
          properties: {
            materialCategoryId: 'cat_steel_profile',
            quantityFormula: 'height / 1000',
            costFormula: 'quantity * material_unit_cost * 1.2'
          },
          parameters: [
            {
              id: 'param_vert_height_r',
              name: 'height',
              displayName: '立柱高度',
              type: 'dimension',
              inputType: 'number',
              unit: 'mm',
              defaultValue: 1800,
              minValue: 800,
              maxValue: 3000,
              required: true,
              helpText: '立柱的总高度，需考虑安装余量'
            }
          ]
        },
        // 外框横梁组件
        {
          id: 'comp_fw_horizontal_top',
          componentId: 'comp_fw_horizontal',
          componentCode: 'FW-HORZ-001',
          componentName: '外框横梁',
          instanceName: '上横梁',
          quantity: 1,
          position: { x: 0, y: 1800, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            width: 1500,
            profile_section: '80x120x4',
            material_grade: 'Q355B'
          },
          optional: false,
          alternatives: [],
          properties: {
            materialCategoryId: 'cat_steel_profile',
            quantityFormula: 'width / 1000',
            costFormula: 'quantity * material_unit_cost * 1.1'
          },
          parameters: [
            {
              id: 'param_horz_width',
              name: 'width',
              displayName: '横梁宽度',
              type: 'dimension',
              inputType: 'number',
              unit: 'mm',
              defaultValue: 1500,
              minValue: 600,
              maxValue: 3000,
              required: true,
              helpText: '横梁的跨度宽度'
            }
          ]
        },
        {
          id: 'comp_fw_horizontal_bottom',
          componentId: 'comp_fw_horizontal',
          componentCode: 'FW-HORZ-001',
          componentName: '外框横梁',
          instanceName: '下横梁',
          quantity: 1,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            width: 1500,
            profile_section: '80x120x4',
            material_grade: 'Q355B'
          },
          optional: false,
          alternatives: [],
          properties: {
            materialCategoryId: 'cat_steel_profile',
            quantityFormula: 'width / 1000',
            costFormula: 'quantity * material_unit_cost * 1.1'
          }
        },
        // 防火玻璃面板组件
        {
          id: 'comp_fw_glass_main',
          componentId: 'comp_fw_glass',
          componentCode: 'FW-GLASS-001',
          componentName: '防火玻璃面板',
          instanceName: '主玻璃面板',
          quantity: 1,
          position: { x: 40, y: 40, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            glass_width: 1420,
            glass_height: 1720,
            glass_thickness: 6,
            fire_rating: '60min',
            glass_type: 'fire_resistant'
          },
          optional: false,
          alternatives: [
            {
              id: 'alt_glass_001',
              name: '夹胶防火玻璃',
              description: '更高安全性的夹胶防火玻璃',
              parameterOverrides: {
                glass_thickness: 8,
                glass_type: 'fire_resistant_laminated'
              },
              additionalCost: 150
            }
          ],
          properties: {
            materialCategoryId: 'cat_fire_glass',
            quantityFormula: '(glass_width * glass_height) / 1000000',
            costFormula: 'quantity * material_unit_cost * (glass_thickness / 6)'
          },
          parameters: [
            {
              id: 'param_glass_width',
              name: 'glass_width',
              displayName: '玻璃宽度',
              type: 'dimension',
              inputType: 'number',
              unit: 'mm',
              defaultValue: 1420,
              minValue: 500,
              maxValue: 2900,
              required: true,
              helpText: '玻璃面板的净宽度，需考虑框架尺寸'
            },
            {
              id: 'param_glass_height',
              name: 'glass_height',
              displayName: '玻璃高度',
              type: 'dimension',
              inputType: 'number',
              unit: 'mm',
              defaultValue: 1720,
              minValue: 700,
              maxValue: 2700,
              required: true,
              helpText: '玻璃面板的净高度'
            },
            {
              id: 'param_glass_thickness',
              name: 'glass_thickness',
              displayName: '玻璃厚度',
              type: 'dimension',
              inputType: 'select',
              unit: 'mm',
              defaultValue: 6,
              required: true,
              options: [
                { value: 6, label: '6mm', additionalCost: 0 },
                { value: 8, label: '8mm', additionalCost: 50 },
                { value: 10, label: '10mm', additionalCost: 120 },
                { value: 12, label: '12mm', additionalCost: 200 }
              ],
              helpText: '防火玻璃的厚度规格'
            },
            {
              id: 'param_fire_rating',
              name: 'fire_rating',
              displayName: '防火等级',
              type: 'feature',
              inputType: 'select',
              defaultValue: '60min',
              required: true,
              options: [
                { value: '30min', label: '30分钟', additionalCost: 0 },
                { value: '60min', label: '60分钟', additionalCost: 100 },
                { value: '90min', label: '90分钟', additionalCost: 250 },
                { value: '120min', label: '120分钟', additionalCost: 400 }
              ],
              helpText: '防火玻璃的耐火时间等级'
            }
          ],
          constraints: [
            {
              id: 'const_glass_001',
              name: '玻璃面积约束',
              expression: '(glass_width * glass_height) <= 6000000',
              errorMessage: '单片玻璃面积不能超过6平方米',
              severity: 'error'
            },
            {
              id: 'const_glass_002',
              name: '厚度匹配约束',
              expression: 'glass_thickness >= 6 || fire_rating !== "120min"',
              errorMessage: '120分钟防火等级需要至少8mm厚度',
              severity: 'warning'
            }
          ]
        },
        // 密封胶条组件
        {
          id: 'comp_fw_sealing_main',
          componentId: 'comp_fw_sealing',
          componentCode: 'FW-SEAL-001',
          componentName: '防火密封胶条',
          instanceName: '主密封胶条',
          quantity: 1,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            total_length: 6600,
            sealing_type: 'fire_resistant',
            material_grade: 'EPDM_FR'
          },
          optional: false,
          alternatives: [],
          properties: {
            materialCategoryId: 'cat_sealing_material',
            quantityFormula: '(glass_width + glass_height) * 2 / 1000',
            costFormula: 'quantity * material_unit_cost'
          },
          parameters: [
            {
              id: 'param_seal_length',
              name: 'total_length',
              displayName: '胶条总长度',
              type: 'dimension',
              inputType: 'number',
              unit: 'mm',
              defaultValue: 6600,
              minValue: 3000,
              maxValue: 15000,
              required: true,
              helpText: '密封胶条的总长度，按周长计算'
            },
            {
              id: 'param_seal_type',
              name: 'sealing_type',
              displayName: '密封类型',
              type: 'feature',
              inputType: 'select',
              defaultValue: 'fire_resistant',
              required: true,
              options: [
                { value: 'fire_resistant', label: '防火密封', additionalCost: 0 },
                { value: 'weather_resistant', label: '耐候密封', additionalCost: -10 },
                { value: 'high_temp_resistant', label: '耐高温密封', additionalCost: 20 }
              ],
              helpText: '密封胶条的功能类型'
            }
          ]
        },
        // 五金配件组件
        {
          id: 'comp_fw_hardware_set',
          componentId: 'comp_fw_hardware',
          componentCode: 'FW-HW-001',
          componentName: '防火窗五金套装',
          instanceName: '标准五金套装',
          quantity: 1,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            hardware_type: 'fixed_window',
            material_grade: 'stainless_steel_304',
            surface_treatment: 'brushed'
          },
          optional: false,
          alternatives: [
            {
              id: 'alt_hw_001',
              name: '开启窗五金',
              description: '支持开启功能的五金配件',
              parameterOverrides: {
                hardware_type: 'casement_window'
              },
              additionalCost: 300
            }
          ],
          properties: {
            materialCategoryId: 'cat_hardware',
            quantityFormula: '1',
            costFormula: 'quantity * material_unit_cost'
          },
          parameters: [
            {
              id: 'param_hw_type',
              name: 'hardware_type',
              displayName: '五金类型',
              type: 'feature',
              inputType: 'select',
              defaultValue: 'fixed_window',
              required: true,
              options: [
                { value: 'fixed_window', label: '固定窗五金', additionalCost: 0 },
                { value: 'casement_window', label: '平开窗五金', additionalCost: 300 },
                { value: 'sliding_window', label: '推拉窗五金', additionalCost: 250 }
              ],
              helpText: '根据开启方式选择对应五金'
            },
            {
              id: 'param_hw_material',
              name: 'material_grade',
              displayName: '五金材质',
              type: 'material',
              inputType: 'select',
              defaultValue: 'stainless_steel_304',
              required: true,
              options: [
                { value: 'stainless_steel_304', label: '304不锈钢', additionalCost: 0 },
                { value: 'stainless_steel_316', label: '316不锈钢', additionalCost: 80 },
                { value: 'galvanized_steel', label: '镀锌钢', additionalCost: -50 }
              ],
              helpText: '五金配件的材质等级'
            }
          ]
        }
      ]
    },
    parameters: [
      {
        id: 'prod_param_width',
        name: 'window_width',
        displayName: '窗户宽度',
        type: 'dimension',
        inputType: 'number',
        unit: 'mm',
        defaultValue: 1500,
        minValue: 600,
        maxValue: 3000,
        required: true,
        helpText: '防火窗的净宽度尺寸'
      },
      {
        id: 'prod_param_height',
        name: 'window_height',
        displayName: '窗户高度',
        type: 'dimension',
        inputType: 'number',
        unit: 'mm',
        defaultValue: 1800,
        minValue: 800,
        maxValue: 2800,
        required: true,
        helpText: '防火窗的净高度尺寸'
      },
      {
        id: 'prod_param_opening',
        name: 'opening_method',
        displayName: '开启方式',
        type: 'feature',
        inputType: 'select',
        defaultValue: 'fixed',
        required: true,
        options: [
          { value: 'fixed', label: '固定窗', additionalCost: 0 },
          { value: 'casement', label: '平开窗', additionalCost: 400 },
          { value: 'sliding', label: '推拉窗', additionalCost: 350 },
          { value: 'top_hung', label: '上悬窗', additionalCost: 450 }
        ],
        helpText: '窗户的开启方式，影响五金配置'
      },
      {
        id: 'prod_param_fire_class',
        name: 'fire_class',
        displayName: '防火分级',
        type: 'feature',
        inputType: 'select',
        defaultValue: 'A',
        required: true,
        options: [
          { value: 'A', label: 'A级防火', additionalCost: 0 },
          { value: 'B', label: 'B级防火', additionalCost: -100 },
          { value: 'C', label: 'C级防火', additionalCost: -200 }
        ],
        helpText: '防火窗的防火分级标准'
      }
    ],
    constraints: [
      {
        id: 'const_prod_001',
        name: '窗户面积约束',
        expression: 'window_width * window_height <= 6000000',
        errorMessage: '窗户面积不能超过6平方米',
        severity: 'error'
      },
      {
        id: 'const_prod_002',
        name: '宽高比约束',
        expression: 'window_width / window_height <= 3 && window_height / window_width <= 3',
        errorMessage: '窗户宽高比不能超过3:1',
        severity: 'warning'
      },
      {
        id: 'const_prod_003',
        name: '开启方式约束',
        expression: 'opening_method === "fixed" || window_width <= 2400',
        errorMessage: '开启窗宽度不能超过2.4米',
        severity: 'error'
      }
    ],
    validationRules: [
      {
        id: 'rule_001',
        name: '防火等级匹配',
        description: '确保玻璃防火等级与窗户防火分级匹配',
        expression: 'fire_class === "A" ? fire_rating >= "60min" : true',
        errorMessage: 'A级防火窗需要至少60分钟防火玻璃'
      }
    ],
    metadata: {
      designStandard: 'GB16809-2008',
      certificationRequired: true,
      testReportRequired: true,
      qualityLevel: 'A',
      manufacturer: '标准玻璃深加工厂'
    },
    tags: ['防火窗', '标准', 'A级', '60分钟', '钢质框架', '防火玻璃']
  },
  {
    id: 'fp_premium_001',
    code: 'FP-PREM-001',
    name: '高端防火隔断结构',
    description: '不锈钢框架+夹胶防火玻璃，120分钟耐火极限，适用于高端场所',
    version: 1,
    status: 'active',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
    createdBy: 'engineer_002',
    updatedBy: 'engineer_002',
    rootAssembly: {
      id: 'asm_fp_main',
      assemblyId: 'asm_fp_main',
      assemblyCode: 'FP-MAIN-ASM',
      assemblyName: '防火隔断主框架构件',
      assemblyVersion: 1,
      instanceName: '主框架系统',
      quantity: 1,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      parameterValues: {
        frame_width: 100,
        frame_depth: 140,
        material_grade: '304SS',
        surface_finish: 'brushed'
      },
      optional: false,
      alternatives: [
        {
          id: 'alt_frame_001',
          name: '316不锈钢框架',
          description: '更高耐腐蚀性的316不锈钢框架',
          parameterOverrides: {
            material_grade: '316SS'
          },
          additionalCost: 800
        }
      ],
      properties: {
        fire_rating: 'A',
        corrosion_resistance: 'high',
        load_bearing: 'heavy_duty'
      },
      parameters: [
        {
          id: 'param_fp_frame_width',
          name: 'frame_width',
          displayName: '框架宽度',
          type: 'dimension',
          inputType: 'select',
          unit: 'mm',
          defaultValue: 100,
          required: true,
          options: [
            { value: 80, label: '80mm', additionalCost: -50 },
            { value: 100, label: '100mm', additionalCost: 0 },
            { value: 120, label: '120mm', additionalCost: 80 }
          ],
          helpText: '隔断框架的宽度规格'
        },
        {
          id: 'param_fp_material',
          name: 'material_grade',
          displayName: '不锈钢等级',
          type: 'material',
          inputType: 'select',
          defaultValue: '304SS',
          required: true,
          options: [
            { value: '304SS', label: '304不锈钢', additionalCost: 0 },
            { value: '316SS', label: '316不锈钢', additionalCost: 200 },
            { value: '316LSS', label: '316L不锈钢', additionalCost: 300 }
          ],
          helpText: '不锈钢材质等级，影响耐腐蚀性'
        }
      ],
      componentInstances: [
        // 不锈钢立柱
        {
          id: 'comp_fp_vertical_001',
          componentId: 'comp_fp_vertical',
          componentCode: 'FP-VERT-001',
          componentName: '不锈钢立柱',
          instanceName: '主立柱',
          quantity: 2,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            height: 2400,
            profile_section: '100x140x3',
            material_grade: '304SS',
            surface_finish: 'brushed'
          },
          optional: false,
          alternatives: [],
          properties: {
            materialCategoryId: 'cat_stainless_steel_profile',
            quantityFormula: 'height / 1000 * quantity',
            costFormula: 'quantity * material_unit_cost * 1.5'
          },
          parameters: [
            {
              id: 'param_fp_vert_height',
              name: 'height',
              displayName: '立柱高度',
              type: 'dimension',
              inputType: 'number',
              unit: 'mm',
              defaultValue: 2400,
              minValue: 2000,
              maxValue: 4000,
              required: true,
              helpText: '隔断立柱的总高度'
            }
          ]
        },
        // 夹胶防火玻璃
        {
          id: 'comp_fp_glass_laminated',
          componentId: 'comp_fp_glass_laminated',
          componentCode: 'FP-GLASS-002',
          componentName: '夹胶防火玻璃',
          instanceName: '主玻璃面板',
          quantity: 1,
          position: { x: 50, y: 50, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            glass_width: 2900,
            glass_height: 2300,
            total_thickness: 14.52,
            fire_rating: '120min',
            glass_composition: '6+2.52PVB+6'
          },
          optional: false,
          alternatives: [
            {
              id: 'alt_glass_fp_001',
              name: '超厚夹胶玻璃',
              description: '更高安全性的超厚夹胶玻璃',
              parameterOverrides: {
                total_thickness: 17.52,
                glass_composition: '8+1.52PVB+8'
              },
              additionalCost: 400
            }
          ],
          properties: {
            materialCategoryId: 'cat_fire_laminated_glass',
            quantityFormula: '(glass_width * glass_height) / 1000000',
            costFormula: 'quantity * material_unit_cost * (total_thickness / 10)'
          },
          parameters: [
            {
              id: 'param_fp_glass_composition',
              name: 'glass_composition',
              displayName: '玻璃构成',
              type: 'material',
              inputType: 'select',
              defaultValue: '6+2.52PVB+6',
              required: true,
              options: [
                { value: '6+1.52PVB+6', label: '6+1.52PVB+6mm', additionalCost: 0 },
                { value: '6+2.52PVB+6', label: '6+2.52PVB+6mm', additionalCost: 50 },
                { value: '8+1.52PVB+8', label: '8+1.52PVB+8mm', additionalCost: 200 },
                { value: '8+2.52PVB+8', label: '8+2.52PVB+8mm', additionalCost: 300 }
              ],
              helpText: '夹胶玻璃的构成配置'
            }
          ]
        }
      ],
      subAssemblies: [
        // 玻璃面板构件
        {
          id: 'asm_fp_glass_panel',
          assemblyId: 'asm_fp_glass_panel',
          assemblyCode: 'FP-GLASS-ASM',
          assemblyName: '玻璃面板构件',
          assemblyVersion: 1,
          instanceName: '主玻璃面板构件',
          quantity: 3,
          position: { x: 100, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            panel_width: 1000,
            panel_height: 2300,
            glass_type: 'laminated_fire'
          },
          optional: false,
          alternatives: [],
          properties: {
            transparency: 'high',
            fire_resistance: '120min'
          },
          parameters: [
            {
              id: 'param_panel_width',
              name: 'panel_width',
              displayName: '面板宽度',
              type: 'dimension',
              inputType: 'number',
              unit: 'mm',
              defaultValue: 1000,
              minValue: 500,
              maxValue: 1500,
              required: true,
              helpText: '单个玻璃面板的宽度'
            }
          ],
          componentInstances: [
            {
              id: 'comp_fp_laminated_glass',
              componentId: 'comp_fp_laminated_glass',
              componentCode: 'FP-LAM-GLASS-001',
              componentName: '夹胶防火玻璃',
              instanceName: '主夹胶玻璃',
              quantity: 1,
              position: { x: 0, y: 0, z: 0 },
              rotation: { x: 0, y: 0, z: 0 },
              parameterValues: {
                glass_width: 1000,
                glass_height: 2300,
                total_thickness: 14.52,
                pvb_thickness: 2.52,
                fire_rating: '120min'
              },
              optional: false,
              alternatives: [],
              properties: {
                materialCategoryId: 'cat_fire_laminated_glass',
                quantityFormula: '(glass_width * glass_height) / 1000000',
                costFormula: 'quantity * material_unit_cost * 2.5'
              },
              parameters: [
                {
                  id: 'param_fp_pvb_thickness',
                  name: 'pvb_thickness',
                  displayName: 'PVB胶片厚度',
                  type: 'material',
                  inputType: 'select',
                  unit: 'mm',
                  defaultValue: 2.52,
                  required: true,
                  options: [
                    { value: 1.52, label: '1.52mm', additionalCost: 0 },
                    { value: 2.52, label: '2.52mm', additionalCost: 80 },
                    { value: 3.04, label: '3.04mm', additionalCost: 150 }
                  ],
                  helpText: 'PVB胶片厚度，影响安全性和隔音效果'
                }
              ]
            }
          ]
        },
        // 密封系统构件
        {
          id: 'asm_fp_sealing',
          assemblyId: 'asm_fp_sealing',
          assemblyCode: 'FP-SEAL-ASM',
          assemblyName: '密封系统构件',
          assemblyVersion: 1,
          instanceName: '完整密封系统',
          quantity: 1,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {
            sealing_level: 'premium',
            weather_resistance: 'high'
          },
          optional: false,
          alternatives: [],
          properties: {
            durability: '25_years',
            maintenance_free: true
          },
          componentInstances: [
            {
              id: 'comp_fp_structural_seal',
              componentId: 'comp_fp_structural_seal',
              componentCode: 'FP-STRUCT-SEAL-001',
              componentName: '结构密封胶',
              instanceName: '主结构密封',
              quantity: 1,
              position: { x: 0, y: 0, z: 0 },
              rotation: { x: 0, y: 0, z: 0 },
              parameterValues: {
                sealant_type: 'structural_silicone',
                color: 'clear',
                cure_time: '24h'
              },
              optional: false,
              alternatives: [],
              properties: {
                materialCategoryId: 'cat_structural_sealant',
                quantityFormula: '(partition_width + partition_height) * 2 * 0.02',
                costFormula: 'quantity * material_unit_cost'
              }
            }
          ]
        }
      ]
    },
    parameters: [
      {
        id: 'prod_param_fp_width',
        name: 'partition_width',
        displayName: '隔断宽度',
        type: 'dimension',
        inputType: 'number',
        unit: 'mm',
        defaultValue: 3000,
        minValue: 1000,
        maxValue: 6000,
        required: true,
        helpText: '防火隔断的总宽度'
      },
      {
        id: 'prod_param_fp_height',
        name: 'partition_height',
        displayName: '隔断高度',
        type: 'dimension',
        inputType: 'number',
        unit: 'mm',
        defaultValue: 2400,
        minValue: 2000,
        maxValue: 4000,
        required: true,
        helpText: '防火隔断的总高度'
      }
    ],
    constraints: [
      {
        id: 'const_fp_001',
        name: '隔断面积约束',
        expression: 'partition_width * partition_height <= 15000000',
        errorMessage: '隔断面积不能超过15平方米',
        severity: 'error'
      }
    ],
    validationRules: [],
    metadata: {
      designStandard: 'GB/T12955-2008',
      certificationRequired: true,
      testReportRequired: true,
      qualityLevel: 'Premium',
      manufacturer: '高端玻璃深加工厂'
    },
    tags: ['防火隔断', '高端', 'A级', '120分钟', '不锈钢框架', '夹胶玻璃']
  }
]);

// 弹窗状态
const showDesignDialog = ref(false);
const selectedStructure = ref<ProductStructure | null>(null);
const showShortcutsHelp = ref(false);

// 快捷键分类
const shortcutCategories = ref([
  {
    name: '文件操作',
    shortcuts: [
      { key: 'Ctrl+N', description: '新建文件' },
      { key: 'Ctrl+O', description: '打开文件' },
      { key: 'Ctrl+S', description: '保存文件' }
    ]
  },
  {
    name: '编辑操作',
    shortcuts: [
      { key: 'Ctrl+Z', description: '撤销' },
      { key: 'Ctrl+Y', description: '重做' },
      { key: 'Delete', description: '删除选中项' },
      { key: 'Ctrl+A', description: '全选' },
      { key: 'Esc', description: '取消选择' }
    ]
  },
  {
    name: '视图操作',
    shortcuts: [
      { key: '+/-', description: '缩放' },
      { key: 'F', description: '适应视图' },
      { key: 'R', description: '重置视图' }
    ]
  }
]);

// 方法
const openDesignDialog = (structure: ProductStructure) => {
  selectedStructure.value = structure;
  showDesignDialog.value = true;
};

const validateStructures = () => {
  console.log('=== 产品结构数据验证报告 ===\n');

  demoStructures.value.forEach((structure, index) => {
    console.log(`\n--- 结构 ${index + 1}: ${structure.name} ---`);
    const report = generateValidationReport(structure);
    console.log(report);
  });

  alert('验证完成！请查看浏览器控制台获取详细报告。');
};

const handleSave = (structure: ProductStructure) => {
  console.log('保存结构:', structure);
  showDesignDialog.value = false;
  alert('结构已保存！');
};

const handleValidate = (structure: ProductStructure) => {
  console.log('验证结构:', structure);
  alert('结构验证通过！');
};
</script>

<style scoped>
.enhanced-design-dialog-demo {
  min-height: 100vh;
  background: #f8fafc;
}

.kbd {
  @apply inline-flex items-center px-2 py-1 text-xs font-mono bg-gray-200 text-gray-800 rounded border border-gray-300;
}
</style>
