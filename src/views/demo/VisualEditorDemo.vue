<template>
  <div class="visual-editor-demo p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-2">可视化产品结构编辑器演示</h1>
      <p class="text-gray-600">
        这是一个演示页面，展示可视化产品结构编辑器的功能。
        支持AUTOCAD风格的操作体验，包括拖拽、快捷键、工具栏等。
      </p>
    </div>

    <!-- 功能说明 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center gap-2 mb-2">
            <MousePointer class="w-5 h-5 text-blue-500" />
            <h3 class="font-medium">交互操作</h3>
          </div>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• 拖拽组件到画布创建节点</li>
            <li>• 点击节点查看和编辑属性</li>
            <li>• 右键菜单进行快速操作</li>
            <li>• 支持多选和批量操作</li>
          </ul>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center gap-2 mb-2">
            <Keyboard class="w-5 h-5 text-green-500" />
            <h3 class="font-medium">快捷键支持</h3>
          </div>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• Ctrl+S 保存结构</li>
            <li>• Ctrl+Z/Y 撤销/重做</li>
            <li>• Del 删除选中节点</li>
            <li>• S/M/C/A 切换工具</li>
          </ul>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center gap-2 mb-2">
            <Layout class="w-5 h-5 text-purple-500" />
            <h3 class="font-medium">布局算法</h3>
          </div>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• 树形布局（层次结构）</li>
            <li>• 力导向布局（自动排列）</li>
            <li>• 环形布局（径向分布）</li>
            <li>• 自定义位置调整</li>
          </ul>
        </CardContent>
      </Card>
    </div>

    <!-- 演示数据选择 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium">选择演示数据</h3>
          <div class="flex gap-2">
            <Button
              v-for="demo in demoStructures"
              :key="demo.id"
              :variant="selectedDemo?.id === demo.id ? 'default' : 'outline'"
              size="sm"
              @click="loadDemoStructure(demo)"
            >
              {{ demo.name }}
            </Button>
          </div>
        </div>
        
        <div v-if="selectedDemo" class="text-sm text-gray-600">
          <p><strong>描述：</strong>{{ selectedDemo.description }}</p>
          <p><strong>节点数：</strong>{{ selectedDemo.nodeCount }} 个</p>
          <p><strong>层级：</strong>{{ selectedDemo.levels }} 层</p>
        </div>
      </CardContent>
    </Card>

    <!-- 可视化编辑器 -->
    <Card class="h-[600px]">
      <CardContent class="p-0 h-full">
        <ProductStructureVisualEditor
          v-if="currentStructure"
          :structure="currentStructure"
          @save="handleSave"
          @validate="handleValidate"
          @update:structure="handleUpdate"
        />
        <div v-else class="h-full flex items-center justify-center">
          <div class="text-center">
            <Package class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">选择演示数据开始体验</h3>
            <p class="text-gray-600">从上方选择一个演示结构来体验可视化编辑器的功能</p>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 操作日志 -->
    <Card class="mt-6">
      <CardContent class="p-4">
        <h3 class="font-medium mb-3">操作日志</h3>
        <div class="max-h-32 overflow-y-auto space-y-1">
          <div
            v-for="(log, index) in operationLogs"
            :key="index"
            class="text-sm p-2 bg-gray-50 rounded"
          >
            <span class="text-gray-500">{{ log.timestamp }}</span>
            <span class="ml-2">{{ log.message }}</span>
          </div>
          <div v-if="operationLogs.length === 0" class="text-sm text-gray-500 text-center py-4">
            暂无操作记录
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  MousePointer,
  Keyboard,
  Layout,
  Package
} from 'lucide-vue-next';

import ProductStructureVisualEditor from '@/components/product/ProductStructureVisualEditor.vue';
import type { ProductStructure } from '@/types/product-structure';

// 演示数据
const demoStructures = [
  {
    id: 'simple',
    name: '简单窗户',
    description: '包含基本框架和玻璃的简单窗户结构',
    nodeCount: 5,
    levels: 2
  },
  {
    id: 'complex',
    name: '复杂门窗',
    description: '包含多个构件和组件的复杂门窗系统',
    nodeCount: 15,
    levels: 4
  },
  {
    id: 'curtain-wall',
    name: '幕墙系统',
    description: '大型幕墙系统，包含多种类型的面板和连接件',
    nodeCount: 25,
    levels: 5
  }
];

// 响应式数据
const selectedDemo = ref(null);
const currentStructure = ref<ProductStructure | null>(null);
const operationLogs = ref<Array<{ timestamp: string; message: string }>>([]);

// 方法
const loadDemoStructure = (demo: any) => {
  selectedDemo.value = demo;
  
  // 生成演示用的产品结构数据
  currentStructure.value = generateDemoStructure(demo);
  
  addLog(`加载演示数据: ${demo.name}`);
};

const generateDemoStructure = (demo: any): ProductStructure => {
  const baseStructure: ProductStructure = {
    id: `demo_${demo.id}`,
    code: demo.id.toUpperCase(),
    name: demo.name,
    description: demo.description,
    version: 1,
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'demo_user',
    updatedBy: 'demo_user',
    parameters: [],
    constraints: [],
    validationRules: [],
    metadata: {
      demoType: demo.id,
      nodeCount: demo.nodeCount,
      levels: demo.levels
    },
    tags: ['演示', '测试']
  };

  // 根据演示类型生成不同的根构件
  switch (demo.id) {
    case 'simple':
      baseStructure.rootAssembly = {
        id: 'simple_root',
        assemblyId: 'simple_window_frame',
        assemblyCode: 'SWF001',
        assemblyName: '简单窗框',
        assemblyVersion: 1,
        instanceName: '主窗框',
        quantity: 1,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        parameterValues: {
          width: 1200,
          height: 1500,
          thickness: 50
        },
        optional: false,
        alternatives: [],
        properties: {
          material: '铝合金',
          color: '白色',
          surface: '粉末喷涂'
        }
      };
      break;
      
    case 'complex':
      baseStructure.rootAssembly = {
        id: 'complex_root',
        assemblyId: 'complex_door_window',
        assemblyCode: 'CDW001',
        assemblyName: '复杂门窗系统',
        assemblyVersion: 1,
        instanceName: '主门窗系统',
        quantity: 1,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        parameterValues: {
          width: 2400,
          height: 2100,
          depth: 120
        },
        optional: false,
        alternatives: [],
        properties: {
          type: '推拉门窗',
          material: '断桥铝',
          glazing: '双层中空玻璃'
        }
      };
      break;
      
    case 'curtain-wall':
      baseStructure.rootAssembly = {
        id: 'curtain_root',
        assemblyId: 'curtain_wall_system',
        assemblyCode: 'CWS001',
        assemblyName: '幕墙系统',
        assemblyVersion: 1,
        instanceName: '主幕墙系统',
        quantity: 1,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        parameterValues: {
          width: 12000,
          height: 36000,
          depth: 200
        },
        optional: false,
        alternatives: [],
        properties: {
          type: '单元式幕墙',
          material: '铝合金+玻璃',
          windLoad: '2.5kN/m²'
        }
      };
      break;
  }

  return baseStructure;
};

const handleSave = (structure: ProductStructure) => {
  addLog(`保存产品结构: ${structure.name} (版本 ${structure.version})`);
  console.log('保存结构:', structure);
};

const handleValidate = (structure: ProductStructure) => {
  addLog(`验证产品结构: ${structure.name}`);
  console.log('验证结构:', structure);
};

const handleUpdate = (structure: ProductStructure) => {
  currentStructure.value = structure;
  addLog(`更新产品结构: ${structure.name}`);
};

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  operationLogs.value.unshift({ timestamp, message });
  
  // 保持最多20条日志
  if (operationLogs.value.length > 20) {
    operationLogs.value = operationLogs.value.slice(0, 20);
  }
};

// 初始化
addLog('可视化编辑器演示页面已加载');
</script>

<style scoped>
.visual-editor-demo {
  min-height: 100vh;
  background: #f8fafc;
}
</style>
