<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">切割布局可视化测试</h1>
    
    <!-- 测试切割可视化组件 -->
    <div class="border border-gray-200 rounded-lg p-6">
      <h2 class="text-lg font-semibold mb-4">切割布局可视化组件</h2>
      <CuttingLayoutVisualization :cutting-result="mockCuttingResult" />
    </div>

    <!-- 测试用户引导组件 -->
    <div class="mt-8 border border-gray-200 rounded-lg p-6">
      <h2 class="text-lg font-semibold mb-4">用户引导系统测试</h2>
      <div class="space-y-4">
        <div class="flex gap-4">
          <Button @click="startGuide" class="release-steps">
            启动引导
          </Button>
          <Button @click="showTooltip" variant="outline" class="batch-controls">
            显示工具提示
          </Button>
        </div>
        
        <!-- 模拟的步骤元素 -->
        <div class="grid grid-cols-4 gap-4 mt-6">
          <div class="step-circle bg-blue-100 p-4 rounded-lg text-center">
            <div class="text-sm font-medium">步骤 1</div>
            <div class="text-xs text-gray-600">工单构成审查</div>
          </div>
          <div class="step-circle bg-green-100 p-4 rounded-lg text-center cutting-content">
            <div class="text-sm font-medium">步骤 2</div>
            <div class="text-xs text-gray-600">切割优化</div>
          </div>
          <div class="step-circle bg-orange-100 p-4 rounded-lg text-center scheduling-content">
            <div class="text-sm font-medium">步骤 3</div>
            <div class="text-xs text-gray-600">生产排程</div>
          </div>
          <div class="step-circle bg-purple-100 p-4 rounded-lg text-center execution-content">
            <div class="text-sm font-medium">步骤 4</div>
            <div class="text-xs text-gray-600">决策与执行</div>
          </div>
        </div>

        <!-- 模拟的批次元素 -->
        <div class="mt-6 space-y-2">
          <div class="batch-header bg-purple-50 p-3 rounded-lg border">
            <div class="flex items-center justify-between">
              <span class="font-medium">批次-1</span>
              <button class="expand-button text-xs px-2 py-1 bg-purple-100 rounded">
                展开详情
              </button>
            </div>
          </div>
          <div class="batch-header bg-blue-50 p-3 rounded-lg border">
            <div class="flex items-center justify-between">
              <span class="font-medium">批次-2</span>
              <button class="expand-button text-xs px-2 py-1 bg-blue-100 rounded">
                展开详情
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 用户引导组件 -->
      <UserGuide 
        ref="userGuideRef"
        :auto-start="false"
        :show-help-button="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Button } from '@/components/ui/button';
import CuttingLayoutVisualization from '@/components/mes/CuttingLayoutVisualization.vue';
import UserGuide from '@/components/common/UserGuide.vue';

// 用户引导组件引用
const userGuideRef = ref();

// 模拟切割优化结果数据
const mockCuttingResult = ref({
  improvements: {
    materialUtilization: 92.5,
    costSaving: 15680,
    timeReduction: 35.2,
    wasteReduction: 28.7
  },
  optimizedLayouts: [
    { id: 1, efficiency: 92.5, pieces: 45 },
    { id: 2, efficiency: 88.7, pieces: 38 },
    { id: 3, efficiency: 85.3, pieces: 42 }
  ],
  cuttingPlans: [
    {
      planId: 'plan_001',
      efficiency: 92.5,
      layout: {
        pieces: [
          {
            pieceId: 'P001',
            dimensions: { length: 1800, width: 1200, thickness: 6 },
            position: { x: 50, y: 50 },
            customerName: '华润置地',
            orderNumber: 'CO-2024-001'
          },
          {
            pieceId: 'P002', 
            dimensions: { length: 1400, width: 1000, thickness: 6 },
            position: { x: 1900, y: 50 },
            customerName: '万科集团',
            orderNumber: 'CO-2024-002'
          },
          {
            pieceId: 'P003',
            dimensions: { length: 1200, width: 800, thickness: 6 },
            position: { x: 50, y: 1300 },
            customerName: '保利地产',
            orderNumber: 'CO-2024-003'
          },
          {
            pieceId: 'P004',
            dimensions: { length: 1000, width: 600, thickness: 6 },
            position: { x: 1300, y: 1300 },
            customerName: '绿地集团',
            orderNumber: 'CO-2024-004'
          }
        ]
      }
    },
    {
      planId: 'plan_002',
      efficiency: 88.7,
      layout: {
        pieces: [
          {
            pieceId: 'P001',
            dimensions: { length: 2000, width: 1400, thickness: 6 },
            position: { x: 100, y: 100 },
            customerName: '华润置地',
            orderNumber: 'CO-2024-001'
          },
          {
            pieceId: 'P002',
            dimensions: { length: 1200, width: 600, thickness: 6 },
            position: { x: 2200, y: 100 },
            customerName: '万科集团', 
            orderNumber: 'CO-2024-002'
          },
          {
            pieceId: 'P003',
            dimensions: { length: 800, width: 500, thickness: 6 },
            position: { x: 100, y: 1600 },
            customerName: '保利地产',
            orderNumber: 'CO-2024-003'
          }
        ]
      }
    }
  ]
});

// 启动用户引导
const startGuide = () => {
  if (userGuideRef.value) {
    userGuideRef.value.startGuide();
  }
};

// 显示工具提示示例
const showTooltip = () => {
  if (userGuideRef.value) {
    const element = document.querySelector('.batch-controls');
    if (element) {
      userGuideRef.value.showTooltipFor(element as HTMLElement, {
        title: '批次控制',
        description: '这里可以展开或收起所有批次的详细信息'
      });
      
      // 3秒后隐藏
      setTimeout(() => {
        userGuideRef.value.hideTooltip();
      }, 3000);
    }
  }
};
</script>

<style scoped>
/* 测试页面样式 */
.step-circle {
  cursor: pointer;
  transition: all 0.2s ease;
}

.step-circle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.batch-header {
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-header:hover {
  background-color: rgba(139, 92, 246, 0.1);
}
</style>
