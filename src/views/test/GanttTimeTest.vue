<template>
  <div class="p-6 space-y-6">
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-xl font-semibold mb-4">甘特图时间测试</h2>
      
      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <Button @click="generateTestData" :disabled="loading">
            {{ loading ? '生成中...' : '生成测试数据' }}
          </Button>
          <Button @click="runTimeDebug" variant="outline" :disabled="!testBatches.length">
            运行时间调试
          </Button>
          <Button @click="clearData" variant="outline">
            清除数据
          </Button>
        </div>
        
        <div v-if="testBatches.length > 0" class="grid grid-cols-2 gap-4">
          <div class="bg-gray-50 p-4 rounded">
            <h3 class="font-medium mb-2">测试批次数据</h3>
            <div class="text-sm space-y-1">
              <div>批次数量: {{ testBatches.length }}</div>
              <div v-if="timeStats.earliest">
                最早开始: {{ formatTime(timeStats.earliest) }}
              </div>
              <div v-if="timeStats.latest">
                最晚结束: {{ formatTime(timeStats.latest) }}
              </div>
              <div v-if="timeStats.totalSpan">
                总时间跨度: {{ timeStats.totalSpan.toFixed(1) }} 小时
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 p-4 rounded">
            <h3 class="font-medium mb-2">甘特图状态</h3>
            <div class="text-sm space-y-1">
              <div>数据状态: {{ ganttData ? '✅ 已生成' : '❌ 未生成' }}</div>
              <div v-if="ganttData">
                任务数量: {{ ganttData.tasks.length }}
              </div>
              <div v-if="ganttData">
                资源数量: {{ ganttData.resources.length }}
              </div>
              <div v-if="ganttData?.timeRange">
                时间范围: {{ formatTime(ganttData.timeRange.start) }} ~ {{ formatTime(ganttData.timeRange.end) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 甘特图显示 -->
    <div v-if="testBatches.length > 0" class="bg-white rounded-lg shadow">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium">工艺段甘特图测试</h3>
      </div>
      <div class="h-96">
        <ProcessSegmentGanttChart
          :scheduled-batches="testBatches"
          :enable-drill-down="true"
          @task-click="handleTaskClick"
        />
      </div>
    </div>
    
    <!-- 调试信息 -->
    <div v-if="debugReport" class="bg-white rounded-lg shadow p-6">
      <h3 class="text-lg font-medium mb-4">调试报告</h3>
      <pre class="text-sm bg-gray-100 p-4 rounded overflow-auto">{{ debugReport }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Button } from '@/components/ui/button';
import ProcessSegmentGanttChart from '@/components/mes/scheduling/ProcessSegmentGanttChart.vue';
import { GanttTimeDebugger } from '@/utils/ganttTimeDebugger';
import { processSegmentGanttService } from '@/services/processSegmentGanttService';
import type { ScheduledBatch, GanttChartData } from '@/types/scheduling';

// 响应式状态
const loading = ref(false);
const testBatches = ref<ScheduledBatch[]>([]);
const ganttData = ref<GanttChartData | null>(null);
const debugReport = ref<string>('');

// 计算属性
const timeStats = computed(() => {
  if (testBatches.value.length === 0) {
    return { earliest: '', latest: '', totalSpan: 0 };
  }
  
  const analysis = GanttTimeDebugger.analyzeScheduledBatches(testBatches.value);
  return analysis.timeStats;
});

// 生成工艺流程
const generateProcessFlow = (primaryWorkstation: string, totalDuration: number) => {
  const processFlows = {
    'cold_processing': [
      { stepName: '切割', workstationGroup: 'cold_processing', estimatedDuration: totalDuration * 0.4 },
      { stepName: '磨边', workstationGroup: 'cold_processing', estimatedDuration: totalDuration * 0.3 },
      { stepName: '清洗', workstationGroup: 'cold_processing', estimatedDuration: totalDuration * 0.3 }
    ],
    'hot_processing': [
      { stepName: '切割', workstationGroup: 'cold_processing', estimatedDuration: totalDuration * 0.2 },
      { stepName: '钢化', workstationGroup: 'hot_processing', estimatedDuration: totalDuration * 0.6 },
      { stepName: '检验', workstationGroup: 'hot_processing', estimatedDuration: totalDuration * 0.2 }
    ],
    'laminating': [
      { stepName: '切割', workstationGroup: 'cold_processing', estimatedDuration: totalDuration * 0.2 },
      { stepName: '夹胶准备', workstationGroup: 'laminating', estimatedDuration: totalDuration * 0.3 },
      { stepName: '夹胶压制', workstationGroup: 'laminating', estimatedDuration: totalDuration * 0.5 }
    ],
    'insulating': [
      { stepName: '切割', workstationGroup: 'cold_processing', estimatedDuration: totalDuration * 0.2 },
      { stepName: '中空组装', workstationGroup: 'insulating', estimatedDuration: totalDuration * 0.5 },
      { stepName: '密封', workstationGroup: 'insulating', estimatedDuration: totalDuration * 0.3 }
    ],
    'packaging': [
      { stepName: '质检', workstationGroup: 'packaging', estimatedDuration: totalDuration * 0.3 },
      { stepName: '包装', workstationGroup: 'packaging', estimatedDuration: totalDuration * 0.7 }
    ]
  };

  return processFlows[primaryWorkstation] || processFlows['cold_processing'];
};

// 生成测试数据
const generateTestData = async () => {
  loading.value = true;

  try {
    // 生成5个测试批次
    const batches: ScheduledBatch[] = [];
    const startTime = new Date();
    startTime.setDate(startTime.getDate() + 1); // 明天
    startTime.setHours(8, 0, 0, 0); // 早上8点
    
    for (let i = 0; i < 5; i++) {
      const batchStartTime = new Date(startTime.getTime() + i * 4 * 60 * 60 * 1000); // 每4小时一个批次
      const duration = 120 + Math.random() * 180; // 2-5小时随机持续时间
      const batchEndTime = new Date(batchStartTime.getTime() + duration * 60 * 1000);
      
      const workstations = ['cold_processing', 'hot_processing', 'laminating', 'insulating', 'packaging'];
      const priorities = ['urgent', 'high', 'normal', 'low'] as const;
      
      batches.push({
        id: `test_batch_${i + 1}`,
        name: `测试批次${i + 1}`,
        batchName: `测试批次${i + 1}`,
        workstation: workstations[i % workstations.length],
        priority: priorities[i % priorities.length],
        totalQuantity: 10 + Math.floor(Math.random() * 20),
        scheduledStartTime: batchStartTime.toISOString(),
        scheduledEndTime: batchEndTime.toISOString(),
        status: 'planned',
        items: [
          {
            id: `item_${i + 1}`,
            productName: `产品${i + 1}`,
            quantity: 10,
            specifications: {
              width: 1000,
              height: 800,
              thickness: 6
            },
            processFlow: generateProcessFlow(workstations[i % workstations.length], duration)
          }
        ],
        assignedResources: [
          {
            resourceId: `resource_${workstations[i % workstations.length]}`,
            resourceName: `${workstations[i % workstations.length]}工作站`,
            resourceType: 'equipment',
            utilization: 70 + Math.random() * 25
          }
        ],
        estimatedCost: 1000 + Math.random() * 2000,
        estimatedTime: duration,
        estimatedDuration: duration
      });
    }
    
    testBatches.value = batches;
    
    // 生成甘特图数据
    ganttData.value = await processSegmentGanttService.convertToProcessSegmentView(batches);
    
    console.log('测试数据生成完成:', {
      batches: batches.length,
      ganttTasks: ganttData.value?.tasks.length,
      ganttResources: ganttData.value?.resources.length
    });
    
  } catch (error) {
    console.error('生成测试数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 运行时间调试
const runTimeDebug = async () => {
  if (!testBatches.value.length || !ganttData.value) {
    return;
  }
  
  const batchAnalysis = GanttTimeDebugger.analyzeScheduledBatches(testBatches.value);
  const ganttAnalysis = GanttTimeDebugger.analyzeGanttData(ganttData.value);
  
  debugReport.value = GanttTimeDebugger.generateDebugReport(batchAnalysis, ganttAnalysis);
};

// 清除数据
const clearData = () => {
  testBatches.value = [];
  ganttData.value = null;
  debugReport.value = '';
};

// 任务点击处理
const handleTaskClick = (task: any) => {
  console.log('任务点击:', task);
};

// 格式化时间
const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleString('zh-CN');
};
</script>
