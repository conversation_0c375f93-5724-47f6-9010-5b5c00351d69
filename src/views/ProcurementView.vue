<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">采购管理</h1>
      <Button>
        <Plus class="mr-2 h-4 w-4" />
        新建采购单
      </Button>
    </div>
    
    <Card>
      <CardHeader>
        <CardTitle>采购订单</CardTitle>
        <CardDescription>
          管理物料采购和供应商关系
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="h-[400px] flex items-center justify-center text-muted-foreground">
          采购数据表格区域
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-vue-next'
</script>