<template>
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">组件测试页面</h1>
        <p class="text-muted-foreground mt-2">
          测试 Dialog、<PERSON>er、<PERSON><PERSON>、Tooltip 组件的功能
        </p>
      </div>

      <!-- 快速测试按钮 -->
      <Card>
        <CardHeader>
          <CardTitle>快速测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex flex-wrap gap-4">
            <!-- Toast 测试 -->
            <Button @click="testToast" variant="default">
              测试 Toast
            </Button>

            <!-- Dialog 测试 -->
            <Dialog v-model:open="dialogOpen">
              <DialogTrigger as-child>
                <Button variant="outline">测试 Dialog</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>测试对话框</DialogTitle>
                  <DialogDescription>
                    这是一个测试对话框，用于验证 Dialog 组件是否正常工作。
                  </DialogDescription>
                </DialogHeader>
                <div class="py-4">
                  <p>对话框内容正常显示。</p>
                </div>
                <DialogFooter>
                  <Button @click="dialogOpen = false">关闭</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <!-- Drawer 测试 -->
            <Drawer v-model:open="drawerOpen">
              <DrawerTrigger as-child>
                <Button variant="outline">测试 Drawer</Button>
              </DrawerTrigger>
              <DrawerContent>
                <div class="mx-auto w-full max-w-sm">
                  <DrawerHeader>
                    <DrawerTitle>测试抽屉</DrawerTitle>
                    <DrawerDescription>
                      这是一个测试抽屉组件
                    </DrawerDescription>
                  </DrawerHeader>
                  <div class="p-4">
                    <p>抽屉内容正常显示。</p>
                  </div>
                  <DrawerFooter>
                    <Button @click="drawerOpen = false">关闭</Button>
                  </DrawerFooter>
                </div>
              </DrawerContent>
            </Drawer>

            <!-- Tooltip 测试 -->
            <Tooltip>
              <TooltipTrigger as-child>
                <Button variant="outline">测试 Tooltip</Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>这是一个测试工具提示</p>
              </TooltipContent>
            </Tooltip>

            <!-- Sheet 测试 -->
            <Sheet v-model:open="sheetOpen">
              <SheetTrigger as-child>
                <Button variant="outline">测试 Sheet</Button>
              </SheetTrigger>
              <SheetContent side="right" class="w-[400px] sm:w-[540px]">
                <SheetHeader>
                  <SheetTitle>右侧设置面板</SheetTitle>
                  <SheetDescription>
                    这是一个测试 Sheet 组件的右侧面板
                  </SheetDescription>
                </SheetHeader>
                <div class="py-4 space-y-4">
                  <div class="space-y-2">
                    <Label>主题设置</Label>
                    <div class="flex items-center space-x-2">
                      <input type="radio" id="light" name="theme" />
                      <Label for="light">浅色</Label>
                    </div>
                    <div class="flex items-center space-x-2">
                      <input type="radio" id="dark" name="theme" />
                      <Label for="dark">深色</Label>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label>通知设置</Label>
                    <div class="flex items-center space-x-2">
                      <input type="checkbox" id="notifications" />
                      <Label for="notifications">启用通知</Label>
                    </div>
                  </div>
                </div>
                <SheetFooter>
                  <SheetClose as-child>
                    <Button>保存设置</Button>
                  </SheetClose>
                </SheetFooter>
              </SheetContent>
            </Sheet>

            <!-- Composable 测试 -->
            <Button @click="testComposables" variant="secondary">
              测试 Composables
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 状态显示 -->
      <Card>
        <CardHeader>
          <CardTitle>组件状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <div class="flex items-center gap-2">
              <span class="font-medium">Dialog 状态:</span>
              <Badge :variant="dialogOpen ? 'default' : 'secondary'">
                {{ dialogOpen ? '打开' : '关闭' }}
              </Badge>
            </div>
            <div class="flex items-center gap-2">
              <span class="font-medium">Drawer 状态:</span>
              <Badge :variant="drawerOpen ? 'default' : 'secondary'">
                {{ drawerOpen ? '打开' : '关闭' }}
              </Badge>
            </div>
            <div class="flex items-center gap-2">
              <span class="font-medium">Sheet 状态:</span>
              <Badge :variant="sheetOpen ? 'default' : 'secondary'">
                {{ sheetOpen ? '打开' : '关闭' }}
              </Badge>
            </div>
            <div class="flex items-center gap-2">
              <span class="font-medium">TooltipProvider:</span>
              <Badge variant="default">已配置</Badge>
            </div>
            <div class="flex items-center gap-2">
              <span class="font-medium">Sonner Toaster:</span>
              <Badge variant="default">已配置</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 确认对话框测试 -->
      <Card>
        <CardHeader>
          <CardTitle>确认对话框测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex gap-4">
            <Button @click="testConfirmDialog" variant="destructive">
              测试删除确认
            </Button>
            <Button @click="testBusinessDialog" variant="outline">
              测试业务对话框
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 通知测试 -->
      <Card>
        <CardHeader>
          <CardTitle>通知系统测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex flex-wrap gap-4">
            <Button @click="testBusinessNotifications.saveSuccess" variant="default">
              保存成功
            </Button>
            <Button @click="testBusinessNotifications.saveError" variant="destructive">
              保存失败
            </Button>
            <Button @click="testBusinessNotifications.networkError" variant="outline">
              网络错误
            </Button>
            <Button @click="testBusinessNotifications.validationError" variant="secondary">
              验证错误
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 确认对话框组件 -->
    <Dialog v-model:open="confirmDialog.isOpen.value">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{{ confirmDialog.title.value }}</DialogTitle>
          <DialogDescription v-if="confirmDialog.description.value">
            {{ confirmDialog.description.value }}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" @click="confirmDialog.handleCancel">
            {{ confirmDialog.cancelText.value }}
          </Button>
          <Button 
            :variant="confirmDialog.variant.value" 
            @click="confirmDialog.handleConfirm"
          >
            {{ confirmDialog.confirmText.value }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger
} from '@/components/ui/drawer'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from '@/components/ui/sheet'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { Label } from '@/components/ui/label'
import { useNotifications, useBusinessNotifications } from '@/composables/useNotifications'
import { useConfirmDialog, useBusinessDialogs } from '@/composables/useDialog'

// 组件状态
const dialogOpen = ref(false)
const drawerOpen = ref(false)
const sheetOpen = ref(false)

// Composables
const notifications = useNotifications()
const testBusinessNotifications = useBusinessNotifications()
const confirmDialog = useConfirmDialog()
const businessDialogs = useBusinessDialogs()

// 测试函数
const testToast = () => {
  notifications.success('测试成功！', '所有组件都正常工作')
}

const testComposables = () => {
  const loadingToast = notifications.loading('正在测试 Composables...')
  
  setTimeout(() => {
    notifications.dismiss(loadingToast)
    notifications.success('Composables 测试完成！', '所有功能都正常工作')
  }, 2000)
}

const testConfirmDialog = async () => {
  const confirmed = await confirmDialog.confirm({
    title: '确认删除',
    description: '这是一个测试确认对话框，确定要继续吗？',
    confirmText: '删除',
    cancelText: '取消',
    variant: 'destructive'
  })

  if (confirmed) {
    notifications.success('确认成功', '用户点击了确认按钮')
  } else {
    notifications.info('操作取消', '用户点击了取消按钮')
  }
}

const testBusinessDialog = async () => {
  const confirmed = await businessDialogs.confirmDelete('测试项目')
  
  if (confirmed) {
    testBusinessNotifications.deleteSuccess('测试项目')
  } else {
    notifications.info('删除已取消')
  }
}
</script>
