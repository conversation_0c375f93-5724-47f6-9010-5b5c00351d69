<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">状态管理系统测试</h1>
    </div>
    
    <!-- 用户状态测试 -->
    <Card>
      <CardHeader>
        <CardTitle>用户状态管理</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-3 gap-4">
          <div>
            <label class="text-sm font-medium">认证状态:</label>
            <p class="text-lg">{{ userStore.isAuthenticated ? '已登录' : '未登录' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">用户名:</label>
            <p class="text-lg">{{ userStore.currentUser?.name || '无' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">权限数量:</label>
            <p class="text-lg">{{ userStore.permissions.length }}</p>
          </div>
        </div>
        
        <div class="flex gap-2">
          <Button @click="testUserLogin" size="sm" :disabled="userStore.isLoading">
            测试登录
          </Button>
          <Button @click="testUserLogout" size="sm" variant="outline" :disabled="userStore.isLoading">
            测试登出
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 应用状态测试 -->
    <Card>
      <CardHeader>
        <CardTitle>应用全局状态</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-3 gap-4">
          <div>
            <label class="text-sm font-medium">侧边栏状态:</label>
            <p class="text-lg">{{ appStore.sidebarCollapsed ? '收起' : '展开' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">当前主题:</label>
            <p class="text-lg">{{ appStore.currentTheme }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">未读通知:</label>
            <p class="text-lg">{{ appStore.unreadNotifications }}</p>
          </div>
        </div>
        
        <div class="flex gap-2">
          <Button @click="appStore.toggleSidebar" size="sm">
            切换侧边栏
          </Button>
          <Button @click="appStore.toggleTheme" size="sm" variant="outline">
            切换主题
          </Button>
          <Button @click="addTestNotification" size="sm" variant="secondary">
            添加通知
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 业务数据状态测试 -->
    <Card>
      <CardHeader>
        <CardTitle>业务数据状态</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-4 gap-4">
          <div>
            <label class="text-sm font-medium">订单数量:</label>
            <p class="text-lg">{{ businessStore.recentOrders.length }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">库存项目:</label>
            <p class="text-lg">{{ businessStore.stockItems.length }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">低库存预警:</label>
            <p class="text-lg text-red-600">{{ businessStore.lowStockCount }}</p>
          </div>
          <div>
            <label class="text-sm font-medium">客户数量:</label>
            <p class="text-lg">{{ businessStore.customers.length }}</p>
          </div>
        </div>
        
        <div class="flex gap-2">
          <Button @click="businessStore.loadRecentOrders()" size="sm" :disabled="businessStore.isLoadingOrders">
            {{ businessStore.isLoadingOrders ? '加载中...' : '加载订单' }}
          </Button>
          <Button @click="businessStore.loadStockItems()" size="sm" variant="outline" :disabled="businessStore.isLoadingStock">
            {{ businessStore.isLoadingStock ? '加载中...' : '加载库存' }}
          </Button>
          <Button @click="businessStore.loadCustomers()" size="sm" variant="secondary" :disabled="businessStore.isLoadingCustomers">
            {{ businessStore.isLoadingCustomers ? '加载中...' : '加载客户' }}
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 订单状态统计 -->
    <Card>
      <CardHeader>
        <CardTitle>订单状态统计</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-5 gap-4">
          <div class="text-center">
            <p class="text-2xl font-bold">{{ businessStore.ordersByStatus.draft }}</p>
            <p class="text-sm text-muted-foreground">草稿</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold">{{ businessStore.ordersByStatus.confirmed }}</p>
            <p class="text-sm text-muted-foreground">已确认</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold">{{ businessStore.ordersByStatus.in_production }}</p>
            <p class="text-sm text-muted-foreground">生产中</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold">{{ businessStore.ordersByStatus.completed }}</p>
            <p class="text-sm text-muted-foreground">已完成</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold">{{ businessStore.ordersByStatus.cancelled }}</p>
            <p class="text-sm text-muted-foreground">已取消</p>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 低库存预警列表 -->
    <Card v-if="businessStore.lowStockItems.length > 0">
      <CardHeader>
        <CardTitle class="text-red-600">低库存预警</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-2">
          <div 
            v-for="item in businessStore.lowStockItems" 
            :key="item.id"
            class="flex justify-between items-center p-3 bg-red-50 border border-red-200 rounded-md"
          >
            <div>
              <p class="font-medium">{{ item.materialName }}</p>
              <p class="text-sm text-muted-foreground">位置: {{ item.location }}</p>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-red-600">{{ item.quantity }} {{ item.unit }}</p>
              <p class="text-sm text-muted-foreground">最低库存: {{ item.minStock }}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 错误信息显示 -->
    <Card v-if="hasErrors">
      <CardHeader>
        <CardTitle class="text-red-600">错误信息</CardTitle>
      </CardHeader>
      <CardContent class="space-y-2">
        <div v-if="userStore.error" class="p-3 bg-red-50 border border-red-200 rounded-md">
          <p class="text-red-800">用户状态错误: {{ userStore.error }}</p>
        </div>
        <div v-if="businessStore.ordersError" class="p-3 bg-red-50 border border-red-200 rounded-md">
          <p class="text-red-800">订单加载错误: {{ businessStore.ordersError }}</p>
        </div>
        <div v-if="businessStore.stockError" class="p-3 bg-red-50 border border-red-200 rounded-md">
          <p class="text-red-800">库存加载错误: {{ businessStore.stockError }}</p>
        </div>
        <Button @click="clearAllErrors" size="sm" variant="outline" class="mt-2">
          清除所有错误
        </Button>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { useBusinessStore } from '@/stores/business'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

const userStore = useUserStore()
const appStore = useAppStore()
const businessStore = useBusinessStore()

const hasErrors = computed(() => {
  return !!(userStore.error || businessStore.ordersError || businessStore.stockError || businessStore.customersError)
})

const testUserLogin = async () => {
  await userStore.login({
    username: 'admin',
    password: 'admin'
  })
}

const testUserLogout = async () => {
  await userStore.logout()
}

const addTestNotification = () => {
  appStore.addNotification({
    type: 'info',
    title: '测试通知',
    message: `这是一个测试通知，时间：${new Date().toLocaleTimeString()}`,
    read: false
  })
}

const clearAllErrors = () => {
  userStore.clearError()
  businessStore.clearErrors()
}
</script>
