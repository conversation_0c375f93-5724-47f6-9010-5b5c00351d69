<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">仪表盘</h1>
      <div class="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          @click="toggleAutoRefresh"
          :class="{ 'bg-green-50 border-green-200 text-green-700': autoRefreshEnabled }"
        >
          <div class="h-2 w-2 rounded-full mr-2"
               :class="autoRefreshEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400'">
          </div>
          {{ autoRefreshEnabled ? '自动刷新' : '手动模式' }}
        </Button>
        <Button variant="outline" size="sm" @click="refreshData(true)" :disabled="isRefreshing">
          <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': isRefreshing }" />
          刷新数据
        </Button>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
      <Card class="hover:shadow-md transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">今日订单</CardTitle>
          <ShoppingCart class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ businessStore.recentOrders.length }}</div>
          <p class="text-xs text-muted-foreground">
            总订单数量
          </p>
        </CardContent>
      </Card>

      <Card class="hover:shadow-md transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">生产进度</CardTitle>
          <Factory class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ businessStore.inProductionOrders.length }}</div>
          <p class="text-xs text-muted-foreground">
            生产中订单
          </p>
        </CardContent>
      </Card>

      <Card class="hover:shadow-md transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">库存预警</CardTitle>
          <AlertTriangle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ businessStore.lowStockCount }}</div>
          <p class="text-xs text-muted-foreground">
            需要补货的物料
          </p>
        </CardContent>
      </Card>

      <Card class="hover:shadow-md transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">质量合格率</CardTitle>
          <CheckCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ qualityRate }}%</div>
          <p class="text-xs text-muted-foreground">
            +0.2% 较昨日
          </p>
        </CardContent>
      </Card>
    </div>

    <!-- 图表区域 -->
    <div class="grid gap-4 grid-cols-1 xl:grid-cols-7">
      <!-- 订单趋势图表 -->
      <Card class="xl:col-span-4 hover:shadow-md transition-shadow">
        <CardHeader>
          <CardTitle>订单趋势</CardTitle>
          <p class="text-sm text-muted-foreground">近7天订单数量和金额变化</p>
        </CardHeader>
        <CardContent>
          <LineChart
            :data="orderTrendData"
            :x-axis-data="last7Days"
            :height="chartHeight"
            :show-legend="true"
          />
        </CardContent>
      </Card>

      <!-- 库存状态饼图 -->
      <Card class="xl:col-span-3 hover:shadow-md transition-shadow">
        <CardHeader>
          <CardTitle>库存状态分布</CardTitle>
          <p class="text-sm text-muted-foreground">当前库存水位分布</p>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="stockStatusData"
            :height="chartHeight"
            :show-legend="true"
            radius="60%"
          />
        </CardContent>
      </Card>
    </div>

    <!-- 生产进度和最近活动 -->
    <div class="grid gap-4 grid-cols-1 xl:grid-cols-7">
      <!-- 生产进度图表 -->
      <Card class="xl:col-span-4 hover:shadow-md transition-shadow">
        <CardHeader>
          <CardTitle>生产进度概览</CardTitle>
          <p class="text-sm text-muted-foreground">各状态订单数量分布</p>
        </CardHeader>
        <CardContent>
          <BarChart
            :data="productionProgressData"
            :x-axis-data="orderStatusLabels"
            :height="chartHeight"
            :show-legend="false"
          />
        </CardContent>
      </Card>

      <!-- 最近活动 -->
      <Card class="xl:col-span-3 hover:shadow-md transition-shadow">
        <CardHeader>
          <CardTitle>最近活动</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4 max-h-80 overflow-y-auto">
            <div v-for="activity in recentActivities" :key="activity.id"
                 class="flex items-start p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
              <div class="space-y-1">
                <p class="text-sm font-medium leading-none">
                  {{ activity.title }}
                </p>
                <p class="text-xs text-muted-foreground">
                  {{ activity.time }}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, computed, ref } from 'vue'
import { useBusinessStore } from '@/stores/business'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { LineChart, BarChart, PieChart } from '@/components/charts'
import { ShoppingCart, Factory, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-vue-next'

const businessStore = useBusinessStore()
const isRefreshing = ref(false)
const autoRefreshInterval = ref<NodeJS.Timeout | null>(null)
const autoRefreshEnabled = ref(true)
const refreshIntervalMinutes = 5 // 5分钟自动刷新一次

// 响应式图表高度
const chartHeight = computed(() => {
  // 根据屏幕尺寸调整图表高度
  if (typeof window !== 'undefined') {
    return window.innerWidth < 768 ? '250px' : '300px'
  }
  return '300px'
})

// 质量合格率（模拟数据）
const qualityRate = computed(() => {
  return (98.5 + Math.random() * 0.5).toFixed(1)
})

// 近7天日期
const last7Days = computed(() => {
  const days = []
  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    days.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
  }
  return days
})

// 订单趋势数据
const orderTrendData = computed(() => {
  // 模拟近7天的订单数量和金额数据
  const orderCounts = [12, 15, 8, 22, 18, 25, businessStore.recentOrders.length]
  const orderAmounts = [120000, 180000, 95000, 260000, 210000, 320000,
    businessStore.recentOrders.reduce((sum, order) => sum + order.totalAmount, 0) / 1000]

  return [
    {
      name: '订单数量',
      data: orderCounts
    },
    {
      name: '订单金额(千元)',
      data: orderAmounts
    }
  ]
})

// 库存状态数据
const stockStatusData = computed(() => {
  const totalStock = businessStore.stockItems.length
  const lowStock = businessStore.lowStockCount
  const normalStock = totalStock - lowStock

  return [
    { name: '正常库存', value: normalStock },
    { name: '低库存预警', value: lowStock }
  ]
})

// 生产进度数据
const productionProgressData = computed(() => {
  const statusCounts = [
    businessStore.pendingOrders.length,
    businessStore.inProductionOrders.length,
    businessStore.completedOrders.length
  ]

  return [
    {
      name: '订单数量',
      data: statusCounts
    }
  ]
})

// 订单状态标签
const orderStatusLabels = ['待生产', '生产中', '已完成']

// 最近活动数据
const recentActivities = computed(() => [
  {
    id: 1,
    title: `订单 #${businessStore.recentOrders[0]?.id || '2024001'} 已完成生产`,
    time: '2小时前'
  },
  {
    id: 2,
    title: `新客户注册：${businessStore.customers[0]?.name || '建筑公司B'}`,
    time: '4小时前'
  },
  {
    id: 3,
    title: `库存补货：${businessStore.lowStockItems[0]?.name || '6mm透明玻璃'}`,
    time: '6小时前'
  }
])

// 刷新数据
const refreshData = async (showLoading = true) => {
  if (showLoading) {
    isRefreshing.value = true
  }
  try {
    await businessStore.initializeBusinessData()
    // 模拟网络延迟
    if (showLoading) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  } finally {
    if (showLoading) {
      isRefreshing.value = false
    }
  }
}

// 启动自动刷新
const startAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
  }

  if (autoRefreshEnabled.value) {
    autoRefreshInterval.value = setInterval(() => {
      refreshData(false) // 自动刷新时不显示加载状态
    }, refreshIntervalMinutes * 60 * 1000)
  }
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
    autoRefreshInterval.value = null
  }
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefreshEnabled.value = !autoRefreshEnabled.value
  if (autoRefreshEnabled.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

onMounted(async () => {
  // 初始化业务数据
  await businessStore.initializeBusinessData()
  // 启动自动刷新
  startAutoRefresh()
})

onUnmounted(() => {
  // 清理定时器
  stopAutoRefresh()
})
</script>