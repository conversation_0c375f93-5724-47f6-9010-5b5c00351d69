<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ValueMetricsCard from '@/components/mes/ValueMetricsCard.vue'
import TelemetryPanel from '@/components/mes/TelemetryPanel.vue'
import { aggregateMetrics } from '@/utils/ValueMetricsAggregator'
import { mesApi } from '@/utils/mesApi'
import { recordEvent } from '@/utils/Telemetry'

const strategy = ref<any>(null)
const valueComparison = ref<any>(null)
const metrics = ref<any[]>([])

async function loadData() {
  const t0 = performance.now()
  const [s, v] = await Promise.all([
    mesApi.getAlgorithmStrategy(),
    mesApi.getCuttingValueComparison()
  ])
  strategy.value = s.ok ? s.data : null
  valueComparison.value = v.ok ? v.data : null
  metrics.value = await aggregateMetrics()
  recordEvent('page_load_cutting', {}, performance.now() - t0)
}

function switchPhase(next: 'validation'|'trial'|'full') {
  // 原型占位：仅前端切换展示
  strategy.value.phase = next
  recordEvent('algo_switch_phase', { to: next })
}

function rollback() {
  // 原型占位：切回fallback算法
  const tmp = strategy.value.primary
  strategy.value.primary = strategy.value.fallback
  strategy.value.fallback = tmp
  recordEvent('algo_rollback', { to: strategy.value.primary })
}

onMounted(loadData)
</script>

<template>
  <div class="p-4 space-y-4">
    <h2 class="text-xl font-semibold">排版优化价值验证</h2>

    <div class="border rounded p-3">
      <div class="flex items-center justify-between">
        <div>
          <div>当前阶段：<b>{{ strategy?.phase }}</b></div>
          <div>主要算法：<b>{{ strategy?.primary }}</b> ｜ 回退算法：<b>{{ strategy?.fallback }}</b></div>
        </div>
        <div class="space-x-2">
          <button class="px-2 py-1 bg-blue-600 text-white rounded" @click="switchPhase('validation')">验证期</button>
          <button class="px-2 py-1 bg-blue-600 text-white rounded" @click="switchPhase('trial')">试用期</button>
          <button class="px-2 py-1 bg-blue-600 text-white rounded" @click="switchPhase('full')">全面切换</button>
          <button class="px-2 py-1 bg-amber-600 text-white rounded" @click="rollback()">一键回退</button>
        </div>
      </div>
      <div class="text-sm text-gray-600 mt-2">
        切换准入：利用率≥{{ strategy?.switchCriteria?.minUtilizationImprovement }}%｜耗时≤{{ strategy?.switchCriteria?.maxComputationTimeSec }}s｜稳定性≥{{ strategy?.switchCriteria?.stabilityScore }}
      </div>
    </div>

    <div class="border rounded p-3">
      <h3 class="font-medium mb-2">价值对比</h3>
      <div v-if="valueComparison">
        <div>传统利用率：{{ valueComparison.traditionalMethod.utilizationRate }}%</div>
        <div>智能利用率：{{ valueComparison.optimizedMethod.utilizationRate }}%</div>
        <div class="font-bold">提升：{{ valueComparison.valueMetrics.utilizationImprovement }}%</div>
        <div>预计ROI：{{ valueComparison.valueMetrics.roiMonths }} 个月回本</div>
      </div>
      <div class="mt-3">
        <ValueMetricsCard :metrics="metrics" />
      </div>
    </div>
    <TelemetryPanel />
  </div>
</template>

<style scoped>
</style>

