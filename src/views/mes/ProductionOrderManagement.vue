<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- 页面标题和操作栏 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">生产工单管理</h1>
        <p class="text-gray-600 mt-1">基于客户订单的MTO生产工单管理，支持完整业务追溯和产品族工艺路线</p>
      </div>
      <div class="flex gap-3">
        <Button @click="refreshWorkOrders" variant="outline">
          <RefreshCw class="w-4 h-4 mr-2" />
          刷新数据
        </Button>
        <Button @click="showDataQualityReport = true" variant="outline">
          <BarChart3 class="w-4 h-4 mr-2" />
          数据质量报告
        </Button>
        <Button @click="showCreateDialog = true" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg">
          <Plus class="w-4 h-4 mr-2" />
          智能创建工单
          <span class="ml-2 text-xs bg-white/20 px-2 py-0.5 rounded-full">MTO</span>
        </Button>
      </div>
    </div>

    <!-- 数据流转概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">工单总数</p>
              <p class="text-2xl font-bold text-blue-600">{{ workOrderStats.total }}</p>
            </div>
            <FileText class="w-8 h-8 text-blue-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">覆盖{{ workOrderStats.totalItems }}个订单项</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">关联客户订单数</p>
              <p class="text-2xl font-bold text-green-600">{{ workOrderStats.linkedCustomerOrders }}</p>
            </div>
            <Link class="w-8 h-8 text-green-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">客户订单关联</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">产品族覆盖</p>
              <p class="text-2xl font-bold text-purple-600">{{ workOrderStats.productFamilies }}</p>
            </div>
            <Layers class="w-8 h-8 text-purple-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">种产品族类型</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">紧急工单</p>
              <p class="text-2xl font-bold text-red-600">{{ workOrderStats.urgent }}</p>
            </div>
            <AlertTriangle class="w-8 h-8 text-red-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">优先处理</p>
        </CardContent>
      </Card>
    </div>

    <!-- 筛选和搜索 -->
    <Card>
      <CardContent class="p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <div class="flex-1 min-w-64">
            <Input
              v-model="searchQuery"
              placeholder="搜索工单号、客户名称、订单编号..."
              class="w-full"
            />
          </div>
          <Select v-model="statusFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="工单状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="pending">待发布</SelectItem>
              <SelectItem value="released">已发布</SelectItem>
              <SelectItem value="in_progress">执行中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
            </SelectContent>
          </Select>
          <Select v-model="priorityFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="优先级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部优先级</SelectItem>
              <SelectItem value="urgent">紧急</SelectItem>
              <SelectItem value="high">高</SelectItem>
              <SelectItem value="normal">普通</SelectItem>
              <SelectItem value="low">低</SelectItem>
            </SelectContent>
          </Select>
          <Select v-model="productFamilyFilter">
            <SelectTrigger class="w-48">
              <SelectValue placeholder="产品族类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部产品族</SelectItem>
              <SelectItem value="PF-TEMPERED">单片钢化玻璃</SelectItem>
              <SelectItem value="PF-IGU">中空玻璃</SelectItem>
              <SelectItem value="PF-LAMINATED">夹胶玻璃</SelectItem>
              <SelectItem value="PF-DECORATIVE">装饰玻璃</SelectItem>
              <SelectItem value="PF-FURNITURE">家具玻璃</SelectItem>
            </SelectContent>
          </Select>
          <Select v-model="orderTypeFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="订单类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="幕墙工程">幕墙工程</SelectItem>
              <SelectItem value="门窗工程">门窗工程</SelectItem>
              <SelectItem value="办公隔断">办公隔断</SelectItem>
              <SelectItem value="中空玻璃">中空玻璃</SelectItem>
              <SelectItem value="夹胶玻璃">夹胶玻璃</SelectItem>
              <SelectItem value="装饰玻璃">装饰玻璃</SelectItem>
              <SelectItem value="家具玻璃">家具玻璃</SelectItem>
              <SelectItem value="阳光房">阳光房</SelectItem>
              <SelectItem value="展示柜">展示柜</SelectItem>
              <SelectItem value="特殊工艺">特殊工艺</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>

    <!-- 生产工单列表 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle class="flex items-center gap-2">
            <FileText class="w-5 h-5" />
            生产工单列表
            <Badge variant="secondary">{{ filteredWorkOrders.length }} 个工单</Badge>
          </CardTitle>

        </div>
      </CardHeader>
      <CardContent>
        <!-- 卡片视图 -->
        <div class="space-y-4">
          <div
            v-for="workOrder in filteredWorkOrders"
            :key="workOrder.id"
            class="border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer"
            :class="getWorkOrderCardClass(workOrder)"
            @click="toggleWorkOrderExpansion(workOrder.id)"
          >
            <!-- 工单头部 -->
            <div class="p-4 border-b bg-gradient-to-r from-gray-50 to-white">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <!-- 工单号和状态 -->
                    <h3 class="font-bold text-lg text-gray-900">{{ workOrder.workOrderNumber }}</h3>
                    <Badge :variant="getStatusVariant(workOrder.status)" class="font-medium">
                      {{ getStatusText(workOrder.status) }}
                    </Badge>
                    <Badge :variant="getPriorityVariant(workOrder.priority)" class="font-medium">
                      {{ getPriorityText(workOrder.priority) }}
                    </Badge>
                    <!-- 延期风险指示器 -->
                    <div v-if="isDelayRisk(workOrder)" class="flex items-center gap-1 text-red-600">
                      <AlertTriangle class="w-4 h-4" />
                      <span class="text-xs font-medium">延期风险</span>
                    </div>
                  </div>

                  <!-- 客户和订单信息（支持多对多关系） -->
                  <div class="space-y-2">
                    <!-- 主要客户信息 -->
                    <div class="flex items-center gap-4 text-sm">
                      <div class="flex items-center gap-2">
                        <Building2 class="w-4 h-4 text-gray-500" />
                        <span class="font-medium text-gray-900">
                          {{ getUniqueCustomerNames(workOrder).length === 1
                              ? getUniqueCustomerNames(workOrder)[0]
                              : `${getUniqueCustomerNames(workOrder).length}个客户` }}
                        </span>
                      </div>
                      <div class="flex items-center gap-2">
                        <FileText class="w-4 h-4 text-gray-500" />
                        <span class="text-blue-600">
                          {{ getUniqueOrderNumbers(workOrder).length === 1
                              ? getUniqueOrderNumbers(workOrder)[0]
                              : `${getUniqueOrderNumbers(workOrder).length}个订单` }}
                        </span>
                      </div>
                      <div class="flex items-center gap-2">
                        <Tag class="w-4 h-4 text-gray-500" />
                        <span class="text-gray-600">{{ getOrderTypesFromWorkOrder(workOrder) }}</span>
                      </div>
                    </div>

                    <!-- 多客户订单详情（当包含多个订单时展示） -->
                    <div v-if="getUniqueCustomerNames(workOrder).length > 1 || getUniqueOrderNumbers(workOrder).length > 1"
                         class="flex flex-wrap gap-2">
                      <div
                        v-for="orderInfo in getCustomerOrderInfo(workOrder)"
                        :key="orderInfo.orderId"
                        class="flex items-center gap-2 px-2 py-1 bg-blue-50 rounded text-xs"
                      >
                        <span class="font-medium text-blue-800">{{ orderInfo.customerName }}</span>
                        <span
                          class="text-blue-600 hover:underline cursor-pointer"
                          @click.stop="jumpToCustomerOrder(orderInfo.orderId)"
                        >
                          {{ orderInfo.orderNumber }}
                        </span>
                        <Badge size="sm" variant="outline" class="text-xs">
                          {{ orderInfo.itemCount }}项
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flex flex-col items-center gap-2">
                  <!-- 进度环形图 -->
                  <div class="relative w-16 h-16">
                    <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#e5e7eb"
                        stroke-width="2"
                      />
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        :stroke="getProgressColor(workOrder)"
                        stroke-width="2"
                        :stroke-dasharray="`${getWorkOrderProgress(workOrder)}, 100`"
                        stroke-linecap="round"
                      />
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                      <span class="text-xs font-bold text-gray-700">{{ getWorkOrderProgress(workOrder) }}%</span>
                    </div>
                  </div>
                  <span class="text-xs text-gray-500">完成进度</span>

                  <!-- 展开指示器 -->
                  <div class="flex items-center gap-1 text-xs text-gray-500">
                    <component
                      :is="expandedWorkOrders.has(workOrder.id) ? 'ChevronUp' : 'ChevronDown'"
                      class="w-4 h-4"
                    />
                    <span>{{ expandedWorkOrders.has(workOrder.id) ? '收起' : '展开' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工单内容 -->
            <div class="p-4">
              <!-- 基础信息行 -->
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div class="flex items-center gap-2">
                  <Package class="w-4 h-4 text-gray-500" />
                  <div>
                    <div class="text-xs text-gray-500">工单明细</div>
                    <div class="font-medium">{{ workOrder.items.length }}项</div>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <Layers class="w-4 h-4 text-gray-500" />
                  <div>
                    <div class="text-xs text-gray-500">产品族</div>
                    <div class="font-medium">{{ getProductFamilyCount(workOrder) }}种</div>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <Clock class="w-4 h-4 text-gray-500" />
                  <div>
                    <div class="text-xs text-gray-500">计划完成</div>
                    <div class="font-medium">{{ formatDate(workOrder.plannedEndDate) }}</div>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <TrendingUp class="w-4 h-4 text-gray-500" />
                  <div>
                    <div class="text-xs text-gray-500">总数量</div>
                    <div class="font-medium">{{ getTotalQuantity(workOrder) }}片</div>
                  </div>
                </div>
              </div>

              <!-- 详细信息（点击展开显示） -->
              <div v-if="expandedWorkOrders.has(workOrder.id)" class="space-y-3">
                <!-- 产品族分布 -->
                <div>
                  <div class="text-sm font-medium text-gray-700 mb-2">产品族分布</div>
                  <div class="flex flex-wrap gap-2">
                    <div
                      v-for="familyInfo in getProductFamilyInfo(workOrder)"
                      :key="familyInfo.id"
                      class="flex items-center gap-2 px-3 py-1 rounded-full text-xs"
                      :class="getProductFamilyBgClass(familyInfo.id)"
                    >
                      <div class="w-2 h-2 rounded-full" :class="getProductFamilyDotClass(familyInfo.id)"></div>
                      <span class="font-medium">{{ familyInfo.name }}</span>
                      <span class="text-gray-600">{{ familyInfo.count }}项</span>
                    </div>
                  </div>
                </div>

                <!-- 当前工序状态 -->
                <div>
                  <div class="flex items-center justify-between mb-3">
                    <div class="text-sm font-medium text-gray-700">当前工序状态</div>
                    <div class="flex items-center gap-2 text-xs text-gray-500">
                      <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>进行中</span>
                      </div>
                      <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span>待处理</span>
                      </div>
                      <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                        <span>未开始</span>
                      </div>
                    </div>
                  </div>

                  <!-- 工序状态表格 -->
                  <div class="overflow-hidden border rounded-lg">
                    <!-- 表格头部 -->
                    <div class="grid grid-cols-12 gap-2 px-3 py-2 bg-gray-100 text-xs font-medium text-gray-700 border-b">
                      <div class="col-span-3">产品规格</div>
                      <div class="col-span-2">产品族/类型</div>
                      <div class="col-span-1 text-center">数量</div>
                      <div class="col-span-2">当前工序</div>
                      <div class="col-span-1 text-center">进度</div>
                      <div class="col-span-2">预计完成</div>
                      <div class="col-span-1 text-center">状态</div>
                    </div>

                    <!-- 表格内容 -->
                    <div class="divide-y">
                      <div
                        v-for="item in workOrder.items.slice(0, expandedWorkOrders.has(workOrder.id) ? workOrder.items.length : 6)"
                        :key="item.id"
                        class="grid grid-cols-12 gap-2 px-3 py-3 hover:bg-gray-50 transition-colors text-sm"
                        :class="getWorkOrderItemCardClass(item)"
                      >
                        <!-- 产品规格 -->
                        <div class="col-span-3">
                          <div class="font-medium text-gray-900">
                            {{ item.specifications.length || item.specifications.width }}×{{ item.specifications.width || item.specifications.length }}×{{ item.specifications.thickness }}mm
                          </div>
                          <div class="text-xs text-gray-500 mt-1">
                            {{ getGlassTypeDisplay(item.specifications.glassType) }} | {{ item.specifications.color || '透明' }}
                          </div>
                          <div class="text-xs text-gray-500">
                            来源: {{ (item as any).customerOrderNumber || '未指定客户订单' }}
                          </div>
                        </div>

                        <!-- 产品族/类型 -->
                        <div class="col-span-2">
                          <Badge size="sm" :variant="getProductFamilyVariant((item as any).productFamilyId)" class="text-xs mb-1">
                            {{ getProductFamilyShortName((item as any).productFamilyId) }}
                          </Badge>
                          <div v-if="hasQualityCheckpoint(item)" class="flex items-center gap-1 text-amber-600 text-xs">
                            <Shield class="w-3 h-3" />
                            <span>质检节点</span>
                          </div>
                        </div>

                        <!-- 数量 -->
                        <div class="col-span-1 text-center">
                          <div class="font-medium text-gray-900">{{ item.quantity }}</div>
                          <div class="text-xs text-gray-500">片</div>
                        </div>

                        <!-- 当前工序 -->
                        <div class="col-span-2">
                          <div class="font-medium text-gray-900 text-xs mb-1">
                            {{ getCurrentProcessName(item) }}
                          </div>
                          <div class="text-xs text-gray-500">
                            工位: {{ getCurrentWorkstation(item) }}
                          </div>
                          <div class="text-xs text-gray-500">
                            {{ getCurrentProcessStep(item) }}/{{ getTotalProcessSteps(item) }}道工序
                          </div>
                        </div>

                        <!-- 进度 -->
                        <div class="col-span-1 text-center">
                          <div class="font-medium text-gray-900 mb-1">{{ getProcessProgress(item) }}%</div>
                          <div class="w-full bg-gray-200 rounded-full h-1.5">
                            <div
                              class="h-1.5 rounded-full transition-all duration-300"
                              :class="getProcessProgressBarClass(item)"
                              :style="{ width: `${getProcessProgress(item)}%` }"
                            ></div>
                          </div>
                        </div>

                        <!-- 预计完成 -->
                        <div class="col-span-2">
                          <div class="text-xs text-gray-900 mb-1">
                            {{ getEstimatedCompletionTime(item) }}
                          </div>
                          <div v-if="isDelayedItem(item)" class="flex items-center gap-1 text-red-600 text-xs">
                            <AlertTriangle class="w-3 h-3" />
                            <span>延期风险</span>
                          </div>
                          <div v-if="expandedWorkOrders.has(workOrder.id)" class="text-xs text-blue-600">
                            下一工序: {{ getNextProcessName(item) }}
                          </div>
                        </div>

                        <!-- 状态 -->
                        <div class="col-span-1 text-center">
                          <Badge size="sm" :variant="getProcessStatusVariant(item.currentStatus || '待排版')" class="text-xs">
                            {{ item.currentStatus || '待排版' }}
                          </Badge>
                          <div v-if="expandedWorkOrders.has(workOrder.id)" class="text-xs text-gray-500 mt-1">
                            {{ getOperatorName(item) }}
                          </div>
                        </div>
                      </div>

                    <!-- 显示更多按钮 -->
                    <div v-if="workOrder.items.length > 6 && !expandedWorkOrders.has(workOrder.id)"
                         class="flex items-center justify-center p-3 border-2 border-dashed border-gray-300 rounded-lg text-sm text-gray-500 hover:border-gray-400 hover:text-gray-600 cursor-pointer transition-colors"
                         @click.stop="showAllItems(workOrder)">
                      <Plus class="w-4 h-4 mr-2" />
                      查看更多工单项 ({{ workOrder.items.length - 6 }}个)
                    </div>
                    </div>
                  </div>
                </div>

                <!-- 完整信息（仅在展开模式显示） -->
                <div v-if="expandedWorkOrders.has(workOrder.id)">
                  <!-- 资源配置信息 -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-3 bg-blue-50 rounded-lg">
                    <div class="text-center">
                      <div class="text-lg font-bold text-blue-600">{{ getEstimatedHours(workOrder) }}h</div>
                      <div class="text-xs text-blue-700">预计工时</div>
                    </div>
                    <div class="text-center">
                      <div class="text-lg font-bold text-green-600">{{ getRequiredEquipment(workOrder) }}</div>
                      <div class="text-xs text-green-700">所需设备</div>
                    </div>
                    <div class="text-center">
                      <div class="text-lg font-bold text-purple-600">{{ getQualityCheckpoints(workOrder) }}</div>
                      <div class="text-xs text-purple-700">质检节点</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮区域 -->
              <div class="flex justify-between items-center mt-4 pt-3 border-t">
                <div class="flex items-center gap-2">
                  <!-- 角色相关的快捷信息 -->
                  <div class="flex items-center gap-4 text-xs text-gray-500">
                    <div class="flex items-center gap-1">
                      <Users class="w-3 h-3" />
                      <span>{{ getRequiredWorkers(workOrder) }}人</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <Zap class="w-3 h-3" />
                      <span>{{ getCapacityUtilization(workOrder) }}%产能</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <Shield class="w-3 h-3" />
                      <span>{{ getQualityLevel(workOrder) }}</span>
                    </div>
                  </div>
                </div>

                <div class="flex items-center gap-2">
                  <!-- 快捷操作按钮 -->
                  <Button size="sm" variant="ghost" @click.stop="viewWorkOrderDetails(workOrder)">
                    <Eye class="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="ghost" @click.stop="viewProcessFlow(workOrder)">
                    <Workflow class="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="ghost" @click.stop="jumpToCustomerOrder(workOrder.customerOrderId)">
                    <ExternalLink class="w-4 h-4" />
                  </Button>

                  <!-- 状态相关操作 -->
                  <Button
                    v-if="workOrder.status === 'pending'"
                    size="sm"
                    @click.stop="releaseWorkOrder(workOrder)"
                    class="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <Play class="w-4 h-4 mr-1" />
                    发布工单
                  </Button>
                  <Button
                    v-if="workOrder.status === 'pending'"
                    size="sm"
                    variant="outline"
                    @click.stop="openProductionRelease(workOrder)"
                  >
                    <Settings class="w-4 h-4 mr-1" />
                    生产发布
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>




      </CardContent>
    </Card>
    
    <!-- 工单详情对话框 -->
    <Dialog v-model:open="showWorkOrderDetails">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>生产工单详情 - {{ selectedWorkOrder?.workOrderNumber }}</DialogTitle>
        </DialogHeader>
        
        <div v-if="selectedWorkOrder" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">工单号</Label>
              <p class="text-sm text-gray-600">{{ selectedWorkOrder.workOrderNumber }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">客户订单</Label>
              <Button 
                variant="link" 
                size="sm" 
                class="p-0 h-auto text-blue-600"
                @click="jumpToCustomerOrder(selectedWorkOrder.customerOrderId)"
              >
                {{ selectedWorkOrder.customerOrderNumber }}
              </Button>
            </div>
            <div>
              <Label class="text-sm font-medium">客户名称</Label>
              <p class="text-sm text-gray-600">{{ selectedWorkOrder.customerName }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">优先级</Label>
              <Badge :variant="getPriorityVariant(selectedWorkOrder.priority)">
                {{ getPriorityText(selectedWorkOrder.priority) }}
              </Badge>
            </div>
            <div>
              <Label class="text-sm font-medium">计划开始</Label>
              <p class="text-sm text-gray-600">{{ formatDate(selectedWorkOrder.plannedStartDate) }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">计划完成</Label>
              <p class="text-sm text-gray-600">{{ formatDate(selectedWorkOrder.plannedEndDate) }}</p>
            </div>
          </div>

          <!-- 工单明细 -->
          <div>
            <h4 class="font-medium mb-3">工单明细</h4>
            <div class="space-y-3">
              <div
                v-for="item in selectedWorkOrder.items"
                :key="item.id"
                class="border rounded-lg p-3"
              >
                <div class="flex justify-between items-start mb-2">
                  <div>
                    <p class="font-medium">
                      {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                    </p>
                    <p class="text-sm text-gray-600">
                      {{ item.specifications.glassType }} {{ item.specifications.color }} × {{ item.quantity }}片
                    </p>
                  </div>
                  <div class="text-right">
                    <Badge variant="outline">{{ item.currentStatus }}</Badge>
                    <p class="text-xs text-gray-500 mt-1">{{ item.currentWorkstation }}</p>
                  </div>
                </div>
                
                <!-- 工艺流程 -->
                <div class="mt-3">
                  <p class="text-sm font-medium mb-2">工艺流程：</p>
                  <div class="flex items-center gap-2 text-xs">
                    <span
                      v-for="(step, index) in item.processFlow"
                      :key="index"
                      class="px-2 py-1 rounded"
                      :class="getStepStatusClass(step.status)"
                    >
                      {{ step.stepName }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 数据质量报告对话框 -->
    <Dialog v-model:open="showDataQualityReport">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>生产工单数据质量报告</DialogTitle>
        </DialogHeader>

        <div class="space-y-6">
          <!-- 数据完整性指标 -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent class="p-4 text-center">
                <div class="text-2xl font-bold text-green-600">100%</div>
                <div class="text-sm text-gray-600">外键引用完整率</div>
                <div class="text-xs text-gray-500 mt-1">所有工单都有有效的客户订单引用</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent class="p-4 text-center">
                <div class="text-2xl font-bold text-green-600">100%</div>
                <div class="text-sm text-gray-600">数据覆盖率</div>
                <div class="text-xs text-gray-500 mt-1">所有客户订单项都有对应工单</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent class="p-4 text-center">
                <div class="text-2xl font-bold text-green-600">100%</div>
                <div class="text-sm text-gray-600">数据一致性</div>
                <div class="text-xs text-gray-500 mt-1">规格、数量、价格完全一致</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent class="p-4 text-center">
                <div class="text-2xl font-bold text-green-600">A+</div>
                <div class="text-sm text-gray-600">质量等级</div>
                <div class="text-xs text-gray-500 mt-1">数据质量优秀</div>
              </CardContent>
            </Card>
          </div>

          <!-- 产品族分布 -->
          <Card>
            <CardHeader>
              <CardTitle>产品族工单分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-3">
                <div class="flex justify-between items-center p-3 bg-red-50 rounded">
                  <div>
                    <div class="font-medium">单片钢化玻璃产品族</div>
                    <div class="text-sm text-gray-600">PF-TEMPERED</div>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-red-600">11项</div>
                    <div class="text-sm text-gray-500">1,780片</div>
                  </div>
                </div>
                <div class="flex justify-between items-center p-3 bg-purple-50 rounded">
                  <div>
                    <div class="font-medium">装饰玻璃产品族</div>
                    <div class="text-sm text-gray-600">PF-DECORATIVE</div>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-purple-600">3项</div>
                    <div class="text-sm text-gray-500">305片</div>
                  </div>
                </div>
                <div class="flex justify-between items-center p-3 bg-blue-50 rounded">
                  <div>
                    <div class="font-medium">中空玻璃产品族</div>
                    <div class="text-sm text-gray-600">PF-IGU</div>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-blue-600">2项</div>
                    <div class="text-sm text-gray-500">350片</div>
                  </div>
                </div>
                <div class="flex justify-between items-center p-3 bg-green-50 rounded">
                  <div>
                    <div class="font-medium">夹胶玻璃产品族</div>
                    <div class="text-sm text-gray-600">PF-LAMINATED</div>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-green-600">2项</div>
                    <div class="text-sm text-gray-500">140片</div>
                  </div>
                </div>
                <div class="flex justify-between items-center p-3 bg-orange-50 rounded">
                  <div>
                    <div class="font-medium">家具玻璃产品族</div>
                    <div class="text-sm text-gray-600">PF-FURNITURE</div>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-orange-600">2项</div>
                    <div class="text-sm text-gray-500">130片</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 业务价值总结 -->
          <Card>
            <CardHeader>
              <CardTitle>业务价值实现</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-sm">支持完整的订单到生产的业务追溯</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-sm">实现基于产品族的智能工艺路线生成</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-sm">提供准确的生产计划和资源配置数据</span>
                  </div>
                </div>
                <div class="space-y-2">
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-sm">确保客户订单与生产执行的数据一致性</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-sm">为MES系统提供可靠的数据基础</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-sm">支持MTO模式的订单驱动生产</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 新建工单对话框 -->
    <ProductionOrderCreationDialog
      :open="showCreateDialog"
      @update:open="showCreateDialog = $event"
      @order-created="handleOrderCreated"
    />

    <!-- 生产发布工作台对话框 -->
    <WorkOrderDeliveryDialog
      :open="showProductionRelease"
      :work-order="selectedReleaseWorkOrder"
      @update:open="showProductionRelease = $event"
      @step-updated="handleReleaseStepUpdated"
      @work-order-released="handleWorkOrderReleased"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Plus,
  RefreshCw,
  AlertTriangle,
  FileText,
  Eye,
  Play,
  Settings,
  Link,
  Layers,
  BarChart3,
  ExternalLink,
  Workflow,
  Building2,
  Tag,
  Package,
  Clock,
  TrendingUp,
  Users,
  Zap,
  Shield,
} from 'lucide-vue-next'

import type { ProductionOrder } from '@/types/mes-validation'
import { mesService } from '@/services/mesService'
import ProductionOrderCreationDialog from '@/components/mes/ProductionOrderCreationDialog.vue'
import WorkOrderDeliveryDialog from '@/components/mes/WorkOrderDeliveryDialog.vue'

const router = useRouter()

// 响应式数据
const workOrders = ref<ProductionOrder[]>([])
const searchQuery = ref('')
const statusFilter = ref('all')
const priorityFilter = ref('all')
const productFamilyFilter = ref('all')
const orderTypeFilter = ref('all')
const showWorkOrderDetails = ref(false)
const showCreateDialog = ref(false)
const showDataQualityReport = ref(false)
const showProductionRelease = ref(false)
const selectedWorkOrder = ref<ProductionOrder | null>(null)
const selectedReleaseWorkOrder = ref<ProductionOrder | null>(null)
const expandedWorkOrders = ref<Set<string>>(new Set())

// 计算属性
const workOrderStats = computed(() => {
  const stats = {
    total: workOrders.value.length,
    totalItems: 0,
    linkedCustomerOrders: 0, // 关联的客户订单数量
    productFamilies: 0,
    pending: 0,
    released: 0,
    inProgress: 0,
    completed: 0,
    urgent: 0,
  }

  const productFamilySet = new Set()
  const customerOrderSet = new Set()

  workOrders.value.forEach(workOrder => {
    stats.totalItems += workOrder.items.length

    // 统计关联的客户订单
    if (workOrder.customerOrderId) {
      customerOrderSet.add(workOrder.customerOrderId)
    }

    // 统计产品族类型
    workOrder.items.forEach(item => {
      if ((item as any).productFamilyId) {
        productFamilySet.add((item as any).productFamilyId)
      }
    })

    switch (workOrder.status) {
      case 'pending':
        stats.pending++
        break
      case 'released':
        stats.released++
        break
      case 'in_progress':
        stats.inProgress++
        break
      case 'completed':
        stats.completed++
        break
    }

    if (workOrder.priority === 'urgent') {
      stats.urgent++
    }
  })

  stats.productFamilies = productFamilySet.size
  stats.linkedCustomerOrders = customerOrderSet.size

  return stats
})


const filteredWorkOrders = computed(() => {
  return workOrders.value.filter(workOrder => {
    // 搜索匹配 - 支持多客户订单搜索
    const matchesSearch = !searchQuery.value ||
      workOrder.workOrderNumber.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      getUniqueCustomerNames(workOrder).some(name => name.toLowerCase().includes(searchQuery.value.toLowerCase())) ||
      getUniqueOrderNumbers(workOrder).some(num => num.toLowerCase().includes(searchQuery.value.toLowerCase())) ||
      getCustomerOrderInfo(workOrder).some(info =>
        info.customerName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        info.orderNumber.toLowerCase().includes(searchQuery.value.toLowerCase())
      );

    const matchesStatus = statusFilter.value === 'all' || workOrder.status === statusFilter.value
    const matchesPriority = priorityFilter.value === 'all' || workOrder.priority === priorityFilter.value

    // 产品族筛选
    const matchesProductFamily = productFamilyFilter.value === 'all' ||
      workOrder.items.some(item => (item as any).productFamilyId === productFamilyFilter.value)

    // 订单类型筛选 - 支持多订单类型
    const matchesOrderType = orderTypeFilter.value === 'all' ||
      getOrderTypesFromWorkOrder(workOrder).includes(orderTypeFilter.value) ||
      getCustomerOrderInfo(workOrder).some(info => {
        // 根据客户名称推断订单类型
        let orderType = '其他'
        if (info.customerName.includes('华润')) orderType = '幕墙工程'
        else if (info.customerName.includes('万科')) orderType = '门窗工程'
        else if (info.customerName.includes('绿地')) orderType = '办公隔断'
        else if (info.customerName.includes('中海')) orderType = '中空玻璃'
        else if (info.customerName.includes('保利')) orderType = '夹胶玻璃'
        else if (info.customerName.includes('龙湖')) orderType = '装饰玻璃'
        else if (info.customerName.includes('融创')) orderType = '家具玻璃'
        else if (info.customerName.includes('碧桂园')) orderType = '阳光房'
        else if (info.customerName.includes('恒大')) orderType = '展示柜'
        else if (info.customerName.includes('金茂')) orderType = '特殊工艺'

        return orderType === orderTypeFilter.value
      })

    return matchesSearch && matchesStatus && matchesPriority && matchesProductFamily && matchesOrderType
  })
})

// --- 新增辅助函数 ---
const getUniqueCustomerNames = (workOrder: ProductionOrder): string[] => {
  if (!workOrder.items || workOrder.items.length === 0) {
    return [workOrder.customerName];
  }
  const customerNames = new Set(workOrder.items.map(item => (item as any).customerName || workOrder.customerName));
  return Array.from(customerNames);
}

// 获取订单类型（支持多对多关系）
const getOrderTypesFromWorkOrder = (workOrder: ProductionOrder): string => {
  const orderTypes = new Set<string>()

  // 从工单项中获取所有订单类型
  workOrder.items.forEach(item => {
    const customerName = (item as any).customerName || workOrder.customerName
    let orderType = '其他'

    if (customerName.includes('华润')) orderType = '幕墙工程'
    else if (customerName.includes('万科')) orderType = '门窗工程'
    else if (customerName.includes('绿地')) orderType = '办公隔断'
    else if (customerName.includes('中海')) orderType = '中空玻璃'
    else if (customerName.includes('保利')) orderType = '夹胶玻璃'
    else if (customerName.includes('龙湖')) orderType = '装饰玻璃'
    else if (customerName.includes('融创')) orderType = '家具玻璃'
    else if (customerName.includes('碧桂园')) orderType = '阳光房'
    else if (customerName.includes('恒大')) orderType = '展示柜'
    else if (customerName.includes('金茂')) orderType = '特殊工艺'

    orderTypes.add(orderType)
  })

  const types = Array.from(orderTypes)
  return types.length === 1 ? types[0] : `${types.length}种类型`
}

// 获取客户订单信息（支持多对多关系）
const getCustomerOrderInfo = (workOrder: ProductionOrder) => {
  const orderMap = new Map()

  workOrder.items.forEach(item => {
    const customerOrderId = (item as any).customerOrderItemId?.split('-')[0] + '-' + (item as any).customerOrderItemId?.split('-')[1] || workOrder.customerOrderId
    const customerName = (item as any).customerName || workOrder.customerName
    const orderNumber = (item as any).customerOrderNumber || workOrder.customerOrderNumber

    if (!orderMap.has(customerOrderId)) {
      orderMap.set(customerOrderId, {
        orderId: customerOrderId,
        customerName: customerName,
        orderNumber: orderNumber,
        itemCount: 0
      })
    }

    orderMap.get(customerOrderId).itemCount++
  })

  return Array.from(orderMap.values())
}



// 获取产品族信息
const getProductFamilyInfo = (workOrder: ProductionOrder) => {
  const familyMap = new Map()

  workOrder.items.forEach(item => {
    const familyId = (item as any).productFamilyId
    const familyName = (item as any).productFamilyName

    if (familyId) {
      if (!familyMap.has(familyId)) {
        familyMap.set(familyId, {
          id: familyId,
          name: familyName || familyId,
          count: 0,
          quantity: 0
        })
      }

      const info = familyMap.get(familyId)
      info.count++
      info.quantity += item.quantity || 0
    }
  })

  return Array.from(familyMap.values())
}

// 获取总数量
const getTotalQuantity = (workOrder: ProductionOrder): number => {
  return workOrder.items.reduce((total, item) => total + (item.quantity || 0), 0)
}

// 获取产品族变体样式
const getProductFamilyVariant = (familyId: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
  const variantMap: Record<string, 'default' | 'destructive' | 'outline' | 'secondary'> = {
    'PF-TEMPERED': 'destructive',
    'PF-IGU': 'default',
    'PF-LAMINATED': 'secondary',
    'PF-DECORATIVE': 'outline',
    'PF-FURNITURE': 'secondary'
  }
  return variantMap[familyId] || 'outline'
}



// 工单卡片样式
const getWorkOrderCardClass = (workOrder: ProductionOrder): string => {
  const baseClass = 'transition-all duration-200'
  if (workOrder.priority === 'urgent') return `${baseClass} border-red-200 bg-red-50/30`
  if (isDelayRisk(workOrder)) return `${baseClass} border-orange-200 bg-orange-50/30`
  return baseClass
}

// 工单行样式
const getWorkOrderRowClass = (workOrder: ProductionOrder): string => {
  if (workOrder.priority === 'urgent') return 'bg-red-50/50'
  if (isDelayRisk(workOrder)) return 'bg-orange-50/50'
  return ''
}



// 状态点样式
const getStatusDotClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': 'bg-gray-400',
    'released': 'bg-blue-500',
    'in_progress': 'bg-orange-500',
    'completed': 'bg-green-500',
    'cancelled': 'bg-red-500'
  }
  return statusMap[status] || 'bg-gray-400'
}

// 进度条样式
const getProgressBarClass = (workOrder: ProductionOrder): string => {
  const progress = getWorkOrderProgress(workOrder)
  if (progress >= 80) return 'bg-green-500'
  if (progress >= 50) return 'bg-blue-500'
  if (progress >= 20) return 'bg-orange-500'
  return 'bg-gray-400'
}

// 进度颜色
const getProgressColor = (workOrder: ProductionOrder): string => {
  const progress = getWorkOrderProgress(workOrder)
  if (progress >= 80) return '#10b981'
  if (progress >= 50) return '#3b82f6'
  if (progress >= 20) return '#f59e0b'
  return '#6b7280'
}

// 工单进度计算
const getWorkOrderProgress = (workOrder: ProductionOrder): number => {
  if (!workOrder.items || workOrder.items.length === 0) return 0

  const statusWeights: Record<string, number> = {
    '待排版': 0,
    '排版中': 20,
    '生产中': 60,
    '质检中': 80,
    '已完成': 100
  }

  const totalWeight = workOrder.items.reduce((sum, item) => {
    const status = (item as any).currentStatus || '待排版'
    return sum + (statusWeights[status] || 0)
  }, 0)

  return Math.round(totalWeight / workOrder.items.length)
}

// 延期风险判断
const isDelayRisk = (workOrder: ProductionOrder): boolean => {
  const plannedEnd = new Date(workOrder.plannedEndDate)
  const now = new Date()
  const daysUntilDeadline = Math.ceil((plannedEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  const progress = getWorkOrderProgress(workOrder)

  // 如果剩余时间少于3天且进度低于70%，认为有延期风险
  return daysUntilDeadline <= 3 && progress < 70
}

// 产品族数量统计
const getProductFamilyCount = (workOrder: ProductionOrder): number => {
  const familySet = new Set()
  workOrder.items.forEach(item => {
    const familyId = (item as any).productFamilyId
    if (familyId) familySet.add(familyId)
  })
  return familySet.size
}

// 产品族背景样式
const getProductFamilyBgClass = (familyId: string): string => {
  const bgMap: Record<string, string> = {
    'PF-TEMPERED': 'bg-red-100 text-red-800',
    'PF-IGU': 'bg-blue-100 text-blue-800',
    'PF-LAMINATED': 'bg-green-100 text-green-800',
    'PF-DECORATIVE': 'bg-purple-100 text-purple-800',
    'PF-FURNITURE': 'bg-orange-100 text-orange-800'
  }
  return bgMap[familyId] || 'bg-gray-100 text-gray-800'
}

// 产品族点样式
const getProductFamilyDotClass = (familyId: string): string => {
  const dotMap: Record<string, string> = {
    'PF-TEMPERED': 'bg-red-500',
    'PF-IGU': 'bg-blue-500',
    'PF-LAMINATED': 'bg-green-500',
    'PF-DECORATIVE': 'bg-purple-500',
    'PF-FURNITURE': 'bg-orange-500'
  }
  return dotMap[familyId] || 'bg-gray-500'
}

// 工序状态变体
const getProcessStatusVariant = (status: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
  const variantMap: Record<string, 'default' | 'destructive' | 'outline' | 'secondary'> = {
    '待排版': 'secondary',
    '排版中': 'default',
    '生产中': 'default',
    '质检中': 'outline',
    '已完成': 'secondary'
  }
  return variantMap[status] || 'secondary'
}

// 业务指标计算函数（为不同角色提供关键信息）

// 预计工时（生产经理关注）
const getEstimatedHours = (workOrder: ProductionOrder): number => {
  const baseHours = workOrder.items.length * 2 // 每个工单项基础2小时
  const complexityMultiplier = getProductFamilyCount(workOrder) * 0.5 // 产品族复杂度
  return Math.round(baseHours + complexityMultiplier)
}

// 所需设备（车间主管关注）
const getRequiredEquipment = (workOrder: ProductionOrder): string => {
  const equipmentSet = new Set<string>()
  workOrder.items.forEach(item => {
    const familyId = (item as any).productFamilyId
    switch (familyId) {
      case 'PF-TEMPERED':
        equipmentSet.add('钢化炉')
        break
      case 'PF-IGU':
        equipmentSet.add('合片机')
        equipmentSet.add('充气设备')
        break
      case 'PF-LAMINATED':
        equipmentSet.add('夹胶设备')
        equipmentSet.add('高压釜')
        break
      default:
        equipmentSet.add('通用设备')
    }
  })
  return `${equipmentSet.size}台`
}

// 质检节点（质量管理员关注）
const getQualityCheckpoints = (workOrder: ProductionOrder): string => {
  const checkpoints = new Set<string>()
  workOrder.items.forEach(item => {
    const familyId = (item as any).productFamilyId
    switch (familyId) {
      case 'PF-TEMPERED':
        checkpoints.add('钢化质检')
        break
      case 'PF-IGU':
        checkpoints.add('密封性检测')
        break
      case 'PF-LAMINATED':
        checkpoints.add('夹胶强度检测')
        break
      default:
        checkpoints.add('常规质检')
    }
  })
  return `${checkpoints.size}个`
}

// 所需人员（计划调度员关注）
const getRequiredWorkers = (workOrder: ProductionOrder): number => {
  const baseWorkers = Math.ceil(workOrder.items.length / 5) // 每5个工单项需要1个工人
  const familyComplexity = getProductFamilyCount(workOrder) // 产品族复杂度需要额外人员
  return Math.max(1, baseWorkers + familyComplexity)
}

// 产能利用率（生产经理关注）
const getCapacityUtilization = (workOrder: ProductionOrder): number => {
  const totalQuantity = getTotalQuantity(workOrder)
  const estimatedHours = getEstimatedHours(workOrder)
  // 假设标准产能是每小时50片
  const standardCapacity = estimatedHours * 50
  return Math.min(100, Math.round((totalQuantity / standardCapacity) * 100))
}

// 质量等级（质量管理员关注）
const getQualityLevel = (workOrder: ProductionOrder): string => {
  const hasHighEndFamily = workOrder.items.some(item => {
    const familyId = (item as any).productFamilyId
    return ['PF-LAMINATED', 'PF-DECORATIVE'].includes(familyId)
  })

  if (hasHighEndFamily) return '高标准'

  const hasStandardFamily = workOrder.items.some(item => {
    const familyId = (item as any).productFamilyId
    return ['PF-TEMPERED', 'PF-IGU'].includes(familyId)
  })

  return hasStandardFamily ? '标准' : '基础'
}

const getUniqueOrderNumbers = (workOrder: ProductionOrder): string[] => {
  if (!workOrder.items || workOrder.items.length === 0) {
    return [workOrder.customerOrderNumber];
  }
  const orderNumbers = new Set(workOrder.items.map(item => (item as any).customerOrderNumber || workOrder.customerOrderNumber));
  return Array.from(orderNumbers);
};


// --- 结束新增 ---

// 方法
const loadWorkOrders = async () => {
  try {
    workOrders.value = await mesService.getProductionOrders()
  } catch (error) {
    console.error('加载生产工单失败:', error)
  }
}

const handleOrderCreated = (orderIds: string[]) => {
  console.log('工单创建成功:', orderIds)
  // 刷新工单列表
  loadWorkOrders()
  
  // 显示成功提示
  if (orderIds.length === 1) {
    alert(`工单创建成功！工单号: ${orderIds[0]}`)
  } else {
    alert(`批量工单创建成功！共创建 ${orderIds.length} 个工单`)
  }
}

const refreshWorkOrders = () => {
  loadWorkOrders()
}

const toggleWorkOrderExpansion = (workOrderId: string) => {
  if (expandedWorkOrders.value.has(workOrderId)) {
    expandedWorkOrders.value.delete(workOrderId)
  } else {
    expandedWorkOrders.value.add(workOrderId)
  }
}

const viewWorkOrderDetails = (workOrder: ProductionOrder) => {
  selectedWorkOrder.value = workOrder
  showWorkOrderDetails.value = true
}

const releaseWorkOrder = async (workOrder: ProductionOrder) => {
  await mesService.updateProductionOrderStatus(workOrder.id, 'released')
  workOrder.status = 'released'
}

const openProductionRelease = (workOrder: ProductionOrder) => {
  selectedReleaseWorkOrder.value = workOrder
  showProductionRelease.value = true
}

const handleReleaseStepUpdated = (workOrderId: string, step: string) => {
  // 根据发布步骤更新工单状态
  const workOrder = workOrders.value.find(wo => wo.id === workOrderId)
  if (workOrder) {
    // 这里可以根据步骤更新工单状态
    console.log(`工单 ${workOrderId} 发布步骤更新为: ${step}`)
  }
}

const handleWorkOrderReleased = (workOrderId: string) => {
  // 工单发布完成，更新状态为已发布
  const workOrder = workOrders.value.find(wo => wo.id === workOrderId)
  if (workOrder) {
    workOrder.status = 'released'
    showProductionRelease.value = false
    console.log(`工单 ${workOrderId} 已发布到车间`)
  }
}



const jumpToCustomerOrder = (customerOrderId: string) => {
  // 这里可以跳转到客户订单详情或CRM系统
  console.log('跳转到客户订单:', customerOrderId)
  // 实际实现中可以使用路由跳转
  // router.push(`/crm/customer-orders/${customerOrderId}`)
}

// 查看工艺流程
const viewProcessFlow = (workOrder: ProductionOrder) => {
  console.log('查看工艺流程:', workOrder.id)
  // 这里可以打开工艺流程详情对话框
}

// 显示数据质量报告
const showDataQualityReportDialog = () => {
  showDataQualityReport.value = true
}

// 状态相关方法
const getStatusVariant = (status: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
  switch (status) {
    case 'pending': return 'secondary'
    case 'released': return 'default'
    case 'in_progress': return 'default'
    case 'completed': return 'default'
    case 'cancelled': return 'destructive'
    default: return 'secondary'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待发布'
    case 'released': return '已发布'
    case 'in_progress': return '执行中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return status
  }
}

const getPriorityVariant = (priority: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
  switch (priority) {
    case 'urgent': return 'destructive'
    case 'high': return 'default'
    case 'normal': return 'secondary'
    case 'low': return 'outline'
    default: return 'secondary'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'urgent': return '紧急'
    case 'high': return '高'
    case 'normal': return '普通'
    case 'low': return '低'
    default: return priority
  }
}

const getWorkstationStatusClass = (status: string) => {
  switch (status) {
    case '待排版': return 'bg-blue-100 text-blue-700'
    case '排版中': return 'bg-orange-100 text-orange-700'
    case '生产中': return 'bg-purple-100 text-purple-700'
    case '质检中': return 'bg-yellow-100 text-yellow-700'
    case '已完成': return 'bg-green-100 text-green-700'
    default: return 'bg-gray-100 text-gray-700'
  }
}

const getStepStatusClass = (status: string) => {
  switch (status) {
    case 'pending': return 'bg-gray-100 text-gray-700'
    case 'in_progress': return 'bg-blue-100 text-blue-700'
    case 'completed': return 'bg-green-100 text-green-700'
    case 'skipped': return 'bg-yellow-100 text-yellow-700'
    default: return 'bg-gray-100 text-gray-700'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 工单项相关的辅助函数

// 获取工单项卡片样式
const getWorkOrderItemCardClass = (item: any): string => {
  const status = item.currentStatus || '待排版'
  if (status.includes('质检') || status.includes('异常')) return 'border-amber-200 bg-amber-50/30'
  if (status.includes('进行中') || status.includes('生产中')) return 'border-green-200 bg-green-50/30'
  if (status.includes('完成')) return 'border-blue-200 bg-blue-50/30'
  return 'border-gray-200'
}

// 获取产品族简称
const getProductFamilyShortName = (familyId: string): string => {
  const shortNames = {
    'PF-TEMPERED': '钢化',
    'PF-IGU': '中空',
    'PF-LAMINATED': '夹胶',
    'PF-DECORATIVE': '装饰',
    'PF-FURNITURE': '家具'
  }
  return shortNames[familyId] || '其他'
}



// 获取玻璃类型显示名称
const getGlassTypeDisplay = (glassType: string): string => {
  const displayNames = {
    'clear': '白玻',
    'low_e': 'Low-E',
    'tinted': '茶玻',
    'reflective': '镀膜',
    'laminated': '夹胶',
    'tempered': '钢化'
  }
  return displayNames[glassType] || glassType
}

// 获取工序进度百分比
const getProcessProgress = (item: any): number => {
  if (!item.processFlow || item.processFlow.length === 0) return 0

  const currentStep = getCurrentProcessStep(item)
  const totalSteps = getTotalProcessSteps(item)

  return Math.round((currentStep / totalSteps) * 100)
}

// 获取当前工序步骤数
const getCurrentProcessStep = (item: any): number => {
  if (!item.processFlow || item.processFlow.length === 0) return 0

  const status = item.currentStatus || '待排版'
  if (status === '待排版') return 0
  if (status === '已完成') return item.processFlow.length

  // 根据状态推断当前步骤
  const statusStepMap = {
    '切割中': 1,
    '磨边中': 2,
    '镀膜中': 3,
    '钢化中': 4,
    '合片中': 5,
    '充气中': 6,
    '夹胶中': 3,
    '高压釜中': 4,
    '表面处理中': 4,
    '抛光中': 3,
    '质检中': item.processFlow.length - 1,
    '包装中': item.processFlow.length
  }

  return statusStepMap[status] || Math.floor(item.processFlow.length / 2)
}

// 获取总工序步骤数
const getTotalProcessSteps = (item: any): number => {
  return item.processFlow ? item.processFlow.length : 6
}

// 获取工序进度条样式
const getProcessProgressBarClass = (item: any): string => {
  const progress = getProcessProgress(item)
  if (progress >= 100) return 'bg-green-500'
  if (progress >= 75) return 'bg-blue-500'
  if (progress >= 50) return 'bg-yellow-500'
  if (progress >= 25) return 'bg-orange-500'
  return 'bg-gray-400'
}

// 获取当前工序名称
const getCurrentProcessName = (item: any): string => {
  const status = item.currentStatus || '待排版'
  if (status === '待排版') return '待排版'
  if (status === '已完成') return '已完成'

  // 从状态中提取工序名称
  const processName = status.replace(/中$/, '').replace(/ing$/, '')
  return processName || '未知工序'
}

// 获取当前工位
const getCurrentWorkstation = (item: any): string => {
  if (!item.processFlow || item.processFlow.length === 0) return '未分配'

  const currentStep = getCurrentProcessStep(item)
  if (currentStep === 0) return '待分配'
  if (currentStep > item.processFlow.length) return '已完成'

  const workstation = item.processFlow[currentStep - 1]?.workstation || 'unknown'
  const workstationNames = {
    'cold_processing': '冷加工区',
    'tempering': '钢化炉',
    'coating': '镀膜线',
    'insulating': '合片线',
    'laminating': '夹胶线',
    'autoclave': '高压釜',
    'surface_treatment': '表面处理',
    'polishing': '抛光区',
    'quality_control': '质检区',
    'packaging': '包装区',
    'cleaning': '清洗区'
  }

  return workstationNames[workstation] || '未知工位'
}

// 检查是否有质量检验节点
const hasQualityCheckpoint = (item: any): boolean => {
  const status = item.currentStatus || '待排版'
  return status.includes('质检') || getCurrentProcessStep(item) === getTotalProcessSteps(item) - 1
}

// 获取质量检验节点名称
const getQualityCheckpointName = (item: any): string => {
  const familyId = item.productFamilyId
  const checkpoints = {
    'PF-TEMPERED': '钢化质检',
    'PF-IGU': '密封性检测',
    'PF-LAMINATED': '夹胶强度检测',
    'PF-DECORATIVE': '外观质检',
    'PF-FURNITURE': '尺寸精度检测'
  }
  return checkpoints[familyId] || '常规质检'
}

// 获取预计完成时间
const getEstimatedCompletionTime = (item: any): string => {
  // 基于当前进度和剩余工序估算完成时间
  const progress = getProcessProgress(item)
  const remainingHours = Math.ceil((100 - progress) / 100 * 24) // 假设总共24小时

  if (remainingHours <= 0) return '已完成'
  if (remainingHours <= 2) return '2小时内'
  if (remainingHours <= 8) return '今日内'
  if (remainingHours <= 24) return '明日内'

  const days = Math.ceil(remainingHours / 24)
  return `${days}天内`
}

// 检查工单项是否延期
const isDelayedItem = (item: any): boolean => {
  // 简单的延期判断逻辑
  const deliveryDate = new Date(item.deliveryDate)
  const now = new Date()
  const progress = getProcessProgress(item)

  // 如果交付日期临近但进度不足，则认为有延期风险
  const daysUntilDelivery = Math.ceil((deliveryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return daysUntilDelivery <= 2 && progress < 80
}

// 获取加工温度
const getProcessTemperature = (item: any): string => {
  const familyId = item.productFamilyId
  const temperatures = {
    'PF-TEMPERED': '680',
    'PF-IGU': '常温',
    'PF-LAMINATED': '140',
    'PF-DECORATIVE': '常温',
    'PF-FURNITURE': '680'
  }
  return temperatures[familyId] || '常温'
}

// 获取加工时长
const getProcessDuration = (item: any): string => {
  const familyId = item.productFamilyId
  const durations = {
    'PF-TEMPERED': '45',
    'PF-IGU': '30',
    'PF-LAMINATED': '120',
    'PF-DECORATIVE': '60',
    'PF-FURNITURE': '40'
  }
  return durations[familyId] || '60'
}

// 获取工单项质量等级
const getItemQualityLevel = (item: any): string => {
  const familyId = item.productFamilyId
  const levels = {
    'PF-TEMPERED': '标准',
    'PF-IGU': '高标准',
    'PF-LAMINATED': '高标准',
    'PF-DECORATIVE': '精品',
    'PF-FURNITURE': '标准'
  }
  return levels[familyId] || '标准'
}

// 获取操作员姓名
const getOperatorName = (item: any): string => {
  const operators = ['张师傅', '李师傅', '王师傅', '刘师傅', '陈师傅', '赵师傅']
  const index = Math.abs(item.id.charCodeAt(item.id.length - 1)) % operators.length
  return operators[index]
}

// 获取下一工序名称
const getNextProcessName = (item: any): string => {
  if (!item.processFlow || item.processFlow.length === 0) return '无'

  const currentStep = getCurrentProcessStep(item)
  if (currentStep >= item.processFlow.length) return '已完成'

  const nextProcess = item.processFlow[currentStep]
  return nextProcess ? nextProcess.stepName : '包装'
}

// 获取下一工序预计时间
const getNextProcessETA = (item: any): string => {
  const progress = getProcessProgress(item)
  if (progress >= 100) return '已完成'

  const remainingSteps = getTotalProcessSteps(item) - getCurrentProcessStep(item)
  const hoursPerStep = 4 // 假设每个工序4小时
  const totalHours = remainingSteps * hoursPerStep

  if (totalHours <= 4) return '4小时后'
  if (totalHours <= 8) return '今日内'
  if (totalHours <= 24) return '明日'

  const days = Math.ceil(totalHours / 24)
  return `${days}天后`
}

// 显示所有工单项
const showAllItems = (workOrder: ProductionOrder) => {
  // 展开该工单以显示所有项目
  expandedWorkOrders.value.add(workOrder.id)
}

// 生命周期
onMounted(async () => {
  await loadWorkOrders()
})
</script>