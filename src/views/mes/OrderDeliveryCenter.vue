<template>
  <div class="h-full flex flex-col bg-gray-50">
    <!-- 页面标题和统计概览 -->
    <div class="bg-white border-b px-6 py-4">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">订单交付管理中心</h1>
          <p class="text-gray-600 mt-1">生产工单的端到端交付管理，从交付计划制定到客户确认收货</p>
        </div>
        
        <!-- 快捷操作 -->
        <div class="flex items-center space-x-3">
          <Button @click="refreshData" variant="outline" size="sm">
            <RefreshCw class="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button @click="showCreateOrderDialog = true" size="sm">
            <Plus class="h-4 w-4 mr-2" />
            导入工单
          </Button>
        </div>
      </div>
      
      <!-- 统计卡片 -->
      <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">总工单</p>
              <p class="text-2xl font-bold text-blue-600">{{ orderDeliveryStore.orderStats.total }}</p>
            </div>
            <Package class="w-8 h-8 text-blue-500" />
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">计划中</p>
              <p class="text-2xl font-bold text-orange-600">{{ orderDeliveryStore.orderStats.planning }}</p>
            </div>
            <Calendar class="w-8 h-8 text-orange-500" />
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">排程中</p>
              <p class="text-2xl font-bold text-purple-600">{{ orderDeliveryStore.orderStats.scheduling }}</p>
            </div>
            <Settings class="w-8 h-8 text-purple-500" />
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">执行中</p>
              <p class="text-2xl font-bold text-green-600">{{ orderDeliveryStore.orderStats.executing }}</p>
            </div>
            <Play class="w-8 h-8 text-green-500" />
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">已交付</p>
              <p class="text-2xl font-bold text-gray-600">{{ orderDeliveryStore.orderStats.delivered }}</p>
            </div>
            <CheckCircle class="w-8 h-8 text-gray-500" />
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">紧急工单</p>
              <p class="text-2xl font-bold text-red-600">{{ orderDeliveryStore.orderStats.urgent }}</p>
            </div>
            <AlertTriangle class="w-8 h-8 text-red-500" />
          </div>
        </Card>
      </div>
    </div>

    <!-- 主工作区域 - 三栏布局 -->
    <div class="flex-1 flex gap-6 p-6 min-h-0">
      <!-- 左侧：订单列表面板 -->
      <div class="w-80 bg-white rounded-lg border overflow-hidden flex flex-col">
        <OrderListPanel
          :orders="orderDeliveryStore.filteredOrders"
          :current-order="orderDeliveryStore.currentOrder"
          :loading="orderDeliveryStore.loading"
          @order-selected="handleOrderSelected"
          @search-changed="handleSearchChanged"
          @filters-changed="handleFiltersChanged"
        />
      </div>

      <!-- 中间：交付工作区 -->
      <div class="flex-1 bg-white rounded-lg border overflow-hidden flex flex-col">
        <DeliveryWorkspace
          :current-order="orderDeliveryStore.currentOrder"
          :action-suggestions="orderDeliveryStore.actionSuggestions"
          :delivery-anomalies="orderDeliveryStore.deliveryAnomalies"
          @phase-transition="handlePhaseTransition"
          @action-executed="handleActionExecuted"
        />
      </div>

      <!-- 右侧：详情面板 -->
      <div class="w-96 bg-white rounded-lg border overflow-hidden flex flex-col">
        <DeliveryDetailsPanel
          :current-order="orderDeliveryStore.currentOrder"
          @timeline-event-clicked="handleTimelineEventClicked"
          @resource-details="handleResourceDetails"
        />
      </div>
    </div>

    <!-- 新建订单对话框 -->
    <Dialog v-model:open="showCreateOrderDialog">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>新建订单</DialogTitle>
          <DialogDescription>
            创建新的客户订单并自动生成交付计划
          </DialogDescription>
        </DialogHeader>
        
        <div class="py-4">
          <p class="text-gray-600">新建订单功能开发中...</p>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useOrderDeliveryStore } from '@/stores/orderDeliveryStore';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  Package,
  Calendar,
  Settings,
  Play,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Plus
} from 'lucide-vue-next';

// 导入子组件
import OrderListPanel from '@/components/delivery/OrderListPanel.vue';
import DeliveryWorkspace from '@/components/delivery/DeliveryWorkspace.vue';
import DeliveryDetailsPanel from '@/components/delivery/DeliveryDetailsPanel.vue';

// Store
const orderDeliveryStore = useOrderDeliveryStore();

// 响应式数据
const showCreateOrderDialog = ref(false);

// 生命周期
onMounted(async () => {
  console.log('订单交付管理中心已加载');
  await orderDeliveryStore.initializeStore();
});

// 事件处理
const handleOrderSelected = (orderId: string) => {
  orderDeliveryStore.selectOrder(orderId);
};

const handleSearchChanged = (query: string) => {
  orderDeliveryStore.updateSearchQuery(query);
};

const handleFiltersChanged = (filters: any) => {
  orderDeliveryStore.updateFilters(filters);
};

const handlePhaseTransition = async (orderId: string, targetPhase: string) => {
  await orderDeliveryStore.transitionOrderPhase(orderId, targetPhase as any);
};

const handleActionExecuted = async (actionId: string) => {
  const result = await orderDeliveryStore.executeAutomatedAction(actionId);
  console.log('操作执行结果:', result);
};

const handleTimelineEventClicked = (eventId: string) => {
  console.log('时间线事件点击:', eventId);
};

const handleResourceDetails = (resourceId: string) => {
  console.log('资源详情:', resourceId);
};

const refreshData = async () => {
  await orderDeliveryStore.loadOrders();
  await orderDeliveryStore.generateActionSuggestions();
  await orderDeliveryStore.detectAnomalies();
};
</script>
