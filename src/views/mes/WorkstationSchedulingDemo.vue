<script setup lang="ts">
import { onMounted, ref } from 'vue'
import ValueMetricsCard from '@/components/mes/ValueMetricsCard.vue'
import TelemetryPanel from '@/components/mes/TelemetryPanel.vue'
import { aggregateMetrics } from '@/utils/ValueMetricsAggregator'
import { mesApi } from '@/utils/mesApi'
import { recordEvent } from '@/utils/Telemetry'
import { simulateUpdate } from '@/utils/MockConsistency'

const efficiency = ref<any>(null)
const exceptions = ref<any[]>([])
const metrics = ref<any[]>([])

async function loadData() {
  const t0 = performance.now()
  const [eff, ex] = await Promise.all([
    mesApi.getEfficiencyMetrics(),
    mesApi.getExceptionSamples()
  ])
  efficiency.value = eff.ok ? eff.data : null
  exceptions.value = ex.ok && Array.isArray(ex.data) ? ex.data : []
  metrics.value = await aggregateMetrics()
  recordEvent('page_load_scheduling', {}, performance.now() - t0)
}

async function simulateException(type: 'breakage'|'shortage'|'cancellation'|'equipment') {
  // 原型占位：前端生成异常并追加
  const id = 'ex_' + String(Date.now())
  exceptions.value.unshift({ id, type, severity: 'medium', status: 'detected', affectedOrders: [], affectedBatches: [], detectedAt: new Date().toISOString() })
}

async function generateReplan(exId: string) {
  const result = await simulateUpdate({ entityId: exId, expectedVersion: 1, payload: {} })
  if (!result.ok && result.conflict) {
    alert('并发冲突：请刷新或重试')
    recordEvent('replan_conflict')
    return
  }
  recordEvent('replan_generated')
}

onMounted(loadData)
</script>

<template>
  <div class="p-4 space-y-4">
    <h2 class="text-xl font-semibold">工段调度效率验证</h2>

    <div class="border rounded p-3">
      <h3 class="font-medium mb-2">效率提升对比</h3>
      <div v-if="efficiency">
        <div>传统平均设备利用率：{{ efficiency.traditionalFlow.averageUtilization }}%</div>
        <div>智能平均设备利用率：{{ efficiency.optimizedFlow.averageUtilization }}%</div>
        <div class="font-bold">提升：{{ (efficiency.optimizedFlow.averageUtilization - efficiency.traditionalFlow.averageUtilization).toFixed(2) }}%</div>
      </div>
      <div class="mt-3">
        <ValueMetricsCard :metrics="metrics" />
      </div>
    </div>

    <div class="border rounded p-3">
      <h3 class="font-medium mb-2">模拟异常</h3>
      <div class="space-x-2">
        <button class="px-2 py-1 bg-rose-600 text-white rounded" @click="() => { simulateException('breakage'); recordEvent('ex_breakage') }">玻璃破损</button>
        <button class="px-2 py-1 bg-amber-600 text-white rounded" @click="() => { simulateException('shortage'); recordEvent('ex_shortage') }">原片缺货</button>
        <button class="px-2 py-1 bg-blue-600 text-white rounded" @click="() => { simulateException('cancellation'); recordEvent('ex_cancellation') }">订单取消</button>
        <button class="px-2 py-1 bg-gray-700 text-white rounded" @click="() => { simulateException('equipment'); recordEvent('ex_equipment') }">设备故障</button>
      </div>
      <ul class="mt-3 list-disc pl-6 text-sm">
        <li v-for="ex in exceptions" :key="ex.id">
          [{{ ex.type }}] 状态：{{ ex.status }} ｜ 检出时间：{{ ex.detectedAt }}
          <button class="ml-2 text-blue-600 underline" @click="() => generateReplan(ex.id)">生成重排任务</button>
        </li>
      </ul>
    </div>
    <TelemetryPanel />
  </div>
</template>

<style scoped>
</style>

