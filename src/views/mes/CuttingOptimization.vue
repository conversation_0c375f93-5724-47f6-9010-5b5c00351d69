i<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- 页面标题和操作栏 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">排版优化管理</h1>
        <p class="text-gray-600 mt-1">管理排版任务，展示优化方案，体现订单到生产批次的数据流转</p>
      </div>
      <div class="flex gap-3">
        <Button @click="refreshTasks" variant="outline">
          <RefreshCw class="w-4 h-4 mr-2" />
          刷新
        </Button>
        <Button @click="showCreateTaskDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          新建排版任务
        </Button>
      </div>
    </div>

    <!-- 数据流转概览 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">待优化任务</p>
              <p class="text-2xl font-bold text-blue-600">{{ taskStats.pending }}</p>
            </div>
            <Clock class="w-8 h-8 text-blue-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">← 订单明细汇总</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">优化中任务</p>
              <p class="text-2xl font-bold text-orange-600">{{ taskStats.optimizing }}</p>
            </div>
            <Zap class="w-8 h-8 text-orange-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">算法计算中</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">已完成任务</p>
              <p class="text-2xl font-bold text-green-600">{{ taskStats.completed }}</p>
            </div>
            <CheckCircle class="w-8 h-8 text-green-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">→ 生成生产批次</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">平均利用率</p>
              <p class="text-2xl font-bold text-purple-600">{{ averageUtilization }}%</p>
            </div>
            <TrendingUp class="w-8 h-8 text-purple-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">智能优化提升</p>
        </CardContent>
      </Card>
    </div>

    <!-- 筛选和搜索 -->
    <Card>
      <CardContent class="p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <div class="flex-1 min-w-64">
            <Input
              v-model="searchQuery"
              placeholder="搜索任务名称、玻璃类型..."
              class="w-full"
            />
          </div>
          <Select v-model="statusFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="任务状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="pending">待优化</SelectItem>
              <SelectItem value="optimizing">优化中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
              <SelectItem value="executed">已执行</SelectItem>
            </SelectContent>
          </Select>
          <Select v-model="algorithmFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="算法类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部算法</SelectItem>
              <SelectItem value="internal">内置算法</SelectItem>
              <SelectItem value="third_party">第三方算法</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>

    <!-- 排版任务列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Scissors class="w-5 h-5" />
          排版任务列表
          <Badge variant="secondary">{{ filteredTasks.length }} 个任务</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="task in filteredTasks"
            :key="task.id"
            class="border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
        ick="selectTask(task)"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <h3 class="font-semibold text-lg">{{ task.taskName }}</h3>
                  <Badge :variant="getTaskStatusVariant(task.status)">
                    {{ getTaskStatusText(task.status) }}
                  </Badge>
                  <Badge variant="outline">
                    {{ task.glassType.thickness }}mm {{ task.glassType.color }}
                  </Badge>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                  <div>
                    <span class="font-medium">订单明细：</span>
                    {{ task.orderItems.length }}个
                  </div>
                  <div>
                    <span class="font-medium">总片数：</span>
                    {{ getTotalPieces(task) }}片
                  </div>
                  <div>
                    <span class="font-medium">创建时间：</span>
                    {{ formatDate(task.createdAt) }}
                  </div>
                </div>

                <!-- 优化结果展示 -->
                <div v-if="task.optimizationResults && task.optimizationResults.length > 0" class="mt-3">
                  <div class="bg-green-50 p-3 rounded-lg">
                    <p class="text-sm font-medium text-green-800 mb-2">优化结果：</p>
                    <div class="grid grid-cols-3 gap-4 text-xs">
                      <div>
                        <span class="text-green-600">利用率：</span>
                        <span class="font-bold">{{ getBestUtilization(task) }}%</span>
                      </div>
                      <div>
                        <span class="text-green-600">原片数：</span>
                        <span class="font-bold">{{ getBestSheetCount(task) }}片</span>
                      </div>
                      <div>
                        <span class="text-green-600">节省成本：</span>
                        <span class="font-bold">¥{{ getBestSavings(task) }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 数据流转链路 -->
                <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                  <p class="text-sm font-medium text-blue-800 mb-2">数据流转链路：</p>
                  <div class="flex items-center gap-2 text-xs">
                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded">订单明细汇总</span>
                    <ArrowRight class="w-3 h-3 text-blue-500" />
                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded">排版任务</span>
                    <ArrowRight class="w-3 h-3 text-orange-500" />
                    <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded">排版方案</span>
                    <ArrowRight class="w-3 h-3 text-purple-500" />
                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded">生产批次</span>
                  </div>
                </div>
              </div>
              
              <div class="flex flex-col items-end gap-2">
                <Button size="sm" variant="outline" @click.stop="viewTaskDetails(task)">
                  <Eye class="w-4 h-4 mr-1" />
                  查看详情
                </Button>
                <Button 
                  v-if="task.status === 'pending'"
                  size="sm" 
                  @click.stop="runOptimization(task)"
                  :disabled="optimizingTasks.has(task.id)"
                >
                  <Zap class="w-4 h-4 mr-1" />
                  {{ optimizingTasks.has(task.id) ? '优化中...' : '开始优化' }}
                </Button>
                <Button 
                  v-if="task.status === 'completed'"
                  size="sm" 
                  variant="outline"
                  @click.stop="generateBatches(task)"
                >
                  <Factory class="w-4 h-4 mr-1" />
                  生成批次
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 任务详情对话框 -->
    <Dialog v-model:open="showTaskDetails">
      <DialogContent class="max-w-6xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>排版任务详情 - {{ selectedTask?.taskName }}</DialogTitle>
        </DialogHeader>
        
        <div v-if="selectedTask" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">任务名称</Label>
              <p class="text-sm text-gray-600">{{ selectedTask.taskName }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">玻璃类型</Label>
              <p class="text-sm text-gray-600">
                {{ selectedTask.glassType.thickness }}mm {{ selectedTask.glassType.material }} {{ selectedTask.glassType.color }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">任务状态</Label>
              <Badge :variant="getTaskStatusVariant(selectedTask.status)">
                {{ getTaskStatusText(selectedTask.status) }}
              </Badge>
            </div>
            <div>
              <Label class="text-sm font-medium">创建时间</Label>
              <p class="text-sm text-gray-600">{{ formatDate(selectedTask.createdAt) }}</p>
            </div>
          </div>

          <!-- 订单明细 -->
          <div>
            <h4 class="font-medium mb-3">关联订单明细</h4>
            <div class="space-y-2">
              <div
                v-for="item in selectedTask.orderItems"
                :key="item.id"
                class="flex justify-between items-center p-3 border rounded-lg"
              >
                <div>
                  <p class="font-medium">
                    {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                  </p>
                  <p class="text-sm text-gray-600">数量：{{ item.quantity }}片</p>
                </div>
                <Button size="sm" variant="outline" @click="jumpToOrder(item.orderId)">
                  <ExternalLink class="w-3 h-3 mr-1" />
                  查看订单
                </Button>
              </div>
            </div>
          </div>

          <!-- 优化结果对比 -->
          <div v-if="selectedTask.optimizationResults && selectedTask.optimizationResults.length > 0">
            <h4 class="font-medium mb-3">优化结果对比</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                v-for="result in selectedTask.optimizationResults"
                :key="result.id"
                class="border rounded-lg p-4"
              >
                <div class="flex justify-between items-center mb-3">
                  <h5 class="font-medium">{{ result.algorithmName }}</h5>
                  <Badge :variant="result.algorithmType === 'internal' ? 'default' : 'secondary'">
                    {{ result.algorithmType === 'internal' ? '内置算法' : '第三方算法' }}
                  </Badge>
                </div>
                
                <div class="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span class="text-gray-600">利用率：</span>
                    <span class="font-bold text-green-600">{{ result.utilizationRate }}%</span>
                  </div>
                  <div>
                    <span class="text-gray-600">原片数：</span>
                    <span class="font-bold">{{ result.totalSheets }}片</span>
                  </div>
                  <div>
                    <span class="text-gray-600">总成本：</span>
                    <span class="font-bold">¥{{ result.totalCost.toLocaleString() }}</span>
                  </div>
                  <div>
                    <span class="text-gray-600">计算耗时：</span>
                    <span class="font-bold">{{ result.computationTime }}秒</span>
                  </div>
                </div>

                <div class="mt-3 flex gap-2">
                  <Button size="sm" variant="outline" @click="viewCuttingPlan(result)">
                    <Layout class="w-3 h-3 mr-1" />
                    查看方案
                  </Button>
                  <Button size="sm" variant="outline" @click="exportResult(result)">
                    <Download class="w-3 h-3 mr-1" />
                    导出数据
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 创建任务对话框 -->
    <Dialog v-model:open="showCreateTaskDialog">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>创建排版任务</DialogTitle>
        </DialogHeader>
        
        <form @submit.prevent="createTask" class="space-y-4">
          <div>
            <Label for="taskName">任务名称</Label>
            <Input
              id="taskName"
              v-model="newTask.taskName"
              placeholder="输入任务名称"
            />
          </div>
          
          <div class="grid grid-cols-3 gap-4">
            <div>
              <Label for="thickness">玻璃厚度(mm)</Label>
              <Select v-model="newTask.thickness">
                <SelectTrigger>
                  <SelectValue placeholder="选择厚度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="4">4mm</SelectItem>
                  <SelectItem value="5">5mm</SelectItem>
                  <SelectItem value="6">6mm</SelectItem>
                  <SelectItem value="8">8mm</SelectItem>
                  <SelectItem value="10">10mm</SelectItem>
                  <SelectItem value="12">12mm</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="material">玻璃材质</Label>
              <Select v-model="newTask.material">
                <SelectTrigger>
                  <SelectValue placeholder="选择材质" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="clear">普通透明</SelectItem>
                  <SelectItem value="low_e">Low-E玻璃</SelectItem>
                  <SelectItem value="tinted">有色玻璃</SelectItem>
                  <SelectItem value="reflective">反射玻璃</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="color">玻璃颜色</Label>
              <Select v-model="newTask.color">
                <SelectTrigger>
                  <SelectValue placeholder="选择颜色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="透明">透明</SelectItem>
                  <SelectItem value="灰色">灰色</SelectItem>
                  <SelectItem value="蓝色">蓝色</SelectItem>
                  <SelectItem value="绿色">绿色</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div class="flex justify-end gap-3">
            <Button type="button" variant="outline" @click="showCreateTaskDialog = false">
              取消
            </Button>
            <Button type="submit">
              创建任务
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>

    <!-- 排版方案查看对话框 -->
    <Dialog v-model:open="showCuttingPlanDialog">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>排版方案详情</DialogTitle>
        </DialogHeader>
        
        <div v-if="selectedResult" class="space-y-4">
          <div class="grid grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-600">算法：</span>
              <span class="font-bold">{{ selectedResult.algorithmName }}</span>
            </div>
            <div>
              <span class="text-gray-600">利用率：</span>
              <span class="font-bold text-green-600">{{ selectedResult.utilizationRate }}%</span>
            </div>
            <div>
              <span class="text-gray-600">原片数：</span>
              <span class="font-bold">{{ selectedResult.totalSheets }}片</span>
            </div>
            <div>
              <span class="text-gray-600">废料面积：</span>
              <span class="font-bold">{{ selectedResult.wasteArea }}m²</span>
            </div>
          </div>

          <!-- 切割方案列表 -->
          <div>
            <h4 class="font-medium mb-3">切割方案</h4>
            <div class="space-y-3">
              <div
                v-for="(plan, index) in selectedResult.cuttingPlans"
                :key="plan.id"
                class="border rounded-lg p-3"
              >
                <div class="flex justify-between items-center mb-2">
                  <h5 class="font-medium">原片 #{{ index + 1 }}</h5>
                  <div class="text-sm text-gray-600">
                    利用率：{{ plan.utilizationRate }}%
                  </div>
                </div>
                
                <!-- 简化的布局展示 -->
                <div class="bg-gray-100 p-3 rounded">
                  <div class="text-xs text-gray-600 mb-2">
                    原片尺寸：{{ plan.layout.sheetLength }}×{{ plan.layout.sheetWidth }}mm
                  </div>
                  <div class="grid grid-cols-4 gap-2 text-xs">
                    <div
                      v-for="(piece, pieceIndex) in plan.layout.pieces.slice(0, 8)"
                      :key="pieceIndex"
                      class="bg-blue-100 p-1 rounded text-center"
                    >
                      {{ piece.length }}×{{ piece.width }}
                    </div>
                    <div v-if="plan.layout.pieces.length > 8" class="text-gray-500 text-center">
                      +{{ plan.layout.pieces.length - 8 }}个
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Plus,
  RefreshCw,
  Clock,
  Zap,
  CheckCircle,
  TrendingUp,
  Scissors,
  Eye,
  ArrowRight,
  ExternalLink,
  Factory,
  Layout,
  Download,
} from 'lucide-vue-next'

import type { CuttingTask, OptimizationResult } from '@/types/mes-validation'
import { mesService } from '@/services/mesService'

const router = useRouter()

// 响应式数据
const tasks = ref<CuttingTask[]>([])
const searchQuery = ref('')
const statusFilter = ref('all')
const algorithmFilter = ref('all')
const showTaskDetails = ref(false)
const showCreateTaskDialog = ref(false)
const showCuttingPlanDialog = ref(false)
const selectedTask = ref<CuttingTask | null>(null)
const selectedResult = ref<OptimizationResult | null>(null)
const optimizingTasks = ref(new Set<string>())

// 新任务表单
const newTask = ref({
  taskName: '',
  thickness: '',
  material: '',
  color: '',
})

// 计算属性
const taskStats = computed(() => {
  const stats = {
    pending: 0,
    optimizing: 0,
    completed: 0,
    executed: 0,
  }
  
  tasks.value.forEach(task => {
    switch (task.status) {
      case 'pending':
        stats.pending++
        break
      case 'optimizing':
        stats.optimizing++
        break
      case 'completed':
        stats.completed++
        break
      case 'executed':
        stats.executed++
        break
    }
  })
  
  return stats
})

const filteredTasks = computed(() => {
  return tasks.value.filter(task => {
    const matchesSearch = !searchQuery.value || 
      task.taskName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      task.glassType.material.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = statusFilter.value === 'all' || task.status === statusFilter.value
    
    const matchesAlgorithm = algorithmFilter.value === 'all' || 
      (task.optimizationResults && task.optimizationResults.some(r => r.algorithmType === algorithmFilter.value))
    
    return matchesSearch && matchesStatus && matchesAlgorithm
  })
})

const averageUtilization = computed(() => {
  const completedTasks = tasks.value.filter(task => task.optimizationResults && task.optimizationResults.length > 0)
  if (completedTasks.length === 0) return 0
  
  const totalUtilization = completedTasks.reduce((sum, task) => {
    const bestResult = task.optimizationResults!.reduce((best, current) => 
      current.utilizationRate > best.utilizationRate ? current : best
    )
    return sum + bestResult.utilizationRate
  }, 0)
  
  return Math.round(totalUtilization / completedTasks.length)
})

// 方法
const loadTasks = async () => {
  try {
    tasks.value = await mesService.getCuttingTasks()
  } catch (error) {
    console.error('加载排版任务失败:', error)
  }
}

const refreshTasks = () => {
  loadTasks()
}

const selectTask = (task: CuttingTask) => {
  selectedTask.value = task
}

const viewTaskDetails = (task: CuttingTask) => {
  selectedTask.value = task
  showTaskDetails.value = true
}

const runOptimization = async (task: CuttingTask) => {
  optimizingTasks.value.add(task.id)
  task.status = 'optimizing'
  
  try {
    // 模拟优化过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const results = await mesService.runOptimization(task.id, 'internal')
    task.optimizationResults = results
    task.status = 'completed'
  } catch (error) {
    console.error('运行优化失败:', error)
    task.status = 'pending'
  } finally {
    optimizingTasks.value.delete(task.id)
  }
}

const generateBatches = async (task: CuttingTask) => {
  try {
    // 跳转到生产调度界面，并传递任务信息
    router.push(`/mes/production-scheduling?fromTask=${task.id}`)
  } catch (error) {
    console.error('生成生产批次失败:', error)
  }
}

const createTask = async () => {
  const taskData = {
    taskName: newTask.value.taskName,
    glassType: {
      material: newTask.value.material,
      thickness: parseInt(newTask.value.thickness),
      color: newTask.value.color
    },
    orderItems: [], // 后续可以关联订单明细
    rawSheets: [],
    status: 'pending' as const,
    createdAt: new Date().toISOString(),
  }
  
  const createdTask = await mesService.createCuttingTask([])
  if (createdTask) {
    tasks.value.unshift({
      ...createdTask,
      ...taskData
    })
    showCreateTaskDialog.value = false
    
    // 重置表单
    newTask.value = {
      taskName: '',
      thickness: '',
      material: '',
      color: '',
    }
  }
}

const viewCuttingPlan = (result: OptimizationResult) => {
  selectedResult.value = result
  showCuttingPlanDialog.value = true
}

const exportResult = (result: OptimizationResult) => {
  const data = {
    算法名称: result.algorithmName,
    利用率: `${result.utilizationRate}%`,
    原片数量: result.totalSheets,
    总成本: result.totalCost,
    计算耗时: `${result.computationTime}秒`,
    切割方案数: result.cuttingPlans.length
  }
  
  console.log('导出优化结果:', data)
  // 这里可以集成实际的导出功能
}

const jumpToOrder = (orderId: string) => {
  router.push(`/mes/orders?orderId=${orderId}`)
}

// 工具方法
const getTotalPieces = (task: CuttingTask): number => {
  return task.orderItems.reduce((sum, item) => sum + item.quantity, 0)
}

const getBestUtilization = (task: CuttingTask): number => {
  if (!task.optimizationResults || task.optimizationResults.length === 0) return 0
  return Math.max(...task.optimizationResults.map(r => r.utilizationRate))
}

const getBestSheetCount = (task: CuttingTask): number => {
  if (!task.optimizationResults || task.optimizationResults.length === 0) return 0
  const bestResult = task.optimizationResults.reduce((best, current) => 
    current.utilizationRate > best.utilizationRate ? current : best
  )
  return bestResult.totalSheets
}

const getBestSavings = (task: CuttingTask): number => {
  if (!task.optimizationResults || task.optimizationResults.length === 0) return 0
  const bestResult = task.optimizationResults.reduce((best, current) => 
    current.utilizationRate > best.utilizationRate ? current : best
  )
  return bestResult.savings || 0
}

const getTaskStatusVariant = (status: string) => {
  switch (status) {
    case 'pending': return 'secondary'
    case 'optimizing': return 'default'
    case 'completed': return 'default'
    case 'executed': return 'default'
    default: return 'secondary'
  }
}

const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待优化'
    case 'optimizing': return '优化中'
    case 'completed': return '已完成'
    case 'executed': return '已执行'
    default: return status
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>