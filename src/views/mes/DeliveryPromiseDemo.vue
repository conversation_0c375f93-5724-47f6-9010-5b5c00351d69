<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import ValueMetricsCard from '@/components/mes/ValueMetricsCard.vue'
import TelemetryPanel from '@/components/mes/TelemetryPanel.vue'
import { aggregateMetrics } from '@/utils/ValueMetricsAggregator'
import { mesApi } from '@/utils/mesApi'
import { recordEvent } from '@/utils/Telemetry'

const analysis = ref<any>(null)
const metrics = ref<any[]>([])

async function loadData() {
  const t0 = performance.now()
  const res = await mesApi.getAccuracyAnalysis()
  analysis.value = res.ok ? res.data : null
  metrics.value = await aggregateMetrics()
  recordEvent('page_load_delivery', {}, performance.now() - t0)
}

const accuracyImprovement = computed(() => {
  if (!analysis.value) return 0
  return (analysis.value.smartMethod.accuracyRate - analysis.value.traditionalMethod.accuracyRate).toFixed(1)
})

onMounted(loadData)
</script>

<template>
  <div class="p-4 space-y-4">
    <h2 class="text-xl font-semibold">交期承诺准确性验证</h2>

    <div class="border rounded p-3" v-if="analysis">
      <div>传统准确率：{{ analysis.traditionalMethod.accuracyRate }}%</div>
      <div>智能准确率：{{ analysis.smartMethod.accuracyRate }}%</div>
      <div class="font-bold">提升：{{ accuracyImprovement }}%</div>
      <div>投诉减少：{{ analysis.businessImpact.complaintReduction }}%</div>
      <div class="mt-3">
        <ValueMetricsCard :metrics="metrics" />
      </div>
    </div>
    <TelemetryPanel />
  </div>
</template>

<style scoped>
</style>

