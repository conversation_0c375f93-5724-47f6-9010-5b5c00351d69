<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- 页面标题和操作栏 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">订单管理</h1>
        <p class="text-gray-600 mt-1">管理客户订单，跟踪生产进度，展示数据流转关系</p>
      </div>
      <div class="flex gap-3">
        <Button @click="refreshOrders" variant="outline">
          <RefreshCw class="w-4 h-4 mr-2" />
          刷新
        </Button>
        <Button @click="showCreateDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          新建订单
        </Button>
      </div>
    </div>

    <!-- 数据流转概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">待排版订单</p>
              <p class="text-2xl font-bold text-blue-600">{{ orderStats.pending }}</p>
            </div>
            <FileText class="w-8 h-8 text-blue-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">→ 排版任务</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">生产中订单</p>
              <p class="text-2xl font-bold text-orange-600">{{ orderStats.inProgress }}</p>
            </div>
            <Factory class="w-8 h-8 text-orange-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">→ 生产批次</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">质检中订单</p>
              <p class="text-2xl font-bold text-yellow-600">{{ orderStats.qualityCheck }}</p>
            </div>
            <CheckCircle class="w-8 h-8 text-yellow-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">→ 质检记录</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">已完成订单</p>
              <p class="text-2xl font-bold text-green-600">{{ orderStats.completed }}</p>
            </div>
            <Package class="w-8 h-8 text-green-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">→ 成品交付</p>
        </CardContent>
      </Card>
    </div>

    <!-- 筛选和搜索 -->
    <Card>
      <CardContent class="p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <div class="flex-1 min-w-64">
            <Input
              :model-value="state.filters.search"
              @update:model-value="handleSearchChange"
              placeholder="搜索订单号、客户名称或项目名称..."
              class="w-full"
            />
          </div>
          <Select :model-value="state.filters.status" @update:model-value="handleStatusFilterChange">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="订单状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="pending">待排版</SelectItem>
              <SelectItem value="in_progress">生产中</SelectItem>
              <SelectItem value="quality_check">质检中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
            </SelectContent>
          </Select>
          <Select :model-value="state.filters.customer" @update:model-value="handleCustomerFilterChange">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="客户筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部客户</SelectItem>
              <SelectItem value="华润置地">华润置地</SelectItem>
              <SelectItem value="万科集团">万科集团</SelectItem>
              <SelectItem value="绿地控股">绿地控股</SelectItem>
              <SelectItem value="中海地产">中海地产</SelectItem>
              <SelectItem value="保利地产">保利地产</SelectItem>
              <SelectItem value="龙湖集团">龙湖集团</SelectItem>
              <SelectItem value="融创中国">融创中国</SelectItem>
              <SelectItem value="碧桂园">碧桂园</SelectItem>
              <SelectItem value="恒大集团">恒大集团</SelectItem>
              <SelectItem value="金茂集团">金茂集团</SelectItem>
            </SelectContent>
          </Select>
          <Select v-model="orderTypeFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="订单类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="幕墙工程">幕墙工程</SelectItem>
              <SelectItem value="门窗工程">门窗工程</SelectItem>
              <SelectItem value="办公隔断">办公隔断</SelectItem>
              <SelectItem value="中空玻璃">中空玻璃</SelectItem>
              <SelectItem value="夹胶玻璃">夹胶玻璃</SelectItem>
              <SelectItem value="装饰玻璃">装饰玻璃</SelectItem>
              <SelectItem value="家具玻璃">家具玻璃</SelectItem>
              <SelectItem value="阳光房">阳光房</SelectItem>
              <SelectItem value="展示柜">展示柜</SelectItem>
              <SelectItem value="特殊工艺">特殊工艺</SelectItem>
            </SelectContent>
          </Select>
          <Select v-model="priorityFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="优先级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部优先级</SelectItem>
              <SelectItem value="urgent">紧急</SelectItem>
              <SelectItem value="high">高</SelectItem>
              <SelectItem value="normal">普通</SelectItem>
              <SelectItem value="low">低</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>

    <!-- 订单列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <List class="w-5 h-5" />
          订单列表
          <Badge variant="secondary">{{ filteredOrders.length }} 个订单</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="order in filteredOrders"
            :key="order.id"
            class="border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
            @click="selectOrder(order)"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <h3 class="font-semibold text-lg">{{ order.orderNumber }}</h3>
                  <Badge :variant="getStatusVariant(order.status)">
                    {{ getStatusText(order.status) }}
                  </Badge>
                  <Badge variant="outline">{{ order.customerName }}</Badge>
                  <Badge v-if="(order as any).orderType" variant="secondary">{{ (order as any).orderType }}</Badge>
                  <Badge v-if="(order as any).priority" :variant="getPriorityVariant((order as any).priority)">
                    {{ getPriorityText((order as any).priority) }}
                  </Badge>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div>
                    <span class="font-medium">项目名称：</span>
                    {{ order.projectName }}
                  </div>
                  <div>
                    <span class="font-medium">订单金额：</span>
                    ¥{{ order.estimatedCost.toLocaleString() }}
                  </div>
                  <div>
                    <span class="font-medium">要求交期：</span>
                    {{ formatDate(order.requiredDate) }}
                  </div>
                </div>
                
                <!-- 数据流转链路展示 -->
                <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                  <p class="text-sm font-medium text-blue-800 mb-2">数据流转链路：</p>
                  <div class="flex items-center gap-2 text-xs">
                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded">订单明细</span>
                    <ArrowRight class="w-3 h-3 text-blue-500" />
                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded">排版任务</span>
                    <ArrowRight class="w-3 h-3 text-orange-500" />
                    <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded">生产批次</span>
                    <ArrowRight class="w-3 h-3 text-purple-500" />
                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded">质检记录</span>
                  </div>
                </div>
              </div>
              
              <div class="flex flex-col items-end gap-2">
                <Button size="sm" variant="outline" @click.stop="viewOrderDetails(order)">
                  <Eye class="w-4 h-4 mr-1" />
                  查看详情
                </Button>
                <Button size="sm" variant="outline" @click.stop="trackDataFlow(order)">
                  <GitBranch class="w-4 h-4 mr-1" />
                  数据流转
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 订单详情对话框 -->
    <Dialog v-model:open="showOrderDetails">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>订单详情 - {{ selectedOrder?.orderNumber }}</DialogTitle>
        </DialogHeader>
        
        <div v-if="selectedOrder" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">客户名称</Label>
              <p class="text-sm text-gray-600">{{ selectedOrder.customerName }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">项目名称</Label>
              <p class="text-sm text-gray-600">{{ selectedOrder.projectName }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">订单金额</Label>
              <p class="text-sm text-gray-600">¥{{ selectedOrder.estimatedCost.toLocaleString() }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">要求交期</Label>
              <p class="text-sm text-gray-600">{{ formatDate(selectedOrder.requiredDate) }}</p>
            </div>
          </div>

          <!-- 订单明细 -->
          <div>
            <h4 class="font-medium mb-3">订单明细</h4>
            <div class="space-y-3">
              <div
                v-for="item in selectedOrder.items"
                :key="item.id"
                class="border rounded-lg p-3"
              >
                <div class="flex justify-between items-start mb-2">
                  <div>
                    <p class="font-medium">
                      {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                    </p>
                    <p class="text-sm text-gray-600">
                      {{ item.specifications.glassType }} {{ item.specifications.color }} × {{ item.quantity }}片
                    </p>
                  </div>
                  <Badge variant="outline">{{ item.currentStatus }}</Badge>
                </div>
                
                <!-- 工艺流程 -->
                <div class="mt-3">
                  <p class="text-sm font-medium mb-2">工艺流程：</p>
                  <div class="flex items-center gap-2 text-xs">
                    <span
                      v-for="(step, index) in item.processFlow"
                      :key="index"
                      class="px-2 py-1 rounded"
                      :class="getStepStatusClass(step.status)"
                    >
                      {{ step.stepName }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 数据流转跟踪对话框 -->
    <Dialog v-model:open="showDataFlowDialog">
      <DialogContent class="max-w-5xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>数据流转跟踪 - {{ selectedOrder?.orderNumber }}</DialogTitle>
        </DialogHeader>
        
        <div v-if="selectedOrder" class="space-y-6">
          <!-- 流转概览 -->
          <div class="bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-lg">
            <h4 class="font-medium mb-3">端到端数据流转</h4>
            <div class="flex items-center justify-between">
              <div class="text-center">
                <div class="w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center mb-2">
                  <FileText class="w-6 h-6" />
                </div>
                <p class="text-sm font-medium">客户订单</p>
                <p class="text-xs text-gray-600">{{ selectedOrder.items.length }}个明细</p>
              </div>
              <ArrowRight class="w-6 h-6 text-gray-400" />
              <div class="text-center">
                <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mb-2">
                  <Scissors class="w-6 h-6" />
                </div>
                <p class="text-sm font-medium">排版任务</p>
                <p class="text-xs text-gray-600">{{ relatedData.cuttingTasks.length }}个任务</p>
              </div>
              <ArrowRight class="w-6 h-6 text-gray-400" />
              <div class="text-center">
                <div class="w-12 h-12 bg-purple-500 text-white rounded-full flex items-center justify-center mb-2">
                  <Factory class="w-6 h-6" />
                </div>
                <p class="text-sm font-medium">生产批次</p>
                <p class="text-xs text-gray-600">{{ relatedData.productionBatches.length }}个批次</p>
              </div>
              <ArrowRight class="w-6 h-6 text-gray-400" />
              <div class="text-center">
                <div class="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center mb-2">
                  <CheckCircle class="w-6 h-6" />
                </div>
                <p class="text-sm font-medium">质检记录</p>
                <p class="text-xs text-gray-600">{{ relatedData.qualityRecords.length }}条记录</p>
              </div>
            </div>
          </div>

          <!-- 详细流转信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 排版任务关联 -->
            <Card>
              <CardHeader>
                <CardTitle class="text-lg">关联排版任务</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-3">
                  <div
                    v-for="task in relatedData.cuttingTasks"
                    :key="task.id"
                    class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                    @click="jumpToCuttingTask(task)"
                  >
                    <div class="flex justify-between items-center">
                      <div>
                        <p class="font-medium">{{ task.taskName }}</p>
                        <p class="text-sm text-gray-600">{{ task.glassType.thickness }}mm {{ task.glassType.color }}</p>
                      </div>
                      <Badge :variant="getTaskStatusVariant(task.status)">
                        {{ task.status }}
                      </Badge>
                    </div>
                    <div class="mt-2 text-xs text-blue-600 flex items-center gap-1">
                      <ExternalLink class="w-3 h-3" />
                      点击跳转到排版优化界面
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- 生产批次关联 -->
            <Card>
              <CardHeader>
                <CardTitle class="text-lg">关联生产批次</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-3">
                  <div
                    v-for="batch in relatedData.productionBatches"
                    :key="batch.id"
                    class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                    @click="jumpToProductionBatch(batch)"
                  >
                    <div class="flex justify-between items-center">
                      <div>
                        <p class="font-medium">{{ batch.batchNumber }}</p>
                        <p class="text-sm text-gray-600">{{ batch.workstation }} - {{ batch.processType }}</p>
                      </div>
                      <Badge :variant="getBatchStatusVariant(batch.status)">
                        {{ batch.status }}
                      </Badge>
                    </div>
                    <div class="mt-2 text-xs text-purple-600 flex items-center gap-1">
                      <ExternalLink class="w-3 h-3" />
                      点击跳转到生产调度界面
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 创建订单对话框 -->
    <Dialog v-model:open="showCreateDialog">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>创建新订单</DialogTitle>
        </DialogHeader>
        
        <form @submit.prevent="handleCreateOrder" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label for="orderNumber">订单号</Label>
              <Input
                id="orderNumber"
                v-model="newOrder.orderNumber"
                placeholder="自动生成"
                disabled
              />
            </div>
            <div>
              <Label for="customerName">客户名称</Label>
              <Select v-model="newOrder.customerName">
                <SelectTrigger>
                  <SelectValue placeholder="选择客户" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="华润置地">华润置地</SelectItem>
                  <SelectItem value="万科集团">万科集团</SelectItem>
                  <SelectItem value="绿地控股">绿地控股</SelectItem>
                  <SelectItem value="保利地产">保利地产</SelectItem>
                  <SelectItem value="中海地产">中海地产</SelectItem>
                  <SelectItem value="龙湖集团">龙湖集团</SelectItem>
                  <SelectItem value="融创中国">融创中国</SelectItem>
                  <SelectItem value="碧桂园">碧桂园</SelectItem>
                  <SelectItem value="恒大集团">恒大集团</SelectItem>
                  <SelectItem value="金茂集团">金茂集团</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <Label for="projectName">项目名称</Label>
            <Input
              id="projectName"
              v-model="newOrder.projectName"
              placeholder="输入项目名称"
            />
          </div>
          
          <div>
            <Label for="requiredDate">要求交期</Label>
            <Input
              id="requiredDate"
              v-model="newOrder.requiredDate"
              type="date"
            />
          </div>
          
          <div class="flex justify-end gap-3">
            <Button type="button" variant="outline" @click="showCreateDialog = false">
              取消
            </Button>
            <Button type="submit">
              创建订单
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderManagement } from '@/composables/useOrderManagement'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Plus,
  RefreshCw,
  FileText,
  Factory,
  CheckCircle,
  Package,
  List,
  Eye,
  GitBranch,
  ArrowRight,
  Scissors,
  ExternalLink,
} from 'lucide-vue-next'

import type { ValidationOrder, CuttingTask, ProductionBatch } from '@/types/mes-validation'

const router = useRouter()

// 使用订单管理 Composable
const {
  state,
  relatedData,
  filteredOrders,
  orderStats,
  urgentOrders,
  loadOrders,
  getOrderById,
  createOrder: createOrderService,
  updateOrderStatus,
  getStatusDisplayName,
  loadOrderRelatedData,
  updateFilters,
} = useOrderManagement()

// 本地状态
const showOrderDetails = ref(false)
const showDataFlowDialog = ref(false)
const showCreateDialog = ref(false)
const selectedOrder = ref<ValidationOrder | null>(null)

// 额外的筛选条件
const orderTypeFilter = ref('all')
const priorityFilter = ref('all')

// 新订单表单
const newOrder = ref({
  orderNumber: '',
  customerName: '',
  projectName: '',
  requiredDate: '',
})

// 监听筛选条件变化
watch(() => state.filters.search, (newValue) => {
  // 可以在这里添加防抖逻辑
})

// 方法
const refreshOrders = async () => {
  await loadOrders()
}

const selectOrder = (order: ValidationOrder) => {
  selectedOrder.value = order
}

const viewOrderDetails = async (order: ValidationOrder) => {
  selectedOrder.value = order
  showOrderDetails.value = true
}

const trackDataFlow = async (order: ValidationOrder) => {
  selectedOrder.value = order
  await loadOrderRelatedData(order.id)
  showDataFlowDialog.value = true
}

const jumpToCuttingTask = (task: CuttingTask) => {
  // 跳转到排版优化界面
  router.push(`/mes/validation/cutting?taskId=${task.id}`)
}

const jumpToProductionBatch = (batch: ProductionBatch) => {
  // 跳转到生产调度界面
  router.push(`/mes/validation/scheduling?batchId=${batch.id}`)
}

const handleCreateOrder = async () => {
  const orderData = {
    customerName: newOrder.value.customerName,
    requiredDate: newOrder.value.requiredDate,
    items: [], // 后续可以添加明细
  }
  
  const createdOrder = await createOrderService(orderData)
  if (createdOrder) {
    showCreateDialog.value = false
    // 重置表单
    newOrder.value = {
      orderNumber: '',
      customerName: '',
      projectName: '',
      requiredDate: '',
    }
  }
}

const handleStatusChange = async (orderId: string, newStatus: string) => {
  await updateOrderStatus(orderId, newStatus)
}

// 搜索和筛选处理
const handleSearchChange = (value: string) => {
  updateFilters({ search: value })
}

const handleStatusFilterChange = (value: string) => {
  updateFilters({ status: value })
}

const handleCustomerFilterChange = (value: string) => {
  updateFilters({ customer: value })
}

// 状态相关方法
const getStatusVariant = (status: string) => {
  switch (status) {
    case 'pending': return 'secondary'
    case 'in_progress': return 'default'
    case 'quality_check': return 'outline'
    case 'completed': return 'default'
    default: return 'secondary'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待排版'
    case 'in_progress': return '生产中'
    case 'quality_check': return '质检中'
    case 'completed': return '已完成'
    default: return status
  }
}

const getTaskStatusVariant = (status: string) => {
  switch (status) {
    case 'pending': return 'secondary'
    case 'optimizing': return 'default'
    case 'completed': return 'default'
    case 'executed': return 'default'
    default: return 'secondary'
  }
}

const getBatchStatusVariant = (status: string) => {
  switch (status) {
    case 'planned': return 'secondary'
    case 'in_progress': return 'default'
    case 'completed': return 'default'
    case 'cancelled': return 'destructive'
    default: return 'secondary'
  }
}

const getStepStatusClass = (status: string) => {
  switch (status) {
    case 'pending': return 'bg-gray-100 text-gray-700'
    case 'in_progress': return 'bg-blue-100 text-blue-700'
    case 'completed': return 'bg-green-100 text-green-700'
    case 'skipped': return 'bg-yellow-100 text-yellow-700'
    default: return 'bg-gray-100 text-gray-700'
  }
}

const getPriorityVariant = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'destructive'
    case 'high': return 'default'
    case 'normal': return 'secondary'
    case 'low': return 'outline'
    default: return 'secondary'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'urgent': return '紧急'
    case 'high': return '高'
    case 'normal': return '普通'
    case 'low': return '低'
    default: return priority
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(async () => {
  await loadOrders()
})
</script>