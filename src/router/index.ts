import { createRouter, createWebHistory } from "vue-router";
import type { RouteRecordRaw } from "vue-router";
import DashboardView from "../views/DashboardView.vue";
import { masterDataRoutes } from "./masterdata"; // 导入新的主数据路由
import { getAllModuleRoutes } from "./modules"; // 导入业务模块路由

// 核心业务路由配置
const coreRoutes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: "/dashboard",
  },
  // 仪表盘路由 (Requirement 6.2)
  {
    path: "/dashboard",
    name: "dashboard",
    component: DashboardView,
    meta: {
      title: "仪表盘",
      icon: "LayoutDashboard",
      requiresAuth: true,
    },
  },
  // 可视化编辑器演示
  {
    path: "/visual-editor-demo",
    name: "visual-editor-demo",
    component: () => import("../views/demo/VisualEditorDemo.vue"),
    meta: {
      title: "可视化编辑器演示",
      icon: "Network",
      requiresAuth: true,
    },
  },
  // 设计按钮演示
  {
    path: "/design-button-demo",
    name: "design-button-demo",
    component: () => import("../views/demo/DesignButtonDemo.vue"),
    meta: {
      title: "设计按钮演示",
      icon: "Palette",
      requiresAuth: true,
    },
  },
  // 甘特图时间测试
  {
    path: "/gantt-time-test",
    name: "gantt-time-test",
    component: () => import("../views/test/GanttTimeTest.vue"),
    meta: {
      title: "甘特图时间测试",
      icon: "Clock",
      requiresAuth: true,
    },
  },
  // 增强版设计弹窗演示
  {
    path: "/enhanced-design-dialog-demo",
    name: "enhanced-design-dialog-demo",
    component: () => import("../views/demo/EnhancedDesignDialogDemo.vue"),
    meta: {
      title: "增强版设计弹窗演示",
      icon: "Settings",
      requiresAuth: true,
    },
  },
  // 客户关系管理路由 (Requirement 6.2)
  {
    path: "/crm",
    name: "crm",
    component: () => import("../views/CrmView.vue"),
    meta: {
      title: "客户关系管理",
      icon: "Users",
      requiresAuth: true,
    },
  },
];

// 库存管理模块路由 (Requirement 6.3 - 模块化扩展)
const inventoryRoutes: RouteRecordRaw[] = [
  {
    path: "/inventory",
    name: "inventory",
    component: () => import("../views/InventoryView.vue"),
    meta: {
      title: "库存管理",
      icon: "Package",
      requiresAuth: true,
    },
  },
];

// 生产管理模块路由 (Requirement 6.3 - 模块化扩展)
const productionRoutes: RouteRecordRaw[] = [
  // 概览/仪表盘
  {
    path: "/mes",
    name: "mes",
    component: () => import("../views/MesView.vue"),
    meta: {
      title: "生产执行系统",
      icon: "Factory",
      requiresAuth: true,
    },
  },

  // MVP 三大验证入口（已实现的演示页）
  {
    path: "/mes/validation/cutting",
    name: "mes-cutting-validation",
    component: () => import("../views/mes/CuttingOptimizationDemo.vue"),
    meta: {
      title: "排版优化验证",
      icon: "PanelRight",
      requiresAuth: false,
      hidden: false,
    },
  },
  {
    path: "/mes/validation/scheduling",
    name: "mes-scheduling-validation",
    component: () => import("../views/mes/WorkstationSchedulingDemo.vue"),
    meta: {
      title: "工段调度验证",
      icon: "PanelsTopLeft",
      requiresAuth: false,
      hidden: false,
    },
  },
  {
    path: "/mes/validation/delivery",
    name: "mes-delivery-validation",
    component: () => import("../views/mes/DeliveryPromiseDemo.vue"),
    meta: {
      title: "交期承诺验证",
      icon: "CalendarCheck2",
      requiresAuth: false,
      hidden: false,
    },
  },

  // 核心功能界面（已实现）
  {
    path: "/mes/orders",
    name: "mes-orders",
    component: () => import("../views/mes/OrderManagement.vue"),
    meta: { title: "客户订单管理", icon: "FileText", requiresAuth: true },
  },
  {
    path: "/mes/production-orders",
    name: "mes-production-orders",
    component: () => import("../views/mes/ProductionOrderManagement.vue"),
    meta: { title: "生产工单管理", icon: "ClipboardList", requiresAuth: true },
  },
  {
    path: "/mes/cutting-optimization",
    name: "mes-cutting-optimization",
    component: () => import("../views/mes/CuttingOptimization.vue"),
    meta: { title: "排版优化管理", icon: "Scissors", requiresAuth: true },
  },

  // 新增：排产规划工作台
  {
    path: "/mes/scheduling-workbench",
    name: "mes-scheduling-workbench",
    component: () => import("../views/mes/ProductionSchedulingWorkbench.vue"),
    meta: { title: "排产规划工作台", icon: "CalendarCog", requiresAuth: true },
  },

  // 功能蓝图（占位路由，指向通用占位页；后续替换为具体页面组件）
  {
    // 生产计划
    path: "/mes/planning",
    name: "mes-planning",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "生产计划", icon: "Calendar", requiresAuth: true },
  },
  {
    // 工艺管理
    path: "/mes/processes",
    name: "mes-processes",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "工艺管理", icon: "Settings", requiresAuth: true },
  },
  {
    // 批次管理
    path: "/mes/batches",
    name: "mes-batches",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "批次管理", icon: "Layers", requiresAuth: true },
  },
  {
    // 工段与工位（总览）
    path: "/mes/workstations",
    name: "mes-workstations",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "工段与工位", icon: "LayoutGrid", requiresAuth: true },
  },
  {
    // 冷加工工段
    path: "/mes/workstations/cutting",
    name: "mes-workstations-cutting",
    component: () => import("../views/RouteTestView.vue"),
    meta: {
      title: "冷加工工段",
      icon: "Scissors",
      requiresAuth: true,
      parent: "mes-workstations",
    },
  },
  {
    // 钢化工段
    path: "/mes/workstations/tempering",
    name: "mes-workstations-tempering",
    component: () => import("../views/RouteTestView.vue"),
    meta: {
      title: "钢化工段",
      icon: "Flame",
      requiresAuth: true,
      parent: "mes-workstations",
    },
  },
  {
    // 合片工段（中空/夹胶）
    path: "/mes/workstations/insulating",
    name: "mes-workstations-insulating",
    component: () => import("../views/RouteTestView.vue"),
    meta: {
      title: "合片工段",
      icon: "SquareStack",
      requiresAuth: true,
      parent: "mes-workstations",
    },
  },
  {
    // 半成品库存与配对
    path: "/mes/semi-finished",
    name: "mes-semi-finished",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "半成品库存", icon: "Boxes", requiresAuth: true },
  },
  {
    // 质量与追溯（订单/批次）
    path: "/mes/tracing/orders",
    name: "mes-tracing-orders",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "订单追溯", icon: "Route", requiresAuth: true },
  },
  {
    path: "/mes/tracing/batches",
    name: "mes-tracing-batches",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "批次追溯", icon: "ListOrdered", requiresAuth: true },
  },
  {
    // 成本与核算
    path: "/mes/costing",
    name: "mes-costing",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "订单成本", icon: "Calculator", requiresAuth: true },
  },
  {
    // 系统集成展示（数据导出/导入、第三方排版）
    path: "/mes/integration",
    name: "mes-integration",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "系统集成", icon: "PlugZap", requiresAuth: true },
  },
  {
    // 算法切换策略演示
    path: "/mes/strategy",
    name: "mes-strategy",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "算法切换策略", icon: "GitCompare", requiresAuth: false },
  },
  {
    // 异常处理演示（破损/缺货/取消/设备）
    path: "/mes/exceptions",
    name: "mes-exceptions",
    component: () => import("../views/RouteTestView.vue"),
    meta: { title: "异常处理演示", icon: "AlertTriangle", requiresAuth: false },
  },
];

// 采购管理模块路由 (Requirement 6.3 - 模块化扩展)
const procurementRoutes: RouteRecordRaw[] = [
  {
    path: "/procurement",
    name: "procurement",
    component: () => import("../views/ProcurementView.vue"),
    meta: {
      title: "采购管理",
      icon: "ShoppingCart",
      requiresAuth: true,
    },
  },
];

// 质量管理模块路由 (Requirement 6.3 - 模块化扩展)
const qualityRoutes: RouteRecordRaw[] = [
  {
    path: "/quality",
    name: "quality",
    component: () => import("../views/QualityView.vue"),
    meta: {
      title: "质量管理",
      icon: "CheckCircle",
      requiresAuth: true,
    },
  },
];

// 开发测试路由 (仅开发环境)
const developmentRoutes: RouteRecordRaw[] = [
  {
    path: "/route-test",
    name: "route-test",
    component: () => import("../views/RouteTestView.vue"),
    meta: {
      title: "路由测试",
      hidden: true,
    },
  },
  {
    path: "/user-test",
    name: "user-test",
    component: () => import("../views/UserTestView.vue"),
    meta: {
      title: "用户测试",
      hidden: true,
    },
  },
  {
    path: "/store-test",
    name: "store-test",
    component: () => import("../views/StoreTestView.vue"),
    meta: {
      title: "状态测试",
      hidden: true,
    },
  },
  {
    path: "/components-test",
    name: "components-test",
    component: () => import("../views/ComponentsTest.vue"),
    meta: {
      title: "组件测试",
      hidden: true,
    },
  },
  {
    path: "/data-integration-test",
    name: "data-integration-test",
    component: () => import("../views/DataIntegrationTestView.vue"),
    meta: {
      title: "数据集成测试",
      hidden: true,
    },
  },
];

// 合并所有路由 (Requirement 6.3 - 支持模块化扩展)
const allRoutes: RouteRecordRaw[] = [
  ...coreRoutes,
  ...getAllModuleRoutes(), // 使用模块化路由
  ...masterDataRoutes, // 添加新的主数据路由
  ...developmentRoutes,
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: allRoutes,
});

// 路由守卫 - 确保路由能够正确导航 (Requirement 6.4)
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 玻璃ERP系统`;
  }

  // 验证路由是否存在
  if (to.matched.length === 0) {
    console.warn(`Route not found: ${to.path}`);
    next("/dashboard"); // 重定向到仪表盘
    return;
  }

  // 这里可以添加权限验证逻辑
  // if (to.meta?.requiresAuth && !isAuthenticated()) {
  //   next('/login')
  //   return
  // }

  next();
});

// 路由错误处理
router.onError((error) => {
  
});

// 导出路由实例和路由配置 (Requirement 6.3 - 支持模块化扩展)
export default router;

// 导出路由配置供其他模块使用
export {
  coreRoutes,
  inventoryRoutes,
  productionRoutes,
  procurementRoutes,
  qualityRoutes,
  masterDataRoutes, // 导出新的主数据路由
  developmentRoutes,
};

// 路由工具函数
export const getRoutesByModule = (module: string): RouteRecordRaw[] => {
  switch (module) {
    case "core":
      return coreRoutes;
    case "inventory":
      return inventoryRoutes;
    case "production":
      return productionRoutes;
    case "procurement":
      return procurementRoutes;
    case "quality":
      return qualityRoutes;
    case "masterdata": // 添加新的case
      return masterDataRoutes;
    case "development":
      return developmentRoutes;
    default:
      return [];
  }
};

// 获取所有可见路由（排除隐藏路由）
export const getVisibleRoutes = (): RouteRecordRaw[] => {
  return allRoutes.filter((route) => !route.meta?.hidden);
};

// 根据路由名称获取路由信息
export const getRouteByName = (name: string): RouteRecordRaw | undefined => {
  return allRoutes.find((route) => route.name === name);
};
