// src/router/masterdata.ts
import type { RouteRecordRaw } from 'vue-router';

export const masterDataRoutes: RouteRecordRaw[] = [
  {
    path: '/masterdata',
    name: 'masterdata',
    redirect: '/masterdata/dashboard',
    meta: {
      title: '主数据管理',
      icon: 'Database',
      requiresAuth: true,
    },
    children: [
      // --- 工作台 ---
      {
        path: 'dashboard',
        name: 'masterdata-dashboard',
        component: () => import('@/views/masterdata/DashboardView.vue'),
        meta: { title: '工作台' },
      },
      // --- 资源管理 ---
      {
        path: 'equipments',
        name: 'masterdata-equipments',
        component: () => import('@/views/masterdata/EquipmentView.vue'),
        meta: { title: '设备管理' },
      },
      {
        path: 'workcenters',
        name: 'masterdata-workcenters',
        component: () => import('@/views/masterdata/WorkCenterView.vue'),
        meta: { title: '工作中心' },
      },
      // --- 产品工程 ---
      {
        path: 'materials',
        name: 'masterdata-materials',
        component: () => import('@/views/masterdata/MaterialView.vue'),
        meta: { title: '物料管理' },
      },
      {
        path: 'product-families',
        name: 'masterdata-product-families',
        component: () => import('@/views/masterdata/ProductFamilyView.vue'),
        meta: { title: '产品族' },
      },
      // --- 工艺工程 ---
      {
        path: 'process-steps',
        name: 'masterdata-process-steps',
        component: () => import('@/views/masterdata/ProcessStepView.vue'),
        meta: { title: '标准工序' },
      },
      {
        path: 'process-segments',
        name: 'masterdata-process-segments',
        component: () => import('@/views/masterdata/ProcessSegmentView.vue'),
        meta: { title: '工艺段' },
      },
      {
        path: 'routings',
        name: 'masterdata-routings',
        component: () => import('@/views/masterdata/RoutingView.vue'),
        meta: { title: '工艺路线' },
      },
    ],
  },
];
