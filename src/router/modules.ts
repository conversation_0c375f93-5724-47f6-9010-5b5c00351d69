import type { RouteRecordRaw } from "vue-router";

/**
 * 路由模块配置文件
 * 支持按功能模块扩展路由配置 (Requirement 6.3)
 */

// 路由模块接口定义
export interface RouteModule {
  name: string;
  routes: RouteRecordRaw[];
  meta?: {
    title: string;
    description?: string;
    order?: number;
  };
}

// 生产管理模块路由配置
export const productionModule: RouteModule = {
  name: "production",
  meta: {
    title: "生产管理",
    description: "生产计划、工艺管理、设备监控",
    order: 1,
  },
  routes: [
    {
      path: "/mes",
      name: "mes",
      component: () => import("../views/MesView.vue"),
      meta: {
        title: "生产执行系统",
        icon: "Factory",
        requiresAuth: true,
      },
    },
    {
      path: "/mes/production-orders",
      name: "mes-production-orders",
      component: () => import("../views/mes/ProductionOrderManagement.vue"),
      meta: {
        title: "生产工单管理",
        icon: "ClipboardList",
        requiresAuth: true,
      },
    },
    {
      path: "/mes/scheduling-workbench",
      name: "mes-scheduling-workbench",
      component: () => import("../views/mes/ProductionSchedulingWorkbench.vue"),
      meta: {
        title: "排产规划工作台",
        icon: "Calendar",
        requiresAuth: true,
        parent: "mes",
      },
    },
    {
      path: "/mes/order-delivery-center",
      name: "mes-order-delivery-center",
      component: () => import("../views/mes/OrderDeliveryCenter.vue"),
      meta: {
        title: "订单交付管理中心",
        icon: "Truck",
        requiresAuth: true,
        parent: "mes",
      },
    },
  ],
};

// 所有模块配置
export const routeModules: RouteModule[] = [productionModule];

// 获取所有模块路由
export const getAllModuleRoutes = (): RouteRecordRaw[] => {
  return routeModules.reduce((routes, module) => {
    return [...routes, ...module.routes];
  }, [] as RouteRecordRaw[]);
};

// 根据模块名获取路由
export const getModuleRoutes = (moduleName: string): RouteRecordRaw[] => {
  const module = routeModules.find((m) => m.name === moduleName);
  return module ? module.routes : [];
};

// 获取模块信息
export const getModuleInfo = (moduleName: string): RouteModule | undefined => {
  return routeModules.find((m) => m.name === moduleName);
};
