<script setup lang="ts">
import AppLayout from './components/layout/AppLayout.vue'
import { TooltipProvider } from '@/components/ui/tooltip'
import { Toaster } from '@/components/ui/sonner'
// 根据 shadcn-vue 官方文档，vue-sonner v2 需要导入样式
import 'vue-sonner/style.css'
</script>

<template>
  <!-- TooltipProvider 应该包装整个应用，为所有 tooltip 组件提供上下文 -->
  <!-- 根据官方文档，delayDuration 设为 0 可以立即显示 tooltip -->
  <TooltipProvider :delay-duration="0" :skip-delay-duration="500">
    <AppLayout />

    <!-- Sonner Toast 通知组件，应该放在应用的根级别 -->
    <!-- 根据官方文档，添加 pointer-events-auto 类以确保与 Dialog 的兼容性 -->
    <Toaster
      class="pointer-events-auto"
      :theme="'system'"
      :position="'bottom-right'"
      :expand="true"
      :rich-colors="true"
      :close-button="true"
      :toast-options="{
        style: {
          background: 'hsl(var(--background))',
          border: '1px solid hsl(var(--border))',
          color: 'hsl(var(--foreground))'
        }
      }"
    />
  </TooltipProvider>
</template>

<style>
/* 确保 toast 通知在最顶层显示 */
.toaster {
  z-index: 9999;
}

/* 为 dialog 和 drawer 设置合适的 z-index */
[data-reka-dialog-overlay] {
  z-index: 50;
}

[data-reka-dialog-content] {
  z-index: 51;
}

[data-vaul-drawer-wrapper] {
  z-index: 50;
}

[data-vaul-overlay] {
  z-index: 50;
}

[data-vaul-drawer] {
  z-index: 51;
}

/* 确保 tooltip 在合适的层级显示 */
[data-reka-tooltip-content] {
  z-index: 60;
}
</style>
