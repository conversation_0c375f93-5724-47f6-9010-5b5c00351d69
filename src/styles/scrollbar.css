/* 自定义滚动条样式 */

/* Webkit 浏览器滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 深色模式下的滚动条 */
.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Firefox 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* 设备详情弹窗专用滚动条样式 */
.equipment-dialog-scroll {
  /* 确保滚动容器有正确的高度计算 */
  height: 0;
  min-height: 100%;
  flex-grow: 1;
}

.equipment-dialog-scroll::-webkit-scrollbar {
  width: 8px;
}

.equipment-dialog-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
  margin: 4px 0;
}

.equipment-dialog-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  border: 1px solid #f1f5f9;
}

.equipment-dialog-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 列表区域滚动条样式 */
.list-scroll-area {
  /* 为列表区域提供更细的滚动条 */
}

.list-scroll-area::-webkit-scrollbar {
  width: 4px;
}

.list-scroll-area::-webkit-scrollbar-track {
  background: transparent;
}

.list-scroll-area::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}

.list-scroll-area::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
}

/* 历史记录时间线滚动优化 */
.timeline-scroll {
  /* 为时间线提供更好的滚动体验 */
  scroll-behavior: smooth;
}

.timeline-scroll::-webkit-scrollbar {
  width: 6px;
}

.timeline-scroll::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
  margin: 8px 0;
}

.timeline-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: 3px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.timeline-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #cbd5e1 0%, #94a3b8 100%);
}

/* 响应式滚动条 */
@media (max-width: 768px) {
  .custom-scrollbar::-webkit-scrollbar,
  .equipment-dialog-scroll::-webkit-scrollbar,
  .list-scroll-area::-webkit-scrollbar {
    width: 3px;
  }
  
  .timeline-scroll::-webkit-scrollbar {
    width: 4px;
  }
}

/* 滚动条动画 */
@keyframes scrollbar-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.scroll-fade-in::-webkit-scrollbar-thumb {
  animation: scrollbar-fade-in 0.3s ease-out;
}

/* 滚动指示器 */
.scroll-indicator {
  position: relative;
}

.scroll-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 10%,
    rgba(0, 0, 0, 0.1) 90%,
    transparent 100%
  );
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.scroll-indicator:hover::after {
  opacity: 1;
}

/* 滚动阴影效果 */
.scroll-shadow {
  position: relative;
  overflow: hidden;
}

.scroll-shadow::before,
.scroll-shadow::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 8px;
  pointer-events: none;
  z-index: 1;
  transition: opacity 0.2s ease;
}

.scroll-shadow::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
  opacity: 0;
}

.scroll-shadow::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.8), transparent);
  opacity: 0;
}

.scroll-shadow.scrolled-top::before {
  opacity: 1;
}

.scroll-shadow.scrolled-bottom::after {
  opacity: 1;
}

/* 深色模式下的滚动阴影 */
.dark .scroll-shadow::before {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), transparent);
}

.dark .scroll-shadow::after {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}
