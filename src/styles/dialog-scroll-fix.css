/* 修复对话框滚动问题 */

/* 确保对话框内容正确处理高度 */
.dialog-content-scroll {
  height: 90vh;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 确保网格布局正确处理高度 */
.dialog-grid-container {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1.5fr 0.5fr;
  min-height: 0;
  overflow: hidden;
}

/* 确保每个面板都能正确滚动 */
.dialog-panel {
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.dialog-panel-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 修复订单选择器的高度问题 */
.order-selector-container {
  display: flex;
  flex-direction: column;
  min-height: 200px;
  max-height: none;
}

.order-items-list {
  flex: 1;
  overflow-y: auto;
  max-height: none;
}

/* 确保批次优化面板内容可滚动 */
.batch-optimization-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* 确保操作控制面板内容可滚动 */
.action-control-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 新增：已选订单项快速预览样式 */
.selected-items-preview {
  background: linear-gradient(90deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
}

/* 新增：已选订单项详情面板样式 */
.selected-items-details {
  background: #f8fafc;
  border-top: 2px solid #e2e8f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* 新增：响应式布局优化 */
.order-selection-responsive {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.order-selection-responsive.screen-sm {
  font-size: 0.875rem;
}

.order-selection-responsive.screen-md {
  font-size: 0.9rem;
}

.order-selection-responsive.screen-lg {
  font-size: 1rem;
}

/* 新增：性能优化的滚动 */
.optimized-scroll {
  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  /* 优化滚动性能 */
  scroll-behavior: smooth;
  overscroll-behavior: contain;
  /* 减少重绘 */
  contain: layout style paint;
}