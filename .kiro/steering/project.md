---
inclusion: always
---

# Glass ERP 原型系统开发指南

## 项目概述

这是一个**玻璃深加工企业ERP原型系统**，使用 Vue 3 + TypeScript + TailwindCSS + ShadCN 组件库构建。所有开发工作必须与玻璃行业工作流程保持一致，使用实际业务术语。

**技术栈**: Vue 3 + Vite + TypeScript + TailwindCSS + ShadCN + Pinia + Vee-validate

## 开发命令

- 开发服务器已经运行在 5173 端口，且支持热更新，不需要再重新启动
- `pnpm build` - 生产环境构建
- `pnpm type-check` - TypeScript 类型验证（必须在任务完成前执行）
- `pnpm lint` - ESLint 检查（存在已知违例，新代码需符合规范）

## 核心开发原则

### 语言要求
- **永远使用简体中文**进行思考、对话和文档编写
- 所有 .md 文档使用中文编写

### 代码架构约束

#### 文件大小限制
- Vue 组件文件: ≤ 500 行
- TypeScript 文件: ≤ 300 行
- 配置文件: ≤ 200 行
- 类型定义文件: ≤ 350 行

#### 组件设计原则
- **原子化**: 基础 UI 组件保持单一职责
- **组合式**: 业务组件通过组合原子组件构建
- **容器组件**: 页面级组件专注数据流和布局
- 使用 Composition API，避免 Options API
- 优先使用现有 ShadCN 组件

#### 模块化设计
- 按业务域分离: CRM、库存、MES、采购、质量等模块独立
- 共享服务层: API 调用、数据转换、工具函数跨模块共享
- 类型定义隔离: 每个模块维护独立的 TypeScript 接口

### 样式规范
- 使用 TailwindCSS 原子类，避免自定义样式
- 移动优先的响应式设计
- 使用统一的断点系统

### 状态管理
- 使用 Pinia 管理共享状态
- 组件只维护局部状态
- 避免状态冗余和组件间直接状态访问

## 项目结构

```
src/
├── components/ui/          # ShadCN UI 组件库
├── components/material/    # 物料管理业务组件
├── views/                  # 主要应用视图
├── types/                  # TypeScript 类型定义
├── stores/                 # Pinia 状态管理
├── utils/                  # 工具函数
└── services/               # API 服务层

public/mock/                # 模拟数据（按业务域组织）
├── metadata/               # 物料模板、分类等元数据
├── inventory/              # 库存数据
├── crm/                    # 客户数据
├── mes/                    # 生产数据
└── ...
```

## Mock 数据规范

### 核心数据模型

#### 物料变体管理（核心业务）
基于"模板 + 变体"模式，支持玻璃深加工行业特性：

```typescript
// 物料模板定义
interface MaterialTemplate {
  id: string;
  name: string;
  code: string;
  materialType: 'raw_glass' | 'profile' | 'hardware';
  baseAttributes: AttributeConfig[];
  variantAttributes: AttributeConfig[];
}

// 物料变体实例
interface MaterialVariant {
  id: string;
  templateId: string;
  sku: string;
  displayName: string;
  baseAttributeValues: AttributeValue[];
  variantAttributeValues: AttributeValue[];
  cost: number;
  isActive: boolean;
}
```

#### 数据一致性要求
- 所有外键引用必须有效
- 枚举值在元数据中预定义
- 单位统一: 长度(mm)、面积(m²)、重量(kg)
- 日期格式: ISO 8601
- 库存逻辑: `availableQuantity = quantity - reservedQuantity`

#### 玻璃行业特征数据
- 原片规格: 3300x2140, 3660x2440 等标准规格
- 厚度系列: 4mm, 5mm, 6mm, 8mm, 10mm, 12mm
- 型材截面: 50x30, 60x40, 80x50 等真实规格

## 代码质量标准

### 避免的"坏味道"
1. **组件僵化**: 使用 Props/Emit 明确接口，避免组件间强耦合
2. **状态冗余**: 共享状态使用 Pinia，避免重复定义
3. **循环依赖**: 建立清晰组件层次，使用事件总线解耦
4. **业务逻辑晦涩**: 复杂逻辑提取到 composables 或 service 层
5. **样式脆弱**: 使用 TailwindCSS 原子类，避免全局样式污染

### 重构触发条件
- 单个文件超过规定行数 20%
- 修改一个组件需要同时修改 3+ 相关组件
- 相同业务逻辑在 3+ 组件中重复
- 组件 props 超过 10 个
- 条件嵌套超过 3 层

## 开发工作流

1. **组件创建** → 遵循 ShadCN 模式，使用 Composition API
2. **视图开发** → 遵循已建立的布局结构
3. **数据处理** → 利用现有 mock 数据和工具函数
4. **类型定义** → 在 `src/types/` 中维护接口定义
5. **功能测试** → 开发服务器验证
6. **质量保证** → 执行 `pnpm type-check`

## 原型开发特点

### 快速迭代优先
- 可读性和可维护性优先
- 优先使用现有组件，减少重复开发
- 使用结构化 mock 数据

### 渐进增强
- 允许现有代码中的 `any` 类型，新代码严格类型定义
- 核心功能优先，次要功能后续迭代
- 原型阶段优先功能完整性

### 已知问题
- ESLint 存在 48 个已知违例（主要是 `any` 类型和 Vue 命名问题）
- ShadCN 组件使用单词命名，与 Vue ESLint 多词组件名规则冲突
- 初期优先关注新代码质量，而非修复现有违例
