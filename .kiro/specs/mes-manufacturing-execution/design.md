# MTO模式MES制造执行系统原型验证设计文档

## 设计概述

本设计文档专注于**原型验证设计**，目标是通过最简可行产品(MVP)验证MTO模式玻璃深加工的核心业务假设。设计遵循"简化优先、价值驱动、渐进验证"的原则，重点验证关键业务价值而非展示完整功能。

## 核心业务假设验证

### 需要验证的关键假设
1. **排版优化价值假设**：智能排版能否真正提升5-8%的原片利用率？
2. **工段调度假设**：半成品精确调度能否显著提升生产效率？
3. **交期承诺假设**：基于实时数据的交期预测能否提升客户满意度？
4. **批次混合假设**：多订单混合批次能否提高设备利用率？
5. **成本核算假设**：订单级精确成本核算能否支持精准定价？

### 验证策略
- **A/B测试设计**：对比传统方式与MTO-MES方式的效果差异
- **用户反馈收集**：通过原型收集用户对核心功能的反馈
- **数据驱动验证**：通过模拟数据验证算法和流程的可行性

## MVP原型架构设计

### 轻量化架构原则
- **最小技术栈**：基于现有项目技术栈，避免引入新的复杂性
- **Mock数据驱动**：重点验证业务逻辑，不关注技术实现细节
- **快速迭代**：支持快速修改和用户反馈收集

### 简化架构
```
┌─────────────────────────────────────────────────────────────┐
│                MTO-MES 原型验证系统                          │
├─────────────────────────────────────────────────────────────┤
│  核心验证界面 (基于现有Vue 3 + ShadCN架构)                  │
│  ├── 排版优化验证    ├── 工段调度验证    ├── 交期承诺验证     │
├─────────────────────────────────────────────────────────────┤
│  业务验证逻辑 (简化的Composables)                           │
│  ├── 排版算法模拟    ├── 调度逻辑模拟    ├── 成本计算模拟     │
├─────────────────────────────────────────────────────────────┤
│  验证数据层 (精心设计的Mock数据)                             │
│  ├── 真实业务场景    ├── 工艺约束数据    ├── 效果对比数据     │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈最小化
- **复用现有技术栈**：Vue 3 + TypeScript + ShadCN + TailwindCSS
- **简化状态管理**：使用Vue 3 Composition API，避免复杂的Pinia Store
- **专注业务验证**：重点实现业务逻辑验证，技术实现从简


## 范围与非目标（原型阶段）

为确保“验证优先、风险可控”，本原型明确以下范围与非目标：
- 原型聚焦三大验证点：排版优化、工段调度、交期承诺；其余功能仅做占位或样例化
- 不在本阶段实现：
  - 99.5%+高可用与生产级容灾（以演示与数据正确为先）
  - 真正的分布式事务与跨系统强一致（以前端校验与Mock一致性模拟为主）
  - 全量IOT实时接入与设备联控（以离线/样例数据验证业务效果）
  - 细粒度RBAC、审计合规体系（以最小可用权限模型演示）
  - 多租户/全链路集成（以接口占位与样例集成为主）
- 成功标准：能以真实场景数据清晰展示“价值差异”与“可切换/可回退”的策略能力

## 玻璃深加工工艺约束矩阵

### 关键工艺约束（验证重点）

#### 切割工艺约束
```typescript
interface CuttingConstraints {
  // 刀具约束
  bladeWidth: 3.2; // mm，切割刀具宽度
  minCutLength: 100; // mm，最小切割长度
  maxCutLength: 3300; // mm，最大切割长度（设备限制）

  // 边部余量
  edgeMargin: {
    top: 20,    // mm
    bottom: 20, // mm
    left: 15,   // mm
    right: 15   // mm
  };

  // 厚度限制
  thicknessRange: {
    min: 3,   // mm
    max: 19   // mm
  };
}
```

#### 钢化工艺约束
```typescript
interface TemperingConstraints {
  // 尺寸约束
  minSize: { length: 300, width: 300 }; // mm
  maxSize: { length: 2440, width: 3660 }; // mm，设备限制

  // 厚度约束
  thicknessRange: { min: 4, max: 19 }; // mm

  // 批次约束
  batchConstraints: {
    maxWeight: 2000;        // kg，单批次最大重量
    thicknessTolerance: 0.5; // mm，同批次厚度差
    temperatureRange: {      // 钢化温度范围
      '4mm': { min: 665, max: 675 },
      '5mm': { min: 670, max: 680 },
      '6mm': { min: 675, max: 685 },
      '8mm': { min: 680, max: 690 },
      '10mm': { min: 685, max: 695 },
      '12mm': { min: 690, max: 700 }
    };
  };

  // 变形控制
  deformationTolerance: {
    flatness: 0.3,    // mm/m，平整度
    bowAndWarp: 0.2   // mm/m，弓形和扭曲
  };
}
```

#### 中空玻璃约束
```typescript
interface InsulatedGlassConstraints {
  // 内外片约束
  glassThicknessDiff: 3; // mm，内外片厚度差限制

  // 间隔条规格
  spacerWidths: [6, 9, 12, 15, 16, 20]; // mm，标准间隔条宽度

  // 密封胶固化
  sealantCuring: {
    structuralGlaze: {
      initialCure: 24,  // 小时，初步固化
      fullCure: 168     // 小时，完全固化
    },
    secondarySeal: {
      initialCure: 2,   // 小时
      fullCure: 24      // 小时
    }
  };

  // 充气约束
  gasFilling: {
    argonConcentration: 85, // %，氩气浓度要求
    fillingTime: 45         // 分钟，充气时间
  };
}
```

### 验证用核心数据模型（简化版）

```typescript
// 验证用订单模型
interface ValidationOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  items: ValidationOrderItem[];
  requiredDate: string;
  estimatedCost: number;
  actualCost?: number; // 用于成本验证
}

// 验证用订单明细
interface ValidationOrderItem {
  id: string;
  specifications: {
    length: number;
    width: number;
    thickness: number;
    glassType: 'clear' | 'tinted' | 'low_e';
  };
  quantity: number;
  processFlow: ProcessStep[];
  currentStatus: string;
  utilizationRate?: number; // 排版利用率验证
}

// 工艺步骤
interface ProcessStep {
  stepName: string;
  workstation: string;
  estimatedDuration: number; // 分钟
  actualDuration?: number;   // 实际用时，用于效率验证
  constraints: any;          // 工艺约束参数
}
```

### 排版优化数据模型

```typescript
// 排版任务
interface CuttingTask {
  id: string;
  taskName: string;
  glassType: {
    material: string;
    thickness: number;
    color: string;
  };
  orderItems: OrderItem[];
  rawSheets: RawSheet[];
  status: 'pending' | 'optimizing' | 'completed' | 'executed';
  createdAt: string;
  optimizationResults?: OptimizationResult[];
}

// 原片规格
interface RawSheet {
  id: string;
  length: number;
  width: number;
  thickness: number;
  material: string;
  color: string;
  cost: number;
  availableQuantity: number;
}


## 渐进式算法切换策略（占位模型）

为满足需求文档Req 2.1的“验证→试用→全面切换”策略，定义最小可用的策略与回退模型：

```typescript
// 算法阶段与切换/回退策略
interface AlgorithmStrategy {
  phase: 'validation' | 'trial' | 'full'; // 当前阶段
  primary: 'third_party' | 'internal';   // 主要算法
  fallback: 'third_party' | 'internal';  // 回退算法
  switchCriteria: {                      // 切换准入阈值（占位）
    minUtilizationImprovement: number;   // 最小利用率提升（如 ≥ 3%）
    maxComputationTimeSec: number;       // 最大计算耗时阈值（如 ≤ 60s）
    stabilityScore: number;              // 稳定性评分阈值（如 ≥ 0.9）
  };
  rollbackTriggers: string[];            // 回退触发条件（如“利用率低于阈值”）
  effectiveFrom: string;                 // 阶段生效日期
}
```

UI与流程：
- 在 CuttingOptimizationDemo.vue 中展示“当前阶段/主要算法/回退算法”与“切换按钮/一键回退”
- AlgorithmComparison.vue 展示准入阈值达标情况（红/绿标识）
- 切换/回退均记录到验证日志（Mock）用于复盘

// 优化结果
interface OptimizationResult {
  id: string;
  algorithmType: 'internal' | 'third_party';
  algorithmName: string;
  utilizationRate: number;
  totalSheets: number;
  totalCost: number;
  cuttingPlans: CuttingPlan[];
  computationTime: number;
  createdAt: string;
}

// 切割方案
interface CuttingPlan {
  id: string;
  sheetId: string;
  layout: CuttingLayout;
  pieces: CuttingPiece[];
  utilizationRate: number;
  wasteArea: number;
}

// 切割布局
interface CuttingLayout {
  sheetLength: number;
  sheetWidth: number;
  pieces: {
    x: number;
    y: number;
    length: number;
    width: number;
    orderItemId: string;
    rotation: boolean;
  }[];
}
```

### 生产执行数据模型

```typescript
// 生产批次
interface ProductionBatch {
  id: string;
  batchNumber: string;
  workstation: string;
  processType: string;
  orderItems: OrderItem[];
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  plannedStartTime: string;
  plannedEndTime: string;
  actualStartTime?: string;
  actualEndTime?: string;
  operator?: string;
  equipment: string;
  processParameters: Record<string, any>;
  qualityRecords: QualityRecord[];
}

// 工段半成品
interface SemiFinishedProduct {
  id: string;
  orderItemId: string;
  currentWorkstation: string;
  specifications: ProductSpecifications;
  quantity: number;
  status: 'available' | 'reserved' | 'in_process' | 'completed' | 'defective';
  location: string;
  qualityStatus: 'pending' | 'passed' | 'failed';
  createdAt: string;
  updatedAt: string;
}

// 工段调度
interface WorkstationSchedule {
  id: string;
  workstationId: string;
  workstationName: string;
  date: string;
  shifts: WorkShift[];
  capacity: {
    maxBatches: number;
    maxPieces: number;
    maxArea: number; // m²
  };
  utilization: {
    plannedBatches: number;
    plannedPieces: number;
    plannedArea: number;
    utilizationRate: number;
  };
}

// 班次安排
interface WorkShift {
  shiftId: string;
  shiftName: string;
  startTime: string;
  endTime: string;
  batches: ProductionBatch[];
  operators: string[];
  equipment: string[];

## 异常与一致性（占位模型）

为满足需求文档Req 16的异常处理与一致性思路，新增最小事件模型与重排占位流程：

```typescript
// 异常事件
interface ExceptionEvent {
  id: string;
  type: 'breakage' | 'shortage' | 'cancellation' | 'equipment';
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedOrders: string[];
  affectedBatches: string[];
  status: 'detected' | 'processing' | 'resolved';
  detectedAt: string;
  resolvedAt?: string;
  notes?: string;
}

// 一致性模拟：简单乐观锁
interface VersionedEntity {
  id: string;
  version: number; // 每次写入+1，用于模拟并发冲突
}
```

占位流程：
- 在 WorkstationSchedulingDemo.vue 提供“模拟异常”按钮（破损/缺货/取消/设备）
- 触发后生成 ExceptionEvent，并显示“影响摘要卡片”（影响订单/批次、预计延误、建议方案）
- 提供“生成重排任务”操作：更新相应 CuttingTask/ProductionBatch 的状态并刷新交期估算
- 在Mock写入处模拟 version 校验，提示“并发冲突（请刷新或重试）”


### 质量管理数据模型

```typescript
// 质量记录
interface QualityRecord {
  id: string;
  orderItemId: string;
  batchId: string;
  processType: string;
  inspectionType: 'incoming' | 'in_process' | 'final';
  inspectionItems: InspectionItem[];
  inspector: string;
  inspectionDate: string;
  result: 'passed' | 'failed' | 'rework';
  defects?: DefectRecord[];
  notes?: string;
}

// 检验项目
interface InspectionItem {
  itemName: string;
  standard: string;
  actualValue: string;
  result: 'pass' | 'fail';
  tolerance: string;
}

// 缺陷记录
interface DefectRecord {
  defectType: string;
  severity: 'minor' | 'major' | 'critical';
  quantity: number;
  location: string;
  cause?: string;
  action: 'accept' | 'rework' | 'scrap';
}
```

## MVP验证组件设计

### 核心验证页面（3个重点）

```
src/views/mes/
├── CuttingOptimizationDemo.vue    # 排版优化价值验证
├── WorkstationSchedulingDemo.vue  # 工段调度效率验证
└── DeliveryPromiseDemo.vue        # 交期承诺准确性验证
```

### 验证组件结构（最小化）

```
src/components/mes/
├── cutting/
│   ├── OptimizationComparison.vue # 算法效果对比（核心验证）
│   ├── UtilizationRateChart.vue   # 利用率对比图表
│   └── CuttingPlanVisualizer.vue  # 排版方案可视化
├── scheduling/
│   ├── WorkstationFlowDemo.vue    # 工段流转演示
│   ├── BatchOptimizationDemo.vue  # 批次优化演示
│   └── EfficiencyMetrics.vue      # 效率指标对比
├── delivery/
│   ├── PromiseAccuracyDemo.vue    # 交期准确性演示
│   ├── CapacityCalculator.vue     # 产能计算器
│   └── DeliveryTracker.vue        # 交期跟踪器
└── shared/
    ├── ValueMetricsCard.vue       # 价值指标卡片
    ├── BeforeAfterComparison.vue  # 前后对比组件
    └── FeedbackCollector.vue      # 用户反馈收集
```

### 组件设计原则
- **单一验证目标**：每个组件专注验证一个核心假设
- **对比展示**：通过前后对比突出价值
- **交互简单**：避免复杂操作，重点展示结果
- **反馈收集**：每个验证点都要收集用户反馈

## 状态管理设计

### Pinia Store 结构

```typescript
// stores/mes/orderStore.ts
export const useOrderStore = defineStore('order', () => {
  const orders = ref<CustomerOrder[]>([]);
  const currentOrder = ref<CustomerOrder | null>(null);
  const orderFilters = ref<OrderFilters>({});

  // Actions
  const fetchOrders = async (filters?: OrderFilters) => { /* ... */ };
  const createOrder = async (orderData: CreateOrderRequest) => { /* ... */ };
  const updateOrderStatus = async (orderId: string, status: string) => { /* ... */ };
  const calculateDeliveryPromise = async (orderItems: OrderItem[]) => { /* ... */ };

  // Getters
  const filteredOrders = computed(() => { /* ... */ });
  const ordersByStatus = computed(() => { /* ... */ });
  const urgentOrders = computed(() => { /* ... */ });

  return {
    orders, currentOrder, orderFilters,
    fetchOrders, createOrder, updateOrderStatus, calculateDeliveryPromise,
    filteredOrders, ordersByStatus, urgentOrders
  };
});

// stores/mes/cuttingStore.ts
export const useCuttingStore = defineStore('cutting', () => {
  const cuttingTasks = ref<CuttingTask[]>([]);
  const optimizationResults = ref<OptimizationResult[]>([]);
  const rawSheets = ref<RawSheet[]>([]);

  // Actions
  const createCuttingTask = async (orderItems: OrderItem[]) => { /* ... */ };
  const runOptimization = async (taskId: string, algorithm: string) => { /* ... */ };
  const compareAlgorithms = async (taskId: string) => { /* ... */ };
  const exportCuttingData = async (taskId: string) => { /* ... */ };

  return { /* ... */ };
});

// stores/mes/productionStore.ts
export const useProductionStore = defineStore('production', () => {
  const batches = ref<ProductionBatch[]>([]);
  const workstations = ref<WorkstationSchedule[]>([]);
  const semiFinishedProducts = ref<SemiFinishedProduct[]>([]);

  // Actions
  const createBatch = async (batchData: CreateBatchRequest) => { /* ... */ };
  const scheduleBatch = async (batchId: string, schedule: ScheduleRequest) => { /* ... */ };
  const transferSemiFinished = async (productId: string, targetWorkstation: string) => { /* ... */ };

  return { /* ... */ };
});
```

## 价值验证界面设计

### 1. 排版优化价值验证界面
```
┌─────────────────────────────────────────────────────────────┐
│  排版优化价值验证 - 能否提升5-8%利用率？                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │价值对比展示                                              │ │
│  │传统排版: 78.5% | 智能排版: 85.2% | 提升: 6.7% ✓        │ │
│  │年节省成本: ¥125,000 | ROI: 6个月回本                    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────────────────────────┐ │
│  │真实订单数据     │ │排版方案可视化对比                    │ │
│  │□ 华润-幕墙项目  │ │传统方式    vs    智能优化            │ │
│  │□ 万科-门窗订单  │ │┌─────────┐    ┌─────────┐          │ │
│  │□ 绿地-隔断工程  │ ││ 78.5%   │    │ 85.2%   │          │ │
│  │                 │ ││利用率   │    │利用率   │          │ │
│  │[开始验证]       │ │└─────────┘    └─────────┘          │ │
│  └─────────────────┘ └─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │用户反馈收集                                              │ │
│  │这个优化效果符合您的预期吗？ [是] [否] [需要改进]        │ │
│  │您认为最重要的价值是什么？ [成本节省] [效率提升] [其他]  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 工段调度效率验证界面
```
┌─────────────────────────────────────────────────────────────┐
│  工段调度效率验证 - 半成品精确调度能否提升效率？             │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │效率提升对比                                              │ │
│  │传统调度: 设备利用率 65% | 智能调度: 75% | 提升: 10% ✓   │ │
│  │半成品周转时间: 48小时 → 24小时 | 减少: 50%               │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │工段流转可视化演示                                        │ │
│  │冷加工 ──→ 钢化 ──→ 合片 ──→ 包装                       │ │
│  │  ↓         ↓        ↓        ↓                          │ │
│  │ 25片     18片     12片      8片                         │ │
│  │(实时)   (批次中)  (待配对)  (完成)                      │ │
│  │                                                         │ │
│  │智能建议: 钢化炉#1可增加6片，提升批次效率15%             │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  │验证结果: 工段协同确实能显著提升效率 [收集更多反馈]      │ │
└─────────────────────────────────────────────────────────────┘
```

### 3. 交期承诺准确性验证界面
```
┌─────────────────────────────────────────────────────────────┐
│  交期承诺准确性验证 - 能否达到90%准确率？                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │准确率对比                                                │ │
│  │传统预测: 准确率 65% | 智能预测: 准确率 88% | 提升: 23%  │ │
│  │客户满意度: 3.2分 → 4.6分 | 投诉减少: 60%                │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │实时交期计算演示                                          │ │
│  │订单: 华润幕墙项目 (1000片中空玻璃)                      │ │
│  │                                                         │ │
│  │基于实时数据计算:                                        │ │
│  │• 当前排队: 3天                                          │ │
│  │• 生产周期: 7天                                          │ │
│  │• 质检包装: 1天                                          │ │
│  │                                                         │ │
│  │承诺交期: 2024-02-15 (11天后) 置信度: 92%               │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  │验证结论: 交期预测准确性显著提升 [查看详细分析]          │ │
└─────────────────────────────────────────────────────────────┘
```

### 界面设计原则
- **价值优先**：每个界面都突出展示业务价值
- **对比清晰**：通过前后对比突出改进效果
- **数据真实**：使用真实业务场景的模拟数据
- **反馈及时**：每个验证点都收集用户反馈
- **简单直观**：避免复杂操作，重点展示结果

## 验证导向的Mock数据设计

### 数据设计原则
- **真实业务场景**：基于实际玻璃深加工企业的业务数据
- **对比验证**：每个数据点都要有传统方式vs智能方式的对比
- **价值量化**：数据要能够量化展示业务价值
- **工艺约束体现**：数据要体现真实的工艺约束和限制

### 验证数据结构
```
public/mock/mes/validation/
├── cutting-optimization/
│   ├── real-orders.json           # 真实订单场景
│   ├── traditional-results.json   # 传统排版结果
│   ├── optimized-results.json     # 智能优化结果
│   └── value-comparison.json      # 价值对比数据
├── workstation-scheduling/
│   ├── production-scenarios.json  # 生产场景数据
│   ├── traditional-flow.json      # 传统工段流转
│   ├── optimized-flow.json        # 优化工段流转
│   └── efficiency-metrics.json    # 效率指标对比
├── delivery-promise/
│   ├── historical-orders.json     # 历史订单数据
│   ├── traditional-promises.json  # 传统交期承诺
│   ├── smart-promises.json        # 智能交期预测
│   └── accuracy-analysis.json     # 准确率分析
└── constraints/
    ├── cutting-constraints.json   # 切割工艺约束
    ├── tempering-constraints.json # 钢化工艺约束
    └── insulating-constraints.json # 中空工艺约束
```

### 关键验证数据示例

#### 排版优化对比数据
```json
// value-comparison.json
{
  "scenario": "华润置地幕墙项目",
  "orderDetails": {
    "totalPieces": 1250,
    "glassType": "6mm透明钢化",
    "rawSheetSize": "3300x2140",
    "totalRawSheets": 45
  },
  "traditionalMethod": {
    "utilizationRate": 78.5,
    "wasteRate": 21.5,
    "totalCost": 125000,
    "planningTime": 240, // 分钟
    "manualErrors": 3
  },
  "optimizedMethod": {
    "utilizationRate": 85.2,
    "wasteRate": 14.8,
    "totalCost": 117500,
    "planningTime": 15, // 分钟
    "manualErrors": 0
  },
  "valueMetrics": {
    "costSaving": 7500,
    "timeSaving": 225,
    "utilizationImprovement": 6.7,
    "annualSaving": 125000,
    "roiMonths": 6
  },
  "userFeedback": {
    "expectedImprovement": "5-8%",
    "actualImprovement": "6.7%",
    "meetExpectation": true,
    "mostValuedBenefit": "成本节省"
  }
}
```

#### 工段调度效率数据
```json
// efficiency-metrics.json
{
  "scenario": "典型生产日(8小时)",
  "workstations": ["cutting", "tempering", "insulating", "packaging"],
  "traditionalFlow": {
    "cuttingUtilization": 68,
    "temperingUtilization": 62,
    "insulatingUtilization": 45,
    "packagingUtilization": 78,
    "averageUtilization": 63.25,
    "semiFinishedTurnover": 48, // 小时
    "bottleneck": "insulating"
  },
  "optimizedFlow": {
    "cuttingUtilization": 75,
    "temperingUtilization": 78,
    "insulatingUtilization": 72,
    "packagingUtilization": 82,
    "averageUtilization": 76.75,
    "semiFinishedTurnover": 24, // 小时
    "bottleneck": "none"
  },
  "improvements": {
    "utilizationIncrease": 13.5,
    "turnoverReduction": 50,
    "throughputIncrease": 21.3,
    "bottleneckEliminated": true
  }
}
```

#### 交期承诺准确性数据
```json
// accuracy-analysis.json
{
  "analysisPeriod": "2024年1-3月",
  "totalOrders": 156,
  "traditionalMethod": {
    "accuratePromises": 101,
    "accuracyRate": 64.7,
    "averageDelay": 3.2, // 天
    "customerSatisfaction": 3.2,
    "complaints": 28
  },
  "smartMethod": {
    "accuratePromises": 137,
    "accuracyRate": 87.8,
    "averageDelay": 0.8, // 天
    "customerSatisfaction": 4.6,
    "complaints": 11
  },
  "businessImpact": {
    "accuracyImprovement": 23.1,
    "satisfactionImprovement": 43.8,
    "complaintReduction": 60.7,
    "customerRetention": 15.2 // 提升百分比
  }

## 价值监测与ROI闭环（占位模型）

为满足需求Req 18的价值导向与复盘闭环，定义统一的价值指标结构并在验证页统一展示：

```typescript
interface ValueMetric {
  name: string;            // 指标名称，如“原片利用率提升”
  target: number;          // 目标值，如 5
  actual: number;          // 实际值，如 6.7
  unit: string;            // 单位，如 “%/万元/天”
  period: string;          // 统计周期，如 “2024Q1”
  source: 'traditional'|'smart'|'comparison';
  notes?: string;          // 备注（差异原因/假设）
}
```

应用方式：
- ValueMetricsCard.vue 从 value-comparison.json / efficiency-metrics.json / accuracy-analysis.json 汇总生成 ValueMetric[]
- BeforeAfterComparison.vue 展示目标-实际-偏差，并允许记录“偏差原因/改进建议”
- FeedbackCollector.vue 绑定到具体指标，形成“数据→反馈→改进”的闭环


### 工艺约束验证数据
```json
// tempering-constraints.json
{
  "constraintValidation": {
    "thicknessGrouping": {
      "4mm": {"minTemp": 665, "maxTemp": 675, "batchSize": 120},
      "6mm": {"minTemp": 675, "maxTemp": 685, "batchSize": 100},
      "8mm": {"minTemp": 680, "maxTemp": 690, "batchSize": 80}
    },
    "batchOptimization": {
      "scenario": "混合厚度批次",
      "traditional": {
        "separateBatches": true,
        "batchCount": 8,
        "utilizationRate": 65,
        "energyConsumption": 2400 // kWh
      },
      "optimized": {
        "mixedBatches": true,
        "batchCount": 6,
        "utilizationRate": 82,
        "energyConsumption": 1950 // kWh
      },
      "savings": {
        "batchReduction": 25,
        "utilizationIncrease": 17,
        "energySaving": 18.75
      }
    }
  }
}
```

## 集成接口设计

### 与其他模块的集成

#### 1. 产品管理模块集成
```typescript
// 获取产品BOM和工艺路线
interface ProductIntegration {
  getProductBOM(productId: string): Promise<ProductBOM>;
  getProcessRoute(productType: string): Promise<ProcessRoute>;
  calculateMaterialRequirement(orderItems: OrderItem[]): Promise<MaterialRequirement[]>;
}
```

#### 2. 物料管理模块集成
```typescript
// 物料库存和消耗
interface MaterialIntegration {
  checkMaterialAvailability(requirements: MaterialRequirement[]): Promise<AvailabilityResult>;
  reserveMaterials(orderId: string, materials: MaterialReservation[]): Promise<ReservationResult>;
  consumeMaterials(batchId: string, consumption: MaterialConsumption[]): Promise<ConsumptionResult>;
}
```

#### 3. 第三方排版软件集成
```typescript
// 排版优化接口
interface CuttingOptimizationAPI {
  exportCuttingData(taskId: string): Promise<CuttingDataExport>;
  importOptimizationResult(taskId: string, result: OptimizationImport): Promise<ImportResult>;
  compareAlgorithms(taskId: string): Promise<AlgorithmComparison>;
}
```

## 技术实现要点

### 1. 响应式设计
- 使用 TailwindCSS 的响应式类
- 移动端适配的关键界面
- 触摸友好的操作设计

### 2. 性能优化
- 虚拟滚动处理大量数据
- 图片懒加载
- 组件按需加载

### 3. 数据可视化
- 使用 Chart.js 实现生产监控图表
- SVG 实现排版方案可视化
- CSS Grid 实现甘特图布局

### 4. 错误处理
- 全局错误边界
- 网络请求错误处理
- 用户友好的错误提示

### 5. 国际化支持
- 中英文切换
- 数字和日期格式化
- 业务术语标准化

这个设计文档为MTO-MES原型的开发提供了完整的技术指导，确保原型能够有效展示设计思想并验证业务需求。