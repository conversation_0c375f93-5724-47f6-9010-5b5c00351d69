# MTO模式MES制造执行系统原型需求文档

## 项目概述

本项目旨在为玻璃深加工及周边企业开发基于MTO（Make-to-Order）模式的智能制造执行系统（MES）**高保真原型**。该原型的核心目标是向用户和开发人员**展示设计思想**、**验证业务流程**、**确认系统需求**，为后续完整系统开发提供设计蓝图和需求确认。

## 原型项目特点

### 原型目标
- **设计验证**：验证MTO模式下玻璃深加工的业务流程设计
- **需求确认**：通过可交互原型确认用户需求的准确性和完整性
- **技术可行性**：验证关键技术方案的可行性
- **用户体验**：展示用户界面设计和操作流程

### 原型范围
- **核心业务流程**：重点展示订单驱动、排版优化、工段调度、批次排程等核心流程
- **关键用户界面**：提供主要功能的可交互界面原型
- **数据模型展示**：通过Mock数据展示完整的数据结构设计
- **集成接口设计**：展示与其他系统的集成接口设计思路

### 原型限制
- **非生产环境**：使用Mock数据，不连接真实的生产设备和数据库
- **功能简化**：复杂算法使用简化版本或模拟结果
- **性能要求**：重点关注功能完整性，性能优化在后续开发阶段
- **异常处理**：展示主要异常处理流程，不实现完整的容错机制

## MTO模式玻璃深加工的核心特征与量化指标

### 生产模式特点（量化定义）
- **小批量生产**：单批次产品数量 < 100片，月均处理订单 > 200个
- **多品种定制**：月均处理产品规格 > 500种，标准化程度 < 30%
- **高定制化**：客户定制化需求占比 > 80%，重复订单率 < 20%
- **原片优化**：标准原片利用率目标 ≥ 85%，余料重复利用率 ≥ 60%
- **工段协同**：冷加工→钢化→合片的多工段流水线，半成品周转时间 < 24小时

### 关键业务指标（KPI）
- **交期准确率**：±2个工作日内准确率 ≥ 90%
- **设备利用率**：钢化炉利用率 ≥ 75%，中空线利用率 ≥ 70%
- **质量指标**：一次合格率 ≥ 95%，客户投诉响应时间 < 4小时
- **成本控制**：订单成本核算准确率 ≥ 98%，成本差异控制在 ±3%以内

### 工艺约束矩阵
#### 钢化玻璃约束
- **厚度范围**：3-19mm（超出范围需特殊工艺）
- **尺寸限制**：最小300×300mm，最大根据设备规格（如2440×3660mm）
- **形状约束**：异形玻璃钢化变形率增加15-25%
- **批次约束**：同批次厚度差 ≤ 0.5mm，重量差 ≤ 20%

#### 中空玻璃约束
- **内外片厚度差**：≤ 3mm，推荐 ≤ 2mm
- **密封胶固化**：结构胶固化时间24-48小时，影响交期计算
- **充气约束**：氩气充气需专用设备，增加工序时间30-45分钟

#### 夹胶玻璃约束
- **PVB胶片匹配**：厚度规格0.38mm、0.76mm、1.14mm等标准规格
- **高温高压工艺**：温度140±5℃，压力1.2-1.4MPa，时间90-120分钟

### 高频异常场景处理
- **玻璃破损**：行业平均破损率3-5%，需要自动重排和补料机制
- **原片缺货**：供应商延期率10-15%，需要替代方案和客户沟通机制
- **订单取消**：客户取消率5-8%，需要半成品处理和成本分摊机制
- **设备故障**：月均故障率2-3次，需要应急预案和批次转移机制

### 原型演示要求
- **响应时间**：界面操作响应 < 2秒，数据加载 < 5秒
- **演示用户**：支持5-10个并发演示用户
- **数据规模**：包含100+个模拟订单，1000+个产品规格，50+个工艺路线
- **演示稳定性**：演示过程中系统稳定运行，无明显卡顿或错误

## 需求概述

### Requirement 1

**User Story:** 作为订单管理员，我希望能够基于客户订单驱动整个生产流程，以便实现真正的按订单生产（MTO）管理。

#### Acceptance Criteria

1. WHEN 接收客户订单 THEN 系统 SHALL 解析订单明细，识别每个产品的规格（长×宽×厚）、工艺要求（钢化、中空、夹胶等）、数量和交期
2. WHEN 订单技术评审 THEN 系统 SHALL 验证产品规格的可行性，检查是否超出设备加工能力范围
3. WHEN 交期承诺计算 THEN 系统 SHALL 基于当前产能、排队工单、设备状态和工艺路线预测准确交期
4. WHEN 动态BOM生成 THEN 系统 SHALL 根据订单产品规格动态计算原片需求、辅材用量和包装材料
5. WHEN 订单变更影响分析 THEN 系统 SHALL 分析订单变更对已排程生产计划的影响，提供重排方案
6. WHEN 订单优先级管理 THEN 系统 SHALL 支持VIP客户、紧急订单的优先级设置和插单处理

### Requirement 2

**User Story:** 作为排版工程师，我希望能够进行智能排版优化，以便最大化原片利用率并降低生产成本。

#### Acceptance Criteria

1. WHEN 收集待排版订单 THEN 系统 SHALL 汇总相同材质、厚度、颜色的订单明细，考虑交期紧急程度形成排版任务池
2. WHEN 数据标准化导出 THEN 系统 SHALL 支持将排版任务数据导出为标准格式（JSON/XML），供第三方优化系统使用
3. WHEN 第三方算法集成 THEN 系统 SHALL 提供API接口接收第三方优化系统的排版结果，支持多种优化算法并行验证
4. WHEN 自研算法开发 THEN 系统 SHALL 基于标准原片规格和订单尺寸实现内置的二维装箱优化算法
5. WHEN 算法效果对比 THEN 系统 SHALL 并行运行自研算法和第三方算法，对比利用率、执行时间、实际效果等指标
6. WHEN 考虑工艺约束 THEN 系统 SHALL 在排版时考虑切割刀具宽度、边部余量、钢化变形等工艺约束
7. WHEN 生成排版方案 THEN 系统 SHALL 输出可视化排版图，显示每块原片的切割路径、利用率和预计损耗
8. WHEN 余料智能管理 THEN 系统 SHALL 建立余料库，支持余料规格匹配和在后续订单中的自动推荐使用
9. WHEN 排版方案选择 THEN 系统 SHALL 支持人工选择使用自研算法或第三方算法结果，记录选择原因和效果
10. WHEN 算法切换策略 THEN 系统 SHALL 基于历史对比数据制定算法切换策略，逐步从第三方算法过渡到自研算法

### Requirement 2.1

**User Story:** 作为产品经理，我希望在原型中展示渐进式排版算法实施策略，以便向用户和开发团队说明实际项目的实施思路。

#### Acceptance Criteria

1. WHEN 原型演示第三方集成 THEN 系统 SHALL 展示数据导出接口设计，模拟与第三方排版软件的集成流程
2. WHEN 原型展示算法对比 THEN 系统 SHALL 提供算法效果对比界面，展示不同算法的利用率、执行时间等指标
3. WHEN 原型模拟切换策略 THEN 系统 SHALL 展示分阶段实施计划的界面设计和流程说明
4. WHEN 原型展示风险控制 THEN 系统 SHALL 提供算法回退机制的界面设计和操作流程
5. WHEN 原型说明实施策略 THEN 系统 SHALL 通过文档和界面说明完整的渐进式实施思路
6. WHEN 原型验证可行性 THEN 系统 SHALL 通过简化的算法实现验证技术方案的可行性
7. WHEN 原型收集反馈 THEN 系统 SHALL 提供用户反馈收集机制，用于优化实施策略

### Requirement 3

**User Story:** 作为生产调度员，我希望能够进行批次生产和混合排程，以便提高设备利用率和生产效率。

#### Acceptance Criteria

1. WHEN 钢化炉批次优化 THEN 系统 SHALL 将相同厚度（±0.5mm）、相似尺寸的多个订单产品智能组合成钢化批次，考虑炉膛尺寸和装载重量限制
2. WHEN 钢化工艺参数统一 THEN 系统 SHALL 确保同一批次内产品的钢化温度、时间参数兼容，避免工艺冲突
3. WHEN 中空线批次管理 THEN 系统 SHALL 根据中空玻璃的间隔条规格、密封胶类型、充气要求等安排中空线生产批次
4. WHEN 夹胶线批次管理 THEN 系统 SHALL 根据PVB胶片类型、厚度、夹胶工艺参数安排夹胶线生产批次
5. WHEN 批次排程优化 THEN 系统 SHALL 综合考虑设备产能、模具更换时间、工艺约束和订单交期进行批次排程
6. WHEN 紧急插单处理 THEN 系统 SHALL 评估插单对现有批次的影响，提供最小化影响的插单方案
7. WHEN 批次执行监控 THEN 系统 SHALL 实时监控批次执行进度，预警可能的延期风险
8. WHEN 批次质量追溯 THEN 系统 SHALL 建立批次号与订单明细、原片批次、工艺参数的完整追溯关系

### Requirement 4

**User Story:** 作为工艺工程师，我希望能够管理动态工艺路线，以便支持不同订单产品的定制化工艺要求。

#### Acceptance Criteria

1. WHEN 分析订单工艺 THEN 系统 SHALL 根据订单产品的规格和工艺要求动态生成工艺路线
2. WHEN 配置可选工序 THEN 系统 SHALL 支持钻孔、丝印、贴膜等可选工序的动态添加或跳过
3. WHEN 工艺参数设置 THEN 系统 SHALL 根据产品规格自动计算钢化温度、中空间隔等工艺参数
4. WHEN 工艺路线优化 THEN 系统 SHALL 基于设备产能和工艺约束优化工艺路线的执行顺序
5. WHEN 工艺变更管理 THEN 系统 SHALL 支持生产过程中的工艺变更和影响分析

### Requirement 5

**User Story:** 作为客户服务代表，我希望能够实时跟踪订单进度和交期，以便准确回复客户询问并主动通知交期变化。

#### Acceptance Criteria

1. WHEN 交期精确承诺 THEN 系统 SHALL 基于实时产能、设备状态、排队工单和历史执行数据预测准确的交期，精确到小时级别
2. WHEN 多维度进度跟踪 THEN 系统 SHALL 实时跟踪每个订单明细在排版、切割、磨边、钢化、合片等各工序的进度状态
3. WHEN 可视化进度展示 THEN 系统 SHALL 提供订单进度的甘特图、进度条等可视化展示，支持客户自助查询
4. WHEN 智能交期预警 THEN 系统 SHALL 基于实际执行进度和剩余工序预测，提前3-5天预警可能延期的订单
5. WHEN 延期影响分析 THEN 系统 SHALL 分析延期原因（设备故障、质量问题、插单影响等），提供针对性应对措施
6. WHEN 客户主动通知 THEN 系统 SHALL 支持短信、邮件、微信等多渠道自动通知客户订单状态变化
7. WHEN 交期承诺优化 THEN 系统 SHALL 基于历史交期达成率数据持续优化交期预测算法
8. WHEN 客户满意度跟踪 THEN 系统 SHALL 记录客户对交期承诺和实际交付的满意度评价

### Requirement 6

**User Story:** 作为生产经理，我希望能够进行柔性生产调度，以便应对订单变化、设备故障等各种生产异常情况。

#### Acceptance Criteria

1. WHEN 设备故障处理 THEN 系统 SHALL 自动识别故障设备影响的生产任务，提供重新排程方案
2. WHEN 紧急插单 THEN 系统 SHALL 支持紧急订单的快速插入，自动调整现有生产计划
3. WHEN 产能平衡 THEN 系统 SHALL 动态平衡各工段产能，避免生产瓶颈和积压
4. WHEN 资源优化 THEN 系统 SHALL 优化人员、设备、物料等资源配置，提高整体效率
5. WHEN 调度决策支持 THEN 系统 SHALL 提供多种调度方案对比和决策支持工具

### Requirement 7

**User Story:** 作为车间主管，我希望能够按工段管理生产执行，以便更好地组织和监控玻璃深加工的各个生产环节。

#### Acceptance Criteria

1. WHEN 冷加工工段管理 THEN 系统 SHALL 管理切割、磨边、钻孔、清洗等工序的执行和质量控制，产出半成品玻璃
2. WHEN 钢化工段调度 THEN 系统 SHALL 调度冷加工工段的半成品产出物，进行钢化工艺的二次加工
3. WHEN 合片工段调度 THEN 系统 SHALL 调度冷加工、钢化工段的半成品产出物，进行夹胶、中空等合片工艺的二次加工
4. WHEN 工段间协调 THEN 系统 SHALL 协调各工段间的半成品流转、库存管理和生产节拍匹配
5. WHEN 工段绩效分析 THEN 系统 SHALL 提供各工段的效率、质量、成本等绩效分析

### Requirement 7.1

**User Story:** 作为钢化工段调度员，我希望能够有效调度冷加工工段的半成品产出物，以便合理安排钢化炉的生产批次。

#### Acceptance Criteria

1. WHEN 钢化前半成品管理 THEN 系统 SHALL 实时跟踪冷加工工段产出的待钢化半成品库存状态和位置
2. WHEN 钢化批次匹配 THEN 系统 SHALL 根据钢化订单需求，匹配相同厚度、相似尺寸的半成品玻璃组成钢化批次
3. WHEN 钢化前质量检验 THEN 系统 SHALL 确保进入钢化工段的半成品已通过冷加工工段的质量检验
4. WHEN 钢化炉批次优化 THEN 系统 SHALL 优化钢化炉装炉方案，最大化炉次利用率和产品质量
5. WHEN 钢化参数设置 THEN 系统 SHALL 根据半成品规格自动设置钢化温度、时间等工艺参数

### Requirement 7.2

**User Story:** 作为合片工段调度员，我希望能够有效调度前序工段的半成品产出物，以便合理安排合片工艺的生产计划。

#### Acceptance Criteria

1. WHEN 半成品库存管理 THEN 系统 SHALL 实时跟踪冷加工、钢化工段产出的半成品库存状态和位置
2. WHEN 合片需求匹配 THEN 系统 SHALL 根据合片订单需求，匹配相应规格的半成品玻璃进行配对
3. WHEN 半成品质量检验 THEN 系统 SHALL 确保进入合片工段的半成品已通过前序工段的质量检验
4. WHEN 合片批次优化 THEN 系统 SHALL 优化合片批次，提高中空线、夹胶线的设备利用率
5. WHEN 半成品追溯 THEN 系统 SHALL 建立半成品从原片到合片成品的完整追溯链

### Requirement 8

**User Story:** 作为物料管理员，我希望能够管理工段间的半成品流转，以便确保合片工段能够及时获得所需的半成品物料。

#### Acceptance Criteria

1. WHEN 半成品入库 THEN 系统 SHALL 记录冷加工、钢化工段产出的半成品入库信息，包含规格、数量、质量状态、存储位置
2. WHEN 半成品出库 THEN 系统 SHALL 支持合片工段的半成品领料，确保先进先出和质量匹配
3. WHEN 半成品配对 THEN 系统 SHALL 为中空玻璃自动配对内外片，为夹胶玻璃配对相应规格的玻璃片
4. WHEN 半成品损耗管理 THEN 系统 SHALL 记录半成品在存储和转运过程中的损耗，更新可用库存
5. WHEN 半成品追溯 THEN 系统 SHALL 建立半成品与原始订单、原片批次的完整追溯关系

### Requirement 9

**User Story:** 作为设备管理员，我希望能够通过IOT技术实现智能化设备管理，以便提高设备利用率和预测性维护能力。

#### Acceptance Criteria

1. WHEN IOT数据采集 THEN 系统 SHALL 通过传感器实时采集钢化炉温度、中空线压力、切割机转速等设备参数
2. WHEN 设备状态监控 THEN 系统 SHALL 实时显示设备运行状态，支持远程监控和控制
3. WHEN 预测性维护 THEN 系统 SHALL 基于设备运行数据预测维护需求，提前安排维护计划
4. WHEN 设备OEE分析 THEN 系统 SHALL 计算设备整体效率，识别生产瓶颈和改进机会
5. WHEN 能耗管理 THEN 系统 SHALL 监控设备能耗，支持节能优化和成本控制

### Requirement 10

**User Story:** 作为成本会计，我希望能够进行精确的订单成本核算和追溯，以便准确评估每个订单的盈利能力。

#### Acceptance Criteria

1. WHEN 实时成本采集 THEN 系统 SHALL 实时采集每个订单的物料消耗、人工工时、设备折旧等成本数据
2. WHEN 成本分摊计算 THEN 系统 SHALL 将共享资源成本按合理方式分摊到具体订单和产品
3. WHEN 成本差异分析 THEN 系统 SHALL 对比标准成本与实际成本，分析差异原因
4. WHEN 盈利能力分析 THEN 系统 SHALL 计算每个订单的毛利率和净利率，支持盈利能力评估
5. WHEN 成本追溯 THEN 系统 SHALL 提供完整的成本追溯链，从原材料到最终产品

### Requirement 11

**User Story:** 作为质量工程师，我希望能够实现全程质量管理和追溯，以便确保产品质量并快速定位质量问题。

#### Acceptance Criteria

1. WHEN 工序质量检验 THEN 系统 SHALL 在关键工序节点进行质量检验，记录检验数据和结果
2. WHEN 不合格品处理 THEN 系统 SHALL 支持不合格品的标识、隔离、返工或报废处理
3. WHEN 质量追溯 THEN 系统 SHALL 建立从原材料到成品的完整质量追溯链
4. WHEN 质量统计分析 THEN 系统 SHALL 提供质量统计报表，分析质量趋势和改进机会
5. WHEN 客户投诉处理 THEN 系统 SHALL 支持客户投诉的快速追溯和根因分析

### Requirement 12

**User Story:** 作为开发工程师，我希望有清晰的MTO-MES数据模型设计，以便支持订单驱动的生产执行功能开发。

#### Acceptance Criteria

1. WHEN 设计订单数据模型 THEN 系统 SHALL 定义CustomerOrder、OrderItem、CuttingPlan等MTO核心数据结构
2. WHEN 设计生产数据模型 THEN 系统 SHALL 定义ProductionBatch、WorkOrder、ProcessExecution等生产执行数据结构
3. WHEN 设计追溯数据模型 THEN 系统 SHALL 建立订单、批次、工单、工序的完整追溯关系
4. WHEN 设计IOT数据模型 THEN 系统 SHALL 定义设备数据、传感器数据、实时监控数据的结构
5. WHEN 数据关联设计 THEN 系统 SHALL 确保MTO业务流程中各环节数据的完整关联

## 支撑系统数据要求

### Requirement 13

**User Story:** 作为系统架构师，我希望明确MTO-MES系统对支撑系统的数据要求，以便指导其他系统提供相应的数据支持能力。

#### Acceptance Criteria

1. WHEN 需要工序数据支撑 THEN 支撑系统 SHALL 提供标准工序库，包含工序编码、名称、类型、标准工时、质检要求等
2. WHEN 需要工艺路线支撑 THEN 支撑系统 SHALL 提供工艺路线模板，包含工序序列、工艺参数、设备要求、质量标准等
3. WHEN 需要设备数据支撑 THEN 支撑系统 SHALL 提供设备主数据，包含设备编码、规格、产能、工艺能力、维护计划等
4. WHEN 需要订单数据支撑 THEN 支撑系统 SHALL 提供客户订单数据，包含订单明细、产品规格、交期要求、质量标准等
5. WHEN 需要BOM数据支撑 THEN 支撑系统 SHALL 提供产品BOM数据，包含物料清单、用量、替代料、工艺要求等

### Requirement 14

**User Story:** 作为数据管理员，我希望明确MTO-MES系统对基础数据的具体要求，以便建立完整的数据支撑体系。

#### Acceptance Criteria

1. WHEN 建立物料主数据 THEN 支撑系统 SHALL 提供完整的物料信息，包含物料编码、规格、单位、成本、供应商、库存等
2. WHEN 建立客户主数据 THEN 支撑系统 SHALL 提供客户信息，包含客户编码、名称、地址、联系方式、信用等级、质量要求等
3. WHEN 建立人员主数据 THEN 支撑系统 SHALL 提供人员信息，包含员工编码、姓名、技能等级、工作中心、班组等
4. WHEN 建立质量标准数据 THEN 支撑系统 SHALL 提供质检标准，包含检验项目、检验方法、合格标准、抽检比例等
5. WHEN 建立成本数据 THEN 支撑系统 SHALL 提供成本基础数据，包含人工费率、设备折旧、能耗标准、间接费用等

### Requirement 15

**User Story:** 作为系统集成工程师，我希望MTO-MES模块能够与其他业务模块和IOT设备良好集成，以便实现端到端的智能制造流程。

#### Acceptance Criteria

1. WHEN 集成订单管理系统 THEN 系统 SHALL 从CRM/销售模块实时接收客户订单变更，支持订单状态双向同步
2. WHEN 集成产品管理系统 THEN 系统 SHALL 获取产品配置、BOM、工艺参数，支持产品变更的影响分析
3. WHEN 集成物料管理系统 THEN 系统 SHALL 与库存模块交互，进行物料需求计算、库存预留和消耗更新
4. WHEN 集成设备管理系统 THEN 系统 SHALL 获取设备状态、维护计划，支持设备故障对生产计划的影响分析
5. WHEN 集成质量管理系统 THEN 系统 SHALL 向质量模块提供检验数据，接收质量标准和不合格品处理指令

### Requirement 16

**User Story:** 作为系统可靠性工程师，我希望建立完善的异常处理和数据一致性机制，以便确保系统在各种异常情况下的稳定运行。

#### Acceptance Criteria

1. WHEN 玻璃破损处理 THEN 系统 SHALL 自动识别破损产品，触发重排机制，更新物料需求和交期预测
2. WHEN 原片缺货应对 THEN 系统 SHALL 提供替代原片方案，自动通知客户交期变化，调整生产计划
3. WHEN 客户订单取消 THEN 系统 SHALL 评估半成品处理方案（转库存、改制其他订单、报废），计算损失成本
4. WHEN 设备故障处理 THEN 系统 SHALL 启动应急预案，自动将受影响批次转移到备用设备或重新排程
5. WHEN 质量异常处理 THEN 系统 SHALL 隔离不合格品，启动根因分析，通知相关责任人和客户
6. WHEN 数据一致性保证 THEN 系统 SHALL 使用分布式事务确保订单变更时生产计划、库存、成本的一致性更新
7. WHEN 系统故障恢复 THEN 系统 SHALL 提供数据备份恢复机制，确保关键业务数据不丢失

### Requirement 17

**User Story:** 作为数据架构师，我希望建立MTO-MES系统的数据接口标准和性能监控，以便确保数据交换的准确性和系统性能。

#### Acceptance Criteria

1. WHEN 定义数据接口标准 THEN 系统 SHALL 制定统一的数据交换格式、字段定义、编码规则和传输协议
2. WHEN 建立数据同步机制 THEN 系统 SHALL 支持实时数据同步、批量数据导入、增量数据更新等多种同步方式
3. WHEN 确保数据一致性 THEN 系统 SHALL 建立数据校验机制，确保跨系统数据的完整性和一致性
4. WHEN 性能监控 THEN 系统 SHALL 监控关键操作响应时间，当超过阈值时自动告警和优化
5. WHEN 并发控制 THEN 系统 SHALL 支持50个并发用户操作，使用乐观锁和悲观锁机制防止数据冲突
6. WHEN 数据追溯 THEN 系统 SHALL 记录数据变更历史，支持数据来源追溯和变更审计
7. WHEN 系统扩展性 THEN 系统 SHALL 采用微服务架构，支持模块化部署和水平扩展

## 价值导向的功能优先级

### Requirement 18

**User Story:** 作为项目经理，我希望明确各功能模块的价值贡献和ROI，以便合理安排开发优先级和资源投入。

#### Acceptance Criteria

1. WHEN 智能排版优化 THEN 系统 SHALL 实现原片利用率提升5-8%，预期6个月内收回投资成本
2. WHEN 实时交期管理 THEN 系统 SHALL 提升客户满意度20%，减少客户投诉60%，预期3个月见效
3. WHEN 批次混合排程 THEN 系统 SHALL 提高设备利用率10%，降低单位产品能耗15%，预期9个月回本
4. WHEN 质量追溯管理 THEN 系统 SHALL 减少质量成本30%，缩短客户投诉处理时间70%，预期12个月回本
5. WHEN 成本精确核算 THEN 系统 SHALL 提高成本核算准确率到98%，支持精准定价和盈利分析
6. WHEN IOT设备集成 THEN 系统 SHALL 减少人工数据录入80%，提高数据准确性95%以上
7. WHEN 功能价值评估 THEN 系统 SHALL 建立功能使用率监控，定期评估功能价值贡献和优化方向