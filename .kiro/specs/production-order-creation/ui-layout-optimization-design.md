# 订单项选择面板界面布局优化设计文档

## 概述

基于用户体验优先的原则，重新设计订单项选择面板的布局架构，解决已选订单项区域遮盖订单列表的问题。采用渐进式信息展示和智能空间分配策略，确保订单列表始终保持最佳的可见性和操作性。

## 架构设计

### 新布局架构

```
┌─────────────────────────────────────────────────────────────┐
│                    订单项选择面板                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              搜索和筛选区域 (固定顶部)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            已选订单项快速预览条 (条件显示)                │ │ ← 新增
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │              订单列表区域 (主要空间)                     │ │ ← 优化
│  │                                                         │ │
│  │                                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │          已选订单项详情面板 (可折叠)                     │ │ ← 重新设计
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 空间分配策略

```typescript
interface LayoutSpaceAllocation {
  // 固定区域
  searchFilter: '60px'        // 搜索筛选区域
  quickPreview: '40px'        // 快速预览条(条件显示)
  
  // 动态区域
  orderList: 'flex-1'         // 订单列表(占用剩余主要空间)
  detailsPanel: 'max-320px'   // 详情面板(最大高度限制)
  
  // 响应式断点
  breakpoints: {
    sm: '< 768px'   // 小屏幕: 垂直堆叠
    md: '768-1024px' // 中屏幕: 紧凑布局
    lg: '> 1024px'   // 大屏幕: 完整布局
  }
}
```

## 组件设计

### 1. 快速预览条组件

```vue
<template>
  <div 
    v-if="selectedOrderItems.length > 0" 
    class="selected-items-preview px-4 py-2 border-b flex-shrink-0 transition-all duration-300"
  >
    <div class="flex items-center justify-between">
      <!-- 状态指示器 -->
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <Badge variant="default" class="text-xs font-medium">
            {{ selectedOrderItems.length }}项
          </Badge>
        </div>
        
        <div class="flex items-center gap-4 text-sm">
          <span class="text-blue-700 font-medium">
            {{ totalSelectedQuantity }}片
          </span>
          <span class="text-blue-600">
            {{ uniqueCustomers }}个客户
          </span>
          <span v-if="hasProcessConflicts" class="text-amber-600 flex items-center gap-1">
            <AlertTriangle class="w-3 h-3" />
            有冲突
          </span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex items-center gap-2">
        <Button 
          size="sm" 
          variant="ghost" 
          @click="toggleDetailsPanel"
          class="text-xs h-7 px-2"
        >
          {{ showDetails ? '收起' : '详情' }}
          <ChevronDown 
            class="w-3 h-3 ml-1 transition-transform duration-200" 
            :class="{ 'rotate-180': showDetails }" 
          />
        </Button>
        
        <Button 
          size="sm" 
          variant="ghost" 
          @click="clearAllSelections"
          class="text-xs h-7 px-2 text-red-600 hover:text-red-700"
        >
          <X class="w-3 h-3 mr-1" />
          清空
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.selected-items-preview {
  background: linear-gradient(90deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
```

### 2. 优化的订单列表区域

```vue
<template>
  <div class="order-list-container flex-1 min-h-0 overflow-hidden">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center h-32">
      <div class="flex items-center gap-2 text-gray-500">
        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
        <span class="text-sm">加载订单数据...</span>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="filteredOrders.length === 0" class="flex flex-col items-center justify-center h-32 text-gray-500">
      <Package class="w-8 h-8 mb-2 text-gray-400" />
      <span class="text-sm">暂无可用订单</span>
    </div>
    
    <!-- 订单列表 -->
    <div v-else class="order-list-scroll h-full overflow-y-auto">
      <div class="space-y-3 p-3">
        <OrderItemSelector
          v-for="order in filteredOrders" 
          :key="order.id"
          :order="order"
          :selected-order-items="selectedOrderItems"
          :conflicting-items="conflictingItems"
          :unavailable-items="unavailableItems"
          @order-item-selected="handleOrderItemSelected"
          @order-item-removed="handleOrderItemRemoved"
          @quantity-changed="handleQuantityChanged"
          @batch-optimization-requested="handleBatchOptimizationRequested"
          class="order-item-card"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.order-list-container {
  /* 确保订单列表占用主要空间 */
  flex: 1;
  min-height: 0; /* 允许flex子元素收缩 */
}

.order-list-scroll {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.order-list-scroll::-webkit-scrollbar {
  width: 6px;
}

.order-list-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.order-list-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.order-list-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.order-item-card {
  transition: all 0.2s ease;
}

.order-item-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
```

### 3. 可折叠详情面板

```vue
<template>
  <div 
    v-if="selectedOrderItems.length > 0 && showDetails" 
    class="selected-details-panel flex-shrink-0 overflow-hidden transition-all duration-300"
    :class="detailsPanelClasses"
  >
    <!-- 面板头部 -->
    <div class="details-header px-4 py-3 border-b bg-gray-50">
      <div class="flex items-center justify-between">
        <h4 class="font-medium text-sm text-gray-800">已选订单项详情</h4>
        <div class="flex items-center gap-2">
          <Badge variant="outline" class="text-xs">
            {{ selectedOrderItems.length }}项
          </Badge>
          <Button 
            size="sm" 
            variant="ghost" 
            @click="$emit('toggle-details')"
            class="h-6 w-6 p-0"
          >
            <X class="w-3 h-3" />
          </Button>
        </div>
      </div>
    </div>
    
    <!-- 详情内容 -->
    <div class="details-content flex-1 overflow-hidden flex flex-col">
      <!-- 已选项列表 -->
      <div class="selected-items-list p-4 border-b">
        <div class="space-y-2 max-h-32 overflow-y-auto">
          <div 
            v-for="item in selectedOrderItems" 
            :key="item.id"
            class="selected-item-card flex items-center justify-between p-3 bg-white rounded-lg border hover:shadow-sm transition-shadow"
          >
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-1">
                <span class="font-medium text-sm text-gray-900">{{ item.orderNumber }}</span>
                <Badge variant="secondary" class="text-xs">{{ item.customerName }}</Badge>
              </div>
              <div class="text-xs text-gray-600 mb-1">
                {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
              </div>
              <div class="flex items-center gap-3 text-xs">
                <span class="text-blue-600 font-medium">{{ item.selectedQuantity }}片</span>
                <span class="text-gray-500">{{ formatDate(item.deliveryDate) }}</span>
              </div>
            </div>
            
            <div class="flex items-center gap-2 ml-3">
              <!-- 快速数量调整 -->
              <div class="flex items-center gap-1">
                <Button 
                  size="sm" 
                  variant="outline" 
                  class="h-6 w-6 p-0"
                  @click="adjustQuantity(item.id, -1)"
                  :disabled="item.selectedQuantity <= 1"
                >
                  <Minus class="w-3 h-3" />
                </Button>
                <span class="text-xs w-8 text-center">{{ item.selectedQuantity }}</span>
                <Button 
                  size="sm" 
                  variant="outline" 
                  class="h-6 w-6 p-0"
                  @click="adjustQuantity(item.id, 1)"
                  :disabled="item.selectedQuantity >= item.totalQuantity"
                >
                  <Plus class="w-3 h-3" />
                </Button>
              </div>
              
              <!-- 移除按钮 -->
              <Button 
                size="sm" 
                variant="ghost" 
                class="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                @click="removeOrderItem(item.id)"
              >
                <X class="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 工艺兼容性检查 -->
      <div class="compatibility-check flex-1 overflow-y-auto p-4">
        <ProcessCompatibilityChecker
          :selected-order-items="selectedOrderItems"
          :auto-check="true"
          @conflict-resolved="handleConflictResolved"
          @conflict-ignored="handleConflictIgnored"
          @grouping-applied="handleGroupingApplied"
          @compatibility-changed="handleCompatibilityChanged"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.selected-details-panel {
  max-height: 320px;
  background: #f8fafc;
  border-top: 2px solid #e2e8f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.details-header {
  flex-shrink: 0;
}

.details-content {
  min-height: 0;
}

.selected-items-list {
  flex-shrink: 0;
}

.compatibility-check {
  min-height: 120px;
}

.selected-item-card {
  transition: all 0.2s ease;
}

.selected-item-card:hover {
  border-color: #3b82f6;
}

/* 响应式调整 */
@media (max-height: 600px) {
  .selected-details-panel {
    max-height: 240px;
  }
  
  .selected-items-list .space-y-2 {
    max-height: 80px;
  }
  
  .compatibility-check {
    min-height: 80px;
  }
}
</style>
```

## 数据模型设计

### 布局状态管理

```typescript
interface LayoutState {
  // 显示状态
  showDetailsPanel: boolean
  detailsPanelHeight: number
  quickPreviewVisible: boolean
  
  // 空间分配
  orderListHeight: number
  availableHeight: number
  
  // 响应式状态
  screenSize: 'sm' | 'md' | 'lg'
  isCompactMode: boolean
  
  // 动画状态
  isTransitioning: boolean
  transitionDuration: number
}

interface SelectionSummary {
  totalItems: number
  totalQuantity: number
  uniqueCustomers: number
  hasConflicts: boolean
  estimatedValue: number
  earliestDelivery: string
  latestDelivery: string
}

interface LayoutConfiguration {
  // 空间限制
  maxDetailsPanelHeight: number
  minOrderListHeight: number
  quickPreviewHeight: number
  
  // 响应式断点
  breakpoints: {
    sm: number
    md: number
    lg: number
  }
  
  // 动画配置
  transitionDuration: number
  easeFunction: string
}
```

### 智能布局算法

```typescript
class SmartLayoutManager {
  private config: LayoutConfiguration
  private state: LayoutState
  
  constructor(config: LayoutConfiguration) {
    this.config = config
    this.state = this.initializeState()
  }
  
  // 计算最优空间分配
  calculateOptimalLayout(
    containerHeight: number,
    selectedItemsCount: number,
    hasConflicts: boolean
  ): LayoutAllocation {
    const fixedHeight = this.calculateFixedHeight()
    const availableHeight = containerHeight - fixedHeight
    
    // 根据选中项数量和冲突情况调整详情面板高度
    const detailsPanelHeight = this.calculateDetailsPanelHeight(
      selectedItemsCount,
      hasConflicts,
      availableHeight
    )
    
    const orderListHeight = availableHeight - detailsPanelHeight
    
    return {
      orderListHeight,
      detailsPanelHeight,
      quickPreviewHeight: selectedItemsCount > 0 ? this.config.quickPreviewHeight : 0
    }
  }
  
  // 响应式布局调整
  adjustForScreenSize(screenWidth: number, screenHeight: number): void {
    const screenSize = this.determineScreenSize(screenWidth)
    const isCompactMode = screenHeight < 600
    
    this.state.screenSize = screenSize
    this.state.isCompactMode = isCompactMode
    
    // 根据屏幕尺寸调整配置
    if (isCompactMode) {
      this.config.maxDetailsPanelHeight = Math.min(
        this.config.maxDetailsPanelHeight,
        screenHeight * 0.4
      )
    }
  }
  
  // 平滑过渡动画
  animateLayoutChange(
    fromLayout: LayoutAllocation,
    toLayout: LayoutAllocation
  ): Promise<void> {
    return new Promise((resolve) => {
      this.state.isTransitioning = true
      
      // 使用CSS动画或JavaScript动画库
      setTimeout(() => {
        this.state.isTransitioning = false
        resolve()
      }, this.config.transitionDuration)
    })
  }
  
  private calculateFixedHeight(): number {
    return 60 + // 搜索筛选区域
           (this.state.quickPreviewVisible ? this.config.quickPreviewHeight : 0)
  }
  
  private calculateDetailsPanelHeight(
    itemsCount: number,
    hasConflicts: boolean,
    availableHeight: number
  ): number {
    if (itemsCount === 0 || !this.state.showDetailsPanel) {
      return 0
    }
    
    // 基础高度计算
    let baseHeight = 120 // 最小高度
    baseHeight += Math.min(itemsCount * 60, 180) // 每项60px，最多3项显示
    
    // 如果有冲突，增加兼容性检查区域高度
    if (hasConflicts) {
      baseHeight += 100
    }
    
    // 限制最大高度
    const maxHeight = Math.min(
      this.config.maxDetailsPanelHeight,
      availableHeight * 0.4 // 不超过可用高度的40%
    )
    
    return Math.min(baseHeight, maxHeight)
  }
  
  private determineScreenSize(width: number): 'sm' | 'md' | 'lg' {
    if (width < this.config.breakpoints.sm) return 'sm'
    if (width < this.config.breakpoints.md) return 'md'
    return 'lg'
  }
}
```

## 交互流程设计

### 布局响应流程

```mermaid
graph TD
    A[用户选择订单项] --> B[更新选择状态]
    B --> C[计算布局需求]
    C --> D[检查空间约束]
    D --> E[生成布局方案]
    E --> F[执行平滑过渡]
    F --> G[更新界面布局]
    
    H[屏幕尺寸变化] --> I[重新计算断点]
    I --> J[调整布局配置]
    J --> E
    
    K[用户展开/收起详情] --> L[切换显示状态]
    L --> C
```

### 空间分配决策树

```mermaid
graph TD
    A[开始布局计算] --> B{有选中项?}
    B -->|否| C[订单列表占满空间]
    B -->|是| D{选中项 ≤ 3?}
    D -->|是| E[显示紧凑预览]
    D -->|否| F[显示汇总预览]
    E --> G{用户展开详情?}
    F --> G
    G -->|否| H[保持当前布局]
    G -->|是| I{有工艺冲突?}
    I -->|否| J[标准详情面板]
    I -->|是| K[扩展详情面板]
    J --> L[应用布局]
    K --> L
    H --> L
    C --> L
```

## 性能优化策略

### 渲染优化

```typescript
// 虚拟滚动优化
interface VirtualScrollConfig {
  itemHeight: number
  bufferSize: number
  threshold: number
}

class VirtualScrollManager {
  private config: VirtualScrollConfig
  private visibleRange: { start: number; end: number }
  
  calculateVisibleItems(
    scrollTop: number,
    containerHeight: number,
    totalItems: number
  ): { start: number; end: number; items: any[] } {
    const start = Math.floor(scrollTop / this.config.itemHeight)
    const visibleCount = Math.ceil(containerHeight / this.config.itemHeight)
    const end = Math.min(start + visibleCount + this.config.bufferSize, totalItems)
    
    return {
      start: Math.max(0, start - this.config.bufferSize),
      end,
      items: this.getItemsInRange(start, end)
    }
  }
}

// 布局计算缓存
class LayoutCache {
  private cache = new Map<string, LayoutAllocation>()
  
  getCachedLayout(key: string): LayoutAllocation | null {
    return this.cache.get(key) || null
  }
  
  setCachedLayout(key: string, layout: LayoutAllocation): void {
    this.cache.set(key, layout)
  }
  
  generateCacheKey(
    containerHeight: number,
    selectedCount: number,
    hasConflicts: boolean,
    screenSize: string
  ): string {
    return `${containerHeight}-${selectedCount}-${hasConflicts}-${screenSize}`
  }
}
```

### 动画性能优化

```css
/* 使用GPU加速的CSS动画 */
.layout-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

.order-list-container {
  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 减少重绘的优化 */
.selected-details-panel {
  contain: layout style paint;
}

/* 滚动性能优化 */
.order-list-scroll {
  /* 使用合成层 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}
```

## 响应式设计

### 断点策略

```typescript
interface ResponsiveBreakpoints {
  sm: {
    maxWidth: 768
    layout: 'vertical-stack'
    detailsPanel: 'bottom-sheet'
    quickPreview: 'compact'
  }
  md: {
    maxWidth: 1024
    layout: 'compact-vertical'
    detailsPanel: 'collapsible'
    quickPreview: 'standard'
  }
  lg: {
    minWidth: 1024
    layout: 'full-featured'
    detailsPanel: 'side-panel' | 'bottom-panel'
    quickPreview: 'enhanced'
  }
}
```

### 移动端适配

```vue
<template>
  <div class="order-selection-panel" :class="responsiveClasses">
    <!-- 移动端：底部抽屉式详情面板 -->
    <div v-if="isMobile && showDetails" class="mobile-details-drawer">
      <div class="drawer-handle" @click="toggleDetails">
        <div class="handle-bar"></div>
      </div>
      <div class="drawer-content">
        <!-- 详情内容 -->
      </div>
    </div>
    
    <!-- 桌面端：标准布局 -->
    <div v-else class="desktop-layout">
      <!-- 标准布局内容 -->
    </div>
  </div>
</template>

<style scoped>
.mobile-details-drawer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(calc(100% - 60px));
  transition: transform 0.3s ease;
}

.mobile-details-drawer.expanded {
  transform: translateY(0);
}

.drawer-handle {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.handle-bar {
  width: 40px;
  height: 4px;
  background: #d1d5db;
  border-radius: 2px;
}
</style>
```

## 测试策略

### 布局测试用例

```typescript
describe('OrderSelectionPanel Layout', () => {
  describe('空间分配', () => {
    it('应该在没有选中项时让订单列表占用全部空间', () => {
      // 测试逻辑
    })
    
    it('应该在有选中项时显示快速预览条', () => {
      // 测试逻辑
    })
    
    it('应该限制详情面板的最大高度', () => {
      // 测试逻辑
    })
  })
  
  describe('响应式行为', () => {
    it('应该在小屏幕上采用垂直堆叠布局', () => {
      // 测试逻辑
    })
    
    it('应该在移动端使用底部抽屉', () => {
      // 测试逻辑
    })
  })
  
  describe('动画性能', () => {
    it('布局变化应该在100ms内完成', () => {
      // 性能测试
    })
    
    it('滚动应该保持60fps', () => {
      // 性能测试
    })
  })
})
```

### 用户体验测试

```typescript
describe('用户体验测试', () => {
  it('用户应该能够在选择订单项后继续浏览列表', () => {
    // 模拟用户操作
    // 验证订单列表可见性
  })
  
  it('用户应该能够快速了解选择状态', () => {
    // 验证状态指示器
  })
  
  it('用户应该能够便捷地管理已选项', () => {
    // 验证操作便捷性
  })
})
```

## 实施计划

### 阶段1：核心布局重构
- 实现新的空间分配算法
- 创建快速预览条组件
- 优化订单列表容器

### 阶段2：详情面板重设计
- 实现可折叠详情面板
- 添加平滑过渡动画
- 集成工艺兼容性检查

### 阶段3：响应式优化
- 实现断点适配
- 添加移动端支持
- 性能优化和测试

### 阶段4：用户体验完善
- 添加智能提示
- 优化交互反馈
- 完善错误处理

这个设计方案彻底解决了已选订单项区域遮盖订单列表的问题，通过智能空间分配和渐进式信息展示，确保用户能够高效地选择和管理订单项。