# 生产工单创建两步式工作流设计文档

## 概述

基于用户体验优化的原则，将生产工单创建流程重构为两步式工作流。通过分离关注点，让用户在每个阶段专注于核心任务，提升操作效率和决策质量。

## 架构设计

### 整体流程架构

```
┌─────────────────────────────────────────────────────────────┐
│                    两步式工作流架构                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                 │
│  │   第一步         │    │   第二步         │                 │
│  │  订单项选择      │ ──▶│  批次优化配置    │                 │
│  │                 │    │                 │                 │
│  │ • 搜索筛选      │    │ • 批次分析      │                 │
│  │ • 订单浏览      │    │ • 优化方案      │                 │
│  │ • 项目选择      │    │ • 参数配置      │                 │
│  │ • 数量调整      │    │ • 工单创建      │                 │
│  └─────────────────┘    └─────────────────┘                 │
└─────────────────────────────────────────────────────────────┘
```

### 组件层次结构

```
ProductionOrderCreationDialog (主容器)
├── StepIndicator (步骤指示器)
├── StepOneOrderSelection (第一步组件)
│   ├── OrderItemSearchFilter
│   ├── OrderItemSelector
│   └── SelectedItemsSummary
├── StepTwoBatchOptimization (第二步组件)
│   ├── SelectedItemsOverview
│   ├── BatchOptimizationPanel
│   ├── ProcessCompatibilityChecker
│   └── WorkOrderConfiguration
└── StepNavigation (步骤导航)
```

## 界面设计

### 步骤指示器设计

```vue
<template>
  <div class="step-indicator">
    <div class="flex items-center justify-center mb-6">
      <!-- 第一步 -->
      <div class="step-item" :class="{ active: currentStep === 1, completed: currentStep > 1 }">
        <div class="step-circle">
          <CheckCircle v-if="currentStep > 1" class="w-5 h-5" />
          <span v-else>1</span>
        </div>
        <div class="step-label">选择订单项</div>
      </div>
      
      <!-- 连接线 -->
      <div class="step-connector" :class="{ active: currentStep > 1 }"></div>
      
      <!-- 第二步 -->
      <div class="step-item" :class="{ active: currentStep === 2 }">
        <div class="step-circle">2</div>
        <div class="step-label">优化批次配置</div>
      </div>
    </div>
  </div>
</template>
```

### 第一步：订单项选择界面

```vue
<template>
  <div class="step-one-container h-full flex flex-col">
    <!-- 步骤标题 -->
    <div class="step-header p-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
      <h2 class="text-lg font-semibold text-gray-800">第一步：选择订单项</h2>
      <p class="text-sm text-gray-600 mt-1">从可用订单中选择需要生产的订单项</p>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="search-filter-section p-4 border-b bg-white">
      <OrderItemSearchFilter
        :search-query="searchQuery"
        :status-filter="statusFilter"
        :process-type-filter="processTypeFilter"
        :customer-filter="customerFilter"
        :available-orders="availableOrders"
        :filtered-count="filteredOrders.length"
        :total-count="availableOrders.length"
        @search-changed="handleSearchChanged"
        @filter-changed="handleFilterChanged"
      />
    </div>
    
    <!-- 主要内容区域 -->
    <div class="flex-1 flex min-h-0">
      <!-- 订单列表 (70%) -->
      <div class="flex-1 order-list-section">
        <div class="h-full overflow-y-auto p-4">
          <div v-if="loading" class="flex items-center justify-center h-32">
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <span class="text-sm text-gray-500">加载订单数据...</span>
            </div>
          </div>
          
          <div v-else-if="filteredOrders.length === 0" class="flex flex-col items-center justify-center h-32 text-gray-500">
            <Package class="w-12 h-12 mb-3 text-gray-400" />
            <span class="text-sm">暂无可用订单</span>
          </div>
          
          <div v-else class="space-y-4">
            <OrderItemSelector
              v-for="order in filteredOrders" 
              :key="order.id"
              :order="order"
              :selected-order-items="selectedOrderItems"
              :conflicting-items="conflictingItems"
              :unavailable-items="unavailableItems"
              @order-item-selected="handleOrderItemSelected"
              @order-item-removed="handleOrderItemRemoved"
              @quantity-changed="handleQuantityChanged"
              class="order-card-enhanced"
            />
          </div>
        </div>
      </div>
      
      <!-- 已选项汇总 (30%) -->
      <div class="w-80 selected-summary-section border-l bg-gray-50">
        <SelectedItemsSummary
          :selected-items="selectedOrderItems"
          :total-quantity="totalSelectedQuantity"
          :unique-customers="uniqueCustomers"
          :has-conflicts="hasProcessConflicts"
          @item-removed="handleOrderItemRemoved"
          @quantity-changed="handleQuantityChanged"
          @clear-all="clearAllSelections"
        />
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="step-footer p-4 border-t bg-white">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          已选择 <span class="font-medium text-blue-600">{{ selectedOrderItems.length }}</span> 个订单项，
          共 <span class="font-medium text-blue-600">{{ totalSelectedQuantity }}</span> 片
        </div>
        <div class="flex items-center gap-3">
          <Button variant="outline" @click="$emit('cancel')">
            取消
          </Button>
          <Button 
            @click="$emit('next-step')" 
            :disabled="selectedOrderItems.length === 0"
            class="bg-blue-600 hover:bg-blue-700"
          >
            下一步：优化批次
            <ArrowRight class="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 第二步：批次优化配置界面

```vue
<template>
  <div class="step-two-container h-full flex flex-col">
    <!-- 步骤标题 -->
    <div class="step-header p-4 border-b bg-gradient-to-r from-green-50 to-emerald-50">
      <h2 class="text-lg font-semibold text-gray-800">第二步：优化批次配置</h2>
      <p class="text-sm text-gray-600 mt-1">基于选择的订单项生成最优批次方案</p>
    </div>
    
    <!-- 已选项概览 -->
    <div class="selected-overview p-4 border-b bg-white">
      <SelectedItemsOverview
        :selected-items="selectedOrderItems"
        :total-quantity="totalSelectedQuantity"
        :unique-customers="uniqueCustomers"
        :estimated-value="estimatedValue"
      />
    </div>
    
    <!-- 主要内容区域 -->
    <div class="flex-1 flex min-h-0">
      <!-- 批次优化面板 (60%) -->
      <div class="flex-1 batch-optimization-section">
        <div class="h-full overflow-y-auto">
          <BatchOptimizationPanel
            :selected-order-items="selectedOrderItems"
            :optimization-result="batchOptimizationResult"
            :loading="optimizationLoading"
            @batch-modified="handleBatchModified"
            @optimization-applied="handleOptimizationApplied"
            @manual-grouping="handleManualGrouping"
          />
        </div>
      </div>
      
      <!-- 配置面板 (40%) -->
      <div class="w-96 config-section border-l bg-gray-50">
        <div class="h-full overflow-y-auto">
          <!-- 工艺兼容性检查 -->
          <div class="p-4 border-b">
            <h3 class="font-medium text-sm mb-3">工艺兼容性检查</h3>
            <ProcessCompatibilityChecker
              :selected-order-items="selectedOrderItems"
              :auto-check="true"
              @conflict-resolved="handleConflictResolved"
              @conflict-ignored="handleConflictIgnored"
              @grouping-applied="handleGroupingApplied"
            />
          </div>
          
          <!-- 工单配置 -->
          <div class="p-4">
            <h3 class="font-medium text-sm mb-3">工单配置</h3>
            <WorkOrderConfiguration
              :priority="workOrderPriority"
              :planned-start-date="plannedStartDate"
              :estimated-end-date="estimatedEndDate"
              :schedule-recommendation="scheduleRecommendation"
              @priority-changed="handlePriorityChanged"
              @schedule-changed="handleScheduleChanged"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="step-footer p-4 border-t bg-white">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          将创建 <span class="font-medium text-green-600">{{ optimizedBatches.length }}</span> 个批次，
          预计节省 <span class="font-medium text-green-600">{{ estimatedTimeSaved }}小时</span>
        </div>
        <div class="flex items-center gap-3">
          <Button variant="outline" @click="$emit('prev-step')">
            <ArrowLeft class="w-4 h-4 mr-2" />
            上一步
          </Button>
          <Button 
            @click="$emit('create-order')" 
            :disabled="!canCreateOrder"
            :loading="isCreating"
            class="bg-green-600 hover:bg-green-700"
          >
            <Plus class="w-4 h-4 mr-2" />
            创建工单
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
```

## 数据模型设计

### 工作流状态管理

```typescript
interface TwoStepWorkflowState {
  // 当前步骤
  currentStep: 1 | 2
  
  // 第一步状态
  stepOneState: {
    searchQuery: string
    statusFilter: string
    processTypeFilter: string
    customerFilter: string
    selectedOrderItems: SelectedOrderItem[]
    conflictingItems: string[]
    unavailableItems: Record<string, string>
  }
  
  // 第二步状态
  stepTwoState: {
    batchOptimizationResult: BatchOptimizationResult | null
    optimizedBatches: OptimizedBatch[]
    workOrderPriority: 'urgent' | 'high' | 'normal' | 'low'
    plannedStartDate: string
    estimatedEndDate: string
    scheduleRecommendation: string
    processConflicts: ProcessConflict[]
  }
  
  // 共享状态
  sharedState: {
    availableOrders: CustomerOrder[]
    loading: boolean
    isCreating: boolean
    validationResults: ValidationResult[]
  }
}

interface StepTransition {
  from: 1 | 2
  to: 1 | 2
  data: any
  timestamp: number
}

interface WorkflowProgress {
  currentStep: 1 | 2
  completedSteps: number[]
  canProceed: boolean
  canGoBack: boolean
  totalSteps: 2
}
```

### 步骤间数据传递

```typescript
interface StepOneOutput {
  selectedOrderItems: SelectedOrderItem[]
  searchCriteria: SearchCriteria
  selectionSummary: SelectionSummary
  timestamp: number
}

interface StepTwoInput extends StepOneOutput {
  autoOptimize: boolean
  preserveManualGrouping: boolean
}

interface StepTwoOutput {
  optimizedBatches: OptimizedBatch[]
  workOrderConfiguration: WorkOrderConfiguration
  validationResults: ValidationResult[]
  estimatedMetrics: EstimatedMetrics
}
```

## 交互流程设计

### 主要用户流程

```mermaid
graph TD
    A[打开工单创建] --> B[第一步：订单项选择]
    B --> C{是否选择了订单项?}
    C -->|否| B
    C -->|是| D[点击下一步]
    D --> E[第二步：批次优化]
    E --> F[自动生成优化方案]
    F --> G[用户调整配置]
    G --> H{确认创建?}
    H -->|否| I[返回上一步]
    I --> B
    H -->|是| J[创建工单]
    J --> K[完成]
```

### 步骤切换动画流程

```mermaid
graph TD
    A[用户点击下一步] --> B[验证当前步骤数据]
    B --> C{验证通过?}
    C -->|否| D[显示错误提示]
    C -->|是| E[保存当前状态]
    E --> F[开始切换动画]
    F --> G[淡出当前步骤]
    G --> H[更新步骤指示器]
    H --> I[淡入下一步骤]
    I --> J[加载步骤数据]
    J --> K[完成切换]
```

## 组件接口设计

### 主要组件Props和Events

```typescript
// 两步式工作流主组件
interface TwoStepWorkflowProps {
  open: boolean
  initialStep?: 1 | 2
  preserveState?: boolean
}

interface TwoStepWorkflowEvents {
  'update:open': (open: boolean) => void
  'step-changed': (step: 1 | 2) => void
  'order-created': (orderIds: string[]) => void
  'workflow-cancelled': () => void
}

// 第一步组件
interface StepOneProps {
  availableOrders: CustomerOrder[]
  initialSelection?: SelectedOrderItem[]
  searchCriteria?: SearchCriteria
}

interface StepOneEvents {
  'selection-changed': (items: SelectedOrderItem[]) => void
  'next-step': (output: StepOneOutput) => void
  'cancel': () => void
}

// 第二步组件
interface StepTwoProps {
  selectedOrderItems: SelectedOrderItem[]
  initialConfiguration?: WorkOrderConfiguration
  autoOptimize?: boolean
}

interface StepTwoEvents {
  'configuration-changed': (config: WorkOrderConfiguration) => void
  'prev-step': () => void
  'create-order': (output: StepTwoOutput) => void
}
```

## 性能优化策略

### 步骤切换优化

```typescript
class StepTransitionManager {
  private transitionCache = new Map<string, any>()
  private preloadPromises = new Map<number, Promise<any>>()
  
  // 预加载下一步数据
  async preloadNextStep(currentStep: number, data: any): Promise<void> {
    const nextStep = currentStep + 1
    if (nextStep <= 2 && !this.preloadPromises.has(nextStep)) {
      this.preloadPromises.set(nextStep, this.loadStepData(nextStep, data))
    }
  }
  
  // 缓存步骤状态
  cacheStepState(step: number, state: any): void {
    this.transitionCache.set(`step-${step}`, {
      ...state,
      timestamp: Date.now()
    })
  }
  
  // 恢复步骤状态
  restoreStepState(step: number): any | null {
    const cached = this.transitionCache.get(`step-${step}`)
    if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟有效
      return cached
    }
    return null
  }
}
```

### 数据加载优化

```typescript
// 懒加载和分页
interface DataLoadingStrategy {
  // 第一步：订单数据分页加载
  loadOrdersPage(page: number, size: number): Promise<CustomerOrder[]>
  
  // 第二步：批次优化异步计算
  calculateBatchOptimization(items: SelectedOrderItem[]): Promise<BatchOptimizationResult>
  
  // 预加载关键数据
  preloadCriticalData(): Promise<void>
}

// 状态持久化
interface StatePersistence {
  saveWorkflowState(state: TwoStepWorkflowState): void
  restoreWorkflowState(): TwoStepWorkflowState | null
  clearWorkflowState(): void
}
```

## 响应式设计

### 移动端适配

```typescript
interface ResponsiveLayout {
  // 小屏幕：垂直堆叠
  sm: {
    stepIndicator: 'compact'
    layout: 'vertical'
    navigation: 'bottom-fixed'
  }
  
  // 中等屏幕：紧凑布局
  md: {
    stepIndicator: 'standard'
    layout: 'adaptive'
    navigation: 'inline'
  }
  
  // 大屏幕：完整布局
  lg: {
    stepIndicator: 'enhanced'
    layout: 'side-by-side'
    navigation: 'integrated'
  }
}
```

### 断点适配策略

```css
/* 移动端优先的响应式设计 */
.two-step-workflow {
  /* 基础移动端样式 */
}

@media (min-width: 768px) {
  .two-step-workflow {
    /* 平板适配 */
  }
}

@media (min-width: 1024px) {
  .two-step-workflow {
    /* 桌面端适配 */
  }
}

@media (min-width: 1280px) {
  .two-step-workflow {
    /* 大屏幕优化 */
  }
}
```

## 错误处理和用户引导

### 步骤验证

```typescript
interface StepValidation {
  validateStepOne(data: StepOneOutput): ValidationResult[]
  validateStepTwo(data: StepTwoOutput): ValidationResult[]
  canProceedToNextStep(currentStep: number, data: any): boolean
  getValidationErrors(step: number): ValidationError[]
}
```

### 用户引导

```typescript
interface UserGuidance {
  showStepIntroduction(step: number): void
  highlightImportantFeatures(step: number): void
  provideContextualHelp(action: string): void
  showProgressFeedback(operation: string): void
}
```

这个设计方案将现有的单页面工单创建流程重构为两步式工作流，每个步骤都有明确的目标和优化的用户体验。通过分离关注点，用户可以更专注于每个阶段的核心任务，从而提升整体的操作效率和决策质量。