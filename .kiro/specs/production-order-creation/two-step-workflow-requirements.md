# 生产工单创建两步式工作流需求文档

## 介绍

将生产工单创建流程重构为两步式操作，提升用户体验和操作效率。第一步专注于订单项选择，第二步专注于批次优化和工单配置，让用户在每个阶段都能专注于核心任务。

## 需求

### 需求1：两步式工作流设计

**用户故事：** 作为生产计划员，我希望工单创建流程分为两个清晰的步骤，让我能够专注于每个阶段的核心任务。

#### 验收标准

1. WHEN 用户打开工单创建界面 THEN 系统应显示第一步：订单项选择界面
2. WHEN 用户完成订单项选择 THEN 系统应提供"下一步"按钮进入批次优化阶段
3. WHEN 用户在第二步 THEN 系统应提供"上一步"按钮返回订单项选择
4. WHEN 用户在任一步骤 THEN 系统应清晰显示当前步骤和整体进度
5. IF 用户未选择任何订单项 THEN "下一步"按钮应为禁用状态

### 需求2：第一步 - 订单项选择专注模式

**用户故事：** 作为生产计划员，我希望在第一步中能够专注于选择合适的订单项，不被其他信息干扰。

#### 验收标准

1. WHEN 用户在第一步 THEN 界面应只显示订单选择相关功能
2. WHEN 用户选择订单项 THEN 系统应提供清晰的选择反馈和汇总信息
3. WHEN 用户需要调整选择 THEN 系统应提供便捷的编辑和移除功能
4. WHEN 用户选择了冲突的订单项 THEN 系统应显示基础的冲突提示
5. IF 选择的订单项过多 THEN 系统应提供分页或虚拟滚动支持

### 需求3：第二步 - 批次优化和配置专注模式

**用户故事：** 作为生产计划员，我希望在第二步中能够专注于批次优化和工单配置，基于第一步的选择结果进行深度处理。

#### 验收标准

1. WHEN 用户进入第二步 THEN 系统应显示已选订单项的汇总信息
2. WHEN 系统分析订单项 THEN 应自动生成批次优化建议
3. WHEN 用户查看优化方案 THEN 系统应显示详细的批次分组和效率提升信息
4. WHEN 用户需要调整批次 THEN 系统应支持拖拽重新分组和参数调整
5. WHEN 用户配置工单参数 THEN 系统应提供优先级、计划时间等设置选项

### 需求4：步骤间数据传递和状态保持

**用户故事：** 作为生产计划员，我希望在两个步骤之间切换时，我的选择和配置能够被完整保留。

#### 验收标准

1. WHEN 用户从第一步进入第二步 THEN 所有选择的订单项应完整传递
2. WHEN 用户从第二步返回第一步 THEN 之前的选择状态应完全保持
3. WHEN 用户在第二步修改了批次配置后返回第一步 THEN 配置应被临时保存
4. WHEN 用户重新进入第二步 THEN 之前的批次配置应被恢复
5. IF 用户在第一步修改了选择 THEN 第二步的批次配置应相应更新

### 需求5：进度指示和导航优化

**用户故事：** 作为生产计划员，我希望能够清楚地知道当前处于哪个步骤，以及如何在步骤间导航。

#### 验收标准

1. WHEN 用户在任一步骤 THEN 系统应显示清晰的步骤指示器
2. WHEN 用户查看进度 THEN 应显示"1/2 选择订单项"或"2/2 优化批次"
3. WHEN 用户需要导航 THEN 应提供明确的"上一步"和"下一步"按钮
4. WHEN 用户在第二步 THEN 应显示"创建工单"按钮替代"下一步"
5. IF 用户尝试跳过必要步骤 THEN 系统应提供友好的提示信息

### 需求6：界面布局和空间优化

**用户故事：** 作为生产计划员，我希望每个步骤的界面都能充分利用空间，提供最佳的操作体验。

#### 验收标准

1. WHEN 用户在第一步 THEN 订单选择区域应占用主要空间
2. WHEN 用户在第二步 THEN 批次优化区域应占用主要空间
3. WHEN 界面切换步骤 THEN 应有平滑的过渡动画
4. WHEN 用户在小屏幕设备上操作 THEN 界面应适配移动端体验
5. IF 内容超出屏幕 THEN 应提供合适的滚动和分页机制

### 需求7：用户体验和操作效率

**用户故事：** 作为生产计划员，我希望两步式流程能够提升我的操作效率，减少认知负担。

#### 验收标准

1. WHEN 用户使用新流程 THEN 整体操作时间应比原流程减少20%
2. WHEN 用户在每个步骤 THEN 应只看到与当前任务相关的信息和控件
3. WHEN 用户完成选择 THEN 系统应提供智能的默认配置建议
4. WHEN 用户需要帮助 THEN 每个步骤应提供相应的操作指导
5. IF 用户操作出错 THEN 系统应提供清晰的错误提示和修正建议

## 设计约束

### 技术约束
- 必须基于现有的Vue 3 + TypeScript技术栈
- 需要保持与现有组件的兼容性
- 必须支持现有的数据流和API接口

### 用户体验约束
- 步骤切换时间不能超过300ms
- 每个步骤的学习成本不能增加
- 必须支持键盘导航和无障碍访问

### 业务约束
- 不能改变现有的业务逻辑和数据模型
- 必须保持向后兼容性
- 需要支持现有的权限和安全机制

## 成功标准

### 用户体验指标
- 工单创建完成时间减少20%
- 用户操作错误率降低30%
- 用户满意度评分提升至4.7/5

### 技术指标
- 步骤切换响应时间<300ms
- 界面渲染性能保持60fps
- 内存使用增长<15%

### 业务指标
- 工单创建成功率提升10%
- 批次优化采用率提升40%
- 用户培训时间减少25%