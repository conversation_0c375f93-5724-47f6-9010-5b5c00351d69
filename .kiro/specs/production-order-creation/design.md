# 生产工单创建界面重构设计文档

## 概述

基于用户体验优先和渐进式智能化的原则，设计一个高效、直观的生产工单创建界面。采用卡片式布局和智能推荐机制，在保证操作效率的同时为未来功能扩展预留空间。

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    工单创建主界面                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   订单选择区     │  │   配置预览区     │  │   操作控制区     │ │
│  │                │  │                │  │                │ │
│  │ • 订单列表      │  │ • 工艺预览      │  │ • 创建按钮      │ │
│  │ • 搜索筛选      │  │ • 参数配置      │  │ • 取消按钮      │ │
│  │ • 详情展示      │  │ • 风险提示      │  │ • 保存草稿      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 组件层次结构

```
ProductionOrderCreationDialog
├── OrderSelectionPanel
│   ├── OrderSearchFilter
│   ├── OrderList
│   └── OrderDetailPreview
├── ProcessConfigurationPanel
│   ├── ProcessFlowViewer
│   ├── ProcessParameterEditor
│   └── ValidationIndicator
└── ActionControlPanel
    ├── PrioritySelector
    ├── ScheduleSettings
    └── CreateOrderActions
```

## 界面设计

### 主界面布局

采用三栏式布局，支持响应式适配：

```vue
<template>
  <Dialog class="max-w-7xl">
    <DialogContent class="h-[90vh]">
      <div class="grid grid-cols-12 gap-6 h-full">
        <!-- 左侧：订单选择 (4/12) -->
        <div class="col-span-4 border-r pr-6">
          <OrderSelectionPanel />
        </div>
        
        <!-- 中间：配置预览 (6/12) -->
        <div class="col-span-6 px-3">
          <ProcessConfigurationPanel />
        </div>
        
        <!-- 右侧：操作控制 (2/12) -->
        <div class="col-span-2 pl-6">
          <ActionControlPanel />
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
```

### 订单项选择面板设计

```vue
<template>
  <div class="space-y-4">
    <!-- 搜索和筛选 -->
    <div class="space-y-3">
      <Input placeholder="搜索订单号、客户名称、产品规格..." />
      <div class="flex gap-2">
        <Select placeholder="订单状态">
          <SelectItem value="confirmed">已确认</SelectItem>
          <SelectItem value="ready">待转换</SelectItem>
        </Select>
        <Select placeholder="工艺类型">
          <SelectItem value="cutting">切割</SelectItem>
          <SelectItem value="tempering">钢化</SelectItem>
          <SelectItem value="laminating">夹胶</SelectItem>
        </Select>
      </div>
    </div>
    
    <!-- 订单项列表 -->
    <div class="space-y-2 max-h-96 overflow-y-auto">
      <div 
        v-for="order in availableOrders" 
        :key="order.id"
        class="border rounded-lg"
      >
        <!-- 订单头部 -->
        <div class="p-3 bg-gray-50 border-b">
          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">{{ order.orderNumber }}</div>
              <div class="text-sm text-gray-600">{{ order.customerName }}</div>
            </div>
            <div class="text-xs text-gray-500">
              {{ formatDate(order.deliveryDate) }}
            </div>
          </div>
        </div>
        
        <!-- 订单项列表 -->
        <div class="divide-y">
          <div 
            v-for="item in order.items" 
            :key="item.id"
            class="p-3 hover:bg-gray-50 cursor-pointer"
            @click="toggleOrderItem(item)"
          >
            <div class="flex items-center gap-3">
              <!-- 选择框 -->
              <Checkbox 
                :checked="isOrderItemSelected(item.id)"
                @change="toggleOrderItem(item)"
              />
              
              <!-- 产品信息 -->
              <div class="flex-1">
                <div class="font-medium text-sm">
                  {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                </div>
                <div class="text-xs text-gray-600">
                  {{ item.specifications.glassType }} {{ item.specifications.color }}
                </div>
                <div class="flex items-center gap-4 text-xs text-gray-500 mt-1">
                  <span>数量: {{ item.quantity }}片</span>
                  <span>工艺: {{ getProcessSummary(item.processFlow) }}</span>
                </div>
              </div>
              
              <!-- 数量调整 -->
              <div v-if="isOrderItemSelected(item.id)" class="flex items-center gap-2">
                <Input 
                  type="number" 
                  :value="getSelectedQuantity(item.id)"
                  @input="updateSelectedQuantity(item.id, $event.target.value)"
                  :max="item.quantity"
                  :min="1"
                  class="w-20 h-8 text-xs"
                />
                <span class="text-xs text-gray-500">/ {{ item.quantity }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 已选订单项汇总 -->
    <div v-if="selectedOrderItems.length > 0" class="border-t pt-4">
      <h4 class="font-medium mb-2 flex items-center gap-2">
        已选订单项 
        <Badge variant="secondary">{{ selectedOrderItems.length }}项</Badge>
      </h4>
      <div class="space-y-2 max-h-32 overflow-y-auto">
        <div 
          v-for="item in selectedOrderItems" 
          :key="item.id"
          class="flex items-center justify-between text-xs p-2 bg-blue-50 rounded"
        >
          <div>
            <div class="font-medium">{{ item.orderNumber }}</div>
            <div class="text-gray-600">
              {{ item.specifications.length }}×{{ item.specifications.width }}mm × {{ item.selectedQuantity }}片
            </div>
          </div>
          <Button 
            size="sm" 
            variant="ghost" 
            @click="removeOrderItem(item.id)"
          >
            <X class="w-3 h-3" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 工艺配置面板设计

```vue
<template>
  <div class="space-y-6">
    <!-- 批次优化方案 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Settings class="w-5 h-5" />
          批次优化方案
          <Badge variant="outline">智能推荐</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="batchOptimization" class="space-y-4">
          <!-- 优化效果展示 -->
          <div class="grid grid-cols-3 gap-4 p-3 bg-green-50 rounded-lg">
            <div class="text-center">
              <div class="text-lg font-bold text-green-700">{{ batchOptimization.efficiency }}%</div>
              <div class="text-xs text-green-600">效率提升</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-green-700">{{ batchOptimization.timeSaved }}h</div>
              <div class="text-xs text-green-600">节省工时</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-green-700">{{ batchOptimization.batches }}</div>
              <div class="text-xs text-green-600">推荐批次</div>
            </div>
          </div>
          
          <!-- 批次详情 -->
          <div class="space-y-3">
            <div 
              v-for="(batch, index) in batchOptimization.batches" 
              :key="index"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium">批次 {{ index + 1 }}</h4>
                <div class="flex items-center gap-2">
                  <Badge variant="outline">{{ batch.totalQuantity }}片</Badge>
                  <Badge variant="secondary">{{ batch.workstation }}</Badge>
                </div>
              </div>
              
              <!-- 批次内订单项 -->
              <div class="space-y-2 mb-3">
                <div 
                  v-for="item in batch.items" 
                  :key="item.id"
                  class="flex items-center justify-between text-sm p-2 bg-gray-50 rounded"
                >
                  <div>
                    <span class="font-medium">{{ item.orderNumber }}</span>
                    <span class="text-gray-600 ml-2">
                      {{ item.specifications.length }}×{{ item.specifications.width }}mm
                    </span>
                  </div>
                  <div class="text-gray-600">{{ item.selectedQuantity }}片</div>
                </div>
              </div>
              
              <!-- 工艺流程 -->
              <div class="flex items-center gap-2 text-sm">
                <div 
                  v-for="(step, stepIndex) in batch.processFlow" 
                  :key="stepIndex"
                  class="flex items-center gap-2"
                >
                  <div class="px-2 py-1 bg-blue-100 text-blue-700 rounded">
                    {{ step.stepName }}
                  </div>
                  <ArrowRight v-if="stepIndex < batch.processFlow.length - 1" class="w-3 h-3" />
                </div>
              </div>
              
              <!-- 批次统计 -->
              <div class="mt-2 text-xs text-gray-600 flex gap-4">
                <span>预估工时：{{ batch.estimatedTime }}分钟</span>
                <span>设备利用率：{{ batch.utilization }}%</span>
              </div>
            </div>
          </div>
          
          <!-- 工艺冲突提示 -->
          <div v-if="processConflicts.length > 0" class="p-3 bg-yellow-50 rounded-lg">
            <div class="flex items-start gap-2">
              <AlertTriangle class="w-4 h-4 text-yellow-600 mt-0.5" />
              <div class="text-sm">
                <div class="font-medium text-yellow-800">工艺冲突检测</div>
                <div class="text-yellow-700 mt-1">
                  检测到 {{ processConflicts.length }} 个工艺冲突，建议拆分为多个工单
                </div>
                <Button size="sm" variant="outline" class="mt-2" @click="showConflictDetails = true">
                  查看详情
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    
    <!-- 生产计划设置 -->
    <Card>
      <CardHeader>
        <CardTitle>生产计划</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <Label>优先级</Label>
            <Select v-model="workOrderPriority">
              <SelectTrigger>
                <SelectValue placeholder="选择优先级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="urgent">
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                    紧急
                  </div>
                </SelectItem>
                <SelectItem value="high">
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                    高
                  </div>
                </SelectItem>
                <SelectItem value="normal">
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    普通
                  </div>
                </SelectItem>
                <SelectItem value="low">
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-gray-500 rounded-full"></div>
                    低
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label>计划开始</Label>
            <Input 
              type="date" 
              v-model="plannedStartDate"
              :min="new Date().toISOString().split('T')[0]"
            />
          </div>
        </div>
        
        <!-- 智能建议 -->
        <div v-if="scheduleRecommendation" class="p-3 bg-blue-50 rounded-lg">
          <div class="flex items-start gap-2">
            <Lightbulb class="w-4 h-4 text-blue-600 mt-0.5" />
            <div class="text-sm">
              <div class="font-medium text-blue-800">智能建议</div>
              <div class="text-blue-700">{{ scheduleRecommendation }}</div>
            </div>
          </div>
        </div>
        
        <!-- 预估完成时间 -->
        <div class="p-3 bg-gray-50 rounded-lg">
          <div class="text-sm">
            <div class="font-medium">预估完成时间</div>
            <div class="text-gray-600">{{ estimatedEndDate }}</div>
          </div>
        </div>
      </CardContent>
    </Card>
    
    <!-- 验证结果 -->
    <Card v-if="validationResults.length > 0">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <AlertTriangle class="w-5 h-5 text-yellow-600" />
          验证提示
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-2">
          <div 
            v-for="result in validationResults" 
            :key="result.id"
            class="flex items-start gap-2 p-2 rounded"
            :class="getValidationClass(result.level)"
          >
            <component 
              :is="getValidationIcon(result.level)" 
              class="w-4 h-4 mt-0.5" 
            />
            <div class="text-sm">
              <div class="font-medium">{{ result.title }}</div>
              <div>{{ result.message }}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
```

## 数据模型设计

### 工单创建状态管理

```typescript
interface WorkOrderCreationState {
  // 基础数据
  availableOrders: CustomerOrder[]
  selectedOrderItems: SelectedOrderItem[]
  
  // 批次优化
  batchOptimization: BatchOptimizationResult | null
  processConflicts: ProcessConflict[]
  
  // 配置参数
  workOrderPriority: 'urgent' | 'high' | 'normal' | 'low'
  plannedStartDate: string
  customBatchConfig?: BatchConfiguration[]
  
  // 智能推荐
  scheduleRecommendation: string
  estimatedEndDate: string
  validationResults: ValidationResult[]
  
  // 界面状态
  isLoading: boolean
  isCreating: boolean
  showConflictDetails: boolean
  errors: Record<string, string>
}

interface SelectedOrderItem {
  id: string
  customerOrderId: string
  orderNumber: string
  customerName: string
  specifications: ProductSpecifications
  totalQuantity: number
  selectedQuantity: number
  processFlow: ProcessStep[]
  deliveryDate: string
}

interface BatchOptimizationResult {
  efficiency: number
  timeSaved: number
  batches: OptimizedBatch[]
  recommendations: string[]
}

interface OptimizedBatch {
  id: string
  items: SelectedOrderItem[]
  processFlow: ProcessStep[]
  workstation: string
  totalQuantity: number
  estimatedTime: number
  utilization: number
}

interface ProcessConflict {
  id: string
  conflictType: 'parameter' | 'sequence' | 'equipment'
  affectedItems: string[]
  description: string
  suggestions: string[]
}

interface ValidationResult {
  id: string
  level: 'info' | 'warning' | 'error'
  title: string
  message: string
  suggestion?: string
}

interface ProcessFlowRecommendation {
  orderId: string
  itemId: string
  recommendedFlow: ProcessStep[]
  confidence: number
  alternatives: ProcessStep[][]
}
```

### 智能推荐服务接口

```typescript
interface SmartRecommendationService {
  // 工艺流程推荐
  generateProcessFlow(orderItem: CustomerOrderItem): Promise<ProcessFlowRecommendation>
  
  // 计划时间推荐
  recommendSchedule(
    processFlow: ProcessStep[], 
    priority: string
  ): Promise<ScheduleRecommendation>
  
  // 实时验证
  validateWorkOrderConfig(config: WorkOrderCreationState): Promise<ValidationResult[]>
  
  // 能力检查（预留接口）
  checkCapacityAvailability?(
    processFlow: ProcessStep[], 
    timeRange: DateRange
  ): Promise<CapacityCheckResult>
}
```

## 交互流程设计

### 主要用户流程

```mermaid
graph TD
    A[打开创建界面] --> B[加载可用订单]
    B --> C[选择客户订单]
    C --> D[自动生成工艺流程]
    D --> E[设置生产计划]
    E --> F[实时验证配置]
    F --> G{验证通过?}
    G -->|是| H[预览工单信息]
    G -->|否| I[显示问题提示]
    I --> J[调整配置]
    J --> F
    H --> K[确认创建工单]
    K --> L[工单创建成功]
```

### 智能推荐流程

```mermaid
graph TD
    A[选择订单] --> B[分析产品规格]
    B --> C[匹配工艺模板]
    C --> D[生成推荐流程]
    D --> E[计算时间参数]
    E --> F[检查资源约束]
    F --> G[生成智能建议]
    G --> H[展示推荐结果]
    H --> I{用户接受?}
    I -->|是| J[应用推荐配置]
    I -->|否| K[允许手动调整]
    K --> L[更新配置]
    L --> F
```

## 组件接口设计

### 主要组件Props和Events

```typescript
// 订单选择面板
interface OrderSelectionPanelProps {
  availableOrders: CustomerOrder[]
  selectedOrder: CustomerOrder | null
  loading: boolean
}

interface OrderSelectionPanelEvents {
  'order-selected': (order: CustomerOrder) => void
  'search-changed': (query: string) => void
}

// 工艺配置面板
interface ProcessConfigurationPanelProps {
  selectedOrder: CustomerOrder | null
  processRecommendations: ProcessFlowRecommendation[]
  validationResults: ValidationResult[]
  scheduleSettings: ScheduleSettings
}

interface ProcessConfigurationPanelEvents {
  'priority-changed': (priority: string) => void
  'schedule-changed': (schedule: ScheduleSettings) => void
  'process-modified': (itemId: string, flow: ProcessStep[]) => void
}

// 操作控制面板
interface ActionControlPanelProps {
  canCreate: boolean
  isCreating: boolean
  validationSummary: ValidationSummary
}

interface ActionControlPanelEvents {
  'create-order': () => void
  'save-draft': () => void
  'cancel': () => void
}
```

## 性能优化策略

### 数据加载优化

1. **懒加载订单详情** - 只在选中时加载完整订单信息
2. **工艺推荐缓存** - 缓存常用产品的工艺推荐结果
3. **分页加载订单** - 大量订单时采用虚拟滚动
4. **预加载关键数据** - 提前加载工艺模板和约束参数

### 交互响应优化

1. **乐观更新** - 用户操作立即反映在界面上
2. **防抖处理** - 搜索和验证操作使用防抖
3. **骨架屏** - 数据加载时显示内容骨架
4. **渐进式加载** - 核心功能优先，增强功能后加载

## 错误处理策略

### 验证错误处理

```typescript
interface ValidationErrorHandler {
  // 字段级验证
  validateField(field: string, value: any): ValidationResult[]
  
  // 表单级验证
  validateForm(state: WorkOrderCreationState): ValidationResult[]
  
  // 业务规则验证
  validateBusinessRules(config: WorkOrderConfig): ValidationResult[]
}
```

### 网络错误处理

1. **自动重试** - 网络请求失败时自动重试3次
2. **离线缓存** - 关键配置数据本地缓存
3. **优雅降级** - 智能功能不可用时回退到基础模式
4. **用户提示** - 清晰的错误信息和恢复建议

## 扩展性设计

### 插件化架构

```typescript
interface WorkOrderCreationPlugin {
  name: string
  version: string
  
  // 生命周期钩子
  onOrderSelected?(order: CustomerOrder): void
  onProcessGenerated?(flow: ProcessStep[]): void
  onValidationRequired?(config: WorkOrderConfig): ValidationResult[]
  onOrderCreated?(workOrder: ProductionOrder): void
}

// 插件注册
class WorkOrderCreationManager {
  private plugins: WorkOrderCreationPlugin[] = []
  
  registerPlugin(plugin: WorkOrderCreationPlugin): void
  executeHook(hookName: string, ...args: any[]): void
}
```

### 功能开关配置

```typescript
interface FeatureFlags {
  enableSmartRecommendation: boolean
  enableCapacityCheck: boolean
  enableInventoryValidation: boolean
  enableDeliveryPromise: boolean
  enableAdvancedProcessConfig: boolean
}
```

这个设计文档提供了完整的架构和实现指导，既满足了当前的快速创建需求，又为未来的智能化功能预留了扩展空间。你觉得这个设计方向如何？有什么需要调整或补充的地方吗？