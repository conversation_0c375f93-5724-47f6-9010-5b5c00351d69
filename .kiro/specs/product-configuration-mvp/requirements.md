# 玻璃产品配置系统需求文档

## 项目概述

基于玻璃深加工企业的实际业务场景，设计一套产品配置和自动报价系统。系统支持基于产品结构的参数化配置，实现从客户订单到自动报价的快速转换，并在产品未配置时提醒工艺工程师进行配置。

## 业务场景示例

**客户订单**：中空5#白玻+12a+5#lowe
- 规格1：1200mm×1800mm，数量：10片
- 规格2：1500mm×1800mm，数量：15片

**产品配置**：基于产品结构"中空玻璃"自动计算
- 5#白玻：1片（长×宽）
- 12a铝隔条：2×长 + 2×宽
- 5#LowE：1片（长×宽）

**系统输出**：自动生成精确的材料用量和报价

## 核心业务价值

- **自动化报价**：基于产品结构和尺寸参数自动计算材料用量和成本
- **标准化配置**：建立统一的产品结构模板，确保配置一致性
- **智能提醒**：未配置产品自动提醒工艺工程师进行配置
- **精确计算**：基于实际尺寸参数精确计算材料用量，减少浪费

## 用户角色定义

### 销售人员
- **职责**：接收客户订单，录入产品规格和数量，获取自动报价
- **技能水平**：熟悉玻璃产品规格，了解基本工艺
- **操作频率**：每日处理5-15个客户订单

### 工艺工程师
- **职责**：配置新产品的结构定义，设置材料用量计算规则
- **技能水平**：深度了解玻璃工艺，熟悉材料特性和加工流程
- **操作频率**：每周配置2-5个新产品结构

### 业务管理员
- **职责**：维护产品库，审核产品配置，管理报价策略
- **技能水平**：综合业务能力，了解成本控制
- **操作频率**：每日审核和维护工作

## 需求列表

### 需求1：客户订单录入和产品识别

**用户故事**：作为销售人员，我希望能够快速录入客户订单信息，系统自动识别产品是否已配置。

#### 验收标准

1. **WHEN** 销售人员录入产品描述（如：中空5#白玻+12a+5#lowe）**THEN** 系统应自动搜索匹配的已配置产品
2. **WHEN** 输入多个规格尺寸和数量 **THEN** 系统应支持批量录入（如：1200×1800mm 10片，1500×1800mm 15片）
3. **WHEN** 产品已在系统中配置 **THEN** 系统应显示产品结构和材料组成预览
4. **WHEN** 产品未在系统中配置 **THEN** 系统应提示"需要工艺工程师配置"并发送通知
5. **WHEN** 存在相似产品 **THEN** 系统应推荐相近的已配置产品供参考

### 需求2：基于产品结构的自动计算

**用户故事**：作为系统，我需要基于预设的产品结构和客户输入的尺寸，自动计算各材料的精确用量。

#### 验收标准

1. **WHEN** 产品结构为"中空玻璃"且输入尺寸为1200×1800mm **THEN** 系统应计算：面玻1片（1200×1800），隔条2×1200+2×1800，背玻1片（1200×1800）
2. **WHEN** 计算材料用量 **THEN** 系统应基于参数化公式：隔条用量=2×长+2×宽
3. **WHEN** 处理多规格订单 **THEN** 系统应分别计算每个规格的材料用量并汇总
4. **WHEN** 材料有损耗率设置 **THEN** 系统应在基础用量上增加相应损耗
5. **WHEN** 计算完成 **THEN** 系统应显示详细的材料用量清单

### 需求3：自动报价生成

**用户故事**：作为销售人员，我希望系统基于材料用量和价格自动生成准确的报价。

#### 验收标准

1. **WHEN** 材料用量计算完成 **THEN** 系统应自动获取最新的材料单价
2. **WHEN** 计算总成本 **THEN** 系统应包含：材料成本+加工费+管理费用
3. **WHEN** 生成报价 **THEN** 应显示：材料明细、数量、单价、小计、总价
4. **WHEN** 需要调整利润率 **THEN** 销售人员应能实时调整并查看价格变化
5. **WHEN** 报价确认 **THEN** 系统应生成标准格式的报价单供客户确认

### 需求4：工艺工程师产品配置

**用户故事**：作为工艺工程师，我希望能够为新产品创建结构配置，定义材料组成和计算规则。

#### 验收标准

1. **WHEN** 接收到新产品配置请求 **THEN** 系统应显示产品描述和客户需求信息
2. **WHEN** 创建产品结构 **THEN** 应能选择基础结构模板（如：中空玻璃、夹胶玻璃、单片玻璃）
3. **WHEN** 定义材料组成 **THEN** 应能设置每种材料的数量计算公式（如：隔条=2×长+2×宽）
4. **WHEN** 设置材料规格 **THEN** 应能关联具体的材料变体（如：5#白玻、12a铝隔条、5#LowE）
5. **WHEN** 配置完成 **THEN** 系统应进行测试计算并通知销售人员可以使用

### 需求5：产品库管理

**用户故事**：作为业务管理员，我希望能够管理产品库，维护产品结构和价格策略。

#### 验收标准

1. **WHEN** 查看产品库 **THEN** 应显示所有已配置产品的列表和基本信息
2. **WHEN** 编辑产品结构 **THEN** 应能修改材料组成、计算公式和损耗率
3. **WHEN** 更新材料价格 **THEN** 系统应自动重新计算相关产品的成本
4. **WHEN** 产品停用 **THEN** 应能设置产品状态，停用后不再出现在选择列表中
5. **WHEN** 导出产品信息 **THEN** 应能生成产品配置报表供业务分析使用

## 数据模型概要

### 产品结构模板
- **中空玻璃**：面玻×1 + 隔条×(2×长+2×宽) + 背玻×1
- **夹胶玻璃**：面玻×1 + 胶片×1 + 背玻×1  
- **单片玻璃**：玻璃×1

### 产品配置示例
```
产品名称：中空5#白玻+12a+5#lowe
基础结构：中空玻璃
材料组成：
- 5#白玻：1片（长×宽）
- 12a铝隔条：2×长 + 2×宽（米）
- 5#LowE：1片（长×宽）
```

### 计算规则示例
```
输入：1200mm×1800mm
计算结果：
- 5#白玻：1.2×1.8 = 2.16㎡
- 12a铝隔条：2×1.2 + 2×1.8 = 6.0米
- 5#LowE：1.2×1.8 = 2.16㎡
```

## 技术约束

### 计算精度约束
- 尺寸计算精确到毫米级别
- 面积计算保留小数点后2位
- 长度计算保留小数点后1位

### 性能约束
- 产品识别响应时间不超过2秒
- 材料用量计算时间不超过3秒
- 报价生成时间不超过5秒

### 业务约束
- 支持的最大尺寸：6000mm×3000mm
- 支持的最小尺寸：300mm×300mm
- 单次订单最多支持10个不同规格

## 验收标准

### 功能验收
- [ ] 所有用户故事的验收标准100%通过
- [ ] 与现有MES系统集成测试通过
- [ ] 用户培训完成，操作熟练度达标

### 性能验收
- [ ] 系统响应时间满足约束要求
- [ ] 并发用户测试通过
- [ ] 数据一致性验证通过

### 用户验收
- [ ] 销售人员能够独立完成产品配置和报价
- [ ] 技术人员能够高效完成BOM转换
- [ ] 生产管理员确认系统集成无缝衔接

## 风险评估

### 高风险
- **系统集成复杂性**：与现有MES系统的数据同步可能存在技术难点
- **用户接受度**：新流程可能需要用户改变现有工作习惯

### 中风险  
- **数据质量**：物料基础数据的准确性直接影响配置结果
- **性能问题**：复杂配置计算可能影响系统响应速度

### 低风险
- **功能范围**：MVP版本功能相对简单，技术实现风险较低

## 成功指标

### 效率指标
- 订单录入到报价生成时间从30分钟缩短到5分钟
- 新产品配置时间从2小时缩短到30分钟
- 材料用量计算准确率达到99%以上

### 业务指标
- 系统识别已配置产品准确率达到95%以上
- 工艺工程师配置响应时间不超过4小时
- 月度处理订单数量提升30%以上

### 用户体验指标
- 销售人员操作熟练度培训时间不超过2小时
- 工艺工程师配置界面易用性评分4.0以上（5分制）
- 系统故障率低于1%