# 玻璃产品配置系统设计文档

## 1. 系统架构设计

### 1.1 整体架构

```mermaid
graph TB
    A[客户订单录入] --> B[产品识别引擎]
    B --> C{产品已配置?}
    C -->|是| D[参数化计算引擎]
    C -->|否| E[工艺工程师配置]
    E --> F[产品结构定义]
    F --> D
    D --> G[材料用量计算]
    G --> H[成本计算引擎]
    H --> I[自动报价生成]
    I --> J[报价单输出]
```

### 1.2 核心模块

#### 产品识别模块
- **功能**：基于产品描述自动匹配已配置产品
- **算法**：关键词匹配 + 模糊搜索
- **输入**：产品描述字符串（如：中空5#白玻+12a+5#lowe）
- **输出**：匹配的产品配置或"未找到"状态

#### 参数化计算引擎
- **功能**：基于产品结构和尺寸参数计算材料用量
- **核心算法**：公式解析器 + 参数替换
- **支持公式**：2×长+2×宽、长×宽、固定数量等
- **输出**：精确的材料用量清单

#### 产品配置管理
- **功能**：工艺工程师创建和维护产品结构
- **结构模板**：中空玻璃、夹胶玻璃、单片玻璃等
- **配置要素**：材料组成、计算公式、损耗率

## 2. 数据模型设计

### 2.1 核心实体关系

```mermaid
erDiagram
    ProductStructureTemplate ||--o{ ProductConfiguration : "基于"
    ProductConfiguration ||--o{ MaterialComponent : "包含"
    MaterialComponent }o--|| MaterialVariant : "关联"
    CustomerOrder ||--o{ OrderItem : "包含"
    OrderItem }o--|| ProductConfiguration : "使用"
    OrderItem ||--o{ MaterialRequirement : "生成"
```

### 2.2 数据模型定义

#### 产品结构模板 (ProductStructureTemplate)
```typescript
interface ProductStructureTemplate {
  id: string;
  name: string;                    // 如：中空玻璃、夹胶玻璃
  description: string;
  components: ComponentDefinition[];
  status: 'active' | 'inactive';
}

interface ComponentDefinition {
  id: string;
  name: string;                    // 如：面玻、隔条、背玻
  materialType: string;            // 材料类型
  quantityFormula: string;         // 如：'1'、'2*length+2*width'、'length*width'
  unit: string;                    // 单位：片、米、㎡
  required: boolean;
}
```

#### 产品配置 (ProductConfiguration)
```typescript
interface ProductConfiguration {
  id: string;
  name: string;                    // 如：中空5#白玻+12a+5#lowe
  description: string;
  structureTemplateId: string;     // 关联的结构模板
  materialComponents: MaterialComponent[];
  status: 'draft' | 'active' | 'inactive';
  createdBy: string;               // 工艺工程师ID
  createdAt: string;
  updatedAt: string;
}

interface MaterialComponent {
  id: string;
  componentId: string;             // 对应ComponentDefinition的ID
  materialVariantId: string;       // 具体材料变体
  materialVariantName: string;     // 如：5#白玻、12a铝隔条
  quantityFormula: string;         // 继承或覆盖模板公式
  wastageRate: number;             // 损耗率 0.05 = 5%
  unitCost: number;                // 单位成本
}
```

#### 客户订单 (CustomerOrder)
```typescript
interface CustomerOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  orderDate: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'draft' | 'quoted' | 'confirmed' | 'production';
}

interface OrderItem {
  id: string;
  productDescription: string;      // 客户描述：中空5#白玻+12a+5#lowe
  productConfigurationId?: string; // 匹配的产品配置ID
  specifications: OrderSpecification[];
  totalQuantity: number;
  unitPrice: number;
  totalPrice: number;
  needsConfiguration: boolean;     // 是否需要工艺工程师配置
}

interface OrderSpecification {
  length: number;                  // 长度 mm
  width: number;                   // 宽度 mm
  quantity: number;                // 数量
  area: number;                    // 面积 ㎡ (自动计算)
}
```

#### 材料需求 (MaterialRequirement)
```typescript
interface MaterialRequirement {
  id: string;
  orderItemId: string;
  materialVariantId: string;
  materialVariantName: string;
  requiredQuantity: number;        // 所需数量
  unit: string;
  unitCost: number;
  totalCost: number;
  calculationDetails: CalculationDetail[];
}

interface CalculationDetail {
  specificationIndex: number;      // 对应OrderSpecification的索引
  length: number;
  width: number;
  quantity: number;
  calculatedAmount: number;        // 单个规格的用量
  formula: string;                 // 使用的计算公式
}
```

## 3. 核心算法设计

### 3.1 产品识别算法

```typescript
class ProductMatcher {
  // 产品识别的核心算法
  async matchProduct(description: string): Promise<ProductMatchResult> {
    // 1. 关键词提取
    const keywords = this.extractKeywords(description);
    
    // 2. 精确匹配
    let exactMatch = await this.findExactMatch(keywords);
    if (exactMatch) {
      return { match: exactMatch, confidence: 1.0 };
    }
    
    // 3. 模糊匹配
    const fuzzyMatches = await this.findFuzzyMatches(keywords);
    if (fuzzyMatches.length > 0) {
      return { 
        match: fuzzyMatches[0], 
        confidence: fuzzyMatches[0].score,
        alternatives: fuzzyMatches.slice(1, 3)
      };
    }
    
    // 4. 未找到匹配
    return { match: null, needsConfiguration: true };
  }
  
  private extractKeywords(description: string): ProductKeywords {
    // 解析产品描述，提取关键信息
    return {
      glassType: this.extractGlassType(description),    // 5#白玻、5#lowe
      structure: this.extractStructure(description),    // 中空、夹胶
      spacer: this.extractSpacer(description),          // 12a、16a
      coating: this.extractCoating(description)         // lowe、镀膜
    };
  }
}
```

### 3.2 参数化计算引擎

```typescript
class CalculationEngine {
  // 基于公式和参数计算材料用量
  calculateMaterialRequirements(
    productConfig: ProductConfiguration,
    specifications: OrderSpecification[]
  ): MaterialRequirement[] {
    const requirements: MaterialRequirement[] = [];
    
    for (const component of productConfig.materialComponents) {
      const requirement = this.calculateComponentRequirement(
        component,
        specifications
      );
      requirements.push(requirement);
    }
    
    return requirements;
  }
  
  private calculateComponentRequirement(
    component: MaterialComponent,
    specifications: OrderSpecification[]
  ): MaterialRequirement {
    const calculationDetails: CalculationDetail[] = [];
    let totalQuantity = 0;
    
    specifications.forEach((spec, index) => {
      const amount = this.evaluateFormula(
        component.quantityFormula,
        {
          length: spec.length / 1000,  // 转换为米
          width: spec.width / 1000,
          area: spec.area
        }
      );
      
      const totalAmount = amount * spec.quantity;
      totalQuantity += totalAmount;
      
      calculationDetails.push({
        specificationIndex: index,
        length: spec.length,
        width: spec.width,
        quantity: spec.quantity,
        calculatedAmount: totalAmount,
        formula: component.quantityFormula
      });
    });
    
    // 应用损耗率
    const finalQuantity = totalQuantity * (1 + component.wastageRate);
    
    return {
      id: generateId(),
      materialVariantId: component.materialVariantId,
      materialVariantName: component.materialVariantName,
      requiredQuantity: Math.ceil(finalQuantity * 100) / 100, // 保留2位小数
      unit: this.getComponentUnit(component),
      unitCost: component.unitCost,
      totalCost: finalQuantity * component.unitCost,
      calculationDetails
    };
  }
  
  private evaluateFormula(formula: string, params: any): number {
    // 安全的公式计算器
    const safeFormula = formula
      .replace(/length/g, params.length.toString())
      .replace(/width/g, params.width.toString())
      .replace(/area/g, params.area.toString());
    
    // 使用安全的数学表达式解析器
    return this.mathEvaluator.evaluate(safeFormula);
  }
}
```

### 3.3 成本计算算法

```typescript
class CostCalculator {
  calculateOrderCost(
    materialRequirements: MaterialRequirement[],
    pricingStrategy: PricingStrategy
  ): OrderCostBreakdown {
    // 1. 材料成本
    const materialCost = materialRequirements.reduce(
      (sum, req) => sum + req.totalCost, 0
    );
    
    // 2. 加工费用
    const processingCost = materialCost * pricingStrategy.processingRate;
    
    // 3. 管理费用
    const overheadCost = materialCost * pricingStrategy.overheadRate;
    
    // 4. 基础成本
    const baseCost = materialCost + processingCost + overheadCost;
    
    // 5. 利润
    const profit = baseCost * pricingStrategy.profitMargin;
    
    // 6. 最终价格
    const finalPrice = baseCost + profit;
    
    return {
      materialCost,
      processingCost,
      overheadCost,
      baseCost,
      profit,
      finalPrice,
      profitMargin: pricingStrategy.profitMargin
    };
  }
}
```

## 4. 用户界面设计

### 4.1 销售人员界面

#### 订单录入页面
```vue
<template>
  <div class="order-entry-page">
    <!-- 客户信息 -->
    <CustomerInfoSection v-model="order.customer" />
    
    <!-- 产品录入 -->
    <ProductEntrySection 
      v-model="order.items"
      @product-matched="handleProductMatched"
      @needs-configuration="handleNeedsConfiguration"
    />
    
    <!-- 规格录入 -->
    <SpecificationEntry
      v-for="(item, index) in order.items"
      :key="index"
      v-model="item.specifications"
      :product-config="item.productConfiguration"
    />
    
    <!-- 自动报价 -->
    <QuoteSection
      :material-requirements="materialRequirements"
      :cost-breakdown="costBreakdown"
      @generate-quote="generateQuote"
    />
  </div>
</template>
```

#### 产品录入组件
```vue
<template>
  <div class="product-entry">
    <div class="input-section">
      <Label>产品描述</Label>
      <Input 
        v-model="productDescription"
        placeholder="如：中空5#白玻+12a+5#lowe"
        @blur="searchProduct"
      />
      <Button @click="searchProduct" :loading="searching">
        识别产品
      </Button>
    </div>
    
    <div v-if="matchResult" class="match-result">
      <div v-if="matchResult.match" class="matched-product">
        <CheckCircle class="text-green-500" />
        <span>已找到匹配产品：{{ matchResult.match.name }}</span>
        <Badge>置信度: {{ (matchResult.confidence * 100).toFixed(0) }}%</Badge>
      </div>
      
      <div v-else-if="matchResult.needsConfiguration" class="needs-config">
        <AlertCircle class="text-orange-500" />
        <span>产品未配置，已通知工艺工程师</span>
        <Button variant="outline" @click="notifyEngineer">
          重新通知
        </Button>
      </div>
    </div>
  </div>
</template>
```

### 4.2 工艺工程师界面

#### 产品配置页面
```vue
<template>
  <div class="product-configuration-page">
    <!-- 配置请求信息 -->
    <ConfigurationRequest :request="configRequest" />
    
    <!-- 结构模板选择 -->
    <StructureTemplateSelector 
      v-model="selectedTemplate"
      @template-selected="loadTemplate"
    />
    
    <!-- 材料组成配置 -->
    <MaterialComponentsEditor
      v-model="productConfig.materialComponents"
      :template="selectedTemplate"
    />
    
    <!-- 计算公式编辑 -->
    <FormulaEditor
      v-for="component in productConfig.materialComponents"
      :key="component.id"
      v-model="component.quantityFormula"
      :component="component"
    />
    
    <!-- 测试计算 -->
    <CalculationTester
      :product-config="productConfig"
      @test-complete="handleTestComplete"
    />
    
    <!-- 保存配置 -->
    <div class="actions">
      <Button @click="saveConfiguration" :loading="saving">
        保存配置
      </Button>
      <Button variant="outline" @click="testConfiguration">
        测试计算
      </Button>
    </div>
  </div>
</template>
```

## 5. API接口设计

### 5.1 产品识别接口

```typescript
// POST /api/products/match
interface ProductMatchRequest {
  description: string;
  customerInfo?: {
    id: string;
    name: string;
  };
}

interface ProductMatchResponse {
  success: boolean;
  data: {
    match?: ProductConfiguration;
    confidence: number;
    alternatives?: ProductConfiguration[];
    needsConfiguration: boolean;
    configurationRequestId?: string;
  };
}
```

### 5.2 材料计算接口

```typescript
// POST /api/calculations/materials
interface MaterialCalculationRequest {
  productConfigurationId: string;
  specifications: OrderSpecification[];
}

interface MaterialCalculationResponse {
  success: boolean;
  data: {
    materialRequirements: MaterialRequirement[];
    totalCost: number;
    calculationTime: number;
  };
}
```

### 5.3 报价生成接口

```typescript
// POST /api/quotes/generate
interface QuoteGenerationRequest {
  orderId: string;
  materialRequirements: MaterialRequirement[];
  pricingStrategy: {
    profitMargin: number;
    processingRate: number;
    overheadRate: number;
  };
}

interface QuoteGenerationResponse {
  success: boolean;
  data: {
    quoteId: string;
    costBreakdown: OrderCostBreakdown;
    quoteDocument: {
      url: string;
      format: 'pdf' | 'excel';
    };
  };
}
```

## 6. 技术实现方案

### 6.1 前端技术栈

- **框架**：Vue 3 + TypeScript
- **状态管理**：Pinia
- **UI组件**：ShadCN Vue
- **表单处理**：Vee-validate
- **数据可视化**：Chart.js（用于成本分析图表）

### 6.2 核心服务实现

#### 产品识别服务
```typescript
@Injectable()
export class ProductMatchingService {
  constructor(
    private readonly productRepository: ProductRepository,
    private readonly keywordExtractor: KeywordExtractor,
    private readonly fuzzyMatcher: FuzzyMatcher
  ) {}
  
  async matchProduct(description: string): Promise<ProductMatchResult> {
    // 实现产品匹配逻辑
  }
}
```

#### 计算引擎服务
```typescript
@Injectable()
export class CalculationEngineService {
  constructor(
    private readonly formulaEvaluator: FormulaEvaluator,
    private readonly materialService: MaterialService
  ) {}
  
  async calculateMaterialRequirements(
    productConfigId: string,
    specifications: OrderSpecification[]
  ): Promise<MaterialRequirement[]> {
    // 实现材料用量计算
  }
}
```

### 6.3 数据存储方案

#### 数据库表结构
```sql
-- 产品结构模板表
CREATE TABLE product_structure_templates (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  components JSON NOT NULL,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 产品配置表
CREATE TABLE product_configurations (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  structure_template_id VARCHAR(36),
  material_components JSON NOT NULL,
  status ENUM('draft', 'active', 'inactive') DEFAULT 'draft',
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (structure_template_id) REFERENCES product_structure_templates(id)
);

-- 客户订单表
CREATE TABLE customer_orders (
  id VARCHAR(36) PRIMARY KEY,
  order_number VARCHAR(50) UNIQUE NOT NULL,
  customer_name VARCHAR(200) NOT NULL,
  order_date DATE NOT NULL,
  total_amount DECIMAL(12,2),
  status ENUM('draft', 'quoted', 'confirmed', 'production') DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 订单项表
CREATE TABLE order_items (
  id VARCHAR(36) PRIMARY KEY,
  order_id VARCHAR(36) NOT NULL,
  product_description VARCHAR(500) NOT NULL,
  product_configuration_id VARCHAR(36),
  specifications JSON NOT NULL,
  total_quantity INT NOT NULL,
  unit_price DECIMAL(10,2),
  total_price DECIMAL(12,2),
  needs_configuration BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES customer_orders(id),
  FOREIGN KEY (product_configuration_id) REFERENCES product_configurations(id)
);
```

## 7. 部署和运维

### 7.1 部署架构

```mermaid
graph TB
    A[前端应用] --> B[API网关]
    B --> C[产品配置服务]
    B --> D[计算引擎服务]
    B --> E[报价服务]
    C --> F[MySQL数据库]
    D --> F
    E --> F
    G[Redis缓存] --> C
    G --> D
    H[文件存储] --> E
```

### 7.2 性能优化

#### 缓存策略
- **产品配置缓存**：Redis缓存热门产品配置，TTL 1小时
- **计算结果缓存**：相同规格的计算结果缓存，TTL 30分钟
- **材料价格缓存**：材料价格信息缓存，TTL 4小时

#### 数据库优化
- **索引策略**：产品描述全文索引，规格尺寸范围索引
- **分区策略**：按月份分区存储历史订单数据
- **读写分离**：查询操作使用只读副本

### 7.3 监控和告警

#### 关键指标监控
- **响应时间**：产品识别 < 2秒，计算 < 3秒，报价生成 < 5秒
- **准确率**：产品识别准确率 > 95%，计算准确率 > 99%
- **可用性**：系统可用性 > 99.5%

#### 业务监控
- **配置请求**：未配置产品的请求数量和响应时间
- **使用统计**：各产品配置的使用频率和成功率
- **错误追踪**：计算错误和匹配失败的详细日志

## 8. 测试策略

### 8.1 单元测试

#### 核心算法测试
```typescript
describe('CalculationEngine', () => {
  it('should calculate hollow glass materials correctly', () => {
    const engine = new CalculationEngine();
    const result = engine.calculateMaterialRequirements(
      hollowGlassConfig,
      [{ length: 1200, width: 1800, quantity: 10 }]
    );
    
    expect(result).toHaveLength(3); // 面玻、隔条、背玻
    expect(result[0].requiredQuantity).toBe(21.6); // 10片 × 2.16㎡
    expect(result[1].requiredQuantity).toBe(60); // 10片 × 6米
  });
});
```

### 8.2 集成测试

#### 端到端业务流程测试
```typescript
describe('Order Processing Flow', () => {
  it('should process complete order from entry to quote', async () => {
    // 1. 录入订单
    const order = await orderService.createOrder(orderData);
    
    // 2. 产品识别
    const matchResult = await productService.matchProduct(
      order.items[0].productDescription
    );
    expect(matchResult.match).toBeDefined();
    
    // 3. 计算材料用量
    const requirements = await calculationService.calculate(
      matchResult.match.id,
      order.items[0].specifications
    );
    expect(requirements).toHaveLength(3);
    
    // 4. 生成报价
    const quote = await quoteService.generateQuote(order.id, requirements);
    expect(quote.finalPrice).toBeGreaterThan(0);
  });
});
```

### 8.3 性能测试

#### 并发处理测试
- **并发用户**：模拟20个用户同时进行产品配置
- **数据量测试**：测试1000个产品配置的查询性能
- **计算压力测试**：复杂产品结构的计算性能测试

## 9. 风险评估和缓解

### 9.1 技术风险

#### 计算精度风险
- **风险**：浮点数计算可能导致精度问题
- **缓解**：使用Decimal库进行精确计算，设置合理的舍入规则

#### 公式解析安全风险
- **风险**：用户输入的公式可能包含恶意代码
- **缓解**：使用安全的数学表达式解析器，限制可用函数和操作符

### 9.2 业务风险

#### 产品识别准确率风险
- **风险**：产品描述的多样性可能导致识别错误
- **缓解**：建立产品描述标准化词典，持续优化匹配算法

#### 工艺工程师响应时间风险
- **风险**：新产品配置请求响应不及时影响业务
- **缓解**：建立配置请求优先级机制，设置自动提醒和升级流程

### 9.3 数据风险

#### 材料价格变动风险
- **风险**：材料价格频繁变动影响报价准确性
- **缓解**：建立价格更新机制，设置价格有效期和预警功能

## 10. 实施计划

### 10.1 开发阶段

#### 第一阶段（2周）：基础框架
- [ ] 数据模型设计和数据库创建
- [ ] 基础API接口开发
- [ ] 前端页面框架搭建

#### 第二阶段（2周）：核心功能
- [ ] 产品识别算法实现
- [ ] 参数化计算引擎开发
- [ ] 工艺工程师配置界面

#### 第三阶段（1周）：报价功能
- [ ] 成本计算算法实现
- [ ] 报价生成和导出功能
- [ ] 用户界面完善

#### 第四阶段（1周）：测试和优化
- [ ] 单元测试和集成测试
- [ ] 性能优化和缓存实现
- [ ] 用户验收测试

### 10.2 部署和上线

#### 测试环境部署
- [ ] 测试数据准备
- [ ] 功能测试验证
- [ ] 性能测试验证

#### 生产环境部署
- [ ] 数据迁移和初始化
- [ ] 用户培训和文档
- [ ] 监控和告警配置

这个设计文档基于你的具体业务需求，提供了完整的技术实现方案。设计重点关注了玻璃行业的特殊性，特别是参数化计算和产品识别的准确性。你觉得这个设计方案是否符合你的预期？