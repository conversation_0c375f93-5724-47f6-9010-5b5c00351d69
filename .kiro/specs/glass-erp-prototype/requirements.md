# Requirements Document

## Introduction

本项目旨在创建一款面向玻璃深加工及周边企业（建筑、家具、装饰、特种玻璃制造，酒店隔断、防火窗定制等）的世界级全流程智能管理软件（ERP+MES）的高保真原型。该原型将构建一个可交互、数据驱动的演示系统，核心用户是软件的前后端开发工程师，主要目的是清晰、高效地沟通和验证产品需求，作为开发工作的直接蓝图。

## Requirements

### Requirement 1

**User Story:** 作为开发工程师，我希望有一个基于现代前端技术栈的项目基础架构，以便能够快速构建和迭代高保真原型。

#### Acceptance Criteria

1. WHEN 初始化项目 THEN 系统 SHALL 使用 Vue 3 + TypeScript + Vite 作为核心技术栈
2. WHEN 配置包管理器 THEN 系统 SHALL 使用 pnpm 作为包管理工具
3. WHEN 安装依赖 THEN 系统 SHALL 包含 vue-router、pinia、tailwindcss、shadcn-vue、lucide-vue-next 等必要依赖
4. WHEN 配置构建工具 THEN 系统 SHALL 正确配置 vite.config.ts 和 tailwind.config.js 以支持 Shadcn Vue

### Requirement 2

**User Story:** 作为开发工程师，我希望有一个清晰的项目目录结构，以便能够高效地组织和管理代码。

#### Acceptance Criteria

1. WHEN 创建项目结构 THEN 系统 SHALL 按功能模块划分目录（crm、inventory、mes、procurement、quality）
2. WHEN 组织组件 THEN 系统 SHALL 将全局共享组件放在 components 目录下
3. WHEN 管理静态资源 THEN 系统 SHALL 将静态资源放在 assets 目录下
4. WHEN 定义类型 THEN 系统 SHALL 将 TypeScript 类型定义放在 types 目录下
5. WHEN 配置路由 THEN 系统 SHALL 将路由配置放在 router 目录下

### Requirement 3

**User Story:** 作为开发工程师，我希望有一个统一的数据模拟机制，以便能够在不依赖后端的情况下开发和测试前端功能。

#### Acceptance Criteria

1. WHEN 存储模拟数据 THEN 系统 SHALL 将所有模拟数据存放在 /public/mock/ 目录下
2. WHEN 组织数据文件 THEN 系统 SHALL 按业务模块分类存储 JSON 文件（common、metadata、crm、inventory、mes、procurement、quality）
3. WHEN 禁止硬编码 THEN 系统 SHALL 严格遵循前后端分离原则，禁止在组件中硬编码业务数据
4. WHEN 加载数据 THEN 系统 SHALL 通过异步方式从 JSON 文件加载数据

### Requirement 4

**User Story:** 作为开发工程师，我希望有符合玻璃行业特性的物料元数据管理功能，以便构建真实的物料主数据管理（MDM）演示。

#### Acceptance Criteria

1. WHEN 创建物料分类数据 THEN 系统 SHALL 在 materialCategories.json 中包含玻璃行业的层级分类结构，包括玻璃类、型材类、配件类等主要分类
2. WHEN 定义属性模式 THEN 系统 SHALL 为每个分类定义 attributeSchema，包含基础属性（厚度、颜色、等级）和变体属性（宽度、高度）
3. WHEN 创建物料实例 THEN 系统 SHALL 在 materials.json 中包含具体的物料数据，每个物料包含基础属性值和多个库存变体
4. WHEN 确保数据关联 THEN 系统 SHALL 通过 categoryId 建立分类与物料的关联关系，通过 variants 数组管理库存变体

### Requirement 5

**User Story:** 作为开发工程师，我希望有一个统一的UI组件系统，以便能够快速构建一致性的用户界面。

#### Acceptance Criteria

1. WHEN 使用UI组件库 THEN 系统 SHALL 基于 Shadcn Vue 和 Tailwind CSS 构建组件系统，使用 reka-ui 作为底层组件库
2. WHEN 添加基础组件 THEN 系统 SHALL 通过 `npx shadcn-vue@latest add button` 等命令安装必要的基础组件
3. WHEN 确保一致性 THEN 系统 SHALL 采用统一的设计语言和可复用的UI组件，配置 components.json 文件进行项目设置
4. WHEN 支持定制化 THEN 系统 SHALL 允许基于 Shadcn 进行二次封装或自定义组件，支持 CSS 变量和主题定制

### Requirement 6

**User Story:** 作为开发工程师，我希望有基础的路由配置，以便能够在不同的功能模块间导航。

#### Acceptance Criteria

1. WHEN 配置路由 THEN 系统 SHALL 在 router/index.ts 中设置基础路由
2. WHEN 定义页面路由 THEN 系统 SHALL 包含"仪表盘"、"元数据"和"客户关系"的路由配置
3. WHEN 支持模块化 THEN 系统 SHALL 支持按功能模块扩展路由配置
4. WHEN 确保导航 THEN 系统 SHALL 确保路由能够正确导航到对应的页面组件

### Requirement 7

**User Story:** 作为开发工程师，我希望有一个简单的API封装函数，以便能够统一管理数据加载逻辑。

#### Acceptance Criteria

1. WHEN 创建API工具 THEN 系统 SHALL 在 utils/api.ts 中提供 fetch 封装函数
2. WHEN 加载JSON数据 THEN 系统 SHALL 支持从 /mock/ 目录异步加载 JSON 数据
3. WHEN 处理错误 THEN 系统 SHALL 包含基本的错误处理机制
4. WHEN 统一接口 THEN 系统 SHALL 提供统一的数据加载接口供组件使用

### Requirement 8

**User Story:** 作为产品经理和开发工程师，我希望原型能够体现角色驱动的设计理念，以便不同角色的用户能够获得定制化的体验。

#### Acceptance Criteria

1. WHEN 设计用户界面 THEN 系统 SHALL 为不同角色提供定制化的视图和仪表盘
2. WHEN 管理权限 THEN 系统 SHALL 支持基于角色的访问控制
3. WHEN 展示数据 THEN 系统 SHALL 根据用户角色展示相关的业务数据
4. WHEN 优化体验 THEN 系统 SHALL 为每个角色提供符合其工作流程的操作界面

### Requirement 9

**User Story:** 作为项目团队成员，我希望有一个清晰的沟通协作机制，以便在原型开发过程中能够高效地讨论需求、收集反馈和迭代改进。

#### Acceptance Criteria

1. WHEN 讨论需求变更 THEN 系统 SHALL 支持通过规范化的文档更新流程来管理需求变更
2. WHEN 收集用户反馈 THEN 系统 SHALL 提供清晰的反馈收集渠道和记录机制
3. WHEN 进行设计评审 THEN 系统 SHALL 支持分阶段的评审流程（需求评审、设计评审、实现评审）
4. WHEN 协作开发 THEN 系统 SHALL 建立明确的角色分工和责任边界（产品经理、UI/UX设计师、前后端开发工程师）
5. WHEN 版本管理 THEN 系统 SHALL 通过Git等版本控制工具管理原型代码和文档的版本迭代
6. WHEN 知识传递 THEN 系统 SHALL 维护完整的项目文档，包括技术架构、业务流程、数据模型等关键信息

## 物料元数据管理模块需求

### Requirement 10

**User Story:** 作为业务用户，我希望能够浏览和查看物料分类层级结构，以便快速定位到我需要的物料类型。

#### Acceptance Criteria

1. WHEN 进入物料元数据管理页面 THEN 系统 SHALL 在左侧显示物料分类树形导航
2. WHEN 查看分类列表 THEN 系统 SHALL 显示分类名称、描述和物料数量统计
3. WHEN 分类有子分类 THEN 系统 SHALL 显示层级关系和展开/收起功能
4. WHEN 点击分类 THEN 系统 SHALL 高亮选中状态并触发右侧内容更新

### Requirement 11

**User Story:** 作为业务用户，我希望能够查看属于特定分类的物料列表，以便了解该分类下的所有物料信息。

#### Acceptance Criteria

1. WHEN 选择物料分类 THEN 系统 SHALL 在右侧主内容区显示该分类下的物料表格
2. WHEN 显示物料列表 THEN 系统 SHALL 包含物料名称、基础属性、变体数量和状态信息
3. WHEN 物料列表为空 THEN 系统 SHALL 显示友好的空状态提示信息
4. WHEN 物料数据加载中 THEN 系统 SHALL 显示加载状态指示器

### Requirement 12

**User Story:** 作为业务用户，我希望能够查看单个物料的详细信息和所有库存变体，以便全面了解物料的规格和库存情况。

#### Acceptance Criteria

1. WHEN 点击物料表格中的某一行 THEN 系统 SHALL 在下方或侧边显示物料详情面板
2. WHEN 显示物料详情 THEN 系统 SHALL 包含物料基础属性、描述信息和变体列表
3. WHEN 显示变体列表 THEN 系统 SHALL 以表格形式展示SKU、规格、库存、成本等信息
4. WHEN 变体库存不足 THEN 系统 SHALL 使用不同颜色的徽章标识库存状态

### Requirement 13

**User Story:** 作为业务用户，我希望有清晰的导航和状态指示，以便始终了解当前所在位置和操作状态。

#### Acceptance Criteria

1. WHEN 在物料元数据管理页面 THEN 系统 SHALL 在主内容区顶部显示面包屑导航
2. WHEN 选择分类和物料 THEN 系统 SHALL 在面包屑中显示完整路径，如"元数据 / 玻璃原片 / 6mm 透明浮法玻璃"
3. WHEN 数据加载中 THEN 系统 SHALL 显示加载状态指示器和友好的提示信息
4. WHEN 发生错误 THEN 系统 SHALL 显示明确的错误信息和建议的解决方案

### Requirement 14

**User Story:** 作为开发工程师，我希望有一个模块化的物料管理组件架构，以便能够高效地构建和维护物料管理界面。

#### Acceptance Criteria

1. WHEN 创建主视图组件 THEN 系统 SHALL 实现 MetadataView.vue 作为页面容器，负责整体布局和状态协调
2. WHEN 创建分类组件 THEN 系统 SHALL 实现 MaterialCategoryList.vue 渲染左侧分类导航，支持层级展示和选择
3. WHEN 创建物料表格组件 THEN 系统 SHALL 实现 MaterialTable.vue 显示物料列表，支持行选择和排序
4. WHEN 创建详情组件 THEN 系统 SHALL 实现 MaterialDetail.vue 和 MaterialVariantTable.vue 展示物料详情和变体信息

### Requirement 15

**User Story:** 作为开发工程师，我希望有一个高效的物料数据状态管理机制，以便能够统一管理物料数据和UI状态。

#### Acceptance Criteria

1. WHEN 创建状态管理 THEN 系统 SHALL 使用 Pinia store 管理物料分类、物料数据和选择状态
2. WHEN 加载数据 THEN 系统 SHALL 通过 store actions 异步从 JSON 文件加载元数据
3. WHEN 筛选数据 THEN 系统 SHALL 通过 store getters 根据选中分类筛选物料列表
4. WHEN 更新状态 THEN 系统 SHALL 通过 store actions 更新选中的分类ID和物料ID