# Design Document

## Overview

本设计文档基于需求文档，为玻璃深加工企业ERP原型系统的物料元数据管理功能提供详细的技术架构和实现方案。该功能是企业资源规划系统的核心模块，采用"分类 -> 物料 -> 变体"三层数据模型，通过现代前端技术栈构建可视化的物料主数据管理（MDM）界面。

### 物料元数据管理核心目标
- **数据关系可视化**：清晰展示物料分类、物料实例和库存变体的层级关系
- **三层主从式布局**：通过左侧分类导航、中间物料列表、右侧详情面板的布局设计，提供直观的数据浏览体验
- **数据驱动渲染**：基于现有的 materialCategories.json 和 materials.json 数据，动态生成界面组件
- **玻璃行业特性**：体现玻璃深加工行业的物料管理特点，包括厚度、尺寸、颜色等关键属性

## Architecture

### 技术架构

```mermaid
graph TB
    A[Browser] --> B[Vue 3 Application]
    B --> C[Vue Router]
    B --> D[Pinia Store]
    B --> E[Shadcn Vue Components]
    B --> F[API Layer]
    F --> G[Mock Data JSON Files]
    
    subgraph "Frontend Stack"
        B
        C
        D
        E
    end
    
    subgraph "Data Layer"
        F
        G
    end
    
    subgraph "UI Framework"
        E --> H[Tailwind CSS]
        E --> I[Reka UI]
        E --> J[Lucide Icons]
    end
```

### 物料元数据管理模块结构设计

专门针对物料元数据管理功能的组件组织结构：

```
src/
├── components/
│   ├── material/           # 物料管理业务组件
│   │   ├── MaterialCategoryList.vue    # 物料分类列表
│   │   ├── MaterialTable.vue           # 物料表格
│   │   ├── MaterialDetail.vue          # 物料详情
│   │   └── MaterialVariantTable.vue    # 库存变体表格
│   └── ui/                 # Shadcn Vue 基础组件
│       ├── Card.vue        # 卡片组件
│       ├── Badge.vue       # 徽章组件
│       ├── Breadcrumb.vue  # 面包屑导航
│       └── Table.vue       # 表格组件
├── stores/
│   └── metadata.ts         # 物料元数据状态管理
├── types/
│   └── material.ts         # 物料相关类型定义
├── views/
│   └── MetadataView.vue    # 物料元数据管理主视图
└── utils/
    └── api.ts              # API 工具函数
```

## Components and Interfaces

### 物料元数据管理组件设计

#### 1. 主视图组件

**MetadataView.vue** - 物料元数据管理主视图
- **职责**：作为物料管理页面的主容器，负责整体布局和状态协调
- **布局**：采用三层主从式布局（左侧分类导航 + 右侧主内容区）
- **功能**：
  - 获取所有物料分类和物料数据
  - 管理当前选中的分类ID和物料ID
  - 协调子组件间的数据传递和事件处理
  - 处理面包屑导航的更新

#### 2. 分类导航组件

**MaterialCategoryList.vue** - 物料分类列表
- **职责**：渲染左侧的分类导航栏，支持层级展示
- **输入 (Props)**：
  - `materialCategories: MaterialCategory[]` - 物料分类数组
  - `selectedCategoryId: string | null` - 当前选中的分类ID
- **输出 (Events)**：
  - `category-selected` - 分类选择事件
- **功能**：
  - 显示分类名称、描述和物料数量统计
  - 支持层级关系的展开/收起
  - 高亮显示选中状态

#### 3. 物料列表组件

**MaterialTable.vue** - 物料表格
- **职责**：显示属于特定分类的物料列表
- **输入 (Props)**：
  - `materials: Material[]` - 筛选后的物料数组
  - `selectedMaterialId: string | null` - 当前选中的物料ID
- **输出 (Events)**：
  - `material-selected` - 物料选择事件
- **功能**：
  - 以表格形式展示物料名称、基础属性、变体数量和状态
  - 支持行选择和排序
  - 处理空状态和加载状态的显示

#### 4. 物料详情组件

**MaterialDetail.vue** - 物料详情
- **职责**：展示单个物料的完整信息及其所有库存变体
- **输入 (Props)**：
  - `material: Material | null` - 完整的物料对象
- **功能**：
  - 显示物料基础属性和描述信息
  - 集成 MaterialVariantTable 子组件
  - 处理详情面板的显示/隐藏

#### 5. 变体表格组件

**MaterialVariantTable.vue** - 库存变体表格
- **职责**：以表格形式展示一个物料的所有库存变体
- **输入 (Props)**：
  - `variants: MaterialVariant[]` - 变体数组
- **功能**：
  - 显示SKU、规格属性、库存数量、成本等信息
  - 使用不同颜色的徽章标识库存状态
  - 支持变体数据的排序和筛选

### 组件接口设计

#### 物料元数据管理组件接口

```typescript
// types/material.ts
interface MaterialCategory {
  categoryId: string;
  categoryName: string;
  description: string;
  parentId: string | null;
  level: number;
  hasChildren: boolean;
  attributeSchema: AttributeSchema | null;
  materialCount: number;
  isSystemDefault: boolean;
  sortOrder: number;
}

interface AttributeSchema {
  baseAttributes: AttributeConfig[];
  variantAttributes: AttributeConfig[];
}

interface AttributeConfig {
  name: string;
  type: 'select' | 'number' | 'text';
  options?: string[];
  unit?: string;
  minValue?: number;
  maxValue?: number;
  description?: string;
  required?: boolean;
  defaultValue?: string;
  placeholder?: string;
}

interface Material {
  materialId: string;
  categoryId: string;
  displayName: string;
  attributes: Record<string, any>;
  variants: MaterialVariant[];
}

interface MaterialVariant {
  variantId: string;
  sku: string;
  displayName: string;
  variantAttributes: Record<string, any>;
  stock: number;
  unit: string;
  cost: number;
  weightKg?: number;
  areaSqm?: number;
  weightKgPerMeter?: number;
  supplier: string;
  leadTimeDays: number;
  isActive: boolean;
}
```

#### API 接口层

```typescript
// utils/api.ts
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// 物料元数据 API
export async function fetchMaterialCategories(): Promise<ApiResponse<MaterialCategory[]>> {
  const response = await fetch('/mock/metadata/materialCategories.json');
  const data = await response.json();
  return { data: data.materialCategories, success: true };
}

export async function fetchMaterials(): Promise<ApiResponse<Material[]>> {
  const response = await fetch('/mock/metadata/materials.json');
  const data = await response.json();
  return { data: data.materials, success: true };
}
```

## Data Models

### 物料元数据管理数据模型

基于现有的 materialCategories.json 和 materials.json 数据结构设计：

#### 1. 物料分类模型

物料分类定义了物料的属性结构模板，包含基础属性和变体属性的配置：

```typescript
// types/material.ts
interface MaterialCategory {
  categoryId: string;           // 分类唯一标识
  categoryName: string;         // 分类名称，如"普通浮法玻璃"
  description: string;          // 分类描述
  parentId: string | null;      // 父分类ID，支持层级结构
  level: number;                // 层级深度
  hasChildren: boolean;         // 是否有子分类
  attributeSchema: AttributeSchema | null;  // 属性模式定义
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  materialCount: number;        // 该分类下的物料数量
  isSystemDefault: boolean;     // 是否为系统默认分类
  sortOrder: number;            // 排序顺序
}

interface AttributeSchema {
  baseAttributes: AttributeConfig[];     // 基础属性：厚度、颜色、等级
  variantAttributes: AttributeConfig[];  // 变体属性：宽度、高度、长度
}

interface AttributeConfig {
  name: string;                 // 属性名称
  type: 'select' | 'number' | 'text';  // 属性类型
  options?: string[];           // 选项值（用于select类型）
  unit?: string;                // 单位，如"mm"
  minValue?: number;            // 最小值（用于number类型）
  maxValue?: number;            // 最大值（用于number类型）
  description?: string;         // 属性描述
  required?: boolean;           // 是否必填
  defaultValue?: string;        // 默认值
  placeholder?: string;         // 占位符文本
}
```

#### 2. 物料实例模型

物料实例是具体的物料，属于某个分类，包含基础属性值和多个库存变体：

```typescript
interface Material {
  materialId: string;           // 物料唯一标识
  categoryId: string;           // 所属分类ID
  displayName: string;          // 物料显示名称
  attributes: Record<string, any>;  // 基础属性值
  variants: MaterialVariant[];  // 库存变体列表
}

interface MaterialVariant {
  variantId: string;            // 变体唯一标识
  sku: string;                  // 库存单位编码
  displayName: string;          // 变体显示名称
  variantAttributes: Record<string, any>;  // 变体属性值
  stock: number;                // 库存数量
  unit: string;                 // 计量单位
  cost: number;                 // 成本价格
  weightKg?: number;            // 重量（公斤）
  areaSqm?: number;             // 面积（平方米）
  weightKgPerMeter?: number;    // 每米重量（用于型材）
  supplier: string;             // 供应商
  leadTimeDays: number;         // 交货周期（天）
  isActive: boolean;            // 是否活跃状态
}
```

#### 3. 玻璃行业特定数据模型

基于现有数据结构，针对玻璃深加工行业的特殊需求：

```typescript
// 玻璃原片物料变体
interface GlassSheetVariant extends MaterialVariant {
  thickness: number;            // 厚度（mm）
  color: string;                // 颜色：透明、茶色、蓝色等
  grade: string;                // 等级：建筑级、汽车级等
  width: number;                // 宽度（mm）
  height: number;               // 高度（mm）
  transparencyRate?: string;    // 透光率（用于超白玻璃）
}

// 型材物料变体
interface ProfileVariant extends MaterialVariant {
  crossSection: string;         // 截面规格：50x30mm等
  surfaceTreatment: string;     // 表面处理：阳极氧化、粉末喷涂等
  color: string;                // 颜色
  wallThickness: string;        // 壁厚
  length: number;               // 长度（mm）
}

// 五金配件物料变体
interface HardwareVariant extends MaterialVariant {
  type: string;                 // 类型：把手、合页、锁具等
  material: string;             // 材质：不锈钢、铝合金等
  surfaceTreatment: string;     // 表面处理：拉丝、抛光等
  specification: string;        // 规格型号
}

// 密封胶物料变体
interface SealantVariant extends MaterialVariant {
  type: string;                 // 类型：结构胶、耐候胶等
  color: string;                // 颜色：透明、白色、黑色等
  curingMethod: string;         // 固化方式：单组份、双组份
  packageSize: string;          // 包装规格：300ml、590ml、20kg等
}
```

#### 4. 状态管理设计

基于 Pinia 的物料元数据状态管理：

```typescript
// stores/metadata.ts
interface MetadataState {
  materialCategories: MaterialCategory[];
  materials: Material[];
  selectedCategoryId: string | null;
  selectedMaterialId: string | null;
  loading: boolean;
  error: string | null;
}

interface MetadataActions {
  fetchMetadata(): Promise<void>;
  selectCategory(categoryId: string): void;
  selectMaterial(materialId: string): void;
  clearSelection(): void;
}

interface MetadataGetters {
  materialsForSelectedCategory: Material[];
  selectedMaterial: Material | null;
  selectedCategory: MaterialCategory | null;
  categoryTree: MaterialCategory[];
  materialVariantCount: number;
}

export const useMetadataStore = defineStore('metadata', {
  state: (): MetadataState => ({
    materialCategories: [],
    materials: [],
    selectedCategoryId: null,
    selectedMaterialId: null,
    loading: false,
    error: null
  }),

  getters: {
    materialsForSelectedCategory: (state) => {
      if (!state.selectedCategoryId) return [];
      return state.materials.filter(
        material => material.categoryId === state.selectedCategoryId
      );
    },

    selectedMaterial: (state) => {
      if (!state.selectedMaterialId) return null;
      return state.materials.find(
        material => material.materialId === state.selectedMaterialId
      ) || null;
    },

    selectedCategory: (state) => {
      if (!state.selectedCategoryId) return null;
      return state.materialCategories.find(
        category => category.categoryId === state.selectedCategoryId
      ) || null;
    },

    categoryTree: (state) => {
      // 构建分类树形结构
      const rootCategories = state.materialCategories.filter(
        category => category.parentId === null
      );
      return buildCategoryTree(rootCategories, state.materialCategories);
    }
  },

  actions: {
    async fetchMetadata() {
      this.loading = true;
      this.error = null;
      
      try {
        const [categoriesResponse, materialsResponse] = await Promise.all([
          fetchMaterialCategories(),
          fetchMaterials()
        ]);
        
        this.materialCategories = categoriesResponse.data;
        this.materials = materialsResponse.data;
      } catch (error) {
        this.error = error instanceof Error ? error.message : '数据加载失败';
      } finally {
        this.loading = false;
      }
    },

    selectCategory(categoryId: string) {
      this.selectedCategoryId = categoryId;
      this.selectedMaterialId = null; // 清除物料选择
    },

    selectMaterial(materialId: string) {
      this.selectedMaterialId = materialId;
    },

    clearSelection() {
      this.selectedCategoryId = null;
      this.selectedMaterialId = null;
    }
  }
});
```

#### 5. 基于变体的库存管理模型

```typescript
// types/variant-inventory.ts
interface MaterialVariantStock {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  locationId: string;
  location: StockLocation;
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  unitCost: number;
  totalValue: number;
  lastMovementDate: string;
  reorderPoint: number; // 物料变体级别的安全库存
  maxStock: number; // 物料变体级别的最大库存
  lotNumbers: string[]; // 批次号
  expiryDate?: string; // 过期日期（如适用）
}

interface MaterialVariantStockMove {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  quantity: number;
  sourceLocationId: string;
  destinationLocationId: string;
  moveType: 'receipt' | 'delivery' | 'internal' | 'adjustment' | 'scrap';
  reference: string;
  state: 'draft' | 'confirmed' | 'done' | 'cancelled';
  unitCost: number;
  totalCost: number;
  scheduledDate: string;
  effectiveDate?: string;
  relatedOrderId?: string;
  cuttingPlanId?: string; // 关联的切割计划
}

interface WasteMaterialVariantStock {
  id: string;
  originalMaterialVariantId: string;
  originalMaterialVariant: MaterialVariant;
  currentDimensions: Dimensions;
  remainingArea: number; // 对于玻璃
  remainingLength: number; // 对于型材
  quality: 'good' | 'damaged' | 'unusable';
  locationId: string;
  createdDate: string;
  lastUsedDate?: string;
  potentialUses: PotentialUse[]; // 可能的用途匹配
}

interface PotentialUse {
  orderItemId: string;
  requiredDimensions: Dimensions;
  matchScore: number; // 匹配度评分
  wasteAfterUse: number;
}

interface MaterialVariantReorderRule {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  minQuantity: number;
  maxQuantity: number;
  reorderQuantity: number;
  leadTime: number;
  supplierId: string;
  isActive: boolean;
  seasonalAdjustment?: SeasonalAdjustment[];
}

interface SeasonalAdjustment {
  month: number;
  adjustmentFactor: number; // 季节性调整系数
}

// 变体库存预警
interface MaterialVariantStockAlert {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  alertType: 'low_stock' | 'overstock' | 'no_stock' | 'expiring' | 'slow_moving';
  currentQuantity: number;
  thresholdQuantity: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  createdDate: string;
  isResolved: boolean;
  resolvedDate?: string;
}
```

#### 4. MTO生产管理模型

```typescript
// types/mto-manufacturing.ts
interface CustomerOrder {
  id: string;
  customerName: string;
  orderDate: string;
  deliveryDate: string;
  items: OrderItem[];
  status: 'draft' | 'confirmed' | 'in_production' | 'completed' | 'delivered';
  totalValue: number;
  specialRequirements?: string;
}

interface OrderItem {
  id: string;
  productType: 'window' | 'door' | 'curtain_wall' | 'partition' | 'custom';
  specifications: GlassSpecification;
  quantity: number;
  unitPrice: number;
  processRoute: ProcessRoute;
  materialRequirements: MaterialRequirement[];
}

interface GlassSpecification {
  width: number;
  height: number;
  thickness: number;
  glassType: 'float' | 'tempered' | 'laminated' | 'insulated' | 'coated';
  edgeWork: 'polished' | 'ground' | 'beveled';
  holes?: HoleSpecification[];
  coating?: CoatingSpecification;
  specialProcessing?: string[];
}

interface ProcessRoute {
  id: string;
  name: string;
  steps: ProcessStep[];
  estimatedDuration: number;
  qualityCheckpoints: QualityCheckpoint[];
}

interface ProcessStep {
  id: string;
  name: string;
  workCenter: string;
  operation: 'cutting' | 'tempering' | 'laminating' | 'coating' | 'drilling' | 'polishing';
  duration: number;
  setupTime: number;
  parameters: Record<string, any>;
  nextSteps: string[];
}

interface MaterialVariantOptimizationInput {
  requiredPieces: RequiredPiece[]; // 客户订单需要的玻璃片
  availableMaterialVariants: GlassSheetVariant[]; // 可用的玻璃原片物料变体
  optimizationGoal: 'minimize_waste' | 'minimize_cost' | 'minimize_variants' | 'balanced';
  constraints: OptimizationConstraints;
}

interface RequiredPiece {
  id: string;
  width: number;
  height: number;
  thickness: number;
  color: string;
  grade: string;
  quantity: number;
  orderItemId: string;
  allowRotation: boolean;
}

interface OptimizationConstraints {
  maxVariantsToUse: number; // 最多使用的变体数量
  minUtilizationRate: number; // 最低利用率要求
  preferredVariants: string[]; // 优先使用的变体ID
  excludedVariants: string[]; // 排除的变体ID
}

interface MaterialVariantOptimizationResult {
  selectedMaterialVariants: SelectedMaterialVariant[];
  totalCost: number;
  totalWasteArea: number;
  overallUtilizationRate: number;
  variantCuttingPlans: VariantCuttingPlan[];
  alternativeOptions: AlternativeOption[];
}

interface SelectedMaterialVariant {
  materialVariantId: string;
  materialVariant: GlassSheetVariant;
  quantityUsed: number;
  totalCost: number;
  utilizationRate: number;
  wasteArea: number;
}

interface MaterialVariantCuttingPlan {
  materialVariantId: string;
  sheetIndex: number; // 第几张原片
  pieces: CuttingPiece[];
  wasteAreas: WasteArea[];
}

interface CuttingPiece {
  pieceId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  orderItemId: string;
  requiredPieceId: string;
}

interface WasteArea {
  x: number;
  y: number;
  width: number;
  height: number;
  area: number;
  isReusable: boolean; // 是否可作为余料重复利用
}

// 型材切割优化
interface ProfileMaterialOptimizationInput {
  requiredLengths: RequiredLength[];
  availableProfileMaterialVariants: ProfileVariant[];
  optimizationGoal: 'minimize_waste' | 'minimize_cost';
}

interface RequiredLength {
  id: string;
  length: number;
  quantity: number;
  orderItemId: string;
  profileTemplateId: string;
}

interface ProfileMaterialOptimizationResult {
  selectedProfileMaterialVariants: SelectedProfileMaterialVariant[];
  cuttingPlans: ProfileCuttingPlan[];
  totalWasteLength: number;
  utilizationRate: number;
}

interface ProfileMaterialCuttingPlan {
  materialVariantId: string;
  profileIndex: number;
  cuts: ProfileCut[];
  wasteLength: number;
}

interface ProfileCut {
  cutId: string;
  startPosition: number;
  length: number;
  orderItemId: string;
  requiredLengthId: string;
}
```

## Error Handling

### 错误处理策略

#### 1. API 错误处理

```typescript
// utils/errorHandler.ts
interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

class ErrorHandler {
  static handleApiError(error: ApiError): void {
    switch (error.code) {
      case 'NETWORK_ERROR':
        this.showNetworkError();
        break;
      case 'VALIDATION_ERROR':
        this.showValidationError(error.details);
        break;
      case 'PERMISSION_DENIED':
        this.showPermissionError();
        break;
      default:
        this.showGenericError(error.message);
    }
  }

  static showNetworkError(): void {
    // 显示网络错误提示
  }

  static showValidationError(details: any): void {
    // 显示表单验证错误
  }
}
```

#### 2. 组件错误边界

```typescript
// components/ErrorBoundary.vue
interface ErrorInfo {
  componentName: string;
  errorMessage: string;
  stackTrace: string;
  timestamp: string;
}

// 使用 Vue 3 的 errorHandler 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  console.error('Global error:', error);
  // 发送错误报告到监控系统
  ErrorReporter.report({
    error,
    component: instance?.$options.name,
    info
  });
};
```

#### 3. 用户友好的错误提示

```typescript
// composables/useNotification.ts
export function useNotification() {
  const showError = (message: string, details?: string) => {
    // 使用 Shadcn Vue 的 Toast 组件显示错误
  };

  const showSuccess = (message: string) => {
    // 显示成功提示
  };

  const showWarning = (message: string) => {
    // 显示警告提示
  };

  return {
    showError,
    showSuccess,
    showWarning
  };
}
```

## Testing Strategy

### 原型验证策略

作为高保真原型项目，测试重点关注功能演示和业务流程验证，而非完整的质量保证。

#### 1. 功能验证测试

重点验证核心业务组件的基本功能：

```typescript
// tests/prototype/order-configurator.test.ts
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import OrderConfigurator from '@/components/OrderConfigurator.vue';

describe('OrderConfigurator Prototype', () => {
  it('displays glass specification form', () => {
    const wrapper = mount(OrderConfigurator);
    expect(wrapper.find('[data-testid="glass-spec-form"]').exists()).toBe(true);
  });

  it('calculates material requirements', async () => {
    const wrapper = mount(OrderConfigurator);
    await wrapper.find('[data-testid="calculate-btn"]').trigger('click');
    expect(wrapper.find('[data-testid="material-list"]').exists()).toBe(true);
  });
});
```

#### 2. 业务流程验证

验证关键业务场景的完整流程：

```typescript
// tests/prototype/mto-workflow.test.ts
import { test, expect } from '@playwright/test';

test('MTO workflow demonstration', async ({ page }) => {
  await page.goto('/');
  
  // 1. 订单配置
  await page.click('[data-testid="new-order"]');
  await page.fill('[data-testid="glass-width"]', '1200');
  await page.fill('[data-testid="glass-height"]', '800');
  await page.selectOption('[data-testid="glass-type"]', 'tempered');
  
  // 2. 工艺路线生成
  await page.click('[data-testid="generate-route"]');
  await expect(page.locator('[data-testid="process-route"]')).toBeVisible();
  
  // 3. 切割优化
  await page.click('[data-testid="optimize-cutting"]');
  await expect(page.locator('[data-testid="cutting-plan"]')).toBeVisible();
  
  // 4. 生产排程
  await page.click('[data-testid="schedule-production"]');
  await expect(page.locator('[data-testid="gantt-chart"]')).toBeVisible();
});
```

#### 3. 演示数据验证

确保演示数据的完整性和一致性：

```typescript
// tests/prototype/demo-data.test.ts
import { describe, it, expect } from 'vitest';
import { loadMockData } from '@/utils/api';

describe('Demo Data Validation', () => {
  it('loads complete glass industry data', async () => {
    const materials = await loadMockData('/materials');
    expect(materials.data).toHaveLength(expect.any(Number));
    expect(materials.data[0]).toHaveProperty('glassType');
    expect(materials.data[0]).toHaveProperty('thickness');
  });

  it('provides realistic order scenarios', async () => {
    const orders = await loadMockData('/orders');
    expect(orders.data).toContainEqual(
      expect.objectContaining({
        productType: expect.stringMatching(/window|door|curtain_wall/),
        specifications: expect.objectContaining({
          width: expect.any(Number),
          height: expect.any(Number)
        })
      })
    );
  });
});
```

### 原型演示场景

#### 核心演示流程

1. **角色切换演示**：展示不同用户角色的界面差异
2. **订单配置演示**：从客户需求到产品规格的转换过程
3. **工艺优化演示**：切割方案和工艺路线的智能生成
4. **生产监控演示**：实时生产进度和质量状态展示
5. **异常处理演示**：订单变更和生产异常的处理流程

#### 演示数据设计原则

- **真实性**：基于实际玻璃深加工企业的业务场景
- **完整性**：覆盖从订单到交付的完整业务链条
- **多样性**：包含不同类型的产品和工艺要求
- **关联性**：数据间保持逻辑一致性和业务关联性

## 原型特色设计

### MTO业务流程可视化

#### 订单驱动的产品配置流程
```mermaid
graph LR
    A[客户需求] --> B[规格配置]
    B --> C[工艺选择]
    C --> D[材料计算]
    D --> E[成本估算]
    E --> F[交期确认]
    F --> G[订单确认]
    G --> H[生产排程]
```

#### 玻璃深加工工艺流程
```mermaid
graph TB
    A[原片玻璃] --> B[切割优化]
    B --> C[边部处理]
    C --> D{工艺分支}
    D -->|钢化| E[钢化炉]
    D -->|夹胶| F[夹胶线]
    D -->|中空| G[中空线]
    E --> H[质量检测]
    F --> H
    G --> H
    H --> I[包装入库]
```

### 关键演示场景设计

#### 场景1：多变体玻璃原片切割优化
- **背景**：某商业大厦幕墙项目，需要1000+块不同规格玻璃，包含6mm、8mm、12mm等不同厚度
- **演示重点**：
  - 智能选择最优的原片变体组合（3300x2140、3660x2440等规格）
  - 多变体并行切割优化算法
  - 实时对比不同变体组合的成本和浪费率
- **技术亮点**：变体组合算法可视化、材料利用率实时计算

#### 场景2：型材变体开料优化
- **背景**：定制化门窗项目，需要多种长度的铝合金型材
- **演示重点**：
  - 从6000mm、4000mm等不同长度的型材变体中选择最优组合
  - 开料方案的智能生成和优化
  - 余料长度的最小化处理
- **技术亮点**：一维切割优化算法、余料重复利用匹配

#### 场景3：变体库存智能补货
- **背景**：基于历史订单分析，预测不同变体的需求量
- **演示重点**：
  - 按变体分析库存周转率和安全库存
  - 智能推荐补货的变体规格和数量
  - 季节性需求的变体库存调整
- **技术亮点**：变体需求预测算法、库存优化建议

#### 场景4：余料变体再利用匹配
- **背景**：利用历史切割产生的余料变体满足新订单需求
- **演示重点**：
  - 余料变体的智能匹配和推荐
  - 余料使用后的二次余料管理
  - 成本效益分析和环保效益展示
- **技术亮点**：余料匹配算法、循环利用追踪

### 用户角色体验设计

#### 销售工程师视角
- **核心功能**：订单配置器、报价计算器、交期查询
- **界面特点**：简洁直观、快速响应、移动端适配
- **数据展示**：客户历史、产品目录、价格体系

#### 生产调度员视角
- **核心功能**：生产排程、资源分配、进度监控
- **界面特点**：甘特图、看板视图、实时更新
- **数据展示**：设备状态、人员安排、物料准备

#### 质量工程师视角
- **核心功能**：检测标准、质量记录、异常追踪
- **界面特点**：检测流程、数据录入、报告生成
- **数据展示**：质量趋势、缺陷分析、改进建议

### 技术创新展示

#### 变体管理智能算法可视化
- **多变体切割优化**：实时展示不同变体组合的切割方案对比，包括成本、浪费率、利用率等关键指标
- **变体选择算法**：动态展示系统如何从众多变体中选择最优组合的决策过程
- **余料变体匹配**：可视化展示余料变体与新需求的智能匹配过程和匹配度评分
- **变体库存优化**：展示基于历史数据和需求预测的变体库存优化建议

#### 实时数据流
- **生产监控**：模拟实时设备数据和生产状态
- **库存变化**：动态展示物料消耗和库存变化
- **订单状态**：实时更新订单进度和交付状态

## 物料变体管理技术实现

### 物料数据结构设计

物料数据被拆分为两个核心文件，分别定义了分类的“结构”和物料的“实例”。

#### 1. `materialCategories.json` - 物料分类结构

此文件定义了物料的类别及其属性框架（Schema）。

```json
{
  "materialCategories": [
    {
      "categoryId": "CAT_RAW_GLASS",
      "categoryName": "玻璃原片",
      "description": "用于切割、钢化、夹胶等深加工的基础玻璃板材。",
      "attributeSchema": {
        "baseAttributes": [
          {"name": "厚度", "type": "select", "options": ["4mm", "5mm", "6mm", "8mm", "10mm", "12mm"], "description": "玻璃的标称厚度"},
          {"name": "颜色", "type": "select", "options": ["透明", "超白", "茶色", "蓝色", "绿色", "灰色"], "description": "玻璃本体颜色"},
          {"name": "等级", "type": "select", "options": ["汽车级", "建筑级", "制镜级"], "description": "玻璃原片质量等级"}
        ],
        "variantAttributes": [
          {"name": "宽度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 3660, "description": "标准库存尺寸的宽度"},
          {"name": "高度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 2440, "description": "标准库存尺寸的高度"}
        ]
      }
    },
    {
      "categoryId": "CAT_PROFILE",
      "categoryName": "铝合金型材",
      "description": "用于门窗框架、隔断等的铝合金型材。",
      "attributeSchema": {
        "baseAttributes": [
          {"name": "截面规格", "type": "select", "options": ["50x30mm", "60x40mm", "80x50mm"]},
          {"name": "表面处理", "type": "select", "options": ["阳极氧化", "粉末喷涂", "木纹转印"]},
          {"name": "颜色", "type": "text"},
          {"name": "壁厚", "type": "select", "options": ["1.2mm", "1.4mm", "1.6mm", "2.0mm"]}
        ],
        "variantAttributes": [
          {"name": "长度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 6000}
        ]
      }
    }
  ]
}
```

#### 2. `materials.json` - 物料实例数据

此文件包含所有具体的物料及其库存变体，通过 `categoryId` 关联到分类。

```json
{
  "materials": [
    {
      "materialId": "BM_FLOAT_CLEAR_6MM",
      "categoryId": "CAT_RAW_GLASS",
      "displayName": "6mm 透明浮法玻璃 (建筑级)",
      "attributes": {
        "厚度": "6mm",
        "颜色": "透明",
        "等级": "建筑级"
      },
      "variants": [
        {
          "variantId": "VAR_GLASS_6MM_CLEAR_3300x2140",
          "sku": "GLASS-FL-CL-6-3300-2140",
          "displayName": "6mm透明浮法玻璃 3300x2140mm",
          "variantAttributes": {
            "宽度": 3300,
            "高度": 2140
          },
          "stock": 120,
          "unit": "片",
          "cost": 85.50,
          "weightKg": 33.8,
          "areaSqm": 7.062,
          "supplier": "信义玻璃",
          "leadTimeDays": 7,
          "isActive": true
        }
      ]
    },
    {
      "materialId": "BM_ALU_50x30_POWDER_WHITE",
      "categoryId": "CAT_PROFILE",
      "displayName": "50x30mm 粉末喷涂（白色）铝型材 1.4mm",
      "attributes": {
        "截面规格": "50x30mm",
        "表面处理": "粉末喷涂",
        "颜色": "白色",
        "壁厚": "1.4mm"
      },
      "variants": [
        {
          "variantId": "VAR_PROFILE_50x30_WHITE_6000",
          "sku": "PROF-AL-5030-PW-WH-14-6000",
          "displayName": "50x30mm白色铝型材 6000mm长",
          "variantAttributes": {
            "长度": 6000
          },
          "stock": 250,
          "unit": "支",
          "cost": 28.50,
          "weightKgPerMeter": 0.475,
          "supplier": "凤铝铝业",
          "leadTimeDays": 14,
          "isActive": true
        }
      ]
    }
  ]
}
```

### 物料变体优化算法核心逻辑

#### 玻璃切割物料变体选择算法
```typescript
class GlassMaterialVariantOptimizer {
  optimize(input: MaterialVariantOptimizationInput): MaterialVariantOptimizationResult {
    // 1. 按需求分组（相同厚度、颜色、等级）
    const groupedRequirements = this.groupRequirementsBySpecs(input.requiredPieces);
    
    // 2. 为每组需求筛选合适的物料变体
    const suitableMaterialVariants = this.filterSuitableMaterialVariants(
      groupedRequirements, 
      input.availableMaterialVariants
    );
    
    // 3. 生成物料变体组合方案
    const combinations = this.generateMaterialVariantCombinations(
      groupedRequirements, 
      suitableMaterialVariants
    );
    
    // 4. 评估每种组合方案
    const evaluatedCombinations = combinations.map(combo => 
      this.evaluateCombination(combo, input.optimizationGoal)
    );
    
    // 5. 选择最优方案
    return this.selectBestCombination(evaluatedCombinations);
  }

  private evaluateCombination(combination: MaterialVariantCombination, goal: string): EvaluationResult {
    const cuttingPlans = combination.materialVariants.map(materialVariant => 
      this.generateCuttingPlan(materialVariant, combination.requirements)
    );
    
    return {
      totalCost: this.calculateTotalCost(cuttingPlans),
      totalWaste: this.calculateTotalWaste(cuttingPlans),
      utilizationRate: this.calculateUtilizationRate(cuttingPlans),
      materialVariantCount: combination.materialVariants.length,
      score: this.calculateScore(cuttingPlans, goal)
    };
  }
}
```

### 物料变体库存管理策略

#### 智能补货算法
```typescript
class MaterialVariantReplenishmentManager {
  generateReplenishmentPlan(materialVariants: MaterialVariant[]): ReplenishmentPlan[] {
    return materialVariants.map(materialVariant => {
      const demandForecast = this.forecastDemand(materialVariant);
      const currentStock = materialVariant.stockQuantity;
      const safetyStock = this.calculateSafetyStock(materialVariant);
      const optimalOrderQuantity = this.calculateEOQ(materialVariant);
      
      if (currentStock <= materialVariant.reorderPoint) {
        return {
          materialVariantId: materialVariant.id,
          recommendedQuantity: optimalOrderQuantity,
          urgency: this.calculateUrgency(currentStock, demandForecast),
          expectedDelivery: this.calculateDeliveryDate(materialVariant.leadTime),
          costImpact: optimalOrderQuantity * materialVariant.cost
        };
      }
      
      return null;
    }).filter(plan => plan !== null);
  }
}
```

这个优化后的设计文档完整体现了玻璃深加工企业基于**物料变体**的管理特性，包括：

1. **完整的物料变体数据模型**：支持基础属性和变体属性的分离管理，区分物料和产品的概念
2. **智能的物料变体选择算法**：针对玻璃原片和型材物料的切割优化
3. **精细的物料变体库存管理**：支持物料变体级别的库存控制和补货策略
4. **实用的演示场景**：展示物料变体管理在MTO生产模式中的应用价值

这样的设计能够为开发工程师提供清晰的技术实现指导，准确体现了玻璃深加工企业中**物料**（原材料）采用变体管理、**产品**（成品）按订单定制的核心业务特性。