# Implementation Plan

- [x] 1. 项目基础架构搭建
  - 使用 pnpm + Vite 初始化 Vue 3 + TypeScript 项目
  - 配置 vite.config.ts 支持路径别名和 Tailwind CSS
  - 配置 tsconfig.json 支持路径别名
  - 安装核心依赖：vue-router、pinia、tailwindcss、shadcn-vue、lucide-vue-next
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 2. Shadcn Vue 组件系统配置
  - 初始化 shadcn-vue CLI 配置
  - 配置 components.json 文件
  - 配置 Tailwind CSS 全局样式文件
  - 安装基础 UI 组件（button、card、table、form 等）
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 3. 项目目录结构创建
  - 创建完整的项目目录结构（assets、components、router、store、types、utils、views）
  - 按功能模块划分 views 目录（crm、inventory、mes、procurement、quality）
  - 创建 components/layout 和 components/ui 目录结构
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 4. Mock 数据系统搭建
  - 创建 /public/mock/ 目录结构
  - 按业务模块创建 JSON 数据文件目录（common、metadata、crm、inventory、mes、procurement、quality）
  - 实现 utils/api.ts 中的 fetch 封装函数
  - 创建统一的数据加载接口和错误处理机制
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 7.1, 7.2, 7.3, 7.4_

- [x] 5. 玻璃行业物料数据模型设计
  - 将物料模型重构为“分类+实例”的分离式结构
  - 创建 types/material.ts 定义 MaterialCategory, Material, MaterialVariant 等核心类型
  - 在 MaterialCategory 中定义属性结构 (Attribute Schema)
  - 在 Material 中通过 categoryId 关联分类，并存储具体物料实例及其变体
  - _Requirements: 4.2, 4.3, 4.4_

- [x] 6. 玻璃行业示例数据创建
  - 创建 users.json 包含不同角色用户数据（销售工程师、生产调度员、质量工程师等）
  - 创建 materialCategory.json 包含玻璃行业物料分类数据
  - 创建 material.json 包含玻璃原片和型材的物料变体数据
  - 确保数据符合玻璃深加工行业特性和 MTO 模式需求
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 7. 核心布局组件开发
  - 实现 AppLayout.vue 主布局组件，集成 Shadcn Vue Sidebar
  - 实现 AppSidebar.vue 侧边栏组件，支持角色驱动的菜单显示
  - 实现 AppHeader.vue 顶部导航组件，包含用户信息和通知功能
  - 确保布局组件支持响应式设计和移动端适配
  - _Requirements: 8.1, 8.2, 8.3, 8.4_

- [x] 8. 路由系统配置
  - 配置 router/index.ts 基础路由
  - 实现"仪表盘"、"元数据管理"、"客户关系"页面路由
  - 支持按功能模块扩展路由配置
  - 确保路由能够正确导航到对应页面组件
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 9. Pinia 状态管理配置
  - 配置 store/index.ts 基础状态管理
  - 实现用户状态管理（当前登录用户、角色权限）
  - 实现全局状态管理（当前选中的物料变体、订单状态等）
  - 支持状态持久化和跨组件数据共享
  - _Requirements: 8.1, 8.2, 8.3_

## 物料元数据管理模块实现

- [ ] 10. 物料元数据类型定义和API工具
- [ ] 10.1 创建物料相关类型定义
  - 在 src/types/material.ts 中定义 MaterialCategory, Material, MaterialVariant 接口
  - 定义 AttributeSchema, AttributeConfig 等属性配置接口
  - 创建玻璃行业特定的变体接口（GlassSheetVariant, ProfileVariant 等）
  - _Requirements: 14.1, 14.2, 14.3, 14.4_

- [ ] 10.2 实现物料元数据API工具函数
  - 在 src/utils/api.ts 中实现 fetchMaterialCategories() 函数
  - 实现 fetchMaterials() 函数，支持从 JSON 文件异步加载数据
  - 添加基本的错误处理机制和统一的响应格式
  - _Requirements: 14.1, 14.2, 14.3, 14.4_

- [ ] 11. 物料元数据状态管理
- [ ] 11.1 创建 Pinia metadata store
  - 在 src/stores/metadata.ts 中实现 useMetadataStore
  - 定义 state：materialCategories, materials, selectedCategoryId, selectedMaterialId, loading, error
  - 实现 actions：fetchMetadata(), selectCategory(), selectMaterial(), clearSelection()
  - _Requirements: 15.1, 15.2, 15.3, 15.4_

- [ ] 11.2 实现状态管理的 getters
  - 实现 materialsForSelectedCategory getter，根据选中分类筛选物料
  - 实现 selectedMaterial getter，获取当前选中的物料详情
  - 实现 selectedCategory getter，获取当前选中的分类信息
  - 实现 categoryTree getter，构建分类树形结构
  - _Requirements: 15.1, 15.2, 15.3, 15.4_

- [ ] 12. 物料分类导航组件
- [ ] 12.1 实现 MaterialCategoryList.vue 组件
  - 创建 src/components/material/MaterialCategoryList.vue
  - 实现左侧分类树形导航，支持层级展示和展开/收起
  - 显示分类名称、描述和物料数量统计
  - 处理分类选择事件，高亮显示选中状态
  - _Requirements: 10.1, 10.2, 10.3, 10.4_

- [ ] 12.2 集成分类导航的交互功能
  - 实现分类点击选择功能，触发 category-selected 事件
  - 支持键盘导航和无障碍访问
  - 添加加载状态和空状态的处理
  - _Requirements: 10.1, 10.2, 10.3, 10.4_

- [ ] 13. 物料列表表格组件
- [ ] 13.1 实现 MaterialTable.vue 组件
  - 创建 src/components/material/MaterialTable.vue
  - 以表格形式展示物料名称、基础属性、变体数量和状态信息
  - 支持行选择和基本的排序功能
  - 处理物料选择事件，触发 material-selected 事件
  - _Requirements: 11.1, 11.2, 11.3, 11.4_

- [ ] 13.2 完善物料表格的状态处理
  - 实现空状态提示信息的显示
  - 添加加载状态指示器
  - 支持表格数据的动态更新
  - _Requirements: 11.1, 11.2, 11.3, 11.4_

- [ ] 14. 物料详情和变体表格组件
- [ ] 14.1 实现 MaterialDetail.vue 组件
  - 创建 src/components/material/MaterialDetail.vue
  - 显示物料基础属性和描述信息
  - 集成 MaterialVariantTable 子组件
  - 处理详情面板的显示/隐藏逻辑
  - _Requirements: 12.1, 12.2, 12.3, 12.4_

- [ ] 14.2 实现 MaterialVariantTable.vue 组件
  - 创建 src/components/material/MaterialVariantTable.vue
  - 以表格形式展示SKU、规格属性、库存数量、成本等信息
  - 使用不同颜色的徽章标识库存状态（正常、不足、缺货）
  - 支持变体数据的排序和基本筛选
  - _Requirements: 12.1, 12.2, 12.3, 12.4_

- [ ] 15. 物料元数据管理主视图
- [ ] 15.1 实现 MetadataView.vue 主视图组件
  - 创建 src/views/MetadataView.vue
  - 采用三层主从式布局（左侧分类导航 + 右侧主内容区）
  - 集成所有物料管理子组件
  - 处理组件间的数据传递和事件协调
  - _Requirements: 14.1, 14.2, 14.3, 14.4_

- [ ] 15.2 实现面包屑导航和状态指示
  - 在主内容区顶部添加面包屑导航组件
  - 显示完整的导航路径，如"元数据 / 玻璃原片 / 6mm 透明浮法玻璃"
  - 添加数据加载状态指示器和错误信息显示
  - _Requirements: 13.1, 13.2, 13.3, 13.4_

- [ ] 16. 响应式布局和用户体验优化
- [ ] 16.1 实现响应式布局适配
  - 确保三层主从式布局在桌面端的完整显示
  - 实现平板设备的布局自动调整
  - 支持移动设备的堆叠布局和导航切换
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 16.2 完善用户交互体验
  - 优化组件的加载性能和数据更新流畅性
  - 添加友好的用户提示和操作反馈
  - 实现键盘快捷键和无障碍访问支持
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 17. 路由配置和页面集成
- [ ] 17.1 配置物料元数据管理路由
  - 在 src/router/index.ts 中添加 /metadata/materials 路由
  - 配置路由指向 MetadataView.vue 组件
  - 确保路由能够正确导航到物料管理页面
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 17.2 集成到主导航菜单
  - 在 AppSidebar.vue 中添加"物料元数据"菜单项
  - 确保菜单能够正确导航到物料管理功能
  - 添加菜单图标和适当的视觉标识
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 18. 数据驱动渲染和玻璃行业特性
- [ ] 18.1 实现基于现有数据的动态渲染
  - 确保组件能够正确读取 materialCategories.json 和 materials.json
  - 实现基于 attributeSchema 的动态表格列生成
  - 支持不同物料类型的属性展示（玻璃、型材、五金、密封胶）
  - _Requirements: 15.1, 15.2, 15.3, 15.4_

- [ ] 18.2 体现玻璃行业特性
  - 正确显示厚度、尺寸、颜色、供应商等行业特定属性
  - 实现玻璃面积计算和型材重量计算的显示
  - 添加行业术语的友好显示和单位转换
  - _Requirements: 15.1, 15.2, 15.3, 15.4_

- [ ] 19. 物料元数据管理功能测试
- [ ] 19.1 组件功能验证测试
  - 测试分类导航的选择和展开/收起功能
  - 验证物料表格的数据显示和行选择功能
  - 测试物料详情和变体表格的数据展示
  - _Requirements: 10.1, 11.1, 12.1_

- [ ] 19.2 数据流和状态管理测试
  - 验证 Pinia store 的数据加载和状态更新
  - 测试组件间的数据传递和事件处理
  - 确保面包屑导航的正确更新
  - _Requirements: 15.1, 15.2, 15.3, 15.4_

- [ ] 20. 物料元数据管理用户体验优化
- [ ] 20.1 界面交互优化
  - 优化三层主从式布局的视觉效果和交互流畅性
  - 完善加载状态、空状态和错误状态的用户提示
  - 添加适当的动画效果和过渡效果
  - _Requirements: 13.1, 13.2, 13.3, 13.4_

- [ ] 20.2 性能优化和错误处理
  - 优化大量数据的渲染性能
  - 实现组件的懒加载和虚拟滚动（如需要）
  - 完善错误边界和异常处理机制
  - _Requirements: 13.1, 13.2, 13.3, 13.4_