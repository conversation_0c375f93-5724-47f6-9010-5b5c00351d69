# 产品结构管理系统需求文档

## 简介

产品结构管理系统是玻璃深加工企业ERP系统的核心模块，用于管理产品的层级结构、组件关系、物料映射和版本控制。系统基于"组件(Component)→构件(Assembly)→产品结构(ProductStructure)"的三层架构，支持参数化设计、约束管理、工艺定义和配置管理，满足MTO（按单生产）模式下的个性化定制需求。

**核心架构：**
- **组件(Component)**：最基础的设计单元，定义参数、约束、工艺要求和物料分类映射
- **构件(Assembly)**：组件的集合，定义装配关系、装配工艺和质量要求
- **产品结构(ProductStructure)**：完整的产品定义，包含根构件、配置选项和版本管理

系统为生产、采购、质量管理等业务环节提供准确的结构数据支撑。

## 需求

### 需求1：组件定义与管理

**用户故事：** 作为产品工程师，我希望能够定义和管理基础组件，包括组件的参数、约束、工艺要求和物料分类映射，以便为构件组装提供标准化的组件库。

#### 验收标准

1. WHEN 创建组件时 THEN 系统应支持组件基本信息：编码、名称、描述、组件类型（框架、玻璃、五金、密封、其他）
2. WHEN 定义组件参数时 THEN 系统应支持多种参数类型：数值、字符串、布尔值、选择、公式
3. WHEN 设置参数属性时 THEN 系统应支持单位、默认值、最值范围、选项列表、必填标识
4. WHEN 建立物料映射时 THEN 系统应关联组件与物料分类：分类ID、名称、编码
5. WHEN 定义计算规则时 THEN 系统应支持数量计算公式和成本计算公式
6. WHEN 设置约束条件时 THEN 系统应支持尺寸、材料、工艺、兼容性约束及自动修复规则
7. WHEN 定义工艺要求时 THEN 系统应记录工艺类型、参数、设备、技能等级、预估时间

### 需求2：构件定义与装配管理

**用户故事：** 作为装配工程师，我希望能够定义构件并管理组件实例的装配关系，包括组件的数量、位置、参数值和装配工艺，以便为产品装配提供详细的装配指导。

#### 验收标准

1. WHEN 创建构件时 THEN 系统应支持构件基本信息：编码、名称、描述、构件类型（框架构件、玻璃构件、五金构件、完整构件）
2. WHEN 添加组件实例时 THEN 系统应支持选择组件、设置参数值、定义数量和位置
3. WHEN 设置组件数量时 THEN 系统应支持固定数量和数量计算公式两种方式
4. WHEN 定义可选组件时 THEN 系统应支持可选标识、显示条件和替代选项
5. WHEN 支持嵌套构件时 THEN 系统应允许构件包含子构件实例
6. WHEN 定义装配工艺时 THEN 系统应记录装配步骤、时间、工具、安全要求、质量检查点
7. WHEN 设置质量要求时 THEN 系统应定义质量类别、要求、测试方法、验收标准

### 需求3：产品结构定义与配置管理

**用户故事：** 作为产品经理，我希望能够定义完整的产品结构并管理产品配置选项，支持客户个性化定制需求，并能根据配置自动调整产品结构。

#### 验收标准

1. WHEN 创建产品结构时 THEN 系统应支持产品基本信息：编码、名称、产品类型、类别、子类别
2. WHEN 定义根构件时 THEN 系统应指定产品的主要构件实例作为结构入口
3. WHEN 设置产品参数时 THEN 系统应支持产品级别的参数定义和约束条件
4. WHEN 定义配置选项时 THEN 系统应支持单选、多选、布尔值、范围等配置类型
5. WHEN 设置配置影响时 THEN 系统应定义配置对组件、构件的变更影响和成本影响
6. WHEN 应用配置时 THEN 系统应根据配置选择自动调整相关组件和构件
7. WHEN 验证配置时 THEN 系统应检查配置的完整性、一致性和约束冲突

### 需求4：参数化设计与约束管理

**用户故事：** 作为设计工程师，我希望能够定义参数化的组件和构件，通过参数驱动设计变更，并设置约束条件确保设计的合理性和可制造性。

#### 验收标准

1. WHEN 定义参数时 THEN 系统应支持参数分类：尺寸、材料、工艺、质量
2. WHEN 设置参数选项时 THEN 系统应支持选项值、标签、描述、附加成本、材料影响
3. WHEN 定义约束时 THEN 系统应支持约束类型：尺寸、材料、工艺、兼容性
4. WHEN 设置约束表达式时 THEN 系统应支持数学表达式和逻辑表达式
5. WHEN 约束违反时 THEN 系统应提供错误信息、严重级别和自动修复选项
6. WHEN 参数变更时 THEN 系统应自动验证所有相关约束并提示冲突

### 需求5：版本控制与变更追溯

**用户故事：** 作为质量管理员，我希望能够追溯产品结构的版本变更历史，确保生产与设计版本的一致性，支持质量问题的根因分析。

#### 验收标准

1. WHEN 修改结构时 THEN 系统应自动创建版本历史记录：版本号、变更日期、变更人、变更类型、变更描述
2. WHEN 记录变更时 THEN 系统应详细记录变更内容：变更类型（组件、构件、参数、约束）、目标对象、操作类型（增加、修改、删除）
3. WHEN 查看版本历史时 THEN 系统应支持版本对比和差异高亮显示
4. WHEN 需要审批时 THEN 系统应支持变更审批流程：审批人、审批日期、审批意见
5. WHEN 回滚版本时 THEN 系统应支持回滚到指定历史版本并记录回滚操作
6. WHEN 查询历史时 THEN 系统应支持按时间范围、变更人、变更类型等条件筛选

### 需求6：结构验证与质量检查

**用户故事：** 作为产品工程师，我希望系统能够自动验证产品结构的完整性和一致性，及时发现结构定义中的错误和冲突，确保结构设计的质量。

#### 验收标准

1. WHEN 保存结构时 THEN 系统应验证结构完整性：缺失组件、无效约束、循环引用、无效公式
2. WHEN 检查错误时 THEN 系统应提供错误类型、错误信息、错误位置、严重级别
3. WHEN 检查警告时 THEN 系统应提示潜在问题：未使用组件、过时组件、性能问题及改进建议
4. WHEN 提供建议时 THEN 系统应给出优化建议：优化类型、建议内容、预期收益、实施难度
5. WHEN 验证通过时 THEN 系统应生成验证报告并标记结构状态
6. WHEN 结构变更时 THEN 系统应自动触发重新验证并更新验证状态

### 需求7：结构搜索与筛选

**用户故事：** 作为业务用户，我希望能够快速搜索和筛选产品结构、组件和构件，便于查找特定的设计信息和重用现有设计。

#### 验收标准

1. WHEN 搜索结构时 THEN 系统应支持关键词搜索：名称、编码、描述、标签
2. WHEN 筛选结构时 THEN 系统应支持多维度筛选：产品类型、类别、状态、创建时间范围、创建人
3. WHEN 搜索组件时 THEN 系统应支持按组件类型、物料分类、工艺要求筛选
4. WHEN 高级搜索时 THEN 系统应支持组合条件搜索和保存搜索条件
5. WHEN 显示结果时 THEN 系统应支持搜索结果高亮和相关性排序
6. WHEN 导出搜索结果时 THEN 系统应支持搜索结果的批量导出

### 需求8：结构统计与分析

**用户故事：** 作为管理人员，我希望能够查看产品结构的统计分析信息，了解结构复杂度、组件使用情况和设计优化机会。

#### 验收标准

1. WHEN 查看统计信息时 THEN 系统应显示产品结构总数、活跃结构数、各类型分布统计
2. WHEN 分析组件使用时 THEN 系统应统计组件使用频率、最常用组件排行、组件在结构中的使用情况
3. WHEN 分析结构复杂度时 THEN 系统应计算平均层级深度、组件数量分布、构件复杂度分析
4. WHEN 查看变更趋势时 THEN 系统应显示最近变更历史、结构变更频率和变更类型分析
5. WHEN 生成报表时 THEN 系统应支持定期自动生成统计报表和趋势分析
6. WHEN 导出分析时 THEN 系统应支持统计数据的图表化展示和多格式导出

### 需求9：BOM物料清单管理

**用户故事：** 作为生产计划员，我希望能够基于产品结构自动生成精确的BOM物料清单，明确列出每个组件对应的具体物料信息，为生产领料和采购提供准确依据。

#### 验收标准

1. WHEN 生成BOM时 THEN 系统应基于产品结构和组件实例自动计算物料需求
2. WHEN 计算物料数量时 THEN 系统应支持基于组件数量公式的动态计算
3. WHEN 展示BOM时 THEN 系统应显示完整的物料层级结构：组件→物料分类→具体物料
4. WHEN 汇总物料时 THEN 系统应按物料分类和具体物料进行数量汇总
5. WHEN 更新结构时 THEN 系统应自动更新相关BOM并标记变更
6. WHEN 导出BOM时 THEN 系统应支持多种格式导出：Excel、PDF、CSV，包含完整的物料信息

### 需求10：工艺管理与制造支持

**用户故事：** 作为工艺工程师，我希望能够为组件和构件定义详细的工艺要求和装配流程，为生产制造提供准确的工艺指导。

#### 验收标准

1. WHEN 定义组件工艺时 THEN 系统应记录工艺类型、工艺参数、所需设备、技能等级、预估时间
2. WHEN 定义装配工艺时 THEN 系统应支持装配步骤定义：步骤编号、名称、描述、预估时间
3. WHEN 设置工艺要求时 THEN 系统应记录所需工具、安全要求、质量检查项
4. WHEN 计算工艺时间时 THEN 系统应汇总各工艺步骤时间并计算总装配时间
5. WHEN 定义技能要求时 THEN 系统应记录所需技能和质量检查点
6. WHEN 导出工艺文档时 THEN 系统应支持工艺卡片和装配指导书的生成和导出