# 产品结构管理系统设计文档

## 概述

产品结构管理系统采用分层架构设计，基于Vue 3 + TypeScript + TailwindCSS技术栈构建。系统核心围绕"组件→构件→产品结构"三层业务模型，提供参数化设计、约束管理、版本控制和BOM生成等功能。

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A[产品结构管理界面]
        B[组件管理界面]
        C[构件管理界面]
        D[BOM管理界面]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        E[产品结构服务]
        F[组件管理服务]
        G[构件管理服务]
        H[BOM生成服务]
        I[版本控制服务]
        J[验证服务]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        K[产品结构Repository]
        L[组件Repository]
        M[构件Repository]
        N[版本Repository]
    end
    
    subgraph "数据存储层 (Data Storage Layer)"
        O[Mock数据文件]
        P[本地存储]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    E --> K
    F --> L
    G --> M
    H --> K
    I --> N
    J --> K
    K --> O
    L --> O
    M --> O
    N --> P
```

### 核心业务模型

```mermaid
erDiagram
    ProductStructure ||--|| Assembly : "根构件"
    ProductStructure ||--o{ ConfigurationOption : "配置选项"
    ProductStructure ||--o{ VersionHistory : "版本历史"
    
    Assembly ||--o{ ComponentInstance : "组件实例"
    Assembly ||--o{ AssemblyInstance : "子构件实例"
    Assembly ||--|| AssemblyProcess : "装配工艺"
    
    ComponentInstance }o--|| Component : "引用组件"
    AssemblyInstance }o--|| Assembly : "引用构件"
    
    Component ||--o{ ComponentParameter : "组件参数"
    Component ||--o{ ComponentConstraint : "组件约束"
    Component ||--o{ ProcessRequirement : "工艺要求"
    
    ConfigurationOption ||--o{ ConfigurationChoice : "配置选择"
    ConfigurationChoice ||--o{ ComponentChange : "组件变更"
    ConfigurationChoice ||--o{ AssemblyChange : "构件变更"
```

## 组件设计

### 1. 产品结构管理组件

#### ProductStructureManagement.vue
- **职责**：产品结构的主管理界面
- **功能**：列表展示、搜索筛选、创建编辑、版本管理
- **组件结构**：
  ```vue
  <template>
    <div class="product-structure-management">
      <ProductStructureFilters @filter="handleFilter" />
      <ProductStructureTable 
        :structures="filteredStructures"
        @edit="handleEdit"
        @delete="handleDelete"
        @version="handleVersion"
      />
      <ProductStructureDialog 
        v-model:open="dialogOpen"
        :structure="currentStructure"
        @save="handleSave"
      />
    </div>
  </template>
  ```

#### ProductStructureEditor.vue
- **职责**：产品结构的可视化编辑器
- **功能**：树形结构展示、拖拽编辑、参数配置
- **特性**：
  - 支持树形结构的可视化展示
  - 支持拖拽调整层级关系
  - 实时参数验证和约束检查
  - 配置选项的动态应用

### 2. 组件管理组件

#### ComponentLibrary.vue
- **职责**：组件库的管理界面
- **功能**：组件列表、分类筛选、参数定义
- **组件结构**：
  ```vue
  <template>
    <div class="component-library">
      <ComponentFilters @filter="handleFilter" />
      <ComponentGrid 
        :components="filteredComponents"
        @select="handleSelect"
        @edit="handleEdit"
      />
      <ComponentEditor 
        v-model:open="editorOpen"
        :component="currentComponent"
        @save="handleSave"
      />
    </div>
  </template>
  ```

#### ComponentEditor.vue
- **职责**：组件的详细编辑器
- **功能**：参数定义、约束设置、工艺配置
- **特性**：
  - 参数类型的动态表单生成
  - 约束表达式的可视化编辑
  - 工艺要求的结构化定义

### 3. 构件管理组件

#### AssemblyDesigner.vue
- **职责**：构件的设计和装配界面
- **功能**：组件实例管理、装配关系定义
- **特性**：
  - 组件实例的可视化布局
  - 装配工艺的步骤定义
  - 质量要求的管理

### 4. BOM管理组件

#### BOMGenerator.vue
- **职责**：BOM的生成和管理
- **功能**：自动生成BOM、物料汇总、导出功能
- **特性**：
  - 基于产品结构自动计算物料需求
  - 支持多级BOM的展示和汇总
  - 多格式导出功能

## 数据模型设计

### 核心接口定义

```typescript
// 基础实体接口
interface BaseEntity {
  id: string;
  code: string;
  name: string;
  description?: string;
  version: number;
  status: 'draft' | 'active' | 'deprecated' | 'archived';
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

// 组件定义
interface Component extends BaseEntity {
  componentType: 'frame' | 'glass' | 'hardware' | 'seal' | 'other';
  materialCategoryId: string;
  materialCategoryName: string;
  materialCategoryCode: string;
  parameters: ComponentParameter[];
  quantityFormula: string;
  costFormula?: string;
  constraints: ComponentConstraint[];
  processRequirements: ProcessRequirement[];
  properties: Record<string, any>;
}

// 构件定义
interface Assembly extends BaseEntity {
  assemblyType: 'frame_assembly' | 'glass_assembly' | 'hardware_assembly' | 'complete_assembly';
  componentInstances: ComponentInstance[];
  subAssemblies: AssemblyInstance[];
  assemblyParameters: ComponentParameter[];
  assemblyConstraints: ComponentConstraint[];
  assemblyProcess: AssemblyProcess;
  qualityRequirements: QualityRequirement[];
}

// 产品结构定义
interface ProductStructure extends BaseEntity {
  productType: 'partition' | 'window' | 'door' | 'curtain_wall' | 'other';
  category: string;
  subCategory: string;
  rootAssembly: AssemblyInstance;
  productParameters: ComponentParameter[];
  productConstraints: ComponentConstraint[];
  configurationOptions: ConfigurationOption[];
  versionHistory: VersionHistory[];
  applications: string[];
  tags: string[];
}
```

### 参数化设计模型

```typescript
// 组件参数
interface ComponentParameter {
  id: string;
  name: string;
  displayName: string;
  type: 'number' | 'string' | 'boolean' | 'select' | 'formula';
  unit?: string;
  defaultValue?: any;
  minValue?: number;
  maxValue?: number;
  options?: ParameterOption[];
  required: boolean;
  description?: string;
  category: 'dimension' | 'material' | 'process' | 'quality';
}

// 约束定义
interface ComponentConstraint {
  id: string;
  name: string;
  type: 'dimension' | 'material' | 'process' | 'compatibility';
  expression: string;
  errorMessage: string;
  severity: 'error' | 'warning' | 'info';
  autoFix?: {
    enabled: boolean;
    fixExpression: string;
    fixMessage: string;
  };
}
```

## 服务层设计

### 1. ProductStructureService

```typescript
class ProductStructureService {
  // 基础CRUD操作
  async getStructures(filters: ProductStructureFilters): Promise<ProductStructure[]>
  async getStructureById(id: string): Promise<ProductStructure>
  async createStructure(structure: Partial<ProductStructure>): Promise<ProductStructure>
  async updateStructure(id: string, updates: Partial<ProductStructure>): Promise<ProductStructure>
  async deleteStructure(id: string): Promise<void>
  
  // 配置管理
  async applyConfiguration(structureId: string, config: Record<string, any>): Promise<ProductStructure>
  async validateConfiguration(structureId: string, config: Record<string, any>): Promise<ValidationResult>
  
  // BOM生成
  async generateBOM(structureId: string, config?: Record<string, any>): Promise<BOMItem[]>
  async exportBOM(structureId: string, format: 'excel' | 'pdf' | 'csv'): Promise<Blob>
}
```

### 2. ComponentService

```typescript
class ComponentService {
  // 组件管理
  async getComponents(filters: ComponentFilters): Promise<Component[]>
  async createComponent(component: Partial<Component>): Promise<Component>
  async updateComponent(id: string, updates: Partial<Component>): Promise<Component>
  
  // 参数和约束管理
  async validateParameters(componentId: string, values: Record<string, any>): Promise<ValidationResult>
  async calculateQuantity(componentId: string, parameters: Record<string, any>): Promise<number>
  async calculateCost(componentId: string, parameters: Record<string, any>): Promise<number>
}
```

### 3. ValidationService

```typescript
class ValidationService {
  // 结构验证
  async validateStructure(structure: ProductStructure): Promise<StructureValidationResult>
  async validateConstraints(constraints: ComponentConstraint[], values: Record<string, any>): Promise<ValidationResult>
  
  // 约束求解
  async resolveConstraints(constraints: ComponentConstraint[], values: Record<string, any>): Promise<Record<string, any>>
  async suggestFixes(errors: ValidationError[]): Promise<ValidationSuggestion[]>
}
```

## 状态管理设计

### Pinia Store结构

```typescript
// 产品结构状态管理
export const useProductStructureStore = defineStore('productStructure', {
  state: () => ({
    structures: [] as ProductStructure[],
    currentStructure: null as ProductStructure | null,
    filters: {} as ProductStructureFilters,
    loading: false,
    error: null as string | null
  }),
  
  actions: {
    async loadStructures(filters?: ProductStructureFilters) {
      this.loading = true;
      try {
        this.structures = await productStructureService.getStructures(filters || {});
        this.filters = filters || {};
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },
    
    async createStructure(structure: Partial<ProductStructure>) {
      const newStructure = await productStructureService.createStructure(structure);
      this.structures.push(newStructure);
      return newStructure;
    },
    
    async updateStructure(id: string, updates: Partial<ProductStructure>) {
      const updatedStructure = await productStructureService.updateStructure(id, updates);
      const index = this.structures.findIndex(s => s.id === id);
      if (index !== -1) {
        this.structures[index] = updatedStructure;
      }
      return updatedStructure;
    }
  }
});
```

## 错误处理策略

### 1. 验证错误处理
- **参数验证**：实时验证用户输入的参数值
- **约束检查**：自动检查约束条件并提供修复建议
- **结构完整性**：验证产品结构的完整性和一致性

### 2. 用户友好的错误提示
- **分级错误**：区分错误、警告和信息提示
- **上下文帮助**：提供错误位置和修复建议
- **自动修复**：支持简单错误的自动修复

## 测试策略

### 1. 单元测试
- **服务层测试**：测试业务逻辑和数据处理
- **工具函数测试**：测试参数验证、约束求解等工具函数
- **组件测试**：测试Vue组件的行为和交互

### 2. 集成测试
- **端到端流程**：测试完整的产品结构创建和编辑流程
- **配置应用**：测试配置选项对结构的影响
- **BOM生成**：测试BOM生成的准确性

### 3. 性能测试
- **大数据量**：测试大量组件和复杂结构的性能
- **实时验证**：测试参数变更时的响应速度
- **内存使用**：监控复杂结构的内存占用

## 部署和维护

### 1. 开发环境配置
- **热更新**：支持开发时的实时更新
- **Mock数据**：提供完整的测试数据
- **调试工具**：集成Vue DevTools和TypeScript支持

### 2. 生产环境优化
- **代码分割**：按功能模块进行代码分割
- **懒加载**：大型组件的懒加载
- **缓存策略**：合理的数据缓存和更新策略

### 3. 监控和日志
- **错误监控**：捕获和报告运行时错误
- **性能监控**：监控关键操作的性能指标
- **用户行为**：记录用户操作日志用于优化