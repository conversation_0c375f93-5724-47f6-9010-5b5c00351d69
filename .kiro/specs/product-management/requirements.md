# 产品管理模块需求文档

## 项目概述

本项目旨在为玻璃深加工ERP+MES原型系统开发产品管理模块的高保真原型。该原型将重点展示产品配置、BOM管理和报价生成等核心功能的数据结构设计和用户界面原型，为后续的完整系统开发提供设计验证和需求确认。该模块将与现有的物料元数据管理模块紧密集成，演示从客户需求到产品配置的完整业务流程。

## 需求概述

### Requirement 1

**User Story:** 作为开发工程师，我希望有清晰的产品数据模型设计，以便能够支持产品配置、BOM管理和成本计算等核心功能的原型开发。

#### Acceptance Criteria

1. WHEN 设计产品数据模型 THEN 系统 SHALL 定义ProductTemplate、ProductConfiguration、ProductBOM等核心数据结构
2. WHEN 关联物料数据 THEN 系统 SHALL 基于现有MaterialVariant模型建立产品与物料的关联关系
3. WHEN 设计配置规则 THEN 系统 SHALL 定义产品配置规则和约束条件的数据结构
4. WHEN 计算产品成本 THEN 系统 SHALL 设计基于BOM的成本计算算法和数据流
5. WHEN 存储配置数据 THEN 系统 SHALL 使用JSON格式的Mock数据展示完整的产品配置案例

### Requirement 2

**User Story:** 作为开发工程师，我希望有完整的产品配置器用户界面原型，以便验证产品配置的用户体验和操作流程。

#### Acceptance Criteria

1. WHEN 创建产品配置界面 THEN 系统 SHALL 采用左侧产品分类、中间配置表单、右侧预览的三栏布局
2. WHEN 显示产品分类 THEN 系统 SHALL 展示玻璃产品的层级分类结构（门窗、幕墙、隔断等）
3. WHEN 配置产品参数 THEN 系统 SHALL 提供直观的表单界面，支持尺寸、材质、工艺等参数的输入和选择
4. WHEN 实时计算成本 THEN 系统 SHALL 在配置变更时实时更新成本预览和BOM清单
5. WHEN 保存配置 THEN 系统 SHALL 生成产品配置的完整数据结构并展示JSON格式的配置结果

### Requirement 3

**User Story:** 作为开发工程师，我希望有完整的BOM管理数据结构和界面原型，以便展示产品与物料的关联关系和成本计算逻辑。

#### Acceptance Criteria

1. WHEN 设计BOM数据结构 THEN 系统 SHALL 定义ProductBOM、BOMItem等数据模型，支持多层级BOM结构
2. WHEN 关联物料变体 THEN 系统 SHALL 建立BOM与MaterialVariant的关联关系，支持物料选择和用量计算
3. WHEN 展示BOM界面 THEN 系统 SHALL 提供树形结构的BOM展示界面，支持层级展开和物料详情查看
4. WHEN 计算物料成本 THEN 系统 SHALL 基于BOM结构和物料变体成本计算产品总成本
5. WHEN 模拟BOM数据 THEN 系统 SHALL 创建典型玻璃产品的完整BOM示例数据

### Requirement 4

**User Story:** 作为开发工程师，我希望有简化的报价生成原型，以便展示从产品配置到报价单生成的完整数据流。

#### Acceptance Criteria

1. WHEN 设计报价数据模型 THEN 系统 SHALL 定义Quotation、QuotationItem等数据结构，关联产品配置和成本信息
2. WHEN 计算报价金额 THEN 系统 SHALL 基于产品BOM成本和预设利润率自动计算报价金额
3. WHEN 生成报价单 THEN 系统 SHALL 提供报价单的格式化展示，包括产品规格、数量、单价和总价
4. WHEN 保存报价记录 THEN 系统 SHALL 将报价数据保存为JSON格式，支持报价历史的查看
5. WHEN 模拟报价场景 THEN 系统 SHALL 创建典型的玻璃产品报价示例，展示完整的报价流程

### Requirement 5

**User Story:** 作为开发工程师，我希望有基础的工艺路线数据模型，以便展示产品配置与生产工艺的关联关系。

#### Acceptance Criteria

1. WHEN 设计工艺路线模型 THEN 系统 SHALL 定义ProcessRoute、ProcessStep等数据结构，描述产品的加工工艺流程
2. WHEN 关联产品工艺 THEN 系统 SHALL 建立产品配置与工艺路线的关联关系，支持不同产品类型的工艺差异
3. WHEN 展示工艺流程 THEN 系统 SHALL 提供工艺路线的可视化展示，包括工艺步骤和参数信息
4. WHEN 计算工艺成本 THEN 系统 SHALL 基于工艺路线和工时定额计算产品的加工成本
5. WHEN 创建工艺示例 THEN 系统 SHALL 为典型玻璃产品（如钢化玻璃、中空玻璃）创建完整的工艺路线示例

### Requirement 6

**User Story:** 作为开发工程师，我希望有完整的产品管理状态管理设计，以便支持产品数据的统一管理和组件间通信。

#### Acceptance Criteria

1. WHEN 设计状态管理 THEN 系统 SHALL 使用Pinia创建productStore，管理产品模板、配置和BOM数据
2. WHEN 集成现有数据 THEN 系统 SHALL 与现有的materialVariantStore和metadataStore进行数据关联
3. WHEN 管理产品状态 THEN 系统 SHALL 定义产品选择、配置编辑、成本计算等状态管理逻辑
4. WHEN 处理异步操作 THEN 系统 SHALL 支持产品数据的异步加载和错误处理
5. WHEN 缓存计算结果 THEN 系统 SHALL 使用计算属性缓存复杂的成本计算和BOM展开结果

### Requirement 7

**User Story:** 作为开发工程师，我希望有完整的Mock数据设计，以便支持产品管理原型的演示和测试。

#### Acceptance Criteria

1. WHEN 创建产品模板数据 THEN 系统 SHALL 在/mock/product/目录下创建productTemplates.json，包含门窗、幕墙、隔断等产品类型
2. WHEN 创建BOM示例数据 THEN 系统 SHALL 创建productBOMs.json，展示典型玻璃产品的完整物料清单
3. WHEN 创建工艺路线数据 THEN 系统 SHALL 创建processRoutes.json，定义钢化、夹胶、中空等工艺流程
4. WHEN 创建配置示例 THEN 系统 SHALL 创建productConfigurations.json，展示完整的产品配置案例
5. WHEN 创建报价示例 THEN 系统 SHALL 创建quotations.json，展示基于产品配置的报价数据

### Requirement 8

**User Story:** 作为开发工程师，我希望有模块化的Vue组件设计，以便构建可复用和可维护的产品管理界面。

#### Acceptance Criteria

1. WHEN 创建产品配置组件 THEN 系统 SHALL 实现ProductConfigurator.vue作为主要的产品配置界面
2. WHEN 创建BOM管理组件 THEN 系统 SHALL 实现ProductBOMTree.vue和BOMItemEditor.vue等BOM相关组件
3. WHEN 创建报价组件 THEN 系统 SHALL 实现QuotationGenerator.vue和QuotationPreview.vue等报价相关组件
4. WHEN 复用现有组件 THEN 系统 SHALL 基于现有的ShadCN组件库构建一致的用户界面
5. WHEN 组件通信 THEN 系统 SHALL 使用Props/Emit和Pinia Store实现组件间的数据传递和状态同步