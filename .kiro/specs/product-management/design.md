# 产品管理模块设计文档

## 概述

本设计文档基于需求文档，为玻璃深加工ERP原型系统的产品管理模块提供详细的技术架构和实现方案。该模块将展示从客户需求到产品配置、BOM生成、成本计算和报价生成的完整业务流程，重点关注数据模型设计和用户界面原型的实现。

### 产品管理模块核心目标
- **数据模型验证**：设计完整的产品管理数据模型，验证业务逻辑的可行性
- **用户体验原型**：构建直观的产品配置界面，展示MTO模式下的产品管理流程
- **系统集成演示**：与现有物料管理模块紧密集成，展示数据关联和业务协同
- **技术架构验证**：验证Vue 3 + TypeScript技术栈在复杂业务场景中的应用

## 架构设计

### 技术架构

```mermaid
graph TB
    A[产品管理前端] --> B[Vue 3 + TypeScript]
    B --> C[Pinia状态管理]
    B --> D[ShadCN Vue组件]
    B --> E[产品配置器]
    B --> F[BOM管理器]
    B --> G[报价生成器]
    
    C --> H[ProductStore]
    C --> I[现有MaterialVariantStore]
    C --> J[现有MetadataStore]
    
    H --> K[Mock数据层]
    K --> L[产品模板数据]
    K --> M[BOM数据]
    K --> N[工艺路线数据]
    K --> O[报价数据]
    
    subgraph "数据集成"
        I --> P[物料变体数据]
        J --> Q[物料分类数据]
        P --> R[成本计算引擎]
        Q --> R
    end
```

### 模块结构设计

```
src/
├── components/
│   ├── product/                    # 产品管理业务组件
│   │   ├── ProductConfigurator.vue     # 产品配置器主界面
│   │   ├── ProductTemplateSelector.vue # 产品模板选择器
│   │   ├── ProductConfigForm.vue       # 产品配置表单
│   │   ├── ProductBOMTree.vue          # BOM树形展示
│   │   ├── BOMItemEditor.vue           # BOM项编辑器
│   │   ├── ProcessRouteViewer.vue      # 工艺路线查看器
│   │   ├── CostCalculator.vue          # 成本计算器
│   │   ├── QuotationGenerator.vue      # 报价生成器
│   │   └── QuotationPreview.vue        # 报价预览
│   └── ui/                         # 复用现有ShadCN组件
├── stores/
│   └── product.ts                  # 产品管理状态管理
├── types/
│   └── product.ts                  # 产品相关类型定义
├── views/
│   └── ProductManagementView.vue   # 产品管理主视图
└── utils/
    ├── productCalculator.ts        # 产品计算工具
    └── bomProcessor.ts             # BOM处理工具

public/mock/product/                # 产品Mock数据
├── productTemplates.json          # 产品模板数据
├── productBOMs.json               # BOM数据
├── processRoutes.json             # 工艺路线数据
├── productConfigurations.json     # 产品配置示例
└── quotations.json                # 报价示例数据
```

## 数据模型设计

### 核心数据实体

#### 1. 产品模板（ProductTemplate）

```typescript
interface ProductTemplate {
  id: string                        // 模板唯一标识
  name: string                      // 模板名称："钢化玻璃门"
  code: string                      // 模板编码："TEMP_GLASS_DOOR"
  category: string                  // 产品分类："门窗"
  productType: 'window' | 'door' | 'curtain_wall' | 'partition' | 'custom'
  description: string               // 模板描述
  
  // 配置规则
  configurationRules: ConfigurationRule[]
  
  // 默认BOM模板
  defaultBOMTemplateId: string
  
  // 默认工艺路线
  defaultProcessRouteId: string
  
  // 成本计算规则
  costCalculationRules: CostCalculationRule[]
  
  // 状态信息
  isActive: boolean
  createdAt: string
  updatedAt?: string
}

interface ConfigurationRule {
  id: string
  name: string                      // 规则名称："尺寸约束"
  type: 'dimension' | 'material' | 'process' | 'custom'
  parameters: ConfigurationParameter[]
  constraints: RuleConstraint[]
  isRequired: boolean
}

interface ConfigurationParameter {
  id: string
  name: string                      // 参数名称："宽度"
  type: 'number' | 'select' | 'text' | 'boolean'
  unit?: string                     // 单位："mm"
  defaultValue?: any
  minValue?: number
  maxValue?: number
  options?: string[]                // 选项值
  description?: string
}

interface RuleConstraint {
  id: string
  expression: string                // 约束表达式："width <= 3000 && height <= 2400"
  errorMessage: string              // 错误提示
  severity: 'error' | 'warning' | 'info'
}
```

#### 2. 产品配置（ProductConfiguration）

```typescript
interface ProductConfiguration {
  id: string                        // 配置唯一标识
  templateId: string                // 关联的产品模板ID
  name: string                      // 配置名称
  code: string                      // 配置编码："CONF_FIRE_WINDOW_001"
  
  // 产品结构配置
  structure: ProductStructure
  
  // 参数化配置
  parameters: ConfigurationParameter[]
  
  // 关联的BOM模板
  bomTemplateId: string
  
  // 关联的工艺路线
  processRouteId: string
  
  // 客户信息（如果是客户定制）
  customerId?: string
  customerName?: string
  
  // 状态信息
  status: 'draft' | 'confirmed' | 'quoted' | 'ordered'
  createdAt: string
  updatedAt?: string
}

interface ProductStructure {
  id: string
  name: string                      // 产品结构名称："防火窗结构"
  components: ProductComponent[]    // 产品构件列表
}

interface ProductComponent {
  id: string
  name: string                      // 构件名称："窗框"、"窗扇"、"固定玻璃"
  type: 'frame' | 'sash' | 'glass' | 'hardware' | 'sealant'
  description: string
  
  // 构件参数
  parameters: ComponentParameter[]
  
  // 子部件
  subComponents: ProductSubComponent[]
  
  // 用量计算公式
  quantityFormula: string           // 参数化公式："frame_perimeter / profile_length"
  
  // 是否必需
  isRequired: boolean
  
  // 是否可选配置
  isConfigurable: boolean
}

interface ProductSubComponent {
  id: string
  name: string                      // 部件名称："立柱"、"横梁"、"连接件"
  materialType: string              // 物料类型："profile"、"hardware"
  
  // 部件参数
  parameters: ComponentParameter[]
  
  // 用量计算公式
  quantityFormula: string           // "2 * window_height / profile_length"
  
  // 物料选择规则
  materialSelectionRules: MaterialSelectionRule[]
}

interface ComponentParameter {
  id: string
  name: string                      // 参数名称："截面规格"、"壁厚"
  type: 'dimension' | 'material_property' | 'structural_property'
  dataType: 'number' | 'select' | 'text' | 'formula'
  
  // 参数值或公式
  value?: any                       // 固定值
  formula?: string                  // 参数化公式："width * height"
  options?: string[]                // 可选值
  
  // 约束条件
  constraints: ParameterConstraint[]
  
  // 单位
  unit?: string
  description?: string
}

interface MaterialSelectionRule {
  id: string
  condition: string                 // 选择条件："fire_rating == 'A' && thickness >= 6"
  materialCriteria: MaterialCriteria
  priority: number
}

interface MaterialCriteria {
  materialType: string              // 物料类型
  requiredProperties: Record<string, any>  // 必需属性
  preferredProperties: Record<string, any> // 优选属性
}

interface ParameterValue {
  parameterId: string
  parameterName: string
  value: any
  unit?: string
}

interface CostBreakdown {
  materialCost: number              // 物料成本
  laborCost: number                 // 人工成本
  overheadCost: number             // 制造费用
  totalCost: number                // 总成本
  suggestedPrice: number           // 建议售价
  profitMargin: number             // 利润率
  calculatedAt: string             // 计算时间
}
```

#### 3. 产品BOM模板（ProductBOMTemplate）

```typescript
interface ProductBOMTemplate {
  id: string                        // BOM模板唯一标识
  configurationId: string           // 关联的产品配置ID
  name: string                      // BOM模板名称
  version: string                   // BOM版本
  
  // BOM模板项目列表
  templateItems: BOMTemplateItem[]
  
  // BOM生成规则
  generationRules: BOMGenerationRule[]
  
  // 状态信息
  status: 'draft' | 'approved' | 'obsolete'
  createdAt: string
  updatedAt?: string
}

interface BOMTemplateItem {
  id: string                        // BOM模板项唯一标识
  parentId?: string                 // 父级BOM项ID（支持多层级）
  level: number                     // BOM层级
  
  // 关联的产品构件/部件
  componentId: string               // 关联的ProductComponent或ProductSubComponent ID
  componentType: 'component' | 'subcomponent'
  
  // 物料选择规则
  materialSelectionRules: MaterialSelectionRule[]
  
  // 用量计算规则
  quantityCalculationRule: QuantityCalculationRule
  
  // 替代规则
  alternativeRules: AlternativeRule[]
  
  // 备注信息
  notes?: string
  isOptional: boolean               // 是否可选
  isActive: boolean
}

interface QuantityCalculationRule {
  id: string
  formula: string                   // 用量计算公式："ceil(perimeter / profile_length)"
  parameters: string[]              // 公式中使用的参数
  unit: string                      // 计算结果单位
  wastageRate: number              // 损耗率（%）
  roundingRule: 'up' | 'down' | 'nearest'  // 取整规则
}

interface BOMGenerationRule {
  id: string
  name: string                      // 规则名称："玻璃用量计算"
  condition: string                 // 应用条件："product_type == 'window'"
  action: 'include' | 'exclude' | 'modify'
  targetItems: string[]            // 目标BOM项ID
  parameters: Record<string, any>
}

interface BOMItem {
  id: string                        // BOM项唯一标识
  parentId?: string                 // 父级BOM项ID（支持多层级）
  level: number                     // BOM层级
  
  // 物料信息
  materialVariantId: string         // 关联的物料变体ID
  materialVariant?: MaterialVariant // 物料变体详情（从现有系统获取）
  
  // 用量信息
  quantity: number                  // 用量
  unit: string                      // 单位
  wastageRate: number               // 损耗率（%）
  actualQuantity: number            // 实际用量（含损耗）
  
  // 成本信息
  unitCost: number                  // 单位成本
  totalCost: number                 // 总成本
  
  // 替代物料
  alternativeMaterials: AlternativeMaterial[]
  
  // 备注信息
  notes?: string
  isOptional: boolean               // 是否可选
  isActive: boolean
}

interface AlternativeMaterial {
  materialVariantId: string
  priority: number                  // 优先级
  costDifference: number           // 成本差异
  notes?: string
}

interface BOMSummary {
  totalItems: number                // 总项目数
  totalMaterialCost: number        // 总物料成本
  totalWeight: number              // 总重量
  totalArea?: number               // 总面积（适用于玻璃）
  criticalMaterials: string[]      // 关键物料列表
}
```

#### 4. 工艺路线（ProcessRoute）

```typescript
interface ProcessRoute {
  id: string                        // 工艺路线唯一标识
  name: string                      // 工艺路线名称："钢化玻璃加工工艺"
  code: string                      // 工艺编码
  productType: string               // 适用产品类型
  
  // 工艺步骤
  steps: ProcessStep[]
  
  // 工艺汇总
  summary: ProcessSummary
  
  // 状态信息
  isActive: boolean
  createdAt: string
  updatedAt?: string
}

interface ProcessStep {
  id: string                        // 工艺步骤ID
  stepNumber: number                // 步骤序号
  name: string                      // 步骤名称："切割"
  description: string               // 步骤描述
  
  // 工艺参数
  parameters: ProcessParameter[]
  
  // 时间信息
  setupTime: number                 // 准备时间（分钟）
  processTime: number               // 加工时间（分钟）
  
  // 质量检验
  qualityCheckpoints: QualityCheckpoint[]
  
  // 设备要求
  requiredEquipment: string[]
  
  // 技能要求
  requiredSkills: string[]
  
  // 成本信息
  laborCost: number                 // 人工成本
  equipmentCost: number            // 设备成本
  
  // 前置步骤
  predecessors: string[]
  
  // 后续步骤
  successors: string[]
}

interface ProcessParameter {
  name: string                      // 参数名称："温度"
  value: string | number           // 参数值
  unit?: string                    // 单位："℃"
  tolerance?: string               // 公差："±5"
  description?: string
}

interface QualityCheckpoint {
  id: string
  name: string                      // 检验项目："尺寸检验"
  type: 'dimensional' | 'visual' | 'functional' | 'safety'
  standard: string                  // 检验标准
  method: string                    // 检验方法
  isRequired: boolean
}

interface ProcessSummary {
  totalSteps: number                // 总步骤数
  totalTime: number                 // 总加工时间
  totalLaborCost: number           // 总人工成本
  totalEquipmentCost: number       // 总设备成本
  criticalPath: string[]           // 关键路径
}
```

#### 5. 报价（Quotation）

```typescript
interface Quotation {
  id: string                        // 报价唯一标识
  quotationNumber: string           // 报价单号
  
  // 客户信息
  customerId: string
  customerName: string
  contactPerson: string
  contactPhone: string
  contactEmail: string
  
  // 产品信息
  items: QuotationItem[]
  
  // 价格信息
  pricing: QuotationPricing
  
  // 商务条款
  terms: QuotationTerms
  
  // 状态信息
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired'
  validUntil: string                // 有效期
  createdAt: string
  updatedAt?: string
}

interface QuotationItem {
  id: string
  configurationId: string           // 关联的产品配置ID
  configuration?: ProductConfiguration
  
  // 产品信息
  productName: string
  productDescription: string
  specifications: Record<string, any>
  
  // 数量和价格
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number
  
  // 交期信息
  leadTime: number                  // 交货周期（天）
  deliveryDate: string             // 预计交货日期
  
  // 备注
  notes?: string
}

interface QuotationPricing {
  subtotal: number                  // 小计
  discountRate: number             // 折扣率
  discountAmount: number           // 折扣金额
  taxRate: number                  // 税率
  taxAmount: number                // 税额
  totalAmount: number              // 总金额
  
  // 付款条件
  paymentTerms: string             // 付款条件
  currency: string                 // 币种
}

interface QuotationTerms {
  deliveryTerms: string            // 交货条件
  warrantyPeriod: string           // 质保期
  technicalSupport: string         // 技术支持
  specialRequirements?: string     // 特殊要求
}
```

## 组件设计

### 1. 产品配置器主界面

**ProductConfigurator.vue** - 产品配置器主界面
- **布局设计**：采用三栏布局（左侧产品模板选择、中间配置表单、右侧预览和成本）
- **功能特性**：
  - 产品模板选择和切换
  - 参数化配置表单
  - 产品结构可视化展示
  - 实时BOM生成和成本计算
  - 配置验证和保存

```vue
<template>
  <div class="product-configurator">
    <!-- 左侧：产品模板选择 -->
    <div class="template-selector">
      <ProductTemplateSelector 
        :templates="productTemplates"
        :selected-template-id="selectedTemplateId"
        @template-selected="handleTemplateSelected"
      />
    </div>
    
    <!-- 中间：配置表单和结构展示 -->
    <div class="config-main">
      <!-- 参数配置区 -->
      <div class="parameter-config">
        <ProductParameterForm
          :template="selectedTemplate"
          :parameters="configurationParameters"
          @parameter-changed="handleParameterChanged"
          @validation-result="handleValidationResult"
        />
      </div>
      
      <!-- 产品结构展示区 -->
      <div class="structure-display">
        <ProductStructureViewer
          :structure="selectedTemplate?.structure"
          :parameters="configurationParameters"
          :calculated-values="calculatedValues"
          @component-selected="handleComponentSelected"
        />
      </div>
    </div>
    
    <!-- 右侧：BOM和成本 -->
    <div class="bom-cost-panel">
      <ProductBOMPreview
        :bom-template="generatedBOMTemplate"
        :calculated-bom="calculatedBOM"
        @bom-item-selected="handleBOMItemSelected"
      />
      <CostCalculator
        :bom="calculatedBOM"
        :cost-breakdown="costBreakdown"
        @cost-calculated="handleCostCalculated"
      />
    </div>
  </div>
</template>
```

### 2. 产品参数配置组件

**ProductParameterForm.vue** - 产品参数配置表单
- **功能特性**：
  - 支持不同类型参数的输入（数值、选择、公式）
  - 实时参数验证和约束检查
  - 参数间依赖关系处理
  - 参数变更的级联更新

```vue
<template>
  <div class="parameter-form">
    <div class="form-header">
      <h3>产品参数配置</h3>
    </div>
    
    <div class="parameter-groups">
      <div class="parameter-group" v-for="group in parameterGroups" :key="group.type">
        <h4>{{ group.title }}</h4>
        <div class="parameter-items">
          <ParameterInput
            v-for="param in group.parameters"
            :key="param.id"
            :parameter="param"
            :value="getParameterValue(param.id)"
            :validation-result="getValidationResult(param.id)"
            @value-changed="handleParameterValueChanged"
          />
        </div>
      </div>
    </div>
  </div>
</template>
```

### 3. 产品结构可视化组件

**ProductStructureViewer.vue** - 产品结构可视化展示
- **功能特性**：
  - 层级化展示产品构件和部件
  - 显示计算后的尺寸和用量
  - 支持构件的展开/折叠
  - 高亮显示选中的构件

```vue
<template>
  <div class="structure-viewer">
    <div class="viewer-header">
      <h3>产品结构</h3>
      <div class="viewer-controls">
        <Button @click="expandAll">全部展开</Button>
        <Button @click="collapseAll">全部折叠</Button>
      </div>
    </div>
    
    <div class="structure-tree">
      <ComponentNode
        v-for="component in structure?.components"
        :key="component.id"
        :component="component"
        :parameters="parameters"
        :calculated-values="calculatedValues"
        :level="0"
        :expanded="expandedComponents.has(component.id)"
        @toggle-expand="handleToggleExpand"
        @component-selected="handleComponentSelected"
      />
    </div>
  </div>
</template>
```

### 4. 构件节点组件

**ComponentNode.vue** - 单个构件/部件节点
- **功能特性**：
  - 显示构件基本信息
  - 显示计算后的参数值
  - 支持子部件的递归展示
  - 物料选择规则的可视化

```vue
<template>
  <div class="component-node" :class="{ 'selected': isSelected, 'expanded': expanded }">
    <div class="node-header" @click="handleNodeClick">
      <div class="node-icon">
        <ChevronRight v-if="hasSubComponents && !expanded" />
        <ChevronDown v-if="hasSubComponents && expanded" />
        <Component v-if="!hasSubComponents" />
      </div>
      <div class="node-info">
        <span class="node-name">{{ component.name }}</span>
        <span class="node-type">{{ component.type }}</span>
        <span class="node-quantity">数量: {{ calculatedQuantity }}</span>
      </div>
    </div>
    
    <div class="node-details" v-if="expanded || isSelected">
      <div class="parameter-values">
        <div v-for="param in component.parameters" :key="param.id" class="param-item">
          <span class="param-name">{{ param.name }}:</span>
          <span class="param-value">{{ getCalculatedValue(param) }}</span>
        </div>
      </div>
      
      <div class="material-selection" v-if="component.subComponents?.length">
        <h5>物料选择规则</h5>
        <div v-for="rule in materialSelectionRules" :key="rule.id" class="rule-item">
          <span class="rule-condition">{{ rule.condition }}</span>
          <span class="rule-material">{{ rule.materialCriteria.materialType }}</span>
        </div>
      </div>
    </div>
    
    <div class="sub-components" v-if="expanded && component.subComponents?.length">
      <SubComponentNode
        v-for="subComponent in component.subComponents"
        :key="subComponent.id"
        :sub-component="subComponent"
        :parameters="parameters"
        :calculated-values="calculatedValues"
        :level="level + 1"
        @sub-component-selected="handleSubComponentSelected"
      />
    </div>
  </div>
</template>
```

### 5. BOM预览组件

**ProductBOMPreview.vue** - BOM预览和生成组件
- **功能特性**：
  - 基于产品结构自动生成BOM
  - 显示物料选择结果
  - 用量计算和成本汇总
  - 支持BOM导出和保存

```vue
<template>
  <div class="bom-preview">
    <div class="bom-header">
      <h3>产品BOM清单</h3>
      <div class="bom-actions">
        <Button @click="generateBOM" :loading="isGenerating">生成BOM</Button>
        <Button @click="exportBOM" :disabled="!calculatedBOM">导出</Button>
      </div>
    </div>
    
    <div class="bom-summary" v-if="calculatedBOM">
      <div class="summary-item">
        <span>总项目数:</span>
        <span>{{ calculatedBOM.items.length }}</span>
      </div>
      <div class="summary-item">
        <span>物料成本:</span>
        <span>¥{{ totalMaterialCost.toFixed(2) }}</span>
      </div>
    </div>
    
    <div class="bom-content">
      <BOMItemList
        :items="calculatedBOM?.items || []"
        :material-variants="materialVariants"
        @item-selected="handleBOMItemSelected"
        @quantity-changed="handleQuantityChanged"
      />
    </div>
  </div>
</template>
```

### 6. 参数输入组件

**ParameterInput.vue** - 通用参数输入组件
- **功能特性**：
  - 支持多种参数类型（数值、选择、文本、公式）
  - 实时验证和错误提示
  - 单位显示和转换
  - 约束条件检查

```vue
<template>
  <div class="parameter-input" :class="{ 'has-error': hasError }">
    <label class="parameter-label">
      {{ parameter.name }}
      <span v-if="parameter.unit" class="parameter-unit">({{ parameter.unit }})</span>
    </label>
    
    <div class="input-wrapper">
      <!-- 数值输入 -->
      <Input
        v-if="parameter.dataType === 'number'"
        type="number"
        :value="value"
        :min="parameter.constraints?.find(c => c.id.includes('min'))?.minValue"
        :max="parameter.constraints?.find(c => c.id.includes('max'))?.maxValue"
        @input="handleValueChange"
      />
      
      <!-- 选择输入 -->
      <Select
        v-else-if="parameter.dataType === 'select'"
        :value="value"
        @value-change="handleValueChange"
      >
        <SelectItem
          v-for="option in parameter.options"
          :key="option"
          :value="option"
        >
          {{ option }}
        </SelectItem>
      </Select>
      
      <!-- 公式显示 -->
      <div
        v-else-if="parameter.dataType === 'formula'"
        class="formula-display"
      >
        <span class="formula-text">{{ parameter.formula }}</span>
        <span class="calculated-value">= {{ calculatedValue }}</span>
      </div>
      
      <!-- 文本输入 -->
      <Input
        v-else
        type="text"
        :value="value"
        @input="handleValueChange"
      />
    </div>
    
    <div v-if="hasError" class="error-message">
      {{ validationResult?.errors?.[0]?.message }}
    </div>
    
    <div v-if="parameter.description" class="parameter-description">
      {{ parameter.description }}
    </div>
  </div>
</template>
```

### 7. 公式计算引擎组件

**FormulaCalculator.vue** - 公式计算引擎
- **功能特性**：
  - 解析和计算参数化公式
  - 处理参数间的依赖关系
  - 实时更新计算结果
  - 支持复杂的数学表达式

```typescript
// utils/formulaCalculator.ts
export class FormulaCalculator {
  static calculateValue(
    formula: string,
    parameters: Record<string, any>
  ): number | string {
    try {
      // 替换公式中的参数变量
      let expression = formula
      Object.entries(parameters).forEach(([key, value]) => {
        expression = expression.replace(new RegExp(key, 'g'), String(value))
      })
      
      // 安全的表达式计算
      return this.evaluateExpression(expression)
    } catch (error) {
      console.error('公式计算错误:', error)
      return 0
    }
  }
  
  static evaluateExpression(expression: string): number {
    // 使用安全的表达式计算器
    // 支持基本数学运算：+, -, *, /, (), ceil, floor, max, min
    const allowedOperators = /^[0-9+\-*/.() ]+$/
    if (!allowedOperators.test(expression)) {
      throw new Error('不支持的表达式')
    }
    
    // 这里可以使用更安全的表达式解析器
    return Function(`"use strict"; return (${expression})`)()
  }
}
```

### 8. 物料选择引擎组件

**MaterialSelector.vue** - 物料选择引擎
- **功能特性**：
  - 基于选择规则自动匹配物料
  - 支持多条件筛选
  - 物料替代方案推荐
  - 成本优化建议

```typescript
// utils/materialSelector.ts
export class MaterialSelector {
  static selectMaterial(
    selectionRules: MaterialSelectionRule[],
    availableMaterials: MaterialVariant[],
    currentParameters: Record<string, any>
  ): MaterialVariant | null {
    // 按优先级排序规则
    const sortedRules = selectionRules.sort((a, b) => a.priority - b.priority)
    
    for (const rule of sortedRules) {
      // 检查规则条件是否满足
      if (this.evaluateCondition(rule.condition, currentParameters)) {
        // 根据物料标准筛选
        const matchedMaterials = this.filterMaterialsByCriteria(
          availableMaterials,
          rule.materialCriteria
        )
        
        if (matchedMaterials.length > 0) {
          // 返回最优匹配的物料（可以基于成本、库存等因素）
          return this.selectOptimalMaterial(matchedMaterials)
        }
      }
    }
    
    return null
  }
  
  private static filterMaterialsByCriteria(
    materials: MaterialVariant[],
    criteria: MaterialCriteria
  ): MaterialVariant[] {
    return materials.filter(material => {
      // 检查必需属性
      for (const [key, value] of Object.entries(criteria.requiredProperties)) {
        const materialValue = this.getMaterialProperty(material, key)
        if (!this.matchesProperty(materialValue, value)) {
          return false
        }
      }
      return true
    })
  }
}
```

### 9. BOM生成引擎组件

**BOMGenerator.vue** - BOM生成引擎
- **功能特性**：
  - 基于产品结构自动生成BOM
  - 计算各部件的用量
  - 选择合适的物料变体
  - 生成完整的物料清单

```typescript
// utils/bomGenerator.ts
export class BOMGenerator {
  static async generateBOM(
    productStructure: ProductStructure,
    parameters: Record<string, any>,
    materialVariantStore: any
  ): Promise<ProductBOM> {
    const bomItems: BOMItem[] = []
    
    // 遍历产品构件
    for (const component of productStructure.components) {
      const componentItems = await this.generateComponentBOM(
        component,
        parameters,
        materialVariantStore,
        1 // 根级别
      )
      bomItems.push(...componentItems)
    }
    
    // 计算BOM汇总
    const summary = this.calculateBOMSummary(bomItems)
    
    return {
      id: `bom_${Date.now()}`,
      configurationId: '', // 将在调用时设置
      name: '产品BOM',
      version: '1.0',
      items: bomItems,
      summary,
      status: 'draft',
      createdAt: new Date().toISOString()
    }
  }
  
  private static async generateComponentBOM(
    component: ProductComponent,
    parameters: Record<string, any>,
    materialVariantStore: any,
    level: number
  ): Promise<BOMItem[]> {
    const items: BOMItem[] = []
    
    // 计算构件数量
    const componentQuantity = FormulaCalculator.calculateValue(
      component.quantityFormula,
      parameters
    ) as number
    
    // 处理子部件
    for (const subComponent of component.subComponents) {
      // 计算子部件数量
      const subQuantity = FormulaCalculator.calculateValue(
        subComponent.quantityFormula,
        parameters
      ) as number
      
      // 选择合适的物料
      const selectedMaterial = MaterialSelector.selectMaterial(
        subComponent.materialSelectionRules,
        materialVariantStore.materialVariants,
        parameters
      )
      
      if (selectedMaterial) {
        items.push({
          id: `item_${subComponent.id}_${Date.now()}`,
          level,
          materialVariantId: selectedMaterial.id,
          materialVariant: selectedMaterial,
          quantity: subQuantity * componentQuantity,
          unit: selectedMaterial.unit || '个',
          wastageRate: 5, // 默认5%损耗
          actualQuantity: (subQuantity * componentQuantity) * 1.05,
          unitCost: selectedMaterial.cost,
          totalCost: (subQuantity * componentQuantity) * 1.05 * selectedMaterial.cost,
          alternativeMaterials: [],
          isOptional: false,
          isActive: true
        })
      }
    }
    
    return items
  }
}
```

## 状态管理设计

### ProductStore设计

```typescript
// stores/product.ts
interface ProductState {
  // 产品模板
  productTemplates: ProductTemplate[]
  selectedTemplateId: string | null
  
  // 产品配置
  productConfigurations: ProductConfiguration[]
  currentConfigurationId: string | null
  
  // BOM数据
  productBOMs: ProductBOM[]
  
  // 工艺路线
  processRoutes: ProcessRoute[]
  
  // 报价数据
  quotations: Quotation[]
  
  // 加载状态
  isLoadingTemplates: boolean
  isLoadingConfigurations: boolean
  isLoadingBOMs: boolean
  
  // 错误状态
  templatesError: string | null
  configurationsError: string | null
  bomsError: string | null
  
  // 计算缓存
  costCalculationCache: Map<string, CostBreakdown>
  bomGenerationCache: Map<string, ProductBOM>
}

export const useProductStore = defineStore('product', {
  state: (): ProductState => ({
    productTemplates: [],
    selectedTemplateId: null,
    productConfigurations: [],
    currentConfigurationId: null,
    productBOMs: [],
    processRoutes: [],
    quotations: [],
    
    isLoadingTemplates: false,
    isLoadingConfigurations: false,
    isLoadingBOMs: false,
    
    templatesError: null,
    configurationsError: null,
    bomsError: null,
    
    costCalculationCache: new Map(),
    bomGenerationCache: new Map()
  }),

  getters: {
    selectedTemplate: (state) => {
      if (!state.selectedTemplateId) return null
      return state.productTemplates.find(t => t.id === state.selectedTemplateId) || null
    },

    currentConfiguration: (state) => {
      if (!state.currentConfigurationId) return null
      return state.productConfigurations.find(c => c.id === state.currentConfigurationId) || null
    },

    configurationsForTemplate: (state) => (templateId: string) => {
      return state.productConfigurations.filter(c => c.templateId === templateId)
    },

    bomForConfiguration: (state) => (configurationId: string) => {
      return state.productBOMs.find(b => b.configurationId === configurationId) || null
    }
  },

  actions: {
    async loadProductTemplates() {
      this.isLoadingTemplates = true
      this.templatesError = null
      
      try {
        const response = await fetch('/mock/product/productTemplates.json')
        const data = await response.json()
        this.productTemplates = data.productTemplates || []
      } catch (error) {
        this.templatesError = error instanceof Error ? error.message : '加载产品模板失败'
      } finally {
        this.isLoadingTemplates = false
      }
    },

    async generateBOMForConfiguration(configurationId: string): Promise<ProductBOM> {
      // 检查缓存
      if (this.bomGenerationCache.has(configurationId)) {
        return this.bomGenerationCache.get(configurationId)!
      }

      const configuration = this.productConfigurations.find(c => c.id === configurationId)
      if (!configuration) {
        throw new Error('配置不存在')
      }

      // BOM生成逻辑
      const bom = await this.generateBOM(configuration)
      
      // 缓存结果
      this.bomGenerationCache.set(configurationId, bom)
      
      return bom
    },

    async calculateCost(configurationId: string): Promise<CostBreakdown> {
      // 检查缓存
      if (this.costCalculationCache.has(configurationId)) {
        return this.costCalculationCache.get(configurationId)!
      }

      const bom = await this.generateBOMForConfiguration(configurationId)
      const costBreakdown = await this.calculateCostFromBOM(bom)
      
      // 缓存结果
      this.costCalculationCache.set(configurationId, costBreakdown)
      
      return costBreakdown
    }
  }
})
```

## 业务逻辑设计

### 1. 产品配置验证引擎

```typescript
// utils/productValidator.ts
export class ProductConfigurationValidator {
  static validateConfiguration(
    template: ProductTemplate,
    parameterValues: ParameterValue[]
  ): ValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []

    // 验证必填参数
    template.configurationRules.forEach(rule => {
      if (rule.isRequired) {
        const hasValue = parameterValues.some(pv => 
          rule.parameters.some(p => p.id === pv.parameterId && pv.value != null)
        )
        if (!hasValue) {
          errors.push({
            ruleId: rule.id,
            message: `${rule.name} 是必填项`,
            severity: 'error'
          })
        }
      }
    })

    // 验证约束条件
    template.configurationRules.forEach(rule => {
      rule.constraints.forEach(constraint => {
        const isValid = this.evaluateConstraint(constraint, parameterValues)
        if (!isValid) {
          if (constraint.severity === 'error') {
            errors.push({
              ruleId: rule.id,
              constraintId: constraint.id,
              message: constraint.errorMessage,
              severity: 'error'
            })
          } else {
            warnings.push({
              ruleId: rule.id,
              constraintId: constraint.id,
              message: constraint.errorMessage,
              severity: constraint.severity
            })
          }
        }
      })
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  private static evaluateConstraint(
    constraint: RuleConstraint,
    parameterValues: ParameterValue[]
  ): boolean {
    // 简化的约束表达式评估
    // 实际实现中可以使用更复杂的表达式解析器
    try {
      const context = this.buildEvaluationContext(parameterValues)
      return this.evaluateExpression(constraint.expression, context)
    } catch (error) {
      console.error('约束评估失败:', error)
      return false
    }
  }
}
```

### 2. BOM生成引擎

```typescript
// utils/bomGenerator.ts
export class BOMGenerator {
  static async generateBOM(
    configuration: ProductConfiguration,
    template: ProductTemplate,
    materialVariantStore: any
  ): Promise<ProductBOM> {
    const bomItems: BOMItem[] = []
    
    // 根据产品配置生成BOM项
    for (const rule of template.configurationRules) {
      if (rule.type === 'material') {
        const items = await this.generateMaterialItems(rule, configuration, materialVariantStore)
        bomItems.push(...items)
      }
    }

    // 计算BOM汇总信息
    const summary = this.calculateBOMSummary(bomItems)

    return {
      id: `bom_${configuration.id}`,
      configurationId: configuration.id,
      name: `${configuration.name} - BOM`,
      version: '1.0',
      items: bomItems,
      summary,
      status: 'draft',
      createdAt: new Date().toISOString()
    }
  }

  private static async generateMaterialItems(
    rule: ConfigurationRule,
    configuration: ProductConfiguration,
    materialVariantStore: any
  ): Promise<BOMItem[]> {
    const items: BOMItem[] = []
    
    // 根据配置参数计算物料需求
    const parameterValues = configuration.parameterValues
    
    // 示例：玻璃用量计算
    if (rule.name === '玻璃用量') {
      const width = parameterValues.find(pv => pv.parameterName === '宽度')?.value as number
      const height = parameterValues.find(pv => pv.parameterName === '高度')?.value as number
      const thickness = parameterValues.find(pv => pv.parameterName === '厚度')?.value as string
      
      if (width && height && thickness) {
        // 选择合适的玻璃原片
        const suitableGlass = await this.findSuitableGlassMaterial(
          width, height, thickness, materialVariantStore
        )
        
        if (suitableGlass) {
          const quantity = this.calculateGlassQuantity(width, height, suitableGlass)
          
          items.push({
            id: `item_${Date.now()}`,
            level: 1,
            materialVariantId: suitableGlass.id,
            materialVariant: suitableGlass,
            quantity,
            unit: '片',
            wastageRate: 5, // 5%损耗率
            actualQuantity: quantity * 1.05,
            unitCost: suitableGlass.cost,
            totalCost: quantity * 1.05 * suitableGlass.cost,
            alternativeMaterials: [],
            isOptional: false,
            isActive: true
          })
        }
      }
    }

    return items
  }
}
```

### 3. 成本计算引擎

```typescript
// utils/costCalculator.ts
export class CostCalculator {
  static calculateProductCost(
    bom: ProductBOM,
    processRoute: ProcessRoute
  ): CostBreakdown {
    // 计算物料成本
    const materialCost = bom.items.reduce((total, item) => {
      return total + item.totalCost
    }, 0)

    // 计算人工成本
    const laborCost = processRoute.steps.reduce((total, step) => {
      return total + step.laborCost
    }, 0)

    // 计算制造费用
    const overheadCost = processRoute.steps.reduce((total, step) => {
      return total + step.equipmentCost
    }, 0)

    // 总成本
    const totalCost = materialCost + laborCost + overheadCost

    // 建议售价（成本加成30%）
    const suggestedPrice = totalCost * 1.3

    // 利润率
    const profitMargin = ((suggestedPrice - totalCost) / suggestedPrice) * 100

    return {
      materialCost,
      laborCost,
      overheadCost,
      totalCost,
      suggestedPrice,
      profitMargin,
      calculatedAt: new Date().toISOString()
    }
  }
}
```

## Mock数据设计

### 1. 产品模板数据

```json
// public/mock/product/productTemplates.json
{
  "productTemplates": [
    {
      "id": "TEMP_FIRE_WINDOW",
      "name": "钢质防火窗",
      "code": "TEMP_FIRE_WINDOW",
      "category": "防火窗",
      "productType": "window",
      "description": "钢质防火窗产品模板（上固定下开启）",
      "structure": {
        "id": "STRUCT_FIRE_WINDOW",
        "name": "钢质防火窗结构",
        "components": [
          {
            "id": "COMP_OUTER_FRAME",
            "name": "外框",
            "type": "frame",
            "description": "防火窗外围框架",
            "subComponents": [
              {
                "id": "SUB_OUTER_VERTICAL_LEFT",
                "name": "外框左立柱",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "outer_left_length",
                    "name": "左立柱长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "window_height",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_OUTER_VERTICAL_RIGHT",
                "name": "外框右立柱",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "outer_right_length",
                    "name": "右立柱长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "window_height",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_OUTER_HORIZONTAL_TOP",
                "name": "外框上横梁",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "outer_top_length",
                    "name": "上横梁长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "window_width - 2 * profile_width",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_OUTER_HORIZONTAL_BOTTOM",
                "name": "外框下横梁",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "outer_bottom_length",
                    "name": "下横梁长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "window_width - 2 * profile_width",
                    "unit": "mm"
                  }
                ]
              }
            ],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": false
          },
          {
            "id": "COMP_MIDDLE_HORIZONTAL_FRAME",
            "name": "中横框",
            "type": "frame",
            "description": "分隔上下部分的中横框",
            "subComponents": [
              {
                "id": "SUB_MIDDLE_HORIZONTAL",
                "name": "中横框型材",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "middle_horizontal_length",
                    "name": "中横框长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "window_width - 2 * profile_width",
                    "unit": "mm"
                  }
                ]
              }
            ],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": false
          },
          {
            "id": "COMP_MIDDLE_VERTICAL_FRAME",
            "name": "中竖框",
            "type": "frame",
            "description": "分隔左右窗扇的中竖框",
            "subComponents": [
              {
                "id": "SUB_MIDDLE_VERTICAL",
                "name": "中竖框型材",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "middle_vertical_length",
                    "name": "中竖框长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "opening_height - 2 * profile_width",
                    "unit": "mm"
                  }
                ]
              }
            ],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": false
          },
          {
            "id": "COMP_LEFT_SASH",
            "name": "左窗扇",
            "type": "sash",
            "description": "左侧开启窗扇",
            "subComponents": [
              {
                "id": "SUB_LEFT_SASH_VERTICAL_LEFT",
                "name": "左窗扇左立柱",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "left_sash_left_length",
                    "name": "左窗扇左立柱长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "opening_height - 2 * profile_width",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_LEFT_SASH_VERTICAL_RIGHT",
                "name": "左窗扇右立柱",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "left_sash_right_length",
                    "name": "左窗扇右立柱长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "opening_height - 2 * profile_width",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_LEFT_SASH_HORIZONTAL_TOP",
                "name": "左窗扇上横梁",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "left_sash_top_length",
                    "name": "左窗扇上横梁长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "(window_width / 2) - profile_width - middle_frame_width / 2",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_LEFT_SASH_HORIZONTAL_BOTTOM",
                "name": "左窗扇下横梁",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "left_sash_bottom_length",
                    "name": "左窗扇下横梁长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "(window_width / 2) - profile_width - middle_frame_width / 2",
                    "unit": "mm"
                  }
                ]
              }
            ],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": false
          },
          {
            "id": "COMP_RIGHT_SASH",
            "name": "右窗扇",
            "type": "sash",
            "description": "右侧开启窗扇",
            "subComponents": [
              {
                "id": "SUB_RIGHT_SASH_VERTICAL_LEFT",
                "name": "右窗扇左立柱",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "right_sash_left_length",
                    "name": "右窗扇左立柱长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "opening_height - 2 * profile_width",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_RIGHT_SASH_VERTICAL_RIGHT",
                "name": "右窗扇右立柱",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "right_sash_right_length",
                    "name": "右窗扇右立柱长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "opening_height - 2 * profile_width",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_RIGHT_SASH_HORIZONTAL_TOP",
                "name": "右窗扇上横梁",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "right_sash_top_length",
                    "name": "右窗扇上横梁长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "(window_width / 2) - profile_width - middle_frame_width / 2",
                    "unit": "mm"
                  }
                ]
              },
              {
                "id": "SUB_RIGHT_SASH_HORIZONTAL_BOTTOM",
                "name": "右窗扇下横梁",
                "materialType": "profile",
                "quantityFormula": "1",
                "parameters": [
                  {
                    "id": "right_sash_bottom_length",
                    "name": "右窗扇下横梁长度",
                    "type": "dimension",
                    "dataType": "formula",
                    "formula": "(window_width / 2) - profile_width - middle_frame_width / 2",
                    "unit": "mm"
                  }
                ]
              }
            ],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": false
          },
          {
            "id": "COMP_FIXED_GLASS_TOP",
            "name": "上部固定玻璃",
            "type": "glass",
            "description": "上部固定区域的防火玻璃",
            "parameters": [
              {
                "id": "fixed_glass_area",
                "name": "固定玻璃面积",
                "type": "dimension",
                "dataType": "formula",
                "formula": "(window_width - 2 * profile_width) * (fixed_height - 2 * profile_width)",
                "unit": "mm²"
              }
            ],
            "subComponents": [],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": true
          },
          {
            "id": "COMP_LEFT_SASH_GLASS",
            "name": "左窗扇玻璃",
            "type": "glass",
            "description": "左窗扇的防火玻璃",
            "parameters": [
              {
                "id": "left_sash_glass_area",
                "name": "左窗扇玻璃面积",
                "type": "dimension",
                "dataType": "formula",
                "formula": "((window_width / 2) - profile_width - middle_frame_width / 2 - profile_width) * (opening_height - 2 * profile_width)",
                "unit": "mm²"
              }
            ],
            "subComponents": [],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": true
          },
          {
            "id": "COMP_RIGHT_SASH_GLASS",
            "name": "右窗扇玻璃",
            "type": "glass",
            "description": "右窗扇的防火玻璃",
            "parameters": [
              {
                "id": "right_sash_glass_area",
                "name": "右窗扇玻璃面积",
                "type": "dimension",
                "dataType": "formula",
                "formula": "((window_width / 2) - profile_width - middle_frame_width / 2 - profile_width) * (opening_height - 2 * profile_width)",
                "unit": "mm²"
              }
            ],
            "subComponents": [],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": true
          },
          {
            "id": "COMP_HARDWARE",
            "name": "五金件",
            "type": "hardware",
            "description": "防火窗五金配件",
            "subComponents": [
              {
                "id": "SUB_HINGES",
                "name": "合页",
                "materialType": "hardware",
                "quantityFormula": "4",
                "parameters": [
                  {
                    "id": "hinge_load_capacity",
                    "name": "合页承重",
                    "type": "structural_property",
                    "dataType": "formula",
                    "formula": "sash_weight * 1.5",
                    "unit": "kg"
                  }
                ]
              },
              {
                "id": "SUB_LOCK",
                "name": "防火锁",
                "materialType": "hardware",
                "quantityFormula": "1",
                "parameters": []
              },
              {
                "id": "SUB_HANDLES",
                "name": "拉手",
                "materialType": "hardware",
                "quantityFormula": "2",
                "parameters": []
              }
            ],
            "quantityFormula": "1",
            "isRequired": true,
            "isConfigurable": true
          }
        ]
      },
      "parameters": [
        {
          "id": "window_width",
          "name": "窗宽",
          "type": "dimension",
          "dataType": "number",
          "unit": "mm",
          "constraints": [
            {
              "id": "width_constraint",
              "expression": "window_width >= 800 && window_width <= 2400",
              "errorMessage": "窗宽必须在800-2400mm之间",
              "severity": "error"
            }
          ]
        },
        {
          "id": "window_height",
          "name": "窗高",
          "type": "dimension",
          "dataType": "number",
          "unit": "mm",
          "constraints": [
            {
              "id": "height_constraint",
              "expression": "window_height >= 1000 && window_height <= 2400",
              "errorMessage": "窗高必须在1000-2400mm之间",
              "severity": "error"
            }
          ]
        },
        {
          "id": "fixed_height",
          "name": "固定窗高度",
          "type": "dimension",
          "dataType": "number",
          "unit": "mm",
          "constraints": [
            {
              "id": "fixed_height_constraint",
              "expression": "fixed_height >= 200 && fixed_height <= window_height * 0.6",
              "errorMessage": "固定窗高度必须在200mm到总高度60%之间",
              "severity": "error"
            }
          ]
        },
        {
          "id": "opening_height",
          "name": "开启窗高度",
          "type": "dimension",
          "dataType": "formula",
          "formula": "window_height - fixed_height - middle_frame_height",
          "unit": "mm"
        },
        {
          "id": "fire_rating",
          "name": "防火等级",
          "type": "structural_property",
          "dataType": "select",
          "options": ["A类", "C类"],
          "constraints": []
        },
        {
          "id": "profile_width",
          "name": "型材宽度",
          "type": "material_property",
          "dataType": "number",
          "value": 60,
          "unit": "mm"
        },
        {
          "id": "middle_frame_width",
          "name": "中框宽度",
          "type": "material_property",
          "dataType": "number",
          "value": 50,
          "unit": "mm"
        },
        {
          "id": "middle_frame_height",
          "name": "中框高度",
          "type": "material_property",
          "dataType": "number",
          "value": 50,
          "unit": "mm"
        }
      ],
      "defaultBOMTemplateId": "BOM_TEMP_FIRE_WINDOW",
      "defaultProcessRouteId": "ROUTE_FIRE_WINDOW",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

## 错误处理

### 错误处理策略

```typescript
// utils/errorHandler.ts
export class ProductErrorHandler {
  static handleConfigurationError(error: ConfigurationError): void {
    switch (error.type) {
      case 'VALIDATION_ERROR':
        this.showValidationError(error.details)
        break
      case 'BOM_GENERATION_ERROR':
        this.showBOMError(error.message)
        break
      case 'COST_CALCULATION_ERROR':
        this.showCostError(error.message)
        break
      default:
        this.showGenericError(error.message)
    }
  }

  static showValidationError(details: ValidationError[]): void {
    // 显示配置验证错误
    const errorMessages = details.map(d => d.message).join('\n')
    // 使用现有的通知组件显示错误
  }
}
```

## 测试策略

### 原型验证重点

1. **数据模型验证**：验证产品配置、BOM生成、成本计算的数据流完整性
2. **用户界面验证**：验证产品配置器的用户体验和操作流程
3. **业务逻辑验证**：验证配置规则、约束检查、成本计算的准确性
4. **系统集成验证**：验证与现有物料管理模块的数据集成

### 测试用例设计

```typescript
// 示例测试用例
describe('产品配置器', () => {
  it('应该能够基于模板创建产品配置', async () => {
    const template = mockProductTemplates[0]
    const configuration = await createConfiguration(template, mockParameterValues)
    expect(configuration.templateId).toBe(template.id)
    expect(configuration.parameterValues).toHaveLength(mockParameterValues.length)
  })

  it('应该能够生成正确的BOM', async () => {
    const configuration = mockConfigurations[0]
    const bom = await generateBOM(configuration)
    expect(bom.items).toHaveLength(expect.any(Number))
    expect(bom.summary.totalMaterialCost).toBeGreaterThan(0)
  })
})
```

这个设计文档为产品管理模块的原型开发提供了完整的技术架构和实现指导，重点关注数据模型设计、用户界面原型和业务逻辑验证，确保原型能够有效展示产品管理的核心功能和价值。