import { globalIgnores } from 'eslint/config'
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript'
import pluginVue from 'eslint-plugin-vue'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'

// To allow more languages other than `ts` in `.vue` files, uncomment the following lines:
// import { configureVueProject } from '@vue/eslint-config-typescript'
// configureVueProject({ scriptLangs: ['ts', 'tsx'] })
// More info at https://github.com/vuejs/eslint-config-typescript/#advanced-setup

export default defineConfigWithVueTs(
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
  },

  globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),

  pluginVue.configs['flat/essential'],
  vueTsConfigs.recommended,
  skipFormatting,

  // 原型项目友好的规则覆盖
  {
    name: 'app/prototype-overrides',
    rules: {
      // TypeScript 相关 - 允许 any 类型用于快速原型开发
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unsafe-any': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      
      // 未使用变量 - 警告而非错误
      '@typescript-eslint/no-unused-vars': 'warn',
      'no-unused-vars': 'off',
      
      // 允许更灵活的函数参数
      '@typescript-eslint/no-unused-expressions': 'off',
      
      // Vue 相关 - 放宽组件定义要求
      'vue/require-default-prop': 'off',
      'vue/no-v-html': 'off',
      'vue/multi-word-component-names': 'off',
      
      // 控制台调用 - 允许用于调试
      'no-console': 'warn',
      'no-debugger': 'warn',
      
      // 允许空的 catch 块
      'no-empty': 'warn',
      
      // 允许非空断言
      '@typescript-eslint/no-non-null-assertion': 'off',
      
      // 放宽对象属性访问
      '@typescript-eslint/no-unsafe-argument': 'off'
    }
  }
) as any
