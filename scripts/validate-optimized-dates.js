#!/usr/bin/env node

/**
 * 验证优化后的生产工单日期数据质量
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

// 日期工具函数
function formatDate(dateString) {
  return new Date(dateString).toISOString().split('T')[0];
}

function getDaysDifference(date1, date2) {
  const diffTime = Math.abs(new Date(date2) - new Date(date1));
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

function getWorkingDays(startDate, endDate) {
  let count = 0;
  let current = new Date(startDate);
  const end = new Date(endDate);
  
  while (current <= end) {
    const dayOfWeek = current.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // 不是周末
      count++;
    }
    current.setDate(current.getDate() + 1);
  }
  
  return count;
}

// 主验证函数
function validateOptimizedDates() {
  console.log('🔍 验证优化后的生产工单日期数据质量\n');
  console.log('=' .repeat(80));
  
  // 1. 加载数据
  const productionOrders = loadJsonFile('public/mock/mes/production-orders.json');
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  
  if (!productionOrders || !customerOrders) {
    console.error('❌ 无法加载必要的数据文件');
    return;
  }
  
  const currentDate = new Date('2025-08-19T09:00:00Z');
  const workOrders = productionOrders.productionOrders;
  
  console.log(`\n📊 验证基准: ${currentDate.toISOString().split('T')[0]}`);
  console.log(`📈 工单数量: ${workOrders.length}\n`);
  
  // 2. 日期格式验证
  console.log('📅 日期格式验证:');
  console.log('-' .repeat(50));
  
  let formatErrors = 0;
  const dateFields = ['createdAt', 'plannedStartDate', 'plannedEndDate'];
  
  workOrders.forEach(wo => {
    dateFields.forEach(field => {
      try {
        const date = new Date(wo[field]);
        if (isNaN(date.getTime())) {
          console.log(`❌ ${wo.workOrderNumber}.${field}: 无效日期格式`);
          formatErrors++;
        }
      } catch (error) {
        console.log(`❌ ${wo.workOrderNumber}.${field}: 日期解析错误`);
        formatErrors++;
      }
    });
    
    // 验证工单项的日期格式
    wo.items.forEach(item => {
      try {
        const date = new Date(item.deliveryDate);
        if (isNaN(date.getTime())) {
          console.log(`❌ ${item.id}.deliveryDate: 无效日期格式`);
          formatErrors++;
        }
      } catch (error) {
        console.log(`❌ ${item.id}.deliveryDate: 日期解析错误`);
        formatErrors++;
      }
    });
  });
  
  console.log(`✅ 日期格式验证完成: ${formatErrors === 0 ? '全部通过' : `发现${formatErrors}个错误`}\n`);
  
  // 3. 业务逻辑验证
  console.log('🏭 业务逻辑验证:');
  console.log('-' .repeat(50));
  
  let logicErrors = 0;
  const validationResults = [];
  
  workOrders.forEach(wo => {
    const createdAt = new Date(wo.createdAt);
    const plannedStartDate = new Date(wo.plannedStartDate);
    const plannedEndDate = new Date(wo.plannedEndDate);
    
    const result = {
      workOrderNumber: wo.workOrderNumber,
      priority: wo.priority,
      status: wo.status,
      createdAt: formatDate(wo.createdAt),
      plannedStartDate: formatDate(wo.plannedStartDate),
      plannedEndDate: formatDate(wo.plannedEndDate),
      issues: []
    };
    
    // 验证创建时间应该在当前时间之前
    if (createdAt > currentDate) {
      result.issues.push('创建时间不能在未来');
      logicErrors++;
    }
    
    // 验证创建时间应该在最近2周内
    const daysSinceCreated = getDaysDifference(createdAt, currentDate);
    if (daysSinceCreated > 14) {
      result.issues.push(`创建时间过早 (${daysSinceCreated}天前)`);
      logicErrors++;
    }
    
    // 验证开始时间应该在创建时间之后
    if (plannedStartDate < createdAt) {
      result.issues.push('计划开始时间早于创建时间');
      logicErrors++;
    }
    
    // 验证结束时间应该在开始时间之后
    if (plannedEndDate <= plannedStartDate) {
      result.issues.push('计划结束时间不晚于开始时间');
      logicErrors++;
    }
    
    // 验证状态与时间的一致性
    if (wo.status === 'in_progress' && plannedStartDate > currentDate) {
      result.issues.push('执行中状态但计划开始时间在未来');
      logicErrors++;
    }
    
    if (wo.status === 'completed' && plannedEndDate > currentDate) {
      result.issues.push('已完成状态但计划结束时间在未来');
      logicErrors++;
    }
    
    if (wo.status === 'pending' && plannedStartDate < currentDate) {
      result.issues.push('待发布状态但计划开始时间已过');
      logicErrors++;
    }
    
    // 验证生产周期合理性
    const productionDays = getWorkingDays(plannedStartDate, plannedEndDate);
    if (productionDays < 1) {
      result.issues.push('生产周期过短');
      logicErrors++;
    } else if (productionDays > 50) {
      result.issues.push(`生产周期过长 (${productionDays}工作日)`);
      logicErrors++;
    }
    
    result.productionDays = productionDays;
    validationResults.push(result);
    
    // 输出验证结果
    if (result.issues.length > 0) {
      console.log(`❌ ${wo.workOrderNumber}:`);
      result.issues.forEach(issue => console.log(`     - ${issue}`));
    } else {
      console.log(`✅ ${wo.workOrderNumber}: 通过验证 (${productionDays}工作日)`);
    }
  });
  
  console.log(`\n📊 业务逻辑验证完成: ${logicErrors === 0 ? '全部通过' : `发现${logicErrors}个问题`}\n`);
  
  // 4. 优先级与时间安排验证
  console.log('⚡ 优先级与时间安排验证:');
  console.log('-' .repeat(50));
  
  const priorityGroups = {
    urgent: [],
    high: [],
    medium: [],
    low: []
  };
  
  workOrders.forEach(wo => {
    const startOffset = getDaysDifference(currentDate, new Date(wo.plannedStartDate));
    const isStarted = new Date(wo.plannedStartDate) <= currentDate;
    
    priorityGroups[wo.priority].push({
      workOrderNumber: wo.workOrderNumber,
      startOffset: isStarted ? -startOffset : startOffset,
      isStarted: isStarted,
      status: wo.status
    });
  });
  
  Object.entries(priorityGroups).forEach(([priority, orders]) => {
    if (orders.length > 0) {
      console.log(`${priority.toUpperCase()}优先级 (${orders.length}个工单):`);
      orders.forEach(order => {
        const timeDesc = order.isStarted ? `${Math.abs(order.startOffset)}天前开始` : `${order.startOffset}天后开始`;
        console.log(`   ${order.workOrderNumber}: ${timeDesc} [${order.status}]`);
      });
      console.log();
    }
  });
  
  // 5. 交付日期一致性验证
  console.log('📦 交付日期一致性验证:');
  console.log('-' .repeat(50));
  
  let deliveryErrors = 0;
  const workOrderItemMap = new Map();
  
  workOrders.forEach(wo => {
    wo.items.forEach(item => {
      workOrderItemMap.set(item.customerOrderItemId, {
        workOrderNumber: wo.workOrderNumber,
        deliveryDate: item.deliveryDate,
        plannedEndDate: wo.plannedEndDate
      });
    });
  });
  
  customerOrders.orders.forEach(order => {
    order.items.forEach(item => {
      const workOrderInfo = workOrderItemMap.get(item.id);
      if (workOrderInfo) {
        const customerDeliveryDate = new Date(item.deliveryDate);
        const workOrderDeliveryDate = new Date(workOrderInfo.deliveryDate);
        
        if (Math.abs(customerDeliveryDate - workOrderDeliveryDate) > 24 * 60 * 60 * 1000) {
          console.log(`❌ ${item.id}: 客户订单与生产工单交付日期不一致`);
          console.log(`     客户订单: ${formatDate(item.deliveryDate)}`);
          console.log(`     生产工单: ${formatDate(workOrderInfo.deliveryDate)}`);
          deliveryErrors++;
        }
        
        // 验证交付日期应该在生产完成后
        const plannedEndDate = new Date(workOrderInfo.plannedEndDate);
        if (workOrderDeliveryDate < plannedEndDate) {
          console.log(`❌ ${item.id}: 交付日期早于生产完成日期`);
          deliveryErrors++;
        }
      }
    });
  });
  
  console.log(`✅ 交付日期一致性验证完成: ${deliveryErrors === 0 ? '全部一致' : `发现${deliveryErrors}个不一致`}\n`);
  
  // 6. 时间分布分析
  console.log('📈 时间分布分析:');
  console.log('-' .repeat(50));
  
  const timeAnalysis = {
    past: { count: 0, workOrders: [] },
    current: { count: 0, workOrders: [] },
    future: { count: 0, workOrders: [] }
  };
  
  workOrders.forEach(wo => {
    const startDate = new Date(wo.plannedStartDate);
    const endDate = new Date(wo.plannedEndDate);
    
    if (endDate < currentDate) {
      timeAnalysis.past.count++;
      timeAnalysis.past.workOrders.push(wo.workOrderNumber);
    } else if (startDate <= currentDate && endDate >= currentDate) {
      timeAnalysis.current.count++;
      timeAnalysis.current.workOrders.push(wo.workOrderNumber);
    } else {
      timeAnalysis.future.count++;
      timeAnalysis.future.workOrders.push(wo.workOrderNumber);
    }
  });
  
  console.log(`已完成时间段: ${timeAnalysis.past.count}个工单`);
  if (timeAnalysis.past.workOrders.length > 0) {
    console.log(`   ${timeAnalysis.past.workOrders.join(', ')}`);
  }
  
  console.log(`当前进行中: ${timeAnalysis.current.count}个工单`);
  if (timeAnalysis.current.workOrders.length > 0) {
    console.log(`   ${timeAnalysis.current.workOrders.join(', ')}`);
  }
  
  console.log(`未来计划: ${timeAnalysis.future.count}个工单`);
  if (timeAnalysis.future.workOrders.length > 0) {
    console.log(`   ${timeAnalysis.future.workOrders.join(', ')}`);
  }
  
  // 7. 总体质量评分
  console.log('\n🏆 数据质量评分:');
  console.log('=' .repeat(80));
  
  const totalErrors = formatErrors + logicErrors + deliveryErrors;
  const totalChecks = workOrders.length * 10; // 每个工单大约10个检查项
  const qualityScore = Math.max(0, (totalChecks - totalErrors) / totalChecks * 100);
  
  console.log(`总检查项: ${totalChecks}`);
  console.log(`发现问题: ${totalErrors}个`);
  console.log(`质量评分: ${qualityScore.toFixed(1)}%`);
  
  if (qualityScore >= 95) {
    console.log('🥇 数据质量优秀！完全符合业务场景要求');
  } else if (qualityScore >= 85) {
    console.log('🥈 数据质量良好，基本符合业务场景要求');
  } else if (qualityScore >= 70) {
    console.log('🥉 数据质量合格，部分需要改进');
  } else {
    console.log('❌ 数据质量需要大幅改进');
  }
  
  console.log('\n💡 验证总结:');
  console.log(`   ✓ 日期格式: ${formatErrors === 0 ? '全部正确' : '存在问题'}`);
  console.log(`   ✓ 业务逻辑: ${logicErrors === 0 ? '全部合理' : '存在问题'}`);
  console.log(`   ✓ 交付一致性: ${deliveryErrors === 0 ? '完全一致' : '存在差异'}`);
  console.log(`   ✓ 时间分布: 合理 (过去${timeAnalysis.past.count}个, 当前${timeAnalysis.current.count}个, 未来${timeAnalysis.future.count}个)`);
  
  console.log('\n' + '=' .repeat(80));
  console.log('🔍 日期数据质量验证完成');
}

// 运行验证
validateOptimizedDates();
