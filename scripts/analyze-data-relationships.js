#!/usr/bin/env node

/**
 * 分析重新组织后的生产工单数据关系
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

// 分析数据关系
function analyzeDataRelationships() {
  console.log('📊 生产工单数据关系深度分析报告\n');
  console.log('=' .repeat(80));
  
  // 1. 加载数据
  const productionOrders = loadJsonFile('public/mock/mes/production-orders.json');
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  
  if (!productionOrders || !customerOrders) {
    console.error('❌ 无法加载必要的数据文件');
    return;
  }
  
  const workOrders = productionOrders.productionOrders;
  const orders = customerOrders.orders;
  
  console.log('\n📈 数据概览:');
  console.log(`   生产工单数量: ${workOrders.length}`);
  console.log(`   客户订单数量: ${orders.length}`);
  
  let totalWorkOrderItems = 0;
  let totalCustomerOrderItems = 0;
  
  workOrders.forEach(wo => {
    totalWorkOrderItems += wo.items.length;
  });
  
  orders.forEach(order => {
    totalCustomerOrderItems += order.items.length;
  });
  
  console.log(`   生产工单项总数: ${totalWorkOrderItems}`);
  console.log(`   客户订单项总数: ${totalCustomerOrderItems}`);
  
  // 2. 详细关系分析
  console.log('\n🔗 详细关系分析:');
  console.log('-' .repeat(80));
  
  const relationshipDetails = [];
  
  workOrders.forEach((wo, index) => {
    const uniqueCustomers = [...new Set(wo.items.map(item => item.customerName))];
    const uniqueOrders = [...new Set(wo.items.map(item => item.customerOrderNumber))];
    const productFamilies = [...new Set(wo.items.map(item => item.productFamilyId))];
    
    let relationshipType = '';
    if (uniqueCustomers.length === 1 && uniqueOrders.length === 1) {
      relationshipType = '一对一';
    } else if (uniqueCustomers.length === 1 && uniqueOrders.length > 1) {
      relationshipType = '一对多';
    } else if (uniqueCustomers.length > 1 && uniqueOrders.length === 1) {
      relationshipType = '多对一';
    } else {
      relationshipType = '多对多';
    }
    
    const detail = {
      workOrderNumber: wo.workOrderNumber,
      relationshipType: relationshipType,
      customerCount: uniqueCustomers.length,
      orderCount: uniqueOrders.length,
      itemCount: wo.items.length,
      productFamilyCount: productFamilies.length,
      customers: uniqueCustomers,
      orders: uniqueOrders,
      productFamilies: productFamilies,
      priority: wo.priority,
      status: wo.status,
      businessScenario: getBusinessScenario(wo, uniqueCustomers.length, uniqueOrders.length)
    };
    
    relationshipDetails.push(detail);
    
    console.log(`\n${index + 1}. 工单 ${wo.workOrderNumber} [${relationshipType}关系]`);
    console.log(`   业务场景: ${detail.businessScenario}`);
    console.log(`   客户数量: ${uniqueCustomers.length}个 (${uniqueCustomers.join(', ')})`);
    console.log(`   订单数量: ${uniqueOrders.length}个 (${uniqueOrders.join(', ')})`);
    console.log(`   工单项数: ${wo.items.length}个`);
    console.log(`   产品族数: ${productFamilies.length}种 (${productFamilies.join(', ')})`);
    console.log(`   优先级: ${wo.priority} | 状态: ${wo.status}`);
    
    // 显示工单项详情
    console.log(`   工单项详情:`);
    wo.items.forEach((item, itemIndex) => {
      console.log(`     ${itemIndex + 1}. ${item.customerName} - ${item.customerOrderNumber}`);
      console.log(`        规格: ${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}mm`);
      console.log(`        产品族: ${item.productFamilyName} | 数量: ${item.quantity}片`);
    });
  });
  
  // 3. 关系类型统计
  console.log('\n📊 关系类型统计分析:');
  console.log('-' .repeat(80));
  
  const relationshipStats = {
    '一对一': relationshipDetails.filter(d => d.relationshipType === '一对一').length,
    '一对多': relationshipDetails.filter(d => d.relationshipType === '一对多').length,
    '多对一': relationshipDetails.filter(d => d.relationshipType === '多对一').length,
    '多对多': relationshipDetails.filter(d => d.relationshipType === '多对多').length
  };
  
  Object.entries(relationshipStats).forEach(([type, count]) => {
    const percentage = (count / workOrders.length * 100).toFixed(1);
    console.log(`   ${type}关系: ${count}个工单 (${percentage}%)`);
  });
  
  // 4. 业务场景分析
  console.log('\n🏭 业务场景分析:');
  console.log('-' .repeat(80));
  
  const scenarioStats = {};
  relationshipDetails.forEach(detail => {
    const scenario = detail.businessScenario;
    scenarioStats[scenario] = (scenarioStats[scenario] || 0) + 1;
  });
  
  Object.entries(scenarioStats).forEach(([scenario, count]) => {
    console.log(`   ${scenario}: ${count}个工单`);
  });
  
  // 5. 复杂度分析
  console.log('\n🎯 复杂度分析:');
  console.log('-' .repeat(80));
  
  const complexityAnalysis = {
    simple: relationshipDetails.filter(d => d.customerCount === 1 && d.orderCount === 1).length,
    moderate: relationshipDetails.filter(d => (d.customerCount === 1 && d.orderCount > 1) || (d.customerCount > 1 && d.orderCount === 1)).length,
    complex: relationshipDetails.filter(d => d.customerCount > 1 && d.orderCount > 1).length
  };
  
  console.log(`   简单工单 (1客户1订单): ${complexityAnalysis.simple}个`);
  console.log(`   中等复杂度工单 (1客户多订单 或 多客户1订单): ${complexityAnalysis.moderate}个`);
  console.log(`   复杂工单 (多客户多订单): ${complexityAnalysis.complex}个`);
  
  // 6. 产品族分布分析
  console.log('\n🔬 产品族分布分析:');
  console.log('-' .repeat(80));
  
  const productFamilyDistribution = {};
  relationshipDetails.forEach(detail => {
    detail.productFamilies.forEach(family => {
      productFamilyDistribution[family] = (productFamilyDistribution[family] || 0) + 1;
    });
  });
  
  Object.entries(productFamilyDistribution).forEach(([family, count]) => {
    console.log(`   ${family}: 出现在${count}个工单中`);
  });
  
  // 7. 界面测试建议
  console.log('\n💡 界面功能测试建议:');
  console.log('-' .repeat(80));
  
  console.log('\n   🎯 重点测试工单:');
  
  // 找出最复杂的工单
  const mostComplexWorkOrder = relationshipDetails.reduce((max, current) => {
    const currentComplexity = current.customerCount * current.orderCount;
    const maxComplexity = max.customerCount * max.orderCount;
    return currentComplexity > maxComplexity ? current : max;
  });
  
  console.log(`   1. 最复杂工单: ${mostComplexWorkOrder.workOrderNumber}`);
  console.log(`      - ${mostComplexWorkOrder.customerCount}个客户, ${mostComplexWorkOrder.orderCount}个订单`);
  console.log(`      - 测试多客户多订单的界面展示效果`);
  
  // 找出多客户工单
  const multiCustomerWorkOrders = relationshipDetails.filter(d => d.customerCount > 1);
  if (multiCustomerWorkOrders.length > 0) {
    console.log(`   2. 多客户工单: ${multiCustomerWorkOrders.map(d => d.workOrderNumber).join(', ')}`);
    console.log(`      - 测试客户信息的聚合显示`);
    console.log(`      - 验证客户订单追溯链接`);
  }
  
  // 找出多订单工单
  const multiOrderWorkOrders = relationshipDetails.filter(d => d.orderCount > 1);
  if (multiOrderWorkOrders.length > 0) {
    console.log(`   3. 多订单工单: ${multiOrderWorkOrders.map(d => d.workOrderNumber).join(', ')}`);
    console.log(`      - 测试订单信息的展开折叠`);
    console.log(`      - 验证订单详情的完整显示`);
  }
  
  console.log('\n   🔍 功能验证清单:');
  console.log('   ✓ 卡片视图中多客户信息的标签展示');
  console.log('   ✓ 表格视图中订单详情的折叠展开');
  console.log('   ✓ 客户订单追溯链接的正确跳转');
  console.log('   ✓ 搜索功能对多客户多订单的支持');
  console.log('   ✓ 筛选功能对复杂关系的处理');
  console.log('   ✓ 信息密度调整对复杂工单的适配');
  
  // 8. 数据质量评估
  console.log('\n🏆 数据质量评估:');
  console.log('-' .repeat(80));
  
  const qualityMetrics = {
    relationshipDiversity: Object.keys(relationshipStats).filter(key => relationshipStats[key] > 0).length,
    complexityDistribution: complexityAnalysis.complex > 0 && complexityAnalysis.moderate > 0 && complexityAnalysis.simple > 0,
    businessScenarioCoverage: Object.keys(scenarioStats).length,
    dataCompleteness: totalWorkOrderItems === totalCustomerOrderItems
  };
  
  console.log(`   关系类型多样性: ${qualityMetrics.relationshipDiversity}/4 种关系类型`);
  console.log(`   复杂度分布均衡: ${qualityMetrics.complexityDistribution ? '✅ 是' : '❌ 否'}`);
  console.log(`   业务场景覆盖: ${qualityMetrics.businessScenarioCoverage} 种场景`);
  console.log(`   数据完整性: ${qualityMetrics.dataCompleteness ? '✅ 完整' : '❌ 不完整'}`);
  
  const overallQuality = Object.values(qualityMetrics).filter(Boolean).length / Object.keys(qualityMetrics).length * 100;
  console.log(`   综合质量评分: ${overallQuality.toFixed(1)}%`);
  
  console.log('\n' + '=' .repeat(80));
  console.log('📊 数据关系分析完成');
  
  if (overallQuality >= 90) {
    console.log('🎉 数据质量优秀，完全满足多对多关系测试需求！');
  } else if (overallQuality >= 70) {
    console.log('👍 数据质量良好，基本满足测试需求');
  } else {
    console.log('⚠️  数据质量需要改进');
  }
}

// 获取业务场景描述
function getBusinessScenario(workOrder, customerCount, orderCount) {
  if (customerCount === 1 && orderCount === 1) {
    return '标准单客户订单生产';
  } else if (customerCount === 1 && orderCount > 1) {
    return '单客户多订单合并生产';
  } else if (customerCount > 1 && orderCount === 1) {
    return '多客户批量生产优化';
  } else if (customerCount > 1 && orderCount > 1) {
    if (workOrder.workOrderNumber.includes('0004')) {
      return '复杂多客户多订单编排';
    } else {
      return '高复杂度生产调度';
    }
  }
  return '未知场景';
}

// 运行分析
analyzeDataRelationships();
