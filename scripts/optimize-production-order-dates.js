#!/usr/bin/env node

/**
 * 优化生产工单MOCK数据的日期字段，使其符合实际业务场景
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取和保存JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

function saveJsonFile(relativePath, data) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`✅ 已保存文件: ${relativePath}`);
  } catch (error) {
    console.error(`❌ 无法保存文件 ${relativePath}:`, error.message);
  }
}

// 日期工具函数
function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

function addHours(date, hours) {
  const result = new Date(date);
  result.setHours(result.getHours() + hours);
  return result;
}

function formatISODate(date) {
  return date.toISOString();
}

function getWorkingDays(startDate, days) {
  let current = new Date(startDate);
  let workingDays = 0;
  let totalDays = 0;
  
  while (workingDays < days && totalDays < days * 2) {
    const dayOfWeek = current.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // 不是周末
      workingDays++;
    }
    if (workingDays < days) {
      current = addDays(current, 1);
      totalDays++;
    }
  }
  
  return current;
}

// 根据产品族计算生产周期（工作日）
function getProductionCycle(productFamilyId, quantity) {
  const baseCycles = {
    'PF-TEMPERED': 3,      // 单片钢化玻璃：3-5天
    'PF-IGU': 7,           // 中空玻璃：7-10天
    'PF-LAMINATED': 10,    // 夹胶玻璃：10-12天
    'PF-DECORATIVE': 5,    // 装饰玻璃：5-8天
    'PF-FURNITURE': 4      // 家具玻璃：4-6天
  };
  
  const baseCycle = baseCycles[productFamilyId] || 5;
  
  // 根据数量调整周期
  let quantityMultiplier = 1;
  if (quantity > 300) quantityMultiplier = 1.5;
  else if (quantity > 200) quantityMultiplier = 1.3;
  else if (quantity > 100) quantityMultiplier = 1.1;
  
  return Math.ceil(baseCycle * quantityMultiplier);
}

// 根据工单复杂度计算额外时间
function getComplexityMultiplier(customerCount, orderCount, itemCount) {
  let multiplier = 1;
  
  // 多客户增加协调时间
  if (customerCount > 1) multiplier += 0.2 * (customerCount - 1);
  
  // 多订单增加管理时间
  if (orderCount > 1) multiplier += 0.1 * (orderCount - 1);
  
  // 多工单项增加生产时间
  if (itemCount > 3) multiplier += 0.05 * (itemCount - 3);
  
  return Math.min(multiplier, 2.0); // 最大不超过2倍
}

// 主优化函数
function optimizeProductionOrderDates() {
  console.log('📅 优化生产工单MOCK数据的日期字段...\n');
  console.log('🕐 当前时间背景: 2025年8月19日\n');
  
  // 1. 加载数据
  const productionOrders = loadJsonFile('public/mock/mes/production-orders.json');
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  
  if (!productionOrders || !customerOrders) {
    console.error('❌ 无法加载必要的数据文件');
    return;
  }
  
  // 当前日期：2025年8月19日
  const currentDate = new Date('2025-08-19T09:00:00Z');
  console.log(`📊 基准日期: ${currentDate.toISOString()}\n`);
  
  // 2. 优化生产工单日期
  console.log('🔧 优化生产工单日期字段...');
  
  const optimizedWorkOrders = productionOrders.productionOrders.map((workOrder, index) => {
    console.log(`\n处理工单: ${workOrder.workOrderNumber}`);
    
    // 分析工单复杂度
    const uniqueCustomers = [...new Set(workOrder.items.map(item => item.customerName))];
    const uniqueOrders = [...new Set(workOrder.items.map(item => item.customerOrderNumber))];
    const customerCount = uniqueCustomers.length;
    const orderCount = uniqueOrders.length;
    const itemCount = workOrder.items.length;
    
    console.log(`   复杂度: ${customerCount}客户, ${orderCount}订单, ${itemCount}工单项`);
    
    // 计算创建时间（最近1-2周内）
    const createdDaysAgo = Math.floor(Math.random() * 14) + 1; // 1-14天前
    const createdHour = 8 + Math.floor(Math.random() * 10); // 8-17点
    const createdAt = new Date(currentDate);
    createdAt.setDate(createdAt.getDate() - createdDaysAgo);
    createdAt.setHours(createdHour, Math.floor(Math.random() * 60), 0, 0);
    
    // 根据优先级和状态确定计划开始时间
    let plannedStartDate;
    const priorityOffsets = {
      'urgent': -2,    // 紧急：2天前就开始
      'high': -1,      // 高：1天前开始
      'medium': 1,     // 中：1天后开始
      'low': 3         // 低：3天后开始
    };
    
    const statusOffsets = {
      'pending': 2,      // 待发布：还要等2天
      'released': 0,     // 已发布：今天或明天开始
      'in_progress': -3, // 执行中：3天前已开始
      'completed': -10   // 已完成：10天前开始
    };
    
    const priorityOffset = priorityOffsets[workOrder.priority] || 0;
    const statusOffset = statusOffsets[workOrder.status] || 0;
    
    // 综合考虑优先级和状态
    let startOffset = priorityOffset;
    if (workOrder.status === 'in_progress') {
      startOffset = Math.min(priorityOffset, statusOffsets['in_progress']);
    } else if (workOrder.status === 'completed') {
      startOffset = statusOffsets['completed'];
    } else if (workOrder.status === 'pending') {
      startOffset = Math.max(priorityOffset, statusOffsets['pending']);
    }
    
    plannedStartDate = addDays(currentDate, startOffset);
    plannedStartDate.setHours(8, 0, 0, 0); // 统一设置为早上8点开始
    
    // 计算生产周期
    let totalProductionDays = 0;
    const productFamilyCycles = new Map();
    
    workOrder.items.forEach(item => {
      const familyId = item.productFamilyId;
      const quantity = item.quantity;
      const cycle = getProductionCycle(familyId, quantity);
      
      if (!productFamilyCycles.has(familyId)) {
        productFamilyCycles.set(familyId, cycle);
      } else {
        // 相同产品族可以并行生产，取最大值
        productFamilyCycles.set(familyId, Math.max(productFamilyCycles.get(familyId), cycle));
      }
    });
    
    // 不同产品族需要串行生产
    totalProductionDays = Array.from(productFamilyCycles.values()).reduce((sum, cycle) => sum + cycle, 0);
    
    // 应用复杂度系数
    const complexityMultiplier = getComplexityMultiplier(customerCount, orderCount, itemCount);
    totalProductionDays = Math.ceil(totalProductionDays * complexityMultiplier);
    
    // 计算计划结束时间（考虑工作日）
    const plannedEndDate = getWorkingDays(plannedStartDate, totalProductionDays);
    plannedEndDate.setHours(18, 0, 0, 0); // 统一设置为下午6点结束
    
    console.log(`   创建时间: ${createdAt.toISOString().split('T')[0]} (${createdDaysAgo}天前)`);
    console.log(`   计划开始: ${plannedStartDate.toISOString().split('T')[0]} (优先级: ${workOrder.priority}, 状态: ${workOrder.status})`);
    console.log(`   生产周期: ${totalProductionDays}工作日 (复杂度系数: ${complexityMultiplier.toFixed(2)})`);
    console.log(`   计划结束: ${plannedEndDate.toISOString().split('T')[0]}`);
    
    // 优化工单项的交付日期
    const optimizedItems = workOrder.items.map(item => {
      // 交付日期应该在生产完成后1-3天
      const deliveryOffset = Math.floor(Math.random() * 3) + 1;
      const deliveryDate = addDays(plannedEndDate, deliveryOffset);
      deliveryDate.setHours(14, 0, 0, 0); // 下午2点交付
      
      return {
        ...item,
        deliveryDate: formatISODate(deliveryDate),
        createdAt: formatISODate(createdAt)
      };
    });
    
    return {
      ...workOrder,
      createdAt: formatISODate(createdAt),
      plannedStartDate: formatISODate(plannedStartDate),
      plannedEndDate: formatISODate(plannedEndDate),
      items: optimizedItems
    };
  });
  
  // 3. 更新客户订单的交付日期以保持一致性
  console.log('\n🔧 同步客户订单交付日期...');
  
  const workOrderItemMap = new Map();
  optimizedWorkOrders.forEach(wo => {
    wo.items.forEach(item => {
      workOrderItemMap.set(item.customerOrderItemId, item.deliveryDate);
    });
  });
  
  const optimizedCustomerOrders = {
    ...customerOrders,
    orders: customerOrders.orders.map(order => ({
      ...order,
      items: order.items.map(item => {
        const newDeliveryDate = workOrderItemMap.get(item.id);
        return {
          ...item,
          deliveryDate: newDeliveryDate || item.deliveryDate
        };
      })
    }))
  };
  
  // 4. 保存优化后的数据
  const optimizedProductionOrdersData = {
    ...productionOrders,
    scenario: "时间优化的生产工单数据",
    description: "基于2025年8月19日的时间背景，优化了所有日期字段以符合实际业务场景",
    lastUpdated: formatISODate(currentDate),
    timeOptimization: {
      baseDate: "2025-08-19T09:00:00Z",
      createdTimeRange: "最近1-14天内",
      productionCycles: {
        "PF-TEMPERED": "3-5工作日",
        "PF-IGU": "7-10工作日", 
        "PF-LAMINATED": "10-12工作日",
        "PF-DECORATIVE": "5-8工作日",
        "PF-FURNITURE": "4-6工作日"
      },
      priorityLogic: {
        "urgent": "提前2天开始",
        "high": "提前1天开始",
        "medium": "延后1天开始", 
        "low": "延后3天开始"
      },
      statusLogic: {
        "pending": "延后2天开始",
        "released": "按计划开始",
        "in_progress": "3天前已开始",
        "completed": "10天前开始"
      }
    },
    productionOrders: optimizedWorkOrders
  };
  
  saveJsonFile('public/mock/mes/production-orders.json', optimizedProductionOrdersData);
  saveJsonFile('public/mock/mes/customer-orders.json', optimizedCustomerOrders);
  
  // 5. 生成优化报告
  console.log('\n📊 日期优化完成统计:');
  
  const now = currentDate;
  let pastWorkOrders = 0;
  let currentWorkOrders = 0;
  let futureWorkOrders = 0;
  let urgentWorkOrders = 0;
  let inProgressWorkOrders = 0;
  let completedWorkOrders = 0;
  
  optimizedWorkOrders.forEach(wo => {
    const startDate = new Date(wo.plannedStartDate);
    const endDate = new Date(wo.plannedEndDate);
    
    if (endDate < now) pastWorkOrders++;
    else if (startDate <= now && endDate >= now) currentWorkOrders++;
    else futureWorkOrders++;
    
    if (wo.priority === 'urgent') urgentWorkOrders++;
    if (wo.status === 'in_progress') inProgressWorkOrders++;
    if (wo.status === 'completed') completedWorkOrders++;
  });
  
  console.log(`   已完成工单: ${pastWorkOrders}个`);
  console.log(`   进行中工单: ${currentWorkOrders}个`);
  console.log(`   未来工单: ${futureWorkOrders}个`);
  console.log(`   紧急工单: ${urgentWorkOrders}个`);
  console.log(`   执行中状态: ${inProgressWorkOrders}个`);
  console.log(`   已完成状态: ${completedWorkOrders}个`);
  
  // 6. 时间范围分析
  const allStartDates = optimizedWorkOrders.map(wo => new Date(wo.plannedStartDate));
  const allEndDates = optimizedWorkOrders.map(wo => new Date(wo.plannedEndDate));
  
  const earliestStart = new Date(Math.min(...allStartDates));
  const latestEnd = new Date(Math.max(...allEndDates));
  
  console.log(`\n📅 时间范围分析:`);
  console.log(`   最早开始: ${earliestStart.toISOString().split('T')[0]}`);
  console.log(`   最晚结束: ${latestEnd.toISOString().split('T')[0]}`);
  console.log(`   总时间跨度: ${Math.ceil((latestEnd - earliestStart) / (1000 * 60 * 60 * 24))}天`);
  
  console.log('\n✅ 生产工单日期优化完成！');
  console.log('🎯 所有日期字段已符合实际业务场景');
  console.log('🚀 数据时间逻辑与当前时间背景完全一致');
}

// 运行优化
optimizeProductionOrderDates();
