#!/usr/bin/env node

/**
 * 验证产品族数据的完整性和覆盖率
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取产品族数据
function loadProductFamilies() {
  const filePath = path.join(__dirname, '../public/mock/masterdata/productFamilies.json');
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('❌ 无法读取产品族数据:', error.message);
    process.exit(1);
  }
}

// 读取客户订单数据
function loadCustomerOrders() {
  const filePath = path.join(__dirname, '../public/mock/mes/customer-orders.json');
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('❌ 无法读取客户订单数据:', error.message);
    process.exit(1);
  }
}

// 验证产品族数据结构
function validateProductFamilyStructure(productFamily) {
  const requiredFields = ['id', 'name', 'category', 'attributes', 'dependencyRules', 'description', 'isActive'];
  const missingFields = requiredFields.filter(field => !productFamily.hasOwnProperty(field));
  
  if (missingFields.length > 0) {
    return { valid: false, errors: [`缺少必需字段: ${missingFields.join(', ')}`] };
  }

  // 验证属性结构
  const attributeErrors = [];
  if (!Array.isArray(productFamily.attributes)) {
    attributeErrors.push('attributes必须是数组');
  } else {
    productFamily.attributes.forEach((attr, index) => {
      const requiredAttrFields = ['id', 'label', 'type', 'defaultValue'];
      const missingAttrFields = requiredAttrFields.filter(field => !attr.hasOwnProperty(field));
      if (missingAttrFields.length > 0) {
        attributeErrors.push(`属性${index}缺少字段: ${missingAttrFields.join(', ')}`);
      }
      
      if (!['number', 'select', 'boolean'].includes(attr.type)) {
        attributeErrors.push(`属性${index}的type无效: ${attr.type}`);
      }
    });
  }

  return {
    valid: attributeErrors.length === 0,
    errors: attributeErrors
  };
}

// 分析订单项与产品族的匹配情况
function analyzeOrderCoverage(productFamilies, customerOrders) {
  const coverage = {
    totalOrderItems: 0,
    coveredItems: 0,
    uncoveredItems: [],
    coverageByFamily: {}
  };

  // 初始化产品族覆盖统计
  productFamilies.forEach(family => {
    coverage.coverageByFamily[family.id] = {
      name: family.name,
      category: family.category,
      coveredItems: 0,
      items: []
    };
  });

  // 分析每个订单项
  customerOrders.orders.forEach(order => {
    order.items.forEach(item => {
      coverage.totalOrderItems++;
      
      const matchedFamily = findMatchingProductFamily(item, productFamilies);
      if (matchedFamily) {
        coverage.coveredItems++;
        coverage.coverageByFamily[matchedFamily.id].coveredItems++;
        coverage.coverageByFamily[matchedFamily.id].items.push({
          orderId: order.id,
          itemId: item.id,
          customerName: order.customerName,
          specifications: item.specifications,
          quantity: item.quantity
        });
      } else {
        coverage.uncoveredItems.push({
          orderId: order.id,
          itemId: item.id,
          customerName: order.customerName,
          specifications: item.specifications,
          quantity: item.quantity,
          reason: '无匹配的产品族'
        });
      }
    });
  });

  coverage.coverageRate = (coverage.coveredItems / coverage.totalOrderItems * 100).toFixed(1);
  return coverage;
}

// 查找匹配的产品族
function findMatchingProductFamily(orderItem, productFamilies) {
  const specs = orderItem.specifications;
  
  // 根据订单项特征匹配产品族
  for (const family of productFamilies) {
    switch (family.category) {
      case 'tempered_glass':
        if (isTempleredGlassMatch(specs, family)) return family;
        break;
      case 'laminated_glass':
        if (isLaminatedGlassMatch(specs, orderItem)) return family;
        break;
      case 'decorative_glass':
        if (isDecorativeGlassMatch(specs, orderItem)) return family;
        break;
      case 'furniture_glass':
        if (isFurnitureGlassMatch(specs, orderItem)) return family;
        break;
      case 'insulated_glass':
        if (isInsulatedGlassMatch(specs, orderItem)) return family;
        break;
    }
  }
  
  return null;
}

// 匹配逻辑函数
function isTempleredGlassMatch(specs, family) {
  // 单片钢化玻璃：适用于幕墙、门窗、阳光房、展示柜、特殊工艺
  const supportedTypes = ['clear', 'low_e', 'tinted', 'reflective'];
  const supportedThickness = [5, 6, 8, 10, 12];
  
  return supportedTypes.includes(specs.glassType) && 
         supportedThickness.includes(specs.thickness);
}

function isLaminatedGlassMatch(specs, orderItem) {
  // 夹胶玻璃：通过订单备注识别
  const notes = orderItem.notes || '';
  return notes.includes('夹胶') || notes.includes('PVB');
}

function isDecorativeGlassMatch(specs, orderItem, customerOrders) {
  // 装饰玻璃：着色玻璃、反射玻璃、隔断用途
  const order = customerOrders.orders.find(o => o.id === orderItem.customerOrderId);
  const orderType = order?.orderType || '';

  return specs.glassType === 'tinted' ||
         specs.glassType === 'reflective' ||
         orderType.includes('装饰') ||
         orderType.includes('隔断');
}

function isFurnitureGlassMatch(specs, orderItem, customerOrders) {
  // 家具玻璃：通过订单类型识别
  const notes = orderItem.notes || '';
  const order = customerOrders.orders.find(o => o.id === orderItem.customerOrderId);
  const orderType = order?.orderType || '';

  return notes.includes('餐桌') || notes.includes('茶几') || notes.includes('家具') ||
         orderType.includes('家具');
}

function isInsulatedGlassMatch(specs, orderItem) {
  // 中空玻璃：通过订单备注识别
  const notes = orderItem.notes || '';
  return notes.includes('中空') || (notes.includes('+') && notes.includes('A'));
}

// 主验证函数
function main() {
  console.log('🔍 开始验证产品族数据...\n');

  // 1. 加载数据
  const productFamilies = loadProductFamilies();
  const customerOrders = loadCustomerOrders();
  
  console.log(`📊 数据概览:`);
  console.log(`   产品族数量: ${productFamilies.length}`);
  console.log(`   客户订单数量: ${customerOrders.orders.length}`);
  console.log(`   订单项总数: ${customerOrders.orders.reduce((sum, order) => sum + order.items.length, 0)}\n`);

  // 2. 验证产品族数据结构
  console.log('🔧 验证产品族数据结构:');
  let structureValid = true;
  
  productFamilies.forEach((family, index) => {
    const validation = validateProductFamilyStructure(family);
    if (validation.valid) {
      console.log(`   ✅ ${family.name} (${family.id})`);
    } else {
      console.log(`   ❌ ${family.name} (${family.id}):`);
      validation.errors.forEach(error => console.log(`      - ${error}`));
      structureValid = false;
    }
  });

  if (!structureValid) {
    console.log('\n❌ 产品族数据结构验证失败');
    process.exit(1);
  }

  // 3. 分析订单覆盖情况
  console.log('\n📈 分析订单覆盖情况:');
  const coverage = analyzeOrderCoverage(productFamilies, customerOrders);
  
  console.log(`   总订单项: ${coverage.totalOrderItems}`);
  console.log(`   已覆盖: ${coverage.coveredItems}`);
  console.log(`   未覆盖: ${coverage.uncoveredItems.length}`);
  console.log(`   覆盖率: ${coverage.coverageRate}%\n`);

  // 4. 按产品族显示覆盖情况
  console.log('📋 各产品族覆盖情况:');
  Object.entries(coverage.coverageByFamily).forEach(([familyId, data]) => {
    if (data.coveredItems > 0) {
      console.log(`   ✅ ${data.name}: ${data.coveredItems}个订单项`);
    } else {
      console.log(`   ⚠️  ${data.name}: 0个订单项`);
    }
  });

  // 5. 显示未覆盖的订单项
  if (coverage.uncoveredItems.length > 0) {
    console.log('\n❌ 未覆盖的订单项:');
    coverage.uncoveredItems.forEach(item => {
      console.log(`   - ${item.customerName} (${item.orderId}): ${item.specifications.glassType} ${item.specifications.thickness}mm`);
    });
  }

  // 6. 总结
  console.log('\n📊 验证总结:');
  if (coverage.coverageRate >= 90) {
    console.log(`   ✅ 产品族覆盖率达标 (${coverage.coverageRate}%)`);
    console.log('   ✅ 数据结构验证通过');
    console.log('   ✅ 产品族配置满足业务需求');
  } else {
    console.log(`   ⚠️  产品族覆盖率不足 (${coverage.coverageRate}%，建议≥90%)`);
    console.log('   ❌ 需要补充或调整产品族配置');
  }
}

// 运行验证
main();
