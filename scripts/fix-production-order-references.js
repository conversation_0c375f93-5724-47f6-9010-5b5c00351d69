#!/usr/bin/env node

/**
 * 修复生产工单与客户订单之间的外键引用问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取和保存JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

function saveJsonFile(relativePath, data) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`✅ 已保存文件: ${relativePath}`);
  } catch (error) {
    console.error(`❌ 无法保存文件 ${relativePath}:`, error.message);
  }
}

// 生成生产工单ID
function generateProductionOrderId(index) {
  return `WO-2024-${String(index).padStart(3, '0')}`;
}

// 生成生产工单项ID
function generateProductionOrderItemId(index) {
  return `WOI-${String(index).padStart(3, '0')}`;
}

// 根据客户订单项生成工艺流程
function generateProcessFlow(customerOrderItem) {
  const productFamilyId = customerOrderItem.productFamilyId;
  const glassType = customerOrderItem.specifications.glassType;
  const thickness = customerOrderItem.specifications.thickness;
  
  let processFlow = [];
  
  // 基础工艺流程
  processFlow.push({ stepName: "切割", workstation: "cold_processing" });
  processFlow.push({ stepName: "磨边", workstation: "cold_processing" });
  
  // 根据产品族和玻璃类型添加特殊工艺
  switch (productFamilyId) {
    case 'PF-TEMPERED':
      if (glassType === 'low_e' || glassType === 'reflective') {
        processFlow.push({ stepName: "镀膜", workstation: "coating" });
      }
      processFlow.push({ stepName: "钢化", workstation: "tempering" });
      break;
      
    case 'PF-IGU':
      if (glassType === 'low_e') {
        processFlow.push({ stepName: "镀膜", workstation: "coating" });
      }
      processFlow.push({ stepName: "钢化", workstation: "tempering" });
      processFlow.push({ stepName: "合片", workstation: "insulating" });
      processFlow.push({ stepName: "充气", workstation: "insulating" });
      break;
      
    case 'PF-LAMINATED':
      processFlow.push({ stepName: "清洗", workstation: "cleaning" });
      processFlow.push({ stepName: "夹胶", workstation: "laminating" });
      processFlow.push({ stepName: "高压釜", workstation: "autoclave" });
      break;
      
    case 'PF-DECORATIVE':
      if (glassType === 'reflective' || glassType === 'tinted') {
        processFlow.push({ stepName: "镀膜", workstation: "coating" });
      }
      processFlow.push({ stepName: "表面处理", workstation: "surface_treatment" });
      break;
      
    case 'PF-FURNITURE':
      processFlow.push({ stepName: "抛光", workstation: "polishing" });
      if (thickness >= 8) {
        processFlow.push({ stepName: "钢化", workstation: "tempering" });
      }
      break;
  }
  
  // 通用结束工艺
  processFlow.push({ stepName: "质检", workstation: "quality_control" });
  processFlow.push({ stepName: "包装", workstation: "packaging" });
  
  return processFlow;
}

// 主修复函数
function main() {
  console.log('🔧 开始修复生产工单与客户订单的外键引用关系...\n');
  
  // 1. 加载数据
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  const productionOrders = loadJsonFile('public/mock/mes/production-orders.json');
  const workOrders = loadJsonFile('public/mock/mes/workOrders.json');
  
  if (!customerOrders || !productionOrders) {
    console.error('❌ 无法加载必要的数据文件');
    return;
  }
  
  // 创建客户订单映射
  const customerOrderMap = new Map();
  const customerOrderItemMap = new Map();
  
  customerOrders.orders.forEach(order => {
    customerOrderMap.set(order.id, order);
    order.items.forEach(item => {
      customerOrderItemMap.set(item.id, {
        ...item,
        customerOrderId: order.id,
        customerName: order.customerName,
        orderType: order.orderType,
        orderNumber: order.orderNumber
      });
    });
  });
  
  console.log(`📊 数据概览:`);
  console.log(`   客户订单项数量: ${customerOrderItemMap.size}`);
  console.log(`   现有生产工单数量: ${productionOrders.productionOrders.length}\n`);
  
  // 2. 重新生成完整的生产工单数据
  console.log('🔧 重新生成生产工单数据...');
  
  const newProductionOrders = [];
  let productionOrderIndex = 1;
  let productionOrderItemIndex = 1;
  
  // 按客户订单分组生成生产工单
  customerOrders.orders.forEach(customerOrder => {
    if (customerOrder.items.length === 0) return;
    
    const productionOrder = {
      id: generateProductionOrderId(productionOrderIndex),
      workOrderNumber: `WO-2024-${String(productionOrderIndex).padStart(4, '0')}`,
      customerOrderId: customerOrder.id,
      customerOrderNumber: customerOrder.orderNumber,
      customerName: customerOrder.customerName,
      orderType: customerOrder.orderType,
      priority: customerOrder.priority || 'medium',
      status: 'pending',
      createdAt: new Date().toISOString(),
      items: []
    };
    
    // 为每个客户订单项生成生产工单项
    customerOrder.items.forEach(customerOrderItem => {
      const productionOrderItem = {
        id: generateProductionOrderItemId(productionOrderItemIndex),
        productionOrderId: productionOrder.id,
        customerOrderItemId: customerOrderItem.id,
        productFamilyId: customerOrderItem.productFamilyId,
        productFamilyName: customerOrderItem.productFamilyName,
        specifications: {
          name: `${customerOrderItem.specifications.thickness}mm ${customerOrderItem.specifications.glassType} ${customerOrderItem.specifications.color}玻璃`,
          length: customerOrderItem.specifications.length || customerOrderItem.specifications.width,
          width: customerOrderItem.specifications.width || customerOrderItem.specifications.length,
          thickness: customerOrderItem.specifications.thickness,
          glassType: customerOrderItem.specifications.glassType,
          color: customerOrderItem.specifications.color
        },
        quantity: customerOrderItem.quantity,
        unitPrice: customerOrderItem.unitPrice,
        totalAmount: customerOrderItem.totalAmount,
        deliveryDate: customerOrderItem.deliveryDate,
        processFlow: generateProcessFlow(customerOrderItem),
        status: 'pending',
        createdAt: new Date().toISOString()
      };
      
      productionOrder.items.push(productionOrderItem);
      productionOrderItemIndex++;
      
      console.log(`✅ 生成工单项 ${productionOrderItem.id} → ${customerOrderItem.id} (${customerOrder.customerName})`);
    });
    
    newProductionOrders.push(productionOrder);
    productionOrderIndex++;
  });
  
  // 3. 保存新的生产工单数据
  const newProductionOrdersData = {
    scenario: "完整的生产工单数据",
    description: "从客户订单完整转换而来的生产工单，确保100%覆盖和数据一致性",
    lastUpdated: new Date().toISOString(),
    productionOrders: newProductionOrders
  };
  
  saveJsonFile('public/mock/mes/production-orders.json', newProductionOrdersData);
  
  // 4. 修复workOrders.json
  console.log('\n🔧 修复 workOrders.json...');
  
  if (workOrders && customerOrderItemMap.size > 0) {
    // 使用第一个客户订单项作为示例
    const firstOrderItem = Array.from(customerOrderItemMap.values())[0];
    const firstOrder = customerOrderMap.get(firstOrderItem.customerOrderId);
    
    const fixedWorkOrders = {
      data: [
        {
          id: "wo_001",
          workOrderNumber: "WO-2024-0001",
          orderId: firstOrder.id,  // 修复为正确的客户订单ID
          orderItemId: firstOrderItem.id,  // 修复为正确的客户订单项ID
          customerName: firstOrder.customerName,
          productType: firstOrder.orderType,
          status: "pending",
          priority: firstOrder.priority || "medium",
          plannedStartDate: "2024-01-20T08:00:00Z",
          plannedEndDate: "2024-01-25T18:00:00Z",
          processRoute: {
            id: "route_001",
            name: `${firstOrder.orderType}加工路线`,
            steps: generateProcessFlow(firstOrderItem).map((step, index) => ({
              id: `step_${String(index + 1).padStart(3, '0')}`,
              name: step.stepName,
              workCenter: step.workstation,
              operation: step.stepName.toLowerCase(),
              status: "pending",
              duration: 120
            }))
          },
          createdAt: new Date().toISOString()
        }
      ],
      success: true,
      message: "工单数据加载成功"
    };
    
    saveJsonFile('public/mock/mes/workOrders.json', fixedWorkOrders);
    console.log(`✅ 修复工单 wo_001 → ${firstOrder.id}/${firstOrderItem.id}`);
  }
  
  // 5. 统计报告
  const totalProductionOrderItems = newProductionOrders.reduce((sum, po) => sum + po.items.length, 0);
  
  console.log('\n📊 修复完成统计:');
  console.log(`   生成生产工单数量: ${newProductionOrders.length}`);
  console.log(`   生成生产工单项数量: ${totalProductionOrderItems}`);
  console.log(`   客户订单项覆盖率: 100.0% (${totalProductionOrderItems}/${customerOrderItemMap.size})`);
  console.log(`   修复workOrders数量: ${workOrders ? 1 : 0}`);
  
  console.log('\n✅ 生产工单与客户订单外键引用关系修复完成！');
  console.log('✅ 所有客户订单项现在都有对应的生产工单');
  console.log('✅ 规格和数量完全一致');
  console.log('✅ 工艺流程基于产品族自动生成');
}

// 运行修复
main();
