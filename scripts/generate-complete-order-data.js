#!/usr/bin/env node

/**
 * 生成包含完整产品族引用的客户订单数据
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 产品族引用映射
const productFamilyMappings = {
  'COI-001': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 1200, height: 1800, thickness: '6', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-002': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 1600, height: 2400, thickness: '6', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-003': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 1000, height: 1500, thickness: '6', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-004': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 800, height: 1200, thickness: '5', glass_type: 'clear', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'none' }},
  'COI-005': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 1200, height: 1500, thickness: '5', glass_type: 'clear', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'none' }},
  'COI-006': { familyId: 'PF-DECORATIVE', familyName: '装饰玻璃产品族', config: { width: 1400, height: 2000, thickness: '8', glass_type: 'tinted', color: '灰色', surface_finish: 'smooth', transparency_level: 'translucent', pattern_type: 'none', edge_treatment: 'polished' }},
  'COI-007': { familyId: 'PF-IGU', familyName: '中空玻璃产品族', config: { width: 1000, height: 1600, glass1_thickness: '6', glass2_thickness: '6', spacer_width: '12', glass_type: 'low_e', color: '透明', is_tempered: false, gas_filling: 'air', sealant_type: 'structural', energy_rating: 'high' }},
  'COI-008': { familyId: 'PF-IGU', familyName: '中空玻璃产品族', config: { width: 800, height: 1400, glass1_thickness: '5', glass2_thickness: '5', spacer_width: '9', glass_type: 'low_e', color: '透明', is_tempered: false, gas_filling: 'air', sealant_type: 'structural', energy_rating: 'high' }},
  'COI-009': { familyId: 'PF-LAMINATED', familyName: '夹胶玻璃产品族', config: { width: 1500, height: 2200, total_thickness: '10', glass1_thickness: '5', glass2_thickness: '5', pvb_thickness: '1.14', glass_type: 'clear', color: '透明', safety_level: 'standard', pvb_type: 'standard' }},
  'COI-010': { familyId: 'PF-LAMINATED', familyName: '夹胶玻璃产品族', config: { width: 1200, height: 1800, total_thickness: '8', glass1_thickness: '4', glass2_thickness: '4', pvb_thickness: '0.76', glass_type: 'clear', color: '透明', safety_level: 'standard', pvb_type: 'standard' }},
  'COI-011': { familyId: 'PF-DECORATIVE', familyName: '装饰玻璃产品族', config: { width: 600, height: 1200, thickness: '6', glass_type: 'reflective', color: '蓝色', surface_finish: 'smooth', transparency_level: 'transparent', pattern_type: 'none', edge_treatment: 'polished' }},
  'COI-012': { familyId: 'PF-DECORATIVE', familyName: '装饰玻璃产品族', config: { width: 600, height: 800, thickness: '5', glass_type: 'tinted', color: '茶色', surface_finish: 'smooth', transparency_level: 'translucent', pattern_type: 'none', edge_treatment: 'polished' }},
  'COI-013': { familyId: 'PF-FURNITURE', familyName: '家具玻璃产品族', config: { width: 500, height: 1000, thickness: '8', glass_type: 'clear', color: '透明', edge_processing: 'polished', corner_type: 'rounded', safety_treatment: 'tempered', surface_quality: 'premium', drilling_required: false }},
  'COI-014': { familyId: 'PF-FURNITURE', familyName: '家具玻璃产品族', config: { width: 400, height: 600, thickness: '5', glass_type: 'clear', color: '透明', edge_processing: 'polished', corner_type: 'rounded', safety_treatment: 'tempered', surface_quality: 'premium', drilling_required: false }},
  'COI-015': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 1000, height: 2000, thickness: '8', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-016': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 2000, height: 1500, thickness: '6', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-017': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 300, height: 800, thickness: '5', glass_type: 'clear', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'none' }},
  'COI-018': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 400, height: 1000, thickness: '6', glass_type: 'clear', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'none' }},
  'COI-019': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 1200, height: 1800, thickness: '12', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'polished', surface_treatment: 'coating' }},
  'COI-020': { familyId: 'PF-TEMPERED', familyName: '单片钢化玻璃产品族', config: { width: 1600, height: 2400, thickness: '10', glass_type: 'reflective', color: '金色', is_tempered: true, edge_type: 'polished', surface_treatment: 'coating' }}
};

// 生成配置哈希
function generateConfigHash(config) {
  const configString = JSON.stringify(config, Object.keys(config).sort());
  return Buffer.from(configString).toString('base64').substring(0, 16);
}

console.log('🔍 开始生成完整的客户订单数据...\n');

// 读取原始订单数据
const filePath = path.join(__dirname, '../public/mock/mes/customer-orders.json');
const customerOrders = JSON.parse(fs.readFileSync(filePath, 'utf8'));

let addedCount = 0;
let updatedCount = 0;

// 为每个订单项添加或更新产品族引用
customerOrders.orders.forEach(order => {
  order.items.forEach(item => {
    const mapping = productFamilyMappings[item.id];
    if (mapping) {
      const hasExisting = item.productFamilyId;
      
      // 添加产品族引用
      item.productFamilyId = mapping.familyId;
      item.productFamilyName = mapping.familyName;
      item.productFamilyConfig = mapping.config;
      item.configurationHash = generateConfigHash(mapping.config);
      
      if (hasExisting) {
        updatedCount++;
        console.log(`🔄 ${item.id}: 更新 ${mapping.familyName} 引用`);
      } else {
        addedCount++;
        console.log(`✅ ${item.id}: 添加 ${mapping.familyName} 引用`);
      }
    } else {
      console.log(`❌ ${item.id}: 未找到产品族映射`);
    }
  });
});

// 保存更新后的数据
fs.writeFileSync(filePath, JSON.stringify(customerOrders, null, 2), 'utf8');

console.log(`\n📊 数据生成完成:`);
console.log(`   新增产品族引用: ${addedCount}个`);
console.log(`   更新产品族引用: ${updatedCount}个`);
console.log(`   处理订单项总数: ${Object.keys(productFamilyMappings).length}个`);
console.log(`\n✅ 完整客户订单数据生成完成！`);
