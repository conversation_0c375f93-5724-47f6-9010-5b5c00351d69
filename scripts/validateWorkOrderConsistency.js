#!/usr/bin/env node

/**
 * 工单编号一致性验证脚本
 * 检查所有文件中的工单编号格式是否统一
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 需要检查的文件列表
const filesToCheck = [
  'public/mock/mes/customer-orders.json',
  'public/mock/mes/production-orders.json', 
  'public/mock/mes/workOrders.json',
  'public/mock/mes/production-release-workbench.json'
];

// 工单编号格式模式
const patterns = {
  shortFormat: /WO-2024-\d{3}$/,  // WO-2024-001, WO-2024-002, etc.
  longFormat: /WO-2024-\d{4}$/,   // WO-2024-0001, WO-2024-0002, etc.
  allFormats: /WO-2024-\d{3,4}/g
};

function loadJsonFile(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return null;
    }
    const content = fs.readFileSync(fullPath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.log(`❌ 读取文件失败 ${filePath}: ${error.message}`);
    return null;
  }
}

function extractWorkOrderNumbers(obj, path = '') {
  const results = [];
  
  if (typeof obj === 'string') {
    const matches = obj.match(patterns.allFormats);
    if (matches) {
      matches.forEach(match => {
        results.push({
          value: match,
          path: path,
          isShortFormat: patterns.shortFormat.test(match),
          isLongFormat: patterns.longFormat.test(match)
        });
      });
    }
  } else if (Array.isArray(obj)) {
    obj.forEach((item, index) => {
      results.push(...extractWorkOrderNumbers(item, `${path}[${index}]`));
    });
  } else if (obj && typeof obj === 'object') {
    Object.entries(obj).forEach(([key, value]) => {
      const newPath = path ? `${path}.${key}` : key;
      results.push(...extractWorkOrderNumbers(value, newPath));
    });
  }
  
  return results;
}

function validateFile(filePath) {
  console.log(`\n🔍 检查文件: ${filePath}`);
  console.log('-'.repeat(60));
  
  const data = loadJsonFile(filePath);
  if (!data) return { file: filePath, valid: false, issues: ['文件加载失败'] };
  
  const workOrderNumbers = extractWorkOrderNumbers(data);
  const issues = [];
  const shortFormatNumbers = [];
  const longFormatNumbers = [];
  
  workOrderNumbers.forEach(item => {
    if (item.isShortFormat) {
      shortFormatNumbers.push(item);
    } else if (item.isLongFormat) {
      longFormatNumbers.push(item);
    }
  });
  
  console.log(`   找到工单编号: ${workOrderNumbers.length} 个`);
  console.log(`   短格式 (WO-2024-XXX): ${shortFormatNumbers.length} 个`);
  console.log(`   长格式 (WO-2024-XXXX): ${longFormatNumbers.length} 个`);
  
  // 检查格式一致性
  if (shortFormatNumbers.length > 0 && longFormatNumbers.length > 0) {
    issues.push('同一文件中存在不同格式的工单编号');
    console.log('   ❌ 格式不一致！');
    
    // 显示不一致的详细信息
    if (longFormatNumbers.length <= 10) {
      console.log('   长格式编号位置:');
      longFormatNumbers.forEach(item => {
        console.log(`      ${item.value} at ${item.path}`);
      });
    }
  } else {
    console.log('   ✅ 格式一致');
  }
  
  // 检查ID和workOrderNumber的一致性
  if (filePath.includes('production-orders.json') || filePath.includes('workOrders.json')) {
    const idWorkOrderPairs = [];
    
    function findIdWorkOrderPairs(obj, currentPath = '') {
      if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
        if (obj.id && obj.workOrderNumber) {
          idWorkOrderPairs.push({
            id: obj.id,
            workOrderNumber: obj.workOrderNumber,
            path: currentPath
          });
        }
        
        Object.entries(obj).forEach(([key, value]) => {
          if (typeof value === 'object') {
            findIdWorkOrderPairs(value, currentPath ? `${currentPath}.${key}` : key);
          }
        });
      } else if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
          findIdWorkOrderPairs(item, `${currentPath}[${index}]`);
        });
      }
    }
    
    findIdWorkOrderPairs(data);
    
    console.log(`   ID-WorkOrderNumber 对: ${idWorkOrderPairs.length} 个`);
    
    idWorkOrderPairs.forEach(pair => {
      if (pair.id !== pair.workOrderNumber) {
        issues.push(`ID与workOrderNumber不匹配: ${pair.id} != ${pair.workOrderNumber}`);
        console.log(`   ❌ 不匹配: ${pair.id} != ${pair.workOrderNumber} at ${pair.path}`);
      }
    });
    
    if (idWorkOrderPairs.length > 0 && issues.length === 0) {
      console.log('   ✅ ID与workOrderNumber匹配');
    }
  }
  
  return {
    file: filePath,
    valid: issues.length === 0,
    issues: issues,
    stats: {
      total: workOrderNumbers.length,
      shortFormat: shortFormatNumbers.length,
      longFormat: longFormatNumbers.length
    }
  };
}

function main() {
  console.log('🔧 工单编号一致性验证');
  console.log('='.repeat(60));
  
  const results = [];
  let totalIssues = 0;
  
  // 验证每个文件
  filesToCheck.forEach(filePath => {
    const result = validateFile(filePath);
    results.push(result);
    totalIssues += result.issues.length;
  });
  
  // 跨文件一致性检查
  console.log('\n🔗 跨文件一致性检查');
  console.log('-'.repeat(60));
  
  const allWorkOrderNumbers = new Set();
  const fileWorkOrderNumbers = new Map();
  
  results.forEach(result => {
    if (result.valid || result.stats) {
      const data = loadJsonFile(result.file);
      if (data) {
        const numbers = extractWorkOrderNumbers(data);
        const uniqueNumbers = [...new Set(numbers.map(n => n.value))];
        fileWorkOrderNumbers.set(result.file, uniqueNumbers);
        uniqueNumbers.forEach(num => allWorkOrderNumbers.add(num));
      }
    }
  });
  
  console.log(`总共发现 ${allWorkOrderNumbers.size} 个不同的工单编号`);
  
  // 检查引用一致性
  const referenceIssues = [];
  fileWorkOrderNumbers.forEach((numbers, filePath) => {
    numbers.forEach(number => {
      // 检查是否在其他文件中也存在
      let foundInOtherFiles = false;
      fileWorkOrderNumbers.forEach((otherNumbers, otherFilePath) => {
        if (filePath !== otherFilePath && otherNumbers.includes(number)) {
          foundInOtherFiles = true;
        }
      });
      
      if (!foundInOtherFiles && numbers.length > 1) {
        // 这个工单编号只在一个文件中出现，可能是孤立引用
        console.log(`⚠️  ${number} 只在 ${path.basename(filePath)} 中出现`);
      }
    });
  });
  
  // 总结报告
  console.log('\n📊 验证结果总结');
  console.log('='.repeat(60));
  
  const validFiles = results.filter(r => r.valid).length;
  const invalidFiles = results.filter(r => !r.valid).length;
  
  console.log(`✅ 通过验证的文件: ${validFiles}/${results.length}`);
  console.log(`❌ 存在问题的文件: ${invalidFiles}/${results.length}`);
  console.log(`🔍 发现的问题总数: ${totalIssues}`);
  
  if (totalIssues === 0) {
    console.log('\n🎉 所有工单编号格式已统一！');
    console.log('   - 所有文件使用一致的短格式 (WO-2024-XXX)');
    console.log('   - ID与workOrderNumber完全匹配');
    console.log('   - 跨文件引用关系正确');
  } else {
    console.log('\n⚠️  仍存在需要修复的问题:');
    results.forEach(result => {
      if (!result.valid) {
        console.log(`\n${result.file}:`);
        result.issues.forEach(issue => {
          console.log(`   - ${issue}`);
        });
      }
    });
  }
  
  return totalIssues === 0;
}

// 运行验证
if (import.meta.url === `file://${process.argv[1]}`) {
  const success = main();
  process.exit(success ? 0 : 1);
}

export { main, validateFile };
