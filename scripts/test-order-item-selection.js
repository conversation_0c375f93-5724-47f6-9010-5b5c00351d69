#!/usr/bin/env node

/**
 * 测试优化后的"选择订单项"功能
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

// 模拟界面中的辅助函数
function getOrderTypeVariant(orderType) {
  const typeMap = {
    '幕墙工程': 'default',
    '门窗工程': 'secondary', 
    '办公隔断': 'outline',
    '家具玻璃': 'destructive',
    '装饰玻璃': 'default',
    '阳光房': 'secondary',
    '展示柜': 'outline',
    '特殊工艺': 'destructive'
  };
  return typeMap[orderType] || 'outline';
}

function getPriorityText(priority) {
  const priorityMap = {
    'urgent': '紧急',
    'high': '高',
    'medium': '中',
    'low': '低'
  };
  return priorityMap[priority] || priority;
}

function getTotalQuantity(order) {
  return order.items.reduce((sum, item) => sum + item.quantity, 0);
}

function getUniqueProductFamilies(order) {
  const families = new Set(order.items.map(item => item.productFamilyId || 'unknown'));
  return Array.from(families);
}

function getEstimatedDuration(order) {
  const maxDuration = Math.max(...order.items.map(item => {
    const processFlow = item.processFlow || [];
    return processFlow.reduce((sum, step) => sum + (step.estimatedDuration || 0), 0);
  }));
  
  const days = Math.ceil(maxDuration / (8 * 60)); // 假设每天8小时工作
  return days;
}

function isUrgentDelivery(order) {
  const deliveryDate = new Date(order.deliveryDate);
  const now = new Date();
  const daysLeft = Math.ceil((deliveryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  return daysLeft <= 7; // 7天内交期视为紧急
}

function getMTOCharacteristics(order) {
  const characteristics = [];
  
  // 个性化规格
  const uniqueSpecs = new Set(order.items.map(item => 
    `${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}`
  ));
  if (uniqueSpecs.size > 1) {
    characteristics.push({
      type: 'custom_specs',
      label: '多规格定制'
    });
  }
  
  // 特殊工艺
  const hasSpecialProcess = order.items.some(item => 
    item.processFlow?.some(step => 
      ['镀膜', '夹胶', '钢化', '表面处理'].includes(step.stepName)
    )
  );
  if (hasSpecialProcess) {
    characteristics.push({
      type: 'special_process',
      label: '特殊工艺'
    });
  }
  
  // 大批量
  const totalQty = getTotalQuantity(order);
  if (totalQty > 500) {
    characteristics.push({
      type: 'large_batch',
      label: '大批量'
    });
  }
  
  // 紧急订单
  if (order.priority === 'urgent') {
    characteristics.push({
      type: 'urgent',
      label: '紧急订单'
    });
  }
  
  return characteristics;
}

function getProductFamilyName(item) {
  const familyId = item.productFamilyId;
  const nameMap = {
    'PF-TEMPERED': '单片钢化',
    'PF-IGU': '中空玻璃',
    'PF-LAMINATED': '夹胶玻璃',
    'PF-DECORATIVE': '装饰玻璃',
    'PF-FURNITURE': '家具玻璃'
  };
  return nameMap[familyId] || '其他';
}

function getItemArea(item) {
  const area = (item.specifications.length * item.specifications.width) / 1000000; // 转换为平方米
  return (area * item.quantity).toFixed(2);
}

// 主测试函数
function testOrderItemSelection() {
  console.log('🔍 测试优化后的"选择订单项"功能\n');
  console.log('=' .repeat(80));
  
  // 1. 加载数据
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  
  if (!customerOrders) {
    console.error('❌ 无法加载客户订单数据');
    return;
  }
  
  const orders = customerOrders.orders;
  
  console.log(`\n📊 数据概览:`);
  console.log(`   客户订单数量: ${orders.length}`);
  
  let totalItems = 0;
  let totalQuantity = 0;
  orders.forEach(order => {
    totalItems += order.items.length;
    totalQuantity += getTotalQuantity(order);
  });
  
  console.log(`   订单项总数: ${totalItems}`);
  console.log(`   总数量: ${totalQuantity}片\n`);
  
  // 2. 测试订单头部信息优化
  console.log('📋 订单头部信息优化测试:');
  console.log('-' .repeat(80));
  
  const headerTests = {
    orderTypeDisplay: 0,
    priorityDisplay: 0,
    projectInfo: 0,
    statisticsDisplay: 0,
    urgentDelivery: 0,
    mtoCharacteristics: 0
  };
  
  orders.forEach((order, index) => {
    console.log(`\n${index + 1}. 订单 ${order.orderNumber}:`);
    
    // 订单类型显示
    const orderTypeVariant = getOrderTypeVariant(order.orderType);
    console.log(`   📝 订单类型: ${order.orderType} (样式: ${orderTypeVariant})`);
    headerTests.orderTypeDisplay++;
    
    // 优先级显示
    const priorityText = getPriorityText(order.priority);
    console.log(`   ⚡ 优先级: ${priorityText}`);
    headerTests.priorityDisplay++;
    
    // 项目信息
    if (order.projectName) {
      console.log(`   🏗️  项目: ${order.projectName}`);
      headerTests.projectInfo++;
    }
    
    // 统计信息
    const totalQty = getTotalQuantity(order);
    const uniqueFamilies = getUniqueProductFamilies(order);
    const estimatedDays = getEstimatedDuration(order);
    console.log(`   📊 统计: ${order.items.length}个产品项, ${totalQty}片, ${uniqueFamilies.length}种产品族, ${estimatedDays}工作日`);
    headerTests.statisticsDisplay++;
    
    // 紧急交期检查
    if (isUrgentDelivery(order)) {
      console.log(`   🚨 紧急交期: 7天内交期`);
      headerTests.urgentDelivery++;
    }
    
    // MTO特征
    const mtoChars = getMTOCharacteristics(order);
    if (mtoChars.length > 0) {
      console.log(`   🎯 MTO特征: ${mtoChars.map(c => c.label).join(', ')}`);
      headerTests.mtoCharacteristics++;
    }
  });
  
  // 3. 测试订单项详细信息优化
  console.log('\n🔧 订单项详细信息优化测试:');
  console.log('-' .repeat(80));
  
  const itemTests = {
    productFamilyDisplay: 0,
    businessMetrics: 0,
    processFlowDisplay: 0,
    specialRequirements: 0,
    areaCalculation: 0
  };
  
  orders.slice(0, 3).forEach((order, orderIndex) => {
    console.log(`\n订单 ${order.orderNumber} 的工单项:`);
    
    order.items.forEach((item, itemIndex) => {
      console.log(`\n  工单项 ${itemIndex + 1}:`);
      
      // 产品族显示
      const familyName = getProductFamilyName(item);
      console.log(`    🏷️  产品族: ${familyName}`);
      itemTests.productFamilyDisplay++;
      
      // 业务指标
      const area = getItemArea(item);
      console.log(`    📏 规格: ${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}mm`);
      console.log(`    📦 数量: ${item.quantity}片`);
      console.log(`    📐 面积: ${area}m²`);
      console.log(`    💰 单价: ¥${item.unitPrice}, 总额: ¥${item.totalAmount.toLocaleString()}`);
      itemTests.businessMetrics++;
      itemTests.areaCalculation++;
      
      // 工艺流程显示
      if (item.processFlow && item.processFlow.length > 0) {
        console.log(`    ⚙️  工艺流程: ${item.processFlow.length}道工序`);
        const processSteps = item.processFlow.slice(0, 3).map(step => 
          `${step.stepName}(${step.estimatedDuration}min)`
        ).join(' → ');
        console.log(`    🔄 主要工序: ${processSteps}${item.processFlow.length > 3 ? '...' : ''}`);
        itemTests.processFlowDisplay++;
      }
      
      // 特殊要求
      if (item.notes) {
        console.log(`    ⚠️  特殊要求: ${item.notes}`);
        itemTests.specialRequirements++;
      }
    });
  });
  
  // 4. MTO模式特征分析
  console.log('\n🎯 MTO模式特征分析:');
  console.log('-' .repeat(80));
  
  const mtoAnalysis = {
    customSpecs: 0,
    specialProcess: 0,
    largeBatch: 0,
    urgentOrders: 0,
    individualizedProduction: 0
  };
  
  orders.forEach(order => {
    const characteristics = getMTOCharacteristics(order);
    
    characteristics.forEach(char => {
      switch (char.type) {
        case 'custom_specs':
          mtoAnalysis.customSpecs++;
          break;
        case 'special_process':
          mtoAnalysis.specialProcess++;
          break;
        case 'large_batch':
          mtoAnalysis.largeBatch++;
          break;
        case 'urgent':
          mtoAnalysis.urgentOrders++;
          break;
      }
    });
    
    // 个性化生产判断
    const uniqueSpecs = new Set(order.items.map(item => 
      `${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}`
    ));
    if (uniqueSpecs.size > 1 || order.items.some(item => item.notes)) {
      mtoAnalysis.individualizedProduction++;
    }
  });
  
  console.log(`   多规格定制订单: ${mtoAnalysis.customSpecs}个`);
  console.log(`   特殊工艺订单: ${mtoAnalysis.specialProcess}个`);
  console.log(`   大批量订单: ${mtoAnalysis.largeBatch}个`);
  console.log(`   紧急订单: ${mtoAnalysis.urgentOrders}个`);
  console.log(`   个性化生产订单: ${mtoAnalysis.individualizedProduction}个`);
  
  // 5. 界面功能验证
  console.log('\n🖥️ 界面功能验证:');
  console.log('-' .repeat(80));
  
  const uiFeatures = {
    enhancedOrderHeader: headerTests.orderTypeDisplay > 0 && headerTests.priorityDisplay > 0,
    businessMetrics: itemTests.businessMetrics > 0,
    processVisualization: itemTests.processFlowDisplay > 0,
    mtoCharacteristics: headerTests.mtoCharacteristics > 0,
    urgentDeliveryWarning: headerTests.urgentDelivery > 0,
    specialRequirements: itemTests.specialRequirements > 0,
    areaCalculation: itemTests.areaCalculation > 0,
    productFamilyClassification: itemTests.productFamilyDisplay > 0
  };
  
  Object.entries(uiFeatures).forEach(([feature, supported]) => {
    const status = supported ? '✅ 支持' : '❌ 不支持';
    const featureNames = {
      enhancedOrderHeader: '增强订单头部',
      businessMetrics: '业务指标展示',
      processVisualization: '工艺流程可视化',
      mtoCharacteristics: 'MTO特征标识',
      urgentDeliveryWarning: '紧急交期预警',
      specialRequirements: '特殊要求展示',
      areaCalculation: '面积计算',
      productFamilyClassification: '产品族分类'
    };
    console.log(`   ${featureNames[feature]}: ${status}`);
  });
  
  // 6. 总体评估
  console.log('\n🏆 "选择订单项"功能优化评估:');
  console.log('=' .repeat(80));
  
  const totalTests = Object.values(headerTests).reduce((sum, count) => sum + count, 0) +
                    Object.values(itemTests).reduce((sum, count) => sum + count, 0);
  const supportedFeatures = Object.values(uiFeatures).filter(Boolean).length;
  const totalFeatures = Object.keys(uiFeatures).length;
  const mtoFeatureScore = (mtoAnalysis.customSpecs + mtoAnalysis.specialProcess + 
                          mtoAnalysis.individualizedProduction) / orders.length * 100;
  
  console.log(`   功能测试项: ${totalTests}项测试通过`);
  console.log(`   界面功能支持: ${supportedFeatures}/${totalFeatures} (${(supportedFeatures/totalFeatures*100).toFixed(1)}%)`);
  console.log(`   MTO特征覆盖: ${mtoFeatureScore.toFixed(1)}% 的订单具有MTO特征`);
  
  const overallScore = (supportedFeatures/totalFeatures + mtoFeatureScore/100) / 2 * 100;
  console.log(`   综合评分: ${overallScore.toFixed(1)}%`);
  
  if (overallScore >= 90) {
    console.log('\n🎉 "选择订单项"功能优化效果优秀！');
    console.log('🎯 完全满足玻璃深加工MTO模式的业务需求');
    console.log('🚀 为智能创建工单提供了专业化的订单选择体验');
  } else if (overallScore >= 75) {
    console.log('\n👍 "选择订单项"功能优化效果良好');
    console.log('✨ 基本满足MTO业务需求，部分功能可进一步完善');
  } else {
    console.log('\n⚠️  "选择订单项"功能需要进一步优化');
  }
  
  console.log('\n💡 建议的验证步骤:');
  console.log('   1. 点击"智能创建工单"按钮打开对话框');
  console.log('   2. 查看订单头部的增强信息展示');
  console.log('   3. 验证MTO特征标识的准确性');
  console.log('   4. 检查订单项的详细业务信息');
  console.log('   5. 测试工艺流程的可视化效果');
  console.log('   6. 验证特殊要求和紧急交期的标识');
  
  console.log('\n' + '=' .repeat(80));
  console.log('🔍 "选择订单项"功能测试完成');
}

// 运行测试
testOrderItemSelection();
