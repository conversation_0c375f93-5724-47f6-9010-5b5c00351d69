#!/usr/bin/env node

/**
 * 测试生产发布工作台数据加载
 */

import fs from 'fs';
import path from 'path';

const testDataLoad = () => {
  console.log('🔧 测试生产发布工作台数据加载');
  console.log('='.repeat(60));

  try {
    // 加载数据文件
    const dataPath = path.resolve('public/mock/mes/production-release-workbench.json');
    const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

    console.log('✅ 数据文件加载成功');
    
    // 检查 WO-2024-001 数据
    const workOrderId = 'WO-2024-001';
    
    console.log(`\n🔍 检查工单 ${workOrderId} 的数据:`);
    console.log('-'.repeat(40));
    
    // 检查工单详情
    const workOrderDetails = data.workOrderDetails[workOrderId];
    if (workOrderDetails) {
      console.log(`✅ 工单详情: 找到 ${workOrderDetails.items.length} 个工单项`);
      workOrderDetails.items.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.specifications.thickness}mm ${item.specifications.glassType} ${item.specifications.length}x${item.specifications.width} (${item.quantity}片)`);
      });
    } else {
      console.log(`❌ 工单详情: 未找到 ${workOrderId} 的数据`);
    }

    // 检查技术规格
    const technicalSpecs = data.technicalSpecifications[workOrderId];
    if (technicalSpecs) {
      console.log(`✅ 技术规格: 找到完整的技术规格数据`);
      console.log(`   - 玻璃规格状态: ${technicalSpecs.glassSpecs.status}`);
      console.log(`   - 工艺要求状态: ${technicalSpecs.processRequirements.status}`);
      console.log(`   - 质量标准状态: ${technicalSpecs.qualityStandards.status}`);
      console.log(`   - 包装要求状态: ${technicalSpecs.packagingRequirements.status}`);
    } else {
      console.log(`❌ 技术规格: 未找到 ${workOrderId} 的数据`);
    }

    // 检查BOM验证
    const bomValidation = data.bomValidation[workOrderId];
    if (bomValidation) {
      console.log(`✅ BOM验证: 找到 ${bomValidation.items.length} 个物料项`);
      console.log(`   - 总项目数: ${bomValidation.summary.totalItems}`);
      console.log(`   - 库存充足: ${bomValidation.summary.sufficientItems}`);
      console.log(`   - 待采购: ${bomValidation.summary.pendingItems}`);
      console.log(`   - 完整度: ${bomValidation.summary.completeness}%`);
    } else {
      console.log(`❌ BOM验证: 未找到 ${workOrderId} 的数据`);
    }

    // 检查其他工单数据
    console.log(`\n📊 数据统计:`);
    console.log('-'.repeat(40));
    console.log(`工单详情数量: ${Object.keys(data.workOrderDetails).length}`);
    console.log(`技术规格数量: ${Object.keys(data.technicalSpecifications).length}`);
    console.log(`BOM验证数量: ${Object.keys(data.bomValidation).length}`);
    console.log(`物料库存数量: ${Object.keys(data.materialStock || {}).length}`);
    console.log(`工作站产能数量: ${Object.keys(data.workstationCapacity || {}).length}`);

    console.log(`\n🎉 数据检查完成！`);
    console.log(`${workOrderId} 的所有必要数据都已准备就绪。`);

    return true;
  } catch (error) {
    console.error('❌ 数据加载失败:', error.message);
    return false;
  }
};

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  const success = testDataLoad();
  process.exit(success ? 0 : 1);
}

export { testDataLoad };
