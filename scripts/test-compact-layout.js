#!/usr/bin/env node

/**
 * 测试优化后的紧凑布局效果
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

// 计算布局效率指标
function calculateLayoutEfficiency(orders) {
  let totalItems = 0;
  let totalDataPoints = 0;
  let compactDataPoints = 0;
  
  orders.forEach(order => {
    totalItems += order.items.length;
    
    // 计算每个订单项的数据点
    order.items.forEach(item => {
      // 基础数据点：规格、产品族、玻璃类型、颜色、数量、面积、单价、总额
      const basicDataPoints = 8;
      
      // 工艺流程数据点
      const processDataPoints = (item.processFlow || []).length;
      
      // 特殊要求数据点
      const specialRequirementPoints = item.notes ? 1 : 0;
      
      // 状态信息数据点
      const statusDataPoints = 3; // 可用性、兼容性、选择状态
      
      const itemTotalDataPoints = basicDataPoints + processDataPoints + specialRequirementPoints + statusDataPoints;
      totalDataPoints += itemTotalDataPoints;
      
      // 紧凑布局下的有效数据点（一行显示的信息）
      const compactItemDataPoints = 6; // 规格、产品族、数量、面积、单价、总额在一行
      compactDataPoints += compactItemDataPoints;
    });
  });
  
  return {
    totalItems,
    totalDataPoints,
    compactDataPoints,
    compressionRatio: compactDataPoints / totalDataPoints,
    averageDataPointsPerItem: totalDataPoints / totalItems,
    compactDataPointsPerItem: compactDataPoints / totalItems
  };
}

// 分析MTO模式的数据密度需求
function analyzeMTODataDensity(orders) {
  const analysis = {
    highVarietyOrders: 0,
    customSpecOrders: 0,
    specialProcessOrders: 0,
    urgentOrders: 0,
    averageItemsPerOrder: 0,
    maxItemsPerOrder: 0,
    totalUniqueSpecs: new Set(),
    totalProcessTypes: new Set()
  };
  
  let totalItems = 0;
  
  orders.forEach(order => {
    totalItems += order.items.length;
    analysis.maxItemsPerOrder = Math.max(analysis.maxItemsPerOrder, order.items.length);
    
    // 高品种订单（超过5个不同规格）
    const uniqueSpecs = new Set(order.items.map(item => 
      `${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}`
    ));
    if (uniqueSpecs.size > 5) {
      analysis.highVarietyOrders++;
    }
    
    // 定制规格订单
    const hasCustomSpecs = order.items.some(item => 
      item.specifications.length % 100 !== 0 || item.specifications.width % 100 !== 0
    );
    if (hasCustomSpecs) {
      analysis.customSpecOrders++;
    }
    
    // 特殊工艺订单
    const hasSpecialProcess = order.items.some(item => 
      (item.processFlow || []).some(step => 
        ['镀膜', '夹胶', '钢化', '表面处理'].includes(step.stepName)
      )
    );
    if (hasSpecialProcess) {
      analysis.specialProcessOrders++;
    }
    
    // 紧急订单
    if (order.priority === 'urgent') {
      analysis.urgentOrders++;
    }
    
    // 收集所有规格和工艺类型
    order.items.forEach(item => {
      const spec = `${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}`;
      analysis.totalUniqueSpecs.add(spec);
      
      (item.processFlow || []).forEach(step => {
        analysis.totalProcessTypes.add(step.stepName);
      });
    });
  });
  
  analysis.averageItemsPerOrder = totalItems / orders.length;
  analysis.totalUniqueSpecs = analysis.totalUniqueSpecs.size;
  analysis.totalProcessTypes = analysis.totalProcessTypes.size;
  
  return analysis;
}

// 主测试函数
function testCompactLayout() {
  console.log('📐 测试优化后的紧凑布局效果\n');
  console.log('=' .repeat(80));
  
  // 1. 加载数据
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  
  if (!customerOrders) {
    console.error('❌ 无法加载客户订单数据');
    return;
  }
  
  const orders = customerOrders.orders;
  
  console.log(`\n📊 数据概览:`);
  console.log(`   客户订单数量: ${orders.length}`);
  
  let totalItems = 0;
  orders.forEach(order => {
    totalItems += order.items.length;
  });
  console.log(`   订单项总数: ${totalItems}\n`);
  
  // 2. 布局效率分析
  console.log('📏 布局效率分析:');
  console.log('-' .repeat(80));
  
  const efficiency = calculateLayoutEfficiency(orders);
  
  console.log(`   总数据点: ${efficiency.totalDataPoints}`);
  console.log(`   紧凑布局数据点: ${efficiency.compactDataPoints}`);
  console.log(`   压缩比: ${(efficiency.compressionRatio * 100).toFixed(1)}%`);
  console.log(`   平均每项数据点: ${efficiency.averageDataPointsPerItem.toFixed(1)} → ${efficiency.compactDataPointsPerItem.toFixed(1)}`);
  
  // 估算屏幕利用率提升
  const originalRowHeight = 120; // 原始GRID布局每行高度(px)
  const compactRowHeight = 80;   // 紧凑布局每行高度(px)
  const screenHeight = 800;      // 假设屏幕高度
  
  const originalVisibleItems = Math.floor(screenHeight / originalRowHeight);
  const compactVisibleItems = Math.floor(screenHeight / compactRowHeight);
  const visibilityImprovement = (compactVisibleItems / originalVisibleItems - 1) * 100;
  
  console.log(`\n📺 可视性提升分析:`);
  console.log(`   原始布局可见项: ${originalVisibleItems}个`);
  console.log(`   紧凑布局可见项: ${compactVisibleItems}个`);
  console.log(`   可视性提升: ${visibilityImprovement.toFixed(1)}%`);
  
  // 3. MTO模式数据密度需求分析
  console.log('\n🎯 MTO模式数据密度需求分析:');
  console.log('-' .repeat(80));
  
  const mtoAnalysis = analyzeMTODataDensity(orders);
  
  console.log(`   高品种订单: ${mtoAnalysis.highVarietyOrders}个 (${(mtoAnalysis.highVarietyOrders/orders.length*100).toFixed(1)}%)`);
  console.log(`   定制规格订单: ${mtoAnalysis.customSpecOrders}个 (${(mtoAnalysis.customSpecOrders/orders.length*100).toFixed(1)}%)`);
  console.log(`   特殊工艺订单: ${mtoAnalysis.specialProcessOrders}个 (${(mtoAnalysis.specialProcessOrders/orders.length*100).toFixed(1)}%)`);
  console.log(`   紧急订单: ${mtoAnalysis.urgentOrders}个 (${(mtoAnalysis.urgentOrders/orders.length*100).toFixed(1)}%)`);
  console.log(`   平均每订单项数: ${mtoAnalysis.averageItemsPerOrder.toFixed(1)}个`);
  console.log(`   最大订单项数: ${mtoAnalysis.maxItemsPerOrder}个`);
  console.log(`   总规格种类: ${mtoAnalysis.totalUniqueSpecs}种`);
  console.log(`   总工艺类型: ${mtoAnalysis.totalProcessTypes}种`);
  
  // 4. 布局优化效果评估
  console.log('\n🏆 布局优化效果评估:');
  console.log('-' .repeat(80));
  
  const optimizationScores = {
    spaceEfficiency: Math.min(100, visibilityImprovement), // 空间效率提升
    dataCompression: efficiency.compressionRatio * 100,    // 数据压缩率
    mtoAdaptability: (mtoAnalysis.highVarietyOrders + mtoAnalysis.customSpecOrders) / orders.length * 100, // MTO适应性
    informationDensity: Math.min(100, efficiency.compactDataPointsPerItem / efficiency.averageDataPointsPerItem * 100) // 信息密度
  };
  
  console.log(`   空间效率提升: ${optimizationScores.spaceEfficiency.toFixed(1)}%`);
  console.log(`   数据压缩效果: ${optimizationScores.dataCompression.toFixed(1)}%`);
  console.log(`   MTO模式适应性: ${optimizationScores.mtoAdaptability.toFixed(1)}%`);
  console.log(`   信息密度优化: ${optimizationScores.informationDensity.toFixed(1)}%`);
  
  const overallScore = Object.values(optimizationScores).reduce((sum, score) => sum + score, 0) / 4;
  console.log(`   综合优化评分: ${overallScore.toFixed(1)}%`);
  
  // 5. 用户体验改进分析
  console.log('\n👥 用户体验改进分析:');
  console.log('-' .repeat(80));
  
  const uxImprovements = {
    scanningSpeed: visibilityImprovement > 20, // 扫描速度提升
    informationAccess: efficiency.compressionRatio > 0.4, // 信息获取便利性
    decisionEfficiency: mtoAnalysis.averageItemsPerOrder > 2, // 决策效率
    cognitiveLoad: efficiency.compactDataPointsPerItem < 10 // 认知负荷控制
  };
  
  Object.entries(uxImprovements).forEach(([aspect, improved]) => {
    const status = improved ? '✅ 改进' : '❌ 需要关注';
    const aspectNames = {
      scanningSpeed: '信息扫描速度',
      informationAccess: '信息获取便利性',
      decisionEfficiency: '决策制定效率',
      cognitiveLoad: '认知负荷控制'
    };
    console.log(`   ${aspectNames[aspect]}: ${status}`);
  });
  
  // 6. 建议和总结
  console.log('\n💡 优化建议:');
  console.log('-' .repeat(80));
  
  if (visibilityImprovement > 30) {
    console.log('   ✅ 紧凑布局显著提升了可视区域利用率');
  } else {
    console.log('   ⚠️  建议进一步优化行高和间距');
  }
  
  if (efficiency.compressionRatio > 0.5) {
    console.log('   ✅ 数据压缩效果良好，关键信息得到保留');
  } else {
    console.log('   ⚠️  建议优化信息层次，突出关键数据');
  }
  
  if (mtoAnalysis.highVarietyOrders / orders.length > 0.3) {
    console.log('   ✅ 布局适应MTO模式的高品种特征');
  } else {
    console.log('   ℹ️  当前数据集品种复杂度较低');
  }
  
  console.log('\n🎯 总结:');
  if (overallScore >= 80) {
    console.log('   🎉 紧凑布局优化效果优秀！');
    console.log('   🚀 显著提升了MTO模式下的数据展示效率');
    console.log('   📈 用户能够在有限空间内获取更多有效信息');
  } else if (overallScore >= 60) {
    console.log('   👍 紧凑布局优化效果良好');
    console.log('   ✨ 基本满足MTO模式的数据密度需求');
  } else {
    console.log('   ⚠️  紧凑布局需要进一步优化');
  }
  
  console.log('\n💡 验证步骤建议:');
  console.log('   1. 打开"智能创建工单"对话框');
  console.log('   2. 观察订单项列表的紧凑程度');
  console.log('   3. 验证关键信息的可读性');
  console.log('   4. 测试不同屏幕尺寸下的显示效果');
  console.log('   5. 检查工艺流程信息的简化展示');
  console.log('   6. 验证特殊要求的标识效果');
  
  console.log('\n' + '=' .repeat(80));
  console.log('📐 紧凑布局优化测试完成');
}

// 运行测试
testCompactLayout();
