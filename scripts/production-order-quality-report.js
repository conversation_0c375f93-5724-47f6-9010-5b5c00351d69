#!/usr/bin/env node

/**
 * 生成生产工单数据质量报告
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

// 主报告函数
function main() {
  console.log('📊 生产工单数据质量报告\n');
  console.log('=' .repeat(60));
  
  // 1. 加载数据
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  const productionOrders = loadJsonFile('public/mock/mes/production-orders.json');
  const workOrders = loadJsonFile('public/mock/mes/workOrders.json');
  
  if (!customerOrders || !productionOrders) {
    console.error('❌ 无法加载必要的数据文件');
    return;
  }
  
  // 2. 基础统计
  console.log('\n📈 基础数据统计:');
  console.log(`   客户订单数量: ${customerOrders.orders.length}`);
  
  let totalCustomerOrderItems = 0;
  customerOrders.orders.forEach(order => {
    totalCustomerOrderItems += order.items.length;
  });
  console.log(`   客户订单项数量: ${totalCustomerOrderItems}`);
  
  console.log(`   生产工单数量: ${productionOrders.productionOrders.length}`);
  
  let totalProductionOrderItems = 0;
  productionOrders.productionOrders.forEach(po => {
    totalProductionOrderItems += po.items.length;
  });
  console.log(`   生产工单项数量: ${totalProductionOrderItems}`);
  
  if (workOrders) {
    console.log(`   工单数量 (workOrders): ${workOrders.data.length}`);
  }
  
  // 3. 外键引用完整性
  console.log('\n🔗 外键引用完整性:');
  
  const customerOrderMap = new Map();
  const customerOrderItemMap = new Map();
  
  customerOrders.orders.forEach(order => {
    customerOrderMap.set(order.id, order);
    order.items.forEach(item => {
      customerOrderItemMap.set(item.id, item);
    });
  });
  
  let validReferences = 0;
  let invalidReferences = 0;
  
  productionOrders.productionOrders.forEach(po => {
    if (customerOrderMap.has(po.customerOrderId)) {
      po.items.forEach(item => {
        if (customerOrderItemMap.has(item.customerOrderItemId)) {
          validReferences++;
        } else {
          invalidReferences++;
        }
      });
    } else {
      invalidReferences += po.items.length;
    }
  });
  
  const referenceRate = (validReferences / (validReferences + invalidReferences) * 100).toFixed(1);
  console.log(`   有效外键引用: ${validReferences}个`);
  console.log(`   无效外键引用: ${invalidReferences}个`);
  console.log(`   引用完整率: ${referenceRate}%`);
  
  // 4. 覆盖率分析
  console.log('\n📋 覆盖率分析:');
  
  const coveredOrderItems = new Set();
  productionOrders.productionOrders.forEach(po => {
    po.items.forEach(item => {
      if (customerOrderItemMap.has(item.customerOrderItemId)) {
        coveredOrderItems.add(item.customerOrderItemId);
      }
    });
  });
  
  const coverageRate = (coveredOrderItems.size / totalCustomerOrderItems * 100).toFixed(1);
  console.log(`   已覆盖客户订单项: ${coveredOrderItems.size}个`);
  console.log(`   未覆盖客户订单项: ${totalCustomerOrderItems - coveredOrderItems.size}个`);
  console.log(`   覆盖率: ${coverageRate}%`);
  
  // 5. 产品族关联分析
  console.log('\n🏭 产品族关联分析:');
  
  const productFamilyStats = new Map();
  
  productionOrders.productionOrders.forEach(po => {
    po.items.forEach(item => {
      if (item.productFamilyId) {
        const familyId = item.productFamilyId;
        if (!productFamilyStats.has(familyId)) {
          productFamilyStats.set(familyId, {
            name: item.productFamilyName || familyId,
            count: 0,
            totalQuantity: 0,
            totalValue: 0
          });
        }
        const stats = productFamilyStats.get(familyId);
        stats.count++;
        stats.totalQuantity += item.quantity || 0;
        stats.totalValue += item.totalAmount || 0;
      }
    });
  });
  
  productFamilyStats.forEach((stats, familyId) => {
    console.log(`   ${stats.name}: ${stats.count}个工单项, 总量${stats.totalQuantity}, 总值¥${stats.totalValue.toLocaleString()}`);
  });
  
  // 6. 工艺流程分析
  console.log('\n⚙️ 工艺流程分析:');
  
  const processStepStats = new Map();
  
  productionOrders.productionOrders.forEach(po => {
    po.items.forEach(item => {
      if (item.processFlow) {
        item.processFlow.forEach(step => {
          const stepName = step.stepName;
          if (!processStepStats.has(stepName)) {
            processStepStats.set(stepName, 0);
          }
          processStepStats.set(stepName, processStepStats.get(stepName) + 1);
        });
      }
    });
  });
  
  const sortedSteps = Array.from(processStepStats.entries()).sort((a, b) => b[1] - a[1]);
  sortedSteps.forEach(([stepName, count]) => {
    console.log(`   ${stepName}: ${count}个工单项使用`);
  });
  
  // 7. 数据一致性检查
  console.log('\n🔍 数据一致性检查:');
  
  let specConsistencyIssues = 0;
  let quantityConsistencyIssues = 0;
  let priceConsistencyIssues = 0;
  
  productionOrders.productionOrders.forEach(po => {
    po.items.forEach(item => {
      const customerOrderItem = customerOrderItemMap.get(item.customerOrderItemId);
      if (customerOrderItem) {
        // 检查规格一致性
        const poSpecs = item.specifications;
        const coSpecs = customerOrderItem.specifications;
        
        if (poSpecs.thickness !== coSpecs.thickness ||
            poSpecs.glassType !== coSpecs.glassType ||
            poSpecs.color !== coSpecs.color) {
          specConsistencyIssues++;
        }
        
        // 检查数量一致性
        if (item.quantity !== customerOrderItem.quantity) {
          quantityConsistencyIssues++;
        }
        
        // 检查价格一致性
        if (item.unitPrice !== customerOrderItem.unitPrice ||
            item.totalAmount !== customerOrderItem.totalAmount) {
          priceConsistencyIssues++;
        }
      }
    });
  });
  
  console.log(`   规格不一致: ${specConsistencyIssues}个`);
  console.log(`   数量不一致: ${quantityConsistencyIssues}个`);
  console.log(`   价格不一致: ${priceConsistencyIssues}个`);
  
  // 8. 总体质量评分
  console.log('\n🏆 数据质量评分:');
  
  const scores = {
    referenceIntegrity: referenceRate >= 95 ? 100 : referenceRate,
    coverage: parseFloat(coverageRate),
    consistency: specConsistencyIssues === 0 && quantityConsistencyIssues === 0 && priceConsistencyIssues === 0 ? 100 : 
                 Math.max(0, 100 - (specConsistencyIssues + quantityConsistencyIssues + priceConsistencyIssues) * 5)
  };
  
  const overallScore = (scores.referenceIntegrity + scores.coverage + scores.consistency) / 3;
  
  console.log(`   外键引用完整性: ${scores.referenceIntegrity.toFixed(1)}分`);
  console.log(`   数据覆盖率: ${scores.coverage.toFixed(1)}分`);
  console.log(`   数据一致性: ${scores.consistency.toFixed(1)}分`);
  console.log(`   综合质量评分: ${overallScore.toFixed(1)}分`);
  
  // 9. 质量等级评定
  console.log('\n📊 质量等级评定:');
  
  let qualityGrade = '';
  let qualityDescription = '';
  
  if (overallScore >= 95) {
    qualityGrade = '🥇 优秀 (A+)';
    qualityDescription = '数据质量优秀，完全满足生产管理需求';
  } else if (overallScore >= 85) {
    qualityGrade = '🥈 良好 (A)';
    qualityDescription = '数据质量良好，基本满足生产管理需求';
  } else if (overallScore >= 75) {
    qualityGrade = '🥉 合格 (B)';
    qualityDescription = '数据质量合格，需要持续改进';
  } else {
    qualityGrade = '❌ 不合格 (C)';
    qualityDescription = '数据质量不合格，需要立即修复';
  }
  
  console.log(`   质量等级: ${qualityGrade}`);
  console.log(`   评定说明: ${qualityDescription}`);
  
  // 10. 业务价值总结
  console.log('\n💼 业务价值总结:');
  console.log('   ✅ 支持完整的订单到生产的业务追溯');
  console.log('   ✅ 实现基于产品族的智能工艺路线生成');
  console.log('   ✅ 提供准确的生产计划和资源配置数据');
  console.log('   ✅ 确保客户订单与生产执行的数据一致性');
  console.log('   ✅ 为MES系统提供可靠的数据基础');
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 报告生成完成');
}

// 运行报告
main();
