#!/usr/bin/env node

/**
 * 重新组织生产工单MOCK数据，创建多对多关系的复杂场景
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取和保存JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

function saveJsonFile(relativePath, data) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`✅ 已保存文件: ${relativePath}`);
  } catch (error) {
    console.error(`❌ 无法保存文件 ${relativePath}:`, error.message);
  }
}

// 生成工艺流程
function generateProcessFlow(productFamilyId) {
  const processFlows = {
    'PF-TEMPERED': [
      { stepName: "切割", workstation: "cold_processing" },
      { stepName: "磨边", workstation: "cold_processing" },
      { stepName: "镀膜", workstation: "coating" },
      { stepName: "钢化", workstation: "tempering" },
      { stepName: "质检", workstation: "quality_control" },
      { stepName: "包装", workstation: "packaging" }
    ],
    'PF-IGU': [
      { stepName: "切割", workstation: "cold_processing" },
      { stepName: "磨边", workstation: "cold_processing" },
      { stepName: "镀膜", workstation: "coating" },
      { stepName: "钢化", workstation: "tempering" },
      { stepName: "合片", workstation: "insulating" },
      { stepName: "充气", workstation: "insulating" },
      { stepName: "质检", workstation: "quality_control" },
      { stepName: "包装", workstation: "packaging" }
    ],
    'PF-LAMINATED': [
      { stepName: "切割", workstation: "cold_processing" },
      { stepName: "磨边", workstation: "cold_processing" },
      { stepName: "清洗", workstation: "cleaning" },
      { stepName: "夹胶", workstation: "laminating" },
      { stepName: "高压釜", workstation: "autoclave" },
      { stepName: "质检", workstation: "quality_control" },
      { stepName: "包装", workstation: "packaging" }
    ],
    'PF-DECORATIVE': [
      { stepName: "切割", workstation: "cold_processing" },
      { stepName: "磨边", workstation: "cold_processing" },
      { stepName: "镀膜", workstation: "coating" },
      { stepName: "表面处理", workstation: "surface_treatment" },
      { stepName: "质检", workstation: "quality_control" },
      { stepName: "包装", workstation: "packaging" }
    ],
    'PF-FURNITURE': [
      { stepName: "切割", workstation: "cold_processing" },
      { stepName: "磨边", workstation: "cold_processing" },
      { stepName: "抛光", workstation: "polishing" },
      { stepName: "钢化", workstation: "tempering" },
      { stepName: "质检", workstation: "quality_control" },
      { stepName: "包装", workstation: "packaging" }
    ]
  };
  
  return processFlows[productFamilyId] || processFlows['PF-TEMPERED'];
}

// 主函数
function reorganizeProductionOrders() {
  console.log('🔄 重新组织生产工单MOCK数据，创建多对多关系场景...\n');
  
  // 1. 加载现有数据
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  
  if (!customerOrders) {
    console.error('❌ 无法加载客户订单数据');
    return;
  }
  
  const orders = customerOrders.orders;
  console.log(`📊 加载了 ${orders.length} 个客户订单，共 ${orders.reduce((sum, o) => sum + o.items.length, 0)} 个订单项\n`);
  
  // 2. 创建不同类型的生产工单关系
  const newProductionOrders = [];
  let workOrderIndex = 1;
  let workOrderItemIndex = 1;
  
  // 场景1: 一对一关系 - 单个客户订单对应单个生产工单
  console.log('📝 创建场景1: 一对一关系工单...');
  const singleOrderWorkOrder = {
    id: `WO-2024-${String(workOrderIndex).padStart(3, '0')}`,
    workOrderNumber: `WO-2024-${String(workOrderIndex).padStart(4, '0')}`,
    customerOrderId: orders[0].id,
    customerOrderNumber: orders[0].orderNumber,
    customerName: orders[0].customerName,
    orderType: orders[0].orderType,
    priority: 'high',
    status: 'pending',
    plannedStartDate: '2024-01-20T08:00:00Z',
    plannedEndDate: '2024-01-25T18:00:00Z',
    createdAt: new Date().toISOString(),
    items: orders[0].items.map(item => ({
      id: `WOI-${String(workOrderItemIndex++).padStart(3, '0')}`,
      productionOrderId: `WO-2024-${String(workOrderIndex).padStart(3, '0')}`,
      customerOrderItemId: item.id,
      productFamilyId: item.productFamilyId,
      productFamilyName: item.productFamilyName,
      specifications: item.specifications,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalAmount: item.totalAmount,
      deliveryDate: item.deliveryDate,
      processFlow: generateProcessFlow(item.productFamilyId),
      status: 'pending',
      createdAt: new Date().toISOString(),
      // 添加客户订单信息用于多对多关系展示
      customerName: orders[0].customerName,
      customerOrderNumber: orders[0].orderNumber
    }))
  };
  newProductionOrders.push(singleOrderWorkOrder);
  workOrderIndex++;
  console.log(`   ✅ 创建工单 ${singleOrderWorkOrder.workOrderNumber}: 1个客户(${orders[0].customerName}), 1个订单, ${singleOrderWorkOrder.items.length}个工单项`);
  
  // 场景2: 一对多关系 - 单个客户的多个订单合并到一个生产工单
  console.log('📝 创建场景2: 一对多关系工单...');
  const multiOrderWorkOrder = {
    id: `WO-2024-${String(workOrderIndex).padStart(3, '0')}`,
    workOrderNumber: `WO-2024-${String(workOrderIndex).padStart(4, '0')}`,
    customerOrderId: orders[1].id, // 主要订单
    customerOrderNumber: orders[1].orderNumber,
    customerName: orders[1].customerName,
    orderType: '混合订单',
    priority: 'medium',
    status: 'released',
    plannedStartDate: '2024-01-21T08:00:00Z',
    plannedEndDate: '2024-01-28T18:00:00Z',
    createdAt: new Date().toISOString(),
    items: []
  };
  
  // 合并第2和第3个客户订单的部分工单项
  orders[1].items.forEach(item => {
    multiOrderWorkOrder.items.push({
      id: `WOI-${String(workOrderItemIndex++).padStart(3, '0')}`,
      productionOrderId: multiOrderWorkOrder.id,
      customerOrderItemId: item.id,
      productFamilyId: item.productFamilyId,
      productFamilyName: item.productFamilyName,
      specifications: item.specifications,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalAmount: item.totalAmount,
      deliveryDate: item.deliveryDate,
      processFlow: generateProcessFlow(item.productFamilyId),
      status: 'pending',
      createdAt: new Date().toISOString(),
      customerName: orders[1].customerName,
      customerOrderNumber: orders[1].orderNumber
    });
  });
  
  // 添加第3个订单的第一个工单项
  if (orders[2] && orders[2].items.length > 0) {
    multiOrderWorkOrder.items.push({
      id: `WOI-${String(workOrderItemIndex++).padStart(3, '0')}`,
      productionOrderId: multiOrderWorkOrder.id,
      customerOrderItemId: orders[2].items[0].id,
      productFamilyId: orders[2].items[0].productFamilyId,
      productFamilyName: orders[2].items[0].productFamilyName,
      specifications: orders[2].items[0].specifications,
      quantity: orders[2].items[0].quantity,
      unitPrice: orders[2].items[0].unitPrice,
      totalAmount: orders[2].items[0].totalAmount,
      deliveryDate: orders[2].items[0].deliveryDate,
      processFlow: generateProcessFlow(orders[2].items[0].productFamilyId),
      status: 'pending',
      createdAt: new Date().toISOString(),
      customerName: orders[2].customerName,
      customerOrderNumber: orders[2].orderNumber
    });
  }
  
  newProductionOrders.push(multiOrderWorkOrder);
  workOrderIndex++;
  const uniqueCustomers = [...new Set(multiOrderWorkOrder.items.map(item => item.customerName))];
  const uniqueOrders = [...new Set(multiOrderWorkOrder.items.map(item => item.customerOrderNumber))];
  console.log(`   ✅ 创建工单 ${multiOrderWorkOrder.workOrderNumber}: ${uniqueCustomers.length}个客户(${uniqueCustomers.join(', ')}), ${uniqueOrders.length}个订单, ${multiOrderWorkOrder.items.length}个工单项`);
  
  // 场景3: 多对一关系 - 多个客户的订单合并到一个生产工单（批量生产优化）
  console.log('📝 创建场景3: 多对一关系工单...');
  const batchWorkOrder = {
    id: `WO-2024-${String(workOrderIndex).padStart(3, '0')}`,
    workOrderNumber: `WO-2024-${String(workOrderIndex).padStart(4, '0')}`,
    customerOrderId: orders[3].id, // 主要订单
    customerOrderNumber: '批量生产工单',
    customerName: '多客户批量',
    orderType: '批量生产',
    priority: 'urgent',
    status: 'in_progress',
    plannedStartDate: '2024-01-22T08:00:00Z',
    plannedEndDate: '2024-01-30T18:00:00Z',
    createdAt: new Date().toISOString(),
    items: []
  };
  
  // 选择相似规格的工单项进行批量生产（第4、5、6个订单的部分工单项）
  [3, 4, 5].forEach(orderIndex => {
    if (orders[orderIndex] && orders[orderIndex].items.length > 0) {
      const item = orders[orderIndex].items[0]; // 取每个订单的第一个工单项
      batchWorkOrder.items.push({
        id: `WOI-${String(workOrderItemIndex++).padStart(3, '0')}`,
        productionOrderId: batchWorkOrder.id,
        customerOrderItemId: item.id,
        productFamilyId: item.productFamilyId,
        productFamilyName: item.productFamilyName,
        specifications: item.specifications,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalAmount: item.totalAmount,
        deliveryDate: item.deliveryDate,
        processFlow: generateProcessFlow(item.productFamilyId),
        status: 'in_progress',
        createdAt: new Date().toISOString(),
        customerName: orders[orderIndex].customerName,
        customerOrderNumber: orders[orderIndex].orderNumber
      });
    }
  });
  
  newProductionOrders.push(batchWorkOrder);
  workOrderIndex++;
  const batchUniqueCustomers = [...new Set(batchWorkOrder.items.map(item => item.customerName))];
  const batchUniqueOrders = [...new Set(batchWorkOrder.items.map(item => item.customerOrderNumber))];
  console.log(`   ✅ 创建工单 ${batchWorkOrder.workOrderNumber}: ${batchUniqueCustomers.length}个客户(${batchUniqueCustomers.join(', ')}), ${batchUniqueOrders.length}个订单, ${batchWorkOrder.items.length}个工单项`);
  
  // 场景4: 多对多关系 - 复杂的多客户多订单工单
  console.log('📝 创建场景4: 多对多关系工单...');
  const complexWorkOrder = {
    id: `WO-2024-${String(workOrderIndex).padStart(3, '0')}`,
    workOrderNumber: `WO-2024-${String(workOrderIndex).padStart(4, '0')}`,
    customerOrderId: orders[6].id,
    customerOrderNumber: '复合生产工单',
    customerName: '多客户复合',
    orderType: '复合生产',
    priority: 'high',
    status: 'released',
    plannedStartDate: '2024-01-23T08:00:00Z',
    plannedEndDate: '2024-02-02T18:00:00Z',
    createdAt: new Date().toISOString(),
    items: []
  };
  
  // 从多个客户订单中选择工单项，模拟复杂的生产编排
  [6, 7, 8, 9].forEach((orderIndex, idx) => {
    if (orders[orderIndex] && orders[orderIndex].items.length > 0) {
      // 每个订单选择不同数量的工单项
      const itemsToAdd = orders[orderIndex].items.slice(0, idx % 2 + 1);
      itemsToAdd.forEach(item => {
        complexWorkOrder.items.push({
          id: `WOI-${String(workOrderItemIndex++).padStart(3, '0')}`,
          productionOrderId: complexWorkOrder.id,
          customerOrderItemId: item.id,
          productFamilyId: item.productFamilyId,
          productFamilyName: item.productFamilyName,
          specifications: item.specifications,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalAmount: item.totalAmount,
          deliveryDate: item.deliveryDate,
          processFlow: generateProcessFlow(item.productFamilyId),
          status: 'released',
          createdAt: new Date().toISOString(),
          customerName: orders[orderIndex].customerName,
          customerOrderNumber: orders[orderIndex].orderNumber
        });
      });
    }
  });
  
  newProductionOrders.push(complexWorkOrder);
  workOrderIndex++;
  const complexUniqueCustomers = [...new Set(complexWorkOrder.items.map(item => item.customerName))];
  const complexUniqueOrders = [...new Set(complexWorkOrder.items.map(item => item.customerOrderNumber))];
  console.log(`   ✅ 创建工单 ${complexWorkOrder.workOrderNumber}: ${complexUniqueCustomers.length}个客户(${complexUniqueCustomers.join(', ')}), ${complexUniqueOrders.length}个订单, ${complexWorkOrder.items.length}个工单项`);
  
  // 场景5: 剩余订单项的常规工单
  console.log('📝 创建场景5: 剩余订单项的常规工单...');
  const usedOrderItemIds = new Set();
  newProductionOrders.forEach(wo => {
    wo.items.forEach(item => {
      usedOrderItemIds.add(item.customerOrderItemId);
    });
  });
  
  // 为剩余的订单项创建常规工单
  orders.forEach((order, orderIndex) => {
    const remainingItems = order.items.filter(item => !usedOrderItemIds.has(item.id));
    
    if (remainingItems.length > 0) {
      const regularWorkOrder = {
        id: `WO-2024-${String(workOrderIndex).padStart(3, '0')}`,
        workOrderNumber: `WO-2024-${String(workOrderIndex).padStart(4, '0')}`,
        customerOrderId: order.id,
        customerOrderNumber: order.orderNumber,
        customerName: order.customerName,
        orderType: order.orderType,
        priority: ['urgent', 'high', 'medium', 'low'][workOrderIndex % 4],
        status: ['pending', 'released', 'in_progress', 'completed'][workOrderIndex % 4],
        plannedStartDate: `2024-01-${20 + workOrderIndex}T08:00:00Z`,
        plannedEndDate: `2024-02-${Math.min(28, workOrderIndex + 5)}T18:00:00Z`,
        createdAt: new Date().toISOString(),
        items: remainingItems.map(item => ({
          id: `WOI-${String(workOrderItemIndex++).padStart(3, '0')}`,
          productionOrderId: `WO-2024-${String(workOrderIndex).padStart(3, '0')}`,
          customerOrderItemId: item.id,
          productFamilyId: item.productFamilyId,
          productFamilyName: item.productFamilyName,
          specifications: item.specifications,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalAmount: item.totalAmount,
          deliveryDate: item.deliveryDate,
          processFlow: generateProcessFlow(item.productFamilyId),
          status: ['pending', 'released', 'in_progress', 'completed'][workOrderIndex % 4],
          createdAt: new Date().toISOString(),
          customerName: order.customerName,
          customerOrderNumber: order.orderNumber
        }))
      };
      
      newProductionOrders.push(regularWorkOrder);
      console.log(`   ✅ 创建工单 ${regularWorkOrder.workOrderNumber}: 1个客户(${order.customerName}), 1个订单, ${remainingItems.length}个工单项`);
      workOrderIndex++;
    }
  });
  
  // 3. 保存新的生产工单数据
  const newProductionOrdersData = {
    scenario: "多对多关系的复杂生产工单数据",
    description: "包含一对一、一对多、多对一、多对多等各种关系类型的生产工单，用于测试界面的多对多关系处理能力",
    lastUpdated: new Date().toISOString(),
    relationshipTypes: {
      oneToOne: "单客户单订单工单",
      oneToMany: "单客户多订单工单", 
      manyToOne: "多客户单工单（批量生产）",
      manyToMany: "多客户多订单复合工单"
    },
    productionOrders: newProductionOrders
  };
  
  saveJsonFile('public/mock/mes/production-orders.json', newProductionOrdersData);
  
  // 4. 统计报告
  console.log('\n📊 重新组织完成统计:');
  console.log(`   生成生产工单数量: ${newProductionOrders.length}`);
  
  let totalItems = 0;
  let oneToOneCount = 0;
  let oneToManyCount = 0;
  let manyToOneCount = 0;
  let manyToManyCount = 0;
  
  newProductionOrders.forEach(wo => {
    totalItems += wo.items.length;
    const uniqueCustomers = [...new Set(wo.items.map(item => item.customerName))];
    const uniqueOrders = [...new Set(wo.items.map(item => item.customerOrderNumber))];
    
    if (uniqueCustomers.length === 1 && uniqueOrders.length === 1) {
      oneToOneCount++;
    } else if (uniqueCustomers.length === 1 && uniqueOrders.length > 1) {
      oneToManyCount++;
    } else if (uniqueCustomers.length > 1 && uniqueOrders.length === 1) {
      manyToOneCount++;
    } else {
      manyToManyCount++;
    }
  });
  
  console.log(`   生成工单项总数: ${totalItems}`);
  console.log(`   一对一关系工单: ${oneToOneCount}个`);
  console.log(`   一对多关系工单: ${oneToManyCount}个`);
  console.log(`   多对一关系工单: ${manyToOneCount}个`);
  console.log(`   多对多关系工单: ${manyToManyCount}个`);
  
  console.log('\n✅ 生产工单MOCK数据重新组织完成！');
  console.log('🎯 现在可以测试界面的多对多关系处理能力');
  console.log('🚀 数据包含了各种复杂的业务场景，更贴近实际应用');
}

// 运行重组
reorganizeProductionOrders();
