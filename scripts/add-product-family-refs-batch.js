#!/usr/bin/env node

/**
 * 批量为客户订单项添加产品族引用
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 订单项与产品族的映射关系
const orderItemMappings = {
  'COI-001': { familyId: 'PF-TEMPERED', config: { width: 1200, height: 1800, thickness: '6', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-002': { familyId: 'PF-TEMPERED', config: { width: 1600, height: 2400, thickness: '6', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-003': { familyId: 'PF-TEMPERED', config: { width: 1000, height: 1500, thickness: '6', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-004': { familyId: 'PF-TEMPERED', config: { width: 800, height: 1200, thickness: '5', glass_type: 'clear', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'none' }},
  'COI-005': { familyId: 'PF-TEMPERED', config: { width: 1200, height: 1500, thickness: '5', glass_type: 'clear', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'none' }},
  'COI-006': { familyId: 'PF-DECORATIVE', config: { width: 1400, height: 2000, thickness: '8', glass_type: 'tinted', color: '灰色', surface_finish: 'smooth', transparency_level: 'translucent', pattern_type: 'none', edge_treatment: 'polished' }},
  'COI-007': { familyId: 'PF-IGU', config: { width: 1000, height: 1600, glass1_thickness: '6', glass2_thickness: '6', spacer_width: '12', glass_type: 'low_e', color: '透明', is_tempered: false, gas_filling: 'air', sealant_type: 'structural', energy_rating: 'high' }},
  'COI-008': { familyId: 'PF-IGU', config: { width: 800, height: 1400, glass1_thickness: '5', glass2_thickness: '5', spacer_width: '9', glass_type: 'low_e', color: '透明', is_tempered: false, gas_filling: 'air', sealant_type: 'structural', energy_rating: 'high' }},
  'COI-009': { familyId: 'PF-LAMINATED', config: { width: 1500, height: 2200, total_thickness: '10', glass1_thickness: '5', glass2_thickness: '5', pvb_thickness: '1.14', glass_type: 'clear', color: '透明', safety_level: 'standard', pvb_type: 'standard' }},
  'COI-010': { familyId: 'PF-LAMINATED', config: { width: 1200, height: 1800, total_thickness: '8', glass1_thickness: '4', glass2_thickness: '4', pvb_thickness: '0.76', glass_type: 'clear', color: '透明', safety_level: 'standard', pvb_type: 'standard' }},
  'COI-011': { familyId: 'PF-DECORATIVE', config: { width: 600, height: 1200, thickness: '6', glass_type: 'reflective', color: '蓝色', surface_finish: 'smooth', transparency_level: 'transparent', pattern_type: 'none', edge_treatment: 'polished' }},
  'COI-012': { familyId: 'PF-DECORATIVE', config: { width: 600, height: 800, thickness: '5', glass_type: 'tinted', color: '茶色', surface_finish: 'smooth', transparency_level: 'translucent', pattern_type: 'none', edge_treatment: 'polished' }},
  'COI-013': { familyId: 'PF-FURNITURE', config: { width: 500, height: 1000, thickness: '8', glass_type: 'clear', color: '透明', edge_processing: 'polished', corner_type: 'rounded', safety_treatment: 'tempered', surface_quality: 'premium', drilling_required: false }},
  'COI-014': { familyId: 'PF-FURNITURE', config: { width: 400, height: 600, thickness: '5', glass_type: 'clear', color: '透明', edge_processing: 'polished', corner_type: 'rounded', safety_treatment: 'tempered', surface_quality: 'premium', drilling_required: false }},
  'COI-015': { familyId: 'PF-TEMPERED', config: { width: 1000, height: 2000, thickness: '8', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-016': { familyId: 'PF-TEMPERED', config: { width: 2000, height: 1500, thickness: '6', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'coating' }},
  'COI-017': { familyId: 'PF-TEMPERED', config: { width: 300, height: 800, thickness: '5', glass_type: 'clear', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'none' }},
  'COI-018': { familyId: 'PF-TEMPERED', config: { width: 400, height: 1000, thickness: '6', glass_type: 'clear', color: '透明', is_tempered: true, edge_type: 'ground', surface_treatment: 'none' }},
  'COI-019': { familyId: 'PF-TEMPERED', config: { width: 1200, height: 1800, thickness: '12', glass_type: 'low_e', color: '透明', is_tempered: true, edge_type: 'polished', surface_treatment: 'coating' }},
  'COI-020': { familyId: 'PF-TEMPERED', config: { width: 1600, height: 2400, thickness: '10', glass_type: 'reflective', color: '金色', is_tempered: true, edge_type: 'polished', surface_treatment: 'coating' }}
};

// 产品族名称映射
const familyNames = {
  'PF-TEMPERED': '单片钢化玻璃产品族',
  'PF-IGU': '中空玻璃产品族',
  'PF-LAMINATED': '夹胶玻璃产品族',
  'PF-DECORATIVE': '装饰玻璃产品族',
  'PF-FURNITURE': '家具玻璃产品族'
};

// 生成配置哈希
function generateConfigHash(config) {
  const configString = JSON.stringify(config, Object.keys(config).sort());
  return Buffer.from(configString).toString('base64').substring(0, 16);
}

console.log('🔍 开始批量添加产品族引用...\n');

// 读取客户订单文件
const filePath = path.join(__dirname, '../public/mock/mes/customer-orders.json');
let content = fs.readFileSync(filePath, 'utf8');

let addedCount = 0;

// 为每个订单项添加产品族引用
Object.entries(orderItemMappings).forEach(([itemId, mapping]) => {
  const familyId = mapping.familyId;
  const familyName = familyNames[familyId];
  const config = mapping.config;
  const configHash = generateConfigHash(config);
  
  // 查找订单项的位置
  const itemPattern = new RegExp(`("id":\\s*"${itemId}"[\\s\\S]*?"currentStatus":\\s*"[^"]*")`, 'g');
  const match = itemPattern.exec(content);
  
  if (match) {
    // 检查是否已经有产品族引用
    if (!content.includes(`"productFamilyId": "${familyId}"`)) {
      const replacement = match[1] + `,
          "productFamilyId": "${familyId}",
          "productFamilyName": "${familyName}",
          "productFamilyConfig": ${JSON.stringify(config, null, 12).replace(/\n/g, '\n          ')},
          "configurationHash": "${configHash}"`;
      
      content = content.replace(match[1], replacement);
      addedCount++;
      console.log(`✅ ${itemId}: 添加 ${familyName} 引用`);
    } else {
      console.log(`ℹ️  ${itemId}: 已有产品族引用`);
    }
  } else {
    console.log(`❌ ${itemId}: 未找到订单项`);
  }
});

// 保存修改后的文件
fs.writeFileSync(filePath, content, 'utf8');

console.log(`\n📊 批量添加完成:`);
console.log(`   新增产品族引用: ${addedCount}个`);
console.log(`   处理订单项总数: ${Object.keys(orderItemMappings).length}个`);
console.log(`\n✅ 客户订单产品族引用批量添加完成！`);
