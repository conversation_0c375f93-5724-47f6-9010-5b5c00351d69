#!/usr/bin/env node

/**
 * 验证客户订单中产品族外键引用的完整性
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    process.exit(1);
  }
}

// 主验证函数
function main() {
  console.log('🔍 开始验证客户订单产品族外键引用...\n');
  
  // 1. 加载数据
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  const productFamilies = loadJsonFile('public/mock/masterdata/productFamilies.json');
  
  // 创建产品族ID映射
  const familyMap = new Map();
  productFamilies.forEach(family => {
    familyMap.set(family.id, family);
  });
  
  console.log(`📊 数据概览:`);
  console.log(`   产品族数量: ${productFamilies.length}`);
  console.log(`   客户订单数量: ${customerOrders.orders.length}`);
  
  // 2. 统计订单项
  let totalOrderItems = 0;
  customerOrders.orders.forEach(order => {
    totalOrderItems += order.items.length;
  });
  console.log(`   订单项总数: ${totalOrderItems}\n`);
  
  // 3. 验证外键引用
  console.log('🔍 验证产品族外键引用:');
  
  const validReferences = [];
  const invalidReferences = [];
  const missingReferences = [];
  const configurationIssues = [];
  
  customerOrders.orders.forEach(order => {
    order.items.forEach(item => {
      const itemInfo = `${item.id} (${order.customerName})`;
      
      // 检查是否有产品族引用
      if (!item.productFamilyId) {
        missingReferences.push(itemInfo);
        return;
      }
      
      // 检查产品族ID是否有效
      const family = familyMap.get(item.productFamilyId);
      if (!family) {
        invalidReferences.push(`${itemInfo} → ${item.productFamilyId}`);
        return;
      }
      
      // 检查产品族配置
      if (!item.productFamilyConfig) {
        configurationIssues.push(`${itemInfo}: 缺少产品族配置`);
      } else {
        // 验证配置属性是否符合产品族定义
        const requiredAttributes = family.attributes.map(attr => attr.id);
        const configAttributes = Object.keys(item.productFamilyConfig);
        const missingAttrs = requiredAttributes.filter(attr => !configAttributes.includes(attr));
        
        if (missingAttrs.length > 0) {
          configurationIssues.push(`${itemInfo}: 缺少配置属性 [${missingAttrs.join(', ')}]`);
        }
      }
      
      // 检查配置哈希
      if (!item.configurationHash) {
        configurationIssues.push(`${itemInfo}: 缺少配置哈希值`);
      }
      
      validReferences.push({
        itemId: item.id,
        customerName: order.customerName,
        familyId: item.productFamilyId,
        familyName: family.name,
        hasConfig: !!item.productFamilyConfig,
        hasHash: !!item.configurationHash
      });
    });
  });
  
  // 4. 显示验证结果
  console.log(`   ✅ 有效引用: ${validReferences.length}个`);
  console.log(`   ❌ 无效引用: ${invalidReferences.length}个`);
  console.log(`   ⚠️  缺少引用: ${missingReferences.length}个`);
  console.log(`   🔧 配置问题: ${configurationIssues.length}个\n`);
  
  // 5. 详细报告
  if (missingReferences.length > 0) {
    console.log('⚠️  缺少产品族引用的订单项:');
    missingReferences.forEach(ref => console.log(`   - ${ref}`));
    console.log();
  }
  
  if (invalidReferences.length > 0) {
    console.log('❌ 无效产品族引用的订单项:');
    invalidReferences.forEach(ref => console.log(`   - ${ref}`));
    console.log();
  }
  
  if (configurationIssues.length > 0) {
    console.log('🔧 配置问题的订单项:');
    configurationIssues.forEach(issue => console.log(`   - ${issue}`));
    console.log();
  }
  
  // 6. 按产品族统计引用情况
  console.log('📋 各产品族引用统计:');
  const familyStats = new Map();
  
  productFamilies.forEach(family => {
    familyStats.set(family.id, {
      name: family.name,
      count: 0,
      items: []
    });
  });
  
  validReferences.forEach(ref => {
    const stats = familyStats.get(ref.familyId);
    if (stats) {
      stats.count++;
      stats.items.push(`${ref.itemId} (${ref.customerName})`);
    }
  });
  
  familyStats.forEach((stats, familyId) => {
    if (stats.count > 0) {
      console.log(`   ✅ ${stats.name}: ${stats.count}个引用`);
      stats.items.forEach(item => console.log(`      - ${item}`));
    } else {
      console.log(`   ⚠️  ${stats.name}: 0个引用`);
    }
  });
  
  // 7. 总结
  const referenceRate = (validReferences.length / totalOrderItems * 100).toFixed(1);
  const integrityRate = ((validReferences.length - configurationIssues.length) / totalOrderItems * 100).toFixed(1);
  
  console.log('\n📊 外键引用完整性报告:');
  console.log(`   引用覆盖率: ${referenceRate}% (${validReferences.length}/${totalOrderItems})`);
  console.log(`   数据完整率: ${integrityRate}% (考虑配置问题)`);
  
  if (referenceRate >= 95 && configurationIssues.length === 0) {
    console.log('   ✅ 外键引用完整性验证通过');
    console.log('   ✅ 客户订单与产品族数据关联完整');
  } else {
    console.log('   ❌ 外键引用完整性验证失败');
    console.log('   ❌ 需要修复缺失或无效的产品族引用');
  }
  
  // 8. 数据质量建议
  console.log('\n💡 数据质量改进建议:');
  if (missingReferences.length > 0) {
    console.log(`   - 为${missingReferences.length}个订单项添加产品族引用`);
  }
  if (invalidReferences.length > 0) {
    console.log(`   - 修复${invalidReferences.length}个无效的产品族引用`);
  }
  if (configurationIssues.length > 0) {
    console.log(`   - 完善${configurationIssues.length}个订单项的产品族配置`);
  }
  if (missingReferences.length === 0 && invalidReferences.length === 0 && configurationIssues.length === 0) {
    console.log('   - 数据质量良好，无需改进');
  }
}

// 运行验证
main();
