#!/usr/bin/env node

/**
 * 验证生产工单与客户订单之间的外键引用完整性
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取JSON文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    return null;
  }
}

// 主验证函数
function main() {
  console.log('🔍 开始验证生产工单与客户订单的外键引用关系...\n');
  
  // 1. 加载数据
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  const productionOrders = loadJsonFile('public/mock/mes/production-orders.json');
  const workOrders = loadJsonFile('public/mock/mes/workOrders.json');
  
  if (!customerOrders || !productionOrders) {
    console.error('❌ 无法加载必要的数据文件');
    return;
  }
  
  // 创建客户订单和订单项的映射
  const customerOrderMap = new Map();
  const customerOrderItemMap = new Map();
  
  customerOrders.orders.forEach(order => {
    customerOrderMap.set(order.id, order);
    order.items.forEach(item => {
      customerOrderItemMap.set(item.id, {
        ...item,
        customerOrderId: order.id,
        customerName: order.customerName,
        orderType: order.orderType
      });
    });
  });
  
  console.log(`📊 数据概览:`);
  console.log(`   客户订单数量: ${customerOrders.orders.length}`);
  console.log(`   客户订单项数量: ${customerOrderItemMap.size}`);
  console.log(`   生产工单数量: ${productionOrders.productionOrders.length}`);
  
  // 统计生产工单项数量
  let totalProductionOrderItems = 0;
  productionOrders.productionOrders.forEach(po => {
    totalProductionOrderItems += po.items.length;
  });
  console.log(`   生产工单项数量: ${totalProductionOrderItems}`);
  
  if (workOrders) {
    console.log(`   工单数量 (workOrders): ${workOrders.data.length}`);
  }
  console.log();
  
  // 2. 验证production-orders.json的外键引用
  console.log('🔍 验证 production-orders.json 外键引用:');
  
  const validReferences = [];
  const invalidOrderReferences = [];
  const invalidItemReferences = [];
  const specificationMismatches = [];
  const quantityMismatches = [];
  
  productionOrders.productionOrders.forEach(po => {
    // 验证客户订单引用
    const customerOrder = customerOrderMap.get(po.customerOrderId);
    if (!customerOrder) {
      invalidOrderReferences.push(`${po.id} → ${po.customerOrderId}`);
      return;
    }
    
    po.items.forEach(item => {
      // 验证客户订单项引用
      const customerOrderItem = customerOrderItemMap.get(item.customerOrderItemId);
      if (!customerOrderItem) {
        invalidItemReferences.push(`${item.id} → ${item.customerOrderItemId}`);
        return;
      }
      
      // 验证规格一致性
      const poSpecs = item.specifications;
      const coSpecs = customerOrderItem.specifications;
      
      const specMismatches = [];
      if (poSpecs.length !== coSpecs.length) specMismatches.push('length');
      if (poSpecs.width !== coSpecs.width) specMismatches.push('width');
      if (poSpecs.thickness !== coSpecs.thickness) specMismatches.push('thickness');
      if (poSpecs.glassType !== coSpecs.glassType) specMismatches.push('glassType');
      if (poSpecs.color !== coSpecs.color) specMismatches.push('color');
      
      if (specMismatches.length > 0) {
        specificationMismatches.push({
          productionOrderItem: item.id,
          customerOrderItem: item.customerOrderItemId,
          mismatches: specMismatches
        });
      }
      
      // 验证数量一致性
      if (item.quantity !== customerOrderItem.quantity) {
        quantityMismatches.push({
          productionOrderItem: item.id,
          customerOrderItem: item.customerOrderItemId,
          productionQuantity: item.quantity,
          customerQuantity: customerOrderItem.quantity
        });
      }
      
      validReferences.push({
        productionOrderId: po.id,
        productionOrderItemId: item.id,
        customerOrderId: po.customerOrderId,
        customerOrderItemId: item.customerOrderItemId,
        customerName: customerOrder.customerName,
        hasSpecMatch: specMismatches.length === 0,
        hasQuantityMatch: item.quantity === customerOrderItem.quantity
      });
    });
  });
  
  // 3. 验证workOrders.json的外键引用
  console.log(`   ✅ 有效引用: ${validReferences.length}个`);
  console.log(`   ❌ 无效订单引用: ${invalidOrderReferences.length}个`);
  console.log(`   ❌ 无效订单项引用: ${invalidItemReferences.length}个`);
  console.log(`   ⚠️  规格不匹配: ${specificationMismatches.length}个`);
  console.log(`   ⚠️  数量不匹配: ${quantityMismatches.length}个\n`);
  
  if (workOrders) {
    console.log('🔍 验证 workOrders.json 外键引用:');
    
    const workOrderIssues = [];
    workOrders.data.forEach(wo => {
      const issues = [];
      
      // 检查orderId格式和有效性
      if (!wo.orderId.startsWith('CO-')) {
        issues.push('orderId格式不符合客户订单ID规范');
      }
      
      if (!customerOrderMap.has(wo.orderId)) {
        issues.push(`引用的客户订单 ${wo.orderId} 不存在`);
      }
      
      // 检查orderItemId格式和有效性
      if (!wo.orderItemId.startsWith('COI-')) {
        issues.push('orderItemId格式不符合客户订单项ID规范');
      }
      
      if (!customerOrderItemMap.has(wo.orderItemId)) {
        issues.push(`引用的客户订单项 ${wo.orderItemId} 不存在`);
      }
      
      if (issues.length > 0) {
        workOrderIssues.push({
          workOrderId: wo.id,
          workOrderNumber: wo.workOrderNumber,
          issues: issues
        });
      }
    });
    
    console.log(`   ❌ 有问题的工单: ${workOrderIssues.length}个`);
    workOrderIssues.forEach(issue => {
      console.log(`      - ${issue.workOrderId}: ${issue.issues.join(', ')}`);
    });
    console.log();
  }
  
  // 4. 检查覆盖率
  console.log('📊 生产工单覆盖率分析:');
  
  const coveredOrderItems = new Set();
  validReferences.forEach(ref => {
    coveredOrderItems.add(ref.customerOrderItemId);
  });
  
  const uncoveredOrderItems = [];
  customerOrderItemMap.forEach((item, itemId) => {
    if (!coveredOrderItems.has(itemId)) {
      uncoveredOrderItems.push({
        itemId: itemId,
        customerName: item.customerName,
        orderType: item.orderType,
        quantity: item.quantity
      });
    }
  });
  
  const coverageRate = (coveredOrderItems.size / customerOrderItemMap.size * 100).toFixed(1);
  console.log(`   覆盖的客户订单项: ${coveredOrderItems.size}个`);
  console.log(`   未覆盖的客户订单项: ${uncoveredOrderItems.size}个`);
  console.log(`   覆盖率: ${coverageRate}%\n`);
  
  // 5. 详细问题报告
  if (invalidOrderReferences.length > 0) {
    console.log('❌ 无效的客户订单引用:');
    invalidOrderReferences.forEach(ref => console.log(`   - ${ref}`));
    console.log();
  }
  
  if (invalidItemReferences.length > 0) {
    console.log('❌ 无效的客户订单项引用:');
    invalidItemReferences.forEach(ref => console.log(`   - ${ref}`));
    console.log();
  }
  
  if (specificationMismatches.length > 0) {
    console.log('⚠️  规格不匹配的工单项:');
    specificationMismatches.forEach(mismatch => {
      console.log(`   - ${mismatch.productionOrderItem} → ${mismatch.customerOrderItem}: ${mismatch.mismatches.join(', ')}`);
    });
    console.log();
  }
  
  if (quantityMismatches.length > 0) {
    console.log('⚠️  数量不匹配的工单项:');
    quantityMismatches.forEach(mismatch => {
      console.log(`   - ${mismatch.productionOrderItem}: 生产${mismatch.productionQuantity} vs 客户${mismatch.customerQuantity}`);
    });
    console.log();
  }
  
  if (uncoveredOrderItems.length > 0) {
    console.log('⚠️  未生成生产工单的客户订单项:');
    uncoveredOrderItems.forEach(item => {
      console.log(`   - ${item.itemId} (${item.customerName}): ${item.orderType}, 数量${item.quantity}`);
    });
    console.log();
  }
  
  // 6. 总结和建议
  console.log('📊 数据关联完整性总结:');
  
  const totalIssues = invalidOrderReferences.length + invalidItemReferences.length + 
                     specificationMismatches.length + quantityMismatches.length + 
                     uncoveredOrderItems.length;
  
  if (totalIssues === 0 && coverageRate >= 95) {
    console.log('   ✅ 生产工单与客户订单关联完整性验证通过');
    console.log('   ✅ 数据质量良好，支持完整的业务追溯');
  } else {
    console.log('   ❌ 发现数据关联问题，需要修复');
    console.log(`   ❌ 总计发现 ${totalIssues} 个问题`);
  }
  
  console.log('\n💡 改进建议:');
  if (invalidOrderReferences.length > 0 || invalidItemReferences.length > 0) {
    console.log('   - 修复无效的外键引用');
  }
  if (specificationMismatches.length > 0) {
    console.log('   - 同步生产工单与客户订单的规格数据');
  }
  if (quantityMismatches.length > 0) {
    console.log('   - 修正生产工单与客户订单的数量差异');
  }
  if (uncoveredOrderItems.length > 0) {
    console.log('   - 为未覆盖的客户订单项生成生产工单');
  }
  if (workOrders) {
    console.log('   - 修复workOrders.json的外键引用问题');
  }
  if (totalIssues === 0 && coverageRate >= 95) {
    console.log('   - 数据质量良好，无需改进');
  }
}

// 运行验证
main();
