#!/usr/bin/env node

/**
 * 修复客户订单数据中缺少的产品族引用
 * 为每个订单项添加 productFamilyId 和 productFamilyConfig
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取数据文件
function loadJsonFile(relativePath) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ 无法读取文件 ${relativePath}:`, error.message);
    process.exit(1);
  }
}

// 保存数据文件
function saveJsonFile(relativePath, data) {
  const filePath = path.join(__dirname, '..', relativePath);
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`✅ 已保存文件: ${relativePath}`);
  } catch (error) {
    console.error(`❌ 无法保存文件 ${relativePath}:`, error.message);
    process.exit(1);
  }
}

// 根据订单项规格和订单类型匹配产品族
function matchProductFamily(orderItem, orderType, productFamilies) {
  const specs = orderItem.specifications;
  const notes = orderItem.notes || '';
  
  // 匹配规则
  if (notes.includes('中空') || notes.includes('+') && notes.includes('A')) {
    return productFamilies.find(f => f.id === 'PF-IGU');
  }
  
  if (notes.includes('夹胶') || notes.includes('PVB')) {
    return productFamilies.find(f => f.id === 'PF-LAMINATED');
  }
  
  if (orderType.includes('家具') || notes.includes('餐桌') || notes.includes('茶几')) {
    return productFamilies.find(f => f.id === 'PF-FURNITURE');
  }
  
  if (orderType.includes('装饰') || orderType.includes('隔断') || 
      specs.glassType === 'tinted' || specs.glassType === 'reflective') {
    return productFamilies.find(f => f.id === 'PF-DECORATIVE');
  }
  
  // 默认匹配单片钢化玻璃
  return productFamilies.find(f => f.id === 'PF-TEMPERED');
}

// 生成产品族配置
function generateProductFamilyConfig(orderItem, productFamily) {
  const specs = orderItem.specifications;
  const notes = orderItem.notes || '';
  const config = {};
  
  // 通用属性
  config.width = specs.width || specs.length || 1000;
  config.height = specs.length || specs.width || 1000;
  
  switch (productFamily.id) {
    case 'PF-TEMPERED':
      config.thickness = specs.thickness.toString();
      config.glass_type = specs.glassType || 'clear';
      config.color = specs.color || '透明';
      config.is_tempered = true;
      config.edge_type = 'ground';
      config.surface_treatment = specs.glassType === 'low_e' || specs.glassType === 'reflective' ? 'coating' : 'none';
      break;
      
    case 'PF-IGU':
      // 解析中空玻璃规格 (如 "6+12A+6")
      const igu_match = notes.match(/(\d+)\+(\d+)A\+(\d+)/);
      if (igu_match) {
        config.glass1_thickness = igu_match[1];
        config.spacer_width = igu_match[2];
        config.glass2_thickness = igu_match[3];
      } else {
        config.glass1_thickness = specs.thickness.toString();
        config.glass2_thickness = specs.thickness.toString();
        config.spacer_width = '12';
      }
      config.glass_type = specs.glassType || 'clear';
      config.color = specs.color || '透明';
      config.is_tempered = false;
      config.gas_filling = 'air';
      config.sealant_type = 'structural';
      config.energy_rating = specs.glassType === 'low_e' ? 'high' : 'standard';
      break;
      
    case 'PF-LAMINATED':
      // 解析夹胶玻璃规格 (如 "5+1.14PVB+5")
      const lam_match = notes.match(/(\d+)\+([0-9.]+)PVB\+(\d+)/);
      if (lam_match) {
        config.glass1_thickness = lam_match[1];
        config.pvb_thickness = lam_match[2];
        config.glass2_thickness = lam_match[3];
        config.total_thickness = (parseInt(lam_match[1]) + parseFloat(lam_match[2]) + parseInt(lam_match[3])).toString();
      } else {
        config.total_thickness = specs.thickness.toString();
        config.glass1_thickness = Math.floor(specs.thickness / 2).toString();
        config.glass2_thickness = Math.floor(specs.thickness / 2).toString();
        config.pvb_thickness = '0.76';
      }
      config.glass_type = specs.glassType || 'clear';
      config.color = specs.color || '透明';
      config.safety_level = 'standard';
      config.pvb_type = 'standard';
      break;
      
    case 'PF-DECORATIVE':
      config.thickness = specs.thickness.toString();
      config.glass_type = specs.glassType || 'tinted';
      config.color = specs.color || '灰色';
      config.surface_finish = 'smooth';
      config.transparency_level = specs.glassType === 'reflective' ? 'transparent' : 'translucent';
      config.pattern_type = 'none';
      config.edge_treatment = 'polished';
      break;
      
    case 'PF-FURNITURE':
      config.thickness = specs.thickness.toString();
      config.glass_type = specs.glassType || 'clear';
      config.color = specs.color || '透明';
      config.edge_processing = 'polished';
      config.corner_type = 'rounded';
      config.safety_treatment = 'tempered';
      config.surface_quality = 'premium';
      config.drilling_required = false;
      break;
  }
  
  return config;
}

// 生成配置哈希值
function generateConfigHash(config) {
  const configString = JSON.stringify(config, Object.keys(config).sort());
  return Buffer.from(configString).toString('base64').substring(0, 16);
}

// 主处理函数
function main() {
  console.log('🔍 开始修复客户订单的产品族引用...\n');
  
  // 1. 加载数据
  const customerOrders = loadJsonFile('public/mock/mes/customer-orders.json');
  const productFamilies = loadJsonFile('public/mock/masterdata/productFamilies.json');
  
  console.log(`📊 数据概览:`);
  console.log(`   产品族数量: ${productFamilies.length}`);
  console.log(`   客户订单数量: ${customerOrders.orders.length}`);
  console.log(`   订单项总数: ${customerOrders.orders.reduce((sum, order) => sum + order.items.length, 0)}\n`);
  
  // 2. 处理每个订单项
  let processedCount = 0;
  let addedReferences = 0;
  
  customerOrders.orders.forEach(order => {
    order.items.forEach(item => {
      processedCount++;
      
      // 检查是否已有产品族引用
      if (!item.productFamilyId) {
        // 匹配产品族
        const matchedFamily = matchProductFamily(item, order.orderType, productFamilies);
        
        if (matchedFamily) {
          // 添加产品族引用
          item.productFamilyId = matchedFamily.id;
          item.productFamilyName = matchedFamily.name;
          
          // 生成产品族配置
          item.productFamilyConfig = generateProductFamilyConfig(item, matchedFamily);
          
          // 生成配置哈希
          item.configurationHash = generateConfigHash(item.productFamilyConfig);
          
          addedReferences++;
          
          console.log(`✅ ${item.id}: ${order.customerName} → ${matchedFamily.name}`);
        } else {
          console.log(`❌ ${item.id}: ${order.customerName} → 无匹配产品族`);
        }
      } else {
        console.log(`ℹ️  ${item.id}: ${order.customerName} → 已有产品族引用`);
      }
    });
  });
  
  // 3. 保存修改后的数据
  saveJsonFile('public/mock/mes/customer-orders.json', customerOrders);
  
  // 4. 统计报告
  console.log('\n📊 处理结果统计:');
  console.log(`   处理订单项总数: ${processedCount}`);
  console.log(`   新增产品族引用: ${addedReferences}`);
  console.log(`   引用完整率: ${((processedCount - (processedCount - addedReferences)) / processedCount * 100).toFixed(1)}%`);
  
  // 5. 验证引用完整性
  console.log('\n🔍 验证产品族引用完整性:');
  const missingReferences = [];
  const invalidReferences = [];
  
  customerOrders.orders.forEach(order => {
    order.items.forEach(item => {
      if (!item.productFamilyId) {
        missingReferences.push(`${item.id} (${order.customerName})`);
      } else {
        const family = productFamilies.find(f => f.id === item.productFamilyId);
        if (!family) {
          invalidReferences.push(`${item.id}: ${item.productFamilyId}`);
        }
      }
    });
  });
  
  if (missingReferences.length === 0 && invalidReferences.length === 0) {
    console.log('   ✅ 所有订单项都有有效的产品族引用');
  } else {
    if (missingReferences.length > 0) {
      console.log(`   ❌ 缺少产品族引用的订单项 (${missingReferences.length}个):`);
      missingReferences.forEach(ref => console.log(`      - ${ref}`));
    }
    if (invalidReferences.length > 0) {
      console.log(`   ❌ 无效产品族引用的订单项 (${invalidReferences.length}个):`);
      invalidReferences.forEach(ref => console.log(`      - ${ref}`));
    }
  }
  
  console.log('\n🎉 客户订单产品族引用修复完成！');
}

// 运行修复
main();
