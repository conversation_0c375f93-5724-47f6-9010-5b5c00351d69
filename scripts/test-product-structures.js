#!/usr/bin/env node

/**
 * 产品结构数据加载测试脚本
 * 验证产品结构数据能否正确加载和解析
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 产品结构数据文件路径
const PRODUCT_STRUCTURES_FILE = path.join(__dirname, '../public/mock/product/product-structures.json');

/**
 * 测试产品结构数据加载
 */
function testProductStructuresLoading() {
  console.log('🧪 开始测试产品结构数据加载...\n');
  
  try {
    // 检查文件是否存在
    if (!fs.existsSync(PRODUCT_STRUCTURES_FILE)) {
      console.error('❌ 产品结构数据文件不存在:', PRODUCT_STRUCTURES_FILE);
      process.exit(1);
    }
    
    console.log('✅ 产品结构数据文件存在');
    
    // 读取和解析JSON
    const fileContent = fs.readFileSync(PRODUCT_STRUCTURES_FILE, 'utf8');
    const data = JSON.parse(fileContent);
    
    console.log('✅ JSON格式正确');
    
    // 验证数据结构
    if (!data.productStructures || !Array.isArray(data.productStructures)) {
      console.error('❌ 数据结构错误：缺少productStructures数组');
      process.exit(1);
    }
    
    console.log('✅ 数据结构正确');
    console.log(`📊 产品结构数量: ${data.productStructures.length}`);
    
    // 验证每个产品结构
    data.productStructures.forEach((structure, index) => {
      console.log(`\n🔍 验证产品结构 ${index + 1}:`);
      console.log(`   ID: ${structure.id}`);
      console.log(`   编码: ${structure.code}`);
      console.log(`   名称: ${structure.name}`);
      console.log(`   产品类型: ${structure.productType}`);
      console.log(`   类别: ${structure.category}`);
      console.log(`   状态: ${structure.status}`);
      
      // 验证必需字段
      const requiredFields = ['id', 'code', 'name', 'productType', 'category', 'status'];
      for (const field of requiredFields) {
        if (!structure[field]) {
          console.error(`   ❌ 缺少必需字段: ${field}`);
        } else {
          console.log(`   ✅ ${field}: ${structure[field]}`);
        }
      }
      
      // 验证产品参数
      if (structure.productParameters && Array.isArray(structure.productParameters)) {
        console.log(`   📋 产品参数数量: ${structure.productParameters.length}`);
        structure.productParameters.forEach((param, paramIndex) => {
          console.log(`      参数 ${paramIndex + 1}: ${param.displayName} (${param.name})`);
        });
      }
      
      // 验证产品约束
      if (structure.productConstraints && Array.isArray(structure.productConstraints)) {
        console.log(`   🔒 产品约束数量: ${structure.productConstraints.length}`);
        structure.productConstraints.forEach((constraint, constraintIndex) => {
          console.log(`      约束 ${constraintIndex + 1}: ${constraint.name}`);
        });
      }
      
      // 验证配置选项
      if (structure.configurationOptions && Array.isArray(structure.configurationOptions)) {
        console.log(`   ⚙️  配置选项数量: ${structure.configurationOptions.length}`);
        structure.configurationOptions.forEach((option, optionIndex) => {
          console.log(`      选项 ${optionIndex + 1}: ${option.optionName}`);
        });
      }
      
      // 验证标签
      if (structure.tags && Array.isArray(structure.tags)) {
        console.log(`   🏷️  标签: ${structure.tags.join(', ')}`);
      }
      
      // 验证应用场景
      if (structure.applications && Array.isArray(structure.applications)) {
        console.log(`   🏢 应用场景: ${structure.applications.join(', ')}`);
      }
    });
    
    console.log('\n📈 数据统计:');
    
    // 按产品类型统计
    const typeStats = {};
    data.productStructures.forEach(structure => {
      typeStats[structure.productType] = (typeStats[structure.productType] || 0) + 1;
    });
    
    console.log('   按产品类型统计:');
    Object.entries(typeStats).forEach(([type, count]) => {
      console.log(`     ${type}: ${count} 个`);
    });
    
    // 按类别统计
    const categoryStats = {};
    data.productStructures.forEach(structure => {
      categoryStats[structure.category] = (categoryStats[structure.category] || 0) + 1;
    });
    
    console.log('   按类别统计:');
    Object.entries(categoryStats).forEach(([category, count]) => {
      console.log(`     ${category}: ${count} 个`);
    });
    
    // 按状态统计
    const statusStats = {};
    data.productStructures.forEach(structure => {
      statusStats[structure.status] = (statusStats[structure.status] || 0) + 1;
    });
    
    console.log('   按状态统计:');
    Object.entries(statusStats).forEach(([status, count]) => {
      console.log(`     ${status}: ${count} 个`);
    });
    
    console.log('\n🎉 产品结构数据测试完成！所有数据都可以正确加载和解析。');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

/**
 * 模拟服务层加载测试
 */
function simulateServiceLoading() {
  console.log('\n🔄 模拟服务层加载测试...\n');
  
  try {
    const fileContent = fs.readFileSync(PRODUCT_STRUCTURES_FILE, 'utf8');
    const data = JSON.parse(fileContent);
    
    // 模拟无筛选条件的加载
    console.log('📥 模拟 getStructures() 调用:');
    console.log(`   返回 ${data.productStructures.length} 个产品结构`);
    
    // 模拟按产品类型筛选
    const windowStructures = data.productStructures.filter(s => s.productType === 'window');
    console.log(`📥 模拟 getStructures({productType: 'window'}) 调用:`);
    console.log(`   返回 ${windowStructures.length} 个窗户类型产品结构`);
    
    // 模拟按类别筛选
    const fireStructures = data.productStructures.filter(s => s.category.includes('防火'));
    console.log(`📥 模拟 getStructures({category: '防火'}) 调用:`);
    console.log(`   返回 ${fireStructures.length} 个防火类别产品结构`);
    
    // 模拟按ID查找
    const specificStructure = data.productStructures.find(s => s.id === 'struct_001');
    console.log(`📥 模拟 getStructureById('struct_001') 调用:`);
    if (specificStructure) {
      console.log(`   返回产品结构: ${specificStructure.name}`);
    } else {
      console.log(`   未找到指定产品结构`);
    }
    
    // 模拟搜索功能
    const searchResults = data.productStructures.filter(s => 
      s.name.includes('标准') || s.description?.includes('标准')
    );
    console.log(`📥 模拟 getStructures({search: '标准'}) 调用:`);
    console.log(`   返回 ${searchResults.length} 个包含"标准"的产品结构`);
    
    console.log('\n✅ 服务层加载模拟测试完成！');
    
  } catch (error) {
    console.error('❌ 服务层模拟测试失败:', error.message);
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testProductStructuresLoading();
  simulateServiceLoading();
}

export { testProductStructuresLoading, simulateServiceLoading };
