# Dialog 内容布局适配新宽度优化

## 概述
基于对话框宽度设置为 `!w-[90vw] !max-w-[1200px]` 后，对内容布局进行响应式优化，充分利用更大的显示空间。

## 主要优化内容

### 1. 网格布局响应式优化

#### CategoryBatchDialog.vue
- **导出格式选择**: `grid-cols-3` → `grid-cols-1 sm:grid-cols-2 lg:grid-cols-3`
- **导入方式选择**: `grid-cols-2` → `grid-cols-1 sm:grid-cols-2`

#### CategoryOperationDialog.vue  
- **基本信息表单**: `grid-cols-2` → `grid-cols-1 lg:grid-cols-2`
- **基础属性列表**: `space-y-2` → `grid-cols-1 xl:grid-cols-2 gap-3`
- **变体属性列表**: `space-y-2` → `grid-cols-1 xl:grid-cols-2 gap-3`

#### MaterialCategoryList.vue
- **分类创建表单**: `grid-cols-2` → `grid-cols-1 lg:grid-cols-2`

#### MaterialDetail.vue
- **基础信息网格**: `grid-cols-1 md:grid-cols-2` → `grid-cols-1 md:grid-cols-2 xl:grid-cols-3`
- **属性模板网格**: `grid-cols-1 md:grid-cols-2` → `grid-cols-1 md:grid-cols-2 xl:grid-cols-3`
- **统计信息网格**: `grid-cols-2 md:grid-cols-4` → `grid-cols-2 md:grid-cols-4 lg:grid-cols-6`

#### AttributeEditor.vue
- **属性基本信息**: `grid-cols-3` → `grid-cols-1 sm:grid-cols-2 lg:grid-cols-3`

### 2. 响应式断点策略

| 屏幕尺寸 | 使用场景 | 对话框宽度 | 内容布局策略 |
|---------|---------|-----------|-------------|
| sm (640px+) | 小屏幕 | 90vw | 单列或两列布局 |
| md (768px+) | 中等屏幕 | 90vw | 两列布局 |
| lg (1024px+) | 大屏幕 | 90vw | 两到三列布局 |
| xl (1280px+) | 超大屏幕 | 1200px (max) | 三列或更多列布局 |

### 3. 布局优化原则

#### 表单字段布局
- **小屏**: 垂直堆叠 (grid-cols-1)
- **中屏**: 两列并排 (lg:grid-cols-2)  
- **大屏**: 保持两列或扩展到三列 (xl:grid-cols-3)

#### 属性编辑器布局
- **小屏**: 单个属性占一行
- **大屏**: 两个属性并排显示 (xl:grid-cols-2)
- 充分利用水平空间减少滚动

#### 统计卡片布局
- **小屏**: 2列展示 (grid-cols-2)
- **中屏**: 4列展示 (md:grid-cols-4)
- **大屏**: 6列展示 (lg:grid-cols-6)

### 4. 具体优化效果

#### 对话框内容密度提升
- 更少的垂直滚动
- 更好的信息组织
- 相关内容的邻近展示

#### 表单填写体验改善
- 字段关联性更强
- 减少视线移动距离
- 提升操作效率

#### 属性管理优化
- 属性编辑器并排显示
- 方便对比和批量编辑
- 空间利用率显著提升

### 5. 兼容性保证

#### 小屏幕设备
- 所有布局在小屏幕上回退到单列
- 保证可用性和可读性
- 触摸友好的操作空间

#### 内容适应性
- 长文本自动换行
- 动态内容不破坏布局
- 保持一致的间距和对齐

### 6. 最佳实践总结

#### 渐进式断点设计
```css
/* 基础: 移动端优先 */
grid-cols-1

/* 小屏幕: 适度增加列数 */
sm:grid-cols-2

/* 中等屏幕: 标准多列 */
md:grid-cols-3

/* 大屏幕: 充分利用空间 */
lg:grid-cols-4

/* 超大屏幕: 最大化利用 */
xl:grid-cols-6
```

#### 内容分组策略
- 相关字段就近放置
- 主要信息优先显示
- 次要信息适当分组

#### 间距和对齐
- 使用一致的 gap 值 (gap-3, gap-4, gap-6)
- 保持视觉层次清晰
- 避免内容过于紧密

## 注意事项

1. **性能影响**: 更复杂的网格布局在低端设备上可能有轻微性能影响
2. **维护性**: 多断点设计增加了CSS维护复杂度
3. **内容适配**: 需要确保所有内容在各种布局下都能正确显示
4. **用户习惯**: 布局变化可能需要用户适应时间

## 验证方法

1. 在不同屏幕尺寸下测试对话框布局
2. 检查内容在各断点下的显示效果
3. 验证表单填写的便利性
4. 确认移动端的可用性