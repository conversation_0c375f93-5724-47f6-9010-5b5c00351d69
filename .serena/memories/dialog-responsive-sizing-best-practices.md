# Dialog 响应式尺寸配置最佳实践

## 概述
本文档记录了在 Vue.js + Shadcn Vue + Tailwind CSS 4 项目中配置对话框响应式尺寸的最佳实践和解决方案。

## 问题背景
- Shadcn Vue 的 DialogContent 组件有默认的尺寸限制：`w-full max-w-[calc(100%-2rem)] sm:max-w-lg`
- 这些默认样式会覆盖业务组件中的自定义样式
- 需要确保所有对话框都有合理的最大高度限制，避免超出视口

## 解决方案

### 1. DialogContent 基础组件修改
**文件**: `src/components/ui/dialog/DialogContent.vue`

移除默认的宽度和高度限制，保留居中和基础样式：
```vue
:class="cn(
  'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200',
  props.class,
)"
```

### 2. 全局样式兜底
**文件**: `src/assets/main.css`

添加全局规则确保所有对话框最大高度限制：
```css
@layer components {
  [data-slot="dialog-content"] {
    max-height: 90vh !important;
  }
}
```

### 3. 业务对话框样式规范

#### 大型对话框（表单、详情等）
适用于：CategoryBatchDialog、CategoryOperationDialog、MaterialDetail 等
```vue
<DialogContent class="!w-[90vw] !max-w-[1200px] !max-h-[90vh] overflow-hidden flex flex-col">
```

#### 中小型对话框（设置、确认等）
适用于：批量导入、右键菜单、设置对话框等
```vue
<DialogContent class="sm:max-w-[500px] !max-h-[90vh]">
```

#### 小型对话框（简单表单）
适用于：演示对话框、简单表单等
```vue
<DialogContent class="sm:max-w-[425px] !max-h-[90vh]">
```

## 核心原则

### 1. 使用 `!important` 覆盖默认样式
由于 Shadcn Vue 组件库的 CSS 优先级较高，需要使用 `!important` 来确保自定义样式生效。

### 2. 自动高度 + 最大高度限制
- 移除固定高度设置（如 `!h-[calc(90vh*9/16)]`）
- 统一使用 `!max-h-[90vh]` 作为最大高度
- 让对话框根据内容自动调整高度

### 3. Flex 布局结构
大型对话框使用 flex 布局：
```vue
<DialogContent class="... overflow-hidden flex flex-col">
  <DialogHeader class="flex-shrink-0">
    <!-- 头部内容 -->
  </DialogHeader>
  
  <div class="flex-1 overflow-y-auto">
    <!-- 可滚动的主要内容 -->
  </div>
  
  <DialogFooter class="flex-shrink-0">
    <!-- 底部按钮 -->
  </DialogFooter>
</DialogContent>
```

## 样式类说明

| 类名 | 作用 | 使用场景 |
|------|------|----------|
| `!w-[90vw]` | 宽度为视口宽度的90% | 大型对话框 |
| `!max-w-[1200px]` | 最大宽度限制 | 大型对话框 |
| `!max-h-[90vh]` | 最大高度为视口高度的90% | 所有对话框 |
| `overflow-hidden` | 隐藏溢出内容 | 需要内部滚动的对话框 |
| `flex flex-col` | 垂直flex布局 | 有头部/内容/底部结构的对话框 |
| `flex-shrink-0` | 防止收缩 | 头部和底部 |
| `flex-1 overflow-y-auto` | 占据剩余空间并允许垂直滚动 | 主要内容区域 |

## 已应用的文件列表

### 大型对话框
- `src/components/metadata/CategoryBatchDialog.vue`
- `src/components/metadata/CategoryOperationDialog.vue`
- `src/components/metadata/MaterialCategoryList.vue` (创建对话框)
- `src/components/metadata/MaterialDetail.vue` (编辑和复制对话框)

### 中小型对话框
- `src/components/metadata/MaterialCategoryList.vue` (批量导入、右键菜单)
- `src/views/MetadataView.vue` (工艺路线、用户管理)
- `src/components/metadata/MaterialTable.vue` (高级筛选)
- `src/components/demo/ComponentsDemo.vue` (演示对话框)

## 注意事项

1. **CSS 优先级**: 必须使用 `!important` 来覆盖组件库默认样式
2. **响应式设计**: 大型对话框在小屏幕上自动适配为90%宽度
3. **滚动处理**: 超出高度的内容通过内部滚动解决，避免对话框超出视口
4. **一致性**: 所有对话框都应遵循相同的最大高度规则

## 验证方法

1. 打开各种对话框检查是否有最大高度限制
2. 调整浏览器窗口大小验证响应式效果
3. 在内容较多时确认内部滚动正常工作
4. 确保对话框不会超出视口边界