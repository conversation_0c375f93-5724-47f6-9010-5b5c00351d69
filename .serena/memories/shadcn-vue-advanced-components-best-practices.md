# Shadcn Vue 高级组件最佳实践

## 数据表格 (DataTable) 最佳实践

### 核心组件结构
```vue
<script setup lang="ts" generic="TData, TValue">
import type { ColumnDef } from '@tanstack/vue-table'
import { useVueTable, getCoreRowModel, getPaginationRowModel } from '@tanstack/vue-table'
</script>
```

### 表格状态管理
- **排序状态**: `const sorting = ref<SortingState>([])`
- **列过滤**: `const columnFilters = ref<ColumnFiltersState>([])`
- **列可见性**: `const columnVisibility = ref<VisibilityState>({})`
- **行选择**: `const rowSelection = ref({})`
- **行展开**: `const expanded = ref<ExpandedState>({})`

### 动作列实现模式
```vue
// 在列定义中添加动作列
{
  id: 'actions',
  enableHiding: false,
  cell: ({ row }) => h(DropdownMenu, {
    // 动作菜单配置
  })
}
```

### 分页组件模式
```vue
<DataTablePagination :table="table" />
```

## 对话框 (Dialog/Sheet) 最佳实践

### Dialog 基础结构
```vue
<Dialog>
  <DialogTrigger>触发器</DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>标题</DialogTitle>
      <DialogDescription>描述</DialogDescription>
    </DialogHeader>
    <DialogFooter>操作按钮</DialogFooter>
  </DialogContent>
</Dialog>
```

### Sheet 侧边栏模式
```vue
<Sheet>
  <SheetTrigger>触发器</SheetTrigger>
  <SheetContent side="right">
    <SheetHeader>
      <SheetTitle>标题</SheetTitle>
      <SheetDescription>描述</SheetDescription>
    </SheetHeader>
  </SheetContent>
</Sheet>
```

### 嵌套使用注意事项
- Dialog 和 ContextMenu 嵌套时，Dialog 必须包裹在外层
- 使用 `as-child` prop 进行正确的组件组合

## 下拉菜单 (DropdownMenu) 最佳实践

### 标准结构
```vue
<DropdownMenu>
  <DropdownMenuTrigger as-child>
    <Button variant="ghost" class="w-8 h-8 p-0">
      <MoreHorizontal class="w-4 h-4" />
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent align="end">
    <DropdownMenuLabel>Actions</DropdownMenuLabel>
    <DropdownMenuItem @click="action">Action</DropdownMenuItem>
    <DropdownMenuSeparator />
    <DropdownMenuCheckboxItem :modelValue="checked">
      Checkbox Item
    </DropdownMenuCheckboxItem>
  </DropdownMenuContent>
</DropdownMenu>
```

### 图标使用
- 使用 `lucide-vue-next` 图标库
- 常用图标: `MoreHorizontal`, `ChevronDown`, `Check`

## 选择器 (Select/Combobox) 最佳实践

### Select 组件
```vue
<Select>
  <SelectTrigger>
    <SelectValue placeholder="选择选项" />
  </SelectTrigger>
  <SelectContent>
    <SelectGroup>
      <SelectLabel>分组标签</SelectLabel>
      <SelectItem value="option1">选项1</SelectItem>
    </SelectGroup>
  </SelectContent>
</Select>
```

### Combobox 组合模式
```vue
<Popover v-model:open="open">
  <PopoverTrigger as-child>
    <Button variant="outline" role="combobox">
      {{ selectedValue || '请选择...' }}
      <ChevronsUpDown class="ml-2 h-4 w-4" />
    </Button>
  </PopoverTrigger>
  <PopoverContent class="w-[200px] p-0">
    <Command v-model="value">
      <CommandInput placeholder="搜索..." />
      <CommandEmpty>无结果</CommandEmpty>
      <CommandGroup>
        <CommandItem>选项</CommandItem>
      </CommandGroup>
    </Command>
  </PopoverContent>
</Popover>
```

## 通知系统 (Sonner) 最佳实践

### 基础使用
```vue
<script setup>
import { toast } from 'vue-sonner'

const showToast = () => {
  toast.success('操作成功!')
  toast.error('操作失败!')
  toast.info('提示信息')
}
</script>
```

## 表单组件最佳实践

### 标签页 (Tabs)
```vue
<Tabs default-value="tab1">
  <TabsList>
    <TabsTrigger value="tab1">标签1</TabsTrigger>
    <TabsTrigger value="tab2">标签2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">内容1</TabsContent>
  <TabsContent value="tab2">内容2</TabsContent>
</Tabs>
```

### 警告对话框 (AlertDialog)
```vue
<AlertDialog>
  <AlertDialogTrigger>删除</AlertDialogTrigger>
  <AlertDialogContent>
    <AlertDialogHeader>
      <AlertDialogTitle>确认删除?</AlertDialogTitle>
      <AlertDialogDescription>
        此操作不可撤销
      </AlertDialogDescription>
    </AlertDialogHeader>
    <AlertDialogFooter>
      <AlertDialogCancel>取消</AlertDialogCancel>
      <AlertDialogAction>确认</AlertDialogAction>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>
```

## 性能优化要点

1. **合理使用 as-child**: 避免不必要的 DOM 嵌套
2. **状态管理**: 使用 ref 而非 reactive 管理表格状态
3. **事件处理**: 使用适当的事件委托
4. **条件渲染**: 使用 v-if/v-show 优化大列表渲染
5. **图标优化**: 按需导入 lucide-vue-next 图标

## 类型安全要点

1. **泛型支持**: DataTable 使用泛型定义数据类型
2. **Props 类型**: 使用 TypeScript interface 定义 props
3. **事件类型**: 明确定义 emit 事件类型
4. **表格列定义**: 使用 ColumnDef 类型约束