# Shadcn Vue Components Setup

This document describes the Shadcn Vue component system configuration for the Glass ERP prototype.

## Configuration

### Components.json
The project is configured with:
- **Style**: New York
- **TypeScript**: Enabled
- **Base Color**: Neutral
- **CSS Variables**: Enabled
- **Icon Library**: Lucide

### Installed Components

The following UI components have been installed and are ready to use:

#### Core Components
- **Button** - Various button variants (default, secondary, destructive, outline, ghost, link)
- **Card** - Card container with header, content, and footer sections
- **Input** - Text input field
- **Label** - Form labels
- **Select** - Dropdown selection component
- **Textarea** - Multi-line text input
- **Table** - Data table with header, body, and footer

#### Form Components
- **Form** - Form wrapper with validation support (vee-validate + zod)
- **FormField** - Individual form field wrapper
- **FormItem** - Form item container
- **FormLabel** - Form field label
- **FormControl** - Form control wrapper
- **FormDescription** - Form field description
- **FormMessage** - Form validation message

#### Overlay Components
- **Dialog** - Modal dialog component for important information or user input
- **Drawer** - Slide-out panel component, suitable for mobile or sidebar scenarios
- **Tooltip** - Hover or focus tooltip component
- **Sonner** - Toast notification component (vue-sonner integration)

#### Navigation Components
- **Sidebar** - Collapsible sidebar navigation component
- **Dropdown Menu** - Context menu and dropdown component
- **Sheet** - Slide-out sheet component

## Usage Examples

### Simple Components (Without Validation)

```vue
<template>
  <div>
    <!-- Button -->
    <Button variant="default">Click me</Button>
    
    <!-- Card -->
    <Card>
      <CardHeader>
        <CardTitle>Title</CardTitle>
        <CardDescription>Description</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Content</p>
      </CardContent>
    </Card>
    
    <!-- Simple Form -->
    <div class="space-y-4">
      <div>
        <Label for="name">Name</Label>
        <Input id="name" v-model="name" placeholder="Enter name" />
      </div>
      
      <div>
        <Label for="status">Status</Label>
        <Select v-model="status">
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'

const name = ref('')
const status = ref('')
</script>
```

### Form with Validation

```vue
<template>
  <Form :validation-schema="formSchema" @submit="onSubmit">
    <FormField v-slot="{ componentField }" name="email">
      <FormItem>
        <FormLabel>Email</FormLabel>
        <FormControl>
          <Input v-bind="componentField" type="email" placeholder="Enter email" />
        </FormControl>
        <FormDescription>We'll never share your email.</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>
    
    <Button type="submit">Submit</Button>
  </Form>
</template>

<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

const formSchema = toTypedSchema(z.object({
  email: z.string().email('Invalid email address'),
}))

function onSubmit(values: any) {
  console.log('Form submitted:', values)
}
</script>
```

## Dependencies

The following dependencies were automatically installed:
- `reka-ui` - Underlying UI library
- `@tanstack/vue-table` - Table functionality
- `@vee-validate/zod` - Form validation with Zod
- `vee-validate` - Vue form validation
- `@vueuse/core` - Vue composition utilities
- `zod` - Schema validation

## Test Components

Two test components have been created to demonstrate usage:
- `ComponentsTest.vue` - Full form validation example
- `SimpleComponentsTest.vue` - Simple components without validation

## Theming

The components support both light and dark themes through CSS variables defined in `src/assets/main.css`. The theme can be toggled by adding/removing the `dark` class on the root element.

## Provider Setup (App.vue)

The following providers have been configured in `App.vue` for optimal component functionality:

```vue
<script setup lang="ts">
import { TooltipProvider } from '@/components/ui/tooltip'
import { Toaster } from '@/components/ui/sonner'
import 'vue-sonner/style.css' // Required for vue-sonner v2
</script>

<template>
  <!-- TooltipProvider wraps the entire app for tooltip context -->
  <TooltipProvider :delay-duration="0" :skip-delay-duration="500">
    <AppLayout />

    <!-- Sonner Toast notifications at root level -->
    <!-- pointer-events-auto class ensures compatibility with Dialog -->
    <Toaster
      class="pointer-events-auto"
      :theme="'system'"
      :position="'bottom-right'"
      :expand="true"
      :rich-colors="true"
      :close-button="true"
    />
  </TooltipProvider>
</template>
```

## Best Practices

### Dialog Component
- Always use `DialogTrigger` with `as-child` for custom trigger elements
- Include `DialogHeader` with `DialogTitle` for accessibility
- Use `DialogFooter` for action buttons
- For forms in dialogs, handle submission properly and close on success

### Drawer Component
- Best for mobile interfaces or side panels
- Use `DrawerClose` with `as-child` for custom close buttons
- Include proper header and footer sections

### Sonner Toast
- Import and use `toast` from 'vue-sonner' for programmatic notifications
- Use different toast types: `toast.success()`, `toast.error()`, `toast.warning()`, `toast.info()`
- Support for actions: `toast('Message', { action: { label: 'Undo', onClick: () => {} } })`
- Promise support: `toast.promise(promise, { loading: '...', success: '...', error: '...' })`

### Tooltip Component
- Must be wrapped in `TooltipProvider` (configured in App.vue)
- Use `TooltipTrigger` with `as-child` for custom trigger elements
- Keep tooltip content concise and helpful

## Composables

### useNotifications
Provides unified notification management:

```typescript
import { useNotifications, useBusinessNotifications } from '@/composables/useNotifications'

const notifications = useNotifications()
const businessNotifications = useBusinessNotifications()

// Basic notifications
notifications.success('Success message', 'Description')
notifications.error('Error message')

// Business-specific notifications
businessNotifications.saveSuccess('Project')
businessNotifications.deleteConfirm(() => deleteItem(), 'Project')
```

### useDialog
Manages dialog state and provides common dialog patterns:

```typescript
import { useDialog, useConfirmDialog, useBusinessDialogs } from '@/composables/useDialog'

const dialog = useDialog()
const confirmDialog = useConfirmDialog()
const businessDialogs = useBusinessDialogs()

// Basic dialog control
dialog.open()
dialog.close()

// Confirmation dialog
const confirmed = await confirmDialog.confirm({
  title: 'Confirm Action',
  description: 'Are you sure?'
})

// Business dialogs
const shouldDelete = await businessDialogs.confirmDelete('Project')
```

## Component Examples

### Dialog with Form
```vue
<template>
  <Dialog v-model:open="dialog.isOpen.value">
    <DialogTrigger as-child>
      <Button>Open Dialog</Button>
    </DialogTrigger>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Edit Profile</DialogTitle>
        <DialogDescription>Make changes to your profile</DialogDescription>
      </DialogHeader>
      <form @submit.prevent="handleSubmit">
        <div class="space-y-4">
          <Input v-model="formData.name" placeholder="Name" />
        </div>
        <DialogFooter>
          <Button type="submit">Save</Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
```

### Toast Notifications
```vue
<script setup>
import { toast } from 'vue-sonner'

const showToast = () => {
  toast.success('Success!', {
    description: 'Your changes have been saved',
    action: {
      label: 'Undo',
      onClick: () => console.log('Undo clicked')
    }
  })
}
</script>
```

### Tooltip Usage
```vue
<template>
  <Tooltip>
    <TooltipTrigger as-child>
      <Button variant="outline">Hover me</Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>This is a helpful tooltip</p>
    </TooltipContent>
  </Tooltip>
</template>
```

## Demo Component

A comprehensive demo component is available at `src/components/demo/ComponentsDemo.vue` showcasing:
- Basic usage of all overlay components
- Form integration with dialogs
- Toast notification examples
- Tooltip variations
- Component combinations and best practices

## Next Steps

The component system is now ready for use in the Glass ERP prototype. You can:
1. Import and use any of the installed components
2. Create custom components that extend the base components
3. Add additional Shadcn Vue components as needed using `pnpm dlx shadcn-vue@latest add <component-name>`
4. Use the provided composables for common patterns
5. Reference the demo component for implementation examples