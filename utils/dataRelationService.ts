/**
 * 数据关联查询服务
 * 提供高效的跨文件数据关联查询功能
 */

import { mockDataLoader } from './mockDataLoader';

export interface RelationQuery {
  sourceData: any;
  relations: RelationConfig[];
}

export interface RelationConfig {
  name: string;
  sourceField: string;
  targetFile: string;
  targetField: string;
  multiple?: boolean;
  required?: boolean;
}

export interface QueryResult<T = any> {
  data: T;
  relations: Record<string, any>;
  metadata: {
    loadTime: number;
    cacheHit: boolean;
    relationsLoaded: number;
  };
}

export class DataRelationService {
  private relationCache: Map<string, any> = new Map();

  /**
   * 执行关联查询
   */
  async executeRelationQuery<T = any>(query: RelationQuery): Promise<QueryResult<T>> {
    const startTime = Date.now();
    const relations: Record<string, any> = {};
    let cacheHits = 0;

    // 并行加载所有关联数据
    const relationPromises = query.relations.map(async (relation) => {
      const cacheKey = `${relation.targetFile}`;
      
      let targetData;
      if (this.relationCache.has(cacheKey)) {
        targetData = this.relationCache.get(cacheKey);
        cacheHits++;
      } else {
        targetData = await mockDataLoader.loadData(relation.targetFile);
        this.relationCache.set(cacheKey, targetData);
      }

      return { relation, targetData };
    });

    const relationResults = await Promise.all(relationPromises);

    // 处理每个关联
    for (const { relation, targetData } of relationResults) {
      const relatedItems = this.findRelatedItems(
        query.sourceData,
        relation,
        targetData
      );

      relations[relation.name] = relation.multiple ? relatedItems : relatedItems[0] || null;
    }

    return {
      data: query.sourceData,
      relations,
      metadata: {
        loadTime: Date.now() - startTime,
        cacheHit: cacheHits > 0,
        relationsLoaded: query.relations.length
      }
    };
  }

  /**
   * 加载客户订单详情（包含所有关联数据）
   */
  async loadCustomerOrderDetail(orderId: string): Promise<QueryResult> {
    const orderData = await mockDataLoader.loadData('mes/customer-orders.json');
    const order = orderData.orders?.find(o => o.id === orderId);

    if (!order) {
      throw new Error(`Order not found: ${orderId}`);
    }

    const relations: RelationConfig[] = [
      {
        name: 'customer',
        sourceField: 'customerId',
        targetFile: 'crm/customers.json',
        targetField: 'data[].id',
        required: true
      },
      {
        name: 'productionOrders',
        sourceField: 'id',
        targetFile: 'mes/production-orders.json',
        targetField: 'productionOrders[].customerOrderId',
        multiple: true
      }
    ];

    return this.executeRelationQuery({
      sourceData: order,
      relations
    });
  }

  /**
   * 加载生产工单详情（包含所有关联数据）
   */
  async loadProductionOrderDetail(workOrderId: string): Promise<QueryResult> {
    const workOrderData = await mockDataLoader.loadData('mes/production-orders.json');
    const workOrder = workOrderData.productionOrders?.find(wo => wo.id === workOrderId);

    if (!workOrder) {
      throw new Error(`Work order not found: ${workOrderId}`);
    }

    const relations: RelationConfig[] = [
      {
        name: 'customer',
        sourceField: 'customerId',
        targetFile: 'crm/customers.json',
        targetField: 'data[].id',
        required: true
      },
      {
        name: 'customerOrder',
        sourceField: 'customerOrderId',
        targetFile: 'mes/customer-orders.json',
        targetField: 'orders[].id',
        required: true
      }
    ];

    const result = await this.executeRelationQuery({
      sourceData: workOrder,
      relations
    });

    // 处理工单项的产品族和工作中心关联
    if (result.data.items) {
      result.data.items = await Promise.all(
        result.data.items.map(async (item) => {
          const productFamilyData = await mockDataLoader.loadData('masterdata/productFamilies.json');
          const productFamily = productFamilyData.find(pf => pf.id === item.productFamilyId);

          // 处理工艺流程的工作中心映射
          const processFlow = item.processFlow?.map(step => ({
            ...step,
            workCenterId: mockDataLoader.getWorkCenterMapping(step.workstation) || step.workstation
          })) || [];

          return {
            ...item,
            productFamily,
            processFlow
          };
        })
      );
    }

    return result;
  }

  /**
   * 加载工作中心产能分析数据
   */
  async loadWorkCenterCapacityAnalysis(): Promise<{
    workCenters: any[];
    currentLoad: Record<string, number>;
    plannedLoad: Record<string, number>;
    recommendations: string[];
  }> {
    const [workCentersData, productionOrdersData, workstationMapping] = await Promise.all([
      mockDataLoader.loadData('masterdata/workCenters.json'),
      mockDataLoader.loadData('mes/production-orders.json'),
      mockDataLoader.loadData('masterdata/workstationMapping.json')
    ]);

    const workCenters = Array.isArray(workCentersData) ? workCentersData : [];
    const productionOrders = productionOrdersData.productionOrders || [];
    const mappings = workstationMapping.mappings || {};

    // 计算当前负载
    const currentLoad: Record<string, number> = {};
    const plannedLoad: Record<string, number> = {};

    // 初始化负载统计
    workCenters.forEach(wc => {
      currentLoad[wc.id] = 0;
      plannedLoad[wc.id] = 0;
    });

    // 统计生产工单的工作中心负载
    productionOrders.forEach(order => {
      if (order.status === 'in_progress') {
        order.items?.forEach(item => {
          item.processFlow?.forEach(step => {
            const workCenterId = mappings[step.workstation]?.workCenterId || step.workstation;
            if (currentLoad[workCenterId] !== undefined) {
              currentLoad[workCenterId] += step.estimatedDuration || 0;
            }
          });
        });
      } else if (order.status === 'pending' || order.status === 'released') {
        order.items?.forEach(item => {
          item.processFlow?.forEach(step => {
            const workCenterId = mappings[step.workstation]?.workCenterId || step.workstation;
            if (plannedLoad[workCenterId] !== undefined) {
              plannedLoad[workCenterId] += step.estimatedDuration || 0;
            }
          });
        });
      }
    });

    // 生成建议
    const recommendations: string[] = [];
    workCenters.forEach(wc => {
      const totalLoad = currentLoad[wc.id] + plannedLoad[wc.id];
      const utilizationRate = totalLoad / (wc.capacity || 100);

      if (utilizationRate > 0.9) {
        recommendations.push(`${wc.name}负载过高(${Math.round(utilizationRate * 100)}%)，建议调整生产计划`);
      } else if (utilizationRate < 0.3) {
        recommendations.push(`${wc.name}负载较低(${Math.round(utilizationRate * 100)}%)，可以安排更多任务`);
      }
    });

    return {
      workCenters,
      currentLoad,
      plannedLoad,
      recommendations
    };
  }

  /**
   * 搜索订单（支持多字段模糊搜索）
   */
  async searchOrders(searchTerm: string, filters?: Record<string, any>): Promise<{
    orders: any[];
    total: number;
    searchTime: number;
  }> {
    const startTime = Date.now();
    
    const orderData = await mockDataLoader.loadData('mes/customer-orders.json');
    let orders = orderData.orders || [];

    // 应用搜索条件
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      orders = orders.filter(order => 
        order.orderNumber?.toLowerCase().includes(term) ||
        order.customerName?.toLowerCase().includes(term) ||
        order.projectName?.toLowerCase().includes(term) ||
        order.orderType?.toLowerCase().includes(term)
      );
    }

    // 应用过滤器
    if (filters) {
      orders = orders.filter(order => {
        return Object.entries(filters).every(([key, value]) => {
          if (!value) return true;
          return order[key] === value;
        });
      });
    }

    // 加载客户关联数据
    const enrichedOrders = await Promise.all(
      orders.map(async order => {
        const customer = await mockDataLoader.getCustomerById(order.customerId);
        return {
          ...order,
          customer
        };
      })
    );

    return {
      orders: enrichedOrders,
      total: enrichedOrders.length,
      searchTime: Date.now() - startTime
    };
  }

  /**
   * 获取订单统计数据
   */
  async getOrderStatistics(): Promise<{
    totalOrders: number;
    ordersByStatus: Record<string, number>;
    ordersByPriority: Record<string, number>;
    ordersByCustomer: Record<string, number>;
    revenueByMonth: Record<string, number>;
  }> {
    const orderData = await mockDataLoader.loadData('mes/customer-orders.json');
    const orders = orderData.orders || [];

    const stats = {
      totalOrders: orders.length,
      ordersByStatus: {},
      ordersByPriority: {},
      ordersByCustomer: {},
      revenueByMonth: {}
    };

    orders.forEach(order => {
      // 按状态统计
      stats.ordersByStatus[order.status] = (stats.ordersByStatus[order.status] || 0) + 1;
      
      // 按优先级统计
      stats.ordersByPriority[order.priority] = (stats.ordersByPriority[order.priority] || 0) + 1;
      
      // 按客户统计
      stats.ordersByCustomer[order.customerName] = (stats.ordersByCustomer[order.customerName] || 0) + 1;
      
      // 按月份统计收入
      const month = new Date(order.createdAt).toISOString().substring(0, 7);
      stats.revenueByMonth[month] = (stats.revenueByMonth[month] || 0) + (order.estimatedCost || 0);
    });

    return stats;
  }

  /**
   * 查找关联项目
   */
  private findRelatedItems(sourceData: any, relation: RelationConfig, targetData: any): any[] {
    const sourceValue = this.getNestedValue(sourceData, relation.sourceField);
    if (!sourceValue) return [];

    const targetItems = this.extractArrayFromData(targetData, relation.targetField);
    
    return targetItems.filter(item => {
      const targetValue = this.getNestedValue(item, this.getFieldName(relation.targetField));
      return targetValue === sourceValue;
    });
  }

  /**
   * 从数据中提取数组
   */
  private extractArrayFromData(data: any, fieldPath: string): any[] {
    if (fieldPath.includes('[]')) {
      const arrayPath = fieldPath.split('[]')[0];
      const arrayData = this.getNestedValue(data, arrayPath);
      return Array.isArray(arrayData) ? arrayData : [];
    }
    
    return Array.isArray(data) ? data : [];
  }

  /**
   * 获取字段名（去除数组标记）
   */
  private getFieldName(fieldPath: string): string {
    return fieldPath.split('[]').pop()?.replace(/^\./, '') || fieldPath;
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 清除关联缓存
   */
  clearRelationCache(): void {
    this.relationCache.clear();
  }
}

// 导出单例实例
export const dataRelationService = new DataRelationService();
