/**
 * 数据验证工具
 * 用于验证MOCK数据的完整性和一致性
 */

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  type: 'FOREIGN_KEY' | 'ENUM' | 'BUSINESS_RULE' | 'DATA_TYPE';
  message: string;
  sourceFile: string;
  sourceField: string;
  value: any;
  constraint?: string;
}

export interface ValidationWarning {
  type: 'MISSING_FIELD' | 'DEPRECATED' | 'PERFORMANCE';
  message: string;
  sourceFile: string;
  sourceField?: string;
  suggestion?: string;
}

export interface ForeignKeyConstraint {
  id: string;
  description: string;
  sourceFile: string;
  sourceField: string;
  targetFile: string;
  targetField: string;
  required: boolean;
  cascadeDelete: boolean;
  validationRule: 'EXACT_MATCH' | 'KEY_EXISTS' | 'PARTIAL_MATCH';
}

export interface EnumConstraint {
  id: string;
  description: string;
  sourceFile: string;
  sourceField: string;
  enumFile: string;
  enumKey: string;
  required: boolean;
}

export interface BusinessRule {
  id: string;
  description: string;
  sourceFile: string;
  rule: string;
  severity: 'error' | 'warning';
}

export class DataValidator {
  private constraints: ForeignKeyConstraint[] = [];
  private enumConstraints: EnumConstraint[] = [];
  private businessRules: BusinessRule[] = [];
  private mockDataCache: Map<string, any> = new Map();

  constructor() {
    this.loadConstraints();
  }

  /**
   * 加载约束配置
   */
  private async loadConstraints() {
    try {
      const constraintsResponse = await fetch('/mock/metadata/foreignKeyConstraints.json');
      const constraintsData = await constraintsResponse.json();
      
      this.constraints = constraintsData.constraints || [];
      this.enumConstraints = constraintsData.enumConstraints || [];
      this.businessRules = constraintsData.businessRules || [];
    } catch (error) {
      console.error('Failed to load constraints:', error);
    }
  }

  /**
   * 验证单个文件的数据
   */
  async validateFile(filePath: string): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      const data = await this.loadMockData(filePath);
      
      // 验证外键约束
      await this.validateForeignKeys(filePath, data, result);
      
      // 验证枚举约束
      await this.validateEnums(filePath, data, result);
      
      // 验证业务规则
      this.validateBusinessRules(filePath, data, result);
      
      result.isValid = result.errors.length === 0;
      
    } catch (error) {
      result.isValid = false;
      result.errors.push({
        type: 'DATA_TYPE',
        message: `Failed to load or parse file: ${error.message}`,
        sourceFile: filePath,
        sourceField: '',
        value: null
      });
    }

    return result;
  }

  /**
   * 验证所有相关文件
   */
  async validateAllFiles(): Promise<Map<string, ValidationResult>> {
    const results = new Map<string, ValidationResult>();
    
    const filesToValidate = [
      'mes/customer-orders.json',
      'mes/production-orders.json',
      'mes/workOrders.json',
      'crm/customers.json',
      'masterdata/productFamilies.json',
      'masterdata/workCenters.json'
    ];

    for (const file of filesToValidate) {
      const result = await this.validateFile(file);
      results.set(file, result);
    }

    return results;
  }

  /**
   * 加载MOCK数据
   */
  private async loadMockData(filePath: string): Promise<any> {
    if (this.mockDataCache.has(filePath)) {
      return this.mockDataCache.get(filePath);
    }

    const response = await fetch(`/mock/${filePath}`);
    const data = await response.json();
    this.mockDataCache.set(filePath, data);
    
    return data;
  }

  /**
   * 验证外键约束
   */
  private async validateForeignKeys(
    filePath: string, 
    data: any, 
    result: ValidationResult
  ): Promise<void> {
    const relevantConstraints = this.constraints.filter(c => 
      c.sourceFile === filePath || c.sourceFile.endsWith(filePath)
    );

    for (const constraint of relevantConstraints) {
      try {
        const targetData = await this.loadMockData(constraint.targetFile);
        const sourceValues = this.extractFieldValues(data, constraint.sourceField);
        const targetValues = this.extractFieldValues(targetData, constraint.targetField);

        for (const sourceValue of sourceValues) {
          if (sourceValue === null || sourceValue === undefined) {
            if (constraint.required) {
              result.errors.push({
                type: 'FOREIGN_KEY',
                message: `Required foreign key field is null: ${constraint.sourceField}`,
                sourceFile: filePath,
                sourceField: constraint.sourceField,
                value: sourceValue,
                constraint: constraint.id
              });
            }
            continue;
          }

          const isValid = this.validateForeignKeyValue(
            sourceValue, 
            targetValues, 
            constraint.validationRule
          );

          if (!isValid) {
            result.errors.push({
              type: 'FOREIGN_KEY',
              message: `Foreign key constraint violation: ${constraint.description}`,
              sourceFile: filePath,
              sourceField: constraint.sourceField,
              value: sourceValue,
              constraint: constraint.id
            });
          }
        }
      } catch (error) {
        result.warnings.push({
          type: 'MISSING_FIELD',
          message: `Could not validate constraint ${constraint.id}: ${error.message}`,
          sourceFile: filePath,
          sourceField: constraint.sourceField
        });
      }
    }
  }

  /**
   * 验证枚举约束
   */
  private async validateEnums(
    filePath: string,
    data: any,
    result: ValidationResult
  ): Promise<void> {
    const relevantConstraints = this.enumConstraints.filter(c => 
      c.sourceFile === filePath || c.sourceFile.endsWith(filePath)
    );

    for (const constraint of relevantConstraints) {
      try {
        const enumData = await this.loadMockData(constraint.enumFile);
        const enumValues = enumData.enums[constraint.enumKey]?.values?.map(v => v.key) || [];
        const sourceValues = this.extractFieldValues(data, constraint.sourceField);

        for (const sourceValue of sourceValues) {
          if (sourceValue === null || sourceValue === undefined) {
            if (constraint.required) {
              result.errors.push({
                type: 'ENUM',
                message: `Required enum field is null: ${constraint.sourceField}`,
                sourceFile: filePath,
                sourceField: constraint.sourceField,
                value: sourceValue,
                constraint: constraint.id
              });
            }
            continue;
          }

          if (!enumValues.includes(sourceValue)) {
            result.errors.push({
              type: 'ENUM',
              message: `Invalid enum value: ${sourceValue}. Valid values: ${enumValues.join(', ')}`,
              sourceFile: filePath,
              sourceField: constraint.sourceField,
              value: sourceValue,
              constraint: constraint.id
            });
          }
        }
      } catch (error) {
        result.warnings.push({
          type: 'MISSING_FIELD',
          message: `Could not validate enum constraint ${constraint.id}: ${error.message}`,
          sourceFile: filePath,
          sourceField: constraint.sourceField
        });
      }
    }
  }

  /**
   * 验证业务规则
   */
  private validateBusinessRules(
    filePath: string,
    data: any,
    result: ValidationResult
  ): void {
    const relevantRules = this.businessRules.filter(r => 
      r.sourceFile === filePath || r.sourceFile.endsWith(filePath)
    );

    for (const rule of relevantRules) {
      try {
        // 这里可以实现更复杂的业务规则验证逻辑
        // 目前只做基本的数值计算验证
        if (rule.id === 'order_total_amount_calculation') {
          this.validateOrderTotalAmount(data, result, rule);
        }
      } catch (error) {
        result.warnings.push({
          type: 'MISSING_FIELD',
          message: `Could not validate business rule ${rule.id}: ${error.message}`,
          sourceFile: filePath
        });
      }
    }
  }

  /**
   * 验证订单总金额计算
   */
  private validateOrderTotalAmount(data: any, result: ValidationResult, rule: BusinessRule): void {
    const orders = data.orders || [];
    
    for (const order of orders) {
      const items = order.items || [];
      
      for (const item of items) {
        const expectedTotal = item.quantity * item.unitPrice;
        
        if (Math.abs(item.totalAmount - expectedTotal) > 0.01) {
          const error: ValidationError = {
            type: 'BUSINESS_RULE',
            message: `Total amount mismatch: expected ${expectedTotal}, got ${item.totalAmount}`,
            sourceFile: rule.sourceFile,
            sourceField: 'totalAmount',
            value: item.totalAmount,
            constraint: rule.id
          };

          if (rule.severity === 'error') {
            result.errors.push(error);
          } else {
            result.warnings.push({
              type: 'MISSING_FIELD',
              message: error.message,
              sourceFile: error.sourceFile,
              sourceField: error.sourceField
            });
          }
        }
      }
    }
  }

  /**
   * 从对象中提取字段值
   */
  private extractFieldValues(data: any, fieldPath: string): any[] {
    // 简化的字段路径解析，实际项目中可能需要更复杂的实现
    const values: any[] = [];
    
    try {
      if (fieldPath.includes('[].')) {
        // 处理数组字段路径，如 "orders[].items[].id"
        const parts = fieldPath.split('[].');
        let current = data;
        
        for (let i = 0; i < parts.length; i++) {
          const part = parts[i];
          if (i === 0) {
            current = current[part];
          } else if (Array.isArray(current)) {
            const nextValues = [];
            for (const item of current) {
              if (item && item[part] !== undefined) {
                if (i === parts.length - 1) {
                  nextValues.push(item[part]);
                } else {
                  nextValues.push(item[part]);
                }
              }
            }
            current = nextValues.flat();
          }
        }
        
        if (Array.isArray(current)) {
          values.push(...current);
        } else if (current !== undefined) {
          values.push(current);
        }
      } else {
        // 处理简单字段路径
        const value = this.getNestedValue(data, fieldPath);
        if (value !== undefined) {
          values.push(value);
        }
      }
    } catch (error) {
      console.warn(`Failed to extract field values for ${fieldPath}:`, error);
    }
    
    return values;
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 验证外键值
   */
  private validateForeignKeyValue(
    sourceValue: any,
    targetValues: any[],
    rule: string
  ): boolean {
    switch (rule) {
      case 'EXACT_MATCH':
        return targetValues.includes(sourceValue);
      case 'KEY_EXISTS':
        return targetValues.some(target => target === sourceValue);
      case 'PARTIAL_MATCH':
        return targetValues.some(target => 
          String(target).toLowerCase().includes(String(sourceValue).toLowerCase())
        );
      default:
        return false;
    }
  }
}

// 导出单例实例
export const dataValidator = new DataValidator();
