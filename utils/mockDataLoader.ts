/**
 * MOCK数据加载器
 * 提供高效的数据加载、缓存和关联查询功能
 */

export interface LoaderOptions {
  useCache?: boolean;
  timeout?: number;
  retries?: number;
}

export interface PaginationOptions {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterOptions {
  [key: string]: any;
}

export interface DataRelation {
  sourceField: string;
  targetFile: string;
  targetField: string;
  targetAlias?: string;
}

export class MockDataLoader {
  private cache: Map<string, any> = new Map();
  private cacheTimestamps: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存
  private workstationMapping: Map<string, string> = new Map();

  constructor() {
    this.initializeWorkstationMapping();
  }

  /**
   * 初始化工作站映射
   */
  private async initializeWorkstationMapping() {
    try {
      const mapping = await this.loadData('masterdata/workstationMapping.json');
      Object.entries(mapping.mappings || {}).forEach(([workstation, config]: [string, any]) => {
        this.workstationMapping.set(workstation, config.workCenterId);
      });
    } catch (error) {
      console.warn('Failed to load workstation mapping:', error);
    }
  }

  /**
   * 加载数据（带缓存）
   */
  async loadData(filePath: string, options: LoaderOptions = {}): Promise<any> {
    const { useCache = true, timeout = 5000, retries = 3 } = options;

    // 检查缓存
    if (useCache && this.isCacheValid(filePath)) {
      return this.cache.get(filePath);
    }

    // 加载数据
    let lastError: Error | null = null;
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(`/mock/${filePath}`, {
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // 更新缓存
        if (useCache) {
          this.cache.set(filePath, data);
          this.cacheTimestamps.set(filePath, Date.now());
        }

        return data;
      } catch (error) {
        lastError = error as Error;
        if (attempt < retries - 1) {
          await this.delay(Math.pow(2, attempt) * 1000); // 指数退避
        }
      }
    }

    throw lastError;
  }

  /**
   * 分页加载数据
   */
  async loadDataWithPagination(
    filePath: string,
    pagination: PaginationOptions,
    filters?: FilterOptions,
    options?: LoaderOptions
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const fullData = await this.loadData(filePath, options);
    let items = this.extractDataArray(fullData);

    // 应用过滤器
    if (filters) {
      items = this.applyFilters(items, filters);
    }

    // 应用排序
    if (pagination.sortBy) {
      items = this.applySorting(items, pagination.sortBy, pagination.sortOrder);
    }

    // 应用分页
    const total = items.length;
    const totalPages = Math.ceil(total / pagination.pageSize);
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    const paginatedData = items.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total,
      page: pagination.page,
      pageSize: pagination.pageSize,
      totalPages
    };
  }

  /**
   * 加载关联数据
   */
  async loadDataWithRelations(
    filePath: string,
    relations: DataRelation[],
    options?: LoaderOptions
  ): Promise<any> {
    const mainData = await this.loadData(filePath, options);
    const items = this.extractDataArray(mainData);

    // 预加载所有关联数据
    const relationDataMap = new Map<string, any>();
    for (const relation of relations) {
      const relationData = await this.loadData(relation.targetFile, options);
      relationDataMap.set(relation.targetFile, relationData);
    }

    // 为每个项目添加关联数据
    const enrichedItems = items.map(item => {
      const enrichedItem = { ...item };

      relations.forEach(relation => {
        const relationData = relationDataMap.get(relation.targetFile);
        const relationItems = this.extractDataArray(relationData);
        
        const sourceValue = this.getNestedValue(item, relation.sourceField);
        const relatedItem = relationItems.find(relItem => 
          this.getNestedValue(relItem, relation.targetField) === sourceValue
        );

        const alias = relation.targetAlias || relation.targetFile.replace(/.*\//, '').replace('.json', '');
        enrichedItem[alias] = relatedItem || null;
      });

      return enrichedItem;
    });

    return {
      ...mainData,
      [this.getDataArrayKey(mainData)]: enrichedItems
    };
  }

  /**
   * 加载客户订单（优化版）
   */
  async loadCustomerOrders(
    pagination?: PaginationOptions,
    filters?: FilterOptions
  ): Promise<any> {
    const relations: DataRelation[] = [
      {
        sourceField: 'customerId',
        targetFile: 'crm/customers.json',
        targetField: 'data[].id',
        targetAlias: 'customer'
      },
      {
        sourceField: 'items[].productFamilyId',
        targetFile: 'masterdata/productFamilies.json',
        targetField: '[].id',
        targetAlias: 'productFamily'
      }
    ];

    if (pagination) {
      const paginatedData = await this.loadDataWithPagination(
        'mes/customer-orders.json',
        pagination,
        filters
      );

      // 为分页数据添加关联
      const enrichedData = await Promise.all(
        paginatedData.data.map(async order => {
          const customer = await this.getCustomerById(order.customerId);
          return {
            ...order,
            customer
          };
        })
      );

      return {
        ...paginatedData,
        data: enrichedData
      };
    } else {
      return this.loadDataWithRelations('mes/customer-orders.json', relations);
    }
  }

  /**
   * 加载生产工单（优化版）
   */
  async loadProductionOrders(filters?: FilterOptions): Promise<any> {
    const data = await this.loadData('mes/production-orders.json');
    let orders = data.productionOrders || [];

    // 应用过滤器
    if (filters) {
      orders = this.applyFilters(orders, filters);
    }

    // 添加关联数据
    const enrichedOrders = await Promise.all(
      orders.map(async order => {
        const customer = await this.getCustomerById(order.customerId);
        const customerOrder = await this.getCustomerOrderById(order.customerOrderId);
        
        // 处理工单项的工作中心映射
        const enrichedItems = order.items?.map(item => ({
          ...item,
          processFlow: item.processFlow?.map(step => ({
            ...step,
            workCenterId: this.workstationMapping.get(step.workstation) || step.workstation
          }))
        })) || [];

        return {
          ...order,
          customer,
          customerOrder,
          items: enrichedItems
        };
      })
    );

    return {
      ...data,
      productionOrders: enrichedOrders
    };
  }

  /**
   * 根据ID获取客户信息
   */
  async getCustomerById(customerId: string): Promise<any> {
    if (!customerId) return null;

    const customersData = await this.loadData('crm/customers.json');
    const customers = customersData.data || [];
    
    return customers.find(customer => customer.id === customerId) || null;
  }

  /**
   * 根据ID获取客户订单
   */
  async getCustomerOrderById(orderId: string): Promise<any> {
    if (!orderId) return null;

    const ordersData = await this.loadData('mes/customer-orders.json');
    const orders = ordersData.orders || [];
    
    return orders.find(order => order.id === orderId) || null;
  }

  /**
   * 获取工作中心映射
   */
  getWorkCenterMapping(workstation: string): string | null {
    return this.workstationMapping.get(workstation) || null;
  }

  /**
   * 清除缓存
   */
  clearCache(filePath?: string): void {
    if (filePath) {
      this.cache.delete(filePath);
      this.cacheTimestamps.delete(filePath);
    } else {
      this.cache.clear();
      this.cacheTimestamps.clear();
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(filePath: string): boolean {
    if (!this.cache.has(filePath)) return false;
    
    const timestamp = this.cacheTimestamps.get(filePath);
    if (!timestamp) return false;
    
    return Date.now() - timestamp < this.CACHE_TTL;
  }

  /**
   * 提取数据数组
   */
  private extractDataArray(data: any): any[] {
    if (Array.isArray(data)) return data;
    if (data.data && Array.isArray(data.data)) return data.data;
    if (data.orders && Array.isArray(data.orders)) return data.orders;
    if (data.productionOrders && Array.isArray(data.productionOrders)) return data.productionOrders;
    return [];
  }

  /**
   * 获取数据数组的键名
   */
  private getDataArrayKey(data: any): string {
    if (Array.isArray(data)) return 'data';
    if (data.data && Array.isArray(data.data)) return 'data';
    if (data.orders && Array.isArray(data.orders)) return 'orders';
    if (data.productionOrders && Array.isArray(data.productionOrders)) return 'productionOrders';
    return 'data';
  }

  /**
   * 应用过滤器
   */
  private applyFilters(items: any[], filters: FilterOptions): any[] {
    return items.filter(item => {
      return Object.entries(filters).every(([key, value]) => {
        if (value === null || value === undefined || value === '') return true;
        
        const itemValue = this.getNestedValue(item, key);
        
        if (typeof value === 'string') {
          return String(itemValue).toLowerCase().includes(value.toLowerCase());
        }
        
        return itemValue === value;
      });
    });
  }

  /**
   * 应用排序
   */
  private applySorting(items: any[], sortBy: string, sortOrder: 'asc' | 'desc' = 'asc'): any[] {
    return items.sort((a, b) => {
      const aValue = this.getNestedValue(a, sortBy);
      const bValue = this.getNestedValue(b, sortBy);
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出单例实例
export const mockDataLoader = new MockDataLoader();
