/**
 * Mock 数据系统使用示例
 * 演示如何使用统一的数据加载接口
 */

import { dataServices } from '../utils/dataService';
import { handleError } from '../utils/errorHandler';
import { setApiConfig } from '../utils/api';

// 配置 API 使用 Mock 数据
setApiConfig({
  useMock: true,
  baseURL: '/mock',
});

/**
 * 示例：加载用户数据
 */
export async function loadUsersExample() {
  try {
    console.log('Loading users...');
    const response = await dataServices.common.getUsers();
    
    if (response.success) {
      console.log('Users loaded successfully:', response.data);
      return response.data;
    } else {
      console.error('Failed to load users:', response.message);
    }
  } catch (error) {
    handleError(error, { context: 'loadUsersExample' });
  }
}

/**
 * 示例：加载物料数据
 */
export async function loadMaterialsExample() {
  try {
    console.log('Loading materials...');
    const response = await dataServices.metadata.getMaterials();
    
    if (response.success) {
      console.log('Materials loaded successfully:', response.data);
      return response.data;
    } else {
      console.error('Failed to load materials:', response.message);
    }
  } catch (error) {
    handleError(error, { context: 'loadMaterialsExample' });
  }
}

/**
 * 示例：加载客户订单数据
 */
export async function loadCustomerOrdersExample() {
  try {
    console.log('Loading customers and orders...');
    
    // 并行加载客户和订单数据
    const [customersResponse, ordersResponse] = await Promise.all([
      dataServices.crm.getCustomers(),
      dataServices.crm.getOrders(),
    ]);
    
    if (customersResponse.success && ordersResponse.success) {
      console.log('Customers:', customersResponse.data);
      console.log('Orders:', ordersResponse.data);
      
      return {
        customers: customersResponse.data,
        orders: ordersResponse.data,
      };
    } else {
      console.error('Failed to load customer or order data');
    }
  } catch (error) {
    handleError(error, { context: 'loadCustomerOrdersExample' });
  }
}

/**
 * 示例：加载库存预警数据
 */
export async function loadLowStockExample() {
  try {
    console.log('Loading low stock items...');
    const response = await dataServices.inventory.getLowStockItems();
    
    if (response.success) {
      console.log('Low stock items:', response.data);
      return response.data;
    } else {
      console.error('Failed to load low stock items:', response.message);
    }
  } catch (error) {
    handleError(error, { context: 'loadLowStockExample' });
  }
}

/**
 * 示例：加载生产工单数据
 */
export async function loadWorkOrdersExample() {
  try {
    console.log('Loading work orders...');
    const response = await dataServices.mes.getWorkOrders({
      status: 'in_progress',
      priority: 'high',
    });
    
    if (response.success) {
      console.log('Work orders:', response.data);
      return response.data;
    } else {
      console.error('Failed to load work orders:', response.message);
    }
  } catch (error) {
    handleError(error, { context: 'loadWorkOrdersExample' });
  }
}

/**
 * 示例：加载供应商数据
 */
export async function loadSuppliersExample() {
  try {
    console.log('Loading suppliers...');
    const response = await dataServices.procurement.getSuppliers({
      category: '玻璃原片',
    });
    
    if (response.success) {
      console.log('Suppliers:', response.data);
      return response.data;
    } else {
      console.error('Failed to load suppliers:', response.message);
    }
  } catch (error) {
    handleError(error, { context: 'loadSuppliersExample' });
  }
}

/**
 * 示例：加载质检数据
 */
export async function loadInspectionsExample() {
  try {
    console.log('Loading inspections...');
    const response = await dataServices.quality.getInspections({
      status: 'passed',
    });
    
    if (response.success) {
      console.log('Inspections:', response.data);
      return response.data;
    } else {
      console.error('Failed to load inspections:', response.message);
    }
  } catch (error) {
    handleError(error, { context: 'loadInspectionsExample' });
  }
}

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  console.log('=== Mock Data System Usage Examples ===');
  
  await loadUsersExample();
  await loadMaterialsExample();
  await loadCustomerOrdersExample();
  await loadLowStockExample();
  await loadWorkOrdersExample();
  await loadSuppliersExample();
  await loadInspectionsExample();
  
  console.log('=== All examples completed ===');
}

// 如果直接运行此文件，执行所有示例
if (typeof window === 'undefined' && require.main === module) {
  runAllExamples().catch(console.error);
}