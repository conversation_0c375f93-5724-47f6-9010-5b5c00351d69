# Vue Flow 使用指南

## 基本设置

### 1. 安装依赖
```bash
pnpm add @vue-flow/core
# 可选的额外组件
pnpm add @vue-flow/background @vue-flow/controls @vue-flow/minimap
```

### 2. 基本导入
```typescript
import { VueFlow, useVueFlow, Panel, PanelPosition } from '@vue-flow/core';
import type { Node, Edge, Connection } from '@vue-flow/core';
```

### 3. 必需的 CSS
```vue
<style>
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';
</style>
```

## 基本用法

### 1. 简单示例
```vue
<template>
  <div class="h-96">
    <VueFlow
      v-model:nodes="nodes"
      v-model:edges="edges"
      fit-view-on-init
    >
      <Panel :position="PanelPosition.TopLeft">
        <div>节点数: {{ nodes.length }}</div>
      </Panel>
    </VueFlow>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { VueFlow, Panel, PanelPosition } from '@vue-flow/core';
import type { Node, Edge } from '@vue-flow/core';

const nodes = ref<Node[]>([
  {
    id: '1',
    type: 'input',
    position: { x: 100, y: 100 },
    data: { label: '开始' },
  },
  {
    id: '2',
    type: 'default',
    position: { x: 300, y: 100 },
    data: { label: '处理' },
  }
]);

const edges = ref<Edge[]>([
  {
    id: 'e1-2',
    source: '1',
    target: '2',
  }
]);
</script>
```

## 高级功能

### 1. 事件处理
```typescript
const { onConnect, onNodeClick, addEdges } = useVueFlow();

// 连接事件
onConnect((connection: Connection) => {
  const edge: Edge = {
    id: `edge_${Date.now()}`,
    source: connection.source!,
    target: connection.target!,
  };
  addEdges([edge]);
});

// 节点点击事件
onNodeClick((event) => {
  console.log('节点被点击:', event.node);
});
```

### 2. 自定义节点样式
```typescript
const createStyledNode = (id: string, label: string, type: 'process' | 'buffer') => ({
  id,
  type: 'default',
  position: { x: Math.random() * 400, y: Math.random() * 300 },
  data: { label },
  style: {
    backgroundColor: type === 'process' ? '#dbeafe' : '#dcfce7',
    border: `2px solid ${type === 'process' ? '#3b82f6' : '#22c55e'}`,
    borderRadius: '8px',
    padding: '10px',
    minWidth: '120px',
    textAlign: 'center'
  }
});
```

### 3. 配置选项
```vue
<VueFlow
  v-model:nodes="nodes"
  v-model:edges="edges"
  :nodes-draggable="true"
  :nodes-connectable="true"
  :elements-selectable="true"
  :zoom-on-scroll="true"
  :zoom-on-pinch="true"
  :pan-on-scroll="false"
  :pan-on-drag="true"
  fit-view-on-init
  :min-zoom="0.1"
  :max-zoom="4"
>
```

## 在 ProcessSegmentEditor 中的应用

### 1. 完整实现
```vue
<template>
  <div class="flex-1 relative">
    <VueFlow
      v-model:nodes="nodes"
      v-model:edges="edges"
      :nodes-draggable="!readonly"
      :nodes-connectable="!readonly"
      :elements-selectable="!readonly"
      :zoom-on-scroll="true"
      :zoom-on-pinch="true"
      :pan-on-scroll="false"
      :pan-on-drag="true"
      fit-view-on-init
      class="vue-flow-container"
    >
      <!-- 控制面板 -->
      <Panel :position="PanelPosition.TopRight" class="space-x-2">
        <Button size="sm" variant="outline" @click="deleteSelectedNode">
          <Trash2 class="h-4 w-4" />
        </Button>
      </Panel>
      
      <!-- 调试信息 -->
      <Panel :position="PanelPosition.BottomLeft" class="text-xs">
        <div>节点数: {{ nodes.length }}</div>
        <div>连接数: {{ edges.length }}</div>
      </Panel>
    </VueFlow>
  </div>
</template>
```

### 2. 业务逻辑集成
```typescript
// 从工序/缓冲区创建节点
const addNodeWithEntity = (entity: ProcessStep | WipBuffer, type: string) => {
  const newNode: Node = {
    id: `node_${Date.now()}`,
    type: 'default',
    position: { x: Math.random() * 400 + 100, y: Math.random() * 200 + 50 },
    data: {
      label: entity.name,
      type: type,
      entityId: entity.id,
    },
    style: {
      backgroundColor: type === 'ProcessStep' ? '#dbeafe' : '#dcfce7',
      border: `2px solid ${type === 'ProcessStep' ? '#3b82f6' : '#22c55e'}`,
      borderRadius: '8px',
      padding: '10px',
      minWidth: '120px',
      textAlign: 'center'
    }
  };
  
  nodes.value.push(newNode);
};

// 保存时转换为业务数据
const save = () => {
  const processSegment: ProcessSegment = {
    id: props.processSegment?.id || `seg_${Date.now()}`,
    name: localProcessSegment.value.name!,
    description: localProcessSegment.value.description || '',
    nodes: nodes.value.map(node => ({
      nodeId: node.id,
      type: node.data.type,
      entityId: node.data.entityId,
      position: node.position,
    })),
    edges: edges.value.map(edge => ({
      sourceNodeId: edge.source,
      targetNodeId: edge.target,
    })),
  };
  
  emit('save', processSegment);
};
```

## 样式定制

### 1. 基本节点样式
```css
.vue-flow__node-default {
  border-radius: 0.5rem;
  border: 2px solid #e5e7eb;
  background-color: white;
  padding: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.vue-flow__node-default.selected {
  border-color: hsl(var(--primary));
}

.vue-flow__node-default:hover {
  border-color: #d1d5db;
}
```

### 2. 边样式
```css
.vue-flow__edge-default {
  stroke: #6b7280;
  stroke-width: 2px;
}

.vue-flow__edge-default.selected {
  stroke: hsl(var(--primary));
  stroke-width: 3px;
}
```

### 3. 容器样式
```css
.vue-flow-container {
  background-color: #fafafa;
}
```

## 常见问题

### 1. 节点不显示
- 确保导入了必需的 CSS
- 检查节点数据格式是否正确
- 确保容器有明确的高度

### 2. 连接不工作
- 检查 `onConnect` 事件是否正确设置
- 确保 `nodes-connectable` 属性为 true
- 验证节点 ID 是否唯一

### 3. 样式问题
- 避免使用 TailwindCSS 的 `@apply` 语法
- 使用标准 CSS 属性
- 确保 Vue Flow 的 CSS 在组件 CSS 之前导入

## 最佳实践

1. **数据管理**: 使用响应式数据管理节点和边
2. **事件处理**: 合理使用 Vue Flow 提供的事件钩子
3. **样式一致性**: 保持节点样式的一致性
4. **性能优化**: 对于大量节点，考虑虚拟化
5. **用户体验**: 提供清晰的视觉反馈和操作提示
