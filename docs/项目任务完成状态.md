# 物料管理系统 - 项目任务状态

## 📋 **任务概览**

**项目状态**：🟠 **重构中**  
**更新时间**：2025年8月8日  
**核心变更**：项目正在从原有的“模板+变体”管理模式，重构为更灵活、更具扩展性的 **“分类 -> 物料”** 管理模式。此举旨在更好地适应玻璃深加工行业的复杂业务需求。

---

## 🔄 **重构任务规划**

### **第一阶段：清理与重构 (当前阶段)**

| 任务ID | 任务名称 | 状态 | 备注 |
| :--- | :--- | :--- | :--- |
| `CLEAN-01` | **清理旧物料管理代码** | ✅ **已完成** | 删除了所有相关的组件、视图、服务、Store和类型定义。 |
| `CLEAN-02` | **清理旧模拟数据** | ✅ **已完成** | 移除了 `material.json`, `materialTemplate.json` 等文件。 |
| `CLEAN-03` | **移除相关路由和引用** | ✅ **已完成** | 清理了 `router/index.ts` 和 `views/InventoryView.vue` 中的引用。 |
| `CLEAN-04` | **同步更新项目文档** | ✅ **已完成** | 本文档已更新，以反映重构状态。 |

### **第二阶段：新数据结构与核心服务开发**

| 任务ID | 任务名称 | 状态 | 预计成果 |
| :--- | :--- | :--- | :--- |
| `REBUILD-DS-01` | **定义新的数据结构** | ⚪️ **待开始** | 创建 `materialCategory.json` 和 `material.json` 的数据模型。 |
| `REBUILD-TS-01` | **创建新的TypeScript类型** | ⚪️ **待开始** | 在 `src/types/` 目录下创建新的 `material.ts` 类型定义文件。 |
| `REBUILD-SVC-01` | **开发新的数据服务** | ⚪️ **待开始** | 创建 `MaterialService` 用于获取和处理物料及分类数据。 |
| `REBUILD-PINIA-01`| **创建新的Pinia Store** | ⚪️ **待开始** | 创建 `useMaterialStore` 用于管理物料相关的应用状态。 |

### **第三阶段：核心UI组件开发**

| 任务ID | 任务名称 | 状态 | 关键组件 |
| :--- | :--- | :--- | :--- |
| `REBUILD-UI-01` | **物料分类管理界面** | ⚪️ **待开始** | `MaterialCategoryTree.vue`, `CategoryConfigDialog.vue` |
| `REBUILD-UI-02` | **物料列表与管理界面** | ⚪️ **待开始** | `MaterialDataTable.vue`, `MaterialEditor.vue` |
| `REBUILD-UI-03` | **物料详情视图** | ⚪️ **待开始** | `MaterialDetailView.vue` |

---

## 🗑️ **已废弃的原有任务**

以下是基于旧“模板+变体”模式的任务，由于架构重构，这些任务和相关交付物已被废弃。

| 原始任务名称 | 状态 | 废弃原因 |
| :--- | :--- | :--- |
| 物料分类变体配置继承机制设计 | ❌ **已废弃** | 新模式采用更直接的分类配置，不再需要复杂的继承逻辑。 |
| 变体属性数据类型和约束规则定义 | 🔄 **待重构** | 概念将被继承，但在新的分类配置中重新实现。 |
| 自动计算字段配置机制设计 | 🔄 **待重构** | 将作为新物料系统的高级功能重新设计。 |
| 物料编码规则与变体属性关联设计 | 🔄 **待重构** | SKU生成逻辑将基于新的分类和属性结构。 |
| 库存管理层级策略确定 | 🔄 **待重构** | 库存管理将直接与新的物料实体关联。 |
| 变体属性组合约束规则引擎设计 | ❌ **已废弃** | 约束将简化并直接在分类级别定义。 |
| 物料模板配置界面开发 | ❌ **已废弃** | 由新的“物料分类配置界面”取代。 |
| 变体生成和管理界面开发 | ❌ **已废弃** | 由新的“物料管理界面”取代。 |
| 约束验证和SKU生成引擎 | ❌ **已废弃** | 相关逻辑将整合到新的服务和工具类中。 |
| 库存汇总和下钻功能 | 🔄 **待重构** | 将基于新的物料数据结构重新实现。 |

## 📝 **总结**

项目正处于一个关键的重构阶段。清理工作已经完成，为新架构的实施奠定了坚实的基础。接下来的工作重心将是按照新的“分类 -> 物料”模式，自底向上地重建数据结构、服务和用户界面。这将使系统更加健壮和可维护，更好地服务于长期业务目标。