# 玻璃深加工_智能制造系统(MES)_总体设计方案

**文档版本**: 2.2
**创建日期**: 2025-0-15
**作者**: Gemini
**更新日志**: 
- 1.1 - 深化数据模型与车间执行流程。
- 2.0 - 采用"后台智能化，前台简单化"混合方案，集成IoT能力。
- 2.1 - 引入分段式制造与WIP管理模型，增加摆炉优化等深度行业场景。
- 2.2 - 新增智能异常处理与闭环管理章节，完善系统鲁棒性设计。

---

## 1. 概述

本文档旨在详细阐述 **玻璃深加工智能制造系统(MES)** 的总体设计与实现方案。该系统是专门针对玻璃深加工行业特点设计的完整制造执行系统，涵盖生产计划、执行控制、质量管理、设备管理、物料管理等全业务流程，旨在实现玻璃深加工企业的数字化转型和智能制造升级。

系统采用 **"后台智能化，前台简单化"** 的设计理念，通过IoT智能设备承担复杂的数据采集和校验任务，为用户提供极简的操作界面。同时，系统构建了完整的 **玻璃深加工数字孪生工厂模型**，能够管理包括 **切割、磨边、清洗、钢化、夹胶、中空合片、包装** 等全工艺流程，以及 **分段式工艺路径**、**在制品 (WIP) 缓冲**、**瓶颈工位优化** 等复杂生产场景。

### 1.1 系统定位与价值

**核心定位**：面向玻璃深加工行业的一体化智能制造执行系统，解决行业内 **"多品种小批量"**、**"工艺复杂"**、**"质量要求高"**、**"交期紧张"** 等核心挑战。

**核心价值**：
- **生产效率提升**：通过智能排产和资源优化，提升设备利用率和生产效率
- **质量管控强化**：建立全流程质量追溯体系，确保产品质量稳定性
- **成本控制优化**：通过材料利用率优化和精细化管理，降低生产成本
- **交期保障能力**：通过精准的生产计划和实时监控，提升交期达成率
- **管理水平提升**：通过数字化手段，提升车间管理的标准化和规范化水平

### 1.2 玻璃深加工行业特点分析

**工艺复杂性**：
- **多工艺路径**：涵盖切割、磨边、清洗、钢化、夹胶、中空合片、镀膜、弯钢等多种工艺
- **工艺参数严格**：温度、时间、压力等工艺参数对产品质量影响巨大
- **质量要求高**：建筑玻璃需满足国家标准和行业规范，质量追溯要求严格

**生产管理挑战**：
- **多品种小批量**：订单规格多样，批量相对较小，排产复杂度高
- **材料利用率**：原片成本占比高，材料利用率直接影响企业盈利能力
- **设备瓶颈**：钢化炉等关键设备投资大，产能利用率要求高
- **交期压力**：建筑项目工期紧张，对玻璃交期要求严格

### 1.3 系统建设目标与量化指标

**技术指标**：
- IoT设备识别准确率：≥99.5%（保证数据采集质量）
- 系统响应时间：≤3秒（保证用户体验）
- 数据同步延迟：≤1秒（保证实时性）
- 系统可用性：≥99.9%（保证生产连续性）

**用户体验指标**：
- 界面操作步骤：≤3步完成核心操作（极简化操作）
- 用户培训时间：≤4小时（快速上手）
- 操作错误率：≤1%（降低人为失误）
- 用户满意度：≥4.5/5.0（用户认可度）

**业务价值指标**：
- 材料利用率提升：≥5%（通过优化排版算法）
- 设备综合效率(OEE)提升：≥10%（通过精细化管理）
- 生产计划准确率：≥95%（通过智能排产）
- 质量一次合格率：≥98%（通过过程控制）
- 交期达成率：≥95%（通过精准计划和监控）
- 库存周转率提升：≥20%（通过精细化库存管理）

### 1.4 系统覆盖范围

**产品类型**：
- **单片玻璃**：钢化玻璃、夹胶玻璃、镀膜玻璃等
- **复合玻璃**：中空玻璃、中空夹胶玻璃、三玻两腔等
- **异形玻璃**：弯钢玻璃、异形切割玻璃等

**工艺流程**：
- **冷加工**：切割→磨边→清洗→检验
- **热加工**：钢化→弯钢→退火
- **复合加工**：夹胶→中空合片→充气→密封
- **表面处理**：镀膜→丝印→喷砂

**管理范围**：
- **生产管理**：计划、排产、执行、监控
- **质量管理**：检验、追溯、不合格品处理
- **设备管理**：维护、保养、故障处理
- **物料管理**：采购、库存、配送、消耗
- **人员管理**：排班、技能、绩效
- **成本管理**：核算、分析、控制

## 2. 系统总体架构设计

### 2.1 系统架构概述

玻璃深加工智能制造系统采用分层架构设计，从下至上分为：设备层、数据层、服务层、应用层、展现层五个层次，实现了从底层设备到上层应用的全面集成。

```
┌─────────────────────────────────────────────────────────────┐
│                        展现层 (Presentation Layer)           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 计划员工作台 │ │ 车间执行终端 │ │ 质量管理界面 │ │ 移动看板 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        应用层 (Application Layer)            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 生产管理模块 │ │ 质量管理模块 │ │ 设备管理模块 │ │ 物料管理 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 人员管理模块 │ │ 成本管理模块 │ │ 报表分析模块 │ │ 系统管理 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        服务层 (Service Layer)                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 排产优化引擎 │ │ 质量控制引擎 │ │ IoT集成服务 │ │ 数据分析 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 工作流引擎   │ │ 规则引擎     │ │ 消息服务     │ │ 集成服务 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        数据层 (Data Layer)                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 生产数据库   │ │ 质量数据库   │ │ 设备数据库   │ │ 实时数据 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 历史数据仓库 │ │ 文档管理     │ │ 配置数据     │ │ 缓存服务 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        设备层 (Device Layer)                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 切割设备     │ │ 钢化设备     │ │ 合片设备     │ │ 检测设备 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ IoT传感器    │ │ 智能料架     │ │ 扫码设备     │ │ 视觉系统 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心设计理念

**智能化与简单化并重**：
- **后台智能化**：采用先进的算法和AI技术，实现智能排产、质量预测、设备预维护等功能
- **前台简单化**：为用户提供简洁直观的操作界面，降低使用门槛和学习成本
- **IoT自动化**：通过IoT设备自动采集数据，减少人工录入，提高数据准确性

**数字孪生驱动**：
- **工厂数字孪生**：构建完整的数字化工厂模型，实现物理工厂与数字工厂的实时同步
- **产品数字孪生**：为每个产品建立数字身份，实现全生命周期追溯
- **设备数字孪生**：实时监控设备状态，预测维护需求，优化设备利用率

**柔性与标准化平衡**：
- **标准化流程**：建立标准化的作业流程和质量标准，确保生产一致性
- **柔性配置**：支持不同规模企业的个性化需求，可灵活配置功能模块
- **快速响应**：支持订单变更、紧急插单等业务场景的快速响应

## 3. 业务流程

### 3.1 车间执行流程（分段式IoT自动化）

原有的线性流程已无法描述真实的、带缓冲的生产过程。现升级为更贴近现实的分段式流程：

1.  **冷工段（切割、磨边、清洗）**
    *   **自动赋码**：切割机配合lens设备，为每片玻璃自动分配并记录 `唯一小片ID` 和 `套装ID`。
    *   **下线落架**：冷工段连线生产结束后，在下线口，工人将玻璃放置在中转架上。
    *   **WIP绑定**：IoT设备（或工人的PDA）自动/手动扫描玻璃ID和中转架ID（如 `RACK-007`），完成 **“玻璃”与“货架”的绑定**。系统此时精确知道，`RACK-007` 上装载了哪些玻璃。
    *   **状态更新**：该批玻璃状态更新为“冷工段完工，待入钢化缓冲”，并被运送至指定缓冲区域。

2.  **钢化工段（瓶颈与二次优化）**
    *   **触发摆炉优化**：当钢化炉即将空闲时，系统或班组长触发 **“摆炉优化”** 任务。
    *   **智能计算**：系统从“钢化缓冲池”中，根据优先级、交期、玻璃厚度、尺寸等约束，自动选取最优的一炉玻璃组合，并生成 **“摆炉图”** 和唯一的 **“钢化炉次号 (TemperingBatchID)”**。
    *   **按图索骥**：上片工位的终端上会显示清晰的摆炉图，并明确指示：“请从 `RACK-007` 取 `3` 片，从 `RACK-012` 取 `8` 片...”。
    *   **上炉跟踪**：工人按图上片，IoT设备记录每一片玻璃已进入钢化炉，并与 `TemperingBatchID` 关联。
    *   **下炉落架**：钢化完成后，玻璃被放置在新的中转架上，并再次进行绑定，状态更新为“钢化完工，待入合片缓冲”。

3.  **合片工段（精准校验）**
    *   **按需送料**：系统根据合片工位的生产节拍，通知AGV或工人将装载着匹配组件的中转架（例如，装外片的 `RACK-015` 和装内片的 `RACK-021`）运送至工位旁。
    *   **自动校验**：工人取片上料，IoT设备自动进行 `套装ID` 的强制校验。对于弯钢等特殊工艺，系统还会 **双重校验 `钢化炉次号`** 是否一致。
    *   **异常报警**：不匹配时设备自动报警并暂停操作。

## 4. 系统功能模块设计

### 4.1 生产管理模块

#### 4.1.1 生产计划管理
**功能描述**：基于销售订单和库存情况，制定中长期生产计划和短期排产计划。

**核心功能**：
- **主生产计划(MPS)**：根据销售预测和订单需求，制定月度、周度生产计划
- **物料需求计划(MRP)**：基于BOM和库存，计算原材料和辅料需求
- **产能平衡分析**：分析各工序产能，识别瓶颈工序，优化资源配置
- **交期承诺**：基于产能和排产情况，给出准确的交期承诺

#### 4.1.2 智能排产优化
**功能描述**：采用先进的优化算法，实现多约束条件下的最优排产。

**核心功能**：
- **多目标优化**：同时考虑交期、成本、质量等多个目标
- **动态排产**：支持订单变更、紧急插单等动态调整
- **材料利用率优化**：通过智能排版算法，最大化原片利用率
- **设备负荷均衡**：合理分配各设备负荷，避免瓶颈工序过载

#### 4.1.3 生产执行控制
**功能描述**：实时监控生产执行过程，确保按计划完成生产任务。

**核心功能**：
- **工单管理**：工单下达、执行、完工全流程管理
- **进度跟踪**：实时跟踪各工序生产进度，及时发现异常
- **资源调度**：动态调度人员、设备、物料等生产资源
- **异常处理**：快速响应生产异常，最小化对生产的影响

### 4.2 质量管理模块

#### 4.2.1 质量计划与标准
**功能描述**：建立完善的质量管理体系，确保产品质量稳定。

**核心功能**：
- **质量标准管理**：维护产品质量标准和检验规范
- **检验计划制定**：根据产品特点制定检验计划和抽检方案
- **质量控制点设置**：在关键工序设置质量控制点
- **质量目标管理**：设定质量目标并跟踪达成情况

#### 4.2.2 过程质量控制
**功能描述**：在生产过程中实施质量控制，预防质量问题发生。

**核心功能**：
- **首件检验**：每批次首件强制检验，确保工艺参数正确
- **过程监控**：实时监控关键工艺参数，及时预警异常
- **SPC统计过程控制**：采用统计方法监控过程稳定性
- **质量预警**：基于历史数据和趋势分析，预警潜在质量风险

#### 4.2.3 质量追溯与分析
**功能描述**：建立完整的质量追溯体系，支持质量问题快速定位。

**核心功能**：
- **全程追溯**：从原材料到成品的全程质量追溯
- **批次管理**：精确的批次管理，支持正向和反向追溯
- **质量分析**：质量数据统计分析，识别质量改进机会
- **不合格品处理**：不合格品的隔离、处置和改进措施跟踪

### 4.3 设备管理模块

#### 4.3.1 设备档案管理
**功能描述**：建立完整的设备档案，支持设备全生命周期管理。

**核心功能**：
- **设备台账**：设备基本信息、技术参数、维护记录等
- **设备分类**：按功能、重要性等维度对设备进行分类管理
- **备件管理**：关键备件库存管理和采购计划
- **设备评估**：设备性能评估和更新改造计划

#### 4.3.2 设备维护管理
**功能描述**：实施预防性维护，确保设备稳定运行。

**核心功能**：
- **维护计划**：制定设备维护计划和保养周期
- **维护执行**：维护任务下达、执行、验收全流程管理
- **故障管理**：故障报告、分析、处理和改进措施跟踪
- **维护分析**：维护成本分析和维护效果评估

#### 4.3.3 设备监控与诊断
**功能描述**：实时监控设备运行状态，实现预测性维护。

**核心功能**：
- **实时监控**：关键设备参数实时监控和报警
- **设备OEE**：设备综合效率计算和分析
- **预测维护**：基于设备状态数据预测维护需求
- **远程诊断**：支持设备远程诊断和技术支持

### 4.4 物料管理模块

#### 4.4.1 库存管理
**功能描述**：实现原材料、半成品、成品的精细化库存管理。

**核心功能**：
- **库存监控**：实时监控各类物料库存水平
- **安全库存**：设置安全库存和补货点，自动预警
- **库存盘点**：支持循环盘点和全面盘点
- **库存分析**：库存周转率、呆滞料分析等

#### 4.4.2 采购管理
**功能描述**：基于生产需求和库存情况，优化采购计划。

**核心功能**：
- **采购计划**：基于MRP计算结果生成采购计划
- **供应商管理**：供应商档案、评估、准入管理
- **采购执行**：采购订单、到货验收、入库管理
- **采购分析**：采购成本、交期、质量分析

#### 4.4.3 仓储管理
**功能描述**：优化仓储作业流程，提高仓储效率。

**核心功能**：
- **入库管理**：原材料、外协件入库管理
- **出库管理**：生产领料、成品出库管理
- **库位管理**：货位分配、库存定位管理
- **盘点管理**：库存盘点、差异处理

### 4.5 人员管理模块

#### 4.5.1 组织架构管理
**功能描述**：建立完整的组织架构体系，支持人员管理。

**核心功能**：
- **组织架构**：部门、岗位、职责定义和维护
- **人员档案**：员工基本信息、技能、资质管理
- **权限管理**：基于角色的权限控制和数据安全
- **班组管理**：班组设置、班长指定、人员分配

#### 4.5.2 排班管理
**功能描述**：根据生产需求和人员情况，合理安排人员排班。

**核心功能**：
- **排班计划**：基于生产计划制定人员排班计划
- **技能匹配**：根据工序要求匹配具备相应技能的人员
- **考勤管理**：员工考勤记录、异常处理
- **加班管理**：加班申请、审批、统计

#### 4.5.3 绩效管理
**功能描述**：建立科学的绩效评估体系，激励员工积极性。

**核心功能**：
- **绩效指标**：设定个人和团队绩效指标
- **绩效考核**：定期绩效考核和评估
- **技能培训**：技能培训计划和效果评估
- **激励机制**：基于绩效的激励和奖惩机制

### 4.6 成本管理模块

#### 4.6.1 成本核算
**功能描述**：精确核算产品成本，支持成本控制决策。

**核心功能**：
- **标准成本**：制定产品标准成本和成本结构
- **实际成本**：实时采集实际成本数据
- **成本差异分析**：分析标准成本与实际成本差异
- **成本分摊**：合理分摊间接费用

#### 4.6.2 成本控制
**功能描述**：通过成本监控和分析，实现成本有效控制。

**核心功能**：
- **成本预警**：设置成本预警阈值，及时预警异常
- **成本分析**：多维度成本分析和趋势分析
- **成本优化**：识别成本优化机会，制定改进措施
- **成本报告**：定期成本报告和管理驾驶舱

### 4.7 数据分析与报表模块

#### 4.7.1 数据采集与处理
**功能描述**：全面采集生产数据，为分析决策提供数据基础。

**核心功能**：
- **数据采集**：自动采集设备、质量、生产等各类数据
- **数据清洗**：数据验证、清洗、标准化处理
- **数据存储**：建立数据仓库，支持历史数据查询
- **数据安全**：数据备份、恢复、权限控制

#### 4.7.2 统计分析
**功能描述**：基于历史数据进行统计分析，发现规律和趋势。

**核心功能**：
- **生产分析**：产量、效率、利用率等生产指标分析
- **质量分析**：合格率、缺陷率、质量趋势分析
- **设备分析**：设备效率、故障率、维护成本分析
- **成本分析**：成本构成、变化趋势、对比分析

#### 4.7.3 报表管理
**功能描述**：提供丰富的报表功能，满足不同层级管理需求。

**核心功能**：
- **标准报表**：提供常用的标准报表模板
- **自定义报表**：支持用户自定义报表格式和内容
- **实时报表**：实时数据报表和看板展示
- **移动报表**：支持移动端报表查看和推送

### 4.8 系统集成模块

#### 4.8.1 ERP集成
**功能描述**：与企业ERP系统无缝集成，实现数据共享。

**核心功能**：
- **订单集成**：自动接收ERP系统的销售订单
- **库存同步**：与ERP库存系统实时同步
- **财务集成**：成本数据与财务系统集成
- **主数据同步**：客户、供应商、物料等主数据同步

#### 4.8.2 第三方系统集成
**功能描述**：支持与各类第三方系统的集成。

**核心功能**：
- **WMS集成**：与仓储管理系统集成
- **PLM集成**：与产品生命周期管理系统集成
- **CRM集成**：与客户关系管理系统集成
- **BI集成**：与商业智能系统集成

## 5. 功能模块深度设计

为了实现“后台智能化，前台简单化”的目标，我们将功能模块划分为 **“用户交互层”** 和 **“后台服务层”** 两大部分。

### 5.1 用户交互层模块 (User-Facing Modules)

用户交互层遵循极简和角色聚焦原则，为不同用户提供其完成核心任务所需的最少必要界面。

#### 5.1.1 计划员工作台模块 (Planner's Workbench)

*   **目标**：为生产计划员提供一个高效、直观的宏观计划制定工具。
*   **核心组件**:
    *   **待办订单池 (Order Pool)**:
        *   **功能**: 以卡片形式展示所有状态为“已审核，待排产”的客户订单。
        *   **UI设计**: 强调状态可视化（紧急/正常/宽松）、核心信息（客户、交期、规格、数量）。提供一个全局的“全选加入”和单个订单的“加入排产”按钮。提供基础的搜索和按交期/客户过滤功能。
    *   **排产队列 (Scheduling Queue)**:
        *   **功能**: 一个临时的暂存区，用于存放计划员勾选的、准备进行一次“智能排产”计算的订单集合。
        *   **UI设计**: 以小标签或头像形式展示已选中的订单号，并实时显示订单总数和玻璃总片数。
    *   **主控操作面板 (Master Control Panel)**:
        *   **功能**: 这是计划的核心触发器。
        *   **UI设计**: 包含一个非常醒目的 **“一键智能排产”** 按钮。点击后，系统接管所有复杂计算。按钮下方会有一个进度条和状态文本（例如：“步骤1/3: 正在进行BOM分解...”、“步骤2/3: 正在计算切割优化方案...”）。
    *   **生产计划看板 (Production Kanban)**:
        *   **功能**: 可视化展示未来一段时间内（例如7天），关键瓶颈资源（如钢化炉、夹胶线）的产能负载和已安排的生产任务。
        *   **UI设计**: Y轴为瓶颈资源，X轴为时间（可切换日/周视图）。任务块以“生产批次”为单位显示，其长度代表预计占用时间。提供有限的拖拽微调功能，并实时反馈对交期的影响。

#### 5.1.2 车间执行终端模块 (Shop Floor Terminal)

*   **目标**：为一线工人和班组长提供指导操作、反馈状态和上报异常的极简终端界面。
*   **核心组件 (根据工位不同而变化)**:
    *   **摆炉指导终端 (Tempering Loading Guide)**:
        *   **功能**: 在钢化炉上片工位，清晰地向工人展示当前“钢化炉次”需要从哪些中转架上取哪些玻璃，以及如何在炉内摆放。
        *   **UI设计**: 左侧为列表，指示“从RACK-007取3片”，右侧为可视化的摆炉图。工人每取一片，IoT设备自动识别，列表对应项自动打勾，提供清晰的进度反馈。
    *   **智能校验终端 (Smart Validation Terminal)**:
        *   **功能**: 在合片等需要精确配对的工位，提供自动化的防错校验。
        *   **UI设计**: 如9.2节所述，采用全屏颜色、巨大图标和声音进行极限的视觉反馈，与设备PLC联动实现物理防错。
    *   **WIP管理终端 (WIP Management Terminal)**:
        *   **功能**: 在各工段的下线口，用于执行“玻璃”与“中转架”的绑定（落架）和解绑（上料）。
        *   **UI设计**: 界面极其简单，只有“绑定到货架”和“从货架取料”两个大按钮。流程由IoT设备或PDA扫码驱动，界面仅用于显示结果（“绑定成功：35片玻璃已存入RACK-007”）和处理异常。

#### 5.1.3 移动监控驾驶舱 (Mobile Dashboard)

*   **目标**：为管理层（车间主任、生产经理）提供在移动端随时随地掌握生产全局的能力。
*   **核心组件**:
    *   **全局KPI概览 (Overall KPI View)**:
        *   **功能**: 展示订单完成率、设备综合效率(OEE)、在制品(WIP)总库存、异常事件数等核心指标。
    *   **瓶颈资源监控 (Bottleneck Monitor)**:
        *   **功能**: 实时显示瓶颈设备（钢化炉）的当前状态、负载、等待队列和预计产出。
    *   **WIP缓冲池看板 (WIP Buffer Kanban)**:
        *   **功能**: 可视化展示各工段之间缓冲区的实时库存状态（料架数量、玻璃片数），并用颜色预警（绿色-健康，黄色-偏低，红色-即将断料）。
    *   **异常处理中心 (Exception Center)**:
        *   **功能**: 集中推送和管理所有生产异常事件（破损、设备故障、缺料），并允许管理者在线指派和跟踪处理进度。

### 5.2 后台服务层模块 (Backend Service Modules)

后台服务层是实现“智能化”的核心，用户对此无感知，但它们是系统的大脑和中枢神经。

#### 5.2.1 订单与BOM解析服务 (Order & BOM Parsing Service)

*   **职责**:
    *   从ERP或订单系统接收客户订单。
    *   根据产品库中的BOM定义，自动将复合产品订单（如中空玻璃）“分解”为一系列可独立生产的“组件任务”。
    *   为每个任务附加所有必要的工艺参数和生产要求。

#### 5.2.2 智能排产引擎 (Intelligent Scheduling Engine)

*   **职责**:
    *   接收计划员选择的订单（或组件任务）队列。
    *   **宏观优化 (Macro-Optimization)**: 核心算法，综合考虑交期、客户优先级、物料库存、设备产能等约束，以“综合成本最低”或“客户满意度最高”为目标，生成初始的“生产批次”建议和时间规划。
    *   **二次优化 (Micro-Optimization)**:
        *   **切割优化 (Nesting Service)**: 与专业排版软件对接，计算每个“生产批次”的最佳切割方案，目标是 **材料利用率最大化**。
        *   **摆炉优化 (Tempering Loading Service)**: 在钢化前，从WIP缓冲池中动态选择玻璃，计算单炉 **面积利用率最大化** 的摆放方案。

#### 5.2.3 车间数字孪生服务 (Digital Twin Service)

*   **职责**: 这是系统的核心状态管理器，负责构建和维护车间的实时数字镜像。
    *   **身份管理 (Identity Management)**: 创建并管理每一个 `Production Batch`, `Piece ID`, `Kit ID`, `Tempering Batch ID` 的生命周期。
    *   **位置跟踪 (Location Tracking)**: 实时记录每一片玻璃 (`Piece ID`) 和每一个中转架 (`Rack ID`) 的物理位置和逻辑状态（例如：`RACK-007` 位于 `钢化前缓冲A区`，状态为 `待钢化`）。
    *   **状态机引擎 (State Machine Engine)**: 管理每个生产对象的标准状态流转（例如：`待生产` -> `切割中` -> `在货架上` -> `钢化中` -> `已完工`），并处理异常状态。

#### 5.2.4 IoT集成与协调服务 (IoT Integration & Orchestration Service)

*   **职责**: 作为MES与物理世界之间的桥梁。
    *   **设备适配**: 提供与不同IoT设备（lens、PLC、RFID读取器、智能料架）通信的标准化接口。
    *   **任务分发**: 将后台生成的指令（如切割清单、摆炉图）准确下发到对应的车间终端或设备。
    *   **数据采集与解析**: 接收来自IoT设备的原始数据（如图像、ID、传感器读数），解析并转化为MES可理解的结构化事件（如“`Piece-001` 已进入钢化炉”）。

## 6. 界面原型深度设计 (Usability-Focused)

#### 6.1 设计哲学：面向最普通的用户，提供最可靠的工具

考虑到车间环境的复杂性和用户背景的多样性，我们的UI/UX设计必须遵循以下原则：

1.  **零学习成本**：界面元素应符合用户在消费级应用（如微信、抖音）中已形成的直觉，避免使用专业术语和复杂图表。
2.  **视觉引导优先**：大量使用颜色、图标、大字体和动画反馈，让用户“看”一眼就知道状态，而不是“读”一段文字。
3.  **防错大于纠错**：在用户可能犯错的地方，通过禁用按钮、强制校验、醒目提示等方式提前阻止，而不是等错误发生后再让用户处理。
4.  **任务聚焦**：每个界面只服务于一个核心任务，避免信息过载。
5.  **移动优先与设备适配**：界面设计需同时考虑PC端的大屏看板和车间内手持终端（PDA、平板）或工位触摸屏的小屏幕操作。

## 7. 分段式智能制造模型深度设计

### 7.1 数字与物理的孪生：在制品(WIP)与中转架管理

玻璃深加工的本质是分段式的，各工段之间通过中转架（料架）进行缓冲和解耦。本系统通过将 **“中转架”** 数字化，构建了车间物流的数字孪生。

- **核心实体**：`中转架 (WIP Rack)` 在MES中被视为一个核心管理对象，拥有唯一的ID、位置、状态和内容清单。
- **数据绑定**：通过IoT设备（固定式扫描器或移动终端），实现每一片玻璃的 `Piece ID` 与其所在的 `Rack ID` 的动态绑定和解绑。
- **两种模式**：
    - **传统模式**：工人在落架时，用PDA依次扫描架子二维码和玻璃二维码完成绑定。
    - **智能料架模式 (IoT)**：料架本身集成RFID/BLE等技术，自动感知架上玻璃的进出，并实时上报MES，实现无人化WIP管理。本方案以后者为最终目标。

### 7.2 瓶颈工段的深度优化：钢化摆炉

“一键智能排产”解决的是“哪些订单应该在什么时间窗口生产”的宏观问题。而 **“摆炉优化”** 解决的是“在当前时间点，如何将待钢化的玻璃最高效地组合进一炉”的微观执行问题。

- **定位**：一个嵌入在钢化工段前的 **二次动态优化引擎**。
- **输入**：钢化前缓冲池中所有状态为“待钢化”的玻璃列表。
- **优化目标**：在满足交期和工艺约束（如厚度、镀膜类型）的前提下，实现单炉 **面积利用率最大化**。
- **输出**：一个包含最佳玻璃组合的 **“钢化炉次 (Tempering Batch)”**，附带一张可视化的 **“摆炉指导图”**，并生成唯一的 `TemperingBatchID`。
- **价值**：不仅提升了设备产能，其输出的 `TemperingBatchID` 更是弯钢弧度匹配、质量问题追溯到炉的重要依据。

## 8. 智能异常处理与闭环管理

本章节详细阐述系统如何应对车间常见异常，实现快速响应、智能决策和流程闭环，确保生产的连续性和鲁棒性。

### 8.1 设计原则

- **主动预警**：通过IoT数据和算法，提前预判风险（如物料即将耗尽），将异常处理从事后补救变为事前预防。
- **简化上报**：为一线工人提供最简单的异常上报方式（如工位终端一键上报、IoT自动检测）。
- **智能决策支持**：系统自动分析异常影响，并为管理者提供量化的解决方案选项。
- **流程闭环**：确保每一个异常事件，从发生、响应、处理到验证，都有一个完整的数字化记录和状态跟踪。

### 8.2 核心异常场景处理流程

#### **场景一：玻璃破损 / 质量不合格**

这是最高频的异常，处理效率直接影响交付。

1.  **检测与上报**：
    *   **IoT自动检测**：在关键质检工位，AI视觉设备（如lens）自动识别出瑕疵或破损，并根据玻璃ID自动上报异常。
    - **人工一键上报**：任何工位的工人发现破损，都可以在最近的终端上，点击“破损上报”按钮。如果当前工位有IoT设备，系统会自动识别当前玻璃ID；否则，工人可用PDA扫描玻璃二维码完成上报。

2.  **系统后台响应（用户无感知）**：
    *   **状态标记**：系统立即将该 `Piece ID` 的状态标记为“已报废”，并记录原因、工位、时间。
    *   **创建补片任务**：系统自动复制该玻璃的所有信息（尺寸、工艺、归属的`Kit ID`等），生成一个全新的 **“补片任务 (Remake Task)”**。
    *   **优先级提升**：该“补片任务”被自动赋予最高优先级（`Priority: URGENT`）。
    *   **影响分析**：系统自动分析该补片对成品交期的影响，如果可能导致延期，则自动向计划员和跟单员发送预警。

3.  **用户界面呈现与决策**：
    *   **计划员工作台**：在“待处理订单”区域，出现一个醒目的红色卡片，标记为“紧急补片任务”。
    *   **智能插入**：计划员在下一次执行“一键智能排产”时，算法会强制将这个高优先级的补片任务插入到最优的生产批次中（例如，与相同材质和厚度的玻璃合并）。
    *   **班组长移动看板**：“异常”列表中出现该条记录，并实时跟踪补片任务的进度，直至其完成。

#### **场景二：关键设备故障**

1.  **检测与上报**：
    *   **IoT自动检测**：通过连接设备PLC，系统实时监控设备状态。一旦接收到故障代码或心跳停止，立即触发“设备故障”流程。
    *   **人工上报**：班组长在移动看板上，选择对应设备，点击“故障上报”，并可选择故障类型（如“机械故障”、“电气故障”）。

2.  **系统后台响应**：
    *   **冻结计划**：系统立即“冻结”该设备上所有已规划但未开始的“生产批次”。
    *   **全局重计算**：系统以该设备不可用为约束，重新计算所有受影响订单的预计完工时间 (ETA)。
    *   **生成报告**：快速生成一份“故障影响报告”，包含受影响的订单列表、预计延误时间等。

3.  **用户界面呈现与决策**：
    *   **计划员工作台**：生产计划看板上，故障设备的时间轴变为灰色不可用状态。所有被冻结的批次变为红色闪烁状态。系统弹出通知：“钢化炉#1发生故障，预计停机2小时。15个订单交期可能受影响。[查看影响报告并重排]”。
    *   **一键重排**：计划员在修复时间预估输入后，可以点击“一键应急重排”，系统会自动寻找替代路径（如使用备用设备）或按最新优先级重新计算一个可行的计划。

#### **场景三：物料短缺**

1.  **检测与预警**：
    *   **实时库存监控**：系统对接WMS或管理原片/辅材库存，根据当前生产计划实时扣减预期用量。
    *   **主动预警**：当系统预测到某规格原片将在未来X小时内（可配置，如4小时）低于安全库存时，自动触发“物料短缺预警”。

2.  **系统后台响应**：
    *   **风险标记**：将所有依赖该物料且计划在预警时间窗口内生产的“生产批次”标记为“物料风险”。
    *   **方案建议**：系统可根据预设规则，自动寻找替代物料（如用更大规格原片替代），并计算其成本影响。

3.  **用户界面呈现与决策**：
    *   **计划员工作台**：看板上，有风险的批次块边缘变为黄色。系统通知：“5mm Low-E原片预计在4小时后用尽，影响3个生产批次。[查看解决方案]”。
    *   **决策支持**：点击通知后，系统弹窗提供选项：
        *   **选项A (物料替代)**：“使用 3660x2440 规格替代，成本增加 5%，不影响交期。”
        *   **选项B (调整计划)**：“暂停这3个批次，优先生产其他批次。预计影响 SO-008 订单延期1天。”
        *   **选项C (手动处理)**：“通知采购紧急补料。”
    计划员可以根据商业利益（如客户重要性、成本容忍度）快速做出决策。

通过以上设计，系统不仅能执行最优计划，更能在一个动态、充满不确定性的真实车间环境中，成为管理者应对异常、降低损失、保障交付的智能副驾。
