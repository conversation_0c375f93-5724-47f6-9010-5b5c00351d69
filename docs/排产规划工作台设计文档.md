# 排产规划工作台设计文档

**文档版本**: V1.0  
**创建日期**: 2025-01-18  
**设计目标**: 基于现有生产工单和主数据管理系统，设计智能排产规划工作台

## 📋 设计概述

### 核心理念
**"智能预排产 + 异步切割优化 + 最终确认"**

基于现有的生产工单创建系统和主数据管理系统，设计一个支持第三方切割优化系统集成的智能排产工作台。

### 设计目标
- **业务目标**: 排产时间缩短80%，设备利用率提升10%，交期达成率95%
- **用户体验**: 流程清晰简单，操作直观高效
- **技术目标**: 与现有系统无缝集成，支持第三方系统异步协作

## 🔄 业务流程设计

### 整体流程
```
生产工单(批次) → 预排产 → 切割数据导出 → 第三方优化 → 结果导入 → 最终排产确认
```

### 三个核心阶段

#### 阶段1: 预排产阶段
**输入**: 已优化的生产批次  
**处理**: 基于标准工时和设备产能进行初步排产  
**输出**: 预排产方案和甘特图  
**用户操作**: 确认预排产方案，导出切割数据

#### 阶段2: 切割优化阶段  
**输入**: 切割优化数据包  
**处理**: 第三方系统进行切割优化  
**输出**: 优化后的切割方案和时间估算  
**用户操作**: 导入第三方优化结果

#### 阶段3: 最终确认阶段
**输入**: 第三方切割优化结果  
**处理**: 基于实际切割数据更新排产计划  
**输出**: 最终排产方案  
**用户操作**: 确认最终方案，下发生产执行

## 🎨 界面设计规范

### 整体布局
采用三栏式布局：
- **左侧**: 待排产批次池和控制面板
- **中间**: 主要工作区域（甘特图/状态展示）
- **右侧**: 详情面板和操作按钮

### 阶段1界面: 预排产
```
┌─────────────────────────────────────────────────────────────┐
│  📋 智能预排产                                               │
├─────────────────────────────────────────────────────────────┤
│  ✅ 已完成批次优化 (12个批次)                                │
│  🎯 预排产结果 (基于标准工时估算)                            │
│  📊 预计指标: 工期5.5天, 设备利用率78%, 交期达成率94%        │
│  📅 预排产甘特图                                            │
│  🚀 [导出切割数据] [确认预排产方案]                          │
└─────────────────────────────────────────────────────────────┘
```

### 阶段2界面: 切割优化等待
```
┌─────────────────────────────────────────────────────────────┐
│  ⏳ 切割优化进行中...                                        │
├─────────────────────────────────────────────────────────────┤
│  📤 已导出数据: cutting_plan_20240115_1430.xlsx            │
│  📥 等待导入结果                                            │
│  💡 操作提示: 发送给第三方系统 → 优化完成 → 导入结果        │
│  📁 [选择结果文件导入]                                      │
└─────────────────────────────────────────────────────────────┘
```

### 阶段3界面: 最终确认
```
┌─────────────────────────────────────────────────────────────┐
│  ✅ 切割优化完成 - 最终排产确认                              │
├─────────────────────────────────────────────────────────────┤
│  📊 优化结果对比: 原片-3张✅, 利用率+4%✅, 工期-0.4天✅      │
│  📅 更新后的排产甘特图                                      │
│  🎯 最终指标: 交期达成率96%, 原片利用率89%, 总工期5.1天     │
│  🚀 [确认最终排产] [重新优化] [导出生产计划]                 │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 技术架构设计

### 组件架构
```
ProductionSchedulingWorkbench (主工作台)
├── SchedulingPhaseManager (阶段管理器)
├── PreSchedulingPanel (预排产面板)
│   ├── BatchPoolDisplay (批次池展示)
│   ├── PreSchedulingEngine (预排产引擎)
│   └── GanttChartView (甘特图视图)
├── CuttingOptimizationPanel (切割优化面板)
│   ├── DataExportManager (数据导出管理)
│   ├── ImportStatusTracker (导入状态跟踪)
│   └── ResultImportManager (结果导入管理)
├── FinalConfirmationPanel (最终确认面板)
│   ├── ComparisonAnalysis (对比分析)
│   ├── UpdatedGanttChart (更新甘特图)
│   └── FinalScheduleConfirm (最终确认)
└── SharedComponents (共享组件)
    ├── ProgressTracker (进度跟踪)
    ├── StatusIndicator (状态指示)
    └── ActionButtons (操作按钮)
```

### 核心服务类
```typescript
// 预排产服务
class PreSchedulingService {
  async generatePreSchedule(batches: OptimizedBatch[]): Promise<PreScheduleResult>
  async calculateEstimatedMetrics(batches: OptimizedBatch[]): Promise<EstimatedMetrics>
}

// 切割数据导出服务
class CuttingDataExportService {
  async exportCuttingData(batches: OptimizedBatch[]): Promise<ExportResult>
  async generateExcelFile(data: CuttingExportData): Promise<Blob>
  async generateJsonFile(data: CuttingExportData): Promise<Blob>
}

// 切割结果导入服务
class CuttingResultImportService {
  async importCuttingResult(file: File): Promise<ImportResult>
  async validateResult(result: CuttingImportResult): Promise<ValidationResult>
  async updateScheduleWithCuttingResult(result: CuttingImportResult): Promise<FinalScheduleResult>
}

// 排产状态管理
class SchedulingStateManager {
  currentPhase: 'pre-scheduling' | 'cutting-optimization' | 'final-confirmation'
  preSchedule: PreScheduleResult | null
  cuttingExport: ExportResult | null
  cuttingResult: CuttingImportResult | null
  finalSchedule: FinalScheduleResult | null
}
```

## 📊 数据结构设计

### 预排产结果
```typescript
interface PreScheduleResult {
  scheduleId: string
  batches: ScheduledBatch[]
  ganttData: GanttChartData
  estimatedMetrics: {
    totalDuration: number        // 总工期(天)
    equipmentUtilization: number // 设备利用率(%)
    deliveryAchievement: number  // 交期达成率(%)
  }
  createdAt: string
}
```

### 切割导出数据
```typescript
interface CuttingExportData {
  exportId: string
  exportTime: string
  batches: {
    batchId: string
    specifications: GlassSpecifications
    quantity: number
    priority: number
    deliveryDate: string
  }[]
  availableMaterials: {
    materialId: string
    dimensions: { length: number; width: number; thickness: number }
    quantity: number
    cost: number
  }[]
  constraints: {
    maxUtilizationRate: number
    minPieceSize: { length: number; width: number }
    cuttingMargin: number
  }
}
```

### 切割导入结果
```typescript
interface CuttingImportResult {
  resultId: string
  importTime: string
  cuttingPlans: CuttingPlan[]
  materialUsage: MaterialUsage[]
  timeEstimates: {
    batchId: string
    cuttingTime: number
    setupTime: number
  }[]
  improvements: {
    materialSaved: number
    timeSaved: number
    utilizationImproved: number
  }
}
```

## 🚀 开发实施计划

### Phase 1: 基础架构 (1周)
- [ ] 创建主工作台组件框架
- [ ] 实现阶段管理器和状态管理
- [ ] 集成现有的批次数据和主数据

### Phase 2: 预排产功能 (1.5周)  
- [ ] 实现预排产算法引擎
- [ ] 开发甘特图展示组件
- [ ] 实现预排产指标计算

### Phase 3: 切割优化集成 (2周)
- [ ] 开发数据导出功能
- [ ] 实现文件导入导出界面
- [ ] 开发结果验证和导入功能

### Phase 4: 最终确认功能 (1周)
- [ ] 实现对比分析功能
- [ ] 开发最终排产确认界面
- [ ] 集成生产计划导出功能

### Phase 5: 测试优化 (0.5周)
- [ ] 端到端流程测试
- [ ] 用户体验优化
- [ ] 性能优化和错误处理

## 📋 验收标准

### 功能验收
- [ ] 支持从生产工单系统接收批次数据
- [ ] 能够生成基于标准工时的预排产方案
- [ ] 支持切割数据的Excel和JSON格式导出
- [ ] 支持第三方切割结果的导入和验证
- [ ] 能够基于切割结果更新最终排产计划
- [ ] 提供完整的甘特图可视化展示

### 性能验收
- [ ] 预排产计算时间 < 30秒
- [ ] 数据导出时间 < 10秒
- [ ] 结果导入验证时间 < 15秒
- [ ] 界面响应时间 < 2秒

### 用户体验验收
- [ ] 流程进度清晰可见
- [ ] 操作步骤简单直观
- [ ] 错误提示明确有用
- [ ] 支持操作撤销和重试

## 🔍 风险控制

### 技术风险
- **第三方数据格式兼容性**: 制定标准化数据格式，提供格式转换工具
- **大数据量处理性能**: 实现分页加载和虚拟滚动
- **异步流程状态管理**: 使用状态机模式确保状态一致性

### 业务风险  
- **切割优化结果不理想**: 提供回退机制和手动调整选项
- **流程中断处理**: 支持断点续传和状态恢复
- **用户操作错误**: 提供操作确认和撤销功能

## 📈 成功指标

### 效率指标
- 排产时间从2小时缩短至15分钟 (87.5%提升)
- 设备利用率从75%提升至85% (13.3%提升)
- 交期达成率从85%提升至95% (11.8%提升)

### 质量指标
- 原片利用率从82%提升至87% (6.1%提升)
- 排产准确率 > 95%
- 用户满意度 > 90%

## 🎯 关键决策记录

### 设计决策1: 异步集成方案
**决策**: 采用预排产 + 异步切割优化 + 最终确认的三阶段方案
**原因**: 平衡了用户体验简洁性和第三方系统集成的实际需求
**影响**: 需要完善的状态管理和进度跟踪机制

### 设计决策2: 数据格式标准化
**决策**: 同时支持Excel和JSON格式的数据导出导入
**原因**: 兼容不同第三方系统的数据格式要求
**影响**: 需要实现多格式解析和验证逻辑

### 设计决策3: 基于现有系统扩展
**决策**: 基于现有生产工单创建系统和主数据管理系统进行扩展
**原因**: 充分利用已有的业务逻辑和数据结构，降低开发成本
**影响**: 需要确保与现有系统的数据一致性和接口兼容性

## 📚 相关文档

- [生产工单创建界面开发指导文档](./production-order-creation-guide.md)
- [主数据管理模块开发计划](../discuss/主数据管理模块开发计划.md)
- [智能排产系统PRD](../discuss/智能排产系统PRD_V1.1.md)
- [讨论过程记录](../discuss/排产规划工作台重新设计讨论过程.md)

---

**备注**: 本设计文档将作为后续开发的指导文档，所有开发任务将基于此文档进行分解和实施。
