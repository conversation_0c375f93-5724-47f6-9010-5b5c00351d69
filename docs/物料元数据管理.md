# 物料元数据管理原型设计方案

本文档详细阐述了基于现有数据模型（`materialCategories.json` 和 `materials.json`）设计和实现物料元数据管理功能的原型方案。

## 1. 核心需求与数据关系分析

首要任务是深刻理解数据模型并将其转化为直观的用户界面。

- **数据核心**: 我们有两个关联的实体：
    - `物料分类 (Material Categories)`: 这是“模板”或“蓝图”，定义了一类物料的属性结构（基础属性 + 变体属性）。
    - `物料 (Materials)`: 这是具体的“实例”，每一个物料都属于一个分类，并拥有符合该分类结构的属性值和一系列“库存变体 (Variants/SKUs)”。

- **核心关系**:
    - **一对多**: 一个`物料分类`可以包含多个`物料`。
    - **一对多**: 一个`物料`可以包含多个`库存变体`。

- **UI设计目标**:
    - 必须清晰地展示这种层级关系。
    - 用户应能轻松地从分类导航到具体的物料，再到其库存变体。
    - 界面需要利用 `shadcn-vue` 组件库，保持项目整体风格统一。
    - 界面需要是数据驱动的，能动态地根据 `mock` 数据渲染。

这个原型不仅仅是展示数据，更是对物料主数据管理（MDM）核心功能的一次可视化实现。

## 2. UI/UX 布局设计：三层主从式布局

为了清晰地展示“分类 -> 物料 -> 变体”的层级关系，计划采用经典的三层主从式（Master-Detail）布局。

- **页面整体布局**:
    - **左侧导航区 (Master)**: 一个固定的侧边栏，用于显示**物料分类列表**。用户在此处选择一个分类作为筛选条件。
    - **右侧主内容区 (Detail)**: 该区域根据左侧的选择动态变化。

- **右侧主内容区交互流程**:
    - **默认状态**: 当用户刚进入页面时，显示一个欢迎或引导信息，提示用户从左侧选择一个物料分类。
    - **选择分类后**: 右侧内容区将展示一个**物料列表**（`materials`），这些物料都属于在左侧被选中的那个分类。这个列表将以表格形式呈现。
    - **选择物料后**: 当用户在物料表格中点击某一行时，该表格下方或旁边会进一步展示被选中物料的**详细信息**，包括其基础属性和一个**库存变体表格**。

- **视觉元素**:
    - **面包屑导航 (Breadcrumb)**: 在主内容区顶部，始终显示用户当前的位置，例如 `元数据 / 玻璃原片 / 6mm 透明浮法玻璃`。
    - **卡片 (Card)**: 使用 `Card` 组件来组织页面的不同区块，如“物料分类”、“物料列表”、“变体详情”等。
    - **徽章 (Badge)**: 用不同颜色的 `Badge` 来展示状态，例如物料的 `isActive` 状态或变体的库存水平。

## 3. 组件化架构设计

为了实现上述UI/UX设计，并保持代码的可维护性，原型将拆分为以下几个Vue组件：

1.  **`MetadataView.vue` (主视图)**:
    - **职责**: 作为物料管理页面的主容器，负责整体布局和状态协调。
    - **功能**: 获取所有数据，管理当前选中的分类和物料ID，并将数据分发给子组件。

2.  **`MaterialCategoryList.vue` (物料分类列表)**:
    - **职责**: 渲染左侧的分类导航栏。
    - **输入 (Props)**: `materialCategories` 数组, `selectedCategoryId`。
    - **输出 (Events)**: `category-selected` 事件。

3.  **`MaterialTable.vue` (物料表格)**:
    - **职责**: 显示属于某个分类的物料列表。
    - **输入 (Props)**: 筛选后的 `materials` 数组。
    - **输出 (Events)**: `material-selected` 事件。

4.  **`MaterialDetail.vue` (物料详情)**:
    - **职责**: 展示单个物料的完整信息及其所有库存变体。
    - **输入 (Props)**: 一个完整的 `material` 对象。
    - **包含**: `MaterialVariantTable.vue` 子组件。

5.  **`MaterialVariantTable.vue` (库存变体表格)**:
    - **职责**: 以表格形式展示一个物料的所有库存变体。
    - **输入 (Props)**: `variants` 数组。

## 4. 状态管理设计 (Pinia Store)

为了高效管理数据流和UI状态，将创建新的Pinia store `src/stores/metadata.ts`。

- **State**:
    - `materialCategories: MaterialCategory[]`: 所有物料分类。
    - `materials: Material[]`: 所有物料实例。
    - `selectedCategoryId: string | null`: 当前选中的分类ID。
    - `selectedMaterialId: string | null`: 当前选中的物料ID。
    - `loading: boolean`: 数据加载状态。
    - `error: string | null`: 错误信息。

- **Actions**:
    - `fetchMetadata()`: 异步从JSON文件加载所有元数据。
    - `selectCategory(categoryId: string)`: 更新选中的分类ID。
    - `selectMaterial(materialId: string)`: 更新选中的物料ID。

- **Getters**:
    - `materialsForSelectedCategory`: 根据 `selectedCategoryId` 筛选物料。
    - `selectedMaterial`: 根据 `selectedMaterialId` 查找并返回完整的物料对象。

## 5. 实施计划

1.  **创建 Pinia Store**:
    - 在 `src/stores/` 目录下创建 `metadata.ts` 文件，并实现上述设计的 state, actions, 和 getters。

2.  **创建类型定义**:
    - 在 `src/types/` 目录下创建 `material.ts` 文件，定义 `MaterialCategory`, `Material`, `MaterialVariant` 等TypeScript接口。

3.  **开发Vue组件**:
    - **`MetadataView.vue`**: 搭建主体布局，调用store action获取数据，并集成子组件。
    - **`MaterialCategoryList.vue`**: 渲染分类列表，处理点击事件。
    - **`MaterialTable.vue`**: 使用store getter获取数据并渲染表格，处理行点击事件。
    - **`MaterialDetail.vue`**: 使用store getter获取数据并渲染详情。
    - **`MaterialVariantTable.vue`**: 接收变体数据并渲染表格。

4.  **更新路由**:
    - 在 `src/router/index.ts` 中，为 `MetadataView.vue` 配置路由，例如 `/metadata/materials`。

5.  **UI润色**:
    - 添加面包屑导航、卡片布局、状态徽章等UI元素，并处理加载和空状态的显示。
