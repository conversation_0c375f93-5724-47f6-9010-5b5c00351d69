# 智能排产调试指南

## 🐛 常见问题和解决方案

### 1. 批次优化服务错误

#### 问题：`batchOptimizationResult.optimizedBatches is not iterable`

**原因分析：**
- `batchOptimizationService.optimizeBatches()` 返回的数据结构与预期不符
- 实际返回的字段名是 `batches` 而不是 `optimizedBatches`
- 批次优化服务可能返回了错误的数据格式

**解决方案：**
1. **检查批次优化服务返回结构**
```javascript
// 在浏览器控制台运行
const testItems = [
  {
    id: 'test-1',
    customerOrderItemId: 'coi-1',
    customerName: '测试客户',
    specifications: { length: 1800, width: 1200, thickness: 6, glassType: 'float_glass', color: 'clear' },
    quantity: 50,
    selectedQuantity: 50,
    processFlow: [
      { stepName: '磨边', workstation: 'edging', workstationGroup: 'edging' }
    ],
    currentStatus: 'pending',
    currentWorkstation: 'cutting'
  }
];

const result = await batchOptimizationService.optimizeBatches(testItems);
console.log('批次优化结果:', result);
console.log('结果键:', Object.keys(result));
```

2. **使用测试按钮验证**
- 在智能排产演示界面点击"测试批次优化"按钮
- 查看浏览器控制台的详细日志
- 确认批次优化服务的返回格式

3. **修复数据结构访问**
```typescript
// 正确的字段访问
const batches = batchOptimizationResult.batches; // 不是 optimizedBatches
const efficiency = batchOptimizationResult.efficiency; // 不是 efficiencyImprovement
const timeSaved = batchOptimizationResult.timeSaved; // 已经是分钟单位
```

### 2. 工单项数据结构问题

#### 问题：工单项缺少必要字段

**检查清单：**
- [ ] `customerName` 字段是否存在
- [ ] `specifications` 对象是否完整
- [ ] `processFlow` 数组是否包含磨边工序
- [ ] `selectedQuantity` 字段是否正确设置

**修复方法：**
```typescript
const selectedItems: SelectedOrderItem[] = workOrder.items.map(item => ({
  id: item.id,
  customerOrderItemId: item.customerOrderItemId,
  customerName: workOrder.customerName, // 确保添加客户名称
  specifications: {
    length: item.specifications.length,
    width: item.specifications.width,
    thickness: item.specifications.thickness,
    glassType: item.specifications.glassType,
    color: item.specifications.color
  },
  quantity: item.quantity,
  selectedQuantity: item.quantity, // 确保设置选中数量
  processFlow: item.processFlow,
  currentStatus: item.currentStatus,
  currentWorkstation: item.currentWorkstation
}));
```

### 3. 设备匹配分析错误

#### 问题：设备匹配服务调用失败

**调试步骤：**
1. **验证工作中心ID**
```javascript
// 确认工作中心ID是否正确
const workCenterId = 'WC-EDGE-001'; // 磨边工作中心
```

2. **检查订单项要求格式**
```javascript
const requirement = {
  orderItemId: item.id,
  dimensions: {
    length: item.specifications.length,
    width: item.specifications.width,
    thickness: item.specifications.thickness,
    area: (item.specifications.length * item.specifications.width) / 1000000, // 转换为m²
    perimeter: 2 * (item.specifications.length + item.specifications.width)
  },
  materialType: 'float_glass',
  glassType: item.specifications.glassType,
  edgeType: 'straight',
  precisionLevel: 'standard',
  surfaceQuality: 'A',
  quantity: item.quantity,
  requiredDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  priority: 'medium'
};
```

3. **测试设备匹配服务**
```javascript
// 在浏览器控制台测试
const matches = await EquipmentCapabilityService.matchEquipmentForOrderItem(
  requirement, 'WC-EDGE-001'
);
console.log('设备匹配结果:', matches);
```

## 🔧 调试工具和方法

### 1. 浏览器控制台调试

#### 基础调试命令
```javascript
// 1. 测试批次优化服务
await ProductionOrderSchedulingTestHelper.testBatchOptimization();

// 2. 测试设备分配
await ProductionOrderSchedulingTestHelper.testEquipmentAllocation();

// 3. 运行完整测试
await ProductionOrderSchedulingTestHelper.runCompleteSchedulingTest();

// 4. 生成测试工单
const testWorkOrder = ProductionOrderSchedulingTestHelper.generateTestWorkOrder();
console.log('测试工单:', testWorkOrder);
```

#### 详细调试信息
```javascript
// 启用详细日志
console.log('开始调试智能排产...');

// 检查服务可用性
console.log('批次优化服务:', typeof batchOptimizationService);
console.log('设备能力服务:', typeof EquipmentCapabilityService);

// 检查数据结构
const workOrder = selectedWorkOrder.value;
console.log('选中工单:', workOrder);
console.log('工单项数量:', workOrder?.items?.length);
workOrder?.items?.forEach((item, index) => {
  console.log(`工单项 ${index}:`, item);
});
```

### 2. 组件内调试

#### 添加调试日志
```vue
<script setup lang="ts">
// 在关键位置添加调试日志
const analyzeWorkOrderScheduling = async () => {
  console.log('🚀 开始智能排产分析');
  console.log('📋 选中工单:', selectedWorkOrder.value);
  
  try {
    const selectedItems = /* ... */;
    console.log('📦 准备分析的工单项:', selectedItems);
    
    const batchResult = await batchOptimizationService.optimizeBatches(selectedItems);
    console.log('🔄 批次优化结果:', batchResult);
    
    // ... 其他处理逻辑
  } catch (error) {
    console.error('❌ 分析失败:', error);
    console.error('错误堆栈:', error.stack);
  }
};
</script>
```

#### 添加错误边界
```vue
<template>
  <div class="space-y-6">
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="p-4 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-center gap-2">
        <AlertTriangle class="w-5 h-5 text-red-600" />
        <span class="font-medium text-red-800">分析失败</span>
      </div>
      <div class="mt-2 text-sm text-red-700">{{ errorMessage }}</div>
      <Button @click="clearError" variant="outline" size="sm" class="mt-2">
        清除错误
      </Button>
    </div>
    
    <!-- 其他内容 -->
  </div>
</template>

<script setup lang="ts">
const errorMessage = ref('');

const clearError = () => {
  errorMessage.value = '';
};

// 在错误处理中设置错误消息
catch (error) {
  console.error('分析失败:', error);
  errorMessage.value = `分析失败: ${error.message}`;
}
</script>
```

### 3. 数据验证工具

#### 工单数据验证
```javascript
function validateWorkOrder(workOrder) {
  const errors = [];
  
  if (!workOrder) {
    errors.push('工单对象为空');
    return errors;
  }
  
  if (!workOrder.workOrderNumber) {
    errors.push('缺少工单号');
  }
  
  if (!workOrder.items || !Array.isArray(workOrder.items)) {
    errors.push('工单项数组无效');
  } else {
    workOrder.items.forEach((item, index) => {
      if (!item.specifications) {
        errors.push(`工单项 ${index} 缺少规格信息`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`工单项 ${index} 数量无效`);
      }
      if (!item.processFlow || !Array.isArray(item.processFlow)) {
        errors.push(`工单项 ${index} 工艺流程无效`);
      }
    });
  }
  
  return errors;
}

// 使用验证工具
const errors = validateWorkOrder(selectedWorkOrder.value);
if (errors.length > 0) {
  console.error('工单验证失败:', errors);
}
```

## 🚀 性能优化建议

### 1. 批量处理优化
- 避免在循环中进行异步调用
- 使用 `Promise.all()` 并行处理多个设备匹配
- 缓存设备能力数据，避免重复查询

### 2. 错误恢复机制
- 实现优雅降级：批次优化失败时使用简化分析
- 添加重试机制：网络错误时自动重试
- 提供手动刷新选项：用户可以手动重新分析

### 3. 用户体验优化
- 添加加载进度指示器
- 显示详细的处理步骤
- 提供取消操作的选项

## 📝 故障排除清单

### 启动前检查
- [ ] 批次优化服务是否正常加载
- [ ] 设备能力服务是否正常加载
- [ ] 工作中心数据是否存在
- [ ] 设备数据是否完整

### 运行时检查
- [ ] 工单数据结构是否正确
- [ ] 工单项是否包含必要字段
- [ ] 批次优化服务返回格式是否正确
- [ ] 设备匹配服务是否正常响应

### 结果验证
- [ ] 批次分组是否合理
- [ ] 设备分配是否符合约束
- [ ] 评分计算是否正确
- [ ] 时间预测是否合理

通过以上调试指南，可以快速定位和解决智能排产演示中的各种问题，确保系统稳定运行。
