# 切割优化模拟数据功能

## 🎯 功能概述

为了让原型项目能够完整演示切割优化流程，我们添加了模拟数据功能，用户无需等待真实的第三方系统处理，可以快速体验完整的排产流程。

## 🔧 实现的功能

### **1. 模拟切割优化完成**

在切割优化等待阶段，用户可以点击"模拟完成"按钮，系统会自动生成优化结果，无需等待真实的第三方系统处理。

#### **触发方式**
- 在切割优化弹窗的"等待处理"步骤中
- 点击"模拟完成"按钮
- 系统自动生成切割优化结果

#### **模拟数据内容**
```json
{
  "improvements": {
    "materialSaved": 5.2,      // 节约5.2%材料
    "timeSaved": 2.1,          // 节约2.1小时
    "utilizationImproved": 7.5, // 利用率提升7.5%
    "costSaved": 4200          // 节约成本4200元
  },
  "cuttingPlans": [
    {
      "planId": "PLAN_001_OPTIMIZED",
      "materialId": "GLASS_3300x2140_6MM",
      "layout": {
        "pieces": [...],         // 详细的切割布局
        "utilization": 92.3,     // 优化后利用率
        "wastePercentage": 7.7   // 废料率
      },
      "cuttingSequence": [...]   // 切割步骤序列
    }
  ]
}
```

### **2. 真实的模拟数据**

#### **数据文件位置**
```
public/mock/cutting/optimizationResults.json
```

#### **数据特点**
- **真实的玻璃规格**: 使用行业标准的玻璃尺寸（3300×2140, 3660×2440）
- **合理的利用率**: 92.3%和89.7%，符合实际切割优化效果
- **详细的切割方案**: 包含切割位置、顺序、时间估算
- **质量指标**: 边缘质量、尺寸精度、表面质量等
- **环保指标**: 废料减少、能耗节约、碳足迹减少

#### **数据结构**
```json
{
  "cuttingPlans": [
    {
      "layout": {
        "pieces": [
          {
            "productName": "客厅落地窗",
            "dimensions": { "length": 1200, "width": 800 },
            "position": { "x": 0, "y": 0 },
            "quantity": 2
          }
        ],
        "utilization": 92.3,
        "wasteArea": 0.28,
        "totalArea": 7.062,
        "usedArea": 6.522
      },
      "cuttingSequence": [
        {
          "type": "vertical",
          "position": 1200,
          "description": "第一刀：垂直切割，分离客厅窗和卧室窗"
        }
      ]
    }
  ],
  "materialUsage": [
    {
      "materialName": "6mm透明浮法玻璃 3300×2140",
      "originalQuantity": 45,
      "usedQuantity": 38,
      "utilization": 92.3,
      "wasteAmount": 2.1
    }
  ]
}
```

## 🎮 使用流程

### **完整的演示流程**

1. **启动排产向导**
   - 点击"排产向导"按钮
   - 选择批次（如华润置地-批次1）
   - 配置排产策略
   - 点击"开始排产"

2. **预排产完成**
   - 查看甘特图和排产结果
   - 点击"确认方案"进入切割优化

3. **切割优化阶段**
   - 查看预排产结果概览
   - 点击右上角"切割优化"按钮
   - 在弹窗中看到"等待处理"状态

4. **模拟优化完成**
   - 点击"模拟完成"按钮
   - 系统自动生成优化结果
   - 查看改进效果和详细方案

5. **进入最终确认**
   - 系统自动切换到最终确认阶段
   - 查看优化前后对比
   - 完成整个排产流程

## 📊 模拟数据的价值

### **1. 完整的用户体验**
- 用户可以体验完整的排产流程
- 无需等待真实的第三方系统处理
- 快速验证系统功能和界面设计

### **2. 真实的业务场景**
- 使用真实的玻璃规格和尺寸
- 符合实际的切割优化效果
- 包含完整的质量和环保指标

### **3. 演示和培训**
- 适合向客户演示系统功能
- 可用于用户培训和操作指导
- 展示系统的完整业务价值

## 🔧 技术实现

### **服务层**
```typescript
// src/services/cuttingResultImportService.ts
async simulateOptimizationComplete(): Promise<CuttingImportResult> {
  // 加载真实的模拟数据
  const response = await fetch('/mock/cutting/optimizationResults.json');
  const mockData = await response.json();
  
  // 转换为系统需要的数据格式
  return {
    resultId: `simulation_${Date.now()}`,
    importTime: new Date().toISOString(),
    cuttingPlans: mockData.data[0].cuttingPlans,
    materialUsage: mockData.data[0].materialUsage,
    improvements: mockData.data[0].improvements,
    validation: mockData.data[0].validation
  };
}
```

### **状态管理**
```typescript
// src/stores/schedulingStore.ts
const simulateCuttingOptimizationComplete = async () => {
  importStatus.value = 'importing';
  
  // 调用模拟服务
  cuttingResult.value = await cuttingResultImportService.simulateOptimizationComplete();
  importStatus.value = 'imported';
  
  // 生成对比数据
  comparisonData.value = generateComparisonData();
};
```

### **界面组件**
```vue
<!-- 在等待处理阶段显示模拟按钮 -->
<div class="border-t border-orange-200 pt-3 mt-3">
  <div class="flex items-center justify-between">
    <div class="text-xs text-orange-600">
      <strong>原型演示：</strong>点击下方按钮模拟第三方系统处理完成
    </div>
    <Button @click="$emit('simulate-complete')" size="sm">
      <Zap class="h-3 w-3 mr-1" />
      模拟完成
    </Button>
  </div>
</div>
```

## 🎯 使用建议

1. **演示场景**: 向客户展示完整的排产优化流程
2. **培训场景**: 用于用户培训，快速体验系统功能
3. **开发测试**: 开发过程中快速测试后续流程
4. **功能验证**: 验证切割优化结果的展示和处理逻辑

这个模拟数据功能让原型项目具备了完整的演示能力，用户可以快速体验从预排产到最终确认的完整流程，大大提升了原型的实用价值。
