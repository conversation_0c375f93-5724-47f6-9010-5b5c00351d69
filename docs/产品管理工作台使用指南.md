# 产品管理工作台使用指南

## 概述

产品管理工作台是产品管理模块的统一入口，提供了产品配置、BOM管理和成本分析的完整工作流程。

## 功能特性

### 1. 工作台概览

产品管理工作台（`/product/workbench`）提供以下功能：

#### 统计概览
- **产品模板统计**：显示总模板数和活跃模板数
- **产品配置统计**：显示总配置数和今日新增配置数
- **BOM生成统计**：显示总BOM数和今日生成BOM数
- **平均成本分析**：显示平均成本和成本趋势

#### 快速操作
- **新建配置**：快速跳转到产品配置器
- **产品模板**：管理产品模板
- **BOM管理**：查看和管理BOM
- **成本分析**：进行成本分析

#### 最近活动
- **最近的产品配置**：显示最近创建或修改的产品配置
- **配置状态管理**：查看配置状态（草稿、活跃、已完成、已归档）
- **快速访问**：点击配置可快速打开配置器

#### 待处理任务
- **配置待完善**：显示状态为草稿的配置
- **BOM需要更新**：显示需要重新生成的BOM
- **任务优先级**：按时间排序显示待处理任务

#### 数据可视化
- **产品类型分布**：显示不同产品类型的数量和占比
- **颜色编码**：不同产品类型使用不同颜色标识

### 2. 产品模板管理

产品模板管理页面（`/product/templates`）提供以下功能：

#### 模板列表
- **搜索功能**：支持按名称、编码或描述搜索
- **筛选功能**：按产品类型和状态筛选
- **排序功能**：按更新时间排序显示

#### 模板信息
- **基本信息**：模板名称、编码、分类、状态
- **结构统计**：显示构件数量和配置规则数量
- **时间信息**：显示最后更新时间

#### 操作功能
- **配置产品**：基于模板创建新的产品配置
- **编辑模板**：修改模板信息和结构
- **复制模板**：复制现有模板创建新模板
- **查看详情**：查看模板详细信息

## 路由结构

```
/product                    # 重定向到工作台
├── /workbench             # 产品管理工作台
├── /configurator          # 产品配置器
└── /templates             # 产品模板管理
```

## 使用流程

### 1. 访问工作台
1. 在侧边栏点击"产品管理"菜单
2. 系统自动跳转到产品管理工作台
3. 查看概览统计和最近活动

### 2. 管理产品模板
1. 在工作台点击"产品模板"快速操作按钮
2. 或直接访问 `/product/templates`
3. 使用搜索和筛选功能查找模板
4. 点击操作按钮进行相应操作

### 3. 创建产品配置
1. 在工作台点击"新建配置"按钮
2. 或在模板列表中点击"配置"按钮
3. 系统跳转到产品配置器进行配置

### 4. 处理待办任务
1. 在工作台右侧查看"待处理任务"
2. 点击任务项快速跳转到相关页面
3. 完成任务后刷新工作台查看更新

## 技术实现

### 组件结构
- `ProductWorkbenchView.vue` - 工作台主页面
- `ProductTemplateView.vue` - 模板管理页面
- 集成现有的 `ProductConfigurator.vue` - 产品配置器

### 状态管理
- 使用 `useProductStore()` 管理产品数据
- 自动加载和缓存产品模板、配置和BOM数据
- 支持数据刷新和实时更新

### 路由配置
- 产品管理模块采用嵌套路由结构
- 支持面包屑导航和父子页面关系
- 自动重定向到工作台页面

## 数据源

工作台从以下Mock数据源获取信息：
- `public/mock/product/productTemplates.json` - 产品模板数据
- `public/mock/product/productConfigurations.json` - 产品配置数据
- `public/mock/product/productBOMs.json` - BOM数据
- `public/mock/product/quotations.json` - 报价数据

## 响应式设计

工作台采用响应式设计，支持不同屏幕尺寸：
- **桌面端**：4列统计卡片，3列主要内容布局
- **平板端**：2列统计卡片，2列主要内容布局
- **移动端**：1列布局，垂直堆叠显示

## 动画效果

- **淡入动画**：页面加载时的渐入效果
- **悬停效果**：卡片和按钮的悬停状态
- **过渡动画**：状态变化时的平滑过渡

## 扩展性

工作台设计具有良好的扩展性：
- **新增统计项**：可轻松添加新的统计指标
- **新增快速操作**：可添加更多快速操作按钮
- **新增数据可视化**：可集成图表组件显示更多数据
- **新增任务类型**：可扩展待处理任务的类型和逻辑

## 注意事项

1. **数据加载**：工作台会在页面加载时自动初始化产品数据
2. **权限控制**：所有产品管理页面都需要用户认证
3. **错误处理**：包含完善的错误处理和加载状态显示
4. **性能优化**：使用计算属性和响应式数据优化性能

## 后续开发

可以考虑添加以下功能：
- **数据导出**：支持导出产品模板和配置数据
- **批量操作**：支持批量编辑和删除模板
- **版本管理**：支持模板版本控制和历史记录
- **协作功能**：支持多用户协作编辑
- **通知系统**：支持任务提醒和状态通知
