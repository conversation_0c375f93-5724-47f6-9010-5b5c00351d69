# MTO模式产品结构数据概览

## 📋 数据结构完整性

我们已经为玻璃深加工企业的MTO模式创建了完整的产品结构数据，包含两个典型产品：

### 🏗️ 1. 标准防火窗结构 (FW-STD-001)

**产品层级**
- **产品名称**: 标准防火窗结构
- **产品编码**: FW-STD-001
- **防火等级**: A级，60分钟耐火极限
- **执行标准**: GB16809-2008

**构件层级**
- **主框架构件** (FW-MAIN-ASM)
  - 钢质框架系统
  - 参数化配置：框架宽度、深度、转角连接方式
  - 约束验证：框架尺寸标准要求

**组件层级**
1. **外框立柱** (FW-VERT-001) × 2
   - 左立柱、右立柱
   - 参数：高度、型材规格、材质等级
   - 物料映射：钢质型材分类

2. **外框横梁** (FW-HORZ-001) × 2
   - 上横梁、下横梁
   - 参数：宽度、型材规格
   - 成本计算：基于长度的动态计算

3. **防火玻璃面板** (FW-GLASS-001) × 1
   - 主玻璃面板
   - 参数：宽度、高度、厚度、防火等级
   - 约束：面积限制、厚度匹配
   - 替代方案：夹胶防火玻璃

4. **防火密封胶条** (FW-SEAL-001) × 1
   - 主密封胶条
   - 参数：总长度、密封类型
   - 计算公式：按周长自动计算

5. **防火窗五金套装** (FW-HW-001) × 1
   - 标准五金套装
   - 参数：五金类型、材质等级
   - 替代方案：开启窗五金

### 🏢 2. 高端防火隔断结构 (FP-PREM-001)

**产品层级**
- **产品名称**: 高端防火隔断结构
- **产品编码**: FP-PREM-001
- **防火等级**: A级，120分钟耐火极限
- **材质特色**: 不锈钢框架+夹胶防火玻璃

**构件层级**
1. **主框架系统** (FP-MAIN-ASM)
   - 不锈钢框架系统
   - 参数：框架宽度、材质等级
   - 替代方案：316不锈钢框架

2. **玻璃面板构件** (FP-GLASS-ASM) × 3
   - 多面板设计
   - 参数：面板宽度、高度
   - 子组件：夹胶防火玻璃

3. **密封系统构件** (FP-SEAL-ASM)
   - 完整密封系统
   - 高端密封等级
   - 子组件：结构密封胶

**组件层级**
- **不锈钢立柱** × 2
- **夹胶防火玻璃** × 3
- **结构密封胶** × 1

## 🎯 参数化配置特性

### 参数类型覆盖
- **尺寸参数** (dimension): 宽度、高度、厚度等
- **材质参数** (material): 型材规格、玻璃类型、材质等级
- **功能参数** (feature): 防火等级、开启方式、连接方式
- **选项参数** (option): 表面处理、颜色等

### 输入类型支持
- **数值输入** (number): 带单位、范围限制
- **选择输入** (select): 预定义选项、成本差异
- **开关输入** (toggle): 布尔值配置

### 约束验证系统
- **表达式约束**: JavaScript表达式验证
- **错误级别**: error、warning、info
- **自定义错误信息**: 业务友好的提示

## 💰 成本计算体系

### 物料分类映射
- `cat_steel_profile`: 钢质型材
- `cat_stainless_steel_profile`: 不锈钢型材
- `cat_fire_glass`: 防火玻璃
- `cat_fire_laminated_glass`: 夹胶防火玻璃
- `cat_sealing_material`: 密封材料
- `cat_hardware`: 五金配件
- `cat_structural_sealant`: 结构密封胶

### 计算公式示例
- **型材**: `quantity * material_unit_cost * 1.2`
- **玻璃**: `quantity * material_unit_cost * (glass_thickness / 6)`
- **密封胶**: `quantity * material_unit_cost`

## 🔄 MTO模式支持

### 客户定制能力
- ✅ 尺寸参数化：支持客户定制尺寸
- ✅ 材质选择：多种材质等级可选
- ✅ 功能配置：防火等级、开启方式等
- ✅ 成本透明：实时成本计算和显示

### 生产适应性
- ✅ 标准化构件：可复用的标准构件库
- ✅ 参数驱动：参数变化自动更新相关计算
- ✅ 约束保证：确保配置符合生产要求
- ✅ 物料清单：自动生成准确的物料需求

### 质量控制
- ✅ 标准符合性：内置国标要求验证
- ✅ 结构完整性：多层级验证机制
- ✅ 参数一致性：跨组件参数关联验证
- ✅ 成本控制：成本计算公式验证

## 📊 数据统计

### 标准防火窗结构
- **总节点数**: 7个 (1产品 + 1构件 + 5组件)
- **参数总数**: 15个
- **约束总数**: 5个
- **物料分类**: 5种

### 高端防火隔断结构
- **总节点数**: 8个 (1产品 + 4构件 + 3组件)
- **参数总数**: 8个
- **约束总数**: 1个
- **物料分类**: 3种

## 🎯 业务价值

1. **提高报价效率**: 参数化配置支持快速生成准确报价
2. **降低错误率**: 约束验证确保配置的正确性
3. **标准化管理**: 构件库实现标准化和复用
4. **成本透明**: 实时成本计算提高报价准确性
5. **客户体验**: 可视化配置提升客户满意度

这套数据结构完全符合玻璃深加工企业在MTO模式下的实际业务需求！
