
# 排产规划工作台：第三阶段“最终确认”详细设计文档

## 1. 背景与目标

“排产规划工作台”是一个三阶段流程，旨在将客户订单高效、准确地转化为可执行的生产计划。前两个阶段“预排产”和“切割优化”分别完成了基于标准工时的初步调度和基于物料利用率的套料优化。

**“最终确认”** 作为流程的最后一步，其核心目标是：

1.  **整合与重构**: 消化前两步的输出，将“逻辑计划”转换为与物理世界一致的“可执行计划”。
2.  **决策支持**: 以清晰、直观的方式对比展示优化前后的关键绩效指标(KPI)，为计划员提供充足的数据以做出最终决策。
3.  **最终执行**: 在用户确认后，正式生成并下达生产任务到车间，关闭本次排产循环。

本文档将详细阐述该阶段的设计细节，以指导原型开发。

## 2. 核心流程与数据转换

本阶段的核心是**“计划重构 (Schedule Reconstruction)”**。它接收预排产和切割优化的结果，通过一系列逻辑转换，生成最终的生产计划。

### 2.1. 输入数据

本阶段依赖于 `schedulingStore` 提供的以下核心数据：

-   `preSchedule`: 预排产结果。包含基于BOM解析的、带有依赖关系的**逻辑任务列表**和初步的甘特图数据。
-   `cuttingResult`: 切割优化结果。包含由第三方系统生成的**切割方案 (Layouts)**，明确了哪些玻璃件被组合在哪张物理原片上进行切割，并提供了精确的切割工时。
-   `selectedBatches`: 用户选择进行排产的所有生产批次及其详细信息。

### 2.2. 核心逻辑：计划重构

当进入此阶段时，系统必须在后台自动完成以下转换：

#### 2.2.1. 任务合并 (Task Consolidation)

-   **识别物理切割任务**: 遍历 `cuttingResult` 中的所有切割方案。每一个独立的切割方案（即一张物理原片）都将生成一个**全新的、唯一的“物理切割任务”**。
-   **废弃逻辑切割任务**: `preSchedule` 中所有原始的、离散的逻辑切割任务将被标记为“已合并”或直接移除。
-   **计算精确工时**: 新的“物理切割任务”的持续时间，将直接采用 `cuttingResult` 中提供的精确预估工时。

#### 2.2.2. 依赖关系重定向 (Dependency Redirection)

这是整个流程最关键的一步，确保了生产流程的连续性。

-   **遍历所有非切割任务**: 检查 `preSchedule` 中的所有后续工序任务（如磨边、钢化、清洗、合片等）。
-   **查找物料来源**: 对于每一个任务，确定其处理的玻璃组件。
-   **重新链接依赖**: 在 `cuttingResult` 中找到该玻璃组件被分配到了哪个“物理切割任务”（即在哪张原片上）。然后，将该任务的前置依赖，从旧的逻辑切割任务，**重定向**到这个新的物理切割任务。

**转换示例**:

-   **优化前**: `[逻辑切:A]` -> `[磨边:A]`, `[逻辑切:B]` -> `[磨边:B]`
-   **优化后**: `[物理切:原片01(含A,B)]` -> `[磨边:A]`, `[物理切:原片01(含A,B)]` -> `[磨边:B]`

### 2.3. 输出数据

经过重构后，系统会生成以下用于UI展示和最终下达的核心数据，并存储在 `schedulingStore` 中：

-   `finalSchedule`: 最终的排产方案对象。
    -   `tasks`: 重构后的任务列表，包含新的物理切割任务和被重定向依赖的后续任务。
    -   `ganttData`: 用于渲染最终甘特图的数据结构。
    -   `metrics`: 最终的各项KPI指标。
-   `comparisonData`: 一个专门用于对比的结构化对象，包含优化前后的关键指标。
    -   `duration`: `{ before: number, after: number }`
    -   `materialCost`: `{ before: number, after: number }`
    -   `utilizationRate`: `{ before: number, after: number }`
    -   `layoutsUsed`: `{ before: number, after: number }`
    -   `taskCount`: `{ cutting: { before, after }, total: { before, after } }`

## 3. UI/UX 详细设计 (`FinalConfirmationView.vue`)

### 3.1. 布局

采用三段式布局，从上至下依次为：决策摘要、可视化总览、执行清单。

1.  **顶部：关键指标对比区 (KPI Comparison Section)**
2.  **中部：最终排产方案总览 (Final Schedule Overview - Gantt Chart)**
3.  **底部：待生成任务清单 (Task Generation Preview Table)**

### 3.2. 组件详解

#### 3.2.1. 关键指标对比区

-   **形式**: 使用一组 `Card` 组件横向排列，每个卡片代表一个核心KPI。
-   **内容**: 每个卡片内都清晰地展示“优化前”和“优化后”的数值，并用颜色（如绿色表示改善）和百分比变化来突出优化效果。
-   **指标**:
    -   **预计总工时**: `comparisonData.duration`
    -   **预估材料成本**: `comparisonData.materialCost`
    -   **平均利用率**: `comparisonData.utilizationRate`
    -   **原片使用数**: `comparisonData.layoutsUsed`
    -   **切割任务数**: `comparisonData.taskCount.cutting`

#### 3.2.2. 最终排产方案总览

-   **形式**: 一个只读的、交互式的甘特图组件。
-   **内容**:
    -   展示 `finalSchedule.ganttData` 的结果。
    -   **可视化“扇出”依赖**: 从合并后的“物理切割任务”块，必须能清晰地看到多条依赖线连接到不同订单的后续工序。
    -   **支持复合玻璃**: 对于中空、夹胶等，`合片` 任务前必须有从多个不同组件玻璃任务汇入的依赖线。
-   **交互**:
    -   **悬停/点击任务**: 在右侧的 `SchedulingDetailsPanel` 中显示任务详情。
    -   **点击物理切割任务**: 详情面板展示该切割方案包含的所有玻璃件列表及其所属批次/订单（**核心追溯功能**）。
    -   **点击合片任务**: 详情面板高亮并列出其所需的所有前置组件玻璃任务。

#### 3.2.3. 待生成任务清单

-   **形式**: 一个可排序、可筛选的表格 (`Table`) 组件。
-   **目的**: 让用户在下达前，对将要创建到数据库中的具体任务有一个清晰的预览。
-   **列定义**:
    -   `任务名称` (例如: "切割:原片-001", "磨边: ZK001-A")
    -   `所属产品` (例如: "中空玻璃单元-001")
    -   `所属批次`
    -   `执行设备`
    -   `预计开始时间`
    -   `预计结束时间`
    -   `前置依赖` (例如: "切割:原片-001")
-   **分组**: 建议表格支持按“最终产品”进行分组，使用户能清晰地看到每个成品的完整加工流程。

## 4. 交互与操作

-   **主操作按钮**:
    -   `确认并下达生产计划`: 位于页面右上角或底部，是最终的执行按钮。
    -   **逻辑**: 调用 `schedulingStore.confirmFinalSchedule()`，该方法会向后端API发送 `finalSchedule` 数据，成功后触发全局通知并重置工作台。
-   **次要操作**:
    -   `返回上一步`: 允许用户返回“切割优化”阶段，以重新导入或调整优化结果。
    -   `导出最终方案`: (可选) 提供将 `finalSchedule` 导出为PDF或Excel的功能。

## 5. 状态管理器交互 (`schedulingStore`)

`FinalConfirmationView.vue` 将深度依赖 `schedulingStore`。需要确保 `store` 提供以下接口：

-   **State (状态)**:
    -   `finalSchedule: FinalSchedule | null`
    -   `comparisonData: ComparisonData | null`
-   **Getters (计算属性)**:
    -   `isFinalScheduleReady: boolean` (判断是否已成功生成最终计划)
-   **Actions (方法)**:
    -   `generateFinalSchedule()`: 内部方法，在进入此阶段时自动调用，执行2.2节描述的“计划重构”逻辑。
    -   `confirmFinalSchedule(): Promise<void>`: 将最终计划提交到后端的异步方法。
