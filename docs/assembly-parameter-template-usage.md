# 构件参数模板功能使用指南

## 功能概述

新的构件参数模板功能为设计师提供了智能化的参数定义辅助，在保持原有工作流程的基础上，大大提高了参数定义的效率和完整性。

## 使用场景

### 场景1：创建新的防火窗框构件

**步骤1：基本信息定义**
1. 打开"新建构件"对话框
2. 填写构件名称：`防火窗框-1200x1500`
3. 填写构件编码：`FW_FRAME_1200_1500`
4. 选择构件类型：`框架构件`

**步骤2：参数模板应用**
1. 选择构件类型后，系统会显示"使用参数模板"按钮
2. 点击按钮，打开参数模板选择对话框
3. 系统自动推荐"防火窗框构件"模板
4. 预览模板包含的参数分组：
   - **尺寸参数**：总宽度、总高度、框架深度
   - **性能参数**：耐火等级、保温性能
   - **材质参数**：型材材质、密封胶类型

**步骤3：参数确认和调整**
1. 点击"应用模板"，系统自动生成参数框架
2. 切换到"构件参数"标签页
3. 根据具体需求调整参数值：
   - 总宽度：1200mm
   - 总高度：1500mm
   - 耐火等级：A级

### 场景2：添加组件时的智能检查

**步骤1：添加组件实例**
1. 切换到"组件集合"标签页
2. 点击"添加组件"，选择"铝合金立柱"组件
3. 系统自动检查参数完整性

**步骤2：参数完整性提醒**
系统检测到缺失参数，显示提醒：
```
发现缺失的必需参数
基于构件类型"框架构件"，检测到以下必需参数尚未定义：
- 立柱截面尺寸 (必需) [数值] [添加]
- 立柱壁厚 (必需) [数值] [添加]
```

**步骤3：快速添加参数**
1. 点击"添加所有必需参数"按钮
2. 系统自动添加缺失的参数到构件参数列表
3. 参数完整性检查通过，显示绿色提示

### 场景3：智能参数建议

**步骤1：组件参数分析**
当添加多个组件后，系统分析组件参数需求，显示智能建议：
```
智能参数建议
基于已选择的组件，建议添加以下参数以提高配置完整性：
- 连接方式 [选择] 基于组件"连接固件"的参数"连接类型"建议添加
- 表面处理 [选择] 基于组件"铝合金立柱"的参数"表面处理"建议添加
```

**步骤2：选择性添加**
1. 根据实际需要选择添加建议的参数
2. 点击单个参数的"添加"按钮，或批量添加
3. 系统自动建立参数传播关系

## 核心功能详解

### 1. 参数模板系统

**预定义模板**：
- 防火窗框构件：包含尺寸、性能、材质三大类参数
- 防火窗扇构件：包含扇框尺寸、玻璃性能等参数
- 可扩展：支持添加更多构件类型的模板

**模板结构**：
```typescript
{
  name: "防火窗框构件",
  parameterGroups: [
    {
      category: "dimensions",
      displayName: "尺寸参数",
      parameters: [
        {
          name: "overallWidth",
          displayName: "总宽度",
          type: "number",
          required: true,
          unit: "mm",
          range: { min: 300, max: 3000 }
        }
        // ...更多参数
      ]
    }
    // ...更多分组
  ]
}
```

### 2. 参数完整性检查

**检查维度**：
1. **必需参数检查**：基于构件类型模板的必需参数
2. **组件需求检查**：基于已选择组件的参数需求
3. **参数冲突检查**：检测重复定义、类型不匹配等

**检查结果**：
- 🔴 **缺失参数**：必须添加的参数
- 🔵 **建议参数**：推荐添加的参数
- ⚠️ **参数冲突**：需要解决的冲突

### 3. 智能参数推荐

**推荐算法**：
1. 分析构件类型的标准参数需求
2. 分析已选择组件的参数依赖
3. 识别参数间的关联关系
4. 生成个性化的参数建议

**推荐类型**：
- **基础参数**：构件类型的标准参数
- **关联参数**：基于组件需求的相关参数
- **优化参数**：提高配置完整性的可选参数

## 最佳实践

### 1. 新建构件的推荐流程

```
1. 定义基本信息 → 选择构件类型
2. 应用参数模板 → 建立参数框架
3. 添加组件实例 → 触发完整性检查
4. 处理参数建议 → 完善参数定义
5. 调整参数值 → 完成构件配置
```

### 2. 参数定义的注意事项

**参数命名**：
- 使用清晰、一致的命名规范
- 避免使用缩写和专业术语
- 保持参数名称的唯一性

**参数分类**：
- 按功能域进行分类（尺寸、性能、材质等）
- 合理设置参数的必需性
- 考虑参数间的依赖关系

**参数验证**：
- 设置合理的数值范围
- 定义清晰的选项列表
- 添加必要的验证规则

### 3. 模板使用技巧

**模板选择**：
- 优先使用系统推荐的模板
- 根据项目特点选择合适的模板
- 必要时可以不使用模板，完全自定义

**模板定制**：
- 应用模板后可以自由修改参数
- 可以删除不需要的参数
- 可以添加项目特有的参数

**模板优化**：
- 基于使用经验优化模板内容
- 反馈常用参数组合给系统管理员
- 建议新的模板类型和参数

## 故障排除

### 常见问题

**Q1：为什么没有显示参数模板按钮？**
A1：确保已选择构件类型，且该类型有对应的参数模板。

**Q2：参数完整性检查一直显示缺失参数？**
A2：检查参数名称是否与模板要求完全一致，注意大小写。

**Q3：添加的参数没有传播到组件实例？**
A3：确保在参数编辑器中正确配置了参数传播规则。

### 性能优化

**大量参数处理**：
- 参数数量超过50个时，考虑分组管理
- 使用参数搜索功能快速定位
- 定期清理不使用的参数

**响应速度优化**：
- 避免频繁触发完整性检查
- 批量操作时使用批量添加功能
- 合理使用参数缓存机制

## 总结

新的构件参数模板功能通过智能化辅助，在保持设计师熟悉工作流程的基础上，显著提高了参数定义的效率和质量。合理使用这些功能，可以：

1. **减少参数定义时间**：通过模板快速建立参数框架
2. **提高参数完整性**：通过智能检查避免遗漏
3. **降低配置错误**：通过冲突检测和验证规则
4. **提升设计质量**：通过标准化的参数管理

建议设计师在日常工作中充分利用这些功能，逐步建立适合自己项目特点的参数管理最佳实践。
