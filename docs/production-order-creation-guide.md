# 生产工单创建界面开发指导文档

## 1. 概述

本文档详细描述了玻璃深加工企业ERP系统中生产工单创建界面的设计规范、功能要求和开发指导，旨在帮助前后端开发人员理解业务需求并进行具体的开发工作。

### 1.1 业务背景

生产工单创建是MES（制造执行系统）的核心功能，用于将客户订单转换为可执行的生产任务。系统采用两步式工作流程：
1. **订单项选择**：从客户订单中选择需要生产的订单项
2. **批次优化**：将选中的订单项进行批次优化和工艺流程配置

### 1.2 技术架构

- **前端框架**：Vue 3 + TypeScript + TailwindCSS + ShadCN
- **状态管理**：Pinia
- **表单验证**：Vee-validate
- **UI组件**：ShadCN组件库
- **构建工具**：Vite

## 2. 界面布局设计

### 2.1 整体布局结构

```
┌─────────────────────────────────────────────────────────────┐
│                    页面标题 + 操作按钮                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   工单向导      │  │   新建工单      │                   │
│  │   (向导模式)    │  │   (对话框模式)  │                   │
│  └─────────────────┘  └─────────────────┘                   │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                工单管理界面                              │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 工单向导布局（主要界面）

#### 2.2.1 第一步：订单项选择

```
┌─────────────────────────────────────────────────────────────┐
│  搜索和筛选区域                                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │   搜索框    │ │  状态筛选   │ │  客户筛选   │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────┐  ┌─────────────────────┐   │
│  │                             │  │                     │   │
│  │        订单列表区域          │  │   已选项汇总区域     │   │
│  │        (70% 宽度)           │  │   (30% 宽度)        │   │
│  │                             │  │                     │   │
│  │  ┌─────────────────────────┐ │  │  ┌───────────────┐  │   │
│  │  │     订单卡片 1          │ │  │  │   统计信息    │  │   │
│  │  │  ┌─────────────────────┐│ │  │  └───────────────┘  │   │
│  │  │  │    订单项列表       ││ │  │                     │   │
│  │  │  │  □ 订单项 1        ││ │  │  ┌───────────────┐  │   │
│  │  │  │  ☑ 订单项 2        ││ │  │  │  已选项列表   │  │   │
│  │  │  └─────────────────────┘│ │  │  │  • 项目 1     │  │   │
│  │  └─────────────────────────┘ │  │  │  • 项目 2     │  │   │
│  │                             │  │  └───────────────┘  │   │
│  │  ┌─────────────────────────┐ │  │                     │   │
│  │  │     订单卡片 2          │ │  │  ┌───────────────┐  │   │
│  │  └─────────────────────────┘ │  │  │   快速操作    │  │   │
│  │                             │  │  └───────────────┘  │   │
│  └─────────────────────────────┘  └─────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  底部操作栏                                                  │
│  ┌─────────────┐              ┌─────────────┐ ┌───────────┐ │
│  │  统计信息   │              │    取消     │ │  下一步   │ │
│  └─────────────┘              └─────────────┘ └───────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 2.2.2 第二步：批次优化

```
┌─────────────────────────────────────────────────────────────┐
│  批次优化配置区域                                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │  优化策略   │ │  批次大小   │ │  优先级设置 │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                批次优化结果                              │ │
│  │                                                         │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │   批次 1    │  │   批次 2    │  │   批次 3    │     │ │
│  │  │             │  │             │  │             │     │ │
│  │  │ • 订单项A   │  │ • 订单项C   │  │ • 订单项E   │     │ │
│  │  │ • 订单项B   │  │ • 订单项D   │  │ • 订单项F   │     │ │
│  │  │             │  │             │  │             │     │ │
│  │  │ 工时: 8.5h  │  │ 工时: 6.2h  │  │ 工时: 4.8h  │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                工艺流程配置                              │ │
│  │  ┌─────────────────────────────────────────────────────┐│ │
│  │  │  批次 1 工艺流程                                    ││ │
│  │  │  切割 → 磨边 → 钢化 → 质检 → 包装                  ││ │
│  │  └─────────────────────────────────────────────────────┘│ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  底部操作栏                                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ ┌───────┐│
│  │  重新优化   │  │    上一步   │  │    预览     │ │ 创建  ││
│  └─────────────┘  └─────────────┘  └─────────────┘ └───────┘│
└─────────────────────────────────────────────────────────────┘
```

## 3. 功能清单

### 3.1 核心功能模块

#### 3.1.1 订单项选择功能

| 功能点     | 描述                             | 优先级 |
| ---------- | -------------------------------- | ------ |
| 订单搜索   | 支持按订单号、客户名称、规格搜索 | P0     |
| 多维度筛选 | 按状态、工艺类型、客户筛选       | P0     |
| 订单项选择 | 支持单选、多选订单项             | P0     |
| 数量调整   | 支持调整选中订单项的生产数量     | P0     |
| 实时统计   | 显示已选项目数量、总数量、客户数 | P1     |
| 冲突检测   | 检测工艺冲突和资源冲突           | P1     |
| 批量操作   | 支持全选、清空、批量数量设置     | P2     |

#### 3.1.2 批次优化功能

| 功能点       | 描述                         | 优先级 |
| ------------ | ---------------------------- | ------ |
| 智能分批     | 基于工艺相似性自动分批       | P0     |
| 优化策略     | 支持工时优先、交期优先等策略 | P0     |
| 工艺流程生成 | 自动生成标准工艺流程         | P0     |
| 批次调整     | 支持手动调整批次分组         | P1     |
| 工时预估     | 计算每个批次的预估工时       | P1     |
| 资源分配     | 分配工作站和设备资源         | P1     |
| 冲突解决     | 提供冲突解决方案             | P2     |

#### 3.1.3 工单管理功能

| 功能点   | 描述                 | 优先级 |
| -------- | -------------------- | ------ |
| 工单列表 | 显示所有生产工单     | P0     |
| 状态管理 | 工单状态流转管理     | P0     |
| 进度跟踪 | 实时显示生产进度     | P0     |
| 工单编辑 | 支持工单信息修改     | P1     |
| 工单复制 | 基于历史工单快速创建 | P1     |
| 批量操作 | 批量状态更新、删除等 | P2     |

### 3.2 辅助功能模块

#### 3.2.1 快速操作面板

| 功能点   | 描述                 | 业务规则               |
| -------- | -------------------- | ---------------------- |
| 快速创建 | 一键创建常用规格工单 | 基于历史数据推荐       |
| 模板应用 | 应用预设工单模板     | 支持自定义模板         |
| 批量导入 | Excel批量导入订单项  | 支持数据验证和错误提示 |
| 重复操作 | 重复上次操作         | 记录最近5次操作        |

#### 3.2.2 数据验证功能

| 验证项       | 规则                 | 错误处理           |
| ------------ | -------------------- | ------------------ |
| 订单项可用性 | 检查库存、交期、状态 | 显示不可用原因     |
| 工艺兼容性   | 检查工艺流程冲突     | 提供解决建议       |
| 资源可用性   | 检查设备、人员可用性 | 显示资源占用情况   |
| 数量合理性   | 检查数量范围和约束   | 自动调整到合理范围 |

## 4. 业务规则详述

### 4.1 订单项选择规则

#### 4.1.1 可选择条件
- **订单状态**：只能选择"已确认"状态的订单
- **库存状态**：原材料库存充足的订单项
- **交期状态**：未超期且有足够生产时间的订单项
- **工艺状态**：工艺流程已定义或可自动生成的订单项

#### 4.1.2 数量限制规则
```typescript
interface QuantityRule {
  minQuantity: number;        // 最小生产数量（通常为1）
  maxQuantity: number;        // 最大数量（不超过订单数量）
  batchSizeMultiple?: number; // 批次数量倍数（某些工艺要求）
  economicQuantity?: number;  // 经济批量（建议数量）
}
```

#### 4.1.3 冲突检测规则

**原料冲突**：
- **厚度不同**：5mm白玻与8mm白玻属于不同原片，不能在同一批次
- **颜色不同**：5mm白玻与5mm彩玻（如灰玻、茶玻）属于不同原片
- **玻璃类型不同**：普通玻璃与Low-E玻璃、钢化玻璃等特殊玻璃不能混批
- **供应商不同**：不同供应商的同规格原片也视为不同物料

**规格冲突**：
- **尺寸差异过大**：300mm×500mm与1800mm×2000mm规格相差太大
- **面积比例限制**：最大面积与最小面积比例不能超过16:1
- **切割效率冲突**：小规格与大规格混合会降低切割台利用率
- **包装兼容性**：规格差异过大的产品包装方式不兼容

**工艺冲突**：
- **工艺流程不同**：不同玻璃类型需要不同的工艺流程
- **钻孔工序冲突**：有钻孔需求与无钻孔需求的订单项不能在同一批次
- **工艺参数冲突**：钢化温度、镀膜工艺等参数不兼容
- **质量标准不同**：不同等级产品的质检标准不同
- **冷工段差异**：钻孔工序会改变冷工段的标准流程

**设备冲突**：
- **设备能力限制**：超出设备加工能力范围的规格
- **工装夹具冲突**：需要不同工装夹具的产品不能连续生产
- **设备切换成本**：频繁切换设备配置增加生产成本

**交期冲突**：
- **紧急程度不同**：紧急订单与普通订单分开批次
- **交期跨度过大**：交期相差超过7天的订单项不建议合批
- **客户优先级**：VIP客户订单与普通客户订单优先级处理

**质量冲突**：
- **质量等级不同**：A级品与B级品不能在同一批次生产
- **检验标准不同**：出口产品与内销产品检验标准不同
- **包装要求不同**：特殊包装要求与标准包装不兼容

### 4.2 批次优化规则

#### 4.2.1 分批策略

**相似性分批**：
```typescript
interface SimilarityRule {
  materialType: number;   // 原料类型权重 (35%) - 厚度+颜色+玻璃类型
  sizeCompatibility: number; // 规格兼容性权重 (30%) - 尺寸相似度
  processFlow: number;    // 工艺流程权重 (20%)
  customer: number;       // 客户权重 (10%)
  deliveryDate: number;   // 交期权重 (5%)
}

// 原料兼容性检测
interface MaterialCompatibility {
  thickness: boolean;     // 厚度必须完全相同
  color: boolean;         // 颜色必须完全相同  
  glassType: boolean;     // 玻璃类型必须兼容
  supplier: boolean;      // 供应商可以不同但需标记
}

// 规格兼容性检测
interface SizeCompatibility {
  areaRatio: number;      // 面积比例：max/min ≤ 16
  lengthRatio: number;    // 长度比例：max/min ≤ 8
  widthRatio: number;     // 宽度比例：max/min ≤ 8
  cuttingEfficiency: number; // 切割台利用率 ≥ 75%
}
```

**冲突检测算法**：
```typescript
class ConflictDetector {
  // 原料冲突检测
  detectMaterialConflict(items: OrderItem[]): ConflictResult {
    const conflicts = [];
    
    for (let i = 0; i < items.length; i++) {
      for (let j = i + 1; j < items.length; j++) {
        const item1 = items[i];
        const item2 = items[j];
        
        // 厚度冲突
        if (item1.thickness !== item2.thickness) {
          conflicts.push({
            type: 'material_thickness',
            items: [item1.id, item2.id],
            message: `厚度不同：${item1.thickness}mm vs ${item2.thickness}mm`,
            severity: 'high'
          });
        }
        
        // 颜色冲突
        if (item1.color !== item2.color) {
          conflicts.push({
            type: 'material_color',
            items: [item1.id, item2.id],
            message: `颜色不同：${item1.color} vs ${item2.color}`,
            severity: 'high'
          });
        }
        
        // 玻璃类型冲突
        if (!this.isGlassTypeCompatible(item1.glassType, item2.glassType)) {
          conflicts.push({
            type: 'material_glass_type',
            items: [item1.id, item2.id],
            message: `玻璃类型不兼容：${item1.glassType} vs ${item2.glassType}`,
            severity: 'high'
          });
        }
      }
    }
    
    return { conflicts, canBatch: conflicts.length === 0 };
  }
  
  // 规格冲突检测
  detectSizeConflict(items: OrderItem[]): ConflictResult {
    const areas = items.map(item => item.length * item.width);
    const maxArea = Math.max(...areas);
    const minArea = Math.min(...areas);
    const areaRatio = maxArea / minArea;
    
    if (areaRatio > 16) {
      return {
        conflicts: [{
          type: 'size_area_ratio',
          items: items.map(item => item.id),
          message: `面积比例过大：${areaRatio.toFixed(1)}:1 (限制16:1)`,
          severity: 'high'
        }],
        canBatch: false
      };
    }
    
    // 检查切割效率
    const efficiency = this.calculateCuttingEfficiency(items);
    if (efficiency < 0.75) {
      return {
        conflicts: [{
          type: 'cutting_efficiency',
          items: items.map(item => item.id),
          message: `切割效率过低：${(efficiency * 100).toFixed(1)}% (要求≥75%)`,
          severity: 'medium'
        }],
        canBatch: false
      };
    }
    
    return { conflicts: [], canBatch: true };
  }
}
```

**工时平衡分批**：
- 每个批次工时控制在6-10小时
- 避免单个批次工时过长或过短
- 考虑设备切换时间和准备时间
- 原料相同的订单项优先合并
- 规格相似的订单项其次考虑

**交期优先分批**：
- 紧急订单优先安排（优先级：urgent > high > normal > low）
- 相同交期的订单项合并（交期差异≤3天）
- 预留缓冲时间（紧急订单预留20%，普通订单预留10%）
- 考虑原料和规格约束的前提下进行交期优化

#### 4.2.2 工艺流程生成规则

**标准工艺流程**：
```typescript
interface ProcessFlowRule {
  // 基础工序（所有玻璃必须）
  cutting: boolean;       // 切割
  edging: boolean;        // 磨边
  cleaning: boolean;      // 清洗
  qualityCheck: boolean;  // 质检
  packaging: boolean;     // 包装
  
  // 冷工段特殊工序
  drilling?: boolean;     // 钻孔（在磨边和清洗之间）
  
  // 热工段特殊工序（根据玻璃类型）
  coating?: boolean;      // 镀膜（Low-E、反射玻璃）
  tempering?: boolean;    // 钢化（厚度≥5mm或钢化玻璃）
  laminating?: boolean;   // 夹胶（夹胶玻璃）
  insulating?: boolean;   // 中空合片（中空玻璃）
}

// 工艺流程顺序定义
interface ProcessFlowSequence {
  coldProcessing: string[];  // 冷工段：切割 → 磨边 → [钻孔] → 清洗
  hotProcessing: string[];   // 热工段：[镀膜] → [钢化] → [夹胶] → [中空合片]
  finalProcessing: string[]; // 终工段：质检 → 包装
}

// 钻孔工序规则
interface DrillingRule {
  required: boolean;         // 是否需要钻孔
  holeCount: number;         // 孔数量
  holeDiameter: number[];    // 孔径（mm）
  holePosition: {            // 孔位置
    x: number;
    y: number;
  }[];
  drillBitType: string;      // 钻头类型
  coolingRequired: boolean;  // 是否需要冷却
}
```

**工时计算规则**：
```typescript
interface TimeCalculationRule {
  // 基础工时 = 面积系数 × 玻璃面积 + 厚度系数 × 玻璃厚度 + 固定工时
  cuttingTime: (area: number, thickness: number) => number;
  edgingTime: (area: number, thickness: number) => number;
  cleaningTime: (area: number) => number;
  qualityCheckTime: (area: number) => number;
  packagingTime: (area: number) => number;
  
  // 钻孔工时计算
  drillingTime: (holeCount: number, thickness: number, holeDiameter: number[]) => number;
  
  // 特殊工艺工时
  coatingTime: (area: number) => number;
  temperingTime: (area: number) => number;
  laminatingTime: (area: number) => number;
  insulatingTime: (area: number) => number;
}

// 钻孔工时计算公式
const calculateDrillingTime = (holeCount: number, thickness: number, holeDiameters: number[]): number => {
  // 基础钻孔时间：每个孔2-5分钟，根据厚度和孔径调整
  const baseTimePerHole = 2; // 分钟
  const thicknessMultiplier = 1 + (thickness - 6) * 0.1; // 厚度系数
  
  let totalTime = 0;
  holeDiameters.forEach(diameter => {
    const diameterMultiplier = 1 + (diameter - 6) * 0.05; // 孔径系数
    totalTime += baseTimePerHole * thicknessMultiplier * diameterMultiplier;
  });
  
  // 加上设备准备和调整时间
  const setupTime = 5; // 分钟
  return totalTime + setupTime;
};
}
```

### 4.3 数据验证规则

#### 4.3.1 前端验证规则
```typescript
interface ValidationRule {
  // 必填字段验证
  required: string[];
  
  // 数值范围验证
  numberRange: {
    quantity: { min: 1, max: 10000 };
    thickness: { min: 3, max: 25 };
    length: { min: 100, max: 6000 };
    width: { min: 100, max: 3000 };
  };
  
  // 格式验证
  format: {
    orderNumber: /^[A-Z]{2}-\d{7}$/;
    customerName: /^[\u4e00-\u9fa5a-zA-Z\s]{2,50}$/;
  };
}
```

#### 4.3.2 业务逻辑验证

**原料兼容性验证**：
```typescript
interface MaterialValidation {
  // 厚度一致性检查
  validateThickness(items: OrderItem[]): ValidationResult {
    const thicknesses = [...new Set(items.map(item => item.thickness))];
    if (thicknesses.length > 1) {
      return {
        valid: false,
        error: `批次中包含不同厚度的玻璃：${thicknesses.join('mm, ')}mm`,
        suggestion: '请将不同厚度的订单项分配到不同批次'
      };
    }
    return { valid: true };
  }
  
  // 颜色一致性检查
  validateColor(items: OrderItem[]): ValidationResult {
    const colors = [...new Set(items.map(item => item.color))];
    if (colors.length > 1) {
      return {
        valid: false,
        error: `批次中包含不同颜色的玻璃：${colors.join(', ')}`,
        suggestion: '请将不同颜色的订单项分配到不同批次'
      };
    }
    return { valid: true };
  }
  
  // 玻璃类型兼容性检查
  validateGlassType(items: OrderItem[]): ValidationResult {
    const incompatiblePairs = [
      ['clear', 'low_e'],
      ['clear', 'reflective'],
      ['tempered', 'laminated']
    ];
    
    const types = items.map(item => item.glassType);
    for (const [type1, type2] of incompatiblePairs) {
      if (types.includes(type1) && types.includes(type2)) {
        return {
          valid: false,
          error: `${type1}玻璃与${type2}玻璃不能在同一批次生产`,
          suggestion: '请将不兼容的玻璃类型分配到不同批次'
        };
      }
    }
    return { valid: true };
  }
}
```

**规格兼容性验证**：
```typescript
interface SizeValidation {
  // 面积比例检查
  validateAreaRatio(items: OrderItem[]): ValidationResult {
    const areas = items.map(item => item.length * item.width / 1000000); // 转换为平方米
    const maxArea = Math.max(...areas);
    const minArea = Math.min(...areas);
    const ratio = maxArea / minArea;
    
    if (ratio > 16) {
      return {
        valid: false,
        error: `规格面积比例过大：${ratio.toFixed(1)}:1 (限制16:1)`,
        suggestion: '请将大规格和小规格的订单项分配到不同批次',
        details: {
          maxArea: maxArea.toFixed(2) + 'm²',
          minArea: minArea.toFixed(2) + 'm²',
          ratio: ratio.toFixed(1)
        }
      };
    }
    
    if (ratio > 8) {
      return {
        valid: true,
        warning: `规格面积比例较大：${ratio.toFixed(1)}:1，可能影响生产效率`,
        suggestion: '建议优化批次分配以提高生产效率'
      };
    }
    
    return { valid: true };
  }
  
  // 切割效率检查
  validateCuttingEfficiency(items: OrderItem[]): ValidationResult {
    const efficiency = this.calculateCuttingEfficiency(items);
    
    if (efficiency < 0.6) {
      return {
        valid: false,
        error: `切割效率过低：${(efficiency * 100).toFixed(1)}% (最低要求60%)`,
        suggestion: '请重新组织批次以提高原片利用率'
      };
    }
    
    if (efficiency < 0.75) {
      return {
        valid: true,
        warning: `切割效率偏低：${(efficiency * 100).toFixed(1)}% (建议≥75%)`,
        suggestion: '建议优化规格组合以提高原片利用率'
      };
    }
    
    return { valid: true };
  }
  
  // 计算切割效率
  private calculateCuttingEfficiency(items: OrderItem[]): number {
    // 简化算法：基于规格相似度计算效率
    const areas = items.map(item => item.length * item.width);
    const avgArea = areas.reduce((sum, area) => sum + area, 0) / areas.length;
    const variance = areas.reduce((sum, area) => sum + Math.pow(area - avgArea, 2), 0) / areas.length;
    const coefficient = Math.sqrt(variance) / avgArea; // 变异系数
    
    // 变异系数越小，效率越高
    return Math.max(0.3, 1 - coefficient);
  }
}
```

**综合业务验证**：
- **库存验证**：检查原材料库存是否充足，考虑不同厚度、颜色的库存分别计算
- **产能验证**：检查生产能力是否满足需求，考虑设备切换时间成本
- **交期验证**：检查是否能在要求时间内完成，考虑批次优化后的实际工时
- **工艺验证**：检查工艺流程是否合理，验证特殊工艺的设备和人员配置
- **成本验证**：评估批次优化对生产成本的影响，包括原料利用率、设备效率等

## 5. 数据结构设计

### 5.1 核心数据模型

#### 5.1.1 客户订单模型
```typescript
interface CustomerOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  projectName?: string;
  status: 'confirmed' | 'in_production' | 'completed' | 'cancelled';
  requiredDate: string;
  estimatedCost: number;
  createdAt: string;
  updatedAt: string;
  items: CustomerOrderItem[];
}

interface CustomerOrderItem {
  id: string;
  customerOrderId: string;
  specifications: GlassSpecifications;
  quantity: number;
  unitPrice: number;
  deliveryDate: string;
  processFlow?: ProcessStep[];
  drillingSpecs?: DrillingSpecifications;  // 钻孔规格
  notes?: string;
}

// 钻孔规格定义
interface DrillingSpecifications {
  required: boolean;                    // 是否需要钻孔
  holePositions: HolePosition[];        // 孔位置信息
  holeDiameters: number[];             // 孔径列表（mm）
  drillBitTypes: string[];             // 钻头类型
  coolingRequired: boolean;            // 是否需要冷却
  precisionLevel: 'standard' | 'high' | 'precision';  // 精度要求
  specialRequirements?: string;        // 特殊要求
}

interface HolePosition {
  x: number;          // X坐标（mm，相对于玻璃左下角）
  y: number;          // Y坐标（mm，相对于玻璃左下角）
  diameter: number;   // 孔径（mm）
  depth?: number;     // 孔深（mm，用于盲孔）
  chamfer?: boolean;  // 是否需要倒角
}

// 扩展玻璃规格接口
interface GlassSpecifications {
  length: number;
  width: number;
  thickness: number;
  glassType: 'clear' | 'tinted' | 'low_e' | 'reflective' | 'tempered' | 'laminated' | 'insulating';
  color?: string;
  drillingRequired?: boolean;  // 是否需要钻孔（快速判断）
}
```

#### 5.1.2 生产工单模型
```typescript
interface ProductionOrder {
  id: string;
  workOrderNumber: string;
  customerOrderId: string;
  customerOrderNumber: string;
  customerName: string;
  status: 'pending' | 'released' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  plannedStartDate: string;
  plannedEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  estimatedDuration: number;
  items: ProductionOrderItem[];
  batches: OptimizedBatch[];
  createdAt: string;
  updatedAt: string;
}
```

#### 5.1.3 批次优化模型
```typescript
interface OptimizedBatch {
  id: string;
  batchNumber: string;
  items: SelectedOrderItem[];
  processFlow: ProcessStep[];
  estimatedTime: number;
  workstations: string[];
  priority: number;
  constraints: BatchConstraint[];
}

interface BatchConstraint {
  type: 'equipment' | 'material' | 'quality' | 'time';
  description: string;
  severity: 'low' | 'medium' | 'high';
  resolution?: string;
}
```

### 5.2 状态管理设计

#### 5.2.1 Pinia Store结构
```typescript
// 生产工单创建状态
interface ProductionOrderCreationState {
  // 第一步：订单选择
  availableOrders: CustomerOrder[];
  selectedOrderItems: SelectedOrderItem[];
  searchQuery: string;
  filters: {
    status: string;
    customer: string;
    processType: string;
  };
  
  // 第二步：批次优化
  optimizationStrategy: 'time' | 'quality' | 'cost';
  optimizedBatches: OptimizedBatch[];
  processConflicts: ProcessConflict[];
  
  // 工单配置
  workOrderConfig: {
    priority: 'low' | 'normal' | 'high' | 'urgent';
    plannedStartDate: string;
    estimatedEndDate: string;
    notes: string;
  };
  
  // UI状态
  currentStep: 1 | 2;
  isLoading: boolean;
  isCreating: boolean;
  validationErrors: ValidationError[];
}
```

## 6. API接口设计

### 6.1 RESTful API规范

#### 6.1.1 订单相关接口
```typescript
// 获取可用订单列表
GET /api/orders/available
Query: {
  search?: string;
  status?: string;
  customer?: string;
  page?: number;
  limit?: number;
}
Response: {
  data: CustomerOrder[];
  total: number;
  page: number;
  limit: number;
}

// 获取订单详情
GET /api/orders/:id
Response: {
  data: CustomerOrder;
}
```

#### 6.1.2 生产工单接口
```typescript
// 创建生产工单
POST /api/production-orders
Body: {
  selectedItems: SelectedOrderItem[];
  optimizedBatches: OptimizedBatch[];
  workOrderConfig: WorkOrderConfig;
}
Response: {
  data: ProductionOrder;
  message: string;
}

// 批次优化
POST /api/production-orders/optimize
Body: {
  selectedItems: SelectedOrderItem[];
  strategy: 'time' | 'quality' | 'cost';
}
Response: {
  data: {
    batches: OptimizedBatch[];
    conflicts: ProcessConflict[];
    recommendations: string[];
  };
}
```

#### 6.1.3 工艺流程接口
```typescript
// 生成工艺流程
POST /api/process-flows/generate
Body: {
  specifications: GlassSpecifications;
}
Response: {
  data: ProcessStep[];
}

// 验证工艺兼容性
POST /api/process-flows/validate
Body: {
  items: SelectedOrderItem[];
}
Response: {
  data: {
    compatible: boolean;
    conflicts: ProcessConflict[];
    suggestions: string[];
  };
}
```

#### 6.1.4 冲突检测接口
```typescript
// 检测批次冲突
POST /api/batch-conflicts/detect
Body: {
  items: SelectedOrderItem[];
  batchingStrategy?: 'similarity' | 'time' | 'cost';
}
Response: {
  data: {
    conflicts: BatchConflict[];
    severity: 'low' | 'medium' | 'high';
    canProceed: boolean;
    recommendations: ConflictResolution[];
  };
}

// 原料兼容性检查
POST /api/materials/compatibility-check
Body: {
  items: SelectedOrderItem[];
}
Response: {
  data: {
    compatible: boolean;
    materialGroups: MaterialGroup[];
    conflicts: MaterialConflict[];
    suggestions: string[];
  };
}

// 规格兼容性检查
POST /api/specifications/compatibility-check
Body: {
  items: SelectedOrderItem[];
}
Response: {
  data: {
    compatible: boolean;
    sizeGroups: SizeGroup[];
    conflicts: SizeConflict[];
    efficiency: {
      cuttingEfficiency: number;
      materialUtilization: number;
      recommendations: string[];
    };
  };
}

// 钻孔工序兼容性检查
POST /api/drilling/compatibility-check
Body: {
  items: SelectedOrderItem[];
}
Response: {
  data: {
    compatible: boolean;
    drillingGroups: DrillingGroup[];
    conflicts: DrillingConflict[];
    efficiency: {
      setupTimeReduction: number;
      drillBitUtilization: number;
      recommendations: string[];
    };
  };
}
Response: {
  data: {
    compatible: boolean;
    sizeGroups: SizeGroup[];
    conflicts: SizeConflict[];
    efficiency: {
      cuttingEfficiency: number;
      materialUtilization: number;
      recommendations: string[];
    };
  };
}

// 获取冲突解决方案
POST /api/conflicts/resolve
Body: {
  conflicts: BatchConflict[];
  items: SelectedOrderItem[];
  preferences: {
    prioritizeDelivery: boolean;
    prioritizeEfficiency: boolean;
    allowPartialBatches: boolean;
  };
}
Response: {
  data: {
    resolutions: ConflictResolution[];
    alternativeBatches: OptimizedBatch[];
    tradeoffs: {
      efficiency: number;
      deliveryImpact: string;
      costImpact: number;
    };
  };
}
```

#### 6.1.5 数据类型定义
```typescript
interface BatchConflict {
  id: string;
  type: 'material' | 'size' | 'process' | 'resource' | 'delivery' | 'quality';
  severity: 'low' | 'medium' | 'high';
  message: string;
  affectedItems: string[];
  resolution?: string;
  autoResolvable: boolean;
}

interface MaterialConflict extends BatchConflict {
  type: 'material';
  subType: 'thickness' | 'color' | 'glass_type' | 'supplier';
  conflictingValues: string[];
}

interface SizeConflict extends BatchConflict {
  type: 'size';
  subType: 'area_ratio' | 'dimension_ratio' | 'cutting_efficiency';
  metrics: {
    areaRatio?: number;
    lengthRatio?: number;
    widthRatio?: number;
    cuttingEfficiency?: number;
  };
}

interface ConflictResolution {
  conflictId: string;
  strategy: 'split_batch' | 'merge_similar' | 'adjust_priority' | 'manual_review';
  description: string;
  impact: {
    efficiency: number;
    cost: number;
    delivery: string;
  };
  newBatches?: OptimizedBatch[];
}

interface MaterialGroup {
  id: string;
  thickness: number;
  color: string;
  glassType: string;
  items: SelectedOrderItem[];
  compatible: boolean;
}

interface SizeGroup {
  id: string;
  items: SelectedOrderItem[];
  metrics: {
    minArea: number;
    maxArea: number;
    areaRatio: number;
    cuttingEfficiency: number;
  };
  compatible: boolean;
}

interface DrillingGroup {
  id: string;
  items: SelectedOrderItem[];
  drillingType: 'none' | 'simple' | 'complex' | 'precision';
  metrics: {
    totalHoles: number;
    uniqueDiameters: number;
    avgHolesPerItem: number;
    setupTimeEstimate: number;  // 分钟
  };
  compatible: boolean;
}

interface DrillingConflict extends BatchConflict {
  type: 'drilling';
  subType: 'mixed_drilling_requirement' | 'diameter_variety' | 'hole_count_variance' | 'precision_mismatch';
  drillingMetrics?: {
    drillingItems: number;
    nonDrillingItems: number;
    diameterCount: number;
    maxHoles: number;
    minHoles: number;
  };
}
```

### 6.2 错误处理规范

#### 6.2.1 HTTP状态码使用
- `200 OK`：请求成功
- `201 Created`：资源创建成功
- `400 Bad Request`：请求参数错误
- `401 Unauthorized`：未授权
- `403 Forbidden`：权限不足
- `404 Not Found`：资源不存在
- `409 Conflict`：资源冲突
- `422 Unprocessable Entity`：业务逻辑错误
- `500 Internal Server Error`：服务器内部错误

#### 6.2.2 错误响应格式
```typescript
interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
  };
}
```

## 7. 开发实施指导

### 7.1 前端开发指导

#### 7.1.1 组件开发顺序
1. **基础UI组件**（1-2天）
   - OrderItemCard（订单项卡片）
   - BatchCard（批次卡片）
   - ProcessFlowDisplay（工艺流程显示）

2. **功能组件**（3-4天）
   - OrderItemSearchFilter（搜索筛选）
   - OrderItemSelector（订单项选择器）
   - BatchOptimizationPanel（批次优化面板）

3. **页面组件**（2-3天）
   - StepOneOrderSelection（第一步）
   - StepTwoBatchOptimization（第二步）
   - ProductionOrderWizard（主向导）

4. **集成测试**（1-2天）
   - 端到端流程测试
   - 边界条件测试
   - 性能优化

#### 7.1.2 关键技术实现

**响应式设计**：
```css
/* 移动端适配 */
@media (max-width: 768px) {
  .order-selection-layout {
    flex-direction: column;
  }
  
  .selected-summary-panel {
    width: 100%;
    max-height: 300px;
  }
}
```

**状态管理**：
```typescript
// 使用Pinia进行状态管理
export const useProductionOrderStore = defineStore('productionOrder', {
  state: (): ProductionOrderCreationState => ({
    availableOrders: [],
    selectedOrderItems: [],
    // ...其他状态
  }),
  
  actions: {
    async loadAvailableOrders() {
      this.isLoading = true;
      try {
        const response = await api.getAvailableOrders();
        this.availableOrders = response.data;
      } finally {
        this.isLoading = false;
      }
    }
  }
});
```

**表单验证**：
```typescript
// 使用Vee-validate进行表单验证
const { handleSubmit, errors } = useForm({
  validationSchema: {
    quantity: (value: number) => {
      if (!value || value < 1) return '数量必须大于0';
      if (value > maxQuantity) return `数量不能超过${maxQuantity}`;
      return true;
    }
  }
});
```

### 7.2 后端开发指导

#### 7.2.1 数据库设计

**订单表结构**：
```sql
CREATE TABLE customer_orders (
  id VARCHAR(36) PRIMARY KEY,
  order_number VARCHAR(20) UNIQUE NOT NULL,
  customer_name VARCHAR(100) NOT NULL,
  project_name VARCHAR(100),
  status ENUM('confirmed', 'in_production', 'completed', 'cancelled'),
  required_date DATE NOT NULL,
  estimated_cost DECIMAL(12,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_order_number (order_number),
  INDEX idx_customer_name (customer_name),
  INDEX idx_status (status)
);
```

**生产工单表结构**：
```sql
CREATE TABLE production_orders (
  id VARCHAR(36) PRIMARY KEY,
  work_order_number VARCHAR(20) UNIQUE NOT NULL,
  customer_order_id VARCHAR(36) NOT NULL,
  status ENUM('pending', 'released', 'in_progress', 'completed', 'cancelled'),
  priority ENUM('low', 'normal', 'high', 'urgent'),
  planned_start_date DATE,
  planned_end_date DATE,
  actual_start_date DATE,
  actual_end_date DATE,
  estimated_duration INT, -- 预估工时（分钟）
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_order_id) REFERENCES customer_orders(id),
  INDEX idx_work_order_number (work_order_number),
  INDEX idx_status (status),
  INDEX idx_priority (priority)
);
```

#### 7.2.2 业务逻辑实现

**批次优化算法**：
```python
class BatchOptimizer:
    def __init__(self):
        self.conflict_detector = ConflictDetector()
        self.material_validator = MaterialValidator()
        self.size_validator = SizeValidator()
    
    def optimize_batches(self, selected_items: List[SelectedOrderItem], 
                        strategy: str) -> BatchOptimizationResult:
        """
        批次优化主算法
        """
        # 1. 预处理：冲突检测和分组
        preprocessing_result = self._preprocess_items(selected_items)
        if not preprocessing_result.success:
            return BatchOptimizationResult(
                success=False,
                errors=preprocessing_result.errors,
                batches=[]
            )
        
        # 2. 根据策略进行优化
        if strategy == 'similarity':
            return self._optimize_by_similarity(preprocessing_result.groups)
        elif strategy == 'time':
            return self._optimize_by_time(preprocessing_result.groups)
        elif strategy == 'cost':
            return self._optimize_by_cost(preprocessing_result.groups)
    
    def _preprocess_items(self, items: List[SelectedOrderItem]) -> PreprocessingResult:
        """
        预处理：按原料和规格进行初步分组
        """
        # 1. 按原料类型分组（厚度+颜色+玻璃类型）
        material_groups = self._group_by_material(items)
        
        # 2. 在每个原料组内按规格兼容性进一步分组
        compatible_groups = []
        errors = []
        
        for material_group in material_groups:
            # 检查原料兼容性
            material_validation = self.material_validator.validate(material_group)
            if not material_validation.valid:
                errors.append(material_validation.error)
                continue
            
            # 按规格兼容性分组
            size_groups = self._group_by_size_compatibility(material_group)
            for size_group in size_groups:
                # 检查规格兼容性
                size_validation = self.size_validator.validate(size_group)
                if size_validation.valid:
                    compatible_groups.append(size_group)
                else:
                    errors.append(size_validation.error)
        
        return PreprocessingResult(
            success=len(errors) == 0,
            groups=compatible_groups,
            errors=errors
        )
    
    def _group_by_material(self, items: List[SelectedOrderItem]) -> List[List[SelectedOrderItem]]:
        """
        按原料类型分组
        """
        material_map = {}
        
        for item in items:
            # 创建原料标识：厚度_颜色_玻璃类型
            material_key = f"{item.thickness}_{item.color}_{item.glassType}"
            
            if material_key not in material_map:
                material_map[material_key] = []
            material_map[material_key].append(item)
        
        return list(material_map.values())
    
    def _group_by_size_compatibility(self, items: List[SelectedOrderItem]) -> List[List[SelectedOrderItem]]:
        """
        按规格兼容性分组
        """
        if len(items) <= 1:
            return [items]
        
        # 使用图算法找到兼容的规格组合
        compatibility_graph = self._build_compatibility_graph(items)
        connected_components = self._find_connected_components(compatibility_graph)
        
        groups = []
        for component in connected_components:
            group = [items[i] for i in component]
            groups.append(group)
        
        return groups
    
    def _build_compatibility_graph(self, items: List[SelectedOrderItem]) -> Dict[int, List[int]]:
        """
        构建规格兼容性图
        """
        graph = {i: [] for i in range(len(items))}
        
        for i in range(len(items)):
            for j in range(i + 1, len(items)):
                if self._are_sizes_compatible(items[i], items[j]):
                    graph[i].append(j)
                    graph[j].append(i)
        
        return graph
    
    def _are_sizes_compatible(self, item1: SelectedOrderItem, item2: SelectedOrderItem) -> bool:
        """
        检查两个订单项的规格是否兼容
        """
        area1 = item1.length * item1.width
        area2 = item2.length * item2.width
        
        # 面积比例检查
        area_ratio = max(area1, area2) / min(area1, area2)
        if area_ratio > 16:
            return False
        
        # 长度比例检查
        length_ratio = max(item1.length, item2.length) / min(item1.length, item2.length)
        if length_ratio > 8:
            return False
        
        # 宽度比例检查
        width_ratio = max(item1.width, item2.width) / min(item1.width, item2.width)
        if width_ratio > 8:
            return False
        
        return True
    
    def _optimize_by_similarity(self, groups: List[List[SelectedOrderItem]]) -> BatchOptimizationResult:
        """
        基于相似性的批次优化（在兼容组内进行）
        """
        batches = []
        
        for group in groups:
            # 在兼容组内按工艺相似性进一步优化
            similarity_matrix = self._calculate_process_similarity_matrix(group)
            sub_clusters = self._cluster_by_process_similarity(group, similarity_matrix)
            
            for cluster in sub_clusters:
                # 检查工时平衡
                balanced_batches = self._balance_work_hours(cluster)
                batches.extend(balanced_batches)
        
        return BatchOptimizationResult(
            success=True,
            batches=batches,
            conflicts=self._detect_remaining_conflicts(batches)
        )

class ConflictDetector:
    """冲突检测器"""
    
    def detect_all_conflicts(self, items: List[SelectedOrderItem]) -> List[Conflict]:
        """检测所有类型的冲突"""
        conflicts = []
        
        conflicts.extend(self._detect_material_conflicts(items))
        conflicts.extend(self._detect_size_conflicts(items))
        conflicts.extend(self._detect_process_conflicts(items))
        conflicts.extend(self._detect_resource_conflicts(items))
        conflicts.extend(self._detect_delivery_conflicts(items))
        
        return conflicts
    
    def _detect_material_conflicts(self, items: List[SelectedOrderItem]) -> List[Conflict]:
        """检测原料冲突"""
        conflicts = []
        
        # 按原料属性分组检查
        thickness_groups = self._group_by_attribute(items, 'thickness')
        if len(thickness_groups) > 1:
            conflicts.append(Conflict(
                type='material_thickness',
                severity='high',
                message=f'批次包含{len(thickness_groups)}种不同厚度的玻璃',
                affected_items=[item.id for item in items],
                resolution='将不同厚度的订单项分配到不同批次'
            ))
        
        color_groups = self._group_by_attribute(items, 'color')
        if len(color_groups) > 1:
            conflicts.append(Conflict(
                type='material_color',
                severity='high',
                message=f'批次包含{len(color_groups)}种不同颜色的玻璃',
                affected_items=[item.id for item in items],
                resolution='将不同颜色的订单项分配到不同批次'
            ))
        
        return conflicts
    
    def _detect_size_conflicts(self, items: List[SelectedOrderItem]) -> List[Conflict]:
        """检测规格冲突"""
        conflicts = []
        
        if len(items) < 2:
            return conflicts
        
        # 计算面积比例
        areas = [item.length * item.width for item in items]
        max_area = max(areas)
        min_area = min(areas)
        area_ratio = max_area / min_area
        
        if area_ratio > 16:
            conflicts.append(Conflict(
                type='size_area_ratio',
                severity='high',
                message=f'规格面积比例过大：{area_ratio:.1f}:1 (限制16:1)',
                affected_items=[item.id for item in items],
                resolution='将大规格和小规格订单项分配到不同批次'
            ))
        elif area_ratio > 8:
            conflicts.append(Conflict(
                type='size_area_ratio',
                severity='medium',
                message=f'规格面积比例较大：{area_ratio:.1f}:1，可能影响效率',
                affected_items=[item.id for item in items],
                resolution='建议优化批次分配以提高生产效率'
            ))
        
        return conflicts
    
    def _detect_process_conflicts(self, items: List[SelectedOrderItem]) -> List[Conflict]:
        """检测工艺流程冲突"""
        conflicts = []
        
        # 检测钻孔工序冲突
        drilling_conflicts = self._detect_drilling_conflicts(items)
        conflicts.extend(drilling_conflicts)
        
        # 检测其他工艺冲突
        glass_type_conflicts = self._detect_glass_type_conflicts(items)
        conflicts.extend(glass_type_conflicts)
        
        return conflicts
    
    def _detect_drilling_conflicts(self, items: List[SelectedOrderItem]) -> List[Conflict]:
        """检测钻孔工序冲突"""
        conflicts = []
        
        # 分组：有钻孔需求 vs 无钻孔需求
        drilling_items = [item for item in items if item.drilling_required]
        non_drilling_items = [item for item in items if not item.drilling_required]
        
        if len(drilling_items) > 0 and len(non_drilling_items) > 0:
            conflicts.append(Conflict(
                type='process_drilling',
                severity='high',
                message=f'批次中混合了有钻孔需求({len(drilling_items)}项)和无钻孔需求({len(non_drilling_items)}项)的订单',
                affected_items=[item.id for item in items],
                resolution='将有钻孔需求和无钻孔需求的订单项分配到不同批次'
            ))
        
        # 检测钻孔参数冲突（在有钻孔需求的订单项中）
        if len(drilling_items) > 1:
            drilling_conflicts = self._detect_drilling_parameter_conflicts(drilling_items)
            conflicts.extend(drilling_conflicts)
        
        return conflicts
    
    def _detect_drilling_parameter_conflicts(self, items: List[SelectedOrderItem]) -> List[Conflict]:
        """检测钻孔参数冲突"""
        conflicts = []
        
        # 检查孔径差异
        all_diameters = set()
        for item in items:
            if item.drilling_specs:
                all_diameters.update(item.drilling_specs.hole_diameters)
        
        if len(all_diameters) > 3:  # 孔径种类过多
            conflicts.append(Conflict(
                type='drilling_diameter_variety',
                severity='medium',
                message=f'批次中包含{len(all_diameters)}种不同孔径，可能需要频繁更换钻头',
                affected_items=[item.id for item in items],
                resolution='建议将相似孔径的订单项合并到同一批次'
            ))
        
        # 检查孔数量差异
        hole_counts = [len(item.drilling_specs.hole_positions) for item in items if item.drilling_specs]
        if hole_counts:
            max_holes = max(hole_counts)
            min_holes = min(hole_counts)
            if max_holes / min_holes > 5:  # 孔数量差异过大
                conflicts.append(Conflict(
                    type='drilling_hole_count_variance',
                    severity='medium',
                    message=f'批次中孔数量差异过大：{min_holes}-{max_holes}个孔',
                    affected_items=[item.id for item in items],
                    resolution='建议将孔数量相近的订单项合并到同一批次'
                ))
        
        return conflicts
    
    def _detect_glass_type_conflicts(self, items: List[SelectedOrderItem]) -> List[Conflict]:
        """检测玻璃类型工艺冲突"""
        conflicts = []
        
        # 检查不兼容的玻璃类型组合
        glass_types = [item.glass_type for item in items]
        incompatible_combinations = [
            ('clear', 'low_e'),
            ('clear', 'reflective'),
            ('tempered', 'laminated')
        ]
        
        for type1, type2 in incompatible_combinations:
            if type1 in glass_types and type2 in glass_types:
                conflicts.append(Conflict(
                    type='glass_type_incompatible',
                    severity='high',
                    message=f'{type1}玻璃与{type2}玻璃工艺不兼容',
                    affected_items=[item.id for item in items if item.glass_type in [type1, type2]],
                    resolution=f'将{type1}玻璃和{type2}玻璃分配到不同批次'
                ))
        
        return conflicts
```

**工艺流程生成**：
```python
class ProcessFlowGenerator:
    def generate_standard_flow(self, specifications: GlassSpecifications) -> List[ProcessStep]:
        """
        生成标准工艺流程
        """
        flow = []
        
        # 冷工段工序
        flow.extend(self._generate_cold_processing_flow(specifications))
        
        # 热工段工序
        flow.extend(self._generate_hot_processing_flow(specifications))
        
        # 终工段工序
        flow.extend(self._generate_final_processing_flow(specifications))
        
        return flow
    
    def _generate_cold_processing_flow(self, specifications: GlassSpecifications) -> List[ProcessStep]:
        """生成冷工段工艺流程"""
        cold_flow = []
        
        # 1. 切割工序（必须）
        cold_flow.append(self._create_cutting_step(specifications))
        
        # 2. 磨边工序（必须）
        cold_flow.append(self._create_edging_step(specifications))
        
        # 3. 钻孔工序（如果需要）
        if self._needs_drilling(specifications):
            cold_flow.append(self._create_drilling_step(specifications))
        
        # 4. 清洗工序（必须）
        cold_flow.append(self._create_cleaning_step(specifications))
        
        return cold_flow
    
    def _generate_hot_processing_flow(self, specifications: GlassSpecifications) -> List[ProcessStep]:
        """生成热工段工艺流程"""
        hot_flow = []
        
        # 镀膜工序（Low-E、反射玻璃）
        if self._needs_coating(specifications):
            hot_flow.append(self._create_coating_step(specifications))
        
        # 钢化工序（厚度≥5mm或钢化玻璃）
        if self._needs_tempering(specifications):
            hot_flow.append(self._create_tempering_step(specifications))
        
        # 夹胶工序（夹胶玻璃）
        if self._needs_laminating(specifications):
            hot_flow.append(self._create_laminating_step(specifications))
        
        # 中空合片工序（中空玻璃）
        if self._needs_insulating(specifications):
            hot_flow.append(self._create_insulating_step(specifications))
        
        return hot_flow
    
    def _generate_final_processing_flow(self, specifications: GlassSpecifications) -> List[ProcessStep]:
        """生成终工段工艺流程"""
        final_flow = []
        
        # 质检工序（必须）
        final_flow.append(self._create_quality_check_step(specifications))
        
        # 包装工序（必须）
        final_flow.append(self._create_packaging_step(specifications))
        
        return final_flow
    
    def _needs_drilling(self, specifications: GlassSpecifications) -> bool:
        """判断是否需要钻孔工序"""
        return hasattr(specifications, 'drilling_specs') and specifications.drilling_specs is not None
    
    def _create_drilling_step(self, specifications: GlassSpecifications) -> ProcessStep:
        """创建钻孔工序步骤"""
        drilling_specs = specifications.drilling_specs
        
        # 计算钻孔工时
        hole_count = len(drilling_specs.hole_positions)
        estimated_time = self._calculate_drilling_time(
            hole_count, 
            specifications.thickness, 
            drilling_specs.hole_diameters
        )
        
        return ProcessStep(
            step_name='钻孔',
            workstation=self._select_drilling_station(drilling_specs),
            estimated_duration=estimated_time,
            constraints={
                'drill_bit_types': drilling_specs.drill_bit_types,
                'cooling_required': drilling_specs.cooling_required,
                'precision_level': drilling_specs.precision_level
            },
            status='pending',
            parameters={
                'hole_count': hole_count,
                'hole_diameters': drilling_specs.hole_diameters,
                'hole_positions': drilling_specs.hole_positions
            }
        )
    
    def _select_drilling_station(self, drilling_specs: DrillingSpecs) -> str:
        """选择合适的钻孔工作站"""
        hole_count = len(drilling_specs.hole_positions)
        max_diameter = max(drilling_specs.hole_diameters)
        
        if hole_count <= 4 and max_diameter <= 12:
            return 'drilling_station_1'  # 小孔位钻孔台
        elif hole_count <= 8 and max_diameter <= 25:
            return 'drilling_station_2'  # 中型钻孔台
        else:
            return 'drilling_station_3'  # 大型钻孔台
    
    def _calculate_drilling_time(self, hole_count: int, thickness: number, hole_diameters: number[]) -> number:
        """计算钻孔工时"""
        base_time_per_hole = 2  # 基础时间：每孔2分钟
        thickness_factor = 1 + (thickness - 6) * 0.1  # 厚度系数
        
        total_time = 0
        for diameter in hole_diameters:
            diameter_factor = 1 + (diameter - 6) * 0.05  # 孔径系数
            total_time += base_time_per_hole * thickness_factor * diameter_factor
        
        # 设备准备时间
        setup_time = 5
        
        return Math.ceil(total_time + setup_time)
```

### 7.3 测试指导

#### 7.3.1 单元测试
```typescript
// 冲突检测服务测试
describe('ConflictDetectionService', () => {
  test('should detect material conflicts', async () => {
    const selectedItems = [
      { 
        id: '1', 
        specifications: { 
          glassType: 'clear', 
          thickness: 6, 
          color: '透明',
          length: 1800,
          width: 1200
        } 
      },
      { 
        id: '2', 
        specifications: { 
          glassType: 'clear', 
          thickness: 8, // 不同厚度
          color: '透明',
          length: 1800,
          width: 1200
        } 
      }
    ];
    
    const result = await conflictDetectionService.detectMaterialConflicts(selectedItems);
    
    expect(result.conflicts).toHaveLength(1);
    expect(result.conflicts[0].type).toBe('material_thickness');
    expect(result.conflicts[0].severity).toBe('high');
    expect(result.canBatch).toBe(false);
  });
  
  test('should detect drilling conflicts', async () => {
    const selectedItems = [
      { 
        id: '1', 
        specifications: { 
          thickness: 6, 
          color: '透明',
          glassType: 'clear',
          length: 1800,
          width: 1200,
          drillingRequired: true
        },
        drillingSpecs: {
          required: true,
          holePositions: [{ x: 100, y: 100, diameter: 8 }],
          holeDiameters: [8],
          drillBitTypes: ['standard'],
          coolingRequired: false,
          precisionLevel: 'standard'
        }
      },
      { 
        id: '2', 
        specifications: { 
          thickness: 6, 
          color: '透明',
          glassType: 'clear',
          length: 1800,
          width: 1200,
          drillingRequired: false  // 无钻孔需求
        }
      }
    ];
    
    const result = await conflictDetectionService.detectDrillingConflicts(selectedItems);
    
    expect(result.conflicts).toHaveLength(1);
    expect(result.conflicts[0].type).toBe('process_drilling');
    expect(result.conflicts[0].severity).toBe('high');
    expect(result.canBatch).toBe(false);
  });
  
  test('should detect size conflicts', async () => {
    const selectedItems = [
      { 
        id: '1', 
        specifications: { 
          thickness: 6, 
          color: '透明',
          glassType: 'clear',
          length: 300,   // 小规格
          width: 500
        } 
      },
      { 
        id: '2', 
        specifications: { 
          thickness: 6, 
          color: '透明',
          glassType: 'clear',
          length: 2400,  // 大规格
          width: 1600
        } 
      }
    ];
    
    const result = await conflictDetectionService.detectSizeConflicts(selectedItems);
    
    expect(result.conflicts).toHaveLength(1);
    expect(result.conflicts[0].type).toBe('size_area_ratio');
    expect(result.conflicts[0].severity).toBe('high');
    expect(result.canBatch).toBe(false);
  });
  
  test('should pass compatible items', async () => {
    const selectedItems = [
      { 
        id: '1', 
        specifications: { 
          thickness: 6, 
          color: '透明',
          glassType: 'clear',
          length: 1800,
          width: 1200
        } 
      },
      { 
        id: '2', 
        specifications: { 
          thickness: 6, 
          color: '透明',
          glassType: 'clear',
          length: 2000,  // 相似规格
          width: 1400
        } 
      }
    ];
    
    const materialResult = await conflictDetectionService.detectMaterialConflicts(selectedItems);
    const sizeResult = await conflictDetectionService.detectSizeConflicts(selectedItems);
    
    expect(materialResult.canBatch).toBe(true);
    expect(sizeResult.canBatch).toBe(true);
    expect(materialResult.conflicts).toHaveLength(0);
    expect(sizeResult.conflicts).toHaveLength(0);
  });
});

// 批次优化服务测试
describe('BatchOptimizationService', () => {
  test('should separate incompatible materials', async () => {
    const selectedItems = [
      { id: '1', specifications: { glassType: 'clear', thickness: 6, color: '透明' } },
      { id: '2', specifications: { glassType: 'clear', thickness: 6, color: '透明' } },
      { id: '3', specifications: { glassType: 'clear', thickness: 8, color: '透明' } }, // 不同厚度
      { id: '4', specifications: { glassType: 'clear', thickness: 6, color: '灰色' } }  // 不同颜色
    ];
    
    const result = await batchOptimizationService.optimizeBatches(
      selectedItems, 
      'similarity'
    );
    
    expect(result.success).toBe(true);
    expect(result.batches).toHaveLength(3); // 应该分成3个批次
    
    // 验证每个批次内的原料一致性
    result.batches.forEach(batch => {
      const thicknesses = [...new Set(batch.items.map(item => item.specifications.thickness))];
      const colors = [...new Set(batch.items.map(item => item.specifications.color))];
      
      expect(thicknesses).toHaveLength(1);
      expect(colors).toHaveLength(1);
    });
  });
  
  test('should handle size compatibility', async () => {
    const selectedItems = [
      { 
        id: '1', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 1800, width: 1200 
        } 
      },
      { 
        id: '2', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 2000, width: 1400  // 相似规格
        } 
      },
      { 
        id: '3', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 300, width: 500    // 规格差异过大
        } 
      }
    ];
    
    const result = await batchOptimizationService.optimizeBatches(
      selectedItems, 
      'similarity'
    );
    
    expect(result.success).toBe(true);
    expect(result.batches).toHaveLength(2); // 应该分成2个批次
    
    // 验证规格兼容性
    result.batches.forEach(batch => {
      const areas = batch.items.map(item => 
        item.specifications.length * item.specifications.width
      );
      const maxArea = Math.max(...areas);
      const minArea = Math.min(...areas);
      const ratio = maxArea / minArea;
      
      expect(ratio).toBeLessThanOrEqual(16); // 面积比例不超过16:1
    });
  });
  
  test('should separate drilling and non-drilling items', async () => {
    const selectedItems = [
      { 
        id: '1', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 1800, width: 1200, drillingRequired: true
        },
        drillingSpecs: {
          required: true,
          holePositions: [{ x: 100, y: 100, diameter: 8 }],
          holeDiameters: [8],
          drillBitTypes: ['standard'],
          coolingRequired: false,
          precisionLevel: 'standard'
        }
      },
      { 
        id: '2', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 1800, width: 1200, drillingRequired: true
        },
        drillingSpecs: {
          required: true,
          holePositions: [
            { x: 50, y: 50, diameter: 8 },
            { x: 150, y: 150, diameter: 8 }
          ],
          holeDiameters: [8],
          drillBitTypes: ['standard'],
          coolingRequired: false,
          precisionLevel: 'standard'
        }
      },
      { 
        id: '3', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 1800, width: 1200, drillingRequired: false  // 无钻孔需求
        }
      }
    ];
    
    const result = await batchOptimizationService.optimizeBatches(
      selectedItems, 
      'similarity'
    );
    
    expect(result.success).toBe(true);
    expect(result.batches).toHaveLength(2); // 应该分成2个批次：钻孔批次和非钻孔批次
    
    // 验证钻孔需求的一致性
    result.batches.forEach(batch => {
      const drillingRequirements = [...new Set(
        batch.items.map(item => item.specifications.drillingRequired || false)
      )];
      expect(drillingRequirements).toHaveLength(1); // 每个批次内钻孔需求一致
    });
    
    // 验证钻孔批次包含正确的工艺流程
    const drillingBatch = result.batches.find(batch => 
      batch.items.some(item => item.specifications.drillingRequired)
    );
    if (drillingBatch) {
      const processSteps = drillingBatch.processFlow.map(step => step.stepName);
      expect(processSteps).toContain('钻孔');
      
      // 验证钻孔工序在正确位置（磨边之后，清洗之前）
      const edgingIndex = processSteps.indexOf('磨边');
      const drillingIndex = processSteps.indexOf('钻孔');
      const cleaningIndex = processSteps.indexOf('清洗');
      
      expect(drillingIndex).toBeGreaterThan(edgingIndex);
      expect(cleaningIndex).toBeGreaterThan(drillingIndex);
    }
  });
  
  test('should group similar drilling specifications', async () => {
    const selectedItems = [
      { 
        id: '1', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 1800, width: 1200, drillingRequired: true
        },
        drillingSpecs: {
          required: true,
          holePositions: [{ x: 100, y: 100, diameter: 8 }],
          holeDiameters: [8],
          drillBitTypes: ['standard'],
          coolingRequired: false,
          precisionLevel: 'standard'
        }
      },
      { 
        id: '2', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 1800, width: 1200, drillingRequired: true
        },
        drillingSpecs: {
          required: true,
          holePositions: [{ x: 120, y: 120, diameter: 8 }], // 相似孔径和数量
          holeDiameters: [8],
          drillBitTypes: ['standard'],
          coolingRequired: false,
          precisionLevel: 'standard'
        }
      },
      { 
        id: '3', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 1800, width: 1200, drillingRequired: true
        },
        drillingSpecs: {
          required: true,
          holePositions: [
            { x: 50, y: 50, diameter: 12 },   // 不同孔径
            { x: 100, y: 100, diameter: 12 },
            { x: 150, y: 150, diameter: 12 }  // 更多孔数
          ],
          holeDiameters: [12],
          drillBitTypes: ['large'],
          coolingRequired: true,  // 需要冷却
          precisionLevel: 'high'  // 高精度
        }
      }
    ];
    
    const result = await batchOptimizationService.optimizeBatches(
      selectedItems, 
      'similarity'
    );
    
    expect(result.success).toBe(true);
    expect(result.batches).toHaveLength(2); // 应该分成2个批次：相似钻孔规格分组
    
    // 验证钻孔规格的相似性
    result.batches.forEach(batch => {
      if (batch.items.length > 1) {
        const firstItem = batch.items[0];
        const firstSpecs = firstItem.drillingSpecs;
        
        batch.items.forEach(item => {
          const specs = item.drillingSpecs;
          // 验证孔径相似性
          expect(specs.holeDiameters).toEqual(firstSpecs.holeDiameters);
          // 验证精度要求一致性
          expect(specs.precisionLevel).toBe(firstSpecs.precisionLevel);
          // 验证冷却需求一致性
          expect(specs.coolingRequired).toBe(firstSpecs.coolingRequired);
        });
      }
    });
  }); 
      },
      { 
        id: '3', 
        specifications: { 
          thickness: 6, color: '透明', glassType: 'clear',
          length: 300, width: 500    // 规格差异过大
        } 
      }
    ];
    
    const result = await batchOptimizationService.optimizeBatches(
      selectedItems, 
      'similarity'
    );
    
    expect(result.success).toBe(true);
    expect(result.batches).toHaveLength(2); // 应该分成2个批次
    
    // 验证规格兼容性
    result.batches.forEach(batch => {
      const areas = batch.items.map(item => 
        item.specifications.length * item.specifications.width
      );
      const maxArea = Math.max(...areas);
      const minArea = Math.min(...areas);
      const ratio = maxArea / minArea;
      
      expect(ratio).toBeLessThanOrEqual(16); // 面积比例不超过16:1
    });
  });
});

// 工艺流程生成器测试
describe('ProcessFlowGenerator', () => {
  test('should generate appropriate flow for different glass types', () => {
    const clearGlass = { thickness: 6, glassType: 'clear', length: 1800, width: 1200 };
    const lowEGlass = { thickness: 8, glassType: 'low_e', length: 1800, width: 1200 };
    
    const clearFlow = processFlowGenerator.generateStandardFlow(clearGlass);
    const lowEFlow = processFlowGenerator.generateStandardFlow(lowEGlass);
    
    // 普通玻璃基础流程（更新为包含清洗工序）
    expect(clearFlow.map(step => step.stepName)).toEqual([
      '切割', '磨边', '清洗', '质检', '包装'
    ]);
    
    // Low-E玻璃包含镀膜和钢化
    expect(lowEFlow.map(step => step.stepName)).toEqual([
      '切割', '磨边', '清洗', '镀膜', '钢化', '质检', '包装'
    ]);
  });
  
  test('should include drilling step when required', () => {
    const glassWithDrilling = { 
      thickness: 6, 
      glassType: 'clear', 
      length: 1800, 
      width: 1200,
      drillingSpecs: {
        required: true,
        holePositions: [{ x: 100, y: 100, diameter: 8 }],
        holeDiameters: [8],
        drillBitTypes: ['standard'],
        coolingRequired: false,
        precisionLevel: 'standard'
      }
    };
    
    const flow = processFlowGenerator.generateStandardFlow(glassWithDrilling);
    const stepNames = flow.map(step => step.stepName);
    
    // 验证包含钻孔工序
    expect(stepNames).toContain('钻孔');
    
    // 验证钻孔工序在正确位置（磨边之后，清洗之前）
    const edgingIndex = stepNames.indexOf('磨边');
    const drillingIndex = stepNames.indexOf('钻孔');
    const cleaningIndex = stepNames.indexOf('清洗');
    
    expect(drillingIndex).toBeGreaterThan(edgingIndex);
    expect(cleaningIndex).toBeGreaterThan(drillingIndex);
    
    // 验证完整流程顺序
    expect(stepNames).toEqual([
      '切割', '磨边', '钻孔', '清洗', '质检', '包装'
    ]);
  });
  
  test('should calculate drilling time correctly', () => {
    const singleHoleSpecs = {
      holePositions: [{ x: 100, y: 100, diameter: 8 }],
      holeDiameters: [8]
    };
    
    const multipleHoleSpecs = {
      holePositions: [
        { x: 50, y: 50, diameter: 8 },
        { x: 100, y: 100, diameter: 12 },
        { x: 150, y: 150, diameter: 8 }
      ],
      holeDiameters: [8, 12, 8]
    };
    
    const singleHoleTime = processFlowGenerator.calculateDrillingTime(
      1, 6, [8]
    );
    const multipleHoleTime = processFlowGenerator.calculateDrillingTime(
      3, 6, [8, 12, 8]
    );
    
    // 单孔时间应该包含基础时间 + 设备准备时间
    expect(singleHoleTime).toBeGreaterThan(5); // 至少5分钟（设备准备时间）
    expect(singleHoleTime).toBeLessThan(10);   // 不超过10分钟
    
    // 多孔时间应该明显更长
    expect(multipleHoleTime).toBeGreaterThan(singleHoleTime * 2);
    
    // 大孔径应该需要更多时间
    const largeHoleTime = processFlowGenerator.calculateDrillingTime(
      1, 6, [20]
    );
    expect(largeHoleTime).toBeGreaterThan(singleHoleTime);
  });
  
  test('should select appropriate drilling station', () => {
    const simpleSpecs = {
      holePositions: [{ x: 100, y: 100, diameter: 6 }],
      holeDiameters: [6]
    };
    
    const complexSpecs = {
      holePositions: [
        { x: 50, y: 50, diameter: 25 },
        { x: 100, y: 100, diameter: 25 },
        { x: 150, y: 150, diameter: 25 }
      ],
      holeDiameters: [25, 25, 25]
    };
    
    const simpleStation = processFlowGenerator.selectDrillingStation(simpleSpecs);
    const complexStation = processFlowGenerator.selectDrillingStation(complexSpecs);
    
    // 简单钻孔使用小型设备
    expect(simpleStation).toBe('drilling_station_1');
    
    // 复杂钻孔使用大型设备
    expect(complexStation).toBe('drilling_station_3');
  });
  
  test('should generate complex flow with drilling and special processes', () => {
    const complexGlass = { 
      thickness: 8, 
      glassType: 'low_e', 
      length: 2400, 
      width: 1600,
      drillingSpecs: {
        required: true,
        holePositions: [
          { x: 100, y: 100, diameter: 8 },
          { x: 200, y: 200, diameter: 8 }
        ],
        holeDiameters: [8, 8],
        drillBitTypes: ['standard'],
        coolingRequired: false,
        precisionLevel: 'high'
      }
    };
    
    const flow = processFlowGenerator.generateStandardFlow(complexGlass);
    const stepNames = flow.map(step => step.stepName);
    
    // 验证完整的复杂工艺流程
    expect(stepNames).toEqual([
      '切割',    // 冷工段
      '磨边', 
      '钻孔', 
      '清洗',
      '镀膜',    // 热工段
      '钢化',
      '质检',    // 终工段
      '包装'
    ]);
    
    // 验证钻孔工序参数
    const drillingStep = flow.find(step => step.stepName === '钻孔');
    expect(drillingStep).toBeDefined();
    expect(drillingStep.parameters.hole_count).toBe(2);
    expect(drillingStep.parameters.hole_diameters).toEqual([8, 8]);
    expect(drillingStep.constraints.precision_level).toBe('high');
  });
});
```

#### 7.3.2 集成测试
```typescript
// E2E测试示例
describe('Production Order Creation Flow', () => {
  test('should create production order successfully', async () => {
    // 1. 访问页面
    await page.goto('/test-production-order');
    
    // 2. 打开工单向导
    await page.click('[data-testid="open-wizard"]');
    
    // 3. 选择订单项
    await page.click('[data-testid="order-item-1"]');
    await page.fill('[data-testid="quantity-input"]', '100');
    
    // 4. 进入下一步
    await page.click('[data-testid="next-step"]');
    
    // 5. 批次优化
    await page.click('[data-testid="optimize-batches"]');
    
    // 6. 创建工单
    await page.click('[data-testid="create-order"]');
    
    // 7. 验证结果
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });
});
```

## 8. 性能优化建议

### 8.1 前端性能优化

#### 8.1.1 组件优化
- 使用`v-memo`缓存大列表渲染
- 实现虚拟滚动处理大量订单项
- 使用`shallowRef`优化大对象的响应式处理

#### 8.1.2 网络优化
- 实现订单列表的分页加载
- 使用防抖处理搜索输入
- 缓存常用的工艺流程模板

### 8.2 后端性能优化

#### 8.2.1 数据库优化
- 为常用查询字段添加索引
- 使用数据库连接池
- 实现查询结果缓存

#### 8.2.2 算法优化
- 批次优化算法使用并行计算
- 工艺流程生成结果缓存
- 大批量数据处理使用异步队列

## 9. 部署和维护

### 9.1 部署配置

#### 9.1.1 环境变量配置
```bash
# 生产环境配置
NODE_ENV=production
VITE_API_BASE_URL=https://api.glass-erp.com
VITE_WS_URL=wss://ws.glass-erp.com

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=glass_erp
DB_USER=glass_user
DB_PASSWORD=secure_password
```

#### 9.1.2 Docker配置
```dockerfile
# 前端Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "preview"]
```

### 9.2 监控和日志

#### 9.2.1 关键指标监控
- 工单创建成功率
- 批次优化耗时
- 用户操作响应时间
- 系统错误率

#### 9.2.2 日志记录
```typescript
// 业务操作日志
logger.info('Production order created', {
  orderId: productionOrder.id,
  userId: user.id,
  itemCount: selectedItems.length,
  batchCount: optimizedBatches.length,
  duration: Date.now() - startTime
});
```

## 10. 总结

本文档详细描述了生产工单创建界面的设计规范和开发指导，特别强调了玻璃深加工行业的原料和规格冲突检测机制。开发团队应该：

### 10.1 核心业务规则
1. **原料兼容性**：严格执行厚度、颜色、玻璃类型的一致性要求
2. **规格兼容性**：控制面积比例≤16:1，确保切割效率≥75%
3. **钻孔工序分离**：有钻孔需求与无钻孔需求的订单项必须分配到不同批次
4. **工艺流程标准化**：
   - 冷工段：切割 → 磨边 → [钻孔] → 清洗
   - 热工段：[镀膜] → [钢化] → [夹胶] → [中空合片]
   - 终工段：质检 → 包装
5. **批次优化**：在满足兼容性约束的前提下进行智能分批

### 10.2 技术实现要点
1. **冲突检测算法**：实现多层次的冲突检测机制
   - 原料冲突：厚度、颜色、玻璃类型检测
   - 规格冲突：面积比例、切割效率检测
   - 钻孔冲突：钻孔需求、孔径种类、孔数量差异检测
   - 工艺冲突：工艺流程兼容性检测
   
2. **批次优化策略**：
   - 预处理阶段：按原料类型进行初步分组
   - 钻孔分离阶段：将有钻孔需求和无钻孔需求的订单项分开
   - 规格兼容性检查：确保同批次规格差异在可接受范围内
   - 优化阶段：在兼容组内进行工艺相似性优化
   - 验证阶段：确保所有批次满足业务约束

3. **工艺流程生成**：
   - 智能识别钻孔需求并插入到正确工序位置
   - 根据钻孔规格选择合适的设备和工作站
   - 准确计算包含钻孔工序的总工时

3. **数据验证体系**：
   - 前端实时验证：提供即时反馈
   - 后端业务验证：确保数据完整性
   - 冲突解决机制：提供自动和手动解决方案

### 10.3 开发指导原则
1. **严格遵循业务规则**：确保所有功能符合玻璃深加工行业的实际需求
2. **注重用户体验**：界面设计要直观易用，冲突提示要清晰明确
3. **保证数据准确性**：实现完善的冲突检测和数据验证机制
4. **考虑系统性能**：冲突检测算法要高效，支持大批量数据处理
5. **便于维护扩展**：模块化设计，便于添加新的冲突检测规则

### 10.4 质量保证
1. **全面测试覆盖**：
   - 单元测试：覆盖所有冲突检测逻辑
   - 集成测试：验证端到端的批次优化流程
   - 边界测试：测试极端情况和边界条件

2. **性能监控**：
   - 冲突检测耗时监控
   - 批次优化成功率统计
   - 用户操作响应时间跟踪

3. **业务指标**：
   - 原料利用率提升
   - 生产效率改善
   - 冲突解决准确率

### 10.4 钻孔工序特别说明

钻孔工序是玻璃深加工中的重要环节，具有以下特点：

#### **工艺位置要求**
- 必须在磨边工序之后进行（确保边缘平整）
- 必须在清洗工序之前完成（避免污染钻孔设备）
- 不能与热工段工序（如钢化）同批次进行

#### **批次分离原则**
- 有钻孔需求的订单项与无钻孔需求的订单项严格分离
- 不同孔径、孔数量的订单项建议分开批次
- 不同精度要求的钻孔订单项必须分开批次

#### **设备和工时考虑**
- 钻孔设备需要专门的准备和调整时间
- 不同孔径需要更换钻头，增加切换成本
- 精密钻孔需要特殊设备和更长工时

#### **质量控制要点**
- 钻孔位置精度直接影响后续装配
- 孔径公差要求严格，需要专门的质检流程
- 钻孔后的清洗工序尤为重要

通过遵循本文档的指导，特别是严格执行原料、规格和钻孔工序的冲突检测规则，开发团队能够构建出既符合玻璃深加工行业特点，又具备智能优化能力的生产工单创建系统。