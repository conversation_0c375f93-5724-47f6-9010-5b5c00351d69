# 产品管理Mock数据和数据服务层使用指南

## 概述

本文档介绍了产品管理模块的Mock数据结构和数据服务层的使用方法。系统采用分层架构设计，提供了完整的组件→构件→产品结构→产品的数据模型和服务接口。

## 数据模型层次结构

```
产品管理数据层次：
├── 组件 (Component)           // 最基础的生产单元
│   ├── 参数定义 (Parameters)
│   ├── 约束条件 (Constraints)
│   └── 工艺要求 (ProcessRequirements)
│
├── 构件 (Assembly)            // 组件的集合体
│   ├── 组件实例 (ComponentInstances)
│   ├── 子构件 (SubAssemblies)
│   ├── 装配工艺 (AssemblyProcess)
│   └── 质量要求 (QualityRequirements)
│
├── 产品结构 (ProductStructure) // 描述产品的层级关系
│   ├── 根构件 (RootAssembly)
│   ├── 产品参数 (ProductParameters)
│   ├── 产品约束 (ProductConstraints)
│   └── 配置选项 (ConfigurationOptions)
│
├── 产品 (Product)             // 基于产品结构的具体产品
│   ├── 物料映射 (ComponentMaterialMap)
│   ├── 默认参数 (DefaultParameters)
│   └── 统计信息 (Statistics)
│
├── 报价BOM (QuoteBOM)         // 基于物料分类的报价清单
│   └── BOM项目 (QuoteBOMItems)
│
└── 生产BOM (ProductionBOM)    // 基于具体物料的生产清单
    └── BOM项目 (ProductionBOMItems)
```

## Mock数据文件结构

### 1. 组件数据 (`public/mock/product/components.json`)

包含系统中所有组件的定义，每个组件包含：

- **基础信息**：ID、编码、名称、描述、类型
- **物料映射**：物料分类ID、名称、编码
- **参数定义**：尺寸、材质、工艺、质量参数
- **约束条件**：尺寸约束、材质约束、工艺约束
- **工艺要求**：加工工艺、时间估算、质量要求

```typescript
// 组件示例
{
  "id": "comp_001",
  "code": "FRAME_OUTER_VERTICAL",
  "name": "外框立柱",
  "componentType": "frame",
  "materialCategoryId": "cat_steel_profile",
  "parameters": [...],
  "constraints": [...],
  "processRequirements": [...]
}
```

### 2. 构件数据 (`public/mock/product/assemblies.json`)

包含构件定义，描述组件的装配关系：

- **组件实例**：引用的组件及其参数值
- **装配工艺**：装配步骤、时间估算、质量检查点
- **质量要求**：装配质量标准和验收标准

```typescript
// 构件示例
{
  "id": "asm_001",
  "code": "FRAME_MAIN_ASSEMBLY",
  "name": "主框架构件",
  "componentInstances": [...],
  "assemblyProcess": {
    "steps": [...],
    "qualityCheckpoints": [...]
  }
}
```

### 3. 产品结构数据 (`public/mock/product/product-structures.json`)

定义产品的整体结构和配置选项：

- **根构件**：产品的主要构件
- **产品参数**：影响整个产品的参数
- **配置选项**：不同的产品配置方案
- **版本历史**：结构变更记录

### 4. 产品数据 (`public/mock/product/products.json`)

基于产品结构的具体产品定义：

- **结构引用**：关联的产品结构
- **物料映射**：组件到物料分类的映射关系
- **默认参数**：产品的默认配置参数
- **统计信息**：BOM数量、订单数量等

### 5. BOM数据

- **报价BOM** (`public/mock/product/quote-boms.json`)：基于物料分类的成本估算
- **生产BOM** (`public/mock/product/production-boms.json`)：基于具体物料的生产清单

## 数据服务层使用

### 1. 组件服务 (ComponentService)

```typescript
import { componentService } from '@/services/productService';

// 获取所有组件
const components = await componentService.getComponents();

// 按条件筛选组件
const frameComponents = await componentService.getComponents({
  componentType: 'frame',
  status: 'active'
});

// 获取单个组件
const component = await componentService.getComponentById('comp_001');

// 创建新组件
const newComponent = await componentService.createComponent({
  code: 'NEW_COMPONENT',
  name: '新组件',
  componentType: 'other'
});

// 验证参数
const validationResults = await componentService.validateParameters(
  'comp_001',
  { width: 80, thickness: 4.0 }
);

// 计算数量
const quantity = await componentService.calculateQuantity(
  'comp_001',
  { window_height: 1800 }
);
```

### 2. 构件服务 (AssemblyService)

```typescript
import { assemblyService } from '@/services/productService';

// 获取构件列表
const assemblies = await assemblyService.getAssemblies({
  assemblyType: 'frame_assembly'
});

// 创建新构件
const newAssembly = await assemblyService.createAssembly({
  code: 'NEW_ASSEMBLY',
  name: '新构件',
  assemblyType: 'complete_assembly'
});
```

### 3. 产品结构服务 (ProductStructureService)

```typescript
import { productStructureService } from '@/services/productService';

// 获取产品结构
const structures = await productStructureService.getStructures({
  productType: 'window',
  category: '防火窗'
});

// 获取单个产品结构
const structure = await productStructureService.getStructureById('struct_001');
```

### 4. 产品服务 (ProductService)

```typescript
import { productService } from '@/services/productService';

// 获取产品列表
const products = await productService.getProducts({
  category: '防火窗',
  lifecycle: 'mass_production'
});

// 创建新产品
const newProduct = await productService.createProduct({
  code: 'NEW_PRODUCT',
  name: '新产品',
  productStructureId: 'struct_001'
});
```

### 5. BOM服务

```typescript
import { quoteBOMService, productionBOMService } from '@/services/productService';

// 获取报价BOM
const quoteBOMs = await quoteBOMService.getQuoteBOMs({
  productId: 'prod_001'
});

// 生成报价BOM
const quoteBOM = await quoteBOMService.generateQuoteBOM(
  'prod_001',
  configuration
);

// 转换为生产BOM
const productionBOM = await productionBOMService.convertFromQuoteBOM('qbom_001');
```

## Pinia状态管理使用

### 1. 组件状态管理

```typescript
import { useComponentStore } from '@/stores/productComponentStore';

const componentStore = useComponentStore();

// 加载组件数据
await componentStore.loadComponents({ componentType: 'frame' });

// 访问状态
const components = componentStore.filteredComponents;
const loading = componentStore.loading;
const error = componentStore.error;

// 创建组件
const newComponent = await componentStore.createComponent({
  code: 'NEW_COMP',
  name: '新组件'
});
```

### 2. 其他Store使用

```typescript
// 构件管理
import { useAssemblyStore } from '@/stores/productAssemblyStore';
const assemblyStore = useAssemblyStore();
await assemblyStore.loadAssemblies();

// 产品结构管理
import { useProductStructureStore } from '@/stores/productStructureStore';
const structureStore = useProductStructureStore();
await structureStore.loadStructures();

// 产品管理
import { useProductStore } from '@/stores/productStore';
const productStore = useProductStore();
await productStore.loadProducts();

// BOM管理
import { useBOMStore } from '@/stores/productBOMStore';
const bomStore = useBOMStore();
await bomStore.loadQuoteBOMs();
await bomStore.loadProductionBOMs();
```

### 3. 统一导入方式

```typescript
// 从统一导出文件导入所有Store
import {
  useProductStore,
  useComponentStore,
  useAssemblyStore,
  useProductStructureStore,
  useBOMStore
} from '@/stores/product';
```

## 工具函数使用

### 1. 参数验证

```typescript
import { ParameterValidator } from '@/utils/productUtils';

const validationResults = ParameterValidator.validateParameters(
  parameters,
  values
);
```

### 2. 约束求解

```typescript
import { ConstraintSolver } from '@/utils/productUtils';

const constraintResults = ConstraintSolver.validateConstraints(
  constraints,
  values
);

const fixedValues = ConstraintSolver.autoFixConstraints(
  constraints,
  values
);
```

### 3. 公式计算

```typescript
import { FormulaCalculator } from '@/utils/productUtils';

const result = FormulaCalculator.calculate(
  'ceiling(window_height / 6000) * 2',
  { window_height: 1800 }
);
```

### 4. 数据格式化

```typescript
import { DataFormatter } from '@/utils/productUtils';

const formattedNumber = DataFormatter.formatNumber(123.456, 2, 'mm');
const formattedCurrency = DataFormatter.formatCurrency(1234.56);
const formattedDate = DataFormatter.formatDate('2024-01-01T00:00:00Z');
const statusInfo = DataFormatter.formatStatus('active');
```

## 测试

运行测试以验证数据服务的正确性：

```bash
npm run test src/tests/productService.test.ts
```

测试覆盖了：
- Mock数据加载
- 服务方法调用
- 数据筛选和查询
- 错误处理
- 数据验证

## 扩展指南

### 1. 添加新的组件类型

1. 在 `src/types/product.ts` 中扩展 `componentType` 枚举
2. 在 `public/mock/product/components.json` 中添加示例数据
3. 更新相关的筛选和验证逻辑

### 2. 添加新的约束类型

1. 扩展 `ComponentConstraint` 接口
2. 在 `ConstraintSolver` 中添加新的约束评估逻辑
3. 更新Mock数据中的约束示例

### 3. 添加新的服务方法

1. 在相应的服务类中添加新方法
2. 在Store中添加对应的状态管理方法
3. 编写相应的测试用例

## 注意事项

1. **数据一致性**：确保Mock数据中的ID引用关系正确
2. **类型安全**：使用TypeScript类型定义确保数据结构正确
3. **错误处理**：服务方法应该包含适当的错误处理逻辑
4. **性能考虑**：大量数据时考虑分页和懒加载
5. **缓存策略**：MockDataLoader提供了简单的缓存机制

## 后续开发

当需要连接真实后端API时：

1. 替换 `MockDataLoader` 为真实的HTTP客户端
2. 更新服务方法中的数据获取逻辑
3. 添加认证和授权处理
4. 实现数据同步和冲突解决机制
