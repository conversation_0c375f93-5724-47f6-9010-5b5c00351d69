# 产品管理模块设计文档

## 1. 概述

### 1.1 设计目标
基于玻璃深加工行业的MTO（Make-to-Order）生产模式，设计一套完整的产品管理系统，支持从客户订单驱动的产品配置、BOM生成到生产工单创建的全流程管理。系统采用双层架构设计，既满足设计工程师的专业配置需求，又为普通业务人员提供简洁易用的操作界面。

### 1.2 设计理念与用户分层

#### 1.2.1 核心理念
- **组件**：生产的基本单元，可直接与物料分类映射
- **构件**：组件的集合体，体现装配与层级关系
- **产品结构**：描述构件、组件信息的层级关系与装配逻辑
- **产品模板**：基于产品结构的标准化产品定义，普通用户的操作对象
- **产品配置**：基于产品模板的个性化配置，支持参数化定制
- **BOM**：分为报价BOM（基于物料分类）和生产BOM（具体物料变体）

#### 1.2.2 用户分层架构

```typescript
// 双层用户架构
用户角色体系：
├── 设计工程师(专业层)
│   ├── 产品结构设计      // 组件→构件→产品结构的专业配置
│   ├── 物料映射管理      // componentMaterialMap的专业配置  
│   ├── 业务规则设置      // 计算公式、约束条件配置
│   ├── 产品模板创建      // 将复杂结构封装为用户友好的模板
│   └── 模板发布审核      // 确保配置正确后发布给普通用户
│
└── 业务人员(应用层)  
    ├── 产品模板选择      // 从现成模板中选择，无需理解内部结构
    ├── 参数化配置        // 简单的尺寸、颜色等参数输入
    ├── 一键报价生成      // 自动生成BOM和报价，无需理解计算逻辑
    ├── 订单状态跟踪      // 可视化的订单进度管理
    └── 物料选择确认      // 在工程师推荐清单中选择具体物料
```

### 1.3 业务价值与用户友好性

#### 1.3.1 专业层价值
- 支持复杂产品结构的层级化管理
- 灵活的业务规则和计算公式配置
- 完整的版本管理和变更追溯
- 专业的物料映射和成本控制

#### 1.3.2 应用层价值  
- **降低使用门槛**：普通用户经过简单培训即可熟练操作
- **提升操作效率**：向导式流程，减少用户决策点
- **减少操作错误**：智能提示和自动修正机制
- **优化用户体验**：角色化界面，隐藏不相关的复杂性

#### 1.3.3 整体业务价值
- 实现订单驱动的个性化产品配置
- 提供精确的成本核算和报价支持
- 确保生产BOM的物料精准性
- 支持产品全生命周期的版本管理和追溯

## 2. 数据模型设计

### 2.1 核心实体关系

```mermaid
erDiagram
    ProductStructure ||--o{ ProductTemplate : "一对多"
    ProductTemplate ||--o{ ProductConfiguration : "一对多"
    ProductConfiguration ||--o{ QuoteBOM : "一对多"
    ProductConfiguration ||--o{ ProductionBOM : "一对多"
    QuoteBOM ||--o| ProductionBOM : "可转换"
    ProductStructure ||--o{ Assembly : "包含"
    Assembly ||--o{ Component : "包含"
    Assembly ||--o{ Assembly : "嵌套"
    Component }o--|| MaterialCategory : "映射"
    ProductionBOMItem }o--|| MaterialVariant : "引用"
    ProductTemplate ||--o{ ParameterDefinition : "包含"
    ProductConfiguration ||--o{ ParameterValue : "包含"
```

### 2.2 TypeScript 接口定义

#### 2.2.1 基础类型

```typescript
// 版本化实体基类
export interface VersionedEntity {
  id: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

// 状态枚举
export enum LifecycleStatus {
  DRAFT = 'draft',           // 草稿
  ACTIVE = 'active',         // 激活
  DEPRECATED = 'deprecated', // 已弃用
  ARCHIVED = 'archived'      // 已归档
}

export enum BOMStatus {
  DRAFT = 'draft',       // 草稿
  CONFIRMED = 'confirmed', // 已确认
  APPROVED = 'approved',   // 已批准
  RELEASED = 'released'    // 已下发
}

// 用户角色枚举
export enum UserRole {
  DESIGN_ENGINEER = 'design_engineer',  // 设计工程师
  SALES_PERSON = 'sales_person',        // 销售人员
  TECHNICIAN = 'technician',            // 技术人员
  PRODUCTION_MANAGER = 'production_manager'  // 生产管理员
}
```

#### 2.2.2 组件定义（专业层）

```typescript
// 组件定义（生产基本单元）- 设计工程师操作
export interface Component extends VersionedEntity {
  code: string;                    // 组件编码
  name: string;                    // 组件名称
  description?: string;            // 描述
  materialCategoryId: string;      // 关联物料分类ID
  materialCategoryName: string;    // 物料分类名称（冗余字段）
  
  // 参数定义
  parameters: ComponentParameter[];
  
  // 计算规则
  quantityFormula?: string;        // 数量计算公式
  costFormula?: string;           // 成本计算公式
  
  // 约束条件
  constraints: ComponentConstraint[];
  
  // 状态
  status: LifecycleStatus;
  
  // 扩展属性
  properties: Record<string, any>;
}

// 组件参数
export interface ComponentParameter {
  id: string;
  name: string;
  displayName: string;             // 用户友好的显示名称
  type: 'number' | 'string' | 'boolean' | 'select';
  unit?: string;
  defaultValue?: any;
  minValue?: number;
  maxValue?: number;
  options?: ParameterOption[];
  required: boolean;
  description?: string;
  category: 'basic' | 'advanced' | 'internal';  // 参数分类
}

// 参数选项
export interface ParameterOption {
  value: any;
  label: string;
  description?: string;
  additionalCost?: number;         // 额外成本
}

// 组件约束
export interface ComponentConstraint {
  id: string;
  name: string;
  expression: string;             // 约束表达式
  errorMessage: string;
  severity: 'error' | 'warning';
  autoFix?: {                     // 自动修正建议
    enabled: boolean;
    fixExpression: string;
    fixMessage: string;
  };
}
```

#### 2.2.3 构件定义（专业层）

```typescript
// 构件定义（组件集合体）- 设计工程师操作
export interface Assembly extends VersionedEntity {
  code: string;
  name: string;
  description?: string;
  
  // 组件实例
  componentInstances: ComponentInstance[];
  
  // 子构件
  subAssemblies: AssemblyInstance[];
  
  // 装配参数
  assemblyParameters: ComponentParameter[];
  
  // 状态
  status: LifecycleStatus;
  
  // 装配约束
  assemblyConstraints: ComponentConstraint[];
}

// 组件实例
export interface ComponentInstance {
  id: string;
  componentId: string;
  componentCode: string;
  componentName: string;
  
  // 实例参数值
  parameterValues: Record<string, any>;
  
  // 数量
  quantity: number;
  quantityFormula?: string;
  
  // 是否可选
  optional: boolean;
  
  // 条件显示
  displayCondition?: string;       // 何时显示此组件的条件表达式
}

// 构件实例
export interface AssemblyInstance {
  id: string;
  assemblyId: string;
  assemblyCode: string;
  assemblyName: string;
  
  // 实例参数值
  parameterValues: Record<string, any>;
  
  // 数量
  quantity: number;
  quantityFormula?: string;
  
  // 是否可选
  optional: boolean;
  
  // 条件显示
  displayCondition?: string;
}
```

#### 2.2.4 产品模板定义（应用层接口）

```typescript
// 产品模板 - 普通用户操作的简化接口
export interface ProductTemplate extends VersionedEntity {
  code: string;
  name: string;
  displayName: string;             // 用户友好的显示名称
  description: string;
  category: string;
  subCategory: string;
  
  // 关联的产品结构（对用户隐藏）
  productStructureId: string;
  
  // 用户可配置的参数（简化）
  userParameters: UserParameter[];
  
  // 预览信息
  previewImage?: string;
  specifications: TemplateSpecification[];
  
  // 价格范围
  priceRange: {
    min: number;
    max: number;
    currency: string;
  };
  
  // 交期信息
  leadTimeInfo: {
    standard: number;              // 标准交期（天）
    rush: number;                  // 加急交期（天）
    rushSurcharge: number;         // 加急附加费率
  };
  
  // 状态
  status: LifecycleStatus;
  
  // 适用场景
  applications: string[];
  
  // 标签
  tags: string[];
}

// 用户参数（简化的参数定义）
export interface UserParameter {
  id: string;
  name: string;
  displayName: string;
  type: 'dimension' | 'material' | 'feature' | 'option';
  inputType: 'number' | 'select' | 'multiSelect' | 'boolean';
  unit?: string;
  defaultValue?: any;
  minValue?: number;
  maxValue?: number;
  options?: ParameterOption[];
  required: boolean;
  helpText?: string;
  group: string;                   // 参数分组
  order: number;                   // 显示顺序
  
  // 智能提示
  validation?: {
    rules: ValidationRule[];
    suggestions: string[];
  };
}

// 验证规则
export interface ValidationRule {
  type: 'range' | 'dependency' | 'constraint';
  expression: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

// 模板规格
export interface TemplateSpecification {
  name: string;
  value: string;
  unit?: string;
  category: string;
}
```

#### 2.2.5 产品配置定义

```typescript
// 产品配置 - 基于模板的具体配置
export interface ProductConfiguration extends VersionedEntity {
  templateId: string;
  templateCode: string;
  templateName: string;
  
  configurationName: string;
  
  // 用户输入的参数值
  parameterValues: Record<string, any>;
  
  // 系统计算的值（对用户只读）
  calculatedValues: Record<string, any>;
  
  // 验证结果
  validationResults: ValidationResult[];
  
  // 成本估算
  costEstimate: {
    materialCost: number;
    laborCost: number;
    overheadCost: number;
    totalCost: number;
    profitMargin: number;
    finalPrice: number;
  };
  
  // 来源信息
  sourceType: 'manual' | 'order' | 'quote';
  sourceId?: string;
  
  // 状态
  status: 'draft' | 'validated' | 'quoted' | 'confirmed';
  
  // 备注
  notes?: string;
}

// 验证结果
export interface ValidationResult {
  type: 'error' | 'warning' | 'info';
  message: string;
  field?: string;
  suggestedAction?: string;
}
```

## 3. 路由与导航设计

### 3.1 分层路由结构

基于用户角色的分层导航设计，专业功能和日常操作功能分离。

```typescript
// 产品管理模块路由 - 分层设计
const productRoutes: RouteRecordRaw[] = [
  // 产品管理主页（概览）- 所有用户可见
  {
    path: "/product-management",
    name: "product-management",
    component: () => import("../views/product/ProductManagementOverview.vue"),
    meta: {
      title: "产品管理",
      icon: "Package",
      requiresAuth: true,
    },
  },
  
  // 应用层路由 - 普通用户日常操作
  {
    path: "/product-management/templates",
    name: "product-templates",
    component: () => import("../views/product/ProductTemplates.vue"),
    meta: {
      title: "产品模板",
      icon: "LayoutTemplate",
      requiresAuth: true,
      parent: "product-management",
      userLevel: "application",
    },
  },
  
  {
    path: "/product-management/configurations",
    name: "product-configurations",
    component: () => import("../views/product/ProductConfigurations.vue"),
    meta: {
      title: "产品配置",
      icon: "Settings",
      requiresAuth: true,
      parent: "product-management",
      userLevel: "application",
    },
  },
  
  {
    path: "/product-management/configurator/:templateId?",
    name: "product-configurator",
    component: () => import("../views/product/ProductConfigurator.vue"),
    meta: {
      title: "产品配置器",
      icon: "Wrench",
      requiresAuth: true,
      parent: "product-management",
      userLevel: "application",
      hideInMenu: true,
    },
  },
  
  // 专业层路由 - 设计工程师专用
  {
    path: "/product-management/design",
    name: "product-design",
    component: () => import("../views/product/ProductDesign.vue"),
    meta: {
      title: "产品设计",
      icon: "PenTool",
      requiresAuth: true,
      parent: "product-management",
      userLevel: "professional",
      roles: ["design_engineer"],
    },
    children: [
      {
        path: "components",
        name: "product-components",
        component: () => import("../views/product/Components.vue"),
        meta: {
          title: "组件管理",
          icon: "Component",
          requiresAuth: true,
          userLevel: "professional",
        },
      },
      {
        path: "assemblies",
        name: "product-assemblies",
        component: () => import("../views/product/Assemblies.vue"),
        meta: {
          title: "构件管理",
          icon: "Layers",
          requiresAuth: true,
          userLevel: "professional",
        },
      },
      {
        path: "structures",
        name: "product-structures",
        component: () => import("../views/product/ProductStructures.vue"),
        meta: {
          title: "产品结构",
          icon: "Workflow",
          requiresAuth: true,
          userLevel: "professional",
        },
      },
    ],
  },
  
  // BOM管理 - 技术人员和工程师
  {
    path: "/product-management/quote-boms",
    name: "quote-boms",
    component: () => import("../views/product/QuoteBOMs.vue"),
    meta: {
      title: "报价BOM",
      icon: "BadgeDollarSign",
      requiresAuth: true,
      parent: "product-management",
      userLevel: "application",
    },
  },
  
  {
    path: "/product-management/production-boms",
    name: "production-boms",
    component: () => import("../views/product/ProductionBOMs.vue"),
    meta: {
      title: "生产BOM",
      icon: "Hammer",
      requiresAuth: true,
      parent: "product-management",
      userLevel: "application",
    },
  },
];
```

### 3.2 智能导航菜单配置

根据用户角色动态显示菜单项，避免界面混乱。

```typescript
// 导航菜单配置 - 基于角色的动态菜单
export const productManagementMenu = {
  id: "product-management",
  title: "产品管理",
  icon: "Package",
  children: [
    // 应用层菜单 - 所有用户可见
    {
      id: "product-templates",
      title: "产品选择",
      icon: "LayoutTemplate",
      path: "/product-management/templates",
      userLevel: "application",
      description: "选择和浏览产品模板"
    },
    {
      id: "product-configurations",
      title: "我的配置",
      icon: "Settings",
      path: "/product-management/configurations",
      userLevel: "application",
      description: "管理产品配置和报价"
    },
    {
      id: "quote-boms",
      title: "报价管理",
      icon: "BadgeDollarSign",
      path: "/product-management/quote-boms",
      userLevel: "application",
      description: "查看和管理报价BOM"
    },
    {
      id: "production-boms",
      title: "生产准备",
      icon: "Hammer",
      path: "/product-management/production-boms",
      userLevel: "application",
      description: "生产BOM和工单管理"
    },
    
    // 专业层菜单 - 仅设计工程师可见
    {
      id: "product-design",
      title: "产品设计",
      icon: "PenTool",
      path: "/product-management/design",
      userLevel: "professional",
      roles: ["design_engineer"],
      description: "专业的产品结构设计工具",
      children: [
        {
          id: "product-components",
          title: "组件库",
          icon: "Component",
          path: "/product-management/design/components",
        },
        {
          id: "product-assemblies",
          title: "构件库",
          icon: "Layers",
          path: "/product-management/design/assemblies",
        },
        {
          id: "product-structures",
          title: "产品结构",
          icon: "Workflow",
          path: "/product-management/design/structures",
        },
      ],
    },
  ],
};

// 菜单过滤函数
export function filterMenuByUser(menu: any, userRoles: string[], userLevel: string) {
  return menu.children.filter((item: any) => {
    // 检查用户级别
    if (item.userLevel === 'professional' && userLevel !== 'professional') {
      return false;
    }
    
    // 检查角色权限
    if (item.roles && !item.roles.some((role: string) => userRoles.includes(role))) {
      return false;
    }
    
    return true;
  });
}
```

## 4. 页面功能设计

### 4.1 应用层页面（普通用户）

#### 4.1.1 产品模板选择页面

**设计理念**：像购物APP一样简单直观，隐藏技术复杂性

**页面布局**：
```typescript
// 产品模板选择界面设计
interface TemplateSelectionUI {
  // 搜索和筛选区
  searchFilters: {
    quickSearch: string;           // 关键字搜索
    categoryFilter: string[];      // 产品类别
    priceRange: [number, number];  // 价格范围
    leadTimeFilter: number;        // 交期要求
    applicationFilter: string[];   // 应用场景
  };
  
  // 产品展示区
  templateDisplay: {
    viewMode: 'grid' | 'list';     // 网格或列表视图
    sortBy: 'popular' | 'price' | 'leadTime' | 'newest';
    templates: TemplateCard[];
  };
  
  // 快速配置区
  quickConfig: {
    basicParameters: UserParameter[];  // 基础参数快速输入
    instantPreview: boolean;           // 实时预览
  };
}

// 产品模板卡片
interface TemplateCard {
  id: string;
  displayName: string;
  description: string;
  previewImage: string;
  priceRange: string;               // "¥1,200 - ¥2,800"
  leadTime: string;                 // "5-7天"
  popularityScore: number;          // 受欢迎程度
  tags: string[];                   // ["防火", "隔断", "A级"]
  specifications: {
    key: string;
    value: string;
  }[];
}
```

**操作流程**：
1. **产品浏览**：可视化展示，支持筛选和搜索
2. **快速预览**：点击查看详细规格和价格
3. **一键配置**：选择模板后进入配置向导
4. **收藏管理**：支持收藏常用模板

#### 4.1.2 产品配置器页面

**设计理念**：向导式配置，分步骤引导用户完成配置

**界面设计**：
```typescript
// 产品配置器界面
interface ProductConfiguratorUI {
  // 步骤导航
  stepNavigation: {
    currentStep: number;
    totalSteps: number;
    steps: ConfigurationStep[];
  };
  
  // 参数配置区
  parameterSection: {
    groups: ParameterGroup[];      // 分组的参数
    validation: ValidationState;   // 实时验证状态
    suggestions: Suggestion[];     // 智能建议
  };
  
  // 实时预览区
  previewSection: {
    visualPreview?: string;        // 可视化预览
    specificationSummary: SpecSummary;  // 规格摘要
    costEstimate: CostBreakdown;   // 成本分解
  };
  
  // 操作区
  actionSection: {
    saveConfig: boolean;           // 保存配置
    generateQuote: boolean;        // 生成报价
    shareConfig: boolean;          // 分享配置
  };
}

// 配置步骤
interface ConfigurationStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  completed: boolean;
  hasErrors: boolean;
}

// 参数分组
interface ParameterGroup {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  parameters: UserParameter[];
  collapsed: boolean;
}
```

**智能化特性**：
```typescript
// 智能提示和验证
interface SmartFeatures {
  // 实时验证
  validation: {
    rules: ValidationRule[];
    autoFix: boolean;              // 自动修正
    suggestions: string[];
  };
  
  // 智能推荐
  recommendations: {
    parameterSuggestions: ParameterSuggestion[];  // 参数建议
    costOptimization: CostOptimizationTip[];      // 成本优化提示
    technicalAdvice: TechnicalAdvice[];           // 技术建议
  };
  
  // 约束处理
  constraintHandling: {
    conflictDetection: boolean;    // 冲突检测
    autoResolution: boolean;       // 自动解决
    userChoiceRequired: boolean;   // 需要用户选择
  };
}
```

#### 4.1.3 配置管理页面

**功能特性**：
- **配置历史**：查看和管理历史配置
- **版本比较**：对比不同配置版本
- **快速复制**：基于现有配置创建新配置
- **分享协作**：与团队成员分享配置

#### 4.1.4 报价BOM管理页面（简化版）

**用户视角**：
```typescript
// 简化的BOM视图
interface SimplifiedBOMView {
  // 概览信息
  summary: {
    configurationName: string;
    totalCost: number;
    totalPrice: number;
    profitMargin: number;
    validUntil: string;
  };
  
  // 成本分解（简化）
  costBreakdown: {
    materials: number;
    labor: number;
    overhead: number;
    profit: number;
  };
  
  // 主要材料清单（隐藏技术细节）
  majorMaterials: {
    category: string;
    description: string;
    quantity: string;
    cost: number;
  }[];
  
  // 操作按钮
  actions: {
    exportQuote: boolean;          // 导出报价单
    sendToCustomer: boolean;       // 发送给客户
    createOrder: boolean;          // 创建订单
    adjustPricing: boolean;        // 调整定价
  };
}
```

### 4.2 专业层页面（设计工程师）

#### 4.2.1 组件管理页面

**专业功能**：
- **组件库管理**：创建、编辑、版本控制
- **参数定义**：复杂参数类型和约束
- **公式编辑**：数量和成本计算公式
- **测试验证**：组件配置测试

#### 4.2.2 产品结构设计页面

**高级功能**：
- **可视化设计器**：拖拽式结构设计
- **规则引擎**：复杂业务规则配置
- **模板生成器**：将结构转换为用户模板
- **影响分析**：结构变更的影响分析

#### 4.2.3 模板发布管理

**发布流程**：
```typescript
// 模板发布工作流
interface TemplatePublishingWorkflow {
  // 模板创建
  creation: {
    structureSelection: string;    // 选择产品结构
    userParameterMapping: UserParameterMapping[];  // 参数映射
    displayConfiguration: DisplayConfig;           // 显示配置
  };
  
  // 测试验证
  testing: {
    parameterValidation: boolean;  // 参数验证
    configurationTest: boolean;    // 配置测试
    costCalculationTest: boolean;  // 成本计算测试
  };
  
  // 审核发布
  approval: {
    technicalReview: boolean;      // 技术审核
    businessApproval: boolean;     // 业务批准
    versionControl: boolean;       // 版本控制
  };
}
```

### 4.3 智能错误处理设计

#### 4.3.1 用户友好的错误提示

```typescript
// 错误处理界面设计
interface ErrorHandlingUI {
  // 错误分类
  errorTypes: {
    validation: 'user_input' | 'business_rule' | 'constraint';
    severity: 'error' | 'warning' | 'info';
    source: 'frontend' | 'backend' | 'calculation';
  };
  
  // 错误展示
  errorDisplay: {
    icon: string;                  // 错误图标
    title: string;                 // 错误标题
    message: string;               // 用户友好的错误信息
    technicalDetails?: string;     // 技术详情（可折叠）
  };
  
  // 解决方案
  solutions: {
    autoFix?: AutoFixOption;       // 自动修复选项
    userActions: UserAction[];     // 用户可执行的操作
    helpResources: HelpResource[]; // 帮助资源
  };
}

// 自动修复选项
interface AutoFixOption {
  description: string;
  impact: string;                  // 修复的影响
  confirmRequired: boolean;        // 是否需要用户确认
  action: () => Promise<void>;
}

// 用户操作
interface UserAction {
  label: string;
  description: string;
  icon: string;
  severity: 'primary' | 'secondary' | 'warning';
  action: () => void;
}
```

#### 4.3.2 智能建议系统

```typescript
// 智能建议界面
interface SmartSuggestionUI {
  // 建议类型
  suggestionTypes: {
    optimization: 'cost' | 'performance' | 'delivery';
    compatibility: 'material' | 'process' | 'constraint';
    improvement: 'efficiency' | 'quality' | 'compliance';
  };
  
  // 建议展示
  suggestionCard: {
    type: string;
    title: string;
    description: string;
    impact: {
      cost?: number;               // 成本影响
      time?: number;               // 时间影响
      quality?: string;            // 质量影响
    };
    confidence: number;            // 建议可信度 0-100
    actions: SuggestionAction[];
  };
}
```

## 5. Store状态管理设计

### 5.1 分层Store架构

基于用户分层的Store设计，专业层和应用层数据分离。

```typescript
// 应用层Store - 普通用户使用
export const useProductTemplateStore = defineStore('productTemplate', () => {
  // 状态
  const templates = ref<ProductTemplate[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const currentTemplate = ref<ProductTemplate | null>(null);

  // 缓存和计算属性
  const templateMap = computed(() => {
    return templates.value.reduce((map, template) => {
      map[template.id] = template;
      return map;
    }, {} as Record<string, ProductTemplate>);
  });

  const templatesByCategory = computed(() => {
    return templates.value.reduce((groups, template) => {
      const category = template.category;
      if (!groups[category]) groups[category] = [];
      groups[category].push(template);
      return groups;
    }, {} as Record<string, ProductTemplate[]>);
  });

  // 用户友好的方法
  const loadTemplates = async (filters?: TemplateFilter) => {
    loading.value = true;
    try {
      const data = await templateApi.getTemplates(filters);
      templates.value = data;
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  const searchTemplates = async (query: string) => {
    const filtered = templates.value.filter(template => 
      template.displayName.toLowerCase().includes(query.toLowerCase()) ||
      template.description.toLowerCase().includes(query.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );
    return filtered;
  };

  const getPopularTemplates = (limit = 6) => {
    return templates.value
      .sort((a, b) => b.popularityScore - a.popularityScore)
      .slice(0, limit);
  };

  return {
    // 状态
    templates: readonly(templates),
    loading: readonly(loading),
    error: readonly(error),
    currentTemplate: readonly(currentTemplate),

    // 计算属性
    templateMap,
    templatesByCategory,

    // 方法
    loadTemplates,
    searchTemplates,
    getPopularTemplates,
  };
});

// 产品配置Store - 普通用户使用
export const useProductConfigurationStore = defineStore('productConfiguration', () => {
  const configurations = ref<ProductConfiguration[]>([]);
  const currentConfiguration = ref<ProductConfiguration | null>(null);
  const validationResults = ref<ValidationResult[]>([]);

  // 智能配置生成
  const createConfigurationFromTemplate = async (
    templateId: string,
    initialValues?: Record<string, any>
  ) => {
    const template = useProductTemplateStore().templateMap[templateId];
    if (!template) throw new Error('模板不存在');

    const configuration: ProductConfiguration = {
      id: generateId(),
      templateId,
      templateCode: template.code,
      templateName: template.displayName,
      configurationName: `${template.displayName} - ${new Date().toLocaleDateString()}`,
      parameterValues: {
        ...template.defaultParameters,
        ...initialValues,
      },
      calculatedValues: {},
      validationResults: [],
      status: 'draft',
      costEstimate: {
        materialCost: 0,
        laborCost: 0,
        overheadCost: 0,
        totalCost: 0,
        profitMargin: 0,
        finalPrice: 0,
      },
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current-user',
      updatedBy: 'current-user',
    };

    // 自动计算和验证
    await calculateAndValidateConfiguration(configuration);
    
    configurations.value.push(configuration);
    currentConfiguration.value = configuration;
    
    return configuration;
  };

  // 智能参数验证和计算
  const calculateAndValidateConfiguration = async (config: ProductConfiguration) => {
    const template = useProductTemplateStore().templateMap[config.templateId];
    
    // 参数验证
    const validationResults = validateParameters(template, config.parameterValues);
    config.validationResults = validationResults;
    
    // 成本计算
    if (validationResults.every(r => r.type !== 'error')) {
      const costEstimate = await calculateCost(template, config.parameterValues);
      config.costEstimate = costEstimate;
      config.status = 'validated';
    }

    config.updatedAt = new Date().toISOString();
  };

  // 用户友好的参数更新
  const updateParameter = async (
    configId: string,
    parameterName: string,
    value: any
  ) => {
    const config = configurations.value.find(c => c.id === configId);
    if (!config) return;

    config.parameterValues[parameterName] = value;
    
    // 实时验证和计算
    await calculateAndValidateConfiguration(config);
  };

  return {
    configurations: readonly(configurations),
    currentConfiguration: readonly(currentConfiguration),
    validationResults: readonly(validationResults),
    
    createConfigurationFromTemplate,
    calculateAndValidateConfiguration,
    updateParameter,
  };
});

// 专业层Store - 设计工程师使用
export const useComponentStore = defineStore('component', () => {
  const components = ref<Component[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 专业功能方法
  const createComponent = async (component: CreateComponentRequest) => {
    // 复杂的组件创建逻辑
    const newComponent = await componentApi.createComponent(component);
    components.value.push(newComponent);
    return newComponent;
  };

  const updateComponentFormula = async (
    id: string,
    formulaType: 'quantity' | 'cost',
    formula: string
  ) => {
    const component = components.value.find(c => c.id === id);
    if (!component) return;

    if (formulaType === 'quantity') {
      component.quantityFormula = formula;
    } else {
      component.costFormula = formula;
    }

    // 验证公式正确性
    const isValid = await validateFormula(formula);
    if (!isValid) {
      throw new Error('公式语法错误');
    }

    await componentApi.updateComponent(id, { [formulaType + 'Formula']: formula });
  };

  return {
    components: readonly(components),
    loading: readonly(loading),
    error: readonly(error),
    
    createComponent,
    updateComponentFormula,
  };
});
```

### 5.2 智能业务逻辑集成

```typescript
// 智能业务服务Store
export const useSmartBusinessStore = defineStore('smartBusiness', () => {
  // 智能推荐系统
  const getParameterRecommendations = async (
    templateId: string,
    currentValues: Record<string, any>
  ): Promise<ParameterSuggestion[]> => {
    const template = useProductTemplateStore().templateMap[templateId];
    const suggestions: ParameterSuggestion[] = [];

    // 基于历史数据的推荐
    const historicalData = await getHistoricalConfigurations(templateId);
    
    // 成本优化建议
    const costOptimizations = await analyzeCostOptimization(template, currentValues);
    suggestions.push(...costOptimizations);

    // 技术兼容性建议
    const compatibilityChecks = await checkTechnicalCompatibility(currentValues);
    suggestions.push(...compatibilityChecks);

    return suggestions;
  };

  // 智能错误处理
  const handleConfigurationError = async (
    error: ValidationResult,
    configuration: ProductConfiguration
  ): Promise<ErrorResolution> => {
    const resolutions: ErrorResolution = {
      autoFix: null,
      userActions: [],
      helpResources: [],
    };

    // 尝试自动修复
    if (error.type === 'error') {
      const autoFix = await generateAutoFix(error, configuration);
      if (autoFix) {
        resolutions.autoFix = autoFix;
      }
    }

    // 生成用户操作建议
    const userActions = generateUserActions(error, configuration);
    resolutions.userActions = userActions;

    // 提供帮助资源
    const helpResources = await getHelpResources(error.type, error.field);
    resolutions.helpResources = helpResources;

    return resolutions;
  };

  // 智能物料选择
  const selectOptimalMaterials = async (
    quoteBOM: QuoteBOM,
    preferences: MaterialSelectionPreferences
  ): Promise<MaterialSelectionResult> => {
    const result: MaterialSelectionResult = {
      selections: [],
      alternatives: [],
      costImpact: 0,
      leadTimeImpact: 0,
    };

    for (const item of quoteBOM.items) {
      // 获取可用物料变体
      const variants = await getMaterialVariants(item.materialCategoryId);
      
      // 智能选择算法
      const selection = await selectBestVariant(variants, preferences, item);
      result.selections.push(selection);

      // 生成替代方案
      const alternatives = await generateAlternatives(variants, selection);
      result.alternatives.push(...alternatives);
    }

    return result;
  };

  return {
    getParameterRecommendations,
    handleConfigurationError,
    selectOptimalMaterials,
  };
});
```

### 5.3 跨Store数据同步

```typescript
// 数据同步服务
export const useDataSyncStore = defineStore('dataSync', () => {
  // 监听配置变更，自动更新相关数据
  watch(
    () => useProductConfigurationStore().currentConfiguration,
    async (newConfig) => {
      if (newConfig) {
        // 自动保存草稿
        await saveDraft(newConfig);
        
        // 更新成本估算
        await updateCostEstimate(newConfig);
        
        // 检查库存状态
        await checkMaterialAvailability(newConfig);
      }
    },
    { deep: true }
  );

  // 实时验证
  const enableRealtimeValidation = (configId: string) => {
    const config = useProductConfigurationStore().configurations.find(c => c.id === configId);
    if (!config) return;

    // 防抖的实时验证
    const debouncedValidation = debounce(async () => {
      await useProductConfigurationStore().calculateAndValidateConfiguration(config);
    }, 500);

    // 监听参数变化
    watch(
      () => config.parameterValues,
      debouncedValidation,
      { deep: true }
    );
  };

  return {
    enableRealtimeValidation,
  };
});
```

## 6. 简化的MTO流程设计

### 6.1 业务流程概览（简化版）

```mermaid
flowchart TD
    A[客户询价] --> B[产品模板选择]
    B --> C[向导式配置]
    C --> D{智能验证}
    D -->|通过| E[一键生成报价]
    D -->|失败| F[智能修正建议]
    F --> C
    E --> G[客户确认]
    G --> H{确认结果}
    H -->|确认| I[工程师物料选择]
    H -->|修改| C
    I --> J[自动生成生产BOM]
    J --> K[库存检查与采购]
    K --> L[一键下发工单]
    L --> M[生产执行]
```

### 6.2 简化的用户操作流程

#### 6.2.1 销售人员操作流程（3步完成）

```typescript
// 销售人员的简化流程
interface SalesPersonWorkflow {
  // 第1步：产品选择（1分钟）
  step1_ProductSelection: {
    action: "选择产品模板";
    interface: "ProductTemplateSelection";
    userInput: {
      productCategory: string;      // 产品类别
      basicRequirements: string;    // 基本需求
      budgetRange?: number;         // 预算范围
    };
    systemOutput: {
      recommendedTemplates: ProductTemplate[];
      estimatedPrices: number[];
    };
  };

  // 第2步：参数配置（3-5分钟）
  step2_Configuration: {
    action: "向导式参数配置";
    interface: "ProductConfigurator";
    userInput: {
      dimensions: { width: number; height: number; };
      materials: string[];
      features: string[];
      specialRequirements?: string;
    };
    systemOutput: {
      realtimeValidation: ValidationResult[];
      costEstimate: CostBreakdown;
      deliveryEstimate: number;
    };
  };

  // 第3步：报价生成（30秒）
  step3_QuoteGeneration: {
    action: "一键生成报价";
    interface: "QuoteGeneration";
    userInput: {
      profitMargin?: number;        // 利润率调整
      validityPeriod: number;       // 报价有效期
      notes?: string;               // 备注说明
    };
    systemOutput: {
      quotePDF: string;             // 报价单PDF
      internalCostAnalysis: CostAnalysis;
      competitiveAnalysis?: CompetitiveInfo;
    };
  };
}
```

#### 6.2.2 技术人员操作流程（4步完成）

```typescript
// 技术人员的简化流程
interface TechnicianWorkflow {
  // 第1步：接收确认订单（30秒）
  step1_OrderReview: {
    action: "订单技术审核";
    interface: "OrderReviewPanel";
    userInput: {
      technicalFeasibility: 'confirmed' | 'need_modification';
      modifications?: string[];
    };
    systemOutput: {
      configurationSnapshot: ProductConfiguration;
      technicalAlerts: TechnicalAlert[];
    };
  };

  // 第2步：物料选择（2-3分钟）
  step2_MaterialSelection: {
    action: "从推荐清单选择具体物料";
    interface: "MaterialSelectionPanel";
    userInput: {
      materialSelections: MaterialSelection[];
      alternativeChoices?: AlternativeChoice[];
    };
    systemOutput: {
      stockStatus: StockInfo[];
      costImpact: CostImpact;
      procurementNeeds: ProcurementRequirement[];
    };
  };

  // 第3步：生产BOM确认（1分钟）
  step3_ProductionBOM: {
    action: "确认生产BOM";
    interface: "ProductionBOMPanel";
    userInput: {
      bomApproval: boolean;
      adjustments?: BOMAdjustment[];
    };
    systemOutput: {
      finalBOM: ProductionBOM;
      materialRequirements: MaterialRequirement[];
    };
  };

  // 第4步：工单创建（1分钟）
  step4_WorkOrderCreation: {
    action: "创建生产工单";
    interface: "WorkOrderPanel";
    userInput: {
      priority: 'normal' | 'urgent' | 'rush';
      scheduledStartDate: string;
      specialInstructions?: string;
    };
    systemOutput: {
      workOrder: ProductionOrder;
      schedulingInfo: SchedulingInfo;
    };
  };
}
```

### 6.3 智能化MTO服务实现

```typescript
// 简化的MTO服务
export class SimplifiedMTOService {
  // 智能产品推荐
  async recommendProducts(
    customerRequirements: CustomerRequirements
  ): Promise<ProductRecommendation[]> {
    // 基于AI的产品匹配
    const matchingTemplates = await this.aiProductMatcher.match(customerRequirements);
    
    // 价格和交期估算
    const recommendations = await Promise.all(
      matchingTemplates.map(async (template) => {
        const estimate = await this.quickEstimate(template, customerRequirements);
        return {
          template,
          estimate,
          matchScore: this.calculateMatchScore(template, customerRequirements),
          reasons: this.explainRecommendation(template, customerRequirements),
        };
      })
    );

    // 按匹配度排序
    return recommendations.sort((a, b) => b.matchScore - a.matchScore);
  }

  // 一键配置生成
  async generateConfigurationFromRequirements(
    templateId: string,
    requirements: CustomerRequirements
  ): Promise<ProductConfiguration> {
    const template = await this.getTemplate(templateId);
    
    // 智能参数映射
    const parameterValues = await this.intelligentParameterMapping(
      template,
      requirements
    );
    
    // 创建配置
    const configuration = await this.createConfiguration(template, parameterValues);
    
    // 自动验证和优化
    await this.autoValidateAndOptimize(configuration);
    
    return configuration;
  }

  // 智能物料选择
  async autoSelectMaterials(
    quoteBOM: QuoteBOM,
    constraints: MaterialConstraints = {}
  ): Promise<MaterialSelectionResult> {
    const selections: MaterialSelection[] = [];
    
    for (const item of quoteBOM.items) {
      // 获取可用物料
      const availableMaterials = await this.getMaterialsByCategory(
        item.materialCategoryId
      );
      
      // 应用智能选择算法
      const selection = await this.selectOptimalMaterial(
        availableMaterials,
        item,
        constraints
      );
      
      selections.push(selection);
    }
    
    return {
      selections,
      totalCostImpact: this.calculateCostImpact(selections),
      leadTimeImpact: this.calculateLeadTimeImpact(selections),
      alternatives: this.generateAlternatives(selections),
    };
  }

  // 智能参数映射
  private async intelligentParameterMapping(
    template: ProductTemplate,
    requirements: CustomerRequirements
  ): Promise<Record<string, any>> {
    const mapping: Record<string, any> = {};
    
    // 基础尺寸映射
    if (requirements.dimensions) {
      mapping.width = requirements.dimensions.width;
      mapping.height = requirements.dimensions.height;
      if (requirements.dimensions.depth) {
        mapping.depth = requirements.dimensions.depth;
      }
    }
    
    // 材料类型映射
    if (requirements.materials) {
      mapping.frameType = this.mapFrameMaterial(requirements.materials.frame);
      mapping.glassType = this.mapGlassType(requirements.materials.glass);
    }
    
    // 功能需求映射
    if (requirements.features) {
      requirements.features.forEach(feature => {
        const parameterMapping = this.getFeatureParameterMapping(feature);
        Object.assign(mapping, parameterMapping);
      });
    }
    
    // 性能要求映射
    if (requirements.performance) {
      if (requirements.performance.fireRating) {
        mapping.fireRating = requirements.performance.fireRating;
      }
      if (requirements.performance.thermalPerformance) {
        mapping.thermalRating = requirements.performance.thermalPerformance;
      }
    }
    
    return mapping;
  }
}
```

### 6.4 错误处理和用户引导

#### 6.4.1 智能错误处理机制

```typescript
// 智能错误处理服务
export class SmartErrorHandlingService {
  // 自动错误修正
  async autoFixConfiguration(
    error: ValidationResult,
    configuration: ProductConfiguration
  ): Promise<AutoFixResult> {
    const template = await this.getTemplate(configuration.templateId);
    
    switch (error.type) {
      case 'dimension_constraint':
        return await this.fixDimensionConstraint(error, configuration, template);
      
      case 'material_compatibility':
        return await this.fixMaterialCompatibility(error, configuration, template);
      
      case 'cost_constraint':
        return await this.optimizeCost(error, configuration, template);
      
      default:
        return { canAutoFix: false, suggestions: this.generateManualSuggestions(error) };
    }
  }

  // 用户友好的错误提示
  generateUserFriendlyMessage(error: ValidationResult): UserFriendlyError {
    const templates = {
      dimension_too_large: {
        title: "尺寸超出标准范围",
        message: "您选择的尺寸 {width}×{height}mm 超出了标准产品范围",
        icon: "📏",
        severity: "warning",
        solutions: [
          {
            type: "auto_adjust",
            description: "自动调整到最接近的标准尺寸",
            impact: "可能会稍微改变产品尺寸，但确保生产可行性"
          },
          {
            type: "custom_design",
            description: "申请定制设计",
            impact: "需要额外设计时间和成本，交期延长5-7天"
          }
        ]
      },
      
      material_conflict: {
        title: "材料组合不兼容",
        message: "所选的 {material1} 和 {material2} 无法在同一产品中使用",
        icon: "⚠️",
        severity: "error",
        solutions: [
          {
            type: "suggest_alternative",
            description: "推荐兼容的替代材料",
            impact: "保持产品性能，可能有轻微成本差异"
          }
        ]
      }
    };

    const template = templates[error.code] || templates.default;
    return this.renderErrorTemplate(template, error.context);
  }
}
```

#### 6.4.2 智能引导系统

```typescript
// 用户引导服务
export class UserGuidanceService {
  // 动态帮助系统
  async getContextualHelp(
    currentStep: string,
    userInput: any,
    userRole: string
  ): Promise<ContextualHelp> {
    return {
      quickTips: await this.getQuickTips(currentStep, userRole),
      videoTutorials: await this.getVideoTutorials(currentStep),
      relatedDocuments: await this.getRelatedDocs(currentStep),
      commonIssues: await this.getCommonIssues(currentStep),
      contactSupport: this.getSupportContact(currentStep),
    };
  }

  // 智能提示系统
  async generateSmartSuggestions(
    configuration: ProductConfiguration,
    currentParameter: string
  ): Promise<SmartSuggestion[]> {
    const suggestions: SmartSuggestion[] = [];
    
    // 基于历史数据的建议
    const historicalSuggestions = await this.getHistoricalSuggestions(
      configuration.templateId,
      currentParameter
    );
    suggestions.push(...historicalSuggestions);
    
    // 成本优化建议
    const costOptimizations = await this.getCostOptimizationSuggestions(
      configuration,
      currentParameter
    );
    suggestions.push(...costOptimizations);
    
    // 性能改进建议
    const performanceImprovements = await this.getPerformanceImprovements(
      configuration,
      currentParameter
    );
    suggestions.push(...performanceImprovements);
    
    return suggestions.sort((a, b) => b.priority - a.priority);
  }
}
```

## 7. Mock数据设计

### 7.1 分层数据文件结构

```
public/mock/product/
├── application-layer/           # 应用层数据 - 普通用户使用
│   ├── product-templates.json   # 产品模板数据
│   ├── configurations.json      # 产品配置数据
│   ├── quote-boms.json         # 报价BOM数据
│   ├── production-boms.json    # 生产BOM数据
│   └── user-preferences.json   # 用户偏好设置
├── professional-layer/         # 专业层数据 - 设计工程师使用
│   ├── components.json         # 组件数据
│   ├── assemblies.json        # 构件数据
│   ├── product-structures.json # 产品结构数据
│   ├── formulas.json          # 计算公式数据
│   └── business-rules.json    # 业务规则数据
├── shared/                    # 共享数据
│   ├── material-mappings.json # 物料映射数据
│   ├── validation-rules.json  # 验证规则
│   └── cost-templates.json    # 成本模板
└── seed-data/                 # 初始化数据
    ├── demo-configurations.json # 演示配置
    └── sample-orders.json       # 示例订单
```

### 7.2 应用层示例数据

#### 7.2.1 产品模板数据示例

```json
{
  "productTemplates": [
    {
      "id": "template_001",
      "code": "FIRE_PARTITION_STD",
      "name": "标准防火隔断",
      "displayName": "标准防火隔断系统",
      "description": "适用于办公楼、商场等场所的标准防火隔断解决方案",
      "category": "隔断系统",
      "subCategory": "防火隔断",
      "productStructureId": "struct_fire_partition_std",
      
      "userParameters": [
        {
          "id": "partition_width",
          "name": "partition_width",
          "displayName": "隔断宽度",
          "type": "dimension",
          "inputType": "number",
          "unit": "mm",
          "defaultValue": 2000,
          "minValue": 1000,
          "maxValue": 6000,
          "required": true,
          "helpText": "隔断的总宽度，建议在1000-6000mm范围内",
          "group": "基本尺寸",
          "order": 1,
          "validation": {
            "rules": [
              {
                "type": "range",
                "expression": "value >= 1000 && value <= 6000",
                "message": "隔断宽度应在1000-6000mm范围内",
                "severity": "error"
              }
            ],
            "suggestions": ["2000mm", "2500mm", "3000mm"]
          }
        },
        {
          "id": "partition_height",
          "name": "partition_height", 
          "displayName": "隔断高度",
          "type": "dimension",
          "inputType": "number",
          "unit": "mm",
          "defaultValue": 2400,
          "minValue": 800,
          "maxValue": 4000,
          "required": true,
          "helpText": "隔断的总高度，标准天花板高度为2400mm",
          "group": "基本尺寸",
          "order": 2
        },
        {
          "id": "fire_rating",
          "name": "fire_rating",
          "displayName": "防火等级",
          "type": "feature",
          "inputType": "select",
          "defaultValue": "A",
          "options": [
            { "value": "A", "label": "A级（120分钟）", "additionalCost": 0 },
            { "value": "B", "label": "B级（90分钟）", "additionalCost": -200 },
            { "value": "C", "label": "C级（60分钟）", "additionalCost": -400 }
          ],
          "required": true,
          "helpText": "选择合适的防火等级，A级适用于高层建筑",
          "group": "性能要求",
          "order": 3
        },
        {
          "id": "frame_material",
          "name": "frame_material",
          "displayName": "框架材质",
          "type": "material",
          "inputType": "select",
          "defaultValue": "stainless_steel",
          "options": [
            { 
              "value": "stainless_steel", 
              "label": "不锈钢框架", 
              "description": "耐腐蚀，美观耐用",
              "additionalCost": 0 
            },
            { 
              "value": "aluminum", 
              "label": "铝合金框架", 
              "description": "轻质，经济实用",
              "additionalCost": -300 
            },
            { 
              "value": "steel", 
              "label": "钢质框架", 
              "description": "强度高，成本低",
              "additionalCost": -500 
            }
          ],
          "required": true,
          "group": "材质选择",
          "order": 4
        },
        {
          "id": "glass_type",
          "name": "glass_type",
          "displayName": "玻璃类型",
          "type": "material",
          "inputType": "select",
          "defaultValue": "fire_laminated",
          "options": [
            { 
              "value": "fire_laminated", 
              "label": "防火夹胶玻璃", 
              "description": "防火性能优异，安全性高",
              "additionalCost": 0 
            },
            { 
              "value": "fire_tempered", 
              "label": "防火钢化玻璃", 
              "description": "强度高，经济实用",
              "additionalCost": -150 
            }
          ],
          "required": true,
          "group": "材质选择",
          "order": 5
        },
        {
          "id": "special_features",
          "name": "special_features",
          "displayName": "特殊功能",
          "type": "option",
          "inputType": "multiSelect",
          "defaultValue": [],
          "options": [
            { 
              "value": "waterproof", 
              "label": "防水密封", 
              "description": "底部防水处理",
              "additionalCost": 100 
            },
            { 
              "value": "ceiling_seal", 
              "label": "顶部密封", 
              "description": "与天花板完全密封",
              "additionalCost": 80 
            },
            { 
              "value": "sound_insulation", 
              "label": "隔音处理", 
              "description": "增强隔音效果",
              "additionalCost": 200 
            }
          ],
          "required": false,
          "group": "选配功能",
          "order": 6
        }
      ],
      
      "previewImage": "/images/templates/fire-partition-std.jpg",
      "specifications": [
        { "name": "防火等级", "value": "A级", "category": "性能" },
        { "name": "耐火时间", "value": "120分钟", "category": "性能" },
        { "name": "标准尺寸", "value": "2000×2400mm", "category": "尺寸" },
        { "name": "框架材质", "value": "不锈钢", "category": "材质" },
        { "name": "玻璃厚度", "value": "6+1.52PVB+6", "category": "材质" }
      ],
      
      "priceRange": {
        "min": 8000,
        "max": 15000,
        "currency": "CNY"
      },
      
      "leadTimeInfo": {
        "standard": 10,
        "rush": 7,
        "rushSurcharge": 0.15
      },
      
      "applications": ["办公楼", "商场", "酒店", "医院", "学校"],
      "tags": ["防火", "隔断", "A级", "不锈钢", "夹胶玻璃"],
      "popularityScore": 85,
      
      "status": "active",
      "version": 1,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "createdBy": "design_engineer_001",
      "updatedBy": "design_engineer_001"
    }
  ]
}
```

#### 7.2.2 产品配置示例

```json
{
  "productConfigurations": [
    {
      "id": "config_001",
      "templateId": "template_001",
      "templateCode": "FIRE_PARTITION_STD",
      "templateName": "标准防火隔断系统",
      "configurationName": "酒店大堂隔断配置",
      
      "parameterValues": {
        "partition_width": 3000,
        "partition_height": 2400,
        "fire_rating": "A",
        "frame_material": "stainless_steel",
        "glass_type": "fire_laminated",
        "special_features": ["waterproof", "ceiling_seal"]
      },
      
      "calculatedValues": {
        "glass_area": 7.2,
        "frame_perimeter": 10.8,
        "total_weight": 180,
        "fire_resistance_time": 120
      },
      
      "validationResults": [
        {
          "type": "warning",
          "message": "大尺寸隔断建议增加中间立柱支撑",
          "field": "partition_width",
          "suggestedAction": "添加中间立柱或分段设计"
        }
      ],
      
      "costEstimate": {
        "materialCost": 6200,
        "laborCost": 1860,
        "overheadCost": 806,
        "totalCost": 8866,
        "profitMargin": 2217,
        "finalPrice": 11083
      },
      
      "sourceType": "manual",
      "status": "validated",
      "notes": "客户要求增加防水和密封功能",
      
      "version": 1,
      "createdAt": "2024-02-01T10:30:00Z",
      "updatedAt": "2024-02-01T11:15:00Z",
      "createdBy": "sales_001",
      "updatedBy": "sales_001"
    }
  ]
}
```

### 7.3 专业层示例数据

#### 7.3.1 组件数据示例

```json
{
  "components": [
    {
      "id": "comp_001",
      "code": "FRAME_VERTICAL",
      "name": "立柱型材",
      "description": "防火隔断立柱型材组件",
      "materialCategoryId": "cat_stainless_steel_profile",
      "materialCategoryName": "不锈钢型材",
      
      "parameters": [
        {
          "id": "profile_width",
          "name": "型材宽度",
          "displayName": "型材宽度",
          "type": "number",
          "unit": "mm",
          "defaultValue": 80,
          "minValue": 60,
          "maxValue": 120,
          "required": true,
          "description": "立柱型材的宽度尺寸",
          "category": "basic"
        },
        {
          "id": "profile_depth", 
          "name": "型材深度",
          "displayName": "型材深度",
          "type": "number",
          "unit": "mm",
          "defaultValue": 60,
          "minValue": 40,
          "maxValue": 100,
          "required": true,
          "description": "立柱型材的深度尺寸",
          "category": "basic"
        },
        {
          "id": "wall_thickness",
          "name": "壁厚",
          "displayName": "壁厚",
          "type": "number",
          "unit": "mm",
          "defaultValue": 4,
          "minValue": 3,
          "maxValue": 6,
          "required": true,
          "description": "型材壁厚，影响强度和成本",
          "category": "basic"
        }
      ],
      
      "quantityFormula": "Math.ceil((partition_height - 100) / 6000) * 2",
      "costFormula": "profile_length * unit_weight * material_price",
      
      "constraints": [
        {
          "id": "const_001",
          "name": "壁厚约束",
          "expression": "wall_thickness >= 3.0",
          "errorMessage": "不锈钢型材壁厚不能小于3.0mm",
          "severity": "error",
          "autoFix": {
            "enabled": true,
            "fixExpression": "wall_thickness = 3.0",
            "fixMessage": "自动调整壁厚为3.0mm"
          }
        },
        {
          "id": "const_002",
          "name": "尺寸比例约束",
          "expression": "profile_width / profile_depth >= 0.8 && profile_width / profile_depth <= 2.0",
          "errorMessage": "型材宽深比应在0.8-2.0之间",
          "severity": "warning"
        }
      ],
      
      "status": "active",
      "version": 1,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "createdBy": "design_engineer_001",
      "updatedBy": "design_engineer_001",
      
      "properties": {
        "fireRating": "A",
        "material": "stainless_steel_304",
        "surfaceFinish": "brushed",
        "structuralComponent": true
      }
    }
  ]
}
```

### 7.4 智能化数据示例

#### 7.4.1 用户偏好和历史数据

```json
{
  "userPreferences": [
    {
      "userId": "sales_001",
      "role": "sales_person",
      "preferences": {
        "favoriteTemplates": ["template_001", "template_003"],
        "defaultProfitMargin": 0.25,
        "preferredMaterials": ["stainless_steel", "fire_laminated"],
        "quickAccessParameters": ["partition_width", "partition_height", "fire_rating"],
        "notificationSettings": {
          "priceAlerts": true,
          "stockAlerts": true,
          "customerFollowUp": true
        }
      },
      "recentConfigurations": [
        {
          "configId": "config_001",
          "templateId": "template_001",
          "customerName": "XX酒店",
          "lastAccessed": "2024-02-01T11:15:00Z"
        }
      ]
    }
  ],
  
  "historicalData": [
    {
      "templateId": "template_001",
      "parameterStatistics": {
        "partition_width": {
          "mostCommon": 2500,
          "average": 2650,
          "distribution": {
            "2000": 0.15,
            "2500": 0.35,
            "3000": 0.30,
            "3500": 0.20
          }
        },
        "fire_rating": {
          "mostCommon": "A",
          "distribution": {
            "A": 0.70,
            "B": 0.25,
            "C": 0.05
          }
        }
      },
      "costTrends": {
        "averageCost": 9200,
        "costRange": [7500, 12000],
        "monthlyTrend": "increasing"
      }
    }
  ]
}
```

#### 7.4.2 智能推荐数据

```json
{
  "smartRecommendations": [
    {
      "type": "cost_optimization",
      "templateId": "template_001",
      "triggers": [
        {
          "condition": "partition_width > 3000",
          "recommendation": {
            "title": "成本优化建议",
            "message": "对于超过3米的隔断，建议采用分段设计以降低成本",
            "impact": "预计可节省成本15-20%",
            "action": "split_design",
            "confidence": 85
          }
        }
      ]
    },
    {
      "type": "material_suggestion",
      "templateId": "template_001",
      "triggers": [
        {
          "condition": "fire_rating === 'C' && frame_material === 'stainless_steel'",
          "recommendation": {
            "title": "材料建议",
            "message": "C级防火要求下，铝合金框架可满足需求且更经济",
            "impact": "可节省成本约500元",
            "action": "suggest_aluminum",
            "confidence": 90
          }
        }
      ]
    }
  ]
}
```

## 8. 实施计划

### 8.1 分阶段开发策略

#### 阶段一：应用层基础（2-3周）
**目标**：快速为普通用户提供可用的产品配置功能

1. **产品模板系统（1周）**
   - 实现产品模板数据模型和API
   - 创建产品模板选择界面
   - 实现基础的模板浏览和搜索功能
   - Mock数据准备和基础样式

2. **产品配置器（1-2周）**
   - 实现向导式配置界面
   - 参数输入和实时验证
   - 基础的成本估算功能
   - 配置保存和管理功能

3. **简化报价系统（0.5周）**
   - 一键生成报价功能
   - 简化的BOM展示
   - 报价单导出功能

**交付成果**：
- 普通用户可以选择产品模板
- 完成基本的产品配置
- 生成简单的报价单

#### 阶段二：智能化增强（2-3周）
**目标**：提升用户体验，增加智能化功能

1. **智能验证和建议（1周）**
   - 实时参数验证
   - 智能错误提示和修正建议
   - 参数推荐系统

2. **成本计算引擎（1周）**
   - 精确的成本计算逻辑
   - 实时成本更新
   - 成本优化建议

3. **用户体验优化（1周）**
   - 界面优化和响应式设计
   - 加载性能优化
   - 错误处理完善

**交付成果**：
- 智能化的配置体验
- 准确的成本计算
- 用户友好的界面

#### 阶段三：专业层功能（3-4周）
**目标**：为设计工程师提供专业的产品设计工具

1. **组件和构件管理（1.5周）**
   - 组件库管理系统
   - 构件定义和编辑
   - 参数和约束配置

2. **产品结构设计（1.5周）**
   - 产品结构编辑器
   - 业务规则引擎
   - 公式编辑和验证

3. **模板生成和发布（1周）**
   - 专业层到应用层的转换
   - 模板测试和验证
   - 发布流程管理

**交付成果**：
- 完整的产品设计工具
- 模板创建和发布系统
- 专业用户界面

#### 阶段四：生产集成（2-3周）
**目标**：与现有MES系统集成，完善MTO流程

1. **物料选择系统（1周）**
   - 物料变体选择界面
   - 库存检查集成
   - 替代物料推荐

2. **生产BOM生成（1周）**
   - 报价BOM到生产BOM转换
   - 工艺路线关联
   - 工单创建集成

3. **系统集成测试（1周）**
   - 端到端流程测试
   - 性能优化
   - 数据一致性验证

**交付成果**：
- 完整的MTO流程
- 与MES系统无缝集成
- 生产就绪的系统

### 8.2 技术实施策略

#### 8.2.1 前端开发策略

```typescript
// 技术栈选择
const frontendTechStack = {
  framework: "Vue 3 + TypeScript",
  stateManagement: "Pinia",
  uiLibrary: "ShadCN Vue",
  routing: "Vue Router 4",
  buildTool: "Vite",
  testing: "Vitest + Vue Test Utils"
};

// 组件架构
const componentArchitecture = {
  // 应用层组件
  applicationLayer: {
    "ProductTemplateGrid": "产品模板网格展示",
    "ProductConfigurator": "产品配置器",
    "ConfigurationWizard": "配置向导",
    "QuoteGenerator": "报价生成器",
    "ConfigurationHistory": "配置历史"
  },
  
  // 专业层组件
  professionalLayer: {
    "ComponentEditor": "组件编辑器",
    "AssemblyDesigner": "构件设计器",
    "StructureBuilder": "结构构建器",
    "FormulaEditor": "公式编辑器",
    "RuleEngine": "规则引擎"
  },
  
  // 共享组件
  sharedComponents: {
    "SmartForm": "智能表单",
    "ValidationDisplay": "验证结果展示",
    "CostBreakdown": "成本分解",
    "ParameterInput": "参数输入控件",
    "ErrorHandler": "错误处理组件"
  }
};
```

#### 8.2.2 后端服务策略

```typescript
// 服务架构
const serviceArchitecture = {
  // 核心服务
  coreServices: {
    "ProductTemplateService": "产品模板服务",
    "ConfigurationService": "产品配置服务", 
    "ValidationService": "验证服务",
    "CostCalculationService": "成本计算服务",
    "BOMGenerationService": "BOM生成服务"
  },
  
  // 业务服务
  businessServices: {
    "MTOWorkflowService": "MTO流程服务",
    "MaterialSelectionService": "物料选择服务",
    "RecommendationService": "推荐服务",
    "HistoricalAnalysisService": "历史分析服务"
  },
  
  // 基础服务
  foundationServices: {
    "FormulaEngineService": "公式引擎",
    "RuleEngineService": "规则引擎",
    "CacheService": "缓存服务",
    "EventService": "事件服务"
  }
};
```

### 8.3 数据迁移策略

#### 8.3.1 现有数据适配

```typescript
// 数据迁移计划
const migrationPlan = {
  phase1: {
    description: "现有物料数据适配",
    tasks: [
      "物料分类映射到组件",
      "现有产品转换为产品模板",
      "历史订单数据分析"
    ],
    estimatedTime: "1周"
  },
  
  phase2: {
    description: "业务规则迁移", 
    tasks: [
      "现有计算逻辑转换为公式",
      "约束条件配置",
      "验证规则建立"
    ],
    estimatedTime: "1周"
  },
  
  phase3: {
    description: "用户数据和偏好",
    tasks: [
      "用户角色配置",
      "权限设置",
      "个人偏好初始化"
    ],
    estimatedTime: "0.5周"
  }
};
```

### 8.4 测试策略

#### 8.4.1 分层测试方法

```typescript
// 测试策略
const testingStrategy = {
  // 应用层测试
  applicationLayerTests: {
    unitTests: [
      "产品模板选择逻辑",
      "参数验证功能",
      "成本计算准确性"
    ],
    integrationTests: [
      "配置器端到端流程",
      "报价生成完整性"
    ],
    userAcceptanceTests: [
      "普通用户操作流程",
      "界面易用性测试"
    ]
  },
  
  // 专业层测试
  professionalLayerTests: {
    unitTests: [
      "公式引擎准确性",
      "规则引擎逻辑",
      "组件关系验证"
    ],
    integrationTests: [
      "模板生成流程",
      "数据一致性检查"
    ]
  },
  
  // 性能测试
  performanceTests: {
    loadTests: "大量配置数据加载测试",
    stressTests: "并发用户操作测试",
    enduranceTests: "长时间运行稳定性测试"
  }
};
```

### 8.5 部署和运维

#### 8.5.1 部署策略

```typescript
// 部署配置
const deploymentStrategy = {
  development: {
    environment: "开发环境",
    features: ["完整功能", "调试工具", "Mock数据"],
    updateFrequency: "每日构建"
  },
  
  staging: {
    environment: "预发布环境", 
    features: ["生产数据", "性能监控", "用户验收测试"],
    updateFrequency: "每周发布"
  },
  
  production: {
    environment: "生产环境",
    features: ["高可用", "数据备份", "监控告警"],
    updateFrequency: "双周发布"
  }
};
```

#### 8.5.2 监控和维护

```typescript
// 监控指标
const monitoringMetrics = {
  // 业务指标
  businessMetrics: {
    "配置成功率": "用户成功完成配置的比例",
    "报价准确率": "系统生成报价的准确性",
    "用户满意度": "用户体验评分",
    "系统采用率": "各功能模块的使用频率"
  },
  
  // 技术指标
  technicalMetrics: {
    "响应时间": "页面和API响应时间",
    "错误率": "系统错误发生频率",
    "可用性": "系统正常运行时间",
    "资源使用率": "CPU、内存、存储使用情况"
  },
  
  // 用户行为指标
  userBehaviorMetrics: {
    "功能使用统计": "各功能使用频次",
    "用户路径分析": "用户操作路径分析",
    "错误操作统计": "常见用户操作错误"
  }
};
```

### 8.6 风险管理

#### 8.6.1 技术风险

```typescript
const technicalRisks = {
  "复杂度管理": {
    risk: "系统复杂度过高，维护困难",
    mitigation: "分层架构设计，模块化开发",
    contingency: "必要时简化功能范围"
  },
  
  "性能问题": {
    risk: "大量计算导致性能瓶颈",
    mitigation: "异步计算，缓存策略，性能优化",
    contingency: "云端计算服务"
  },
  
  "数据一致性": {
    risk: "多层数据不一致",
    mitigation: "事务管理，数据验证，同步机制",
    contingency: "数据修复工具"
  }
};
```

#### 8.6.2 业务风险

```typescript
const businessRisks = {
  "用户接受度": {
    risk: "用户不适应新系统",
    mitigation: "用户培训，渐进式迁移，界面优化",
    contingency: "保留原有操作方式"
  },
  
  "业务流程变更": {
    risk: "现有业务流程需要调整",
    mitigation: "充分的业务调研，灵活的配置",
    contingency: "系统适配现有流程"
  },
  
  "数据安全": {
    risk: "敏感数据泄露",
    mitigation: "权限控制，数据加密，审计日志",
    contingency: "数据恢复机制"
  }
};
```

## 9. 总结

本设计文档提供了一套基于用户分层架构的产品管理模块解决方案，通过双层设计有效平衡了专业功能的完整性和普通用户的易用性，支持MTO模式的订单驱动生产流程。

### 9.1 核心创新点

#### 9.1.1 双层用户架构
- **专业层**：为设计工程师提供完整的产品结构设计能力
- **应用层**：为普通用户提供简化的产品配置界面
- **智能转换**：专业层的复杂结构自动转换为用户友好的模板

#### 9.1.2 智能化用户体验
- **向导式配置**：分步骤引导用户完成产品配置
- **实时验证**：参数输入时即时验证和反馈
- **智能推荐**：基于历史数据和规则的智能建议
- **自动修正**：错误自动检测和修复建议

#### 9.1.3 简化的业务流程
- **3步报价**：销售人员3步完成从询价到报价
- **4步生产**：技术人员4步完成从订单到工单
- **人工物料选择**：技术人员在推荐清单中选择具体物料
- **一键操作**：复杂的BOM生成和成本计算一键完成

### 9.2 技术特点

#### 9.2.1 架构优势
- **分层清晰**：专业层和应用层完全分离，互不干扰
- **组件复用**：基于ShadCN Vue的现代化组件库
- **类型安全**：完整的TypeScript类型定义确保开发质量
- **状态管理**：基于Pinia的响应式状态管理

#### 9.2.2 数据模型
- **灵活配置**：支持参数化配置和业务规则
- **版本管理**：完整的版本控制和变更追溯
- **数据一致性**：跨层级的数据同步和验证机制
- **扩展性**：模块化设计支持功能扩展

#### 9.2.3 智能化特性
- **AI推荐**：基于机器学习的产品和参数推荐
- **自动验证**：规则驱动的配置验证和错误处理
- **成本优化**：实时成本计算和优化建议
- **历史学习**：基于历史数据的智能决策支持

### 9.3 业务价值

#### 9.3.1 用户体验提升
- **降低门槛**：普通用户经过简单培训即可熟练操作
- **提升效率**：自动化的流程大幅减少操作时间
- **减少错误**：智能验证和提示减少人为错误
- **直观操作**：可视化界面和向导式流程

#### 9.3.2 业务流程优化
- **快速响应**：从询价到报价时间大幅缩短
- **精确成本**：从报价BOM到生产BOM的精确转换
- **库存优化**：实时库存检查和采购建议
- **质量保证**：完整的配置验证和追溯机制

#### 9.3.3 管理效益
- **数据驱动**：丰富的统计分析和报表功能
- **流程标准化**：统一的操作流程和质量标准
- **知识沉淀**：产品配置知识的系统化管理
- **持续改进**：基于数据分析的流程优化

### 9.4 实施建议

#### 9.4.1 分阶段实施
1. **第一阶段**：应用层基础功能，快速为用户提供价值
2. **第二阶段**：智能化增强，提升用户体验
3. **第三阶段**：专业层功能，满足设计工程师需求
4. **第四阶段**：系统集成，完善MTO流程

#### 9.4.2 风险控制
- **技术风险**：分层架构降低复杂度，模块化开发降低风险
- **业务风险**：渐进式迁移，保留原有操作方式作为备选
- **用户风险**：充分培训和用户引导，界面优化降低学习成本

#### 9.4.3 成功关键因素
- **用户参与**：在设计和测试阶段充分听取用户反馈
- **数据质量**：确保基础数据的准确性和完整性
- **培训支持**：提供充分的用户培训和技术支持
- **持续优化**：基于使用反馈持续改进系统

### 9.5 未来展望

#### 9.5.1 技术演进
- **AI集成**：更深度的人工智能应用
- **移动支持**：移动端产品配置应用
- **3D可视化**：产品配置的3D预览功能
- **语音交互**：语音输入和智能助手

#### 9.5.2 业务扩展
- **多行业适配**：扩展到其他制造行业
- **供应链集成**：与供应商系统深度集成
- **客户门户**：客户自助配置和下单
- **云服务化**：SaaS模式的产品配置服务

### 9.6 预期成果

通过本设计的实施，预期将为玻璃深加工企业带来以下成果：

#### 9.6.1 量化指标
- **效率提升**：报价生成时间缩短70%，从2小时降至30分钟
- **错误减少**：配置错误率降低80%，从20%降至4%
- **成本节约**：通过智能优化，平均项目成本降低5-10%
- **客户满意度**：响应速度提升，客户满意度提高15-20%

#### 9.6.2 定性收益
- **竞争优势**：快速响应能力成为市场竞争优势
- **知识管理**：产品配置知识得到有效管理和传承
- **流程标准化**：业务流程标准化，质量更加稳定
- **创新能力**：释放员工创造力，专注于价值创造

通过这套创新的双层架构设计，我们相信能够为玻璃深加工企业提供一套既专业又易用的产品管理解决方案，有效支撑MTO生产模式的业务需求，推动企业数字化转型和智能制造发展。

---

**附录：快速开始指南**

对于急于开始实施的团队，建议按以下优先级开始：

1. **立即开始**：产品模板数据准备和基础UI组件
2. **第一周**：产品模板选择界面和基础配置器
3. **第二周**：参数验证和成本计算
4. **第三周**：报价生成和用户测试

这样可以在最短时间内为用户提供可用的功能，然后逐步完善和扩展。

## 10. 客户全新产品订单场景流程

### 10.1 业务场景设定

**客户**：某五星级酒店
**需求**：定制防火玻璃隔断，用于大堂与餐厅分隔
**特点**：全新产品规格，系统中无现成产品可匹配

**具体规格**：
- 尺寸：3000mm(宽) × 2400mm(高)
- 防火等级：A级，耐火120分钟
- 玻璃：夹胶防火玻璃 6+1.52PVB+6
- 框架：304不锈钢，拉丝工艺
- 安装：固定式，底部防水，顶部密封
- 数量：8樘

### 10.2 完整流程演示

#### 第1步：订单接收与录入

**操作人员**：销售人员
**系统界面**：客户订单管理页面

```typescript
// 订单项数据
const orderItem: CustomerOrderItem = {
  id: "order_item_001",
  customerOrderId: "CO-2024-001",
  orderNumber: "CO-2024-001",
  customerName: "XX五星级酒店",
  specifications: {
    length: 3000,        // 宽度
    width: 2400,         // 高度
    thickness: 14.52,    // 总厚度(6+1.52+6)
    glassType: "fire_resistant_laminated",
    fireRating: "A",
    fireResistanceTime: 120,
    frameType: "stainless_steel",
    frameSurface: "brushed",
    installationType: "fixed",
    specialRequirements: ["waterproof_bottom", "ceiling_seal"]
  },
  quantity: 8,
  deliveryDate: "2024-03-15",
  notes: "酒店大堂与餐厅隔断，需要防水和密封处理"
}
```

#### 第2步：产品匹配分析

**操作人员**：技术人员
**系统界面**：产品管理 → 产品列表

**系统操作**：
1. 系统自动根据规格搜索现有产品
2. 搜索条件：防火玻璃隔断 + A级防火 + 夹胶玻璃
3. **结果**：未找到完全匹配的产品

**技术人员决策**：需要基于现有产品结构创建新产品

#### 第3步：选择基础产品结构

**系统界面**：产品管理 → 产品结构列表

**操作流程**：
1. 浏览产品结构列表，筛选"隔断"类别
2. 找到"标准防火隔断结构"（struct_fire_partition_std）
3. 查看结构详情：
   - 根构件：主框架（不锈钢型材 + 玻璃面板 + 密封胶条）
   - 支持参数：宽度、高度、玻璃厚度、框架材质、防火等级
   - 业务规则：大尺寸加强、防火等级约束

**技术人员操作**：选择此结构作为基础

#### 第4步：创建新产品

**系统界面**：产品管理 → 新增产品

**表单填写**：
```typescript
const newProduct: Product = {
  code: "FIRE_PARTITION_HOTEL_001",
  name: "酒店防火玻璃隔断-A级120分钟",
  description: "酒店大堂餐厅隔断，A级防火，夹胶玻璃，不锈钢框架",
  productStructureId: "struct_fire_partition_std",
  category: "隔断",
  subCategory: "防火隔断",
  lifecycle: "design",

  // 组件物料映射
  componentMaterialMap: [
    {
      componentId: "comp_frame_vertical",   // 立柱
      materialCategoryId: "cat_stainless_steel_profile"
    },
    {
      componentId: "comp_frame_horizontal", // 横梁
      materialCategoryId: "cat_stainless_steel_profile"
    },
    {
      componentId: "comp_glass_panel",      // 玻璃面板
      materialCategoryId: "cat_fire_laminated_glass"
    },
    {
      componentId: "comp_seal_gasket",      // 密封胶条
      materialCategoryId: "cat_fire_seal_gasket"
    }
  ],

  // 默认参数（基于订单规格）
  defaultParameters: {
    partition_width: 3000,
    partition_height: 2400,
    glass_thickness: 14.52,
    glass_type: "fire_resistant_laminated",
    frame_material: "stainless_steel_304",
    frame_surface: "brushed",
    fire_rating: "A",
    fire_resistance_time: 120,
    waterproof_required: true,
    ceiling_seal_required: true
  }
}
```

**系统验证**：
- 检查产品编码唯一性 ✓
- 验证产品结构存在性 ✓
- 检查物料分类映射有效性 ✓

#### 第5步：产品配置生成

**系统界面**：产品管理 → 产品详情 → 生成配置

**自动配置过程**：
```typescript
// 系统根据订单规格和产品默认参数生成配置
const configuration: ProductConfiguration = {
  id: "config_001",
  productId: "FIRE_PARTITION_HOTEL_001",
  configurationName: "酒店隔断配置-CO-2024-001",
  parameterValues: {
    partition_width: 3000,
    partition_height: 2400,
    glass_thickness: 14.52,
    glass_composition: "6+1.52PVB+6",
    frame_profile_size: "80x60x4",  // 根据尺寸规则自动计算
    seal_gasket_type: "fire_resistant_epdm"
  },
  sourceType: "order",
  sourceId: "order_item_001"
}
```

**业务规则验证**：
1. **大尺寸检查**：3000×2400 > 4㎡，触发加强规则
   - 自动调整框架型材：80×60×4mm → 100×80×5mm
2. **防火等级验证**：A级120分钟要求
   - 验证玻璃类型：夹胶防火玻璃 ✓
   - 验证密封材料：防火密封胶条 ✓
3. **特殊要求处理**：
   - 底部防水：添加防水密封组件
   - 顶部密封：添加天花板密封组件

#### 第6步：报价BOM生成

**系统界面**：报价BOM管理 → 新增报价BOM

**自动生成过程**：
```typescript
// 系统遍历产品结构，根据配置参数计算BOM
const quoteBOMItems: QuoteBOMItem[] = [
  {
    level: 1,
    componentCode: "FRAME_VERTICAL",
    componentName: "立柱型材",
    materialCategoryName: "不锈钢型材",
    quantity: 6,  // (3000+2400)*2/1000 = 10.8m，按6米标准长度
    unit: "根",
    quantityFormula: "ceiling((partition_width + partition_height) * 2 / 6000)",
    unitCost: 280.00,  // 100×80×5mm不锈钢型材单价
    totalCost: 1680.00,
    wastageRate: 8,    // 型材切割损耗
    actualQuantity: 6.48
  },
  {
    level: 1,
    componentCode: "GLASS_PANEL",
    componentName: "防火夹胶玻璃",
    materialCategoryName: "防火夹胶玻璃",
    quantity: 7.2,    // 3.0×2.4=7.2㎡
    unit: "㎡",
    quantityFormula: "partition_width * partition_height / 1000000",
    unitCost: 450.00,  // 6+1.52PVB+6防火玻璃单价
    totalCost: 3240.00,
    wastageRate: 5,
    actualQuantity: 7.56
  },
  {
    level: 1,
    componentCode: "SEAL_GASKET",
    componentName: "防火密封胶条",
    materialCategoryName: "防火密封材料",
    quantity: 22,     // 周长×2 + 10%余量
    unit: "米",
    unitCost: 25.00,
    totalCost: 550.00,
    wastageRate: 10,
    actualQuantity: 24.2
  },
  {
    level: 1,
    componentCode: "WATERPROOF_SEAL",
    componentName: "底部防水密封",
    materialCategoryName: "防水密封材料",
    quantity: 3.3,    // 底边长度+余量
    unit: "米",
    unitCost: 35.00,
    totalCost: 115.50,
    actualQuantity: 3.3
  }
  // ... 其他组件
];

const quoteBOM: QuoteBOM = {
  code: "QB-HOTEL-001",
  name: "酒店防火隔断-报价BOM",
  items: quoteBOMItems,
  costSummary: {
    materialCost: 6200.00,   // 材料成本
    laborCost: 1860.00,      // 人工成本(材料成本30%)
    overheadCost: 806.00,    // 制造费用(材料成本13%)
    totalCost: 8866.00       // 总成本
  },
  status: "confirmed"
}
```

#### 第7步：成本分析与报价

**系统界面**：报价BOM详情 → 成本分析

**成本构成分析**：
- **材料成本**：6,200元/樘
  - 不锈钢型材：1,680元 (27%)
  - 防火玻璃：3,240元 (52%)
  - 密封材料：665元 (11%)
  - 五金配件：615元 (10%)

- **加工成本**：1,860元/樘
  - 型材切割加工：480元
  - 玻璃切割磨边：720元
  - 组装焊接：660元

- **制造费用**：806元/樘
  - 设备折旧、水电等

**报价策略**：
- 成本价：8,866元/樘
- 毛利率：25%
- **报价**：11,083元/樘
- **总报价**：88,664元（8樘）

#### 第8步：客户确认

**操作人员**：销售人员
**沟通内容**：
- 产品技术方案确认
- 价格接受度
- 交期可行性
- 特殊要求落实

**客户反馈**：
- 价格可接受
- 要求提供防火认证证书
- 希望缩短交期到2024-03-10

**系统记录**：更新订单状态为"已确认"

#### 第9步：生产BOM转换

**系统界面**：生产BOM管理 → 基于报价BOM创建

**物料变体解析**：
```typescript
// 系统将物料分类解析为具体物料变体
const productionBOMItems: ProductionBOMItem[] = [
  {
    componentCode: "FRAME_VERTICAL",
    materialVariantCode: "SS304-100x80x5-6000",
    materialVariantName: "304不锈钢方管100×80×5×6000mm",
    quantity: 6,
    unitCost: 285.00,  // 实际采购价
    stockInfo: {
      availableQuantity: 12,  // 库存充足
      reservedQuantity: 0,
      shortageQuantity: 0
    },
    alternativeMaterials: [
      {
        materialVariantCode: "SS316-100x80x5-6000",
        materialVariantName: "316不锈钢方管(升级选项)",
        costDifference: 45.00
      }
    ]
  },
  {
    componentCode: "GLASS_PANEL",
    materialVariantCode: "FIRE-GLASS-6+152PVB+6",
    materialVariantName: "防火夹胶玻璃6+1.52PVB+6",
    quantity: 7.56,
    unitCost: 445.00,
    stockInfo: {
      availableQuantity: 0,    // 需要定制生产
      shortageQuantity: 7.56
    },
    procurementInfo: {
      leadTime: 15,           // 15天采购周期
      supplierCode: "SUP_GLASS_001"
    }
  }
  // ... 其他物料
];
```

**库存检查结果**：
- ✅ 不锈钢型材：库存充足
- ❌ 防火玻璃：需要定制，15天周期
- ✅ 密封材料：库存充足
- ⚠️ 防水密封：库存不足，需要采购

#### 第10步：采购计划生成

**系统界面**：采购管理 → 采购建议

**自动生成采购单**：
```typescript
const procurementSuggestions = [
  {
    materialVariantCode: "FIRE-GLASS-6+152PVB+6",
    requiredQuantity: 60.48,  // 8樘×7.56㎡
    supplierCode: "SUP_GLASS_001",
    leadTime: 15,
    urgency: "high",
    notes: "定制防火玻璃，需要提供图纸"
  },
  {
    materialVariantCode: "WATERPROOF-SEAL-20MM",
    requiredQuantity: 30,     // 8樘×3.3米+余量
    supplierCode: "SUP_SEAL_002",
    leadTime: 7,
    urgency: "medium"
  }
];
```

#### 第11步：生产工单生成

**系统界面**：生产管理 → 生产工单 → 新增

**工单信息**：
```typescript
const workOrder: ProductionOrder = {
  workOrderNumber: "WO-2024-001",
  customerOrderId: "CO-2024-001",
  customerName: "XX五星级酒店",
  items: [{
    productCode: "FIRE_PARTITION_HOTEL_001",
    quantity: 8,
    specifications: {/* 配置快照 */},
    processFlow: [
      {
        stepName: "型材切割",
        workstation: "cutting_station",
        estimatedDuration: 120,  // 分钟
        constraints: {
          sawBladeType: "stainless_steel",
          cuttingSpeed: "medium"
        }
      },
      {
        stepName: "型材加工",
        workstation: "machining_station",
        estimatedDuration: 180,
        constraints: {
          surfaceFinish: "brushed",
          toleranceGrade: "±0.5mm"
        }
      },
      {
        stepName: "玻璃安装",
        workstation: "assembly_station",
        estimatedDuration: 90,
        dependencies: ["型材加工", "玻璃到货"]
      },
      {
        stepName: "密封处理",
        workstation: "sealing_station",
        estimatedDuration: 60,
        constraints: {
          sealantType: "fire_resistant",
          curingTime: 24  // 小时
        }
      },
      {
        stepName: "质量检验",
        workstation: "qc_station",
        estimatedDuration: 45,
        checkPoints: ["尺寸精度", "防火性能", "密封效果"]
      }
    ]
  }],
  plannedStartDate: "2024-02-20",  // 等待玻璃到货
  plannedEndDate: "2024-03-08",
  priority: "high"
}
```

#### 第12步：生产执行监控

**系统界面**：生产管理 → 工单跟踪

**生产进度**：
- **2024-02-15**：采购订单下达
- **2024-02-20**：型材切割开始
- **2024-02-22**：型材加工完成
- **2024-02-28**：防火玻璃到货检验
- **2024-03-01**：玻璃安装开始
- **2024-03-05**：密封处理完成
- **2024-03-06**：质量检验通过
- **2024-03-08**：包装发货

**质量追溯记录**：
- 型材批次：SS304-20240215-001
- 玻璃批次：FIRE-20240228-003
- 密封胶批次：SEAL-20240301-012
- 检验报告：QC-20240306-001

### 10.3 关键成功因素

#### 1. 系统智能化
- **自动匹配**：根据规格自动匹配产品结构
- **规则驱动**：业务规则自动验证和调整参数
- **成本计算**：实时成本核算和报价生成

#### 2. 数据一致性
- **配置快照**：保证生产与报价的一致性
- **版本管理**：产品结构变更不影响在制订单
- **追溯链条**：从订单到成品的完整追溯

#### 3. 协同效率
- **跨部门协作**：销售、技术、采购、生产无缝衔接
- **实时状态**：各环节状态实时更新和通知
- **异常预警**：库存不足、交期风险及时提醒

#### 4. 客户体验
- **快速响应**：从询价到报价大幅缩短时间
- **透明可视**：生产进度客户可查询
- **质量保证**：完整的质量记录和认证

### 10.4 流程价值体现

这个场景展示了MTO模式下，系统如何支持从客户个性化需求到最终产品交付的全流程管理，实现了：

1. **订单驱动**：以客户需求为起点，自动匹配或创建产品
2. **智能配置**：基于业务规则自动生成和验证产品配置
3. **精确成本**：从物料分类到具体变体的精确成本核算
4. **柔性生产**：根据订单特点自动生成工艺路线和工单
5. **全程追溯**：从原材料到成品的完整质量追溯链

通过这套系统，玻璃深加工企业能够高效处理个性化订单，提升客户满意度，降低运营成本，增强市场竞争力。
