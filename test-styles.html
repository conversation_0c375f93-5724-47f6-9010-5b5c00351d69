<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式测试</title>
    <style>
        /* 复制 main.css 中的关键样式 */
        :root {
            --background: oklch(1 0 0);
            --foreground: oklch(0.145 0 0);
            --card: oklch(1 0 0);
            --card-foreground: oklch(0.145 0 0);
            --popover: oklch(1 0 0);
            --popover-foreground: oklch(0.145 0 0);
            --primary: oklch(0.205 0 0);
            --primary-foreground: oklch(0.985 0 0);
            --secondary: oklch(0.97 0 0);
            --secondary-foreground: oklch(0.205 0 0);
            --muted: oklch(0.97 0 0);
            --muted-foreground: oklch(0.556 0 0);
            --accent: oklch(0.97 0 0);
            --accent-foreground: oklch(0.205 0 0);
            --destructive: oklch(0.577 0.245 27.325);
            --destructive-foreground: oklch(0.577 0.245 27.325);
            --border: oklch(0.922 0 0);
            --input: oklch(0.922 0 0);
            --ring: oklch(0.708 0 0);
            --radius: 0.625rem;
            --sidebar: oklch(0.985 0 0);
            --sidebar-foreground: oklch(0.145 0 0);
            --sidebar-primary: oklch(0.205 0 0);
            --sidebar-primary-foreground: oklch(0.985 0 0);
            --sidebar-accent: oklch(0.97 0 0);
            --sidebar-accent-foreground: oklch(0.205 0 0);
            --sidebar-border: oklch(0.922 0 0);
            --sidebar-ring: oklch(0.708 0 0);
        }
        
        body {
            background: oklch(var(--background));
            color: oklch(var(--foreground));
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .card {
            background: oklch(var(--card));
            color: oklch(var(--card-foreground));
            border: 1px solid oklch(var(--border));
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
        }
        
        .primary-button {
            background: oklch(var(--primary));
            color: oklch(var(--primary-foreground));
            border: none;
            border-radius: var(--radius);
            padding: 10px 20px;
            cursor: pointer;
        }
        
        .sidebar-demo {
            background: oklch(var(--sidebar));
            color: oklch(var(--sidebar-foreground));
            border: 1px solid oklch(var(--sidebar-border));
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>样式测试页面</h1>
    
    <div class="card">
        <h2>卡片组件测试</h2>
        <p>这是一个测试卡片，用于验证 CSS 变量是否正常工作。</p>
        <button class="primary-button">主要按钮</button>
    </div>
    
    <div class="sidebar-demo">
        <h2>侧边栏样式测试</h2>
        <p>这是侧边栏样式的测试区域。</p>
    </div>
    
    <script>
        console.log('CSS 变量测试:');
        console.log('--background:', getComputedStyle(document.documentElement).getPropertyValue('--background'));
        console.log('--foreground:', getComputedStyle(document.documentElement).getPropertyValue('--foreground'));
        console.log('--primary:', getComputedStyle(document.documentElement).getPropertyValue('--primary'));
    </script>
</body>
</html>