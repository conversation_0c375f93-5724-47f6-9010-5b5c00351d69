[{"id": "PF-TEMPERED", "name": "单片钢化玻璃产品族", "category": "tempered_glass", "attributes": [{"id": "width", "label": "宽度", "type": "number", "defaultValue": 1000, "rules": ["min:300,max:3660"]}, {"id": "height", "label": "高度", "type": "number", "defaultValue": 1000, "rules": ["min:300,max:2440"]}, {"id": "thickness", "label": "厚度", "type": "select", "defaultValue": "6", "options": ["5", "6", "8", "10", "12"]}, {"id": "glass_type", "label": "玻璃类型", "type": "select", "defaultValue": "clear", "options": ["clear", "low_e", "tinted", "reflective"]}, {"id": "color", "label": "颜色", "type": "select", "defaultValue": "透明", "options": ["透明", "灰色", "蓝色", "茶色", "金色", "绿色"]}, {"id": "is_tempered", "label": "是否钢化", "type": "boolean", "defaultValue": true}, {"id": "edge_type", "label": "边缘类型", "type": "select", "defaultValue": "ground", "options": ["ground", "polished", "beveled"]}, {"id": "surface_treatment", "label": "表面处理", "type": "select", "defaultValue": "none", "options": ["none", "coating", "frosting", "sandblasting"]}], "dependencyRules": ["IF(attribute.is_tempered == true) THEN (attribute.edge_type MUST BE 'polished' OR 'ground')", "IF(attribute.glass_type == 'low_e') THEN (attribute.surface_treatment MUST BE 'coating')", "IF(attribute.glass_type == 'reflective') THEN (attribute.surface_treatment MUST BE 'coating')", "IF(attribute.thickness >= 10) THEN (attribute.is_tempered SHOULD BE true)"], "description": "单片钢化玻璃产品族，适用于幕墙、门窗、阳光房等应用场景，支持多种玻璃类型和表面处理", "isActive": true, "createdAt": "2025-01-19T08:00:00Z", "updatedAt": "2025-01-19T08:00:00Z"}, {"id": "PF-LAMINATED", "name": "夹胶玻璃产品族", "category": "laminated_glass", "attributes": [{"id": "width", "label": "宽度", "type": "number", "defaultValue": 1000, "rules": ["min:300,max:3300"]}, {"id": "height", "label": "高度", "type": "number", "defaultValue": 1000, "rules": ["min:300,max:2140"]}, {"id": "total_thickness", "label": "总厚度", "type": "select", "defaultValue": "8", "options": ["8", "10", "12", "14", "16"]}, {"id": "glass1_thickness", "label": "外片厚度", "type": "select", "defaultValue": "4", "options": ["3", "4", "5", "6", "8"]}, {"id": "glass2_thickness", "label": "内片厚度", "type": "select", "defaultValue": "4", "options": ["3", "4", "5", "6", "8"]}, {"id": "pvb_thickness", "label": "PVB厚度", "type": "select", "defaultValue": "0.76", "options": ["0.38", "0.76", "1.14", "1.52", "2.28"]}, {"id": "glass_type", "label": "玻璃类型", "type": "select", "defaultValue": "clear", "options": ["clear", "low_e", "tinted"]}, {"id": "color", "label": "颜色", "type": "select", "defaultValue": "透明", "options": ["透明", "灰色", "蓝色", "茶色", "绿色"]}, {"id": "safety_level", "label": "安全等级", "type": "select", "defaultValue": "standard", "options": ["standard", "high", "bulletproof"]}, {"id": "pvb_type", "label": "PVB类型", "type": "select", "defaultValue": "standard", "options": ["standard", "acoustic", "structural", "colored"]}], "dependencyRules": ["IF(attribute.total_thickness == 8) THEN (attribute.glass1_thickness + attribute.glass2_thickness + attribute.pvb_thickness MUST EQUAL 8)", "IF(attribute.safety_level == 'bulletproof') THEN (attribute.pvb_thickness >= 1.52)", "IF(attribute.pvb_type == 'acoustic') THEN (attribute.pvb_thickness >= 0.76)", "IF(attribute.glass1_thickness != attribute.glass2_thickness) THEN (attribute.safety_level SHOULD BE 'high')"], "description": "夹胶玻璃产品族，提供安全防护功能，适用于建筑安全玻璃、防弹玻璃等应用", "isActive": true, "createdAt": "2025-01-19T08:00:00Z", "updatedAt": "2025-01-19T08:00:00Z"}, {"id": "PF-DECORATIVE", "name": "装饰玻璃产品族", "category": "decorative_glass", "attributes": [{"id": "width", "label": "宽度", "type": "number", "defaultValue": 1000, "rules": ["min:200,max:3300"]}, {"id": "height", "label": "高度", "type": "number", "defaultValue": 1000, "rules": ["min:200,max:2140"]}, {"id": "thickness", "label": "厚度", "type": "select", "defaultValue": "6", "options": ["5", "6", "8", "10"]}, {"id": "glass_type", "label": "玻璃类型", "type": "select", "defaultValue": "tinted", "options": ["tinted", "reflective", "frosted", "patterned"]}, {"id": "color", "label": "颜色", "type": "select", "defaultValue": "灰色", "options": ["灰色", "蓝色", "茶色", "绿色", "金色", "银色", "黑色"]}, {"id": "surface_finish", "label": "表面处理", "type": "select", "defaultValue": "smooth", "options": ["smooth", "textured", "frosted", "sandblasted", "etched"]}, {"id": "transparency_level", "label": "透明度", "type": "select", "defaultValue": "translucent", "options": ["transparent", "translucent", "opaque"]}, {"id": "pattern_type", "label": "图案类型", "type": "select", "defaultValue": "none", "options": ["none", "geometric", "floral", "abstract", "custom"]}, {"id": "edge_treatment", "label": "边缘处理", "type": "select", "defaultValue": "polished", "options": ["ground", "polished", "beveled", "rounded"]}], "dependencyRules": ["IF(attribute.glass_type == 'reflective') THEN (attribute.transparency_level MUST BE 'transparent' OR 'translucent')", "IF(attribute.glass_type == 'frosted') THEN (attribute.transparency_level MUST BE 'translucent' OR 'opaque')", "IF(attribute.pattern_type != 'none') THEN (attribute.surface_finish SHOULD BE 'textured' OR 'etched')", "IF(attribute.thickness >= 8) THEN (attribute.edge_treatment SHOULD BE 'polished')"], "description": "装饰玻璃产品族，适用于室内装饰、隔断、展示等应用，支持多种颜色、图案和表面处理", "isActive": true, "createdAt": "2025-01-19T08:00:00Z", "updatedAt": "2025-01-19T08:00:00Z"}, {"id": "PF-FURNITURE", "name": "家具玻璃产品族", "category": "furniture_glass", "attributes": [{"id": "width", "label": "宽度", "type": "number", "defaultValue": 800, "rules": ["min:200,max:2000"]}, {"id": "height", "label": "高度", "type": "number", "defaultValue": 600, "rules": ["min:200,max:1500"]}, {"id": "thickness", "label": "厚度", "type": "select", "defaultValue": "8", "options": ["5", "6", "8", "10", "12"]}, {"id": "glass_type", "label": "玻璃类型", "type": "select", "defaultValue": "clear", "options": ["clear", "tinted", "frosted"]}, {"id": "color", "label": "颜色", "type": "select", "defaultValue": "透明", "options": ["透明", "茶色", "灰色", "蓝色"]}, {"id": "edge_processing", "label": "边缘加工", "type": "select", "defaultValue": "polished", "options": ["ground", "polished", "beveled", "rounded"]}, {"id": "corner_type", "label": "角部处理", "type": "select", "defaultValue": "rounded", "options": ["sharp", "rounded", "chamfered"]}, {"id": "safety_treatment", "label": "安全处理", "type": "select", "defaultValue": "tempered", "options": ["none", "tempered", "laminated"]}, {"id": "surface_quality", "label": "表面质量", "type": "select", "defaultValue": "premium", "options": ["standard", "premium", "ultra"]}, {"id": "drilling_required", "label": "是否需要钻孔", "type": "boolean", "defaultValue": false}], "dependencyRules": ["IF(attribute.thickness >= 8) THEN (attribute.safety_treatment SHOULD BE 'tempered')", "IF(attribute.corner_type == 'rounded') THEN (attribute.edge_processing MUST BE 'polished')", "IF(attribute.drilling_required == true) THEN (attribute.safety_treatment MUST BE 'tempered')", "IF(attribute.surface_quality == 'ultra') THEN (attribute.edge_processing MUST BE 'polished')"], "description": "家具玻璃产品族，适用于餐桌、茶几、展示柜等家具应用，注重安全性和美观度", "isActive": true, "createdAt": "2025-01-19T08:00:00Z", "updatedAt": "2025-01-19T08:00:00Z"}, {"id": "PF-IGU", "name": "中空玻璃产品族", "category": "insulated_glass", "attributes": [{"id": "width", "label": "宽度", "type": "number", "defaultValue": 1000, "rules": ["min:300,max:3300"]}, {"id": "height", "label": "高度", "type": "number", "defaultValue": 1000, "rules": ["min:300,max:2140"]}, {"id": "glass1_thickness", "label": "外片厚度", "type": "select", "defaultValue": "6", "options": ["5", "6", "8", "10", "12"]}, {"id": "glass2_thickness", "label": "内片厚度", "type": "select", "defaultValue": "6", "options": ["5", "6", "8", "10", "12"]}, {"id": "spacer_width", "label": "间隔条宽度", "type": "select", "defaultValue": "12", "options": ["6", "9", "12", "15", "16", "20"]}, {"id": "glass_type", "label": "玻璃类型", "type": "select", "defaultValue": "clear", "options": ["clear", "low_e", "tinted", "reflective"]}, {"id": "color", "label": "颜色", "type": "select", "defaultValue": "透明", "options": ["透明", "灰色", "蓝色", "茶色", "绿色"]}, {"id": "is_tempered", "label": "是否钢化", "type": "boolean", "defaultValue": false}, {"id": "gas_filling", "label": "充气类型", "type": "select", "defaultValue": "air", "options": ["air", "argon", "krypton"]}, {"id": "sealant_type", "label": "密封胶类型", "type": "select", "defaultValue": "structural", "options": ["structural", "polysulfide", "silicone"]}, {"id": "energy_rating", "label": "节能等级", "type": "select", "defaultValue": "standard", "options": ["standard", "high", "ultra"]}], "dependencyRules": ["IF(attribute.is_tempered == true) THEN (attribute.edge_type MUST BE 'polished')", "IF(attribute.glass_type == 'low_e') THEN (attribute.energy_rating SHOULD BE 'high' OR 'ultra')", "IF(attribute.gas_filling == 'argon') THEN (attribute.spacer_width >= 12)", "IF(attribute.energy_rating == 'ultra') THEN (attribute.gas_filling SHOULD BE 'argon' OR 'krypton')"], "description": "中空玻璃产品族，提供优异的保温隔热性能，适用于节能门窗、幕墙等应用，支持多种厚度组合和功能配置", "isActive": true, "createdAt": "2025-01-19T08:00:00Z", "updatedAt": "2025-01-19T08:00:00Z"}]