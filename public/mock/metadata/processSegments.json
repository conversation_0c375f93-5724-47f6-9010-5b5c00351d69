{"data": [{"id": "SEG-COLD", "name": "冷工段", "description": "玻璃冷加工工艺段，包含切割、磨边、钻孔、清洗等工序", "nodes": [{"nodeId": "node-cold-001", "type": "ProcessStep", "entityId": "PS-CUT", "position": {"x": 100, "y": 50}}, {"nodeId": "node-cold-002", "type": "ProcessStep", "entityId": "PS-EDGE", "position": {"x": 300, "y": 50}}, {"nodeId": "node-cold-003", "type": "ProcessStep", "entityId": "PS-DRILL", "position": {"x": 500, "y": 50}}, {"nodeId": "node-cold-004", "type": "ProcessStep", "entityId": "PS-CLEAN", "position": {"x": 700, "y": 50}}, {"nodeId": "node-cold-buffer", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entityId": "BUF-COLD-OUT", "position": {"x": 900, "y": 50}}], "edges": [{"sourceNodeId": "node-cold-001", "targetNodeId": "node-cold-002"}, {"sourceNodeId": "node-cold-002", "targetNodeId": "node-cold-003"}, {"sourceNodeId": "node-cold-003", "targetNodeId": "node-cold-004"}, {"sourceNodeId": "node-cold-004", "targetNodeId": "node-cold-buffer"}], "processStepIds": ["PS-CUT", "PS-EDGE", "PS-DRILL", "PS-CLEAN"], "wipBufferIds": ["BUF-COLD-OUT"], "inputBufferId": null, "outputBufferId": "BUF-COLD-OUT"}, {"id": "SEG-HOT", "name": "热工段", "description": "玻璃热加工工艺段，包含钢化、弯钢化等工序", "nodes": [{"nodeId": "node-hot-buffer-in", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entityId": "BUF-HOT-IN", "position": {"x": 100, "y": 50}}, {"nodeId": "node-hot-001", "type": "ProcessStep", "entityId": "PS-TEMPER", "position": {"x": 300, "y": 50}}, {"nodeId": "node-hot-002", "type": "ProcessStep", "entityId": "PS-BEND", "position": {"x": 500, "y": 50}}, {"nodeId": "node-hot-buffer-out", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entityId": "BUF-HOT-OUT", "position": {"x": 700, "y": 50}}], "edges": [{"sourceNodeId": "node-hot-buffer-in", "targetNodeId": "node-hot-001"}, {"sourceNodeId": "node-hot-001", "targetNodeId": "node-hot-002"}, {"sourceNodeId": "node-hot-002", "targetNodeId": "node-hot-buffer-out"}], "processStepIds": ["PS-TEMPER", "PS-BEND"], "wipBufferIds": ["BUF-HOT-IN", "BUF-HOT-OUT"], "inputBufferId": "BUF-HOT-IN", "outputBufferId": "BUF-HOT-OUT"}, {"id": "SEG-LAMINATING", "name": "夹胶工段", "description": "夹胶玻璃生产工艺段，包含PVB夹胶、EVA夹胶等工序", "nodes": [{"nodeId": "node-lam-buffer-in", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entityId": "BUF-LAM-IN", "position": {"x": 100, "y": 50}}, {"nodeId": "node-lam-001", "type": "ProcessStep", "entityId": "PS-PVB-LAM", "position": {"x": 300, "y": 50}}, {"nodeId": "node-lam-002", "type": "ProcessStep", "entityId": "PS-EVA-LAM", "position": {"x": 300, "y": 150}}, {"nodeId": "node-lam-buffer-out", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entityId": "BUF-LAM-OUT", "position": {"x": 500, "y": 100}}], "edges": [{"sourceNodeId": "node-lam-buffer-in", "targetNodeId": "node-lam-001"}, {"sourceNodeId": "node-lam-buffer-in", "targetNodeId": "node-lam-002"}, {"sourceNodeId": "node-lam-001", "targetNodeId": "node-lam-buffer-out"}, {"sourceNodeId": "node-lam-002", "targetNodeId": "node-lam-buffer-out"}], "processStepIds": ["PS-PVB-LAM", "PS-EVA-LAM"], "wipBufferIds": ["BUF-LAM-IN", "BUF-LAM-OUT"], "inputBufferId": "BUF-LAM-IN", "outputBufferId": "BUF-LAM-OUT"}, {"id": "SEG-INSULATING", "name": "中空工段", "description": "中空玻璃生产工艺段，包含中空组装、充气等工序", "nodes": [{"nodeId": "node-ins-buffer-in", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entityId": "BUF-INS-IN", "position": {"x": 100, "y": 50}}, {"nodeId": "node-ins-001", "type": "ProcessStep", "entityId": "PS-SPACER", "position": {"x": 300, "y": 50}}, {"nodeId": "node-ins-002", "type": "ProcessStep", "entityId": "PS-SEAL", "position": {"x": 500, "y": 50}}, {"nodeId": "node-ins-003", "type": "ProcessStep", "entityId": "PS-GAS-FILL", "position": {"x": 700, "y": 50}}, {"nodeId": "node-ins-buffer-out", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entityId": "BUF-INS-OUT", "position": {"x": 900, "y": 50}}], "edges": [{"sourceNodeId": "node-ins-buffer-in", "targetNodeId": "node-ins-001"}, {"sourceNodeId": "node-ins-001", "targetNodeId": "node-ins-002"}, {"sourceNodeId": "node-ins-002", "targetNodeId": "node-ins-003"}, {"sourceNodeId": "node-ins-003", "targetNodeId": "node-ins-buffer-out"}], "processStepIds": ["PS-SPACER", "PS-SEAL", "PS-GAS-FILL"], "wipBufferIds": ["BUF-INS-IN", "BUF-INS-OUT"], "inputBufferId": "BUF-INS-IN", "outputBufferId": "BUF-INS-OUT"}, {"id": "SEG-PACKAGING", "name": "包装工段", "description": "产品包装工艺段，包含质检、包装、入库等工序", "nodes": [{"nodeId": "node-pack-buffer-in", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entityId": "BUF-PACK-IN", "position": {"x": 100, "y": 50}}, {"nodeId": "node-pack-001", "type": "ProcessStep", "entityId": "PS-QC", "position": {"x": 300, "y": 50}}, {"nodeId": "node-pack-002", "type": "ProcessStep", "entityId": "PS-PACK", "position": {"x": 500, "y": 50}}, {"nodeId": "node-pack-003", "type": "ProcessStep", "entityId": "PS-WAREHOUSE", "position": {"x": 700, "y": 50}}], "edges": [{"sourceNodeId": "node-pack-buffer-in", "targetNodeId": "node-pack-001"}, {"sourceNodeId": "node-pack-001", "targetNodeId": "node-pack-002"}, {"sourceNodeId": "node-pack-002", "targetNodeId": "node-pack-003"}], "processStepIds": ["PS-QC", "PS-PACK", "PS-WAREHOUSE"], "wipBufferIds": ["BUF-PACK-IN"], "inputBufferId": "BUF-PACK-IN", "outputBufferId": null}], "meta": {"total": 5, "lastUpdated": "2025-01-18T10:00:00Z", "version": "1.0"}}