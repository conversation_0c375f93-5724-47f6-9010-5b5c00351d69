{"uploadSimulation": {"supportedFormats": [{"format": "excel", "extensions": [".xlsx", ".xls"], "mimeTypes": ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel"], "maxSize": "50MB", "description": "Excel格式的切割优化结果文件"}, {"format": "json", "extensions": [".json"], "mimeTypes": ["application/json"], "maxSize": "10MB", "description": "JSON格式的结构化数据文件"}, {"format": "xml", "extensions": [".xml"], "mimeTypes": ["application/xml", "text/xml"], "maxSize": "20MB", "description": "XML格式的数据交换文件"}], "sampleResults": [{"filename": "cutting_optimization_result_20250821_001.xlsx", "simulatedResult": {"resultId": "SIM_RESULT_001", "uploadTime": "2025-08-21T14:30:00Z", "processTime": 2847, "status": "success", "batchInfo": {"batchId": "BATCH_DEMO_001", "customerName": "华润置地有限公司", "projectName": "华润悦府二期玻璃幕墙项目", "totalPieces": 128, "totalSheets": 45, "estimatedCost": 28500}, "optimizationMetrics": {"originalUtilization": 78.3, "optimizedUtilization": 91.7, "utilizationImprovement": 13.4, "materialSaved": 6.8, "costSaved": 4200, "timeSaved": 180, "wasteReduction": 35.2, "qualityScore": 96.5}, "detailedResults": {"totalLayouts": 12, "successfulOptimizations": 11, "partialOptimizations": 1, "failedOptimizations": 0, "averageUtilization": 91.7, "bestUtilization": 96.2, "worstUtilization": 83.4}, "materialBreakdown": [{"materialId": "GLASS_3300x2140_6MM", "materialName": "6mm透明浮法玻璃 3300×2140", "originalSheets": 20, "optimizedSheets": 17, "utilization": 92.5, "wasteArea": 2.1, "costSaved": 1800, "timeSaved": 45}, {"materialId": "GLASS_3660x2440_8MM", "materialName": "8mm超白浮法玻璃 3660×2440", "originalSheets": 15, "optimizedSheets": 13, "utilization": 89.8, "wasteArea": 1.8, "costSaved": 1600, "timeSaved": 38}, {"materialId": "GLASS_2440x1830_6MM", "materialName": "6mm钢化玻璃 2440×1830", "originalSheets": 10, "optimizedSheets": 9, "utilization": 94.1, "wasteArea": 0.9, "costSaved": 800, "timeSaved": 22}], "layoutPlans": [{"planId": "LAYOUT_001", "materialId": "GLASS_3300x2140_6MM", "sheetIndex": 1, "utilization": 94.2, "pieces": [{"pieceId": "P001", "productName": "大堂幕墙单元", "position": {"x": 15, "y": 15}, "dimensions": {"length": 1500, "width": 1000}, "rotation": 0, "quantity": 2}, {"pieceId": "P002", "productName": "走廊侧窗", "position": {"x": 1530, "y": 15}, "dimensions": {"length": 1200, "width": 800}, "rotation": 0, "quantity": 3}, {"pieceId": "P003", "productName": "办公室窗户", "position": {"x": 15, "y": 1030}, "dimensions": {"length": 1000, "width": 600}, "rotation": 0, "quantity": 4}], "cuttingPath": [{"step": 1, "type": "vertical", "position": 1515, "description": "第一刀垂直切割"}, {"step": 2, "type": "horizontal", "position": 1015, "description": "第二刀水平切割"}, {"step": 3, "type": "vertical", "position": 1015, "description": "第三刀垂直分割"}], "estimatedTime": 42}], "qualityChecks": {"dimensionAccuracy": 99.8, "edgeQuality": "A级", "surfaceIntegrity": "优秀", "toleranceCompliance": 100, "defectPrediction": 0.15}, "warnings": [{"code": "EDGE_UTILIZATION", "severity": "low", "message": "部分边角料尺寸较大，建议考虑小型产品复用", "suggestion": "可将边角料用于制作装饰条或维修备件"}, {"code": "SETUP_TIME", "severity": "medium", "message": "不同厚度玻璃混排可能增加设备调整时间", "suggestion": "建议按厚度分组批量切割"}], "recommendations": ["建议将8mm玻璃集中在上午切割，以减少设备调整频次", "可考虑将小尺寸边角料用于样品制作或维修用途", "建议定期校准切割设备以维持精度"]}}, {"filename": "optimization_batch_20250821_002.json", "simulatedResult": {"resultId": "SIM_RESULT_002", "uploadTime": "2025-08-21T15:15:00Z", "processTime": 1923, "status": "success", "batchInfo": {"batchId": "BATCH_DEMO_002", "customerName": "万科集团", "projectName": "万科云城商业综合体", "totalPieces": 86, "totalSheets": 32, "estimatedCost": 21300}, "optimizationMetrics": {"originalUtilization": 81.2, "optimizedUtilization": 93.8, "utilizationImprovement": 12.6, "materialSaved": 5.9, "costSaved": 3600, "timeSaved": 145, "wasteReduction": 28.7, "qualityScore": 94.8}, "detailedResults": {"totalLayouts": 8, "successfulOptimizations": 8, "partialOptimizations": 0, "failedOptimizations": 0, "averageUtilization": 93.8, "bestUtilization": 97.1, "worstUtilization": 88.9}}}, {"filename": "error_simulation.xlsx", "simulatedResult": {"status": "error", "error": {"code": "INVALID_FORMAT", "message": "文件格式不正确或数据结构不匹配", "details": "缺少必要的工作表'OptimizationResults'", "suggestions": ["请确保Excel文件包含标准的优化结果工作表", "检查数据格式是否符合第三方系统导出规范", "联系技术支持获取标准模板"]}}}], "processingStages": [{"stage": "upload", "name": "文件上传", "duration": 500, "description": "上传文件到服务器"}, {"stage": "validation", "name": "格式验证", "duration": 300, "description": "验证文件格式和数据结构"}, {"stage": "parsing", "name": "数据解析", "duration": 800, "description": "解析切割优化结果数据"}, {"stage": "analysis", "name": "结果分析", "duration": 1200, "description": "分析优化效果和质量指标"}, {"stage": "integration", "name": "数据集成", "duration": 400, "description": "集成到生产计划系统"}], "commonErrors": [{"code": "FILE_TOO_LARGE", "message": "文件大小超过限制", "solution": "请确保文件大小不超过50MB"}, {"code": "INVALID_EXTENSION", "message": "不支持的文件格式", "solution": "请上传.xlsx, .xls, .json或.xml格式文件"}, {"code": "CORRUPTED_FILE", "message": "文件损坏或无法读取", "solution": "请重新导出文件或检查文件完整性"}, {"code": "MISSING_DATA", "message": "缺少必要的数据字段", "solution": "请确保文件包含完整的优化结果数据"}, {"code": "VERSION_MISMATCH", "message": "数据格式版本不兼容", "solution": "请使用最新版本的第三方优化系统"}]}}