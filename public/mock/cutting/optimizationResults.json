{"data": [{"resultId": "cutting_result_001", "importTime": "2025-08-19T12:00:00Z", "batchId": "batch_华润置地_001", "customerName": "华润置地", "cuttingPlans": [{"planId": "PLAN_001_OPTIMIZED", "materialId": "GLASS_3300x2140_6MM", "materialName": "6mm透明浮法玻璃 3300×2140", "layout": {"pieces": [{"batchId": "batch_华润置地_001", "pieceId": "piece_001", "productName": "客厅落地窗", "position": {"x": 0, "y": 0}, "dimensions": {"length": 1200, "width": 800}, "rotation": 0, "quantity": 2}, {"batchId": "batch_华润置地_001", "pieceId": "piece_002", "productName": "卧室窗户", "position": {"x": 1200, "y": 0}, "dimensions": {"length": 1000, "width": 800}, "rotation": 0, "quantity": 3}, {"batchId": "batch_华润置地_001", "pieceId": "piece_003", "productName": "厨房窗户", "position": {"x": 0, "y": 800}, "dimensions": {"length": 800, "width": 600}, "rotation": 0, "quantity": 2}, {"batchId": "batch_华润置地_001", "pieceId": "piece_004", "productName": "卫生间窗户", "position": {"x": 800, "y": 800}, "dimensions": {"length": 900, "width": 600}, "rotation": 0, "quantity": 1}], "utilization": 92.3, "wasteArea": 0.28, "wastePercentage": 7.7, "totalArea": 7.062, "usedArea": 6.522}, "cuttingSequence": [{"stepId": "step_001", "type": "vertical", "position": 1200, "affectedPieces": ["piece_001", "piece_002"], "description": "第一刀：垂直切割，分离客厅窗和卧室窗"}, {"stepId": "step_002", "type": "horizontal", "position": 800, "affectedPieces": ["piece_001", "piece_003"], "description": "第二刀：水平切割，分离上下部分"}, {"stepId": "step_003", "type": "vertical", "position": 800, "affectedPieces": ["piece_003", "piece_004"], "description": "第三刀：垂直切割，分离厨房窗和卫生间窗"}], "estimatedCuttingTime": 45, "setupTime": 15, "totalTime": 60}, {"planId": "PLAN_002_OPTIMIZED", "materialId": "GLASS_3660x2440_6MM", "materialName": "6mm透明浮法玻璃 3660×2440", "layout": {"pieces": [{"batchId": "batch_华润置地_001", "pieceId": "piece_005", "productName": "阳台推拉门", "position": {"x": 0, "y": 0}, "dimensions": {"length": 1500, "width": 1000}, "rotation": 0, "quantity": 2}, {"batchId": "batch_华润置地_001", "pieceId": "piece_006", "productName": "书房窗户", "position": {"x": 1500, "y": 0}, "dimensions": {"length": 1200, "width": 1000}, "rotation": 0, "quantity": 1}], "utilization": 89.7, "wasteArea": 0.92, "wastePercentage": 10.3, "totalArea": 8.9304, "usedArea": 8.01}, "cuttingSequence": [{"stepId": "step_004", "type": "vertical", "position": 1500, "affectedPieces": ["piece_005", "piece_006"], "description": "垂直切割，分离推拉门和书房窗"}], "estimatedCuttingTime": 35, "setupTime": 10, "totalTime": 45}], "materialUsage": [{"materialId": "GLASS_3300x2140_6MM", "materialName": "6mm透明浮法玻璃 3300×2140", "originalQuantity": 45, "usedQuantity": 38, "utilization": 92.3, "unitCost": 120, "totalCost": 4560, "wasteAmount": 2.1, "wasteValue": 252}, {"materialId": "GLASS_3660x2440_6MM", "materialName": "6mm透明浮法玻璃 3660×2440", "originalQuantity": 30, "usedQuantity": 25, "utilization": 89.7, "unitCost": 150, "totalCost": 3750, "wasteAmount": 1.8, "wasteValue": 270}], "timeEstimates": [{"batchId": "batch_华润置地_001", "originalEstimate": 462, "optimizedEstimate": 385, "cuttingTime": 360, "setupTime": 25, "totalTime": 385, "timeSaved": 77, "efficiency": 83.3}], "improvements": {"materialSaved": 5.2, "timeSaved": 2.1, "utilizationImproved": 7.5, "costSaved": 4200, "details": {"originalUtilization": 84.8, "optimizedUtilization": 92.3, "originalCost": 12510, "optimizedCost": 8310, "originalTime": 462, "optimizedTime": 385}}, "validation": {"valid": true, "errors": [], "warnings": [{"code": "MINOR_WASTE", "message": "部分原片存在少量边角料，建议考虑小尺寸产品利用", "suggestion": "可考虑将边角料用于制作装饰条或小型配件"}, {"code": "CUTTING_SEQUENCE", "message": "建议优化切割顺序以减少设备调整时间", "suggestion": "可将相同厚度的玻璃集中切割"}]}, "qualityMetrics": {"edgeQuality": "优秀", "dimensionAccuracy": 99.8, "surfaceQuality": "A级", "defectRate": 0.2}, "environmentalImpact": {"wasteReduction": 15.3, "energySaved": 8.7, "carbonFootprintReduction": 12.1}}], "metadata": {"totalResults": 1, "generatedAt": "2025-08-19T12:00:00Z", "version": "1.0", "optimizationEngine": "GlassCut Pro v3.2", "processingTime": 1847}}