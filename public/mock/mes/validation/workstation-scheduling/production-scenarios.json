{"scenario": "典型生产日工段调度场景", "description": "基于玻璃深加工企业典型8小时工作日的多工段协同生产场景", "date": "2024-01-08", "workstations": [{"id": "WS-CUTTING-01", "name": "切割工段1号线", "type": "cutting", "capacity": {"maxBatches": 12, "maxPieces": 200, "maxArea": 150}, "equipment": [{"id": "CUT-001", "name": "全自动切割机1号", "status": "运行中", "efficiency": 0.85}, {"id": "CUT-002", "name": "全自动切割机2号", "status": "运行中", "efficiency": 0.82}], "operators": [{"id": "OP-001", "name": "张师傅", "skill": "高级", "shift": "白班"}, {"id": "OP-002", "name": "李师傅", "skill": "中级", "shift": "白班"}]}, {"id": "WS-TEMPERING-01", "name": "钢化工段1号线", "type": "tempering", "capacity": {"maxBatches": 8, "maxPieces": 120, "maxArea": 100}, "equipment": [{"id": "TEMP-001", "name": "钢化炉1号", "status": "运行中", "efficiency": 0.78, "specifications": {"maxLength": 2440, "maxWidth": 3660, "maxThickness": 19, "minThickness": 4}}], "operators": [{"id": "OP-003", "name": "王师傅", "skill": "高级", "shift": "白班"}, {"id": "OP-004", "name": "赵师傅", "skill": "高级", "shift": "白班"}]}, {"id": "WS-INSULATING-01", "name": "中空工段1号线", "type": "insulating", "capacity": {"maxBatches": 6, "maxPieces": 80, "maxArea": 60}, "equipment": [{"id": "INS-001", "name": "中空生产线1号", "status": "运行中", "efficiency": 0.72, "specifications": {"maxLength": 3000, "maxWidth": 2000, "spacerWidths": [6, 9, 12, 15, 16, 20]}}], "operators": [{"id": "OP-005", "name": "刘师傅", "skill": "中级", "shift": "白班"}, {"id": "OP-006", "name": "陈师傅", "skill": "中级", "shift": "白班"}]}, {"id": "WS-PACKAGING-01", "name": "包装工段1号线", "type": "packaging", "capacity": {"maxBatches": 15, "maxPieces": 300, "maxArea": 200}, "equipment": [{"id": "PACK-001", "name": "自动包装线1号", "status": "运行中", "efficiency": 0.88}], "operators": [{"id": "OP-007", "name": "孙师傅", "skill": "中级", "shift": "白班"}, {"id": "OP-008", "name": "周师傅", "skill": "初级", "shift": "白班"}]}], "productionBatches": [{"id": "BATCH-001", "batchNumber": "B240108001", "workstation": "WS-CUTTING-01", "processType": "切割", "orderItems": [{"id": "ITEM-001", "orderId": "ORD-2024-001", "specifications": {"length": 1800, "width": 1200, "thickness": 6, "glassType": "low_e"}, "quantity": 25, "currentStatus": "切割中"}], "status": "in_progress", "plannedStartTime": "2024-01-08T08:00:00Z", "plannedEndTime": "2024-01-08T09:30:00Z", "actualStartTime": "2024-01-08T08:05:00Z", "operator": "张师傅", "equipment": "CUT-001", "processParameters": {"cuttingSpeed": 15, "bladeType": "diamond", "coolantFlow": 2.5}, "efficiency": 0.85}, {"id": "BATCH-002", "batchNumber": "B240108002", "workstation": "WS-TEMPERING-01", "processType": "钢化", "orderItems": [{"id": "ITEM-001", "orderId": "ORD-2024-001", "specifications": {"length": 1800, "width": 1200, "thickness": 6, "glassType": "low_e"}, "quantity": 18, "currentStatus": "待钢化"}], "status": "planned", "plannedStartTime": "2024-01-08T10:00:00Z", "plannedEndTime": "2024-01-08T11:15:00Z", "operator": "王师傅", "equipment": "TEMP-001", "processParameters": {"temperature": 680, "heatingTime": 45, "coolingTime": 180}, "efficiency": 0.78}], "semiFinishedInventory": [{"id": "SEMI-001", "orderItemId": "ITEM-001", "currentWorkstation": "WS-CUTTING-01", "specifications": {"length": 1800, "width": 1200, "thickness": 6, "glassType": "low_e"}, "quantity": 25, "status": "available", "location": "切割工段暂存区A1", "qualityStatus": "passed", "createdAt": "2024-01-08T09:30:00Z", "updatedAt": "2024-01-08T09:30:00Z"}, {"id": "SEMI-002", "orderItemId": "ITEM-004", "currentWorkstation": "WS-CUTTING-01", "specifications": {"length": 1200, "width": 800, "thickness": 5, "glassType": "clear"}, "quantity": 30, "status": "available", "location": "切割工段暂存区A2", "qualityStatus": "passed", "createdAt": "2024-01-08T09:45:00Z", "updatedAt": "2024-01-08T09:45:00Z"}, {"id": "SEMI-003", "orderItemId": "ITEM-001", "currentWorkstation": "WS-TEMPERING-01", "specifications": {"length": 1800, "width": 1200, "thickness": 6, "glassType": "low_e"}, "quantity": 18, "status": "in_process", "location": "钢化炉1号", "qualityStatus": "pending", "createdAt": "2024-01-08T10:00:00Z", "updatedAt": "2024-01-08T10:30:00Z"}], "workflowConstraints": {"transferTime": {"cutting_to_tempering": 30, "tempering_to_insulating": 45, "insulating_to_packaging": 20}, "qualityCheckTime": {"cutting": 10, "tempering": 15, "insulating": 20}, "batchSizeConstraints": {"tempering": {"minBatchSize": 10, "maxBatchSize": 50, "thicknessTolerance": 0.5}, "insulating": {"minBatchSize": 5, "maxBatchSize": 30, "pairingRequired": true}}}, "dailyTargets": {"totalPieces": 180, "utilizationRate": 75, "qualityRate": 98, "onTimeDelivery": 95}}