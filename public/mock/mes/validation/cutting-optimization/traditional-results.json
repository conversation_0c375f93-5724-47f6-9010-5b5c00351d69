{"scenario": "传统人工排版结果", "description": "基于经验丰富排版师傅的人工排版方案，代表传统排版方式的典型效果", "optimizationResults": [{"id": "TRAD-001", "taskId": "TASK-001", "algorithmType": "third_party", "algorithmName": "人工经验排版", "utilizationRate": 78.5, "totalSheets": 48, "totalCost": 13440, "computationTime": 14400, "createdAt": "2024-01-08T09:00:00Z", "wasteArea": 15.2, "cuttingPlans": [{"id": "PLAN-TRAD-001", "sheetId": "SHEET-001", "utilizationRate": 76.8, "wasteArea": 1.64, "layout": {"sheetLength": 3300, "sheetWidth": 2140, "pieces": [{"x": 20, "y": 20, "length": 1800, "width": 1200, "orderItemId": "ITEM-001", "rotation": false}, {"x": 1840, "y": 20, "length": 1200, "width": 800, "orderItemId": "ITEM-004", "rotation": true}, {"x": 20, "y": 1240, "length": 1500, "width": 1000, "orderItemId": "ITEM-003", "rotation": false}]}, "pieces": [{"id": "PIECE-001", "orderItemId": "ITEM-001", "x": 20, "y": 20, "length": 1800, "width": 1200, "rotation": false}, {"id": "PIECE-002", "orderItemId": "ITEM-004", "x": 1840, "y": 20, "length": 1200, "width": 800, "rotation": true}, {"id": "PIECE-003", "orderItemId": "ITEM-003", "x": 20, "y": 1240, "length": 1500, "width": 1000, "rotation": false}]}, {"id": "PLAN-TRAD-002", "sheetId": "SHEET-001", "utilizationRate": 79.2, "wasteArea": 1.47, "layout": {"sheetLength": 3300, "sheetWidth": 2140, "pieces": [{"x": 20, "y": 20, "length": 2400, "width": 1600, "orderItemId": "ITEM-002", "rotation": false}, {"x": 2440, "y": 20, "length": 800, "width": 1200, "orderItemId": "ITEM-004", "rotation": true}]}, "pieces": [{"id": "PIECE-004", "orderItemId": "ITEM-002", "x": 20, "y": 20, "length": 2400, "width": 1600, "rotation": false}, {"id": "PIECE-005", "orderItemId": "ITEM-004", "x": 2440, "y": 20, "length": 800, "width": 1200, "rotation": true}]}], "performance": {"planningTime": 240, "manualErrors": 3, "revisionCount": 2, "experienceLevel": "高级排版师傅", "consistencyScore": 0.75}}], "aggregateMetrics": {"averageUtilizationRate": 78.5, "totalWasteArea": 15.2, "totalPlanningTime": 240, "totalSheets": 48, "totalCost": 13440, "errorRate": 6.25, "revisionRate": 4.17}, "limitations": ["依赖排版师傅经验，结果不稳定", "计算时间长，影响生产效率", "容易出现人工计算错误", "难以处理复杂约束条件", "无法快速响应订单变更"], "typicalIssues": [{"issue": "边角余料浪费", "frequency": "经常", "impact": "利用率降低2-3%", "solution": "需要更精确的计算"}, {"issue": "工艺约束遗漏", "frequency": "偶尔", "impact": "返工成本增加", "solution": "需要系统化约束检查"}, {"issue": "批次优化不足", "frequency": "经常", "impact": "设备利用率低", "solution": "需要全局优化算法"}]}