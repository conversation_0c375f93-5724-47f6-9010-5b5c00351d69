# 产品管理模块实施规格 (Product Management Module Implementation Spec)

## 1. 概述

本文档定义了为 Glass ERP 原型系统开发“产品管理模块”所需的具体任务和交付成果。该模块的核心定位是一个动态的、规则驱动的**“产品定义中心”**，旨在将客户的定制化需求高效、准确地转化为可制造、可核算的生产指令。

## 2. 开发阶段与任务分解

### **第一阶段：核心数据模型与规则引擎建设 (预计1-2周)**

此阶段的目标是搭建新功能所需的数据结构和核心逻辑，为后续UI开发奠定基础。

#### **任务 1.1: 创建“产品模板”数据模型**

-   **操作**: 创建新文件 `public/mock/metadata/productTemplates.json`。
-   **交付内容**:
    -   一个JSON文件，包含一个 `productTemplates` 数组。
    -   每个模板对象应至少包含以下字段：
        -   `templateId` (string, 唯一标识)
        -   `templateName` (string, e.g., "80系列平开窗")
        -   `description` (string, 描述)
        -   `associatedCategoryIds` (string[], 关联的物料分类ID，定义了配置所需的物料类型)
        -   `configurationRules` (Object[], 存储配置规则的数组)
-   **示例**:
    ```json
    {
      "productTemplates": [
        {
          "templateId": "TPL-FW-01",
          "templateName": "80系列平开窗",
          "description": "标准80系列平开窗，可定制玻璃、型材和五金。",
          "associatedCategoryIds": ["CAT_RAW_GLASS", "CAT_PROFILE", "CAT_HARDWARE"],
          "configurationRules": [
            {
              "ruleId": "RULE-001",
              "description": "当窗户高度超过2200mm时，玻璃厚度必须大于等于8mm。",
              "condition": "height > 2200",
              "assertion": "glass.thickness >= 8",
              "errorMessage": "高度超过2200mm，玻璃厚度至少需要8mm。"
            }
          ]
        }
      ]
    }
    ```

#### **任务 1.2: 扩展TypeScript类型定义**

-   **操作**: 创建新文件 `src/types/product.ts`。
-   **交付内容**:
    -   定义与 `productTemplates.json` 结构匹配的TypeScript接口。
    -   核心接口应包括：
        -   `ProductTemplate`
        -   `ConfigurationRule`
        -   `ProductConfiguration` (用于描述一个被配置完成的产品实例)

#### **任务 1.3: 开发配置规则引擎**

-   **操作**: 创建新文件 `src/utils/configurationRuleEngine.ts`。
-   **交付内容**:
    -   一个独立的、可测试的规则引擎模块。
    -   导出一个核心函数，例如 `validateConfiguration(config: ProductConfiguration, rules: ConfigurationRule[]): ValidationResult`。
    -   `ValidationResult` 应包含一个布尔值 `isValid` 和一个错误/警告消息数组。
    -   该引擎应能解析简单的条件和断言表达式。

---

### **第二阶段：核心UI组件开发 (预计2-3周)**

此阶段的重点是开发用户交互的核心界面。

#### **任务 2.1: 开发“产品模板管理”视图**

-   **操作**: 创建新文件 `src/views/ProductTemplateView.vue`。
-   **交付内容**:
    -   一个完整的页面组件。
    -   使用 `DataTable` 组件展示 `productTemplates.json` 中的模板列表。
    -   提供“新建模板”的入口按钮。
    -   表格中的每一行应有“编辑”和“删除”操作。

#### **任务 2.2: 开发“产品模板编辑器”组件**

-   **操作**: 创建新文件 `src/components/product/ProductTemplateEditor.vue`。
-   **交付内容**:
    -   一个功能完善的对话框（Dialog）或侧拉面板（Sheet）组件。
    -   用于创建和编辑 `ProductTemplate` 对象。
    -   **表单应包括**:
        -   模板名称和描述的输入框。
        -   一个多选组件，用于关联物料分类 (`associatedCategoryIds`)。
        -   一个动态列表编辑器，用于添加、编辑和删除配置规则 (`configurationRules`)。

#### **任务 2.3: 开发“产品配置器”核心组件**

-   **操作**: 创建新文件 `src/components/product/ProductConfigurator.vue`。
-   **交付内容**:
    -   一个向导式（Wizard）的、多步骤的Vue组件。
    -   **核心功能**:
        1.  **启动**: 接收一个 `templateId` 作为prop，并加载对应的产品模板。
        2.  **动态步骤**: 根据模板的 `associatedCategoryIds` 动态生成配置步骤（例如，步骤1：选择玻璃，步骤2：选择型材...）。
        3.  **实时验证**: 在用户做出每个选择后，立即调用 `configurationRuleEngine` 进行验证，并实时在界面上显示反馈信息。
        4.  **实时报价**: 界面包含一个始终可见的区域，用于显示根据当前配置实时计算的成本和建议售价。
        5.  **完成**: 最后一步提供配置总览，并允许用户确认配置。

---

### **第三阶段：工作流集成与交付 (预计1-2周)**

此阶段的目标是将新开发的功能无缝融入现有业务流程。

#### **任务 3.1: 集成到销售订单流程**

-   **操作**: 修改现有的销售订单创建/编辑视图（例如 `CrmView.vue` 或相关组件）。
-   **交付内容**:
    -   在订单项旁边，添加一个“配置产品”按钮。
    -   点击该按钮时，应能启动 `ProductConfigurator.vue` 组件，并将配置结果关联到该订单项。

#### **任务 3.2: 实现BOM和工艺路线生成器**

-   **操作**: 创建新文件 `src/services/bomGenerator.ts`。
-   **交付内容**:
    -   一个服务模块，包含一个核心函数，例如 `generateProductionData(config: ProductConfiguration)`。
    -   该函数接收 `ProductConfigurator` 的最终配置结果。
    -   **输出**: 一个结构化的对象，包含精确的物料清单（BOM）和初步的工艺路线建议，为MES模块提供数据输入。

#### **任务 3.3: 更新导航与路由**

-   **操作**: 修改 `src/router/index.ts`。
-   **交付内容**:
    -   添加一条新路由，例如 `/products/templates`，并将其指向 `ProductTemplateView.vue`。

-   **操作**: 修改 `src/components/layout/AppSidebar.vue`。
-   **交付内容**:
    -   在侧边栏导航菜单中，新增一个名为“产品模板管理”的菜单项，链接到上述新路由。
